# GACO Platform - Next.js App & Firebase Integration Knowledge

## Overview

The GACO Platform is a Next.js 14 application that serves as the frontend for a consignment management platform connecting stores and artists. It integrates deeply with Firebase services and uses the GACO Component Library for UI consistency. The platform follows modern React patterns with TypeScript, styled-components, and ReactFire for Firebase integration.

## Architecture & Technology Stack

### Core Technologies
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type safety throughout the application
- **ReactFire** - Official Firebase React SDK for seamless integration
- **Styled Components** - CSS-in-JS styling (integrated with SSR)
- **GACO Component Library** - Custom component library (`@gaco/gaco-library`)
- **React Hook Form** - Form management and validation
- **Firebase SDK v9** - Modular Firebase SDK

### Firebase Services Integration
- **Firebase Authentication** - User authentication
- **Firestore** - NoSQL database for all application data
- **Cloud Functions** - Serverless backend (TypeScript & Python)
- **Cloud Storage** - File storage
- **Firebase Hosting** - Deployment platform

## Project Structure

```
/src
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with providers
│   ├── page.tsx           # Homepage
│   ├── account-overview/  # User account management
│   ├── artists/           # Artist (producer) pages
│   ├── stores/            # Store pages
│   ├── login/             # Authentication pages
│   ├── signup/
│   └── ...
├── components/            # Shared components
│   ├── FirebaseComponents.tsx  # Firebase provider setup
│   ├── ga-ui/            # App-specific UI components
│   ├── layouts/          # Layout components
│   ├── molecules/        # Mid-level components
│   └── organisms/        # Complex components
├── contexts/             # React contexts
│   ├── UserContext.tsx   # User authentication state
│   ├── StoreContext.tsx  # Store-specific context
│   ├── ThemeClient.tsx   # Theme provider wrapper
│   └── ...
├── hooks/                # Custom React hooks
│   ├── useFirestoreTypedRefs.ts  # Typed Firestore references
│   ├── useFirebaseFunctions.ts   # Cloud Functions hook
│   ├── useStores.ts      # Store data fetching
│   ├── useSales.ts       # Sales data fetching
│   └── ...
├── types/                # TypeScript type definitions
│   └── collections/      # Firestore collection interfaces
├── utils/                # Utility functions
├── constants/            # App constants
└── lib/                  # Library configurations
```

## Firebase Integration Architecture

### 1. Firebase Initialization & Provider Pattern

The app uses a layered provider pattern for Firebase services:

```typescript
// src/components/FirebaseComponents.tsx
export const FirebaseComponents = ({ children }: PropsWithChildren) => {
  return (
    <FirebaseAppProvider firebaseConfig={firebaseConfig}>
      <FirebaseComponentsInner>{children}</FirebaseComponentsInner>
    </FirebaseAppProvider>
  );
};

export const FirebaseComponentsInner = ({ children }: PropsWithChildren) => {
  const app = useFirebaseApp();
  
  // Initialize all Firebase services
  const auth = getAuth(app);
  const firestoreInstance = getFirestore(app);
  const storageInstance = getStorage(app);
  const functionsInstance = getFunctions(app, "europe-west3");
  
  // Connect to emulators in development
  if (process.env.NEXT_PUBLIC_FIRESTORE_EMULATOR === "true") {
    connectFirestoreEmulator(firestoreInstance, "localhost", 9000);
    connectStorageEmulator(storageInstance, "localhost", 9199);
    connectFunctionsEmulator(functionsInstance, "localhost", 5001);
    connectAuthEmulator(auth, "http://localhost:9099");
  }
  
  return (
    <AuthProvider sdk={auth}>
      <FunctionsProvider sdk={functionsInstance}>
        <StorageProvider sdk={storageInstance}>
          <FirestoreProvider sdk={firestoreInstance}>
            {children}
          </FirestoreProvider>
        </StorageProvider>
      </FunctionsProvider>
    </AuthProvider>
  );
};
```

### 2. Typed Firestore References Pattern

The app implements a sophisticated pattern for type-safe Firestore operations:

```typescript
// src/hooks/useFirestoreTypedRefs.ts
const createFirestoreConverter = <T extends { createdAt: Timestamp }>() => {
  return {
    toFirestore: (item: T) => {
      return {
        ...item,
        createdAt: serverTimestamp(),
      };
    },
    fromFirestore: (
      snapshot: QueryDocumentSnapshot<T>,
      options?: SnapshotOptions
    ) => {
      const data = snapshot.data(options);
      return {
        ...data,
        id: snapshot.id,
        createdAt: data.createdAt,
      };
    },
  };
};

export const useFirestoreTypedRefs = () => {
  const firestore = useFirestore();
  
  const getTypedCollectionRef = useCallback(
    <T extends { createdAt: Timestamp }>(path: string) => {
      return collection(firestore, path).withConverter(
        createFirestoreConverter<T>()
      );
    },
    [firestore]
  );
  
  // Similar for document references...
};
```

### 3. Data Fetching Hooks Pattern

Custom hooks encapsulate Firestore queries with ReactFire:

```typescript
// src/hooks/useStores.ts
export const useStores = () => {
  const { getTypedCollectionRef } = useFirestoreTypedRefs();
  
  const storesCollectionRef = useMemo(
    () => getTypedCollectionRef<Store>(FirebaseCollections.STORES),
    [getTypedCollectionRef]
  );
  
  const { status, data: stores } = useFirestoreCollectionData(
    storesCollectionRef,
    { idField: "id" }
  );
  
  const getStoreById = useMemo(
    () => (storeId: string) => stores?.find((store) => store.id === storeId),
    [stores]
  );
  
  return { stores: stores || [], status, getStoreById };
};
```

Complex queries with multiple collections:

```typescript
// src/hooks/useSales.ts
export const useSales = ({ producerId, storeId }: useSalesProps) => {
  // Fetch from both gold and staging collections
  const salesQuery = useMemo(() => {
    return query(
      salesCollectionRef,
      where("storeId", "==", storeId),
      orderBy("updatedAt", "desc")
    );
  }, [salesCollectionRef, storeId]);
  
  // Combine multiple collection results
  const sales = useMemo(
    () => [...salesGold, ...salesStaging],
    [salesGold, salesStaging]
  );
  
  // Compute derived values
  const totalRevenue = useMemo(
    () => sales?.reduce((acc, sale) => acc + sale.totalPrice, 0) || 0,
    [sales]
  );
};
```

## Next.js App Router Integration

### 1. Root Layout Structure

```typescript
// src/app/layout.tsx
export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body className={figtree.className}>
        <StyledComponentsRegistry>
          <FirebaseComponents>
            <UserProvider>
              <ThemeClient>
                <LayoutCenteredWithNav>{children}</LayoutCenteredWithNav>
              </ThemeClient>
            </UserProvider>
          </FirebaseComponents>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
```

### 2. Page Organization Pattern

Pages use client components with Firebase hooks:

```typescript
"use client";

export default function StorePage() {
  const { store } = useStoreContext();
  const { status, sales, totalRevenue } = useSales({ storeId: store.id });
  
  // ReactFire provides loading states
  if (status === "loading") return <Spinner />;
  
  return (
    <OverviewGrid>
      {/* Component composition with GACO library */}
      <Card>
        <CardSections.Title>Total Revenue</CardSections.Title>
        <CardSections.Content>
          {numberToCurrency(totalRevenue)}
        </CardSections.Content>
      </Card>
    </OverviewGrid>
  );
}
```

### 3. Dynamic Routes

```
/stores/[storeId]/
├── page.tsx           # Store overview
├── layout.tsx         # Store-specific providers
├── artists/           # Nested routes
├── sales/
├── invoices/
└── settings/
```

## Component Library Integration

### 1. Theme Provider Setup

```typescript
// src/contexts/ThemeClient.tsx
"use client";

import { ThemeOptions, ThemeProvider } from "@gaco/gaco-library";

export const ThemeClient = ({ children }: { children: ReactNode }) => {
  return <ThemeProvider theme={ThemeOptions.light}>{children}</ThemeProvider>;
};
```

### 2. Styled Components SSR

```typescript
// src/lib/StyledComponentsRegistry.tsx
export default function StyledComponentsRegistry({ children }) {
  const [styledComponentsStyleSheet] = useState(() => new ServerStyleSheet());
  
  useServerInsertedHTML(() => {
    const styles = styledComponentsStyleSheet.getStyleElement();
    styledComponentsStyleSheet.instance.clearTag();
    return <>{styles}</>;
  });
  
  if (typeof window !== "undefined") return <>{children}</>;
  
  return (
    <StyleSheetManager sheet={styledComponentsStyleSheet.instance}>
      {children}
    </StyleSheetManager>
  );
}
```

### 3. Component Usage Pattern

```typescript
import { 
  Card, 
  Button, 
  CardSections, 
  Spinner,
  media 
} from "@gaco/gaco-library";
import styled from "styled-components";

// Extend library components with custom styles
const CustomCard = styled(Card)`
  grid-area: 1 / 1 / 2 / 3;
  
  ${media.md`
    grid-area: 2 / 1 / 3 / 4;
  `}
`;
```

## Firebase Data Architecture

### 1. Collection Structure

```typescript
// src/constants/firebaseCollections.ts
export enum FirebaseCollections {
  STORES = "storesV2",
  PRODUCERS = "producersV2", 
  SALES_GOLD = "sales-gold",      // Processed sales data
  SALES_STAGING = "sales-staging", // Pending sales data
}
```

### 2. Data Pipeline Pattern

The platform uses a multi-stage data pipeline:
- **Staging Collections**: Raw data awaiting processing
- **Silver Collections**: Cleaned/validated data
- **Gold Collections**: Aggregated, business-ready data

### 3. Type Safety

Shared types between frontend and functions:

```typescript
// packages/shared-types/src/
export interface Store {
  id: string;
  displayName: string;
  email: string;
  createdAt: Timestamp;
  // ... other fields
}
```

## Cloud Functions Integration

### 1. Function Invocation Pattern

```typescript
// Using ReactFire's callable functions
import { httpsCallable } from "firebase/functions";
import { useFunctions } from "reactfire";

const functions = useFunctions();
const createStore = httpsCallable(functions, "createStore");

// Invoke with type safety
const result = await createStore({ 
  displayName: "New Store",
  email: "<EMAIL>"
});
```

### 2. Region-Specific Functions

All functions are deployed to `europe-west3`:

```typescript
const functionsInstance = getFunctions(app, "europe-west3");
```

## Authentication Flow

### 1. Sign-In Check Pattern

```typescript
import { useSigninCheck } from "reactfire";

export default function HomePage() {
  const { status, data: signInCheckResult } = useSigninCheck();
  
  if (status === "loading") return <Spinner />;
  
  return signInCheckResult.signedIn ? (
    <AuthenticatedContent />
  ) : (
    <PublicContent />
  );
}
```

### 2. User Context

```typescript
// src/contexts/UserContext.tsx
export const UserProvider = ({ children }) => {
  const { data: user } = useUser();
  const { storeDocs, producerDocs } = useUserData();
  
  return (
    <UserContext.Provider value={{ user, storeDocs, producerDocs }}>
      {children}
    </UserContext.Provider>
  );
};
```

## Development Workflow

### 1. Firebase Emulators

Full emulator suite for local development:
- Firestore: `localhost:8089`
- Auth: `localhost:9099`
- Functions: `localhost:5001`
- Storage: `localhost:9199`
- Hosting: `localhost:3000`

### 2. Environment Configuration

```env
# .env.example
NEXT_PUBLIC_API_KEY=
NEXT_PUBLIC_AUTH_DOMAIN=
NEXT_PUBLIC_PROJECT_ID=
NEXT_PUBLIC_STORAGE_BUCKET=
NEXT_PUBLIC_MESSAGING_SENDER_ID=
NEXT_PUBLIC_APP_ID=
NEXT_PUBLIC_FIRESTORE_EMULATOR=true
```

### 3. Monorepo Structure

```json
{
  "workspaces": [
    "packages/*",    // Shared types package
    "functions/*"    // Cloud functions
  ]
}
```

## Best Practices Implemented

### 1. Performance Optimization
- **Memoization**: Heavy use of `useMemo` for derived data
- **Lazy Loading**: Dynamic imports for route-based code splitting
- **Real-time Updates**: ReactFire's automatic subscription management

### 2. Type Safety
- **End-to-End Types**: Shared types package between frontend and backend
- **Firestore Converters**: Type-safe data serialization
- **Strict TypeScript**: Comprehensive type coverage

### 3. Error Handling
- **Loading States**: Consistent loading UI with GACO Spinner
- **Error Boundaries**: Graceful error handling
- **Status Tracking**: All hooks return status for proper state management

### 4. Code Organization
- **Feature-Based Structure**: Organized by domain (stores, artists, sales)
- **Reusable Hooks**: Encapsulated data fetching logic
- **Component Composition**: Leveraging GACO library patterns

## Integration Patterns Summary

1. **Provider Hierarchy**: Firebase → Auth → Theme → Layout → Page
2. **Data Flow**: Firestore → ReactFire Hooks → Components → UI
3. **Type Flow**: Shared Types → Firestore Converters → Component Props
4. **Style System**: GACO Theme → Styled Components → Extended Components
5. **State Management**: ReactFire + Context API for global state

This architecture enables a scalable, type-safe, and maintainable platform that leverages Firebase's real-time capabilities while maintaining a clean separation of concerns and excellent developer experience.