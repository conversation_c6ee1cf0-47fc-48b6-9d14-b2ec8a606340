*,
:after,
:before {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x: ;
	--tw-pan-y: ;
	--tw-pinch-zoom: ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position: ;
	--tw-gradient-via-position: ;
	--tw-gradient-to-position: ;
	--tw-ordinal: ;
	--tw-slashed-zero: ;
	--tw-numeric-figure: ;
	--tw-numeric-spacing: ;
	--tw-numeric-fraction: ;
	--tw-ring-inset: ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur: ;
	--tw-brightness: ;
	--tw-contrast: ;
	--tw-grayscale: ;
	--tw-hue-rotate: ;
	--tw-invert: ;
	--tw-saturate: ;
	--tw-sepia: ;
	--tw-drop-shadow: ;
	--tw-backdrop-blur: ;
	--tw-backdrop-brightness: ;
	--tw-backdrop-contrast: ;
	--tw-backdrop-grayscale: ;
	--tw-backdrop-hue-rotate: ;
	--tw-backdrop-invert: ;
	--tw-backdrop-opacity: ;
	--tw-backdrop-saturate: ;
	--tw-backdrop-sepia: ;
	--tw-contain-size: ;
	--tw-contain-layout: ;
	--tw-contain-paint: ;
	--tw-contain-style: ;
}
::backdrop {
	--tw-border-spacing-x: 0;
	--tw-border-spacing-y: 0;
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-pan-x: ;
	--tw-pan-y: ;
	--tw-pinch-zoom: ;
	--tw-scroll-snap-strictness: proximity;
	--tw-gradient-from-position: ;
	--tw-gradient-via-position: ;
	--tw-gradient-to-position: ;
	--tw-ordinal: ;
	--tw-slashed-zero: ;
	--tw-numeric-figure: ;
	--tw-numeric-spacing: ;
	--tw-numeric-fraction: ;
	--tw-ring-inset: ;
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgb(59 130 246 / 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	--tw-blur: ;
	--tw-brightness: ;
	--tw-contrast: ;
	--tw-grayscale: ;
	--tw-hue-rotate: ;
	--tw-invert: ;
	--tw-saturate: ;
	--tw-sepia: ;
	--tw-drop-shadow: ;
	--tw-backdrop-blur: ;
	--tw-backdrop-brightness: ;
	--tw-backdrop-contrast: ;
	--tw-backdrop-grayscale: ;
	--tw-backdrop-hue-rotate: ;
	--tw-backdrop-invert: ;
	--tw-backdrop-opacity: ;
	--tw-backdrop-saturate: ;
	--tw-backdrop-sepia: ;
	--tw-contain-size: ;
	--tw-contain-layout: ;
	--tw-contain-paint: ;
	--tw-contain-style: ;
} /*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/
*,
:after,
:before {
	box-sizing: border-box;
	border: 0 solid #e5e7eb;
}
:after,
:before {
	--tw-content: "";
}
:host,
html {
	line-height: 1.5;
	-webkit-text-size-adjust: 100%;
	-moz-tab-size: 4;
	tab-size: 4;
	font-family:
		ui-sans-serif, system-ui, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
	font-feature-settings: normal;
	font-variation-settings: normal;
	-webkit-tap-highlight-color: transparent;
}
body {
	margin: 0;
	line-height: inherit;
}
hr {
	height: 0;
	color: inherit;
	border-top-width: 1px;
}
abbr:where([title]) {
	text-decoration: underline dotted;
}
h1,
h2,
h3,
h4,
h5,
h6 {
	font-size: inherit;
	font-weight: inherit;
}
a {
	color: inherit;
	text-decoration: inherit;
}
b,
strong {
	font-weight: bolder;
}
code,
kbd,
pre,
samp {
	font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation
		Mono, Courier New, monospace;
	font-feature-settings: normal;
	font-variation-settings: normal;
	font-size: 1em;
}
small {
	font-size: 80%;
}
sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sub {
	bottom: -.25em;
}
sup {
	top: -.5em;
}
table {
	text-indent: 0;
	border-color: inherit;
	border-collapse: collapse;
}
button,
input,
optgroup,
select,
textarea {
	font-family: inherit;
	font-feature-settings: inherit;
	font-variation-settings: inherit;
	font-size: 100%;
	font-weight: inherit;
	line-height: inherit;
	letter-spacing: inherit;
	color: inherit;
	margin: 0;
	padding: 0;
}
button,
select {
	text-transform: none;
}
button,
input:where([type="button"]),
input:where([type="reset"]),
input:where([type="submit"]) {
	-webkit-appearance: button;
	background-color: transparent;
	background-image: none;
}
:-moz-focusring {
	outline: auto;
}
:-moz-ui-invalid {
	box-shadow: none;
}
progress {
	vertical-align: baseline;
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}
[type="search"] {
	-webkit-appearance: textfield;
	outline-offset: -2px;
}
::-webkit-search-decoration {
	-webkit-appearance: none;
}
::-webkit-file-upload-button {
	-webkit-appearance: button;
	font: inherit;
}
summary {
	display: list-item;
}
blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
	margin: 0;
}
fieldset {
	margin: 0;
}
fieldset,
legend {
	padding: 0;
}
menu,
ol,
ul {
	list-style: none;
	margin: 0;
	padding: 0;
}
dialog {
	padding: 0;
}
textarea {
	resize: vertical;
}
input::placeholder,
textarea::placeholder {
	opacity: 1;
	color: #9ca3af;
}
[role="button"],
button {
	cursor: pointer;
}
:disabled {
	cursor: default;
}
audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
	display: block;
	vertical-align: middle;
}
img,
video {
	max-width: 100%;
	height: auto;
}
[hidden]:where(:not([hidden="until-found"])) {
	display: none;
}
:root {
	--background: 0 0% 100%;
	--foreground: 0 0% 3.9%;
	--card: 0 0% 100%;
	--card-foreground: 0 0% 3.9%;
	--popover: 0 0% 100%;
	--popover-foreground: 0 0% 3.9%;
	--primary: 0 0% 9%;
	--primary-foreground: 0 0% 98%;
	--secondary: 0 0% 96.1%;
	--secondary-foreground: 0 0% 9%;
	--muted: 0 0% 96.1%;
	--muted-foreground: 0 0% 45.1%;
	--accent: 0 0% 96.1%;
	--accent-foreground: 0 0% 9%;
	--destructive: 0 84.2% 60.2%;
	--destructive-foreground: 0 0% 98%;
	--border: 0 0% 89.8%;
	--input: 0 0% 89.8%;
	--ring: 0 0% 3.9%;
	--chart-1: 12 76% 61%;
	--chart-2: 173 58% 39%;
	--chart-3: 197 37% 24%;
	--chart-4: 43 74% 66%;
	--chart-5: 27 87% 67%;
	--radius: 0.5rem;
	--sidebar-background: 0 0% 98%;
	--sidebar-foreground: 240 5.3% 26.1%;
	--sidebar-primary: 240 5.9% 10%;
	--sidebar-primary-foreground: 0 0% 98%;
	--sidebar-accent: 240 4.8% 95.9%;
	--sidebar-accent-foreground: 240 5.9% 10%;
	--sidebar-border: 220 13% 91%;
	--sidebar-ring: 217.2 91.2% 59.8%;
}
.dark {
	--background: 0 0% 3.9%;
	--foreground: 0 0% 98%;
	--card: 0 0% 3.9%;
	--card-foreground: 0 0% 98%;
	--popover: 0 0% 3.9%;
	--popover-foreground: 0 0% 98%;
	--primary: 0 0% 98%;
	--primary-foreground: 0 0% 9%;
	--secondary: 0 0% 14.9%;
	--secondary-foreground: 0 0% 98%;
	--muted: 0 0% 14.9%;
	--muted-foreground: 0 0% 63.9%;
	--accent: 0 0% 14.9%;
	--accent-foreground: 0 0% 98%;
	--destructive: 0 62.8% 30.6%;
	--destructive-foreground: 0 0% 98%;
	--border: 0 0% 14.9%;
	--input: 0 0% 14.9%;
	--ring: 0 0% 83.1%;
	--chart-1: 220 70% 50%;
	--chart-2: 160 60% 45%;
	--chart-3: 30 80% 55%;
	--chart-4: 280 65% 60%;
	--chart-5: 340 75% 55%;
	--sidebar-background: 240 5.9% 10%;
	--sidebar-foreground: 240 4.8% 95.9%;
	--sidebar-primary: 224.3 76.3% 48%;
	--sidebar-primary-foreground: 0 0% 100%;
	--sidebar-accent: 240 3.7% 15.9%;
	--sidebar-accent-foreground: 240 4.8% 95.9%;
	--sidebar-border: 240 3.7% 15.9%;
	--sidebar-ring: 217.2 91.2% 59.8%;
}
* {
	border-color: hsl(var(--border));
}
body {
	background-color: hsl(var(--background));
	color: hsl(var(--foreground));
}
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.pointer-events-none {
	pointer-events: none;
}
.pointer-events-auto {
	pointer-events: auto;
}
.visible {
	visibility: visible;
}
.invisible {
	visibility: hidden;
}
.static {
	position: static;
}
.fixed {
	position: fixed;
}
.absolute {
	position: absolute;
}
.relative {
	position: relative;
}
.sticky {
	position: sticky;
}
.inset-0 {
	inset: 0;
}
.inset-x-0 {
	left: 0;
	right: 0;
}
.inset-y-0 {
	top: 0;
	bottom: 0;
}
.-bottom-1 {
	bottom: -.25rem;
}
.-bottom-12 {
	bottom: -3rem;
}
.-left-12 {
	left: -3rem;
}
.-right-1 {
	right: -.25rem;
}
.-right-12 {
	right: -3rem;
}
.-top-12 {
	top: -3rem;
}
.bottom-0 {
	bottom: 0;
}
.bottom-20 {
	bottom: 5rem;
}
.left-0 {
	left: 0;
}
.left-1 {
	left: .25rem;
}
.left-1\/2 {
	left: 50%;
}
.left-2 {
	left: .5rem;
}
.left-\[50\%\] {
	left: 50%;
}
.right-0 {
	right: 0;
}
.right-1 {
	right: .25rem;
}
.right-2 {
	right: .5rem;
}
.right-3 {
	right: .75rem;
}
.right-4 {
	right: 1rem;
}
.top-0 {
	top: 0;
}
.top-1 {
	top: .25rem;
}
.top-1\.5 {
	top: .375rem;
}
.top-1\/2 {
	top: 50%;
}
.top-2 {
	top: .5rem;
}
.top-3\.5 {
	top: .875rem;
}
.top-4 {
	top: 1rem;
}
.top-\[1px\] {
	top: 1px;
}
.top-\[50\%\] {
	top: 50%;
}
.top-\[60\%\] {
	top: 60%;
}
.top-full {
	top: 100%;
}
.z-10 {
	z-index: 10;
}
.z-20 {
	z-index: 20;
}
.z-40 {
	z-index: 40;
}
.z-50 {
	z-index: 50;
}
.z-\[100\] {
	z-index: 100;
}
.z-\[1\] {
	z-index: 1;
}
.col-span-1 {
	grid-column: span 1 / span 1;
}
.col-span-2 {
	grid-column: span 2 / span 2;
}
.row-span-1 {
	grid-row: span 1 / span 1;
}
.row-span-2 {
	grid-row: span 2 / span 2;
}
.-mx-1 {
	margin-left: -.25rem;
	margin-right: -.25rem;
}
.mx-2 {
	margin-left: .5rem;
	margin-right: .5rem;
}
.mx-3\.5 {
	margin-left: .875rem;
	margin-right: .875rem;
}
.mx-auto {
	margin-left: auto;
	margin-right: auto;
}
.my-0\.5 {
	margin-top: .125rem;
	margin-bottom: .125rem;
}
.my-1 {
	margin-top: .25rem;
	margin-bottom: .25rem;
}
.-ml-4 {
	margin-left: -1rem;
}
.-mt-12 {
	margin-top: -3rem;
}
.-mt-4 {
	margin-top: -1rem;
}
.mb-1 {
	margin-bottom: .25rem;
}
.mb-12 {
	margin-bottom: 3rem;
}
.mb-16 {
	margin-bottom: 4rem;
}
.mb-2 {
	margin-bottom: .5rem;
}
.mb-3 {
	margin-bottom: .75rem;
}
.mb-4 {
	margin-bottom: 1rem;
}
.mb-6 {
	margin-bottom: 1.5rem;
}
.mb-8 {
	margin-bottom: 2rem;
}
.ml-1 {
	margin-left: .25rem;
}
.ml-4 {
	margin-left: 1rem;
}
.ml-auto {
	margin-left: auto;
}
.mr-1 {
	margin-right: .25rem;
}
.mr-1\.5 {
	margin-right: .375rem;
}
.mr-2 {
	margin-right: .5rem;
}
.mr-3 {
	margin-right: .75rem;
}
.mr-4 {
	margin-right: 1rem;
}
.mt-1 {
	margin-top: .25rem;
}
.mt-1\.5 {
	margin-top: .375rem;
}
.mt-12 {
	margin-top: 3rem;
}
.mt-16 {
	margin-top: 4rem;
}
.mt-2 {
	margin-top: .5rem;
}
.mt-20 {
	margin-top: 5rem;
}
.mt-24 {
	margin-top: 6rem;
}
.mt-3 {
	margin-top: .75rem;
}
.mt-4 {
	margin-top: 1rem;
}
.mt-5 {
	margin-top: 1.25rem;
}
.mt-6 {
	margin-top: 1.5rem;
}
.mt-8 {
	margin-top: 2rem;
}
.mt-auto {
	margin-top: auto;
}
.line-clamp-3 {
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 3;
}
.block {
	display: block;
}
.flex {
	display: flex;
}
.inline-flex {
	display: inline-flex;
}
.table {
	display: table;
}
.grid {
	display: grid;
}
.hidden {
	display: none;
}
.aspect-\[3\/4\] {
	aspect-ratio: 3 / 4;
}
.aspect-square {
	aspect-ratio: 1 / 1;
}
.aspect-video {
	aspect-ratio: 16 / 9;
}
.size-4 {
	width: 1rem;
	height: 1rem;
}
.h-1\.5 {
	height: .375rem;
}
.h-10 {
	height: 2.5rem;
}
.h-11 {
	height: 2.75rem;
}
.h-12 {
	height: 3rem;
}
.h-16 {
	height: 4rem;
}
.h-2 {
	height: .5rem;
}
.h-2\.5 {
	height: .625rem;
}
.h-24 {
	height: 6rem;
}
.h-3 {
	height: .75rem;
}
.h-3\.5 {
	height: .875rem;
}
.h-4 {
	height: 1rem;
}
.h-48 {
	height: 12rem;
}
.h-5 {
	height: 1.25rem;
}
.h-6 {
	height: 1.5rem;
}
.h-64 {
	height: 16rem;
}
.h-7 {
	height: 1.75rem;
}
.h-8 {
	height: 2rem;
}
.h-9 {
	height: 2.25rem;
}
.h-\[1px\] {
	height: 1px;
}
.h-\[80vh\] {
	height: 80vh;
}
.h-\[var\(--radix-navigation-menu-viewport-height\)\] {
	height: var(--radix-navigation-menu-viewport-height);
}
.h-\[var\(--radix-select-trigger-height\)\] {
	height: var(--radix-select-trigger-height);
}
.h-auto {
	height: auto;
}
.h-full {
	height: 100%;
}
.h-px {
	height: 1px;
}
.h-svh {
	height: 100svh;
}
.max-h-96 {
	max-height: 24rem;
}
.max-h-\[300px\] {
	max-height: 300px;
}
.max-h-\[90vh\] {
	max-height: 90vh;
}
.max-h-\[calc\(90vh-120px\)\] {
	max-height: calc(90vh - 120px);
}
.max-h-\[calc\(90vh-180px\)\] {
	max-height: calc(90vh - 180px);
}
.max-h-screen {
	max-height: 100vh;
}
.min-h-0 {
	min-height: 0;
}
.min-h-48 {
	min-height: 12rem;
}
.min-h-\[100px\] {
	min-height: 100px;
}
.min-h-\[250px\] {
	min-height: 250px;
}
.min-h-\[2rem\] {
	min-height: 2rem;
}
.min-h-\[300px\] {
	min-height: 300px;
}
.min-h-\[80px\] {
	min-height: 80px;
}
.min-h-screen {
	min-height: 100vh;
}
.min-h-svh {
	min-height: 100svh;
}
.w-0 {
	width: 0;
}
.w-1 {
	width: .25rem;
}
.w-10 {
	width: 2.5rem;
}
.w-11 {
	width: 2.75rem;
}
.w-2 {
	width: .5rem;
}
.w-2\.5 {
	width: .625rem;
}
.w-24 {
	width: 6rem;
}
.w-3 {
	width: .75rem;
}
.w-3\.5 {
	width: .875rem;
}
.w-3\/4 {
	width: 75%;
}
.w-4 {
	width: 1rem;
}
.w-5 {
	width: 1.25rem;
}
.w-6 {
	width: 1.5rem;
}
.w-64 {
	width: 16rem;
}
.w-7 {
	width: 1.75rem;
}
.w-72 {
	width: 18rem;
}
.w-8 {
	width: 2rem;
}
.w-9 {
	width: 2.25rem;
}
.w-\[--sidebar-width\] {
	width: var(--sidebar-width);
}
.w-\[100px\] {
	width: 100px;
}
.w-\[1px\] {
	width: 1px;
}
.w-auto {
	width: auto;
}
.w-full {
	width: 100%;
}
.w-max {
	width: max-content;
}
.w-px {
	width: 1px;
}
.min-w-0 {
	min-width: 0;
}
.min-w-10 {
	min-width: 2.5rem;
}
.min-w-11 {
	min-width: 2.75rem;
}
.min-w-5 {
	min-width: 1.25rem;
}
.min-w-9 {
	min-width: 2.25rem;
}
.min-w-\[120px\] {
	min-width: 120px;
}
.min-w-\[12rem\] {
	min-width: 12rem;
}
.min-w-\[300px\] {
	min-width: 300px;
}
.min-w-\[8rem\] {
	min-width: 8rem;
}
.min-w-\[var\(--radix-select-trigger-width\)\] {
	min-width: var(--radix-select-trigger-width);
}
.min-w-full {
	min-width: 100%;
}
.max-w-24 {
	max-width: 6rem;
}
.max-w-2xl {
	max-width: 42rem;
}
.max-w-4xl {
	max-width: 56rem;
}
.max-w-6xl {
	max-width: 72rem;
}
.max-w-7xl {
	max-width: 80rem;
}
.max-w-\[--skeleton-width\] {
	max-width: var(--skeleton-width);
}
.max-w-lg {
	max-width: 32rem;
}
.max-w-max {
	max-width: max-content;
}
.max-w-screen-xl {
	max-width: 1280px;
}
.max-w-xl {
	max-width: 36rem;
}
.flex-1 {
	flex: 1 1 0%;
}
.flex-shrink {
	flex-shrink: 1;
}
.flex-shrink-0,
.shrink-0 {
	flex-shrink: 0;
}
.flex-grow,
.grow {
	flex-grow: 1;
}
.grow-0 {
	flex-grow: 0;
}
.basis-full {
	flex-basis: 100%;
}
.caption-bottom {
	caption-side: bottom;
}
.border-collapse {
	border-collapse: collapse;
}
.-translate-x-1\/2 {
	--tw-translate-x: -50%;
}
.-translate-x-1\/2,
.-translate-x-px {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-px {
	--tw-translate-x: -1px;
}
.-translate-y-1\/2 {
	--tw-translate-y: -50%;
}
.-translate-y-1\/2,
.translate-x-\[-50\%\] {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\] {
	--tw-translate-x: -50%;
}
.translate-x-px {
	--tw-translate-x: 1px;
}
.translate-x-px,
.translate-y-\[-50\%\] {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\] {
	--tw-translate-y: -50%;
}
.rotate-45 {
	--tw-rotate: 45deg;
}
.rotate-45,
.rotate-90 {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
	--tw-rotate: 90deg;
}
.transform {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes pulse {
	50% {
		opacity: 0.5;
	}
}
.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.cursor-default {
	cursor: default;
}
.cursor-pointer {
	cursor: pointer;
}
.cursor-text {
	cursor: text;
}
.touch-none {
	touch-action: none;
}
.select-none {
	user-select: none;
}
.resize-none {
	resize: none;
}
.resize {
	resize: both;
}
.list-none {
	list-style-type: none;
}
.auto-rows-\[200px\] {
	grid-auto-rows: 200px;
}
.grid-cols-1 {
	grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
	grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
	grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-7 {
	grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
	flex-direction: row;
}
.flex-col {
	flex-direction: column;
}
.flex-col-reverse {
	flex-direction: column-reverse;
}
.flex-wrap {
	flex-wrap: wrap;
}
.items-start {
	align-items: flex-start;
}
.items-end {
	align-items: flex-end;
}
.items-center {
	align-items: center;
}
.items-stretch {
	align-items: stretch;
}
.justify-start {
	justify-content: flex-start;
}
.justify-end {
	justify-content: flex-end;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.gap-1 {
	gap: .25rem;
}
.gap-1\.5 {
	gap: .375rem;
}
.gap-12 {
	gap: 3rem;
}
.gap-2 {
	gap: .5rem;
}
.gap-3 {
	gap: .75rem;
}
.gap-4 {
	gap: 1rem;
}
.gap-6 {
	gap: 1.5rem;
}
.gap-8 {
	gap: 2rem;
}
.gap-x-6 {
	column-gap: 1.5rem;
}
.gap-y-1 {
	row-gap: .25rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(.25rem * var(--tw-space-x-reverse));
	margin-left: calc(.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(.5rem * var(--tw-space-x-reverse));
	margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1rem * var(--tw-space-x-reverse));
	margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1.5rem * var(--tw-space-x-reverse));
	margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(2rem * var(--tw-space-x-reverse));
	margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(.125rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
	--tw-divide-y-reverse: 0;
	border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
	border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
	--tw-divide-opacity: 1;
	border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
	--tw-divide-opacity: 1;
	border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.overflow-auto {
	overflow: auto;
}
.overflow-hidden {
	overflow: hidden;
}
.overflow-x-auto {
	overflow-x: auto;
}
.overflow-y-auto {
	overflow-y: auto;
}
.overflow-x-hidden {
	overflow-x: hidden;
}
.truncate {
	overflow: hidden;
	text-overflow: ellipsis;
}
.truncate,
.whitespace-nowrap {
	white-space: nowrap;
}
.whitespace-pre-line {
	white-space: pre-line;
}
.whitespace-pre-wrap {
	white-space: pre-wrap;
}
.break-words {
	overflow-wrap: break-word;
}
.rounded {
	border-radius: .25rem;
}
.rounded-\[2px\] {
	border-radius: 2px;
}
.rounded-\[inherit\] {
	border-radius: inherit;
}
.rounded-full {
	border-radius: 9999px;
}
.rounded-lg {
	border-radius: var(--radius);
}
.rounded-md {
	border-radius: calc(var(--radius) - 2px);
}
.rounded-sm {
	border-radius: calc(var(--radius) - 4px);
}
.rounded-t-\[10px\] {
	border-top-left-radius: 10px;
	border-top-right-radius: 10px;
}
.rounded-tl-sm {
	border-top-left-radius: calc(var(--radius) - 4px);
}
.border {
	border-width: 1px;
}
.border-2 {
	border-width: 2px;
}
.border-4 {
	border-width: 4px;
}
.border-\[1\.5px\] {
	border-width: 1.5px;
}
.border-y {
	border-top-width: 1px;
}
.border-b,
.border-y {
	border-bottom-width: 1px;
}
.border-b-2 {
	border-bottom-width: 2px;
}
.border-l {
	border-left-width: 1px;
}
.border-r {
	border-right-width: 1px;
}
.border-t {
	border-top-width: 1px;
}
.border-dashed {
	border-style: dashed;
}
.border-none {
	border-style: none;
}
.border-\[--color-border\] {
	border-color: var(--color-border);
}
.border-blue-200 {
	--tw-border-opacity: 1;
	border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-border\/50 {
	border-color: hsl(var(--border) / 0.5);
}
.border-destructive {
	border-color: hsl(var(--destructive));
}
.border-destructive\/50 {
	border-color: hsl(var(--destructive) / 0.5);
}
.border-gray-100 {
	--tw-border-opacity: 1;
	border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
	--tw-border-opacity: 1;
	border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300 {
	--tw-border-opacity: 1;
	border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-input {
	border-color: hsl(var(--input));
}
.border-primary {
	border-color: hsl(var(--primary));
}
.border-red-200 {
	--tw-border-opacity: 1;
	border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-sidebar-border {
	border-color: hsl(var(--sidebar-border));
}
.border-transparent {
	border-color: transparent;
}
.border-white {
	--tw-border-opacity: 1;
	border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-l-transparent {
	border-left-color: transparent;
}
.border-t-transparent {
	border-top-color: transparent;
}
.bg-\[--color-bg\] {
	background-color: var(--color-bg);
}
.bg-accent {
	background-color: hsl(var(--accent));
}
.bg-background {
	background-color: hsl(var(--background));
}
.bg-black {
	--tw-bg-opacity: 1;
	background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/0 {
	background-color: rgb(0 0 0 / 0);
}
.bg-black\/20 {
	background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/40 {
	background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/80 {
	background-color: rgb(0 0 0 / 0.8);
}
.bg-blue-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-400 {
	--tw-bg-opacity: 1;
	background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-50 {
	--tw-bg-opacity: 1;
	background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-900 {
	--tw-bg-opacity: 1;
	background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.bg-border {
	background-color: hsl(var(--border));
}
.bg-card {
	background-color: hsl(var(--card));
}
.bg-destructive {
	background-color: hsl(var(--destructive));
}
.bg-foreground {
	background-color: hsl(var(--foreground));
}
.bg-gray-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-300 {
	--tw-bg-opacity: 1;
	background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-50 {
	--tw-bg-opacity: 1;
	background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-50\/30 {
	background-color: rgb(249 250 251 / 0.3);
}
.bg-gray-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-800 {
	--tw-bg-opacity: 1;
	background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900 {
	--tw-bg-opacity: 1;
	background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-400 {
	--tw-bg-opacity: 1;
	background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-600 {
	--tw-bg-opacity: 1;
	background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-900 {
	--tw-bg-opacity: 1;
	background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.bg-muted {
	background-color: hsl(var(--muted));
}
.bg-muted\/50 {
	background-color: hsl(var(--muted) / 0.5);
}
.bg-orange-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-popover {
	background-color: hsl(var(--popover));
}
.bg-primary {
	background-color: hsl(var(--primary));
}
.bg-red-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-400 {
	--tw-bg-opacity: 1;
	background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-900 {
	--tw-bg-opacity: 1;
	background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.bg-secondary {
	background-color: hsl(var(--secondary));
}
.bg-sidebar {
	background-color: hsl(var(--sidebar-background));
}
.bg-sidebar-border {
	background-color: hsl(var(--sidebar-border));
}
.bg-transparent {
	background-color: transparent;
}
.bg-white {
	--tw-bg-opacity: 1;
	background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/80 {
	background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/95 {
	background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100 {
	--tw-bg-opacity: 1;
	background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-500 {
	--tw-bg-opacity: 1;
	background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-opacity-50 {
	--tw-bg-opacity: 0.5;
}
.bg-opacity-90 {
	--tw-bg-opacity: 0.9;
}
.bg-gradient-to-br {
	background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.from-amber-600 {
	--tw-gradient-from: #d97706 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600 {
	--tw-gradient-from: #2563eb var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-600 {
	--tw-gradient-from: #059669 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500 {
	--tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500 {
	--tw-gradient-from: #f97316 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500 {
	--tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-400 {
	--tw-gradient-from: #c084fc var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-900 {
	--tw-gradient-from: #0f172a var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violet-500 {
	--tw-gradient-from: #8b5cf6 var(--tw-gradient-from-position);
	--tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-pink-500 {
	--tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
	--tw-gradient-stops: var(--tw-gradient-from), #ec4899
		var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-blue-500 {
	--tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-purple-500 {
	--tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-600 {
	--tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-red-500 {
	--tw-gradient-to: #ef4444 var(--tw-gradient-to-position);
}
.to-rose-500 {
	--tw-gradient-to: #f43f5e var(--tw-gradient-to-position);
}
.to-slate-700 {
	--tw-gradient-to: #334155 var(--tw-gradient-to-position);
}
.to-teal-600 {
	--tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.to-yellow-600 {
	--tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);
}
.bg-cover {
	background-size: cover;
}
.bg-center {
	background-position: 50%;
}
.fill-current {
	fill: currentColor;
}
.fill-yellow-400 {
	fill: #facc15;
}
.object-cover {
	object-fit: cover;
}
.p-0 {
	padding: 0;
}
.p-1 {
	padding: .25rem;
}
.p-12 {
	padding: 3rem;
}
.p-2 {
	padding: .5rem;
}
.p-3 {
	padding: .75rem;
}
.p-4 {
	padding: 1rem;
}
.p-6 {
	padding: 1.5rem;
}
.p-8 {
	padding: 2rem;
}
.p-\[1px\] {
	padding: 1px;
}
.px-1 {
	padding-left: .25rem;
	padding-right: .25rem;
}
.px-1\.5 {
	padding-left: .375rem;
	padding-right: .375rem;
}
.px-2 {
	padding-left: .5rem;
	padding-right: .5rem;
}
.px-2\.5 {
	padding-left: .625rem;
	padding-right: .625rem;
}
.px-3 {
	padding-left: .75rem;
	padding-right: .75rem;
}
.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}
.px-5 {
	padding-left: 1.25rem;
	padding-right: 1.25rem;
}
.px-6 {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}
.px-8 {
	padding-left: 2rem;
	padding-right: 2rem;
}
.py-0\.5 {
	padding-top: .125rem;
	padding-bottom: .125rem;
}
.py-1 {
	padding-top: .25rem;
	padding-bottom: .25rem;
}
.py-1\.5 {
	padding-top: .375rem;
	padding-bottom: .375rem;
}
.py-10 {
	padding-top: 2.5rem;
	padding-bottom: 2.5rem;
}
.py-12 {
	padding-top: 3rem;
	padding-bottom: 3rem;
}
.py-16 {
	padding-top: 4rem;
	padding-bottom: 4rem;
}
.py-2 {
	padding-top: .5rem;
	padding-bottom: .5rem;
}
.py-24 {
	padding-top: 6rem;
	padding-bottom: 6rem;
}
.py-3 {
	padding-top: .75rem;
	padding-bottom: .75rem;
}
.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}
.py-6 {
	padding-top: 1.5rem;
	padding-bottom: 1.5rem;
}
.py-8 {
	padding-top: 2rem;
	padding-bottom: 2rem;
}
.pb-3 {
	padding-bottom: .75rem;
}
.pb-4 {
	padding-bottom: 1rem;
}
.pl-10 {
	padding-left: 2.5rem;
}
.pl-2\.5 {
	padding-left: .625rem;
}
.pl-3 {
	padding-left: .75rem;
}
.pl-4 {
	padding-left: 1rem;
}
.pl-8 {
	padding-left: 2rem;
}
.pr-2 {
	padding-right: .5rem;
}
.pr-2\.5 {
	padding-right: .625rem;
}
.pr-4 {
	padding-right: 1rem;
}
.pr-8 {
	padding-right: 2rem;
}
.pt-0 {
	padding-top: 0;
}
.pt-1 {
	padding-top: .25rem;
}
.pt-2 {
	padding-top: .5rem;
}
.pt-3 {
	padding-top: .75rem;
}
.pt-4 {
	padding-top: 1rem;
}
.pt-5 {
	padding-top: 1.25rem;
}
.pt-6 {
	padding-top: 1.5rem;
}
.pt-8 {
	padding-top: 2rem;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.align-middle {
	vertical-align: middle;
}
.font-mono {
	font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation
		Mono, Courier New, monospace;
}
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}
.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.text-5xl {
	font-size: 3rem;
	line-height: 1;
}
.text-\[0\.8rem\] {
	font-size: .8rem;
}
.text-\[10px\] {
	font-size: 10px;
}
.text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}
.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.text-sm {
	font-size: .875rem;
	line-height: 1.25rem;
}
.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}
.text-xs {
	font-size: .75rem;
	line-height: 1rem;
}
.font-bold {
	font-weight: 700;
}
.font-light {
	font-weight: 300;
}
.font-medium {
	font-weight: 500;
}
.font-normal {
	font-weight: 400;
}
.font-semibold {
	font-weight: 600;
}
.uppercase {
	text-transform: uppercase;
}
.capitalize {
	text-transform: capitalize;
}
.italic {
	font-style: italic;
}
.tabular-nums {
	--tw-numeric-spacing: tabular-nums;
	font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero)
		var(--tw-numeric-figure) var(--tw-numeric-spacing)
		var(--tw-numeric-fraction);
}
.leading-5 {
	line-height: 1.25rem;
}
.leading-none {
	line-height: 1;
}
.leading-relaxed {
	line-height: 1.625;
}
.leading-tight {
	line-height: 1.25;
}
.tracking-tight {
	letter-spacing: -.025em;
}
.tracking-wide {
	letter-spacing: .025em;
}
.tracking-wider {
	letter-spacing: .05em;
}
.tracking-widest {
	letter-spacing: .1em;
}
.text-accent-foreground {
	color: hsl(var(--accent-foreground));
}
.text-black {
	--tw-text-opacity: 1;
	color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-200 {
	--tw-text-opacity: 1;
	color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300 {
	--tw-text-opacity: 1;
	color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400 {
	--tw-text-opacity: 1;
	color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
	--tw-text-opacity: 1;
	color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
	--tw-text-opacity: 1;
	color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
	--tw-text-opacity: 1;
	color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-card-foreground {
	color: hsl(var(--card-foreground));
}
.text-current {
	color: currentColor;
}
.text-destructive {
	color: hsl(var(--destructive));
}
.text-destructive-foreground {
	color: hsl(var(--destructive-foreground));
}
.text-foreground {
	color: hsl(var(--foreground));
}
.text-foreground\/50 {
	color: hsl(var(--foreground) / 0.5);
}
.text-gray-300 {
	--tw-text-opacity: 1;
	color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400 {
	--tw-text-opacity: 1;
	color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
	--tw-text-opacity: 1;
	color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
	--tw-text-opacity: 1;
	color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
	--tw-text-opacity: 1;
	color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
	--tw-text-opacity: 1;
	color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
	--tw-text-opacity: 1;
	color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-200 {
	--tw-text-opacity: 1;
	color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.text-green-300 {
	--tw-text-opacity: 1;
	color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400 {
	--tw-text-opacity: 1;
	color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-600 {
	--tw-text-opacity: 1;
	color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-800 {
	--tw-text-opacity: 1;
	color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
	color: hsl(var(--muted-foreground));
}
.text-orange-500 {
	--tw-text-opacity: 1;
	color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
	--tw-text-opacity: 1;
	color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-popover-foreground {
	color: hsl(var(--popover-foreground));
}
.text-primary {
	color: hsl(var(--primary));
}
.text-primary-foreground {
	color: hsl(var(--primary-foreground));
}
.text-red-200 {
	--tw-text-opacity: 1;
	color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.text-red-500 {
	--tw-text-opacity: 1;
	color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
	--tw-text-opacity: 1;
	color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-800 {
	--tw-text-opacity: 1;
	color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground {
	color: hsl(var(--secondary-foreground));
}
.text-sidebar-foreground {
	color: hsl(var(--sidebar-foreground));
}
.text-sidebar-foreground\/70 {
	color: hsl(var(--sidebar-foreground) / 0.7);
}
.text-white {
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-yellow-400 {
	--tw-text-opacity: 1;
	color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-800 {
	--tw-text-opacity: 1;
	color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.underline {
	text-decoration-line: underline;
}
.underline-offset-4 {
	text-underline-offset: 4px;
}
.accent-foreground {
	accent-color: hsl(var(--foreground));
}
.opacity-0 {
	opacity: 0;
}
.opacity-50 {
	opacity: 0.5;
}
.opacity-60 {
	opacity: 0.6;
}
.opacity-70 {
	opacity: 0.7;
}
.opacity-80 {
	opacity: 0.8;
}
.opacity-90 {
	opacity: 0.9;
}
.shadow {
	--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px
		var(--tw-shadow-color);
}
.shadow,
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_hsl\(var\(--sidebar-border\)\)\] {
	--tw-shadow: 0 0 0 1px hsl(var(--sidebar-border));
	--tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
}
.shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px
		rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px
		var(--tw-shadow-color);
}
.shadow-lg,
.shadow-md {
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
	--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px
		var(--tw-shadow-color);
}
.shadow-none {
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
}
.shadow-none,
.shadow-sm {
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
}
.shadow-xl {
	--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px
		rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px
		var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.outline {
	outline-style: solid;
}
.ring {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}
.ring,
.ring-0 {
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.ring-0 {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}
.ring-2 {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.ring-black {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-500 {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.ring-ring {
	--tw-ring-color: hsl(var(--ring));
}
.ring-sidebar-ring {
	--tw-ring-color: hsl(var(--sidebar-ring));
}
.ring-offset-2 {
	--tw-ring-offset-width: 2px;
}
.ring-offset-background {
	--tw-ring-offset-color: hsl(var(--background));
}
.drop-shadow-lg {
	--tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04))
		drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
}
.drop-shadow-lg,
.filter {
	filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast)
		var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate)
		var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-sm {
	--tw-backdrop-blur: blur(4px);
	-webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
		var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
		var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
		var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
		var(--tw-backdrop-sepia);
	backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness)
		var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale)
		var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert)
		var(--tw-backdrop-opacity) var(--tw-backdrop-saturate)
		var(--tw-backdrop-sepia);
}
.transition {
	transition-property: color, background-color, border-color,
		text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
		backdrop-filter;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-\[left\2c right\2c width\] {
	transition-property: left, right, width;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-\[margin\2c opa\] {
	transition-property: margin, opa;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-\[width\2c height\2c padding\] {
	transition-property: width, height, padding;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-\[width\] {
	transition-property: width;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-all {
	transition-property: all;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-colors {
	transition-property: color, background-color, border-color,
		text-decoration-color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-opacity {
	transition-property: opacity;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-shadow {
	transition-property: box-shadow;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.transition-transform {
	transition-property: transform;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: .15s;
}
.duration-1000 {
	transition-duration: 1s;
}
.duration-200 {
	transition-duration: .2s;
}
.duration-300 {
	transition-duration: .3s;
}
.duration-500 {
	transition-duration: .5s;
}
.duration-700 {
	transition-duration: .7s;
}
.ease-in-out {
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
	transition-timing-function: linear;
}
.ease-out {
	transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@keyframes enter {
	0% {
		opacity: var(--tw-enter-opacity, 1);
		transform: translate3d(
				var(--tw-enter-translate-x, 0),
				var(--tw-enter-translate-y, 0),
				0
			)
			scale3d(
				var(--tw-enter-scale, 1),
				var(--tw-enter-scale, 1),
				var(--tw-enter-scale, 1)
			)
			rotate(var(--tw-enter-rotate, 0));
	}
}
@keyframes exit {
	to {
		opacity: var(--tw-exit-opacity, 1);
		transform: translate3d(
				var(--tw-exit-translate-x, 0),
				var(--tw-exit-translate-y, 0),
				0
			)
			scale3d(
				var(--tw-exit-scale, 1),
				var(--tw-exit-scale, 1),
				var(--tw-exit-scale, 1)
			)
			rotate(var(--tw-exit-rotate, 0));
	}
}
.animate-in {
	animation-name: enter;
	animation-duration: .15s;
	--tw-enter-opacity: initial;
	--tw-enter-scale: initial;
	--tw-enter-rotate: initial;
	--tw-enter-translate-x: initial;
	--tw-enter-translate-y: initial;
}
.fade-in-0 {
	--tw-enter-opacity: 0;
}
.fade-in-80 {
	--tw-enter-opacity: 0.8;
}
.zoom-in-95 {
	--tw-enter-scale: 0.95;
}
.duration-1000 {
	animation-duration: 1s;
}
.duration-200 {
	animation-duration: .2s;
}
.duration-300 {
	animation-duration: .3s;
}
.duration-500 {
	animation-duration: .5s;
}
.duration-700 {
	animation-duration: .7s;
}
.ease-in-out {
	animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-linear {
	animation-timing-function: linear;
}
.ease-out {
	animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
body {
	font-family: Arial, Helvetica, sans-serif;
}
.file\:border-0::file-selector-button {
	border-width: 0;
}
.file\:bg-transparent::file-selector-button {
	background-color: transparent;
}
.file\:text-sm::file-selector-button {
	font-size: .875rem;
	line-height: 1.25rem;
}
.file\:font-medium::file-selector-button {
	font-weight: 500;
}
.file\:text-foreground::file-selector-button {
	color: hsl(var(--foreground));
}
.placeholder\:text-muted-foreground::placeholder {
	color: hsl(var(--muted-foreground));
}
.after\:absolute:after {
	content: var(--tw-content);
	position: absolute;
}
.after\:-inset-2:after {
	content: var(--tw-content);
	inset: -.5rem;
}
.after\:inset-y-0:after {
	content: var(--tw-content);
	top: 0;
	bottom: 0;
}
.after\:left-1\/2:after {
	content: var(--tw-content);
	left: 50%;
}
.after\:w-1:after {
	content: var(--tw-content);
	width: .25rem;
}
.after\:w-\[2px\]:after {
	content: var(--tw-content);
	width: 2px;
}
.after\:-translate-x-1\/2:after {
	content: var(--tw-content);
	--tw-translate-x: -50%;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.first\:rounded-l-md:first-child {
	border-top-left-radius: calc(var(--radius) - 2px);
	border-bottom-left-radius: calc(var(--radius) - 2px);
}
.first\:border-l:first-child {
	border-left-width: 1px;
}
.last\:rounded-r-md:last-child {
	border-top-right-radius: calc(var(--radius) - 2px);
	border-bottom-right-radius: calc(var(--radius) - 2px);
}
.focus-within\:relative:focus-within {
	position: relative;
}
.focus-within\:z-20:focus-within {
	z-index: 20;
}
.hover\:border-gray-400:hover {
	--tw-border-opacity: 1;
	border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\:bg-accent:hover {
	background-color: hsl(var(--accent));
}
.hover\:bg-black\/30:hover {
	background-color: rgb(0 0 0 / 0.3);
}
.hover\:bg-blue-100:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-800:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.hover\:bg-destructive\/80:hover {
	background-color: hsl(var(--destructive) / 0.8);
}
.hover\:bg-destructive\/90:hover {
	background-color: hsl(var(--destructive) / 0.9);
}
.hover\:bg-gray-100:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-200:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-700:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-800:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-700:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-800:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}
.hover\:bg-muted:hover {
	background-color: hsl(var(--muted));
}
.hover\:bg-muted\/50:hover {
	background-color: hsl(var(--muted) / 0.5);
}
.hover\:bg-primary:hover {
	background-color: hsl(var(--primary));
}
.hover\:bg-primary\/80:hover {
	background-color: hsl(var(--primary) / 0.8);
}
.hover\:bg-primary\/90:hover {
	background-color: hsl(var(--primary) / 0.9);
}
.hover\:bg-red-800:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}
.hover\:bg-secondary:hover {
	background-color: hsl(var(--secondary));
}
.hover\:bg-secondary\/80:hover {
	background-color: hsl(var(--secondary) / 0.8);
}
.hover\:bg-sidebar-accent:hover {
	background-color: hsl(var(--sidebar-accent));
}
.hover\:bg-white:hover {
	--tw-bg-opacity: 1;
	background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/20:hover {
	background-color: rgb(255 255 255 / 0.2);
}
.hover\:fill-blue-600:hover {
	fill: #2563eb;
}
.hover\:fill-green-600:hover {
	fill: #16a34a;
}
.hover\:text-accent-foreground:hover {
	color: hsl(var(--accent-foreground));
}
.hover\:text-black:hover {
	--tw-text-opacity: 1;
	color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-700:hover {
	--tw-text-opacity: 1;
	color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover {
	--tw-text-opacity: 1;
	color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-foreground:hover {
	color: hsl(var(--foreground));
}
.hover\:text-gray-600:hover {
	--tw-text-opacity: 1;
	color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover {
	--tw-text-opacity: 1;
	color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\:text-muted-foreground:hover {
	color: hsl(var(--muted-foreground));
}
.hover\:text-primary-foreground:hover {
	color: hsl(var(--primary-foreground));
}
.hover\:text-red-700:hover {
	--tw-text-opacity: 1;
	color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:text-sidebar-accent-foreground:hover {
	color: hsl(var(--sidebar-accent-foreground));
}
.hover\:text-yellow-300:hover {
	--tw-text-opacity: 1;
	color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover {
	text-decoration-line: underline;
}
.hover\:opacity-100:hover {
	opacity: 1;
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover {
	--tw-shadow: 0 0 0 1px hsl(var(--sidebar-accent));
	--tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
}
.hover\:shadow-\[0_0_0_1px_hsl\(var\(--sidebar-accent\)\)\]:hover,
.hover\:shadow-lg:hover {
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover {
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px
		rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px
		var(--tw-shadow-color);
}
.hover\:shadow-md:hover {
	--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px
		var(--tw-shadow-color);
}
.hover\:shadow-md:hover,
.hover\:shadow-sm:hover {
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover {
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
}
.hover\:shadow-xl:hover {
	--tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px
		rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px
		var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:ring-1:hover {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-gray-400:hover {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}
.hover\:after\:bg-sidebar-border:hover:after {
	content: var(--tw-content);
	background-color: hsl(var(--sidebar-border));
}
.focus\:border-blue-500:focus {
	--tw-border-opacity: 1;
	border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\:border-gray-300:focus {
	--tw-border-opacity: 1;
	border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.focus\:bg-accent:focus {
	background-color: hsl(var(--accent));
}
.focus\:bg-primary:focus {
	background-color: hsl(var(--primary));
}
.focus\:text-accent-foreground:focus {
	color: hsl(var(--accent-foreground));
}
.focus\:text-primary-foreground:focus {
	color: hsl(var(--primary-foreground));
}
.focus\:opacity-100:focus {
	opacity: 1;
}
.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus\:ring-0:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}
.focus\:ring-0:focus,
.focus\:ring-2:focus {
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}
.focus\:ring-black:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.focus\:ring-ring:focus {
	--tw-ring-color: hsl(var(--ring));
}
.focus\:ring-offset-2:focus {
	--tw-ring-offset-width: 2px;
}
.focus-visible\:outline-none:focus-visible {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus-visible\:ring-1:focus-visible {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0
		var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0
		calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
		var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-ring:focus-visible {
	--tw-ring-color: hsl(var(--ring));
}
.focus-visible\:ring-sidebar-ring:focus-visible {
	--tw-ring-color: hsl(var(--sidebar-ring));
}
.focus-visible\:ring-offset-1:focus-visible {
	--tw-ring-offset-width: 1px;
}
.focus-visible\:ring-offset-2:focus-visible {
	--tw-ring-offset-width: 2px;
}
.focus-visible\:ring-offset-background:focus-visible {
	--tw-ring-offset-color: hsl(var(--background));
}
.active\:bg-sidebar-accent:active {
	background-color: hsl(var(--sidebar-accent));
}
.active\:text-sidebar-accent-foreground:active {
	color: hsl(var(--sidebar-accent-foreground));
}
.disabled\:pointer-events-none:disabled {
	pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled {
	cursor: not-allowed;
}
.disabled\:opacity-50:disabled {
	opacity: 0.5;
}
.group\/menu-item:focus-within .group-focus-within\/menu-item\:opacity-100 {
	opacity: 1;
}
.group:hover .group-hover\:scale-105 {
	--tw-scale-x: 1.05;
	--tw-scale-y: 1.05;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:opacity-100,
.group\/menu-item:hover .group-hover\/menu-item\:opacity-100 {
	opacity: 1;
}
.group.destructive .group-\[\.destructive\]\:border-muted\/40 {
	border-color: hsl(var(--muted) / 0.4);
}
.group.toaster .group-\[\.toaster\]\:border-border {
	border-color: hsl(var(--border));
}
.group.toast .group-\[\.toast\]\:bg-muted {
	background-color: hsl(var(--muted));
}
.group.toast .group-\[\.toast\]\:bg-primary {
	background-color: hsl(var(--primary));
}
.group.toaster .group-\[\.toaster\]\:bg-background {
	background-color: hsl(var(--background));
}
.group.destructive .group-\[\.destructive\]\:text-red-300 {
	--tw-text-opacity: 1;
	color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.group.toast .group-\[\.toast\]\:text-muted-foreground {
	color: hsl(var(--muted-foreground));
}
.group.toast .group-\[\.toast\]\:text-primary-foreground {
	color: hsl(var(--primary-foreground));
}
.group.toaster .group-\[\.toaster\]\:text-foreground {
	color: hsl(var(--foreground));
}
.group.toaster .group-\[\.toaster\]\:shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px
		rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px
		var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group.destructive
	.group-\[\.destructive\]\:hover\:border-destructive\/30:hover {
	border-color: hsl(var(--destructive) / 0.3);
}
.group.destructive .group-\[\.destructive\]\:hover\:bg-destructive:hover {
	background-color: hsl(var(--destructive));
}
.group.destructive
	.group-\[\.destructive\]\:hover\:text-destructive-foreground:hover {
	color: hsl(var(--destructive-foreground));
}
.group.destructive .group-\[\.destructive\]\:hover\:text-red-50:hover {
	--tw-text-opacity: 1;
	color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-destructive:focus {
	--tw-ring-color: hsl(var(--destructive));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-red-400:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.group.destructive .group-\[\.destructive\]\:focus\:ring-offset-red-600:focus {
	--tw-ring-offset-color: #dc2626;
}
.peer\/menu-button:hover
	~ .peer-hover\/menu-button\:text-sidebar-accent-foreground {
	color: hsl(var(--sidebar-accent-foreground));
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
	cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70 {
	opacity: 0.7;
}
.has-\[\[data-variant\=inset\]\]\:bg-sidebar:has([data-variant="inset"]) {
	background-color: hsl(var(--sidebar-background));
}
.has-\[\:disabled\]\:opacity-50:has(:disabled) {
	opacity: 0.5;
}
.group\/menu-item:has([data-sidebar="menu-action"])
	.group-has-\[\[data-sidebar\=menu-action\]\]\/menu-item\:pr-8 {
	padding-right: 2rem;
}
.aria-disabled\:pointer-events-none[aria-disabled="true"] {
	pointer-events: none;
}
.aria-disabled\:opacity-50[aria-disabled="true"] {
	opacity: 0.5;
}
.aria-selected\:bg-accent[aria-selected="true"] {
	background-color: hsl(var(--accent));
}
.aria-selected\:bg-accent\/50[aria-selected="true"] {
	background-color: hsl(var(--accent) / 0.5);
}
.aria-selected\:text-accent-foreground[aria-selected="true"] {
	color: hsl(var(--accent-foreground));
}
.aria-selected\:text-muted-foreground[aria-selected="true"] {
	color: hsl(var(--muted-foreground));
}
.aria-selected\:opacity-100[aria-selected="true"] {
	opacity: 1;
}
.data-\[disabled\=true\]\:pointer-events-none[data-disabled="true"],
.data-\[disabled\]\:pointer-events-none[data-disabled] {
	pointer-events: none;
}
.data-\[panel-group-direction\=vertical\]\:h-px[data-panel-group-direction="vertical"] {
	height: 1px;
}
.data-\[panel-group-direction\=vertical\]\:w-full[data-panel-group-direction="vertical"] {
	width: 100%;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"] {
	--tw-translate-y: 0.25rem;
}
.data-\[side\=bottom\]\:translate-y-1[data-side="bottom"],
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=left\]\:-translate-x-1[data-side="left"] {
	--tw-translate-x: -0.25rem;
}
.data-\[side\=right\]\:translate-x-1[data-side="right"] {
	--tw-translate-x: 0.25rem;
}
.data-\[side\=right\]\:translate-x-1[data-side="right"],
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[side\=top\]\:-translate-y-1[data-side="top"] {
	--tw-translate-y: -0.25rem;
}
.data-\[state\=checked\]\:translate-x-5[data-state="checked"] {
	--tw-translate-x: 1.25rem;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=unchecked\]\:translate-x-0[data-state="unchecked"],
.data-\[swipe\=cancel\]\:translate-x-0[data-swipe="cancel"] {
	--tw-translate-x: 0px;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
	--tw-translate-x: var(--radix-toast-swipe-end-x);
}
.data-\[swipe\=end\]\:translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"],
.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[swipe\=move\]\:translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
	--tw-translate-x: var(--radix-toast-swipe-move-x);
}
@keyframes accordion-up {
	0% {
		height: var(--radix-accordion-content-height);
	}
	to {
		height: 0;
	}
}
.data-\[state\=closed\]\:animate-accordion-up[data-state="closed"] {
	animation: accordion-up .2s ease-out;
}
@keyframes accordion-down {
	0% {
		height: 0;
	}
	to {
		height: var(--radix-accordion-content-height);
	}
}
.data-\[state\=open\]\:animate-accordion-down[data-state="open"] {
	animation: accordion-down .2s ease-out;
}
.data-\[panel-group-direction\=vertical\]\:flex-col[data-panel-group-direction="vertical"] {
	flex-direction: column;
}
.data-\[active\=true\]\:bg-sidebar-accent[data-active="true"] {
	background-color: hsl(var(--sidebar-accent));
}
.data-\[active\]\:bg-accent\/50[data-active] {
	background-color: hsl(var(--accent) / 0.5);
}
.data-\[selected\=\'true\'\]\:bg-accent[data-selected="true"] {
	background-color: hsl(var(--accent));
}
.data-\[state\=active\]\:bg-background[data-state="active"] {
	background-color: hsl(var(--background));
}
.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
	background-color: hsl(var(--primary));
}
.data-\[state\=on\]\:bg-accent[data-state="on"],
.data-\[state\=open\]\:bg-accent[data-state="open"] {
	background-color: hsl(var(--accent));
}
.data-\[state\=open\]\:bg-accent\/50[data-state="open"] {
	background-color: hsl(var(--accent) / 0.5);
}
.data-\[state\=open\]\:bg-secondary[data-state="open"] {
	background-color: hsl(var(--secondary));
}
.data-\[state\=selected\]\:bg-muted[data-state="selected"] {
	background-color: hsl(var(--muted));
}
.data-\[state\=unchecked\]\:bg-input[data-state="unchecked"] {
	background-color: hsl(var(--input));
}
.data-\[active\=true\]\:font-medium[data-active="true"] {
	font-weight: 500;
}
.data-\[active\=true\]\:text-sidebar-accent-foreground[data-active="true"] {
	color: hsl(var(--sidebar-accent-foreground));
}
.data-\[selected\=true\]\:text-accent-foreground[data-selected="true"] {
	color: hsl(var(--accent-foreground));
}
.data-\[state\=active\]\:text-foreground[data-state="active"] {
	color: hsl(var(--foreground));
}
.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
	color: hsl(var(--primary-foreground));
}
.data-\[state\=on\]\:text-accent-foreground[data-state="on"],
.data-\[state\=open\]\:text-accent-foreground[data-state="open"] {
	color: hsl(var(--accent-foreground));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"] {
	color: hsl(var(--muted-foreground));
}
.data-\[disabled\=true\]\:opacity-50[data-disabled="true"],
.data-\[disabled\]\:opacity-50[data-disabled] {
	opacity: 0.5;
}
.data-\[state\=open\]\:opacity-100[data-state="open"] {
	opacity: 1;
}
.data-\[state\=active\]\:shadow-sm[data-state="active"] {
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[swipe\=move\]\:transition-none[data-swipe="move"] {
	transition-property: none;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
	transition-duration: .3s;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
	transition-duration: .5s;
}
.data-\[motion\^\=from-\]\:animate-in[data-motion^="from-"],
.data-\[state\=open\]\:animate-in[data-state="open"],
.data-\[state\=visible\]\:animate-in[data-state="visible"] {
	animation-name: enter;
	animation-duration: .15s;
	--tw-enter-opacity: initial;
	--tw-enter-scale: initial;
	--tw-enter-rotate: initial;
	--tw-enter-translate-x: initial;
	--tw-enter-translate-y: initial;
}
.data-\[motion\^\=to-\]\:animate-out[data-motion^="to-"],
.data-\[state\=closed\]\:animate-out[data-state="closed"],
.data-\[state\=hidden\]\:animate-out[data-state="hidden"],
.data-\[swipe\=end\]\:animate-out[data-swipe="end"] {
	animation-name: exit;
	animation-duration: .15s;
	--tw-exit-opacity: initial;
	--tw-exit-scale: initial;
	--tw-exit-rotate: initial;
	--tw-exit-translate-x: initial;
	--tw-exit-translate-y: initial;
}
.data-\[motion\^\=from-\]\:fade-in[data-motion^="from-"] {
	--tw-enter-opacity: 0;
}
.data-\[motion\^\=to-\]\:fade-out[data-motion^="to-"],
.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
	--tw-exit-opacity: 0;
}
.data-\[state\=closed\]\:fade-out-80[data-state="closed"] {
	--tw-exit-opacity: 0.8;
}
.data-\[state\=hidden\]\:fade-out[data-state="hidden"] {
	--tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"],
.data-\[state\=visible\]\:fade-in[data-state="visible"] {
	--tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
	--tw-exit-scale: 0.95;
}
.data-\[state\=open\]\:zoom-in-90[data-state="open"] {
	--tw-enter-scale: 0.9;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
	--tw-enter-scale: 0.95;
}
.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion="from-end"] {
	--tw-enter-translate-x: 13rem;
}
.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion="from-start"] {
	--tw-enter-translate-x: -13rem;
}
.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion="to-end"] {
	--tw-exit-translate-x: 13rem;
}
.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion="to-start"] {
	--tw-exit-translate-x: -13rem;
}
.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
	--tw-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
	--tw-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
	--tw-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
	--tw-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
	--tw-exit-translate-y: 100%;
}
.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
	--tw-exit-translate-x: -100%;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"] {
	--tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-right-full[data-state="closed"],
.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
	--tw-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
	--tw-exit-translate-y: -100%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"] {
	--tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
	--tw-enter-translate-y: 100%;
}
.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
	--tw-enter-translate-x: -100%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"] {
	--tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
	--tw-enter-translate-x: 100%;
}
.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
	--tw-enter-translate-y: -100%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"] {
	--tw-enter-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-top-full[data-state="open"] {
	--tw-enter-translate-y: -100%;
}
.data-\[state\=closed\]\:duration-300[data-state="closed"] {
	animation-duration: .3s;
}
.data-\[state\=open\]\:duration-500[data-state="open"] {
	animation-duration: .5s;
}
.data-\[panel-group-direction\=vertical\]\:after\:left-0[data-panel-group-direction="vertical"]:after {
	content: var(--tw-content);
	left: 0;
}
.data-\[panel-group-direction\=vertical\]\:after\:h-1[data-panel-group-direction="vertical"]:after {
	content: var(--tw-content);
	height: .25rem;
}
.data-\[panel-group-direction\=vertical\]\:after\:w-full[data-panel-group-direction="vertical"]:after {
	content: var(--tw-content);
	width: 100%;
}
.data-\[panel-group-direction\=vertical\]\:after\:-translate-y-1\/2[data-panel-group-direction="vertical"]:after {
	content: var(--tw-content);
	--tw-translate-y: -50%;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[panel-group-direction\=vertical\]\:after\:translate-x-0[data-panel-group-direction="vertical"]:after {
	content: var(--tw-content);
	--tw-translate-x: 0px;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.data-\[state\=open\]\:hover\:bg-sidebar-accent:hover[data-state="open"] {
	background-color: hsl(var(--sidebar-accent));
}
.data-\[state\=open\]\:hover\:text-sidebar-accent-foreground:hover[data-state="open"] {
	color: hsl(var(--sidebar-accent-foreground));
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:left-\[calc\(var\(--sidebar-width\)\*-1\)\] {
	left: calc(var(--sidebar-width) * -1);
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:right-\[calc\(var\(--sidebar-width\)\*-1\)\] {
	right: calc(var(--sidebar-width) * -1);
}
.group[data-side="left"] .group-data-\[side\=left\]\:-right-4 {
	right: -1rem;
}
.group[data-side="right"] .group-data-\[side\=right\]\:left-0 {
	left: 0;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:-mt-8 {
	margin-top: -2rem;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:hidden {
	display: none;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!size-8 {
	width: 2rem !important;
	height: 2rem !important;
}
.group[data-collapsible="icon"]
	.group-data-\[collapsible\=icon\]\:w-\[--sidebar-width-icon\] {
	width: var(--sidebar-width-icon);
}
.group[data-collapsible="icon"]
	.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)\)\] {
	width: calc(var(--sidebar-width-icon) + 1rem);
}
.group[data-collapsible="icon"]
	.group-data-\[collapsible\=icon\]\:w-\[calc\(var\(--sidebar-width-icon\)_\+_theme\(spacing\.4\)_\+2px\)\] {
	width: calc(var(--sidebar-width-icon) + 1rem + 2px);
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:w-0 {
	width: 0;
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:translate-x-0 {
	--tw-translate-x: 0px;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-side="right"] .group-data-\[side\=right\]\:rotate-180,
.group[data-state="open"] .group-data-\[state\=open\]\:rotate-180 {
	--tw-rotate: 180deg;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group[data-collapsible="icon"]
	.group-data-\[collapsible\=icon\]\:overflow-hidden {
	overflow: hidden;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:rounded-lg {
	border-radius: var(--radius);
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:border {
	border-width: 1px;
}
.group[data-side="left"] .group-data-\[side\=left\]\:border-r {
	border-right-width: 1px;
}
.group[data-side="right"] .group-data-\[side\=right\]\:border-l {
	border-left-width: 1px;
}
.group[data-variant="floating"]
	.group-data-\[variant\=floating\]\:border-sidebar-border {
	border-color: hsl(var(--sidebar-border));
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-0 {
	padding: 0 !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:\!p-2 {
	padding: .5rem !important;
}
.group[data-collapsible="icon"] .group-data-\[collapsible\=icon\]\:opacity-0 {
	opacity: 0;
}
.group[data-variant="floating"] .group-data-\[variant\=floating\]\:shadow {
	--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px
		var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:after\:left-full:after {
	content: var(--tw-content);
	left: 100%;
}
.group[data-collapsible="offcanvas"]
	.group-data-\[collapsible\=offcanvas\]\:hover\:bg-sidebar:hover {
	background-color: hsl(var(--sidebar-background));
}
.peer\/menu-button[data-size="default"]
	~ .peer-data-\[size\=default\]\/menu-button\:top-1\.5 {
	top: .375rem;
}
.peer\/menu-button[data-size="lg"]
	~ .peer-data-\[size\=lg\]\/menu-button\:top-2\.5 {
	top: .625rem;
}
.peer\/menu-button[data-size="sm"]
	~ .peer-data-\[size\=sm\]\/menu-button\:top-1 {
	top: .25rem;
}
.peer[data-variant="inset"]
	~ .peer-data-\[variant\=inset\]\:min-h-\[calc\(100svh-theme\(spacing\.4\)\)\] {
	min-height: calc(100svh - 1rem);
}
.peer\/menu-button[data-active="true"]
	~ .peer-data-\[active\=true\]\/menu-button\:text-sidebar-accent-foreground {
	color: hsl(var(--sidebar-accent-foreground));
}
.dark\:border-destructive:is(.dark *) {
	border-color: hsl(var(--destructive));
}
@media (min-width: 640px) {
	.sm\:bottom-0 {
		bottom: 0;
	}
	.sm\:right-0 {
		right: 0;
	}
	.sm\:top-auto {
		top: auto;
	}
	.sm\:mt-0 {
		margin-top: 0;
	}
	.sm\:flex {
		display: flex;
	}
	.sm\:max-w-md {
		max-width: 28rem;
	}
	.sm\:max-w-sm {
		max-width: 24rem;
	}
	.sm\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.sm\:flex-row {
		flex-direction: row;
	}
	.sm\:flex-col {
		flex-direction: column;
	}
	.sm\:justify-end {
		justify-content: flex-end;
	}
	.sm\:gap-2\.5 {
		gap: .625rem;
	}
	.sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(.5rem * var(--tw-space-x-reverse));
		margin-left: calc(.5rem * calc(1 - var(--tw-space-x-reverse)));
	}
	.sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(1rem * var(--tw-space-x-reverse));
		margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
	}
	.sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-y-reverse: 0;
		margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
		margin-bottom: calc(0px * var(--tw-space-y-reverse));
	}
	.sm\:rounded-lg {
		border-radius: var(--radius);
	}
	.sm\:px-6 {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}
	.sm\:text-left {
		text-align: left;
	}
	.data-\[state\=open\]\:sm\:slide-in-from-bottom-full[data-state="open"] {
		--tw-enter-translate-y: 100%;
	}
}
@media (min-width: 768px) {
	.md\:absolute {
		position: absolute;
	}
	.md\:block {
		display: block;
	}
	.md\:flex {
		display: flex;
	}
	.md\:hidden {
		display: none;
	}
	.md\:min-h-0 {
		min-height: 0;
	}
	.md\:w-1\/3 {
		width: 33.333333%;
	}
	.md\:w-80 {
		width: 20rem;
	}
	.md\:w-\[var\(--radix-navigation-menu-viewport-width\)\] {
		width: var(--radix-navigation-menu-viewport-width);
	}
	.md\:w-auto {
		width: auto;
	}
	.md\:min-w-\[200px\] {
		min-width: 200px;
	}
	.md\:max-w-\[420px\] {
		max-width: 420px;
	}
	.md\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
	.md\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}
	.md\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
	.md\:flex-row {
		flex-direction: row;
	}
	.md\:gap-8 {
		gap: 2rem;
	}
	.md\:px-0 {
		padding-left: 0;
		padding-right: 0;
	}
	.md\:text-sm {
		font-size: .875rem;
		line-height: 1.25rem;
	}
	.md\:opacity-0 {
		opacity: 0;
	}
	.after\:md\:hidden:after {
		content: var(--tw-content);
		display: none;
	}
	.peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:m-2 {
		margin: .5rem;
	}
	.peer[data-state="collapsed"][data-variant="inset"]
		~ .md\:peer-data-\[state\=collapsed\]\:peer-data-\[variant\=inset\]\:ml-2 {
		margin-left: .5rem;
	}
	.peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:ml-0 {
		margin-left: 0;
	}
	.peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:rounded-xl {
		border-radius: .75rem;
	}
	.peer[data-variant="inset"] ~ .md\:peer-data-\[variant\=inset\]\:shadow {
		--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
		--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px
			var(--tw-shadow-color);
		box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
			var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
	}
}
@media (min-width: 1024px) {
	.lg\:col-span-2 {
		grid-column: span 2 / span 2;
	}
	.lg\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}
	.lg\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
	.lg\:px-8 {
		padding-left: 2rem;
		padding-right: 2rem;
	}
	.lg\:text-6xl {
		font-size: 3.75rem;
		line-height: 1;
	}
}
@media (min-width: 1280px) {
	.xl\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}
}
.\[\&\:has\(\[aria-selected\]\)\]\:bg-accent:has([aria-selected]) {
	background-color: hsl(var(--accent));
}
.first\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-l-md:has(
		[aria-selected]
	):first-child {
	border-top-left-radius: calc(var(--radius) - 2px);
	border-bottom-left-radius: calc(var(--radius) - 2px);
}
.last\:\[\&\:has\(\[aria-selected\]\)\]\:rounded-r-md:has(
		[aria-selected]
	):last-child {
	border-top-right-radius: calc(var(--radius) - 2px);
	border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[aria-selected\]\.day-outside\)\]\:bg-accent\/50:has(
		[aria-selected].day-outside
	) {
	background-color: hsl(var(--accent) / 0.5);
}
.\[\&\:has\(\[aria-selected\]\.day-range-end\)\]\:rounded-r-md:has(
		[aria-selected].day-range-end
	) {
	border-top-right-radius: calc(var(--radius) - 2px);
	border-bottom-right-radius: calc(var(--radius) - 2px);
}
.\[\&\:has\(\[role\=checkbox\]\)\]\:pr-0:has([role="checkbox"]) {
	padding-right: 0;
}
.\[\&\>button\]\:hidden > button {
	display: none;
}
.\[\&\>span\:last-child\]\:truncate > span:last-child {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.\[\&\>span\]\:line-clamp-1 > span {
	overflow: hidden;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
}
.\[\&\>svg\+div\]\:translate-y-\[-3px\] > svg + div {
	--tw-translate-y: -3px;
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\>svg\]\:absolute > svg {
	position: absolute;
}
.\[\&\>svg\]\:left-4 > svg {
	left: 1rem;
}
.\[\&\>svg\]\:top-4 > svg {
	top: 1rem;
}
.\[\&\>svg\]\:size-4 > svg {
	width: 1rem;
	height: 1rem;
}
.\[\&\>svg\]\:h-2\.5 > svg {
	height: .625rem;
}
.\[\&\>svg\]\:h-3 > svg {
	height: .75rem;
}
.\[\&\>svg\]\:h-3\.5 > svg {
	height: .875rem;
}
.\[\&\>svg\]\:w-2\.5 > svg {
	width: .625rem;
}
.\[\&\>svg\]\:w-3 > svg {
	width: .75rem;
}
.\[\&\>svg\]\:w-3\.5 > svg {
	width: .875rem;
}
.\[\&\>svg\]\:shrink-0 > svg {
	flex-shrink: 0;
}
.\[\&\>svg\]\:text-destructive > svg {
	color: hsl(var(--destructive));
}
.\[\&\>svg\]\:text-foreground > svg {
	color: hsl(var(--foreground));
}
.\[\&\>svg\]\:text-muted-foreground > svg {
	color: hsl(var(--muted-foreground));
}
.\[\&\>svg\]\:text-sidebar-accent-foreground > svg {
	color: hsl(var(--sidebar-accent-foreground));
}
.\[\&\>svg\~\*\]\:pl-7 > svg ~ * {
	padding-left: 1.75rem;
}
.\[\&\>tr\]\:last\:border-b-0:last-child > tr {
	border-bottom-width: 0;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"]
	> div {
	--tw-rotate: 90deg;
}
.\[\&\[data-panel-group-direction\=vertical\]\>div\]\:rotate-90[data-panel-group-direction="vertical"]
	> div,
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
	transform: translate(var(--tw-translate-x), var(--tw-translate-y))
		rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y))
		scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state="open"] > svg {
	--tw-rotate: 180deg;
}
.\[\&_\.recharts-cartesian-axis-tick_text\]\:fill-muted-foreground
	.recharts-cartesian-axis-tick
	text {
	fill: hsl(var(--muted-foreground));
}
.\[\&_\.recharts-cartesian-grid_line\[stroke\=\'\#ccc\'\]\]\:stroke-border\/50
	.recharts-cartesian-grid
	line[stroke="#ccc"] {
	stroke: hsl(var(--border) / 0.5);
}
.\[\&_\.recharts-curve\.recharts-tooltip-cursor\]\:stroke-border
	.recharts-curve.recharts-tooltip-cursor {
	stroke: hsl(var(--border));
}
.\[\&_\.recharts-dot\[stroke\=\'\#fff\'\]\]\:stroke-transparent
	.recharts-dot[stroke="#fff"] {
	stroke: transparent;
}
.\[\&_\.recharts-layer\]\:outline-none .recharts-layer {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.\[\&_\.recharts-polar-grid_\[stroke\=\'\#ccc\'\]\]\:stroke-border
	.recharts-polar-grid
	[stroke="#ccc"] {
	stroke: hsl(var(--border));
}
.\[\&_\.recharts-radial-bar-background-sector\]\:fill-muted
	.recharts-radial-bar-background-sector,
.\[\&_\.recharts-rectangle\.recharts-tooltip-cursor\]\:fill-muted
	.recharts-rectangle.recharts-tooltip-cursor {
	fill: hsl(var(--muted));
}
.\[\&_\.recharts-reference-line_\[stroke\=\'\#ccc\'\]\]\:stroke-border
	.recharts-reference-line
	[stroke="#ccc"] {
	stroke: hsl(var(--border));
}
.\[\&_\.recharts-sector\[stroke\=\'\#fff\'\]\]\:stroke-transparent
	.recharts-sector[stroke="#fff"] {
	stroke: transparent;
}
.\[\&_\.recharts-sector\]\:outline-none .recharts-sector,
.\[\&_\.recharts-surface\]\:outline-none .recharts-surface {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.\[\&_\[cmdk-group-heading\]\]\:px-2 [cmdk-group-heading] {
	padding-left: .5rem;
	padding-right: .5rem;
}
.\[\&_\[cmdk-group-heading\]\]\:py-1\.5 [cmdk-group-heading] {
	padding-top: .375rem;
	padding-bottom: .375rem;
}
.\[\&_\[cmdk-group-heading\]\]\:text-xs [cmdk-group-heading] {
	font-size: .75rem;
	line-height: 1rem;
}
.\[\&_\[cmdk-group-heading\]\]\:font-medium [cmdk-group-heading] {
	font-weight: 500;
}
.\[\&_\[cmdk-group-heading\]\]\:text-muted-foreground [cmdk-group-heading] {
	color: hsl(var(--muted-foreground));
}
.\[\&_\[cmdk-group\]\:not\(\[hidden\]\)_\~\[cmdk-group\]\]\:pt-0
	[cmdk-group]:not([hidden])
	~ [cmdk-group] {
	padding-top: 0;
}
.\[\&_\[cmdk-group\]\]\:px-2 [cmdk-group] {
	padding-left: .5rem;
	padding-right: .5rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:h-5 [cmdk-input-wrapper] svg {
	height: 1.25rem;
}
.\[\&_\[cmdk-input-wrapper\]_svg\]\:w-5 [cmdk-input-wrapper] svg {
	width: 1.25rem;
}
.\[\&_\[cmdk-input\]\]\:h-12 [cmdk-input] {
	height: 3rem;
}
.\[\&_\[cmdk-item\]\]\:px-2 [cmdk-item] {
	padding-left: .5rem;
	padding-right: .5rem;
}
.\[\&_\[cmdk-item\]\]\:py-3 [cmdk-item] {
	padding-top: .75rem;
	padding-bottom: .75rem;
}
.\[\&_\[cmdk-item\]_svg\]\:h-5 [cmdk-item] svg {
	height: 1.25rem;
}
.\[\&_\[cmdk-item\]_svg\]\:w-5 [cmdk-item] svg {
	width: 1.25rem;
}
.\[\&_p\]\:leading-relaxed p {
	line-height: 1.625;
}
.\[\&_svg\]\:pointer-events-none svg {
	pointer-events: none;
}
.\[\&_svg\]\:size-4 svg {
	width: 1rem;
	height: 1rem;
}
.\[\&_svg\]\:shrink-0 svg {
	flex-shrink: 0;
}
.\[\&_tr\:last-child\]\:border-0 tr:last-child {
	border-width: 0;
}
.\[\&_tr\]\:border-b tr {
	border-bottom-width: 1px;
}
[data-side="left"][data-collapsible="offcanvas"]
	.\[\[data-side\=left\]\[data-collapsible\=offcanvas\]_\&\]\:-right-2 {
	right: -.5rem;
}
[data-side="left"][data-state="collapsed"]
	.\[\[data-side\=left\]\[data-state\=collapsed\]_\&\]\:cursor-e-resize {
	cursor: e-resize;
}
[data-side="left"] .\[\[data-side\=left\]_\&\]\:cursor-w-resize {
	cursor: w-resize;
}
[data-side="right"][data-collapsible="offcanvas"]
	.\[\[data-side\=right\]\[data-collapsible\=offcanvas\]_\&\]\:-left-2 {
	left: -.5rem;
}
[data-side="right"][data-state="collapsed"]
	.\[\[data-side\=right\]\[data-state\=collapsed\]_\&\]\:cursor-w-resize {
	cursor: w-resize;
}
[data-side="right"] .\[\[data-side\=right\]_\&\]\:cursor-e-resize {
	cursor: e-resize;
}
