(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[285],
	{
		900: (e, t, a) => {
			a.d(t, { p: () => n });
			var o = a(8081),
				i = a(2149),
				r = a(7687);
			const n = i.forwardRef((e, t) => {
				const { className: a, type: i, ...n } = e;
				return (0, o.jsx)("input", {
					type: i,
					className: (0, r.cn)(
						"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						a,
					),
					ref: t,
					...n,
				});
			});
			n.displayName = "Input";
		},
		1153: (e, t, a) => {
			a.d(t, { A: () => o });
			const o = (0, a(1018).A)("Menu", [
				["line", { x1: "4", x2: "20", y1: "12", y2: "12", key: "1e0a9i" }],
				["line", { x1: "4", x2: "20", y1: "6", y2: "6", key: "1owob3" }],
				["line", { x1: "4", x2: "20", y1: "18", y2: "18", key: "yk5zj1" }],
			]);
		},
		2950: (e, t, a) => {
			a.d(t, { A: () => o });
			const o = (0, a(1018).A)("Search", [
				["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
				["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
			]);
		},
		3771: (e, t, a) => {
			a.d(t, {
				Ex: () => r,
				Qh: () => o,
				R4: () => i,
				Zd: () => s,
				gL: () => n,
			});
			const o = {
					1: {
						name: "SARAH MARKETING",
						type: "CREATIVE DIRECTOR",
						location: "Los Angeles, CA",
						followers: 892,
						following: 234,
						bio: "Creative director specializing in fashion and lifestyle campaigns",
					},
				},
				i = [
					{
						id: "1",
						src: "/assets/mood_boards/mb1.png",
						alt: "Fashion Campaign",
						title: "Spring Fashion Campaign",
						client: "Luxury Brand Co.",
						budget: 15e3,
						status: "active",
						position: 0,
						description:
							"Spring collection photoshoot featuring new seasonal designs in an urban setting.",
						startDate: "2024-02-15",
						endDate: "2024-02-20",
						location: "Downtown Studio, Los Angeles",
						projectType: "Photography",
						creativesNeeded: [
							"Photographer",
							"Makeup Artist",
							"Stylist",
							"Models (3)",
						],
					},
					{
						id: "2",
						src: "/assets/mood_boards/mb2.png",
						alt: "Product Launch",
						title: "Product Launch Event",
						client: "Tech Startup Inc.",
						budget: 8e3,
						status: "planning",
						position: 1,
						description:
							"Product launch event for new tech gadget with live demonstrations and media coverage.",
						startDate: "2024-03-10",
						endDate: "2024-03-10",
						location: "Convention Center, San Francisco",
						projectType: "Event Photography & Videography",
						creativesNeeded: [
							"Event Photographer",
							"Videographer",
							"Sound Engineer",
						],
					},
					{
						id: "3",
						src: "/assets/creatives_portfolio/crt3.png",
						alt: "Brand Photoshoot",
						title: "Brand Identity Shoot",
						client: "Wellness Brand",
						budget: 12e3,
						status: "completed",
						position: 2,
						description:
							"Brand identity photoshoot for wellness company featuring products and lifestyle imagery.",
						startDate: "2024-01-05",
						endDate: "2024-01-07",
						location: "Beachside Studio, Malibu",
						projectType: "Product & Lifestyle Photography",
						creativesNeeded: ["Photographer", "Stylist", "Models (2)"],
					},
					{
						id: "4",
						src: "/placeholder.svg?height=400&width=400&text=Social Media Campaign",
						alt: "Social Campaign",
						title: "Social Media Campaign",
						client: "Food & Beverage Co.",
						budget: 6e3,
						status: "active",
						position: 3,
						description:
							"Series of social media content featuring food products in various settings.",
						startDate: "2024-02-01",
						endDate: "2024-02-05",
						location: "Culinary Studio, New York",
						projectType: "Food Photography",
						creativesNeeded: [
							"Food Photographer",
							"Food Stylist",
							"Videographer",
						],
					},
					{
						id: "5",
						src: "/placeholder.svg?height=400&width=400&text=Corporate Event",
						alt: "Corporate Event",
						title: "Annual Conference",
						client: "Fortune 500 Company",
						budget: 25e3,
						status: "planning",
						position: 4,
						description:
							"Annual corporate conference with keynote speakers, breakout sessions, and networking events.",
						startDate: "2024-04-15",
						endDate: "2024-04-17",
						location: "Grand Hotel, Chicago",
						projectType: "Event Coverage",
						creativesNeeded: [
							"Event Photographer",
							"Videographer Team (2)",
							"Editor",
						],
					},
					{
						id: "6",
						src: "/placeholder.svg?height=400&width=400&text=Music Video",
						alt: "Music Video",
						title: "Artist Music Video",
						client: "Record Label",
						budget: 18e3,
						status: "completed",
						position: 5,
						description:
							"Music video for upcoming single release featuring urban settings and choreography.",
						startDate: "2023-12-10",
						endDate: "2023-12-12",
						location: "Various Locations, Atlanta",
						projectType: "Music Video Production",
						creativesNeeded: [
							"Director",
							"Cinematographer",
							"Lighting Technician",
							"Editor",
						],
					},
				],
				r = [
					{
						id: "offer1",
						projectTitle: "Spring Fashion Campaign",
						artistName: "Alex Johnson",
						artistId: "1",
						amount: 3500,
						date: "2024-01-10",
						status: "pending",
						projectType: "Photography",
						location: "Los Angeles, CA",
					},
					{
						id: "offer2",
						projectTitle: "Spring Fashion Campaign",
						artistName: "Emma Davis",
						artistId: "4",
						amount: 1200,
						date: "2024-01-12",
						status: "accepted",
						projectType: "Makeup",
						location: "Los Angeles, CA",
					},
					{
						id: "offer3",
						projectTitle: "Spring Fashion Campaign",
						artistName: "James Anderson",
						artistId: "7",
						amount: 800,
						date: "2024-01-14",
						status: "declined",
						projectType: "Styling",
						location: "Los Angeles, CA",
					},
					{
						id: "offer4",
						projectTitle: "Spring Fashion Campaign",
						artistName: "Sarah Wilson",
						artistId: "2",
						amount: 2e3,
						date: "2024-01-15",
						status: "accepted",
						projectType: "Modeling",
						location: "Los Angeles, CA",
					},
					{
						id: "offer5",
						projectTitle: "Spring Fashion Campaign",
						artistName: "Maria Garcia",
						artistId: "8",
						amount: 1800,
						date: "2024-01-16",
						status: "pending",
						projectType: "Modeling",
						location: "Los Angeles, CA",
					},
					{
						id: "offer6",
						projectTitle: "Product Launch Event",
						artistName: "Michael Chen",
						artistId: "3",
						amount: 2800,
						date: "2024-01-08",
						status: "accepted",
						projectType: "Videography",
						location: "San Francisco, CA",
					},
					{
						id: "offer7",
						projectTitle: "Product Launch Event",
						artistName: "David Rodriguez",
						artistId: "5",
						amount: 2200,
						date: "2024-01-09",
						status: "declined",
						projectType: "Photography",
						location: "San Francisco, CA",
					},
					{
						id: "offer8",
						projectTitle: "Product Launch Event",
						artistName: "Carlos Mendez",
						artistId: "11",
						amount: 1500,
						date: "2024-01-11",
						status: "pending",
						projectType: "Sound Engineering",
						location: "San Francisco, CA",
					},
					{
						id: "offer9",
						projectTitle: "Brand Identity Shoot",
						artistName: "Robert Klein",
						artistId: "9",
						amount: 3200,
						date: "2024-01-05",
						status: "accepted",
						projectType: "Photography",
						location: "Malibu, CA",
					},
					{
						id: "offer10",
						projectTitle: "Brand Identity Shoot",
						artistName: "Lisa Thompson",
						artistId: "6",
						amount: 900,
						date: "2024-01-06",
						status: "accepted",
						projectType: "Styling",
						location: "Malibu, CA",
					},
					{
						id: "offer11",
						projectTitle: "Brand Identity Shoot",
						artistName: "Sophie Laurent",
						artistId: "12",
						amount: 1600,
						date: "2024-01-07",
						status: "pending",
						projectType: "Modeling",
						location: "Malibu, CA",
					},
					{
						id: "offer12",
						projectTitle: "Social Media Campaign",
						artistName: "Nina Patel",
						artistId: "10",
						amount: 1800,
						date: "2024-01-03",
						status: "accepted",
						projectType: "Food Photography",
						location: "New York, NY",
					},
					{
						id: "offer13",
						projectTitle: "Social Media Campaign",
						artistName: "James Anderson",
						artistId: "7",
						amount: 1200,
						date: "2024-01-04",
						status: "accepted",
						projectType: "Food Styling",
						location: "New York, NY",
					},
					{
						id: "offer14",
						projectTitle: "Social Media Campaign",
						artistName: "Carlos Mendez",
						artistId: "11",
						amount: 2200,
						date: "2024-01-05",
						status: "pending",
						projectType: "Videography",
						location: "New York, NY",
					},
					{
						id: "offer15",
						projectTitle: "Annual Conference",
						artistName: "Alex Johnson",
						artistId: "1",
						amount: 4500,
						date: "2024-01-20",
						status: "pending",
						projectType: "Event Photography",
						location: "Chicago, IL",
					},
					{
						id: "offer16",
						projectTitle: "Annual Conference",
						artistName: "Michael Chen",
						artistId: "3",
						amount: 5200,
						date: "2024-01-21",
						status: "accepted",
						projectType: "Videography",
						location: "Chicago, IL",
					},
					{
						id: "offer17",
						projectTitle: "Annual Conference",
						artistName: "Carlos Mendez",
						artistId: "11",
						amount: 4800,
						date: "2024-01-22",
						status: "pending",
						projectType: "Videography",
						location: "Chicago, IL",
					},
					{
						id: "offer18",
						projectTitle: "Artist Music Video",
						artistName: "David Rodriguez",
						artistId: "5",
						amount: 6e3,
						date: "2023-11-15",
						status: "accepted",
						projectType: "Director",
						location: "Atlanta, GA",
					},
					{
						id: "offer19",
						projectTitle: "Artist Music Video",
						artistName: "Robert Klein",
						artistId: "9",
						amount: 4200,
						date: "2023-11-16",
						status: "accepted",
						projectType: "Cinematography",
						location: "Atlanta, GA",
					},
					{
						id: "offer20",
						projectTitle: "Artist Music Video",
						artistName: "Nina Patel",
						artistId: "10",
						amount: 2800,
						date: "2023-11-17",
						status: "accepted",
						projectType: "Lighting",
						location: "Atlanta, GA",
					},
				],
				n = [
					{
						id: "budget1",
						projectId: "1",
						projectTitle: "Spring Fashion Campaign",
						category: "Photography",
						amount: 3500,
						date: "2024-01-15",
						status: "paid",
						paymentMethod: "Visa ****1234",
					},
					{
						id: "budget2",
						projectId: "2",
						projectTitle: "Product Launch Event",
						category: "Videography",
						amount: 2800,
						date: "2024-01-20",
						status: "scheduled",
						paymentMethod: "Mastercard ****5678",
					},
					{
						id: "budget3",
						projectId: "1",
						projectTitle: "Spring Fashion Campaign",
						category: "Makeup Artist",
						amount: 1200,
						date: "2024-01-18",
						status: "pending",
						paymentMethod: "Visa ****1234",
					},
				],
				s = [
					{
						id: "collab1",
						projectTitle: "Winter Fashion Campaign",
						artistName: "Alex Johnson",
						artistId: "1",
						artistAvatar: "/placeholder.svg?height=60&width=60&text=AJ",
						completedDate: "2023-12-15",
						budget: 4500,
						rating: 5,
						review:
							"Exceptional work! Alex delivered stunning photos that exceeded our expectations. Professional, punctual, and creative.",
						projectType: "Photography",
					},
					{
						id: "collab2",
						projectTitle: "Corporate Headshots",
						artistName: "Emma Davis",
						artistId: "4",
						artistAvatar: "/placeholder.svg?height=60&width=60&text=ED",
						completedDate: "2023-11-28",
						budget: 1800,
						rating: 4,
						review:
							"Great makeup work for our corporate shoot. Very professional and detail-oriented.",
						projectType: "Makeup",
					},
					{
						id: "collab3",
						projectTitle: "Product Launch Video",
						artistName: "Michael Chen",
						artistId: "3",
						artistAvatar: "/placeholder.svg?height=60&width=60&text=MC",
						completedDate: "2023-10-20",
						budget: 6e3,
						rating: 5,
						review:
							"Outstanding videography work. Michael captured our product perfectly and delivered on time.",
						projectType: "Videography",
					},
				];
		},
		5160: (e, t, a) => {
			a.d(t, { $: () => c, r: () => d });
			var o = a(8081),
				i = a(2149),
				r = a(3629),
				n = a(3484),
				s = a(7687);
			const d = (0, n.F)(
					"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
					{
						variants: {
							variant: {
								default:
									"bg-primary text-primary-foreground hover:bg-primary/90",
								destructive:
									"bg-destructive text-destructive-foreground hover:bg-destructive/90",
								outline:
									"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
								secondary:
									"bg-secondary text-secondary-foreground hover:bg-secondary/80",
								ghost: "hover:bg-accent hover:text-accent-foreground",
								link: "text-primary underline-offset-4 hover:underline",
							},
							size: {
								default: "h-10 px-4 py-2",
								sm: "h-9 rounded-md px-3",
								lg: "h-11 rounded-md px-8",
								icon: "h-10 w-10",
							},
						},
						defaultVariants: { variant: "default", size: "default" },
					},
				),
				c = i.forwardRef((e, t) => {
					const {
							className: a,
							variant: i,
							size: n,
							asChild: c = !1,
							...l
						} = e,
						p = c ? r.DX : "button";
					return (0, o.jsx)(p, {
						className: (0, s.cn)(d({ variant: i, size: n, className: a })),
						ref: t,
						...l,
					});
				});
			c.displayName = "Button";
		},
		5904: (e, t, a) => {
			a.d(t, {
				bq: () => u,
				eb: () => f,
				gC: () => m,
				l6: () => l,
				yv: () => p,
			});
			var o = a(8081),
				i = a(2149),
				r = a(4290),
				n = a(392),
				s = a(6801),
				d = a(6722),
				c = a(7687);
			const l = r.bL;
			r.YJ;
			const p = r.WT,
				u = i.forwardRef((e, t) => {
					const { className: a, children: i, ...s } = e;
					return (0, o.jsxs)(r.l9, {
						ref: t,
						className: (0, c.cn)(
							"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
							a,
						),
						...s,
						children: [
							i,
							(0, o.jsx)(r.In, {
								asChild: !0,
								children: (0, o.jsx)(n.A, { className: "h-4 w-4 opacity-50" }),
							}),
						],
					});
				});
			u.displayName = r.l9.displayName;
			const g = i.forwardRef((e, t) => {
				const { className: a, ...i } = e;
				return (0, o.jsx)(r.PP, {
					ref: t,
					className: (0, c.cn)(
						"flex cursor-default items-center justify-center py-1",
						a,
					),
					...i,
					children: (0, o.jsx)(s.A, { className: "h-4 w-4" }),
				});
			});
			g.displayName = r.PP.displayName;
			const h = i.forwardRef((e, t) => {
				const { className: a, ...i } = e;
				return (0, o.jsx)(r.wn, {
					ref: t,
					className: (0, c.cn)(
						"flex cursor-default items-center justify-center py-1",
						a,
					),
					...i,
					children: (0, o.jsx)(n.A, { className: "h-4 w-4" }),
				});
			});
			h.displayName = r.wn.displayName;
			const m = i.forwardRef((e, t) => {
				const { className: a, children: i, position: n = "popper", ...s } = e;
				return (0, o.jsx)(r.ZL, {
					children: (0, o.jsxs)(r.UC, {
						ref: t,
						className: (0, c.cn)(
							"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
							"popper" === n &&
								"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
							a,
						),
						position: n,
						...s,
						children: [
							(0, o.jsx)(g, {}),
							(0, o.jsx)(r.LM, {
								className: (0, c.cn)(
									"p-1",
									"popper" === n &&
										"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]",
								),
								children: i,
							}),
							(0, o.jsx)(h, {}),
						],
					}),
				});
			});
			(m.displayName = r.UC.displayName),
				(i.forwardRef((e, t) => {
					const { className: a, ...i } = e;
					return (0, o.jsx)(r.JU, {
						ref: t,
						className: (0, c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold", a),
						...i,
					});
				}).displayName = r.JU.displayName);
			const f = i.forwardRef((e, t) => {
				const { className: a, children: i, ...n } = e;
				return (0, o.jsxs)(r.q7, {
					ref: t,
					className: (0, c.cn)(
						"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
						a,
					),
					...n,
					children: [
						(0, o.jsx)("span", {
							className:
								"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
							children: (0, o.jsx)(r.VF, {
								children: (0, o.jsx)(d.A, { className: "h-4 w-4" }),
							}),
						}),
						(0, o.jsx)(r.p4, { children: i }),
					],
				});
			});
			(f.displayName = r.q7.displayName),
				(i.forwardRef((e, t) => {
					const { className: a, ...i } = e;
					return (0, o.jsx)(r.wv, {
						ref: t,
						className: (0, c.cn)("-mx-1 my-1 h-px bg-muted", a),
						...i,
					});
				}).displayName = r.wv.displayName);
		},
		5993: (e, t, a) => {
			a.d(t, { A: () => i });
			var o = a(8081);
			const i = (e) => {
				const { children: t, className: a } = e;
				return (0, o.jsx)("div", {
					className: "max-w-4xl mx-auto px-6 py-16 ".concat(a || "").trim(),
					children: t,
				});
			};
		},
		6425: (e, t, a) => {
			a.d(t, { A: () => o });
			const o = (0, a(1018).A)("CirclePlus", [
				["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
				["path", { d: "M8 12h8", key: "1wcyev" }],
				["path", { d: "M12 8v8", key: "napkw2" }],
			]);
		},
		7687: (e, t, a) => {
			a.d(t, { cn: () => r });
			var o = a(6522),
				i = a(4483);
			function r() {
				for (var e = arguments.length, t = Array(e), a = 0; a < e; a++)
					t[a] = arguments[a];
				return (0, i.QP)((0, o.$)(t));
			}
		},
		7753: (e, t, a) => {
			a.d(t, { A: () => c });
			var o = a(8081),
				i = a(7950),
				r = a.n(i),
				n = a(5160),
				s = a(2950),
				d = a(1153);
			const c = () =>
				(0, o.jsx)("header", {
					className: "border-b border-gray-100",
					children: (0, o.jsx)("div", {
						className: "max-w-4xl mx-auto px-6 py-6",
						children: (0, o.jsxs)("div", {
							className: "flex items-center justify-between",
							children: [
								(0, o.jsx)(r(), {
									href: "/",
									className: "text-2xl font-light tracking-wide",
									children: "DUA",
								}),
								(0, o.jsxs)("nav", {
									className: "hidden md:flex items-center space-x-8 text-sm",
									children: [
										(0, o.jsx)(r(), {
											href: "/",
											className:
												"text-gray-600 hover:text-black transition-colors",
											children: "Search",
										}),
										(0, o.jsx)("span", {
											className: "text-black",
											children: "Portfolio",
										}),
										(0, o.jsx)("span", {
											className: "text-gray-600",
											children: "About",
										}),
										(0, o.jsx)("span", {
											className: "text-gray-600",
											children: "Contact",
										}),
										(0, o.jsx)(s.A, { className: "w-4 h-4 text-gray-600" }),
									],
								}),
								(0, o.jsx)(n.$, {
									variant: "ghost",
									size: "icon",
									className: "md:hidden",
									children: (0, o.jsx)(d.A, { className: "h-5 w-5" }),
								}),
							],
						}),
					}),
				});
		},
		9651: (e, t, a) => {
			var o = a(4571);
			a.o(o, "useRouter") && a.d(t, { useRouter: () => o.useRouter });
		},
	},
]);
