(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[792],
	{
		8: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "escapeStringRegexp", {
					enumerable: !0,
					get: () => o,
				});
			const r = /[|\\{}()[\]^$+*?.-]/,
				n = /[|\\{}()[\]^$+*?.-]/g;
			function o(e) {
				return r.test(e) ? e.replace(n, "\\$&") : e;
			}
		},
		58: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				r(4011),
				(self.__next_set_public_path__ = (e) => {
					r.p = e;
				}),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		109: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "RedirectStatusCode", {
					enumerable: !0,
					get: () => r,
				});
			var r = ((e) => (
				(e[(e.SeeOther = 303)] = "SeeOther"),
				(e[(e.TemporaryRedirect = 307)] = "TemporaryRedirect"),
				(e[(e.PermanentRedirect = 308)] = "PermanentRedirect"),
				e
			))({});
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		121: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "BloomFilter", {
					enumerable: !0,
					get: () => r,
				});
			class r {
				static from(e, t) {
					void 0 === t && (t = 1e-4);
					const n = new r(e.length, t);
					for (const t of e) n.add(t);
					return n;
				}
				export() {
					return {
						numItems: this.numItems,
						errorRate: this.errorRate,
						numBits: this.numBits,
						numHashes: this.numHashes,
						bitArray: this.bitArray,
					};
				}
				import(e) {
					(this.numItems = e.numItems),
						(this.errorRate = e.errorRate),
						(this.numBits = e.numBits),
						(this.numHashes = e.numHashes),
						(this.bitArray = e.bitArray);
				}
				add(e) {
					this.getHashValues(e).forEach((e) => {
						this.bitArray[e] = 1;
					});
				}
				contains(e) {
					return this.getHashValues(e).every((e) => this.bitArray[e]);
				}
				getHashValues(e) {
					const t = [];
					for (let r = 1; r <= this.numHashes; r++) {
						const n =
							((e) => {
								let t = 0;
								for (let r = 0; r < e.length; r++)
									(t = Math.imul(t ^ e.charCodeAt(r), 0x5bd1e995)),
										(t ^= t >>> 13),
										(t = Math.imul(t, 0x5bd1e995));
								return t >>> 0;
							})("" + e + r) % this.numBits;
						t.push(n);
					}
					return t;
				}
				constructor(e, t = 1e-4) {
					(this.numItems = e),
						(this.errorRate = t),
						(this.numBits = Math.ceil(
							-(e * Math.log(t)) / (Math.log(2) * Math.log(2)),
						)),
						(this.numHashes = Math.ceil((this.numBits / e) * Math.log(2))),
						(this.bitArray = Array(this.numBits).fill(0));
				}
			}
		},
		144: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					default: () => b,
					handleClientScriptLoad: () => _,
					initScriptLoader: () => m,
				});
			const n = r(1532),
				o = r(8781),
				a = r(5640),
				i = n._(r(7897)),
				l = o._(r(148)),
				u = r(9283),
				s = r(9343),
				c = r(9307),
				f = new Map(),
				d = new Set(),
				p = (e) => {
					if (i.default.preinit) {
						e.forEach((e) => {
							i.default.preinit(e, { as: "style" });
						});
						return;
					}
					{
						const t = document.head;
						e.forEach((e) => {
							const r = document.createElement("link");
							(r.type = "text/css"),
								(r.rel = "stylesheet"),
								(r.href = e),
								t.appendChild(r);
						});
					}
				},
				h = (e) => {
					const {
							src: t,
							id: r,
							onLoad: n = () => {},
							onReady: o = null,
							dangerouslySetInnerHTML: a,
							children: i = "",
							strategy: l = "afterInteractive",
							onError: u,
							stylesheets: c,
						} = e,
						h = r || t;
					if (h && d.has(h)) return;
					if (f.has(t)) {
						d.add(h), f.get(t).then(n, u);
						return;
					}
					const _ = () => {
							o && o(), d.add(h);
						},
						m = document.createElement("script"),
						g = new Promise((e, t) => {
							m.addEventListener("load", function (t) {
								e(), n && n.call(this, t), _();
							}),
								m.addEventListener("error", (e) => {
									t(e);
								});
						}).catch((e) => {
							u && u(e);
						});
					a
						? ((m.innerHTML = a.__html || ""), _())
						: i
							? ((m.textContent =
									"string" == typeof i
										? i
										: Array.isArray(i)
											? i.join("")
											: ""),
								_())
							: t && ((m.src = t), f.set(t, g)),
						(0, s.setAttributesFromProps)(m, e),
						"worker" === l && m.setAttribute("type", "text/partytown"),
						m.setAttribute("data-nscript", l),
						c && p(c),
						document.body.appendChild(m);
				};
			function _(e) {
				const { strategy: t = "afterInteractive" } = e;
				"lazyOnload" === t
					? window.addEventListener("load", () => {
							(0, c.requestIdleCallback)(() => h(e));
						})
					: h(e);
			}
			function m(e) {
				e.forEach(_),
					[
						...document.querySelectorAll('[data-nscript="beforeInteractive"]'),
						...document.querySelectorAll('[data-nscript="beforePageRender"]'),
					].forEach((e) => {
						const t = e.id || e.getAttribute("src");
						d.add(t);
					});
			}
			function g(e) {
				const {
						id: t,
						src: r = "",
						onLoad: n = () => {},
						onReady: o = null,
						strategy: s = "afterInteractive",
						onError: f,
						stylesheets: p,
						..._
					} = e,
					{
						updateScripts: m,
						scripts: g,
						getIsSsr: b,
						appDir: E,
						nonce: y,
					} = (0, l.useContext)(u.HeadManagerContext),
					P = (0, l.useRef)(!1);
				(0, l.useEffect)(() => {
					const e = t || r;
					P.current || (o && e && d.has(e) && o(), (P.current = !0));
				}, [o, t, r]);
				const R = (0, l.useRef)(!1);
				if (
					((0, l.useEffect)(() => {
						if (!R.current) {
							if ("afterInteractive" === s) h(e);
							else if ("lazyOnload" === s)
								"complete" === document.readyState
									? (0, c.requestIdleCallback)(() => h(e))
									: window.addEventListener("load", () => {
											(0, c.requestIdleCallback)(() => h(e));
										});
							R.current = !0;
						}
					}, [e, s]),
					("beforeInteractive" === s || "worker" === s) &&
						(m
							? ((g[s] = (g[s] || []).concat([
									{ id: t, src: r, onLoad: n, onReady: o, onError: f, ..._ },
								])),
								m(g))
							: b && b()
								? d.add(t || r)
								: b && !b() && h(e)),
					E)
				) {
					if (
						(p &&
							p.forEach((e) => {
								i.default.preinit(e, { as: "style" });
							}),
						"beforeInteractive" === s)
					)
						return r
							? (i.default.preload(
									r,
									_.integrity
										? {
												as: "script",
												integrity: _.integrity,
												nonce: y,
												crossOrigin: _.crossOrigin,
											}
										: { as: "script", nonce: y, crossOrigin: _.crossOrigin },
								),
								(0, a.jsx)("script", {
									nonce: y,
									dangerouslySetInnerHTML: {
										__html:
											"(self.__next_s=self.__next_s||[]).push(" +
											JSON.stringify([r, { ..._, id: t }]) +
											")",
									},
								}))
							: (_.dangerouslySetInnerHTML &&
									((_.children = _.dangerouslySetInnerHTML.__html),
									delete _.dangerouslySetInnerHTML),
								(0, a.jsx)("script", {
									nonce: y,
									dangerouslySetInnerHTML: {
										__html:
											"(self.__next_s=self.__next_s||[]).push(" +
											JSON.stringify([0, { ..._, id: t }]) +
											")",
									},
								}));
					"afterInteractive" === s &&
						r &&
						i.default.preload(
							r,
							_.integrity
								? {
										as: "script",
										integrity: _.integrity,
										nonce: y,
										crossOrigin: _.crossOrigin,
									}
								: { as: "script", nonce: y, crossOrigin: _.crossOrigin },
						);
				}
				return null;
			}
			Object.defineProperty(g, "__nextScript", { value: !0 });
			const b = g;
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		170: (e, t) => {
			function r(e) {
				return e.replace(/\/$/, "") || "/";
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "removeTrailingSlash", {
					enumerable: !0,
					get: () => r,
				});
		},
		258: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					getNamedMiddlewareRegex: () => _,
					getNamedRouteRegex: () => h,
					getRouteRegex: () => f,
					parseParameter: () => u,
				});
			const n = r(6786),
				o = r(7496),
				a = r(8),
				i = r(170),
				l = /^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;
			function u(e) {
				const t = e.match(l);
				return t ? s(t[2]) : s(e);
			}
			function s(e) {
				const t = e.startsWith("[") && e.endsWith("]");
				t && (e = e.slice(1, -1));
				const r = e.startsWith("...");
				return r && (e = e.slice(3)), { key: e, repeat: r, optional: t };
			}
			function c(e, t, r) {
				let n = {},
					u = 1,
					c = [];
				for (const f of (0, i.removeTrailingSlash)(e).slice(1).split("/")) {
					const e = o.INTERCEPTION_ROUTE_MARKERS.find((e) => f.startsWith(e)),
						i = f.match(l);
					if (e && i && i[2]) {
						const { key: t, optional: r, repeat: o } = s(i[2]);
						(n[t] = { pos: u++, repeat: o, optional: r }),
							c.push("/" + (0, a.escapeStringRegexp)(e) + "([^/]+?)");
					} else if (i && i[2]) {
						const { key: e, repeat: t, optional: o } = s(i[2]);
						(n[e] = { pos: u++, repeat: t, optional: o }),
							r && i[1] && c.push("/" + (0, a.escapeStringRegexp)(i[1]));
						let l = t ? (o ? "(?:/(.+?))?" : "/(.+?)") : "/([^/]+?)";
						r && i[1] && (l = l.substring(1)), c.push(l);
					} else c.push("/" + (0, a.escapeStringRegexp)(f));
					t && i && i[3] && c.push((0, a.escapeStringRegexp)(i[3]));
				}
				return { parameterizedRoute: c.join(""), groups: n };
			}
			function f(e, t) {
				let {
						includeSuffix: r = !1,
						includePrefix: n = !1,
						excludeOptionalTrailingSlash: o = !1,
					} = void 0 === t ? {} : t,
					{ parameterizedRoute: a, groups: i } = c(e, r, n),
					l = a;
				return o || (l += "(?:/)?"), { re: RegExp("^" + l + "$"), groups: i };
			}
			function d(e) {
				let t,
					{
						interceptionMarker: r,
						getSafeRouteKey: n,
						segment: o,
						routeKeys: i,
						keyPrefix: l,
						backreferenceDuplicateKeys: u,
					} = e,
					{ key: c, optional: f, repeat: d } = s(o),
					p = c.replace(/\W/g, "");
				l && (p = "" + l + p);
				let h = !1;
				(0 === p.length || p.length > 30) && (h = !0),
					isNaN(Number.parseInt(p.slice(0, 1))) || (h = !0),
					h && (p = n());
				const _ = p in i;
				l ? (i[p] = "" + l + c) : (i[p] = c);
				const m = r ? (0, a.escapeStringRegexp)(r) : "";
				return (
					(t =
						_ && u
							? "\\k<" + p + ">"
							: d
								? "(?<" + p + ">.+?)"
								: "(?<" + p + ">[^/]+?)"),
					f ? "(?:/" + m + t + ")?" : "/" + m + t
				);
			}
			function p(e, t, r, u, s) {
				let c;
				const f =
						((c = 0),
						() => {
							let e = "",
								t = ++c;
							while (t > 0)
								(e += String.fromCharCode(97 + ((t - 1) % 26))),
									(t = Math.floor((t - 1) / 26));
							return e;
						}),
					p = {},
					h = [];
				for (const c of (0, i.removeTrailingSlash)(e).slice(1).split("/")) {
					const e = o.INTERCEPTION_ROUTE_MARKERS.some((e) => c.startsWith(e)),
						i = c.match(l);
					if (e && i && i[2])
						h.push(
							d({
								getSafeRouteKey: f,
								interceptionMarker: i[1],
								segment: i[2],
								routeKeys: p,
								keyPrefix: t ? n.NEXT_INTERCEPTION_MARKER_PREFIX : void 0,
								backreferenceDuplicateKeys: s,
							}),
						);
					else if (i && i[2]) {
						u && i[1] && h.push("/" + (0, a.escapeStringRegexp)(i[1]));
						let e = d({
							getSafeRouteKey: f,
							segment: i[2],
							routeKeys: p,
							keyPrefix: t ? n.NEXT_QUERY_PARAM_PREFIX : void 0,
							backreferenceDuplicateKeys: s,
						});
						u && i[1] && (e = e.substring(1)), h.push(e);
					} else h.push("/" + (0, a.escapeStringRegexp)(c));
					r && i && i[3] && h.push((0, a.escapeStringRegexp)(i[3]));
				}
				return { namedParameterizedRoute: h.join(""), routeKeys: p };
			}
			function h(e, t) {
				var r, n, o;
				let a = p(
						e,
						t.prefixRouteKeys,
						null != (r = t.includeSuffix) && r,
						null != (n = t.includePrefix) && n,
						null != (o = t.backreferenceDuplicateKeys) && o,
					),
					i = a.namedParameterizedRoute;
				return (
					t.excludeOptionalTrailingSlash || (i += "(?:/)?"),
					{ ...f(e, t), namedRegex: "^" + i + "$", routeKeys: a.routeKeys }
				);
			}
			function _(e, t) {
				const { parameterizedRoute: r } = c(e, !1, !1),
					{ catchAll: n = !0 } = t;
				if ("/" === r) return { namedRegex: "^/" + (n ? ".*" : "") + "$" };
				const { namedParameterizedRoute: o } = p(e, !1, !1, !1, !1);
				return { namedRegex: "^" + o + (n ? "(?:(/.*)?)" : "") + "$" };
			}
		},
		311: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { BailoutToCSRError: () => n, isBailoutToCSRError: () => o });
			const r = "BAILOUT_TO_CLIENT_SIDE_RENDERING";
			class n extends Error {
				constructor(e) {
					super("Bail out to client-side rendering: " + e),
						(this.reason = e),
						(this.digest = r);
				}
			}
			function o(e) {
				return (
					"object" == typeof e && null !== e && "digest" in e && e.digest === r
				);
			}
		},
		461: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					PathnameContextProviderAdapter: () => p,
					adaptForAppRouterInstance: () => c,
					adaptForPathParams: () => d,
					adaptForSearchParams: () => f,
				});
			const n = r(8781),
				o = r(5640),
				a = n._(r(148)),
				i = r(7439),
				l = r(4233),
				u = r(905),
				s = r(258);
			function c(e) {
				return {
					back() {
						e.back();
					},
					forward() {
						e.forward();
					},
					refresh() {
						e.reload();
					},
					hmrRefresh() {},
					push(t, r) {
						const { scroll: n } = void 0 === r ? {} : r;
						e.push(t, void 0, { scroll: n });
					},
					replace(t, r) {
						const { scroll: n } = void 0 === r ? {} : r;
						e.replace(t, void 0, { scroll: n });
					},
					prefetch(t) {
						e.prefetch(t);
					},
				};
			}
			function f(e) {
				return e.isReady && e.query
					? (0, u.asPathToSearchParams)(e.asPath)
					: new URLSearchParams();
			}
			function d(e) {
				if (!e.isReady || !e.query) return null;
				const t = {};
				for (const r of Object.keys((0, s.getRouteRegex)(e.pathname).groups))
					t[r] = e.query[r];
				return t;
			}
			function p(e) {
				const { children: t, router: r, ...n } = e,
					u = (0, a.useRef)(n.isAutoExport),
					s = (0, a.useMemo)(() => {
						let e;
						const t = u.current;
						if (
							(t && (u.current = !1),
							(0, l.isDynamicRoute)(r.pathname) &&
								(r.isFallback || (t && !r.isReady)))
						)
							return null;
						try {
							e = new URL(r.asPath, "http://f");
						} catch (e) {
							return "/";
						}
						return e.pathname;
					}, [r.asPath, r.isFallback, r.isReady, r.pathname]);
				return (0, o.jsx)(i.PathnameContext.Provider, {
					value: s,
					children: t,
				});
			}
		},
		592: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					createKey: () => G,
					default: () => z,
					matchesMiddleware: () => D,
				});
			const n = r(1532),
				o = r(8781),
				a = r(170),
				i = r(6732),
				l = r(144),
				u = o._(r(8718)),
				s = r(3255),
				c = r(5810),
				f = n._(r(8771)),
				d = r(2150),
				p = r(4839),
				h = r(7529);
			r(8126);
			const _ = r(4083),
				m = r(258),
				g = r(7524);
			r(9372);
			const b = r(8618),
				E = r(5475),
				y = r(3872),
				P = r(9861),
				R = r(5856),
				v = r(8603),
				O = r(2013),
				S = r(7868),
				j = r(4747),
				T = r(864),
				A = r(1499),
				C = r(4529),
				w = r(2035),
				I = r(5346),
				N = r(6369),
				x = r(6192),
				M = r(6786);
			function L() {
				return Object.assign(
					Object.defineProperty(Error("Route Cancelled"), "__NEXT_ERROR_CODE", {
						value: "E315",
						enumerable: !1,
						configurable: !0,
					}),
					{ cancelled: !0 },
				);
			}
			async function D(e) {
				const t = await Promise.resolve(e.router.pageLoader.getMiddleware());
				if (!t) return !1;
				const { pathname: r } = (0, b.parsePath)(e.asPath),
					n = (0, v.hasBasePath)(r) ? (0, P.removeBasePath)(r) : r,
					o = (0, R.addBasePath)((0, E.addLocale)(n, e.locale));
				return t.some((e) => new RegExp(e.regexp).test(o));
			}
			function U(e) {
				const t = (0, d.getLocationOrigin)();
				return e.startsWith(t) ? e.substring(t.length) : e;
			}
			function k(e, t, r) {
				let [n, o] = (0, O.resolveHref)(e, t, !0),
					a = (0, d.getLocationOrigin)(),
					i = n.startsWith(a),
					l = o && o.startsWith(a);
				(n = U(n)), (o = o ? U(o) : o);
				const u = i ? n : (0, R.addBasePath)(n),
					s = r ? U((0, O.resolveHref)(e, r)) : o || n;
				return { url: u, as: l ? s : (0, R.addBasePath)(s) };
			}
			function F(e, t) {
				const r = (0, a.removeTrailingSlash)((0, s.denormalizePagePath)(e));
				return "/404" === r || "/_error" === r
					? e
					: (t.includes(r) ||
							t.some((t) => {
								if (
									(0, p.isDynamicRoute)(t) &&
									(0, m.getRouteRegex)(t).re.test(r)
								)
									return (e = t), !0;
							}),
						(0, a.removeTrailingSlash)(e));
			}
			async function B(e) {
				if (!(await D(e)) || !e.fetchData) return null;
				const t = await e.fetchData(),
					r = await ((e, t, r) => {
						let n = {
								basePath: r.router.basePath,
								i18n: { locales: r.router.locales },
								trailingSlash: !1,
							},
							o = t.headers.get("x-nextjs-rewrite"),
							l = o || t.headers.get("x-nextjs-matched-path"),
							u = t.headers.get(M.MATCHED_PATH_HEADER);
						if (
							(!u ||
								l ||
								u.includes("__next_data_catchall") ||
								u.includes("/_error") ||
								u.includes("/404") ||
								(l = u),
							l)
						) {
							if (l.startsWith("/")) {
								let t = (0, h.parseRelativeUrl)(l),
									u = (0, j.getNextPathnameInfo)(t.pathname, {
										nextConfig: n,
										parseData: !0,
									}),
									s = (0, a.removeTrailingSlash)(u.pathname);
								return Promise.all([
									r.router.pageLoader.getPageList(),
									(0, i.getClientBuildManifest)(),
								]).then((a) => {
									let [i, { __rewrites: l }] = a,
										f = (0, E.addLocale)(u.pathname, u.locale);
									if (
										(0, p.isDynamicRoute)(f) ||
										(!o &&
											i.includes(
												(0, c.normalizeLocalePath)(
													(0, P.removeBasePath)(f),
													r.router.locales,
												).pathname,
											))
									) {
										const r = (0, j.getNextPathnameInfo)(
											(0, h.parseRelativeUrl)(e).pathname,
											{ nextConfig: n, parseData: !0 },
										);
										t.pathname = f = (0, R.addBasePath)(r.pathname);
									}
									if (!i.includes(s)) {
										const e = F(s, i);
										e !== s && (s = e);
									}
									const d = i.includes(s)
										? s
										: F(
												(0, c.normalizeLocalePath)(
													(0, P.removeBasePath)(t.pathname),
													r.router.locales,
												).pathname,
												i,
											);
									if ((0, p.isDynamicRoute)(d)) {
										const e = (0, _.getRouteMatcher)((0, m.getRouteRegex)(d))(
											f,
										);
										Object.assign(t.query, e || {});
									}
									return { type: "rewrite", parsedAs: t, resolvedHref: d };
								});
							}
							const t = (0, b.parsePath)(e);
							return Promise.resolve({
								type: "redirect-external",
								destination:
									"" +
									(0, T.formatNextPathnameInfo)({
										...(0, j.getNextPathnameInfo)(t.pathname, {
											nextConfig: n,
											parseData: !0,
										}),
										defaultLocale: r.router.defaultLocale,
										buildId: "",
									}) +
									t.query +
									t.hash,
							});
						}
						const s = t.headers.get("x-nextjs-redirect");
						if (s) {
							if (s.startsWith("/")) {
								const e = (0, b.parsePath)(s),
									t = (0, T.formatNextPathnameInfo)({
										...(0, j.getNextPathnameInfo)(e.pathname, {
											nextConfig: n,
											parseData: !0,
										}),
										defaultLocale: r.router.defaultLocale,
										buildId: "",
									});
								return Promise.resolve({
									type: "redirect-internal",
									newAs: "" + t + e.query + e.hash,
									newUrl: "" + t + e.query + e.hash,
								});
							}
							return Promise.resolve({
								type: "redirect-external",
								destination: s,
							});
						}
						return Promise.resolve({ type: "next" });
					})(t.dataHref, t.response, e);
				return {
					dataHref: t.dataHref,
					json: t.json,
					response: t.response,
					text: t.text,
					cacheKey: t.cacheKey,
					effect: r,
				};
			}
			const H = Symbol("SSG_DATA_NOT_FOUND");
			function X(e) {
				try {
					return JSON.parse(e);
				} catch (e) {
					return null;
				}
			}
			function W(e) {
				const {
						dataHref: t,
						inflightCache: r,
						isPrefetch: n,
						hasMiddleware: o,
						isServerRender: a,
						parseJSON: l,
						persistCache: u,
						isBackground: s,
						unstable_skipClientCache: c,
					} = e,
					{ href: f } = new URL(t, window.location.href),
					d = (e) => {
						var s;
						return (function e(t, r, n) {
							return fetch(t, {
								credentials: "same-origin",
								method: n.method || "GET",
								headers: Object.assign({}, n.headers, { "x-nextjs-data": "1" }),
							}).then((o) =>
								!o.ok && r > 1 && o.status >= 500 ? e(t, r - 1, n) : o,
							);
						})(t, a ? 3 : 1, {
							headers: Object.assign(
								{},
								n ? { purpose: "prefetch" } : {},
								n && o ? { "x-middleware-prefetch": "1" } : {},
								{},
							),
							method: null != (s = null == e ? void 0 : e.method) ? s : "GET",
						})
							.then((r) =>
								r.ok && (null == e ? void 0 : e.method) === "HEAD"
									? {
											dataHref: t,
											response: r,
											text: "",
											json: {},
											cacheKey: f,
										}
									: r.text().then((e) => {
											if (!r.ok) {
												if (o && [301, 302, 307, 308].includes(r.status))
													return {
														dataHref: t,
														response: r,
														text: e,
														json: {},
														cacheKey: f,
													};
												if (404 === r.status) {
													var n;
													if (null == (n = X(e)) ? void 0 : n.notFound)
														return {
															dataHref: t,
															json: { notFound: H },
															response: r,
															text: e,
															cacheKey: f,
														};
												}
												const l = Object.defineProperty(
													Error("Failed to load static props"),
													"__NEXT_ERROR_CODE",
													{ value: "E124", enumerable: !1, configurable: !0 },
												);
												throw (a || (0, i.markAssetError)(l), l);
											}
											return {
												dataHref: t,
												json: l ? X(e) : null,
												response: r,
												text: e,
												cacheKey: f,
											};
										}),
							)
							.then(
								(e) => (
									(u &&
										"no-cache" !==
											e.response.headers.get("x-middleware-cache")) ||
										delete r[f],
									e
								),
							)
							.catch((e) => {
								throw (
									(c || delete r[f],
									("Failed to fetch" === e.message ||
										"NetworkError when attempting to fetch resource." ===
											e.message ||
										"Load failed" === e.message) &&
										(0, i.markAssetError)(e),
									e)
								);
							});
					};
				return c && u
					? d({}).then(
							(e) => (
								"no-cache" !== e.response.headers.get("x-middleware-cache") &&
									(r[f] = Promise.resolve(e)),
								e
							),
						)
					: void 0 !== r[f]
						? r[f]
						: (r[f] = d(s ? { method: "HEAD" } : {}));
			}
			function G() {
				return Math.random().toString(36).slice(2, 10);
			}
			function q(e) {
				const { url: t, router: r } = e;
				if (t === (0, R.addBasePath)((0, E.addLocale)(r.asPath, r.locale)))
					throw Object.defineProperty(
						Error(
							"Invariant: attempted to hard navigate to the same URL " +
								t +
								" " +
								location.href,
						),
						"__NEXT_ERROR_CODE",
						{ value: "E282", enumerable: !1, configurable: !0 },
					);
				window.location.href = t;
			}
			const V = (e) => {
				let { route: t, router: r } = e,
					n = !1,
					o = (r.clc = () => {
						n = !0;
					});
				return () => {
					if (n) {
						const e = Object.defineProperty(
							Error('Abort fetching component for route: "' + t + '"'),
							"__NEXT_ERROR_CODE",
							{ value: "E483", enumerable: !1, configurable: !0 },
						);
						throw ((e.cancelled = !0), e);
					}
					o === r.clc && (r.clc = null);
				};
			};
			class z {
				reload() {
					window.location.reload();
				}
				back() {
					window.history.back();
				}
				forward() {
					window.history.forward();
				}
				push(e, t, r) {
					return (
						void 0 === r && (r = {}),
						({ url: e, as: t } = k(this, e, t)),
						this.change("pushState", e, t, r)
					);
				}
				replace(e, t, r) {
					return (
						void 0 === r && (r = {}),
						({ url: e, as: t } = k(this, e, t)),
						this.change("replaceState", e, t, r)
					);
				}
				async _bfl(e, t, n, o) {
					{
						if (!this._bfl_s && !this._bfl_d) {
							let t, a;
							const { BloomFilter: l } = r(121);
							try {
								({ __routerFilterStatic: t, __routerFilterDynamic: a } =
									await (0, i.getClientBuildManifest)());
							} catch (t) {
								if ((console.error(t), o)) return !0;
								return (
									q({
										url: (0, R.addBasePath)(
											(0, E.addLocale)(e, n || this.locale, this.defaultLocale),
										),
										router: this,
									}),
									new Promise(() => {})
								);
							}
							(null == t ? void 0 : t.numHashes) &&
								((this._bfl_s = new l(t.numItems, t.errorRate)),
								this._bfl_s.import(t)),
								(null == a ? void 0 : a.numHashes) &&
									((this._bfl_d = new l(a.numItems, a.errorRate)),
									this._bfl_d.import(a));
						}
						let c = !1,
							f = !1;
						for (const { as: r, allowMatchCurrent: i } of [
							{ as: e },
							{ as: t },
						])
							if (r) {
								const t = (0, a.removeTrailingSlash)(
										new URL(r, "http://n").pathname,
									),
									d = (0, R.addBasePath)((0, E.addLocale)(t, n || this.locale));
								if (
									i ||
									t !==
										(0, a.removeTrailingSlash)(
											new URL(this.asPath, "http://n").pathname,
										)
								) {
									var l, u, s;
									for (const e of ((c =
										c ||
										!!(null == (l = this._bfl_s) ? void 0 : l.contains(t)) ||
										!!(null == (u = this._bfl_s) ? void 0 : u.contains(d))),
									[t, d])) {
										const t = e.split("/");
										for (let e = 0; !f && e < t.length + 1; e++) {
											const r = t.slice(0, e).join("/");
											if (
												r &&
												(null == (s = this._bfl_d) ? void 0 : s.contains(r))
											) {
												f = !0;
												break;
											}
										}
									}
									if (c || f) {
										if (o) return !0;
										return (
											q({
												url: (0, R.addBasePath)(
													(0, E.addLocale)(
														e,
														n || this.locale,
														this.defaultLocale,
													),
												),
												router: this,
											}),
											new Promise(() => {})
										);
									}
								}
							}
					}
					return !1;
				}
				async change(e, t, r, n, o) {
					var s, c, f, O, S, j, T, w, x;
					let M, U;
					if (!(0, C.isLocalURL)(t)) return q({ url: t, router: this }), !1;
					const B = 1 === n._h;
					B || n.shallow || (await this._bfl(r, void 0, n.locale));
					let X =
							B ||
							n._shouldResolveHref ||
							(0, b.parsePath)(t).pathname === (0, b.parsePath)(r).pathname,
						W = { ...this.state },
						G = !0 !== this.isReady;
					this.isReady = !0;
					const V = this.isSsr;
					if ((B || (this.isSsr = !1), B && this.clc)) return !1;
					const Y = W.locale;
					d.ST && performance.mark("routeChange");
					const { shallow: K = !1, scroll: $ = !0 } = n,
						Q = { shallow: K };
					this._inFlightRoute &&
						this.clc &&
						(V ||
							z.events.emit("routeChangeError", L(), this._inFlightRoute, Q),
						this.clc(),
						(this.clc = null)),
						(r = (0, R.addBasePath)(
							(0, E.addLocale)(
								(0, v.hasBasePath)(r) ? (0, P.removeBasePath)(r) : r,
								n.locale,
								this.defaultLocale,
							),
						));
					const J = (0, y.removeLocale)(
						(0, v.hasBasePath)(r) ? (0, P.removeBasePath)(r) : r,
						W.locale,
					);
					this._inFlightRoute = r;
					const Z = Y !== W.locale;
					if (!B && this.onlyAHashChange(J) && !Z) {
						(W.asPath = J),
							z.events.emit("hashChangeStart", r, Q),
							this.changeState(e, t, r, { ...n, scroll: !1 }),
							$ && this.scrollToHash(J);
						try {
							await this.set(W, this.components[W.route], null);
						} catch (e) {
							throw (
								((0, u.default)(e) &&
									e.cancelled &&
									z.events.emit("routeChangeError", e, J, Q),
								e)
							);
						}
						return z.events.emit("hashChangeComplete", r, Q), !0;
					}
					let ee = (0, h.parseRelativeUrl)(t),
						{ pathname: et, query: er } = ee;
					try {
						[M, { __rewrites: U }] = await Promise.all([
							this.pageLoader.getPageList(),
							(0, i.getClientBuildManifest)(),
							this.pageLoader.getMiddleware(),
						]);
					} catch (e) {
						return q({ url: r, router: this }), !1;
					}
					this.urlIsNew(J) || Z || (e = "replaceState");
					let en = r;
					et = et ? (0, a.removeTrailingSlash)((0, P.removeBasePath)(et)) : et;
					let eo = (0, a.removeTrailingSlash)(et),
						ea = r.startsWith("/") && (0, h.parseRelativeUrl)(r).pathname;
					if (null == (s = this.components[et]) ? void 0 : s.__appRouter)
						return q({ url: r, router: this }), new Promise(() => {});
					const ei = !!(
							ea &&
							eo !== ea &&
							(!(0, p.isDynamicRoute)(eo) ||
								!(0, _.getRouteMatcher)((0, m.getRouteRegex)(eo))(ea))
						),
						el =
							!n.shallow &&
							(await D({ asPath: r, locale: W.locale, router: this }));
					if (
						(B && el && (X = !1),
						X &&
							"/_error" !== et &&
							((n._shouldResolveHref = !0),
							(ee.pathname = F(et, M)),
							ee.pathname === et ||
								((et = ee.pathname),
								(ee.pathname = (0, R.addBasePath)(et)),
								el || (t = (0, g.formatWithValidation)(ee)))),
						!(0, C.isLocalURL)(r))
					)
						return q({ url: r, router: this }), !1;
					(en = (0, y.removeLocale)((0, P.removeBasePath)(en), W.locale)),
						(eo = (0, a.removeTrailingSlash)(et));
					let eu = !1;
					if ((0, p.isDynamicRoute)(eo)) {
						const e = (0, h.parseRelativeUrl)(en),
							n = e.pathname,
							o = (0, m.getRouteRegex)(eo);
						eu = (0, _.getRouteMatcher)(o)(n);
						const a = eo === n,
							i = a ? (0, N.interpolateAs)(eo, n, er) : {};
						if (eu && (!a || i.result))
							a
								? (r = (0, g.formatWithValidation)(
										Object.assign({}, e, {
											pathname: i.result,
											query: (0, I.omit)(er, i.params),
										}),
									))
								: Object.assign(er, eu);
						else {
							const e = Object.keys(o.groups).filter(
								(e) => !er[e] && !o.groups[e].optional,
							);
							if (e.length > 0 && !el)
								throw Object.defineProperty(
									Error(
										(a
											? "The provided `href` (" +
												t +
												") value is missing query values (" +
												e.join(", ") +
												") to be interpolated properly. "
											: "The provided `as` value (" +
												n +
												") is incompatible with the `href` value (" +
												eo +
												"). ") +
											"Read more: https://nextjs.org/docs/messages/" +
											(a
												? "href-interpolation-failed"
												: "incompatible-href-as"),
									),
									"__NEXT_ERROR_CODE",
									{ value: "E344", enumerable: !1, configurable: !0 },
								);
						}
					}
					B || z.events.emit("routeChangeStart", r, Q);
					const es = "/404" === this.pathname || "/_error" === this.pathname;
					try {
						let a = await this.getRouteInfo({
							route: eo,
							pathname: et,
							query: er,
							as: r,
							resolvedAs: en,
							routeProps: Q,
							locale: W.locale,
							isPreview: W.isPreview,
							hasMiddleware: el,
							unstable_skipClientCache: n.unstable_skipClientCache,
							isQueryUpdating: B && !this.isFallback,
							isMiddlewareRewrite: ei,
						});
						if (
							(B ||
								n.shallow ||
								(await this._bfl(
									r,
									"resolvedAs" in a ? a.resolvedAs : void 0,
									W.locale,
								)),
							"route" in a && el)
						) {
							(eo = et = a.route || eo),
								Q.shallow || (er = Object.assign({}, a.query || {}, er));
							const e = (0, v.hasBasePath)(ee.pathname)
								? (0, P.removeBasePath)(ee.pathname)
								: ee.pathname;
							if (
								(eu &&
									et !== e &&
									Object.keys(eu).forEach((e) => {
										eu && er[e] === eu[e] && delete er[e];
									}),
								(0, p.isDynamicRoute)(et))
							) {
								let e =
									!Q.shallow && a.resolvedAs
										? a.resolvedAs
										: (0, R.addBasePath)(
												(0, E.addLocale)(
													new URL(r, location.href).pathname,
													W.locale,
												),
												!0,
											);
								(0, v.hasBasePath)(e) && (e = (0, P.removeBasePath)(e));
								const t = (0, m.getRouteRegex)(et),
									n = (0, _.getRouteMatcher)(t)(
										new URL(e, location.href).pathname,
									);
								n && Object.assign(er, n);
							}
						}
						if ("type" in a) {
							if ("redirect-internal" === a.type)
								return this.change(e, a.newUrl, a.newAs, n);
							return (
								q({ url: a.destination, router: this }), new Promise(() => {})
							);
						}
						const i = a.Component;
						if (
							(i &&
								i.unstable_scriptLoader &&
								[].concat(i.unstable_scriptLoader()).forEach((e) => {
									(0, l.handleClientScriptLoad)(e.props);
								}),
							(a.__N_SSG || a.__N_SSP) && a.props)
						) {
							if (a.props.pageProps && a.props.pageProps.__N_REDIRECT) {
								n.locale = !1;
								const t = a.props.pageProps.__N_REDIRECT;
								if (
									t.startsWith("/") &&
									!1 !== a.props.pageProps.__N_REDIRECT_BASE_PATH
								) {
									const r = (0, h.parseRelativeUrl)(t);
									r.pathname = F(r.pathname, M);
									const { url: o, as: a } = k(this, t, t);
									return this.change(e, o, a, n);
								}
								return q({ url: t, router: this }), new Promise(() => {});
							}
							if (
								((W.isPreview = !!a.props.__N_PREVIEW), a.props.notFound === H)
							) {
								let e;
								try {
									await this.fetchComponent("/404"), (e = "/404");
								} catch (t) {
									e = "/_error";
								}
								if (
									((a = await this.getRouteInfo({
										route: e,
										pathname: e,
										query: er,
										as: r,
										resolvedAs: en,
										routeProps: { shallow: !1 },
										locale: W.locale,
										isPreview: W.isPreview,
										isNotFound: !0,
									})),
									"type" in a)
								)
									throw Object.defineProperty(
										Error("Unexpected middleware effect on /404"),
										"__NEXT_ERROR_CODE",
										{ value: "E158", enumerable: !1, configurable: !0 },
									);
							}
						}
						B &&
							"/_error" === this.pathname &&
							(null == (f = self.__NEXT_DATA__.props)
								? void 0
								: null == (c = f.pageProps)
									? void 0
									: c.statusCode) === 500 &&
							(null == (O = a.props) ? void 0 : O.pageProps) &&
							(a.props.pageProps.statusCode = 500);
						const s = n.shallow && W.route === (null != (S = a.route) ? S : eo),
							d = null != (j = n.scroll) ? j : !B && !s,
							g = null != o ? o : d ? { x: 0, y: 0 } : null,
							b = {
								...W,
								route: eo,
								pathname: et,
								query: er,
								asPath: J,
								isFallback: !1,
							};
						if (B && es) {
							if (
								((a = await this.getRouteInfo({
									route: this.pathname,
									pathname: this.pathname,
									query: er,
									as: r,
									resolvedAs: en,
									routeProps: { shallow: !1 },
									locale: W.locale,
									isPreview: W.isPreview,
									isQueryUpdating: B && !this.isFallback,
								})),
								"type" in a)
							)
								throw Object.defineProperty(
									Error("Unexpected middleware effect on " + this.pathname),
									"__NEXT_ERROR_CODE",
									{ value: "E225", enumerable: !1, configurable: !0 },
								);
							"/_error" === this.pathname &&
								(null == (w = self.__NEXT_DATA__.props)
									? void 0
									: null == (T = w.pageProps)
										? void 0
										: T.statusCode) === 500 &&
								(null == (x = a.props) ? void 0 : x.pageProps) &&
								(a.props.pageProps.statusCode = 500);
							try {
								await this.set(b, a, g);
							} catch (e) {
								throw (
									((0, u.default)(e) &&
										e.cancelled &&
										z.events.emit("routeChangeError", e, J, Q),
									e)
								);
							}
							return !0;
						}
						if (
							(z.events.emit("beforeHistoryChange", r, Q),
							this.changeState(e, t, r, n),
							!(
								B &&
								!g &&
								!G &&
								!Z &&
								(0, A.compareRouterStates)(b, this.state)
							))
						) {
							try {
								await this.set(b, a, g);
							} catch (e) {
								if (e.cancelled) a.error = a.error || e;
								else throw e;
							}
							if (a.error)
								throw (
									(B || z.events.emit("routeChangeError", a.error, J, Q),
									a.error)
								);
							B || z.events.emit("routeChangeComplete", r, Q),
								d && /#.+$/.test(r) && this.scrollToHash(r);
						}
						return !0;
					} catch (e) {
						if ((0, u.default)(e) && e.cancelled) return !1;
						throw e;
					}
				}
				changeState(e, t, r, n) {
					void 0 === n && (n = {}),
						("pushState" !== e || (0, d.getURL)() !== r) &&
							((this._shallow = n.shallow),
							window.history[e](
								{
									url: t,
									as: r,
									options: n,
									__N: !0,
									key: (this._key = "pushState" !== e ? this._key : G()),
								},
								"",
								r,
							));
				}
				async handleRouteInfoError(e, t, r, n, o, a) {
					if (e.cancelled) throw e;
					if ((0, i.isAssetError)(e) || a)
						throw (
							(z.events.emit("routeChangeError", e, n, o),
							q({ url: n, router: this }),
							L())
						);
					console.error(e);
					try {
						let n;
						const { page: o, styleSheets: a } =
								await this.fetchComponent("/_error"),
							i = { props: n, Component: o, styleSheets: a, err: e, error: e };
						if (!i.props)
							try {
								i.props = await this.getInitialProps(o, {
									err: e,
									pathname: t,
									query: r,
								});
							} catch (e) {
								console.error("Error in error page `getInitialProps`: ", e),
									(i.props = {});
							}
						return i;
					} catch (e) {
						return this.handleRouteInfoError(
							(0, u.default)(e)
								? e
								: Object.defineProperty(Error(e + ""), "__NEXT_ERROR_CODE", {
										value: "E394",
										enumerable: !1,
										configurable: !0,
									}),
							t,
							r,
							n,
							o,
							!0,
						);
					}
				}
				async getRouteInfo(e) {
					let {
							route: t,
							pathname: r,
							query: n,
							as: o,
							resolvedAs: i,
							routeProps: l,
							locale: s,
							hasMiddleware: f,
							isPreview: d,
							unstable_skipClientCache: p,
							isQueryUpdating: h,
							isMiddlewareRewrite: _,
							isNotFound: m,
						} = e,
						b = t;
					try {
						var E, y, R, v;
						let e = this.components[b];
						if (l.shallow && e && this.route === b) return e;
						const t = V({ route: b, router: this });
						f && (e = void 0);
						let u = !e || "initial" in e ? void 0 : e,
							O = {
								dataHref: this.pageLoader.getDataHref({
									href: (0, g.formatWithValidation)({ pathname: r, query: n }),
									skipInterpolation: !0,
									asPath: m ? "/404" : i,
									locale: s,
								}),
								hasMiddleware: !0,
								isServerRender: this.isSsr,
								parseJSON: !0,
								inflightCache: h ? this.sbc : this.sdc,
								persistCache: !d,
								isPrefetch: !1,
								unstable_skipClientCache: p,
								isBackground: h,
							},
							j =
								h && !_
									? null
									: await B({
											fetchData: () => W(O),
											asPath: m ? "/404" : i,
											locale: s,
											router: this,
										}).catch((e) => {
											if (h) return null;
											throw e;
										});
						if (
							(j && ("/_error" === r || "/404" === r) && (j.effect = void 0),
							h &&
								(j
									? (j.json = self.__NEXT_DATA__.props)
									: (j = { json: self.__NEXT_DATA__.props })),
							t(),
							(null == j
								? void 0
								: null == (E = j.effect)
									? void 0
									: E.type) === "redirect-internal" ||
								(null == j
									? void 0
									: null == (y = j.effect)
										? void 0
										: y.type) === "redirect-external")
						)
							return j.effect;
						if (
							(null == j
								? void 0
								: null == (R = j.effect)
									? void 0
									: R.type) === "rewrite"
						) {
							const t = (0, a.removeTrailingSlash)(j.effect.resolvedHref),
								o = await this.pageLoader.getPageList();
							if (
								(!h || o.includes(t)) &&
								((b = t),
								(r = j.effect.resolvedHref),
								(n = { ...n, ...j.effect.parsedAs.query }),
								(i = (0, P.removeBasePath)(
									(0, c.normalizeLocalePath)(
										j.effect.parsedAs.pathname,
										this.locales,
									).pathname,
								)),
								(e = this.components[b]),
								l.shallow && e && this.route === b && !f)
							)
								return { ...e, route: b };
						}
						if ((0, S.isAPIRoute)(b))
							return q({ url: o, router: this }), new Promise(() => {});
						const T =
								u ||
								(await this.fetchComponent(b).then((e) => ({
									Component: e.page,
									styleSheets: e.styleSheets,
									__N_SSG: e.mod.__N_SSG,
									__N_SSP: e.mod.__N_SSP,
								}))),
							A =
								null == j
									? void 0
									: null == (v = j.response)
										? void 0
										: v.headers.get("x-middleware-skip"),
							C = T.__N_SSG || T.__N_SSP;
						A &&
							(null == j ? void 0 : j.dataHref) &&
							delete this.sdc[j.dataHref];
						const { props: w, cacheKey: I } = await this._getData(async () => {
							if (C) {
								if ((null == j ? void 0 : j.json) && !A)
									return { cacheKey: j.cacheKey, props: j.json };
								const e = (null == j ? void 0 : j.dataHref)
										? j.dataHref
										: this.pageLoader.getDataHref({
												href: (0, g.formatWithValidation)({
													pathname: r,
													query: n,
												}),
												asPath: i,
												locale: s,
											}),
									t = await W({
										dataHref: e,
										isServerRender: this.isSsr,
										parseJSON: !0,
										inflightCache: A ? {} : this.sdc,
										persistCache: !d,
										isPrefetch: !1,
										unstable_skipClientCache: p,
									});
								return { cacheKey: t.cacheKey, props: t.json || {} };
							}
							return {
								headers: {},
								props: await this.getInitialProps(T.Component, {
									pathname: r,
									query: n,
									asPath: o,
									locale: s,
									locales: this.locales,
									defaultLocale: this.defaultLocale,
								}),
							};
						});
						return (
							T.__N_SSP && O.dataHref && I && delete this.sdc[I],
							this.isPreview ||
								!T.__N_SSG ||
								h ||
								W(
									Object.assign({}, O, {
										isBackground: !0,
										persistCache: !1,
										inflightCache: this.sbc,
									}),
								).catch(() => {}),
							(w.pageProps = Object.assign({}, w.pageProps)),
							(T.props = w),
							(T.route = b),
							(T.query = n),
							(T.resolvedAs = i),
							(this.components[b] = T),
							T
						);
					} catch (e) {
						return this.handleRouteInfoError(
							(0, u.getProperError)(e),
							r,
							n,
							o,
							l,
						);
					}
				}
				set(e, t, r) {
					return (
						(this.state = e), this.sub(t, this.components["/_app"].Component, r)
					);
				}
				beforePopState(e) {
					this._bps = e;
				}
				onlyAHashChange(e) {
					if (!this.asPath) return !1;
					const [t, r] = this.asPath.split("#", 2),
						[n, o] = e.split("#", 2);
					return (!!o && t === n && r === o) || (t === n && r !== o);
				}
				scrollToHash(e) {
					const [, t = ""] = e.split("#", 2);
					(0, x.handleSmoothScroll)(
						() => {
							if ("" === t || "top" === t) {
								window.scrollTo(0, 0);
								return;
							}
							const e = decodeURIComponent(t),
								r = document.getElementById(e);
							if (r) {
								r.scrollIntoView();
								return;
							}
							const n = document.getElementsByName(e)[0];
							n && n.scrollIntoView();
						},
						{ onlyHashChange: this.onlyAHashChange(e) },
					);
				}
				urlIsNew(e) {
					return this.asPath !== e;
				}
				async prefetch(e, t, r) {
					if (
						(void 0 === t && (t = e),
						void 0 === r && (r = {}),
						(0, w.isBot)(window.navigator.userAgent))
					)
						return;
					let n = (0, h.parseRelativeUrl)(e),
						o = n.pathname,
						{ pathname: i, query: l } = n,
						u = i,
						s = await this.pageLoader.getPageList(),
						c = t,
						f = void 0 !== r.locale ? r.locale || void 0 : this.locale,
						d = await D({ asPath: t, locale: f, router: this });
					(n.pathname = F(n.pathname, s)),
						(0, p.isDynamicRoute)(n.pathname) &&
							((i = n.pathname),
							(n.pathname = i),
							Object.assign(
								l,
								(0, _.getRouteMatcher)((0, m.getRouteRegex)(n.pathname))(
									(0, b.parsePath)(t).pathname,
								) || {},
							),
							d || (e = (0, g.formatWithValidation)(n)));
					const E = await B({
						fetchData: () =>
							W({
								dataHref: this.pageLoader.getDataHref({
									href: (0, g.formatWithValidation)({ pathname: u, query: l }),
									skipInterpolation: !0,
									asPath: c,
									locale: f,
								}),
								hasMiddleware: !0,
								isServerRender: !1,
								parseJSON: !0,
								inflightCache: this.sdc,
								persistCache: !this.isPreview,
								isPrefetch: !0,
							}),
						asPath: t,
						locale: f,
						router: this,
					});
					if (
						((null == E ? void 0 : E.effect.type) === "rewrite" &&
							((n.pathname = E.effect.resolvedHref),
							(i = E.effect.resolvedHref),
							(l = { ...l, ...E.effect.parsedAs.query }),
							(c = E.effect.parsedAs.pathname),
							(e = (0, g.formatWithValidation)(n))),
						(null == E ? void 0 : E.effect.type) === "redirect-external")
					)
						return;
					const y = (0, a.removeTrailingSlash)(i);
					(await this._bfl(t, c, r.locale, !0)) &&
						(this.components[o] = { __appRouter: !0 }),
						await Promise.all([
							this.pageLoader._isSsg(y).then(
								(t) =>
									!!t &&
									W({
										dataHref: (null == E ? void 0 : E.json)
											? null == E
												? void 0
												: E.dataHref
											: this.pageLoader.getDataHref({
													href: e,
													asPath: c,
													locale: f,
												}),
										isServerRender: !1,
										parseJSON: !0,
										inflightCache: this.sdc,
										persistCache: !this.isPreview,
										isPrefetch: !0,
										unstable_skipClientCache:
											r.unstable_skipClientCache || (r.priority && !0),
									})
										.then(() => !1)
										.catch(() => !1),
							),
							this.pageLoader[r.priority ? "loadPage" : "prefetch"](y),
						]);
				}
				async fetchComponent(e) {
					const t = V({ route: e, router: this });
					try {
						const r = await this.pageLoader.loadPage(e);
						return t(), r;
					} catch (e) {
						throw (t(), e);
					}
				}
				_getData(e) {
					let t = !1,
						r = () => {
							t = !0;
						};
					return (
						(this.clc = r),
						e().then((e) => {
							if ((r === this.clc && (this.clc = null), t)) {
								const e = Object.defineProperty(
									Error("Loading initial props cancelled"),
									"__NEXT_ERROR_CODE",
									{ value: "E405", enumerable: !1, configurable: !0 },
								);
								throw ((e.cancelled = !0), e);
							}
							return e;
						})
					);
				}
				getInitialProps(e, t) {
					const { Component: r } = this.components["/_app"],
						n = this._wrapApp(r);
					return (
						(t.AppTree = n),
						(0, d.loadGetInitialProps)(r, {
							AppTree: n,
							Component: e,
							router: this,
							ctx: t,
						})
					);
				}
				get route() {
					return this.state.route;
				}
				get pathname() {
					return this.state.pathname;
				}
				get query() {
					return this.state.query;
				}
				get asPath() {
					return this.state.asPath;
				}
				get locale() {
					return this.state.locale;
				}
				get isFallback() {
					return this.state.isFallback;
				}
				get isPreview() {
					return this.state.isPreview;
				}
				constructor(
					e,
					t,
					r,
					{
						initialProps: n,
						pageLoader: o,
						App: i,
						wrapApp: l,
						Component: u,
						err: s,
						subscription: c,
						isFallback: f,
						locale: _,
						locales: m,
						defaultLocale: b,
						domainLocales: E,
						isPreview: y,
					},
				) {
					(this.sdc = {}),
						(this.sbc = {}),
						(this.isFirstPopStateEvent = !0),
						(this._key = G()),
						(this.onPopState = (e) => {
							let t;
							const { isFirstPopStateEvent: r } = this;
							this.isFirstPopStateEvent = !1;
							const n = e.state;
							if (!n) {
								const { pathname: e, query: t } = this;
								this.changeState(
									"replaceState",
									(0, g.formatWithValidation)({
										pathname: (0, R.addBasePath)(e),
										query: t,
									}),
									(0, d.getURL)(),
								);
								return;
							}
							if (n.__NA) {
								window.location.reload();
								return;
							}
							if (
								!n.__N ||
								(r && this.locale === n.options.locale && n.as === this.asPath)
							)
								return;
							const { url: o, as: a, options: i, key: l } = n;
							this._key = l;
							const { pathname: u } = (0, h.parseRelativeUrl)(o);
							(!this.isSsr ||
								a !== (0, R.addBasePath)(this.asPath) ||
								u !== (0, R.addBasePath)(this.pathname)) &&
								(!this._bps || this._bps(n)) &&
								this.change(
									"replaceState",
									o,
									a,
									Object.assign({}, i, {
										shallow: i.shallow && this._shallow,
										locale: i.locale || this.defaultLocale,
										_h: 0,
									}),
									t,
								);
						});
					const P = (0, a.removeTrailingSlash)(e);
					(this.components = {}),
						"/_error" !== e &&
							(this.components[P] = {
								Component: u,
								initial: !0,
								props: n,
								err: s,
								__N_SSG: n && n.__N_SSG,
								__N_SSP: n && n.__N_SSP,
							}),
						(this.components["/_app"] = { Component: i, styleSheets: [] }),
						(this.events = z.events),
						(this.pageLoader = o);
					const v = (0, p.isDynamicRoute)(e) && self.__NEXT_DATA__.autoExport;
					if (
						((this.basePath = ""),
						(this.sub = c),
						(this.clc = null),
						(this._wrapApp = l),
						(this.isSsr = !0),
						(this.isLocaleDomain = !1),
						(this.isReady = !!(
							self.__NEXT_DATA__.gssp ||
							self.__NEXT_DATA__.gip ||
							self.__NEXT_DATA__.isExperimentalCompile ||
							(self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||
							(!v && !self.location.search)
						)),
						(this.state = {
							route: P,
							pathname: e,
							query: t,
							asPath: v ? e : r,
							isPreview: !!y,
							locale: void 0,
							isFallback: f,
						}),
						(this._initialMatchesMiddlewarePromise = Promise.resolve(!1)),
						!r.startsWith("//"))
					) {
						const n = { locale: _ },
							o = (0, d.getURL)();
						this._initialMatchesMiddlewarePromise = D({
							router: this,
							locale: _,
							asPath: o,
						}).then(
							(a) => (
								(n._shouldResolveHref = r !== e),
								this.changeState(
									"replaceState",
									a
										? o
										: (0, g.formatWithValidation)({
												pathname: (0, R.addBasePath)(e),
												query: t,
											}),
									o,
									n,
								),
								a
							),
						);
					}
					window.addEventListener("popstate", this.onPopState);
				}
			}
			z.events = (0, f.default)();
		},
		710: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "Portal", { enumerable: !0, get: () => a });
			const n = r(148),
				o = r(7897),
				a = (e) => {
					const { children: t, type: r } = e,
						[a, i] = (0, n.useState)(null);
					return (
						(0, n.useEffect)(() => {
							const e = document.createElement(r);
							return (
								document.body.appendChild(e),
								i(e),
								() => {
									document.body.removeChild(e);
								}
							);
						}, [r]),
						a ? (0, o.createPortal)(t, a) : null
					);
				};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		864: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "formatNextPathnameInfo", {
					enumerable: !0,
					get: () => l,
				});
			const n = r(170),
				o = r(9925),
				a = r(5143),
				i = r(1259);
			function l(e) {
				let t = (0, i.addLocale)(
					e.pathname,
					e.locale,
					e.buildId ? void 0 : e.defaultLocale,
					e.ignorePrefix,
				);
				return (
					(e.buildId || !e.trailingSlash) &&
						(t = (0, n.removeTrailingSlash)(t)),
					e.buildId &&
						(t = (0, a.addPathSuffix)(
							(0, o.addPathPrefix)(t, "/_next/data/" + e.buildId),
							"/" === e.pathname ? "index.json" : ".json",
						)),
					(t = (0, o.addPathPrefix)(t, e.basePath)),
					!e.buildId && e.trailingSlash
						? t.endsWith("/")
							? t
							: (0, a.addPathSuffix)(t, "/")
						: (0, n.removeTrailingSlash)(t)
				);
			}
		},
		905: (e, t) => {
			function r(e) {
				return new URL(e, "http://n").searchParams;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "asPathToSearchParams", {
					enumerable: !0,
					get: () => r,
				});
		},
		1108: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => d });
			const n = r(1532),
				o = r(5856),
				a = r(6369),
				i = n._(r(1711)),
				l = r(5475),
				u = r(4839),
				s = r(7529),
				c = r(170),
				f = r(6732);
			r(1638);
			class d {
				getPageList() {
					return (0, f.getClientBuildManifest)().then((e) => e.sortedPages);
				}
				getMiddleware() {
					return (
						(window.__MIDDLEWARE_MATCHERS = []), window.__MIDDLEWARE_MATCHERS
					);
				}
				getDataHref(e) {
					const { asPath: t, href: r, locale: n } = e,
						{ pathname: f, query: d, search: p } = (0, s.parseRelativeUrl)(r),
						{ pathname: h } = (0, s.parseRelativeUrl)(t),
						_ = (0, c.removeTrailingSlash)(f);
					if ("/" !== _[0])
						throw Object.defineProperty(
							Error('Route name should start with a "/", got "' + _ + '"'),
							"__NEXT_ERROR_CODE",
							{ value: "E303", enumerable: !1, configurable: !0 },
						);
					return ((e) => {
						const t = (0, i.default)(
							(0, c.removeTrailingSlash)((0, l.addLocale)(e, n)),
							".json",
						);
						return (0, o.addBasePath)(
							"/_next/data/" + this.buildId + t + p,
							!0,
						);
					})(
						e.skipInterpolation
							? h
							: (0, u.isDynamicRoute)(_)
								? (0, a.interpolateAs)(f, h, d).result
								: _,
					);
				}
				_isSsg(e) {
					return this.promisedSsgManifest.then((t) => t.has(e));
				}
				loadPage(e) {
					return this.routeLoader.loadRoute(e).then((e) => {
						if ("component" in e)
							return {
								page: e.component,
								mod: e.exports,
								styleSheets: e.styles.map((e) => ({
									href: e.href,
									text: e.content,
								})),
							};
						throw e.error;
					});
				}
				prefetch(e) {
					return this.routeLoader.prefetch(e);
				}
				constructor(e, t) {
					(this.routeLoader = (0, f.createRouteLoader)(t)),
						(this.buildId = e),
						(this.assetPrefix = t),
						(this.promisedSsgManifest = new Promise((e) => {
							window.__SSG_MANIFEST
								? e(window.__SSG_MANIFEST)
								: (window.__SSG_MANIFEST_CB = () => {
										e(window.__SSG_MANIFEST);
									});
						}));
				}
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		1259: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "addLocale", { enumerable: !0, get: () => a });
			const n = r(9925),
				o = r(6368);
			function a(e, t, r, a) {
				if (!t || t === r) return e;
				const i = e.toLowerCase();
				return !a &&
					((0, o.pathHasPrefix)(i, "/api") ||
						(0, o.pathHasPrefix)(i, "/" + t.toLowerCase()))
					? e
					: (0, n.addPathPrefix)(e, "/" + t);
			}
		},
		1499: (e, t) => {
			function r(e, t) {
				const r = Object.keys(e);
				if (r.length !== Object.keys(t).length) return !1;
				for (let n = r.length; n--; ) {
					const o = r[n];
					if ("query" === o) {
						const r = Object.keys(e.query);
						if (r.length !== Object.keys(t.query).length) return !1;
						for (let n = r.length; n--; ) {
							const o = r[n];
							if (!t.query.hasOwnProperty(o) || e.query[o] !== t.query[o])
								return !1;
						}
					} else if (!t.hasOwnProperty(o) || e[o] !== t[o]) return !1;
				}
				return !0;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "compareRouterStates", {
					enumerable: !0,
					get: () => r,
				});
		},
		1532: (e, t, r) => {
			function n(e) {
				return e && e.__esModule ? e : { default: e };
			}
			r.r(t), r.d(t, { _: () => n });
		},
		1556: (e, t) => {
			function r(e) {
				const t = {};
				for (const [r, n] of e.entries()) {
					const e = t[r];
					void 0 === e
						? (t[r] = n)
						: Array.isArray(e)
							? e.push(n)
							: (t[r] = [e, n]);
				}
				return t;
			}
			function n(e) {
				return "string" == typeof e
					? e
					: ("number" != typeof e || isNaN(e)) && "boolean" != typeof e
						? ""
						: String(e);
			}
			function o(e) {
				const t = new URLSearchParams();
				for (const [r, o] of Object.entries(e))
					if (Array.isArray(o)) for (const e of o) t.append(r, n(e));
					else t.set(r, n(o));
				return t;
			}
			function a(e) {
				for (
					var t = arguments.length, r = Array(t > 1 ? t - 1 : 0), n = 1;
					n < t;
					n++
				)
					r[n - 1] = arguments[n];
				for (const t of r) {
					for (const r of t.keys()) e.delete(r);
					for (const [r, n] of t.entries()) e.append(r, n);
				}
				return e;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					assign: () => a,
					searchParamsToUrlQuery: () => r,
					urlQueryToSearchParams: () => o,
				});
		},
		1638: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					APP_BUILD_MANIFEST: () => E,
					APP_CLIENT_INTERNALS: () => Q,
					APP_PATHS_MANIFEST: () => m,
					APP_PATH_ROUTES_MANIFEST: () => g,
					BARREL_OPTIMIZATION_PREFIX: () => X,
					BLOCKED_PAGES: () => U,
					BUILD_ID_FILE: () => D,
					BUILD_MANIFEST: () => b,
					CLIENT_PUBLIC_FILES_PATH: () => k,
					CLIENT_REFERENCE_MANIFEST: () => W,
					CLIENT_STATIC_FILES_PATH: () => F,
					CLIENT_STATIC_FILES_RUNTIME_AMP: () => Z,
					CLIENT_STATIC_FILES_RUNTIME_MAIN: () => K,
					CLIENT_STATIC_FILES_RUNTIME_MAIN_APP: () => $,
					CLIENT_STATIC_FILES_RUNTIME_POLYFILLS: () => et,
					CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL: () => er,
					CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH: () => J,
					CLIENT_STATIC_FILES_RUNTIME_WEBPACK: () => ee,
					COMPILER_INDEXES: () => a,
					COMPILER_NAMES: () => o,
					CONFIG_FILES: () => L,
					DEFAULT_RUNTIME_WEBPACK: () => en,
					DEFAULT_SANS_SERIF_FONT: () => eu,
					DEFAULT_SERIF_FONT: () => el,
					DEV_CLIENT_MIDDLEWARE_MANIFEST: () => N,
					DEV_CLIENT_PAGES_MANIFEST: () => C,
					DYNAMIC_CSS_MANIFEST: () => Y,
					EDGE_RUNTIME_WEBPACK: () => eo,
					EDGE_UNSUPPORTED_NODE_APIS: () => ep,
					EXPORT_DETAIL: () => O,
					EXPORT_MARKER: () => v,
					FUNCTIONS_CONFIG_MANIFEST: () => y,
					IMAGES_MANIFEST: () => T,
					INTERCEPTION_ROUTE_REWRITE_MANIFEST: () => z,
					MIDDLEWARE_BUILD_MANIFEST: () => q,
					MIDDLEWARE_MANIFEST: () => w,
					MIDDLEWARE_REACT_LOADABLE_MANIFEST: () => V,
					MODERN_BROWSERSLIST_TARGET: () => n.default,
					NEXT_BUILTIN_DOCUMENT: () => H,
					NEXT_FONT_MANIFEST: () => R,
					PAGES_MANIFEST: () => h,
					PHASE_DEVELOPMENT_SERVER: () => f,
					PHASE_EXPORT: () => u,
					PHASE_INFO: () => p,
					PHASE_PRODUCTION_BUILD: () => s,
					PHASE_PRODUCTION_SERVER: () => c,
					PHASE_TEST: () => d,
					PRERENDER_MANIFEST: () => S,
					REACT_LOADABLE_MANIFEST: () => x,
					ROUTES_MANIFEST: () => j,
					RSC_MODULE_TYPES: () => ed,
					SERVER_DIRECTORY: () => M,
					SERVER_FILES_MANIFEST: () => A,
					SERVER_PROPS_ID: () => ei,
					SERVER_REFERENCE_MANIFEST: () => G,
					STATIC_PROPS_ID: () => ea,
					STATIC_STATUS_PAGES: () => es,
					STRING_LITERAL_DROP_BUNDLE: () => B,
					SUBRESOURCE_INTEGRITY_MANIFEST: () => P,
					SYSTEM_ENTRYPOINTS: () => eh,
					TRACE_OUTPUT_VERSION: () => ec,
					TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST: () => I,
					TURBO_TRACE_DEFAULT_MEMORY_LIMIT: () => ef,
					UNDERSCORE_NOT_FOUND_ROUTE: () => i,
					UNDERSCORE_NOT_FOUND_ROUTE_ENTRY: () => l,
					WEBPACK_STATS: () => _,
				});
			const n = r(1532)._(r(6658)),
				o = { client: "client", server: "server", edgeServer: "edge-server" },
				a = { [o.client]: 0, [o.server]: 1, [o.edgeServer]: 2 },
				i = "/_not-found",
				l = "" + i + "/page",
				u = "phase-export",
				s = "phase-production-build",
				c = "phase-production-server",
				f = "phase-development-server",
				d = "phase-test",
				p = "phase-info",
				h = "pages-manifest.json",
				_ = "webpack-stats.json",
				m = "app-paths-manifest.json",
				g = "app-path-routes-manifest.json",
				b = "build-manifest.json",
				E = "app-build-manifest.json",
				y = "functions-config-manifest.json",
				P = "subresource-integrity-manifest",
				R = "next-font-manifest",
				v = "export-marker.json",
				O = "export-detail.json",
				S = "prerender-manifest.json",
				j = "routes-manifest.json",
				T = "images-manifest.json",
				A = "required-server-files.json",
				C = "_devPagesManifest.json",
				w = "middleware-manifest.json",
				I = "_clientMiddlewareManifest.json",
				N = "_devMiddlewareManifest.json",
				x = "react-loadable-manifest.json",
				M = "server",
				L = ["next.config.js", "next.config.mjs", "next.config.ts"],
				D = "BUILD_ID",
				U = ["/_document", "/_app", "/_error"],
				k = "public",
				F = "static",
				B = "__NEXT_DROP_CLIENT_FILE__",
				H = "__NEXT_BUILTIN_DOCUMENT__",
				X = "__barrel_optimize__",
				W = "client-reference-manifest",
				G = "server-reference-manifest",
				q = "middleware-build-manifest",
				V = "middleware-react-loadable-manifest",
				z = "interception-route-rewrite-manifest",
				Y = "dynamic-css-manifest",
				K = "main",
				$ = "" + K + "-app",
				Q = "app-pages-internals",
				J = "react-refresh",
				Z = "amp",
				ee = "webpack",
				et = "polyfills",
				er = Symbol(et),
				en = "webpack-runtime",
				eo = "edge-runtime-webpack",
				ea = "__N_SSG",
				ei = "__N_SSP",
				el = {
					name: "Times New Roman",
					xAvgCharWidth: 821,
					azAvgWidth: 854.3953488372093,
					unitsPerEm: 2048,
				},
				eu = {
					name: "Arial",
					xAvgCharWidth: 904,
					azAvgWidth: 934.5116279069767,
					unitsPerEm: 2048,
				},
				es = ["/500"],
				ec = 1,
				ef = 6e3,
				ed = { client: "client", server: "server" },
				ep = [
					"clearImmediate",
					"setImmediate",
					"BroadcastChannel",
					"ByteLengthQueuingStrategy",
					"CompressionStream",
					"CountQueuingStrategy",
					"DecompressionStream",
					"DomException",
					"MessageChannel",
					"MessageEvent",
					"MessagePort",
					"ReadableByteStreamController",
					"ReadableStreamBYOBRequest",
					"ReadableStreamDefaultController",
					"TransformStreamDefaultController",
					"WritableStreamDefaultController",
				],
				eh = new Set([K, J, Z, $]);
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		1711: (e, t) => {
			function r(e, t) {
				return (
					void 0 === t && (t = ""),
					("/" === e ? "/index" : /^\/index(\/|$)/.test(e) ? "/index" + e : e) +
						t
				);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => r });
		},
		1715: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "onRecoverableError", {
					enumerable: !0,
					get: () => u,
				});
			const n = r(1532),
				o = r(311),
				a = r(4565),
				i = r(2259),
				l = n._(r(8718)),
				u = (e, t) => {
					const r = (0, l.default)(e) && "cause" in e ? e.cause : e,
						n = (0, i.getReactStitchedError)(r);
					(0, o.isBailoutToCSRError)(r) || (0, a.reportGlobalError)(n);
				};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		1806: (e, t) => {
			function r(e) {
				return "(" === e[0] && e.endsWith(")");
			}
			function n(e) {
				return e.startsWith("@") && "@children" !== e;
			}
			function o(e, t) {
				if (e.includes(a)) {
					const e = JSON.stringify(t);
					return "{}" !== e ? a + "?" + e : a;
				}
				return e;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					DEFAULT_SEGMENT_KEY: () => i,
					PAGE_SEGMENT_KEY: () => a,
					addSearchParamsIfPageSegment: () => o,
					isGroupSegment: () => r,
					isParallelRouteSegment: () => n,
				});
			const a = "__PAGE__",
				i = "__DEFAULT__";
		},
		2013: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "resolveHref", {
					enumerable: !0,
					get: () => f,
				});
			const n = r(1556),
				o = r(7524),
				a = r(5346),
				i = r(2150),
				l = r(4505),
				u = r(4529),
				s = r(4233),
				c = r(6369);
			function f(e, t, r) {
				let f;
				let d = "string" == typeof t ? t : (0, o.formatWithValidation)(t),
					p = d.match(/^[a-zA-Z]{1,}:\/\//),
					h = p ? d.slice(p[0].length) : d;
				if ((h.split("?", 1)[0] || "").match(/(\/\/|\\)/)) {
					console.error(
						"Invalid href '" +
							d +
							"' passed to next/router in page: '" +
							e.pathname +
							"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.",
					);
					const t = (0, i.normalizeRepeatedSlashes)(h);
					d = (p ? p[0] : "") + t;
				}
				if (!(0, u.isLocalURL)(d)) return r ? [d] : d;
				try {
					f = new URL(d.startsWith("#") ? e.asPath : e.pathname, "http://n");
				} catch (e) {
					f = new URL("/", "http://n");
				}
				try {
					const e = new URL(d, f);
					e.pathname = (0, l.normalizePathTrailingSlash)(e.pathname);
					let t = "";
					if ((0, s.isDynamicRoute)(e.pathname) && e.searchParams && r) {
						const r = (0, n.searchParamsToUrlQuery)(e.searchParams),
							{ result: i, params: l } = (0, c.interpolateAs)(
								e.pathname,
								e.pathname,
								r,
							);
						i &&
							(t = (0, o.formatWithValidation)({
								pathname: i,
								hash: e.hash,
								query: (0, a.omit)(r, l),
							}));
					}
					const i =
						e.origin === f.origin ? e.href.slice(e.origin.length) : e.href;
					return r ? [i, t || i] : i;
				} catch (e) {
					return r ? [d] : d;
				}
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		2035: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					HTML_LIMITED_BOT_UA_RE: () => n.HTML_LIMITED_BOT_UA_RE,
					HTML_LIMITED_BOT_UA_RE_STRING: () => a,
					getBotType: () => u,
					isBot: () => l,
				});
			const n = r(2091),
				o =
					/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,
				a = n.HTML_LIMITED_BOT_UA_RE.source;
			function i(e) {
				return n.HTML_LIMITED_BOT_UA_RE.test(e);
			}
			function l(e) {
				return o.test(e) || i(e);
			}
			function u(e) {
				return o.test(e) ? "dom" : i(e) ? "html" : void 0;
			}
		},
		2091: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "HTML_LIMITED_BOT_UA_RE", {
					enumerable: !0,
					get: () => r,
				});
			const r =
				/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview/i;
		},
		2124: (e, t) => {
			function r(e) {
				return Object.prototype.toString.call(e);
			}
			function n(e) {
				if ("[object Object]" !== r(e)) return !1;
				const t = Object.getPrototypeOf(e);
				return null === t || t.hasOwnProperty("isPrototypeOf");
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { getObjectClassLabel: () => r, isPlainObject: () => n });
		},
		2150: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					DecodeError: () => h,
					MiddlewareNotFoundError: () => b,
					MissingStaticPage: () => g,
					NormalizeError: () => _,
					PageNotFoundError: () => m,
					SP: () => d,
					ST: () => p,
					WEB_VITALS: () => r,
					execOnce: () => n,
					getDisplayName: () => u,
					getLocationOrigin: () => i,
					getURL: () => l,
					isAbsoluteUrl: () => a,
					isResSent: () => s,
					loadGetInitialProps: () => f,
					normalizeRepeatedSlashes: () => c,
					stringifyError: () => E,
				});
			const r = ["CLS", "FCP", "FID", "INP", "LCP", "TTFB"];
			function n(e) {
				let t,
					r = !1;
				return () => {
					for (var n = arguments.length, o = Array(n), a = 0; a < n; a++)
						o[a] = arguments[a];
					return r || ((r = !0), (t = e(...o))), t;
				};
			}
			const o = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/,
				a = (e) => o.test(e);
			function i() {
				const { protocol: e, hostname: t, port: r } = window.location;
				return e + "//" + t + (r ? ":" + r : "");
			}
			function l() {
				const { href: e } = window.location,
					t = i();
				return e.substring(t.length);
			}
			function u(e) {
				return "string" == typeof e ? e : e.displayName || e.name || "Unknown";
			}
			function s(e) {
				return e.finished || e.headersSent;
			}
			function c(e) {
				const t = e.split("?");
				return (
					t[0].replace(/\\/g, "/").replace(/\/\/+/g, "/") +
					(t[1] ? "?" + t.slice(1).join("?") : "")
				);
			}
			async function f(e, t) {
				const r = t.res || (t.ctx && t.ctx.res);
				if (!e.getInitialProps)
					return t.ctx && t.Component
						? { pageProps: await f(t.Component, t.ctx) }
						: {};
				const n = await e.getInitialProps(t);
				if (r && s(r)) return n;
				if (!n)
					throw Object.defineProperty(
						Error(
							'"' +
								u(e) +
								'.getInitialProps()" should resolve to an object. But found "' +
								n +
								'" instead.',
						),
						"__NEXT_ERROR_CODE",
						{ value: "E394", enumerable: !1, configurable: !0 },
					);
				return n;
			}
			const d = "undefined" != typeof performance,
				p =
					d &&
					["mark", "measure", "getEntriesByName"].every(
						(e) => "function" == typeof performance[e],
					);
			class h extends Error {}
			class _ extends Error {}
			class m extends Error {
				constructor(e) {
					super(),
						(this.code = "ENOENT"),
						(this.name = "PageNotFoundError"),
						(this.message = "Cannot find module for page: " + e);
				}
			}
			class g extends Error {
				constructor(e, t) {
					super(),
						(this.message =
							"Failed to load static file for page: " + e + " " + t);
				}
			}
			class b extends Error {
				constructor() {
					super(),
						(this.code = "ENOENT"),
						(this.message = "Cannot find the middleware module");
				}
			}
			function E(e) {
				return JSON.stringify({ message: e.message, stack: e.stack });
			}
		},
		2259: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getReactStitchedError", {
					enumerable: !0,
					get: () => s,
				});
			const n = r(1532),
				o = n._(r(148)),
				a = n._(r(8718)),
				i = r(8487),
				l = "react-stack-bottom-frame",
				u = RegExp("(at " + l + " )|(" + l + "\\@)");
			function s(e) {
				const t = (0, a.default)(e),
					r = (t && e.stack) || "",
					n = t ? e.message : "",
					l = r.split("\n"),
					s = l.findIndex((e) => u.test(e)),
					c = s >= 0 ? l.slice(0, s).join("\n") : r,
					f = Object.defineProperty(Error(n), "__NEXT_ERROR_CODE", {
						value: "E394",
						enumerable: !1,
						configurable: !0,
					});
				return (
					Object.assign(f, e),
					(0, i.copyNextErrorCode)(e, f),
					(f.stack = c),
					((e) => {
						if (!o.default.captureOwnerStack) return;
						let t = e.stack || "",
							r = o.default.captureOwnerStack();
						r && !1 === t.endsWith(r) && (e.stack = t += r);
					})(f),
					f
				);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		2272: (e) => {
			var t,
				r,
				n,
				o = (e.exports = {});
			function a() {
				throw Error("setTimeout has not been defined");
			}
			function i() {
				throw Error("clearTimeout has not been defined");
			}
			function l(e) {
				if (t === setTimeout) return setTimeout(e, 0);
				if ((t === a || !t) && setTimeout)
					return (t = setTimeout), setTimeout(e, 0);
				try {
					return t(e, 0);
				} catch (r) {
					try {
						return t.call(null, e, 0);
					} catch (r) {
						return t.call(this, e, 0);
					}
				}
			}
			!(() => {
				try {
					t = "function" == typeof setTimeout ? setTimeout : a;
				} catch (e) {
					t = a;
				}
				try {
					r = "function" == typeof clearTimeout ? clearTimeout : i;
				} catch (e) {
					r = i;
				}
			})();
			var u = [],
				s = !1,
				c = -1;
			function f() {
				s &&
					n &&
					((s = !1), n.length ? (u = n.concat(u)) : (c = -1), u.length && d());
			}
			function d() {
				if (!s) {
					var e = l(f);
					s = !0;
					for (var t = u.length; t; ) {
						for (n = u, u = []; ++c < t; ) n && n[c].run();
						(c = -1), (t = u.length);
					}
					(n = null),
						(s = !1),
						(function (e) {
							if (r === clearTimeout) return clearTimeout(e);
							if ((r === i || !r) && clearTimeout)
								return (r = clearTimeout), clearTimeout(e);
							try {
								r(e);
							} catch (t) {
								try {
									return r.call(null, e);
								} catch (t) {
									return r.call(this, e);
								}
							}
						})(e);
				}
			}
			function p(e, t) {
				(this.fun = e), (this.array = t);
			}
			function h() {}
			(o.nextTick = (e) => {
				var t = Array(arguments.length - 1);
				if (arguments.length > 1)
					for (var r = 1; r < arguments.length; r++) t[r - 1] = arguments[r];
				u.push(new p(e, t)), 1 !== u.length || s || l(d);
			}),
				(p.prototype.run = function () {
					this.fun.apply(null, this.array);
				}),
				(o.title = "browser"),
				(o.browser = !0),
				(o.env = {}),
				(o.argv = []),
				(o.version = ""),
				(o.versions = {}),
				(o.on = h),
				(o.addListener = h),
				(o.once = h),
				(o.off = h),
				(o.removeListener = h),
				(o.removeAllListeners = h),
				(o.emit = h),
				(o.prependListener = h),
				(o.prependOnceListener = h),
				(o.listeners = (e) => []),
				(o.binding = (e) => {
					throw Error("process.binding is not supported");
				}),
				(o.cwd = () => "/"),
				(o.chdir = (e) => {
					throw Error("process.chdir is not supported");
				}),
				(o.umask = () => 0);
		},
		2367: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => a }),
				r(1532);
			const n = r(5640);
			r(148);
			const o = r(3074);
			function a(e) {
				function t(t) {
					return (0, n.jsx)(e, { router: (0, o.useRouter)(), ...t });
				}
				return (
					(t.getInitialProps = e.getInitialProps),
					(t.origGetInitialProps = e.origGetInitialProps),
					t
				);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		2395: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					REDIRECT_ERROR_CODE: () => o,
					RedirectType: () => a,
					isRedirectError: () => i,
				});
			const n = r(109),
				o = "NEXT_REDIRECT";
			var a = ((e) => ((e.push = "push"), (e.replace = "replace"), e))({});
			function i(e) {
				if (
					"object" != typeof e ||
					null === e ||
					!("digest" in e) ||
					"string" != typeof e.digest
				)
					return !1;
				const t = e.digest.split(";"),
					[r, a] = t,
					i = t.slice(2, -2).join(";"),
					l = Number(t.at(-2));
				return (
					r === o &&
					("replace" === a || "push" === a) &&
					"string" == typeof i &&
					!isNaN(l) &&
					l in n.RedirectStatusCode
				);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		2411: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isNextRouterError", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(4953),
				o = r(2395);
			function a(e) {
				return (0, o.isRedirectError)(e) || (0, n.isHTTPAccessFallbackError)(e);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		3074: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					Router: () => a.default,
					createRouter: () => _,
					default: () => p,
					makePublicRouterInstance: () => m,
					useRouter: () => h,
					withRouter: () => u.default,
				});
			const n = r(1532),
				o = n._(r(148)),
				a = n._(r(592)),
				i = r(5512),
				l = n._(r(8718)),
				u = n._(r(2367)),
				s = {
					router: null,
					readyCallbacks: [],
					ready(e) {
						if (this.router) return e();
						this.readyCallbacks.push(e);
					},
				},
				c = [
					"pathname",
					"route",
					"query",
					"asPath",
					"components",
					"isFallback",
					"basePath",
					"locale",
					"locales",
					"defaultLocale",
					"isReady",
					"isPreview",
					"isLocaleDomain",
					"domainLocales",
				],
				f = ["push", "replace", "reload", "back", "prefetch", "beforePopState"];
			function d() {
				if (!s.router)
					throw Object.defineProperty(
						Error(
							'No router instance found.\nYou should only use "next/router" on the client side of your app.\n',
						),
						"__NEXT_ERROR_CODE",
						{ value: "E394", enumerable: !1, configurable: !0 },
					);
				return s.router;
			}
			Object.defineProperty(s, "events", { get: () => a.default.events }),
				c.forEach((e) => {
					Object.defineProperty(s, e, { get: () => d()[e] });
				}),
				f.forEach((e) => {
					s[e] = () => {
						for (var t = arguments.length, r = Array(t), n = 0; n < t; n++)
							r[n] = arguments[n];
						return d()[e](...r);
					};
				}),
				[
					"routeChangeStart",
					"beforeHistoryChange",
					"routeChangeComplete",
					"routeChangeError",
					"hashChangeStart",
					"hashChangeComplete",
				].forEach((e) => {
					s.ready(() => {
						a.default.events.on(e, () => {
							for (var t = arguments.length, r = Array(t), n = 0; n < t; n++)
								r[n] = arguments[n];
							const o = "on" + e.charAt(0).toUpperCase() + e.substring(1);
							if (s[o])
								try {
									s[o](...r);
								} catch (e) {
									console.error("Error when running the Router event: " + o),
										console.error(
											(0, l.default)(e) ? e.message + "\n" + e.stack : e + "",
										);
								}
						});
					});
				});
			const p = s;
			function h() {
				const e = o.default.useContext(i.RouterContext);
				if (!e)
					throw Object.defineProperty(
						Error(
							"NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted",
						),
						"__NEXT_ERROR_CODE",
						{ value: "E509", enumerable: !1, configurable: !0 },
					);
				return e;
			}
			function _() {
				for (var e = arguments.length, t = Array(e), r = 0; r < e; r++)
					t[r] = arguments[r];
				return (
					(s.router = new a.default(...t)),
					s.readyCallbacks.forEach((e) => e()),
					(s.readyCallbacks = []),
					s.router
				);
			}
			function m(e) {
				const t = {};
				for (const r of c) {
					if ("object" == typeof e[r]) {
						t[r] = Object.assign(Array.isArray(e[r]) ? [] : {}, e[r]);
						continue;
					}
					t[r] = e[r];
				}
				return (
					(t.events = a.default.events),
					f.forEach((r) => {
						t[r] = () => {
							for (var t = arguments.length, n = Array(t), o = 0; o < t; o++)
								n[o] = arguments[o];
							return e[r](...n);
						};
					}),
					t
				);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		3255: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "denormalizePagePath", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(4233),
				o = r(7767);
			function a(e) {
				const t = (0, o.normalizePathSep)(e);
				return t.startsWith("/index/") && !(0, n.isDynamicRoute)(t)
					? t.slice(6)
					: "/index" !== t
						? t
						: "/";
			}
		},
		3287: (e, t, r) => {
			let n;
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => i, isEqualNode: () => a });
			const o = r(9343);
			function a(e, t) {
				if (e instanceof HTMLElement && t instanceof HTMLElement) {
					const r = t.getAttribute("nonce");
					if (r && !e.getAttribute("nonce")) {
						const n = t.cloneNode(!0);
						return (
							n.setAttribute("nonce", ""),
							(n.nonce = r),
							r === e.nonce && e.isEqualNode(n)
						);
					}
				}
				return e.isEqualNode(t);
			}
			function i() {
				return {
					mountedInstances: new Set(),
					updateHead: (e) => {
						const t = {};
						e.forEach((e) => {
							if ("link" === e.type && e.props["data-optimized-fonts"]) {
								if (
									document.querySelector(
										'style[data-href="' + e.props["data-href"] + '"]',
									)
								)
									return;
								(e.props.href = e.props["data-href"]),
									(e.props["data-href"] = void 0);
							}
							const r = t[e.type] || [];
							r.push(e), (t[e.type] = r);
						});
						let r = t.title ? t.title[0] : null,
							o = "";
						if (r) {
							const { children: e } = r.props;
							o = "string" == typeof e ? e : Array.isArray(e) ? e.join("") : "";
						}
						o !== document.title && (document.title = o),
							["meta", "base", "link", "style", "script"].forEach((e) => {
								n(e, t[e] || []);
							});
					},
				};
			}
			(n = (e, t) => {
				const r = document.querySelector("head");
				if (!r) return;
				const n = new Set(r.querySelectorAll("" + e + "[data-next-head]"));
				if ("meta" === e) {
					const e = r.querySelector("meta[charset]");
					null !== e && n.add(e);
				}
				const i = [];
				for (let e = 0; e < t.length; e++) {
					const r = ((e) => {
						const { type: t, props: r } = e,
							n = document.createElement(t);
						(0, o.setAttributesFromProps)(n, r);
						const { children: a, dangerouslySetInnerHTML: i } = r;
						return (
							i
								? (n.innerHTML = i.__html || "")
								: a &&
									(n.textContent =
										"string" == typeof a
											? a
											: Array.isArray(a)
												? a.join("")
												: ""),
							n
						);
					})(t[e]);
					r.setAttribute("data-next-head", "");
					let l = !0;
					for (const e of n)
						if (a(e, r)) {
							n.delete(e), (l = !1);
							break;
						}
					l && i.push(r);
				}
				for (const e of n) {
					var l;
					null == (l = e.parentNode) || l.removeChild(e);
				}
				for (const e of i)
					"meta" === e.tagName.toLowerCase() &&
						null !== e.getAttribute("charset") &&
						r.prepend(e),
						r.appendChild(e);
			}),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		3300: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => u });
			const n = r(1532),
				o = r(5640),
				a = n._(r(148)),
				i = r(2150);
			async function l(e) {
				const { Component: t, ctx: r } = e;
				return { pageProps: await (0, i.loadGetInitialProps)(t, r) };
			}
			class u extends a.default.Component {
				render() {
					const { Component: e, pageProps: t } = this.props;
					return (0, o.jsx)(e, { ...t });
				}
			}
			(u.origGetInitialProps = l),
				(u.getInitialProps = l),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		3862: (e, t) => {
			let r;
			function n(e) {
				var t;
				return (
					(null ==
					(t = (() => {
						if (void 0 === r) {
							var e;
							r =
								(null == (e = window.trustedTypes)
									? void 0
									: e.createPolicy("nextjs", {
											createHTML: (e) => e,
											createScript: (e) => e,
											createScriptURL: (e) => e,
										})) || null;
						}
						return r;
					})())
						? void 0
						: t.createScriptURL(e)) || e
				);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "__unsafeCreateTrustedScriptURL", {
					enumerable: !0,
					get: () => n,
				}),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		3872: (e, t, r) => {
			function n(e, t) {
				return e;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "removeLocale", {
					enumerable: !0,
					get: () => n,
				}),
				r(8618),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		4011: (e, t) => {
			function r() {
				return "";
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getDeploymentIdQueryOrEmptyString", {
					enumerable: !0,
					get: () => r,
				});
		},
		4083: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getRouteMatcher", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(2150);
			function o(e) {
				const { re: t, groups: r } = e;
				return (e) => {
					const o = t.exec(e);
					if (!o) return !1;
					const a = (e) => {
							try {
								return decodeURIComponent(e);
							} catch (e) {
								throw Object.defineProperty(
									new n.DecodeError("failed to decode param"),
									"__NEXT_ERROR_CODE",
									{ value: "E528", enumerable: !1, configurable: !0 },
								);
							}
						},
						i = {};
					for (const [e, t] of Object.entries(r)) {
						const r = o[t.pos];
						void 0 !== r &&
							(t.repeat
								? (i[e] = r.split("/").map((e) => a(e)))
								: (i[e] = a(r)));
					}
					return i;
				};
			}
		},
		4233: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					getSortedRouteObjects: () => n.getSortedRouteObjects,
					getSortedRoutes: () => n.getSortedRoutes,
					isDynamicRoute: () => o.isDynamicRoute,
				});
			const n = r(6179),
				o = r(4839);
		},
		4259: (e, t) => {
			function r(e) {
				const {
					ampFirst: t = !1,
					hybrid: r = !1,
					hasQuery: n = !1,
				} = void 0 === e ? {} : e;
				return t || (r && n);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isInAmpMode", {
					enumerable: !0,
					get: () => r,
				});
		},
		4505: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "normalizePathTrailingSlash", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(170),
				o = r(8618),
				a = (e) => {
					if (!e.startsWith("/")) return e;
					const { pathname: t, query: r, hash: a } = (0, o.parsePath)(e);
					return "" + (0, n.removeTrailingSlash)(t) + r + a;
				};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		4529: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isLocalURL", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(2150),
				o = r(8603);
			function a(e) {
				if (!(0, n.isAbsoluteUrl)(e)) return !0;
				try {
					const t = (0, n.getLocationOrigin)(),
						r = new URL(e, t);
					return r.origin === t && (0, o.hasBasePath)(r.pathname);
				} catch (e) {
					return !1;
				}
			}
		},
		4565: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "reportGlobalError", {
					enumerable: !0,
					get: () => r,
				});
			const r =
				"function" == typeof reportError
					? reportError
					: (e) => {
							globalThis.console.error(e);
						};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		4646: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					AppRouterContext: () => o,
					GlobalLayoutRouterContext: () => i,
					LayoutRouterContext: () => a,
					MissingSlotContext: () => u,
					TemplateContext: () => l,
				});
			const n = r(1532)._(r(148)),
				o = n.default.createContext(null),
				a = n.default.createContext(null),
				i = n.default.createContext(null),
				l = n.default.createContext(null),
				u = n.default.createContext(new Set());
		},
		4693: () => {
			"trimStart" in String.prototype ||
				(String.prototype.trimStart = String.prototype.trimLeft),
				"trimEnd" in String.prototype ||
					(String.prototype.trimEnd = String.prototype.trimRight),
				"description" in Symbol.prototype ||
					Object.defineProperty(Symbol.prototype, "description", {
						configurable: !0,
						get: function () {
							var e = /\((.*)\)/.exec(this.toString());
							return e ? e[1] : void 0;
						},
					}),
				Array.prototype.flat ||
					((Array.prototype.flat = function (e, t) {
						return (
							(t = this.concat.apply([], this)),
							e > 1 && t.some(Array.isArray) ? t.flat(e - 1) : t
						);
					}),
					(Array.prototype.flatMap = function (e, t) {
						return this.map(e, t).flat();
					})),
				Promise.prototype.finally ||
					(Promise.prototype.finally = function (e) {
						if ("function" != typeof e) return this.then(e, e);
						var t = this.constructor || Promise;
						return this.then(
							(r) => t.resolve(e()).then(() => r),
							(r) =>
								t.resolve(e()).then(() => {
									throw r;
								}),
						);
					}),
				Object.fromEntries ||
					(Object.fromEntries = (e) =>
						Array.from(e).reduce((e, t) => ((e[t[0]] = t[1]), e), {})),
				Array.prototype.at ||
					(Array.prototype.at = function (e) {
						var t = Math.trunc(e) || 0;
						if ((t < 0 && (t += this.length), !(t < 0 || t >= this.length)))
							return this[t];
					}),
				Object.hasOwn ||
					(Object.hasOwn = (e, t) => {
						if (null == e)
							throw TypeError("Cannot convert undefined or null to object");
						return Object.prototype.hasOwnProperty.call(Object(e), t);
					}),
				"canParse" in URL ||
					(URL.canParse = (e, t) => {
						try {
							return new URL(e, t), !0;
						} catch (e) {
							return !1;
						}
					});
		},
		4724: (e, t) => {
			function r(e) {
				return e
					.split("/")
					.map((e) => encodeURIComponent(e))
					.join("/");
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "encodeURIPath", {
					enumerable: !0,
					get: () => r,
				});
		},
		4731: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "AmpStateContext", {
					enumerable: !0,
					get: () => n,
				});
			const n = r(1532)._(r(148)).default.createContext({});
		},
		4747: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getNextPathnameInfo", {
					enumerable: !0,
					get: () => i,
				});
			const n = r(5810),
				o = r(4944),
				a = r(6368);
			function i(e, t) {
				var r, i;
				const {
						basePath: l,
						i18n: u,
						trailingSlash: s,
					} = null != (r = t.nextConfig) ? r : {},
					c = { pathname: e, trailingSlash: "/" !== e ? e.endsWith("/") : s };
				l &&
					(0, a.pathHasPrefix)(c.pathname, l) &&
					((c.pathname = (0, o.removePathPrefix)(c.pathname, l)),
					(c.basePath = l));
				let f = c.pathname;
				if (
					c.pathname.startsWith("/_next/data/") &&
					c.pathname.endsWith(".json")
				) {
					const e = c.pathname
						.replace(/^\/_next\/data\//, "")
						.replace(/\.json$/, "")
						.split("/");
					(c.buildId = e[0]),
						(f = "index" !== e[1] ? "/" + e.slice(1).join("/") : "/"),
						!0 === t.parseData && (c.pathname = f);
				}
				if (u) {
					let e = t.i18nProvider
						? t.i18nProvider.analyze(c.pathname)
						: (0, n.normalizeLocalePath)(c.pathname, u.locales);
					(c.locale = e.detectedLocale),
						(c.pathname = null != (i = e.pathname) ? i : c.pathname),
						!e.detectedLocale &&
							c.buildId &&
							(e = t.i18nProvider
								? t.i18nProvider.analyze(f)
								: (0, n.normalizeLocalePath)(f, u.locales)).detectedLocale &&
							(c.locale = e.detectedLocale);
				}
				return c;
			}
		},
		4839: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isDynamicRoute", {
					enumerable: !0,
					get: () => i,
				});
			const n = r(7496),
				o = /\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,
				a = /\/\[[^/]+\](?=\/|$)/;
			function i(e, t) {
				return (void 0 === t && (t = !0),
				(0, n.isInterceptionRouteAppPath)(e) &&
					(e = (0, n.extractInterceptionRouteInformation)(e).interceptedRoute),
				t)
					? a.test(e)
					: o.test(e);
			}
		},
		4868: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => i });
			const n = r(148),
				o = n.useLayoutEffect,
				a = n.useEffect;
			function i(e) {
				const { headManager: t, reduceComponentsToState: r } = e;
				function i() {
					if (t && t.mountedInstances) {
						const o = n.Children.toArray(
							Array.from(t.mountedInstances).filter(Boolean),
						);
						t.updateHead(r(o, e));
					}
				}
				return (
					o(() => {
						var r;
						return (
							null == t ||
								null == (r = t.mountedInstances) ||
								r.add(e.children),
							() => {
								var r;
								null == t ||
									null == (r = t.mountedInstances) ||
									r.delete(e.children);
							}
						);
					}),
					o(
						() => (
							t && (t._pendingUpdate = i),
							() => {
								t && (t._pendingUpdate = i);
							}
						),
					),
					a(
						() => (
							t &&
								t._pendingUpdate &&
								(t._pendingUpdate(), (t._pendingUpdate = null)),
							() => {
								t &&
									t._pendingUpdate &&
									(t._pendingUpdate(), (t._pendingUpdate = null));
							}
						),
					),
					null
				);
			}
		},
		4944: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "removePathPrefix", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(6368);
			function o(e, t) {
				if (!(0, n.pathHasPrefix)(e, t)) return e;
				const r = e.slice(t.length);
				return r.startsWith("/") ? r : "/" + r;
			}
		},
		4953: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					HTTPAccessErrorStatus: () => r,
					HTTP_ERROR_FALLBACK_ERROR_CODE: () => o,
					getAccessFallbackErrorTypeByStatus: () => l,
					getAccessFallbackHTTPStatus: () => i,
					isHTTPAccessFallbackError: () => a,
				});
			const r = { NOT_FOUND: 404, FORBIDDEN: 403, UNAUTHORIZED: 401 },
				n = new Set(Object.values(r)),
				o = "NEXT_HTTP_ERROR_FALLBACK";
			function a(e) {
				if (
					"object" != typeof e ||
					null === e ||
					!("digest" in e) ||
					"string" != typeof e.digest
				)
					return !1;
				const [t, r] = e.digest.split(";");
				return t === o && n.has(Number(r));
			}
			function i(e) {
				return Number(e.digest.split(";")[1]);
			}
			function l(e) {
				switch (e) {
					case 401:
						return "unauthorized";
					case 403:
						return "forbidden";
					case 404:
						return "not-found";
					default:
						return;
				}
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		5143: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "addPathSuffix", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(8618);
			function o(e, t) {
				if (!e.startsWith("/") || !t) return e;
				const { pathname: r, query: o, hash: a } = (0, n.parsePath)(e);
				return "" + r + t + o + a;
			}
		},
		5346: (e, t) => {
			function r(e, t) {
				const r = {};
				return (
					Object.keys(e).forEach((n) => {
						t.includes(n) || (r[n] = e[n]);
					}),
					r
				);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "omit", { enumerable: !0, get: () => r });
		},
		5475: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "addLocale", { enumerable: !0, get: () => n }),
				r(4505);
			const n = (e) => {
				for (
					var t = arguments.length, r = Array(t > 1 ? t - 1 : 0), n = 1;
					n < t;
					n++
				)
					r[n - 1] = arguments[n];
				return e;
			};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		5512: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "RouterContext", {
					enumerable: !0,
					get: () => n,
				});
			const n = r(1532)._(r(148)).default.createContext(null);
		},
		5810: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "normalizeLocalePath", {
					enumerable: !0,
					get: () => n,
				});
			const r = new WeakMap();
			function n(e, t) {
				let n;
				if (!t) return { pathname: e };
				let o = r.get(t);
				o || ((o = t.map((e) => e.toLowerCase())), r.set(t, o));
				const a = e.split("/", 2);
				if (!a[1]) return { pathname: e };
				const i = a[1].toLowerCase(),
					l = o.indexOf(i);
				return l < 0
					? { pathname: e }
					: ((n = t[l]),
						{
							pathname: (e = e.slice(n.length + 1) || "/"),
							detectedLocale: n,
						});
			}
		},
		5856: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "addBasePath", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(9925),
				o = r(4505);
			function a(e, t) {
				return (0, o.normalizePathTrailingSlash)((0, n.addPathPrefix)(e, ""));
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		6179: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { getSortedRouteObjects: () => o, getSortedRoutes: () => n });
			class r {
				insert(e) {
					this._insert(e.split("/").filter(Boolean), [], !1);
				}
				smoosh() {
					return this._smoosh();
				}
				_smoosh(e) {
					void 0 === e && (e = "/");
					const t = [...this.children.keys()].sort();
					null !== this.slugName && t.splice(t.indexOf("[]"), 1),
						null !== this.restSlugName && t.splice(t.indexOf("[...]"), 1),
						null !== this.optionalRestSlugName &&
							t.splice(t.indexOf("[[...]]"), 1);
					const r = t
						.map((t) => this.children.get(t)._smoosh("" + e + t + "/"))
						.reduce((e, t) => [...e, ...t], []);
					if (
						(null !== this.slugName &&
							r.push(
								...this.children
									.get("[]")
									._smoosh(e + "[" + this.slugName + "]/"),
							),
						!this.placeholder)
					) {
						const t = "/" === e ? "/" : e.slice(0, -1);
						if (null != this.optionalRestSlugName)
							throw Object.defineProperty(
								Error(
									'You cannot define a route with the same specificity as a optional catch-all route ("' +
										t +
										'" and "' +
										t +
										"[[..." +
										this.optionalRestSlugName +
										']]").',
								),
								"__NEXT_ERROR_CODE",
								{ value: "E458", enumerable: !1, configurable: !0 },
							);
						r.unshift(t);
					}
					return (
						null !== this.restSlugName &&
							r.push(
								...this.children
									.get("[...]")
									._smoosh(e + "[..." + this.restSlugName + "]/"),
							),
						null !== this.optionalRestSlugName &&
							r.push(
								...this.children
									.get("[[...]]")
									._smoosh(e + "[[..." + this.optionalRestSlugName + "]]/"),
							),
						r
					);
				}
				_insert(e, t, n) {
					if (0 === e.length) {
						this.placeholder = !1;
						return;
					}
					if (n)
						throw Object.defineProperty(
							Error("Catch-all must be the last part of the URL."),
							"__NEXT_ERROR_CODE",
							{ value: "E392", enumerable: !1, configurable: !0 },
						);
					let o = e[0];
					if (o.startsWith("[") && o.endsWith("]")) {
						let r = o.slice(1, -1),
							i = !1;
						if (
							(r.startsWith("[") &&
								r.endsWith("]") &&
								((r = r.slice(1, -1)), (i = !0)),
							r.startsWith("…"))
						)
							throw Object.defineProperty(
								Error(
									"Detected a three-dot character ('…') at ('" +
										r +
										"'). Did you mean ('...')?",
								),
								"__NEXT_ERROR_CODE",
								{ value: "E147", enumerable: !1, configurable: !0 },
							);
						if (
							(r.startsWith("...") && ((r = r.substring(3)), (n = !0)),
							r.startsWith("[") || r.endsWith("]"))
						)
							throw Object.defineProperty(
								Error(
									"Segment names may not start or end with extra brackets ('" +
										r +
										"').",
								),
								"__NEXT_ERROR_CODE",
								{ value: "E421", enumerable: !1, configurable: !0 },
							);
						if (r.startsWith("."))
							throw Object.defineProperty(
								Error(
									"Segment names may not start with erroneous periods ('" +
										r +
										"').",
								),
								"__NEXT_ERROR_CODE",
								{ value: "E288", enumerable: !1, configurable: !0 },
							);
						function a(e, r) {
							if (null !== e && e !== r)
								throw Object.defineProperty(
									Error(
										"You cannot use different slug names for the same dynamic path ('" +
											e +
											"' !== '" +
											r +
											"').",
									),
									"__NEXT_ERROR_CODE",
									{ value: "E337", enumerable: !1, configurable: !0 },
								);
							t.forEach((e) => {
								if (e === r)
									throw Object.defineProperty(
										Error(
											'You cannot have the same slug name "' +
												r +
												'" repeat within a single dynamic path',
										),
										"__NEXT_ERROR_CODE",
										{ value: "E247", enumerable: !1, configurable: !0 },
									);
								if (e.replace(/\W/g, "") === o.replace(/\W/g, ""))
									throw Object.defineProperty(
										Error(
											'You cannot have the slug names "' +
												e +
												'" and "' +
												r +
												'" differ only by non-word symbols within a single dynamic path',
										),
										"__NEXT_ERROR_CODE",
										{ value: "E499", enumerable: !1, configurable: !0 },
									);
							}),
								t.push(r);
						}
						if (n) {
							if (i) {
								if (null != this.restSlugName)
									throw Object.defineProperty(
										Error(
											'You cannot use both an required and optional catch-all route at the same level ("[...' +
												this.restSlugName +
												']" and "' +
												e[0] +
												'" ).',
										),
										"__NEXT_ERROR_CODE",
										{ value: "E299", enumerable: !1, configurable: !0 },
									);
								a(this.optionalRestSlugName, r),
									(this.optionalRestSlugName = r),
									(o = "[[...]]");
							} else {
								if (null != this.optionalRestSlugName)
									throw Object.defineProperty(
										Error(
											'You cannot use both an optional and required catch-all route at the same level ("[[...' +
												this.optionalRestSlugName +
												']]" and "' +
												e[0] +
												'").',
										),
										"__NEXT_ERROR_CODE",
										{ value: "E300", enumerable: !1, configurable: !0 },
									);
								a(this.restSlugName, r), (this.restSlugName = r), (o = "[...]");
							}
						} else {
							if (i)
								throw Object.defineProperty(
									Error(
										'Optional route parameters are not yet supported ("' +
											e[0] +
											'").',
									),
									"__NEXT_ERROR_CODE",
									{ value: "E435", enumerable: !1, configurable: !0 },
								);
							a(this.slugName, r), (this.slugName = r), (o = "[]");
						}
					}
					this.children.has(o) || this.children.set(o, new r()),
						this.children.get(o)._insert(e.slice(1), t, n);
				}
				constructor() {
					(this.placeholder = !0),
						(this.children = new Map()),
						(this.slugName = null),
						(this.restSlugName = null),
						(this.optionalRestSlugName = null);
				}
			}
			function n(e) {
				const t = new r();
				return e.forEach((e) => t.insert(e)), t.smoosh();
			}
			function o(e, t) {
				const r = {},
					o = [];
				for (let n = 0; n < e.length; n++) {
					const a = t(e[n]);
					(r[a] = n), (o[n] = a);
				}
				return n(o).map((t) => e[r[t]]);
			}
		},
		6192: (e, t) => {
			function r(e, t) {
				if ((void 0 === t && (t = {}), t.onlyHashChange)) {
					e();
					return;
				}
				const r = document.documentElement,
					n = r.style.scrollBehavior;
				(r.style.scrollBehavior = "auto"),
					t.dontForceLayout || r.getClientRects(),
					e(),
					(r.style.scrollBehavior = n);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "handleSmoothScroll", {
					enumerable: !0,
					get: () => r,
				});
		},
		6239: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { VALID_LOADERS: () => r, imageConfigDefault: () => n });
			const r = ["default", "imgix", "cloudinary", "akamai", "custom"],
				n = {
					deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
					imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
					path: "/_next/image",
					loader: "default",
					loaderFile: "",
					domains: [],
					disableStaticImages: !1,
					minimumCacheTTL: 60,
					formats: ["image/webp"],
					dangerouslyAllowSVG: !1,
					contentSecurityPolicy:
						"script-src 'none'; frame-src 'none'; sandbox;",
					contentDispositionType: "attachment",
					localPatterns: void 0,
					remotePatterns: [],
					qualities: void 0,
					unoptimized: !1,
				};
		},
		6368: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "pathHasPrefix", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(8618);
			function o(e, t) {
				if ("string" != typeof e) return !1;
				const { pathname: r } = (0, n.parsePath)(e);
				return r === t || r.startsWith(t + "/");
			}
		},
		6369: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "interpolateAs", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(4083),
				o = r(258);
			function a(e, t, r) {
				let a = "",
					i = (0, o.getRouteRegex)(e),
					l = i.groups,
					u = (t !== e ? (0, n.getRouteMatcher)(i)(t) : "") || r;
				a = e;
				const s = Object.keys(l);
				return (
					s.every((e) => {
						let t = u[e] || "",
							{ repeat: r, optional: n } = l[e],
							o = "[" + (r ? "..." : "") + e + "]";
						return (
							n && (o = (t ? "" : "/") + "[" + o + "]"),
							r && !Array.isArray(t) && (t = [t]),
							(n || e in u) &&
								(a =
									a.replace(
										o,
										r
											? t.map((e) => encodeURIComponent(e)).join("/")
											: encodeURIComponent(t),
									) || "/")
						);
					}) || (a = ""),
					{ params: s, result: a }
				);
			}
		},
		6658: (e) => {
			e.exports = [
				"chrome 64",
				"edge 79",
				"firefox 67",
				"opera 51",
				"safari 12",
			];
		},
		6732: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					createRouteLoader: () => m,
					getClientBuildManifest: () => h,
					isAssetError: () => c,
					markAssetError: () => s,
				}),
				r(1532),
				r(1711);
			const n = r(3862),
				o = r(9307),
				a = r(4011),
				i = r(4724);
			function l(e, t, r) {
				let n,
					o = t.get(e);
				if (o) return "future" in o ? o.future : Promise.resolve(o);
				const a = new Promise((e) => {
					n = e;
				});
				return (
					t.set(e, { resolve: n, future: a }),
					r
						? r()
								.then((e) => (n(e), e))
								.catch((r) => {
									throw (t.delete(e), r);
								})
						: a
				);
			}
			const u = Symbol("ASSET_LOAD_ERROR");
			function s(e) {
				return Object.defineProperty(e, u, {});
			}
			function c(e) {
				return e && u in e;
			}
			const f = ((e) => {
					try {
						return (
							(e = document.createElement("link")),
							(!!window.MSInputMethodContext && !!document.documentMode) ||
								e.relList.supports("prefetch")
						);
					} catch (e) {
						return !1;
					}
				})(),
				d = () => (0, a.getDeploymentIdQueryOrEmptyString)();
			function p(e, t, r) {
				return new Promise((n, a) => {
					let i = !1;
					e
						.then((e) => {
							(i = !0), n(e);
						})
						.catch(a),
						(0, o.requestIdleCallback)(() =>
							setTimeout(() => {
								i || a(r);
							}, t),
						);
				});
			}
			function h() {
				return self.__BUILD_MANIFEST
					? Promise.resolve(self.__BUILD_MANIFEST)
					: p(
							new Promise((e) => {
								const t = self.__BUILD_MANIFEST_CB;
								self.__BUILD_MANIFEST_CB = () => {
									e(self.__BUILD_MANIFEST), t && t();
								};
							}),
							3800,
							s(
								Object.defineProperty(
									Error("Failed to load client build manifest"),
									"__NEXT_ERROR_CODE",
									{ value: "E273", enumerable: !1, configurable: !0 },
								),
							),
						);
			}
			function _(e, t) {
				return h().then((r) => {
					if (!(t in r))
						throw s(
							Object.defineProperty(
								Error("Failed to lookup route: " + t),
								"__NEXT_ERROR_CODE",
								{ value: "E446", enumerable: !1, configurable: !0 },
							),
						);
					const o = r[t].map((t) => e + "/_next/" + (0, i.encodeURIPath)(t));
					return {
						scripts: o
							.filter((e) => e.endsWith(".js"))
							.map((e) => (0, n.__unsafeCreateTrustedScriptURL)(e) + d()),
						css: o.filter((e) => e.endsWith(".css")).map((e) => e + d()),
					};
				});
			}
			function m(e) {
				const t = new Map(),
					r = new Map(),
					n = new Map(),
					a = new Map();
				function i(e) {
					{
						var t;
						let n = r.get(e.toString());
						return n
							? n
							: document.querySelector('script[src^="' + e + '"]')
								? Promise.resolve()
								: (r.set(
										e.toString(),
										(n = new Promise((r, n) => {
											((t = document.createElement("script")).onload = r),
												(t.onerror = () =>
													n(
														s(
															Object.defineProperty(
																Error("Failed to load script: " + e),
																"__NEXT_ERROR_CODE",
																{
																	value: "E74",
																	enumerable: !1,
																	configurable: !0,
																},
															),
														),
													)),
												(t.crossOrigin = void 0),
												(t.src = e),
												document.body.appendChild(t);
										})),
									),
									n);
					}
				}
				function u(e) {
					let t = n.get(e);
					return (
						t ||
							n.set(
								e,
								(t = fetch(e, { credentials: "same-origin" })
									.then((t) => {
										if (!t.ok)
											throw Object.defineProperty(
												Error("Failed to load stylesheet: " + e),
												"__NEXT_ERROR_CODE",
												{ value: "E189", enumerable: !1, configurable: !0 },
											);
										return t.text().then((t) => ({ href: e, content: t }));
									})
									.catch((e) => {
										throw s(e);
									})),
							),
						t
					);
				}
				return {
					whenEntrypoint: (e) => l(e, t),
					onEntrypoint(e, r) {
						(r
							? Promise.resolve()
									.then(() => r())
									.then(
										(e) => ({ component: (e && e.default) || e, exports: e }),
										(e) => ({ error: e }),
									)
							: Promise.resolve(void 0)
						).then((r) => {
							const n = t.get(e);
							n && "resolve" in n
								? r && (t.set(e, r), n.resolve(r))
								: (r ? t.set(e, r) : t.delete(e), a.delete(e));
						});
					},
					loadRoute(r, n) {
						return l(r, a, () => {
							let o;
							return p(
								_(e, r)
									.then((e) => {
										const { scripts: n, css: o } = e;
										return Promise.all([
											t.has(r) ? [] : Promise.all(n.map(i)),
											Promise.all(o.map(u)),
										]);
									})
									.then((e) =>
										this.whenEntrypoint(r).then((t) => ({
											entrypoint: t,
											styles: e[1],
										})),
									),
								3800,
								s(
									Object.defineProperty(
										Error("Route did not complete loading: " + r),
										"__NEXT_ERROR_CODE",
										{ value: "E12", enumerable: !1, configurable: !0 },
									),
								),
							)
								.then((e) => {
									const { entrypoint: t, styles: r } = e,
										n = Object.assign({ styles: r }, t);
									return "error" in t ? t : n;
								})
								.catch((e) => {
									if (n) throw e;
									return { error: e };
								})
								.finally(() => (null == o ? void 0 : o()));
						});
					},
					prefetch(t) {
						let r;
						return (r = navigator.connection) &&
							(r.saveData || /2g/.test(r.effectiveType))
							? Promise.resolve()
							: _(e, t)
									.then((e) =>
										Promise.all(
											f
												? e.scripts.map((e) => {
														var t, r, n;
														return (
															(t = e.toString()),
															(r = "script"),
															new Promise((e, o) => {
																const a =
																	'\n      link[rel="prefetch"][href^="' +
																	t +
																	'"],\n      link[rel="preload"][href^="' +
																	t +
																	'"],\n      script[src^="' +
																	t +
																	'"]';
																if (document.querySelector(a)) return e();
																(n = document.createElement("link")),
																	r && (n.as = r),
																	(n.rel = "prefetch"),
																	(n.crossOrigin = void 0),
																	(n.onload = e),
																	(n.onerror = () =>
																		o(
																			s(
																				Object.defineProperty(
																					Error("Failed to prefetch: " + t),
																					"__NEXT_ERROR_CODE",
																					{
																						value: "E268",
																						enumerable: !1,
																						configurable: !0,
																					},
																				),
																			),
																		)),
																	(n.href = t),
																	document.head.appendChild(n);
															})
														);
													})
												: [],
										),
									)
									.then(() => {
										(0, o.requestIdleCallback)(() =>
											this.loadRoute(t, !0).catch(() => {}),
										);
									})
									.catch(() => {});
					},
				};
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		6786: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					ACTION_SUFFIX: () => f,
					APP_DIR_ALIAS: () => I,
					CACHE_ONE_YEAR: () => v,
					DOT_NEXT_ALIAS: () => C,
					ESLINT_DEFAULT_DIRS: () => $,
					GSP_NO_RETURNED_VALUE: () => G,
					GSSP_COMPONENT_MEMBER_ERROR: () => z,
					GSSP_NO_RETURNED_VALUE: () => q,
					INFINITE_CACHE: () => O,
					INSTRUMENTATION_HOOK_FILENAME: () => T,
					MATCHED_PATH_HEADER: () => o,
					MIDDLEWARE_FILENAME: () => S,
					MIDDLEWARE_LOCATION_REGEXP: () => j,
					NEXT_BODY_SUFFIX: () => h,
					NEXT_CACHE_IMPLICIT_TAG_ID: () => R,
					NEXT_CACHE_REVALIDATED_TAGS_HEADER: () => m,
					NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER: () => g,
					NEXT_CACHE_SOFT_TAG_MAX_LENGTH: () => P,
					NEXT_CACHE_TAGS_HEADER: () => _,
					NEXT_CACHE_TAG_MAX_ITEMS: () => E,
					NEXT_CACHE_TAG_MAX_LENGTH: () => y,
					NEXT_DATA_SUFFIX: () => d,
					NEXT_INTERCEPTION_MARKER_PREFIX: () => n,
					NEXT_META_SUFFIX: () => p,
					NEXT_QUERY_PARAM_PREFIX: () => r,
					NEXT_RESUME_HEADER: () => b,
					NON_STANDARD_NODE_ENV: () => Y,
					PAGES_DIR_ALIAS: () => A,
					PRERENDER_REVALIDATE_HEADER: () => a,
					PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER: () => i,
					PUBLIC_DIR_MIDDLEWARE_CONFLICT: () => k,
					ROOT_DIR_ALIAS: () => w,
					RSC_ACTION_CLIENT_WRAPPER_ALIAS: () => U,
					RSC_ACTION_ENCRYPTION_ALIAS: () => D,
					RSC_ACTION_PROXY_ALIAS: () => M,
					RSC_ACTION_VALIDATE_ALIAS: () => x,
					RSC_CACHE_WRAPPER_ALIAS: () => L,
					RSC_MOD_REF_PROXY_ALIAS: () => N,
					RSC_PREFETCH_SUFFIX: () => l,
					RSC_SEGMENTS_DIR_SUFFIX: () => u,
					RSC_SEGMENT_SUFFIX: () => s,
					RSC_SUFFIX: () => c,
					SERVER_PROPS_EXPORT_ERROR: () => W,
					SERVER_PROPS_GET_INIT_PROPS_CONFLICT: () => B,
					SERVER_PROPS_SSG_CONFLICT: () => H,
					SERVER_RUNTIME: () => Q,
					SSG_FALLBACK_EXPORT_ERROR: () => K,
					SSG_GET_INITIAL_PROPS_CONFLICT: () => F,
					STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR: () => X,
					UNSTABLE_REVALIDATE_RENAME_ERROR: () => V,
					WEBPACK_LAYERS: () => Z,
					WEBPACK_RESOURCE_QUERIES: () => ee,
				});
			const r = "nxtP",
				n = "nxtI",
				o = "x-matched-path",
				a = "x-prerender-revalidate",
				i = "x-prerender-revalidate-if-generated",
				l = ".prefetch.rsc",
				u = ".segments",
				s = ".segment.rsc",
				c = ".rsc",
				f = ".action",
				d = ".json",
				p = ".meta",
				h = ".body",
				_ = "x-next-cache-tags",
				m = "x-next-revalidated-tags",
				g = "x-next-revalidate-tag-token",
				b = "next-resume",
				E = 128,
				y = 256,
				P = 1024,
				R = "_N_T_",
				v = 31536e3,
				O = 0xfffffffe,
				S = "middleware",
				j = `(?:src/)?${S}`,
				T = "instrumentation",
				A = "private-next-pages",
				C = "private-dot-next",
				w = "private-next-root-dir",
				I = "private-next-app-dir",
				N = "private-next-rsc-mod-ref-proxy",
				x = "private-next-rsc-action-validate",
				M = "private-next-rsc-server-reference",
				L = "private-next-rsc-cache-wrapper",
				D = "private-next-rsc-action-encryption",
				U = "private-next-rsc-action-client-wrapper",
				k =
					"You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",
				F =
					"You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",
				B =
					"You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",
				H =
					"You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",
				X =
					"can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",
				W =
					"pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",
				G =
					"Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",
				q =
					"Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",
				V =
					"The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",
				z =
					"can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",
				Y =
					'You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',
				K =
					"Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",
				$ = ["app", "pages", "components", "lib", "src"],
				Q = {
					edge: "edge",
					experimentalEdge: "experimental-edge",
					nodejs: "nodejs",
				},
				J = {
					shared: "shared",
					reactServerComponents: "rsc",
					serverSideRendering: "ssr",
					actionBrowser: "action-browser",
					apiNode: "api-node",
					apiEdge: "api-edge",
					middleware: "middleware",
					instrument: "instrument",
					edgeAsset: "edge-asset",
					appPagesBrowser: "app-pages-browser",
					pagesDirBrowser: "pages-dir-browser",
					pagesDirEdge: "pages-dir-edge",
					pagesDirNode: "pages-dir-node",
				},
				Z = {
					...J,
					GROUP: {
						builtinReact: [J.reactServerComponents, J.actionBrowser],
						serverOnly: [
							J.reactServerComponents,
							J.actionBrowser,
							J.instrument,
							J.middleware,
						],
						neutralTarget: [J.apiNode, J.apiEdge],
						clientOnly: [J.serverSideRendering, J.appPagesBrowser],
						bundled: [
							J.reactServerComponents,
							J.actionBrowser,
							J.serverSideRendering,
							J.appPagesBrowser,
							J.shared,
							J.instrument,
							J.middleware,
						],
						appPages: [
							J.reactServerComponents,
							J.serverSideRendering,
							J.appPagesBrowser,
							J.actionBrowser,
						],
					},
				},
				ee = {
					edgeSSREntry: "__next_edge_ssr_entry__",
					metadata: "__next_metadata__",
					metadataRoute: "__next_metadata_route__",
					metadataImageMeta: "__next_metadata_image_meta__",
				};
		},
		7410: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { RouteAnnouncer: () => u, default: () => s });
			const n = r(1532),
				o = r(5640),
				a = n._(r(148)),
				i = r(3074),
				l = {
					border: 0,
					clip: "rect(0 0 0 0)",
					height: "1px",
					margin: "-1px",
					overflow: "hidden",
					padding: 0,
					position: "absolute",
					top: 0,
					width: "1px",
					whiteSpace: "nowrap",
					wordWrap: "normal",
				},
				u = () => {
					const { asPath: e } = (0, i.useRouter)(),
						[t, r] = a.default.useState(""),
						n = a.default.useRef(e);
					return (
						a.default.useEffect(() => {
							if (n.current !== e) {
								if (((n.current = e), document.title)) r(document.title);
								else {
									var t;
									const n = document.querySelector("h1");
									r(
										(null != (t = null == n ? void 0 : n.innerText)
											? t
											: null == n
												? void 0
												: n.textContent) || e,
									);
								}
							}
						}, [e]),
						(0, o.jsx)("p", {
							"aria-live": "assertive",
							id: "__next-route-announcer__",
							role: "alert",
							style: l,
							children: t,
						})
					);
				},
				s = u;
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		7439: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					PathParamsContext: () => i,
					PathnameContext: () => a,
					SearchParamsContext: () => o,
				});
			const n = r(148),
				o = (0, n.createContext)(null),
				a = (0, n.createContext)(null),
				i = (0, n.createContext)(null);
		},
		7496: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					INTERCEPTION_ROUTE_MARKERS: () => o,
					extractInterceptionRouteInformation: () => i,
					isInterceptionRouteAppPath: () => a,
				});
			const n = r(9987),
				o = ["(..)(..)", "(.)", "(..)", "(...)"];
			function a(e) {
				return (
					void 0 !== e.split("/").find((e) => o.find((t) => e.startsWith(t)))
				);
			}
			function i(e) {
				let t, r, a;
				for (const n of e.split("/"))
					if ((r = o.find((e) => n.startsWith(e)))) {
						[t, a] = e.split(r, 2);
						break;
					}
				if (!t || !r || !a)
					throw Object.defineProperty(
						Error(
							"Invalid interception route: " +
								e +
								". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>",
						),
						"__NEXT_ERROR_CODE",
						{ value: "E269", enumerable: !1, configurable: !0 },
					);
				switch (((t = (0, n.normalizeAppPath)(t)), r)) {
					case "(.)":
						a = "/" === t ? "/" + a : t + "/" + a;
						break;
					case "(..)":
						if ("/" === t)
							throw Object.defineProperty(
								Error(
									"Invalid interception route: " +
										e +
										". Cannot use (..) marker at the root level, use (.) instead.",
								),
								"__NEXT_ERROR_CODE",
								{ value: "E207", enumerable: !1, configurable: !0 },
							);
						a = t.split("/").slice(0, -1).concat(a).join("/");
						break;
					case "(...)":
						a = "/" + a;
						break;
					case "(..)(..)":
						const i = t.split("/");
						if (i.length <= 2)
							throw Object.defineProperty(
								Error(
									"Invalid interception route: " +
										e +
										". Cannot use (..)(..) marker at the root level or one level up.",
								),
								"__NEXT_ERROR_CODE",
								{ value: "E486", enumerable: !1, configurable: !0 },
							);
						a = i.slice(0, -2).concat(a).join("/");
						break;
					default:
						throw Object.defineProperty(
							Error("Invariant: unexpected marker"),
							"__NEXT_ERROR_CODE",
							{ value: "E112", enumerable: !1, configurable: !0 },
						);
				}
				return { interceptingRoute: t, interceptedRoute: a };
			}
		},
		7524: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					formatUrl: () => a,
					formatWithValidation: () => l,
					urlObjectKeys: () => i,
				});
			const n = r(8781)._(r(1556)),
				o = /https?|ftp|gopher|file/;
			function a(e) {
				let { auth: t, hostname: r } = e,
					a = e.protocol || "",
					i = e.pathname || "",
					l = e.hash || "",
					u = e.query || "",
					s = !1;
				(t = t ? encodeURIComponent(t).replace(/%3A/i, ":") + "@" : ""),
					e.host
						? (s = t + e.host)
						: r &&
							((s = t + (~r.indexOf(":") ? "[" + r + "]" : r)),
							e.port && (s += ":" + e.port)),
					u &&
						"object" == typeof u &&
						(u = String(n.urlQueryToSearchParams(u)));
				let c = e.search || (u && "?" + u) || "";
				return (
					a && !a.endsWith(":") && (a += ":"),
					e.slashes || ((!a || o.test(a)) && !1 !== s)
						? ((s = "//" + (s || "")), i && "/" !== i[0] && (i = "/" + i))
						: s || (s = ""),
					l && "#" !== l[0] && (l = "#" + l),
					c && "?" !== c[0] && (c = "?" + c),
					"" +
						a +
						s +
						(i = i.replace(/[?#]/g, encodeURIComponent)) +
						(c = c.replace("#", "%23")) +
						l
				);
			}
			const i = [
				"auth",
				"hash",
				"host",
				"hostname",
				"href",
				"path",
				"pathname",
				"port",
				"protocol",
				"query",
				"search",
				"slashes",
			];
			function l(e) {
				return a(e);
			}
		},
		7529: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "parseRelativeUrl", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(2150),
				o = r(1556);
			function a(e, t, r) {
				void 0 === r && (r = !0);
				const a = new URL((0, n.getLocationOrigin)()),
					i = t
						? new URL(t, a)
						: e.startsWith(".")
							? new URL(window.location.href)
							: a,
					{
						pathname: l,
						searchParams: u,
						search: s,
						hash: c,
						href: f,
						origin: d,
					} = new URL(e, i);
				if (d !== a.origin)
					throw Object.defineProperty(
						Error("invariant: invalid relative URL, router received " + e),
						"__NEXT_ERROR_CODE",
						{ value: "E159", enumerable: !1, configurable: !0 },
					);
				return {
					pathname: l,
					query: r ? (0, o.searchParamsToUrlQuery)(u) : void 0,
					search: s,
					hash: c,
					href: f.slice(d.length),
				};
			}
		},
		7767: (e, t) => {
			function r(e) {
				return e.replace(/\\/g, "/");
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "normalizePathSep", {
					enumerable: !0,
					get: () => r,
				});
		},
		7811: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "warnOnce", { enumerable: !0, get: () => r });
			const r = (e) => {};
		},
		7868: (e, t) => {
			function r(e) {
				return "/api" === e || !!(null == e ? void 0 : e.startsWith("/api/"));
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isAPIRoute", {
					enumerable: !0,
					get: () => r,
				});
		},
		8113: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "ImageConfigContext", {
					enumerable: !0,
					get: () => a,
				});
			const n = r(1532)._(r(148)),
				o = r(6239),
				a = n.default.createContext(o.imageConfigDefault);
		},
		8126: () => {},
		8318: (e, t) => {
			function r(e) {
				return e.startsWith("/") ? e : "/" + e;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "ensureLeadingSlash", {
					enumerable: !0,
					get: () => r,
				});
		},
		8487: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					copyNextErrorCode: () => n,
					createDigestWithErrorCode: () => r,
					extractNextErrorCode: () => o,
				});
			const r = (e, t) =>
					"object" == typeof e && null !== e && "__NEXT_ERROR_CODE" in e
						? `${t}@${e.__NEXT_ERROR_CODE}`
						: t,
				n = (e, t) => {
					const r = o(e);
					r &&
						"object" == typeof t &&
						null !== t &&
						Object.defineProperty(t, "__NEXT_ERROR_CODE", {
							value: r,
							enumerable: !1,
							configurable: !0,
						});
				},
				o = (e) =>
					"object" == typeof e &&
					null !== e &&
					"__NEXT_ERROR_CODE" in e &&
					"string" == typeof e.__NEXT_ERROR_CODE
						? e.__NEXT_ERROR_CODE
						: "object" == typeof e &&
								null !== e &&
								"digest" in e &&
								"string" == typeof e.digest
							? e.digest.split("@").find((e) => e.startsWith("E"))
							: void 0;
		},
		8521: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => c });
			const n = r(1532),
				o = r(5640),
				a = n._(r(148)),
				i = n._(r(9051)),
				l = {
					400: "Bad Request",
					404: "This page could not be found",
					405: "Method Not Allowed",
					500: "Internal Server Error",
				};
			function u(e) {
				const { req: t, res: r, err: n } = e;
				return {
					statusCode: r && r.statusCode ? r.statusCode : n ? n.statusCode : 404,
					hostname: window.location.hostname,
				};
			}
			const s = {
				error: {
					fontFamily:
						'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',
					height: "100vh",
					textAlign: "center",
					display: "flex",
					flexDirection: "column",
					alignItems: "center",
					justifyContent: "center",
				},
				desc: { lineHeight: "48px" },
				h1: {
					display: "inline-block",
					margin: "0 20px 0 0",
					paddingRight: 23,
					fontSize: 24,
					fontWeight: 500,
					verticalAlign: "top",
				},
				h2: { fontSize: 14, fontWeight: 400, lineHeight: "28px" },
				wrap: { display: "inline-block" },
			};
			class c extends a.default.Component {
				render() {
					const { statusCode: e, withDarkMode: t = !0 } = this.props,
						r = this.props.title || l[e] || "An unexpected error has occurred";
					return (0, o.jsxs)("div", {
						style: s.error,
						children: [
							(0, o.jsx)(i.default, {
								children: (0, o.jsx)("title", {
									children: e
										? e + ": " + r
										: "Application error: a client-side exception has occurred",
								}),
							}),
							(0, o.jsxs)("div", {
								style: s.desc,
								children: [
									(0, o.jsx)("style", {
										dangerouslySetInnerHTML: {
											__html:
												"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}" +
												(t
													? "@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"
													: ""),
										},
									}),
									e
										? (0, o.jsx)("h1", {
												className: "next-error-h1",
												style: s.h1,
												children: e,
											})
										: null,
									(0, o.jsx)("div", {
										style: s.wrap,
										children: (0, o.jsxs)("h2", {
											style: s.h2,
											children: [
												this.props.title || e
													? r
													: (0, o.jsxs)(o.Fragment, {
															children: [
																"Application error: a client-side exception has occurred",
																" ",
																!!this.props.hostname &&
																	(0, o.jsxs)(o.Fragment, {
																		children: [
																			"while loading ",
																			this.props.hostname,
																		],
																	}),
																" ",
																"(see the browser console for more information)",
															],
														}),
												".",
											],
										}),
									}),
								],
							}),
						],
					});
				}
			}
			(c.displayName = "ErrorPage"),
				(c.getInitialProps = u),
				(c.origGetInitialProps = u),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		8603: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "hasBasePath", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(6368);
			function o(e) {
				return (0, n.pathHasPrefix)(e, "");
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		8618: (e, t) => {
			function r(e) {
				const t = e.indexOf("#"),
					r = e.indexOf("?"),
					n = r > -1 && (t < 0 || r < t);
				return n || t > -1
					? {
							pathname: e.substring(0, n ? r : t),
							query: n ? e.substring(r, t > -1 ? t : void 0) : "",
							hash: t > -1 ? e.slice(t) : "",
						}
					: { pathname: e, query: "", hash: "" };
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "parsePath", { enumerable: !0, get: () => r });
		},
		8718: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => o, getProperError: () => a });
			const n = r(2124);
			function o(e) {
				return (
					"object" == typeof e && null !== e && "name" in e && "message" in e
				);
			}
			function a(e) {
				return o(e)
					? e
					: Object.defineProperty(
							Error(
								(0, n.isPlainObject)(e)
									? ((e) => {
											const t = new WeakSet();
											return JSON.stringify(e, (e, r) => {
												if ("object" == typeof r && null !== r) {
													if (t.has(r)) return "[Circular]";
													t.add(r);
												}
												return r;
											});
										})(e)
									: e + "",
							),
							"__NEXT_ERROR_CODE",
							{ value: "E394", enumerable: !1, configurable: !0 },
						);
			}
		},
		8771: (e, t) => {
			function r() {
				const e = Object.create(null);
				return {
					on(t, r) {
						(e[t] || (e[t] = [])).push(r);
					},
					off(t, r) {
						e[t] && e[t].splice(e[t].indexOf(r) >>> 0, 1);
					},
					emit(t) {
						for (
							var r = arguments.length, n = Array(r > 1 ? r - 1 : 0), o = 1;
							o < r;
							o++
						)
							n[o - 1] = arguments[o];
						(e[t] || []).slice().map((e) => {
							e(...n);
						});
					},
				};
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => r });
		},
		8777: (e, t) => {
			let r;
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => n, setConfig: () => o });
			const n = () => r;
			function o(e) {
				r = e;
			}
		},
		8781: (e, t, r) => {
			function n(e) {
				if ("function" != typeof WeakMap) return null;
				var t = new WeakMap(),
					r = new WeakMap();
				return (n = (e) => (e ? r : t))(e);
			}
			function o(e, t) {
				if (!t && e && e.__esModule) return e;
				if (null === e || ("object" != typeof e && "function" != typeof e))
					return { default: e };
				var r = n(t);
				if (r && r.has(e)) return r.get(e);
				var o = { __proto__: null },
					a = Object.defineProperty && Object.getOwnPropertyDescriptor;
				for (var i in e)
					if ("default" !== i && Object.prototype.hasOwnProperty.call(e, i)) {
						var l = a ? Object.getOwnPropertyDescriptor(e, i) : null;
						l && (l.get || l.set)
							? Object.defineProperty(o, i, l)
							: (o[i] = e[i]);
					}
				return (o.default = e), r && r.set(e, o), o;
			}
			r.r(t), r.d(t, { _: () => o });
		},
		8881: (e, t, r) => {
			let n, o, a, i, l, u, s, c, f, d, p, h;
			Object.defineProperty(t, "__esModule", { value: !0 });
			const _ = r(8781);
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, {
					emitter: () => X,
					hydrate: () => eu,
					initialize: () => V,
					router: () => n,
					version: () => H,
				});
			const m = r(1532),
				g = r(5640);
			r(4693);
			const b = m._(r(148)),
				E = m._(r(7324)),
				y = r(9283),
				P = m._(r(8771)),
				R = r(5512),
				v = r(6192),
				O = r(4839),
				S = r(1556),
				j = r(8777),
				T = r(2150),
				A = r(710),
				C = m._(r(3287)),
				w = m._(r(1108)),
				I = r(7410),
				N = r(3074),
				x = r(8718),
				M = r(8113),
				L = r(9861),
				D = r(8603),
				U = r(4646),
				k = r(461),
				F = r(7439),
				B = r(1715);
			r(9069), r(2411);
			let H = "15.2.4",
				X = (0, P.default)(),
				W = (e) => [].slice.call(e),
				G = !1;
			class q extends b.default.Component {
				componentDidCatch(e, t) {
					this.props.fn(e, t);
				}
				componentDidMount() {
					this.scrollToHash(),
						n.isSsr &&
							(o.isFallback ||
								(o.nextExport &&
									((0, O.isDynamicRoute)(n.pathname) ||
										location.search ||
										G)) ||
								(o.props && o.props.__N_SSG && (location.search || G))) &&
							n
								.replace(
									n.pathname +
										"?" +
										String(
											(0, S.assign)(
												(0, S.urlQueryToSearchParams)(n.query),
												new URLSearchParams(location.search),
											),
										),
									a,
									{ _h: 1, shallow: !o.isFallback && !G },
								)
								.catch((e) => {
									if (!e.cancelled) throw e;
								});
				}
				componentDidUpdate() {
					this.scrollToHash();
				}
				scrollToHash() {
					let { hash: e } = location;
					if (!(e = e && e.substring(1))) return;
					const t = document.getElementById(e);
					t && setTimeout(() => t.scrollIntoView(), 0);
				}
				render() {
					return this.props.children;
				}
			}
			async function V(e) {
				void 0 === e && (e = {}),
					(o = JSON.parse(
						document.getElementById("__NEXT_DATA__").textContent,
					)),
					(window.__NEXT_DATA__ = o),
					(h = o.defaultLocale);
				const t = o.assetPrefix || "";
				if (
					(self.__next_set_public_path__("" + t + "/_next/"),
					(0, j.setConfig)({
						serverRuntimeConfig: {},
						publicRuntimeConfig: o.runtimeConfig || {},
					}),
					(a = (0, T.getURL)()),
					(0, D.hasBasePath)(a) && (a = (0, L.removeBasePath)(a)),
					o.scriptLoader)
				) {
					const { initScriptLoader: e } = r(144);
					e(o.scriptLoader);
				}
				i = new w.default(o.buildId, t);
				const s = (e) => {
					const [t, r] = e;
					return i.routeLoader.onEntrypoint(t, r);
				};
				return (
					window.__NEXT_P &&
						window.__NEXT_P.map((e) => setTimeout(() => s(e), 0)),
					(window.__NEXT_P = []),
					(window.__NEXT_P.push = s),
					((u = (0, C.default)()).getIsSsr = () => n.isSsr),
					(l = document.getElementById("__next")),
					{ assetPrefix: t }
				);
			}
			function z(e, t) {
				return (0, g.jsx)(e, { ...t });
			}
			function Y(e) {
				var t;
				const { children: r } = e,
					o = b.default.useMemo(() => (0, k.adaptForAppRouterInstance)(n), []);
				return (0, g.jsx)(q, {
					fn: (e) =>
						$({ App: f, err: e }).catch((e) =>
							console.error("Error rendering page: ", e),
						),
					children: (0, g.jsx)(U.AppRouterContext.Provider, {
						value: o,
						children: (0, g.jsx)(F.SearchParamsContext.Provider, {
							value: (0, k.adaptForSearchParams)(n),
							children: (0, g.jsx)(k.PathnameContextProviderAdapter, {
								router: n,
								isAutoExport: null != (t = self.__NEXT_DATA__.autoExport) && t,
								children: (0, g.jsx)(F.PathParamsContext.Provider, {
									value: (0, k.adaptForPathParams)(n),
									children: (0, g.jsx)(R.RouterContext.Provider, {
										value: (0, N.makePublicRouterInstance)(n),
										children: (0, g.jsx)(y.HeadManagerContext.Provider, {
											value: u,
											children: (0, g.jsx)(M.ImageConfigContext.Provider, {
												value: {
													deviceSizes: [
														640, 750, 828, 1080, 1200, 1920, 2048, 3840,
													],
													imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
													path: "/_next/image",
													loader: "default",
													dangerouslyAllowSVG: !1,
													unoptimized: !0,
												},
												children: r,
											}),
										}),
									}),
								}),
							}),
						}),
					}),
				});
			}
			const K = (e) => (t) => {
				const r = { ...t, Component: p, err: o.err, router: n };
				return (0, g.jsx)(Y, { children: z(e, r) });
			};
			function $(e) {
				let { App: t, err: l } = e;
				return (
					console.error(l),
					console.error(
						"A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred",
					),
					i
						.loadPage("/_error")
						.then((n) => {
							const { page: o, styleSheets: a } = n;
							return (null == s ? void 0 : s.Component) === o
								? Promise.resolve()
										.then(() => _._(r(8521)))
										.then((n) =>
											Promise.resolve()
												.then(() => _._(r(3300)))
												.then((r) => ((e.App = t = r.default), n)),
										)
										.then((e) => ({
											ErrorComponent: e.default,
											styleSheets: [],
										}))
								: { ErrorComponent: o, styleSheets: a };
						})
						.then((r) => {
							var i;
							const { ErrorComponent: u, styleSheets: s } = r,
								c = K(t),
								f = {
									Component: u,
									AppTree: c,
									router: n,
									ctx: {
										err: l,
										pathname: o.page,
										query: o.query,
										asPath: a,
										AppTree: c,
									},
								};
							return Promise.resolve(
								(null == (i = e.props) ? void 0 : i.err)
									? e.props
									: (0, T.loadGetInitialProps)(t, f),
							).then((t) =>
								ei({ ...e, err: l, Component: u, styleSheets: s, props: t }),
							);
						})
				);
			}
			function Q(e) {
				const { callback: t } = e;
				return b.default.useLayoutEffect(() => t(), [t]), null;
			}
			let J = {
					navigationStart: "navigationStart",
					beforeRender: "beforeRender",
					afterRender: "afterRender",
					afterHydrate: "afterHydrate",
					routeChange: "routeChange",
				},
				Z = {
					hydration: "Next.js-hydration",
					beforeHydration: "Next.js-before-hydration",
					routeChangeToRender: "Next.js-route-change-to-render",
					render: "Next.js-render",
				},
				ee = null,
				et = !0;
			function er() {
				[J.beforeRender, J.afterHydrate, J.afterRender, J.routeChange].forEach(
					(e) => performance.clearMarks(e),
				);
			}
			function en() {
				T.ST &&
					(performance.mark(J.afterHydrate),
					performance.getEntriesByName(J.beforeRender, "mark").length &&
						(performance.measure(
							Z.beforeHydration,
							J.navigationStart,
							J.beforeRender,
						),
						performance.measure(Z.hydration, J.beforeRender, J.afterHydrate)),
					d && performance.getEntriesByName(Z.hydration).forEach(d),
					er());
			}
			function eo() {
				if (!T.ST) return;
				performance.mark(J.afterRender);
				const e = performance.getEntriesByName(J.routeChange, "mark");
				e.length &&
					(performance.getEntriesByName(J.beforeRender, "mark").length &&
						(performance.measure(
							Z.routeChangeToRender,
							e[0].name,
							J.beforeRender,
						),
						performance.measure(Z.render, J.beforeRender, J.afterRender),
						d &&
							(performance.getEntriesByName(Z.render).forEach(d),
							performance.getEntriesByName(Z.routeChangeToRender).forEach(d))),
					er(),
					[Z.routeChangeToRender, Z.render].forEach((e) =>
						performance.clearMeasures(e),
					));
			}
			function ea(e) {
				const { callbacks: t, children: r } = e;
				return b.default.useLayoutEffect(() => t.forEach((e) => e()), [t]), r;
			}
			function ei(e) {
				let t,
					{ App: r, Component: o, props: a, err: i } = e,
					u = "initial" in e ? void 0 : e.styleSheets;
				o = o || s.Component;
				const f = { ...(a = a || s.props), Component: o, err: i, router: n };
				s = f;
				let d = !1,
					p = new Promise((e, r) => {
						c && c(),
							(t = () => {
								(c = null), e();
							}),
							(c = () => {
								(d = !0), (c = null);
								const e = Object.defineProperty(
									Error("Cancel rendering route"),
									"__NEXT_ERROR_CODE",
									{ value: "E503", enumerable: !1, configurable: !0 },
								);
								(e.cancelled = !0), r(e);
							});
					});
				function h() {
					t();
				}
				!(() => {
					if (!u) return;
					const e = new Set(
							W(document.querySelectorAll("style[data-n-href]")).map((e) =>
								e.getAttribute("data-n-href"),
							),
						),
						t = document.querySelector("noscript[data-n-css]"),
						r = null == t ? void 0 : t.getAttribute("data-n-css");
					u.forEach((t) => {
						const { href: n, text: o } = t;
						if (!e.has(n)) {
							const e = document.createElement("style");
							e.setAttribute("data-n-href", n),
								e.setAttribute("media", "x"),
								r && e.setAttribute("nonce", r),
								document.head.appendChild(e),
								e.appendChild(document.createTextNode(o));
						}
					});
				})();
				const _ = (0, g.jsxs)(g.Fragment, {
					children: [
						(0, g.jsx)(Q, {
							callback: () => {
								if (u && !d) {
									const e = new Set(u.map((e) => e.href)),
										t = W(document.querySelectorAll("style[data-n-href]")),
										r = t.map((e) => e.getAttribute("data-n-href"));
									for (let n = 0; n < r.length; ++n)
										e.has(r[n])
											? t[n].removeAttribute("media")
											: t[n].setAttribute("media", "x");
									let n = document.querySelector("noscript[data-n-css]");
									n &&
										u.forEach((e) => {
											const { href: t } = e,
												r = document.querySelector(
													'style[data-n-href="' + t + '"]',
												);
											r &&
												(n.parentNode.insertBefore(r, n.nextSibling), (n = r));
										}),
										W(document.querySelectorAll("link[data-n-p]")).forEach(
											(e) => {
												e.parentNode.removeChild(e);
											},
										);
								}
								if (e.scroll) {
									const { x: t, y: r } = e.scroll;
									(0, v.handleSmoothScroll)(() => {
										window.scrollTo(t, r);
									});
								}
							},
						}),
						(0, g.jsxs)(Y, {
							children: [
								z(r, f),
								(0, g.jsx)(A.Portal, {
									type: "next-route-announcer",
									children: (0, g.jsx)(I.RouteAnnouncer, {}),
								}),
							],
						}),
					],
				});
				return (
					!((e, t) => {
						T.ST && performance.mark(J.beforeRender);
						const r = t(et ? en : eo);
						ee
							? (0, b.default.startTransition)(() => {
									ee.render(r);
								})
							: ((ee = E.default.hydrateRoot(e, r, {
									onRecoverableError: B.onRecoverableError,
								})),
								(et = !1));
					})(l, (e) => (0, g.jsx)(ea, { callbacks: [e, h], children: _ })),
					p
				);
			}
			async function el(e) {
				if (e.err && (void 0 === e.Component || !e.isHydratePass)) {
					await $(e);
					return;
				}
				try {
					await ei(e);
				} catch (r) {
					const t = (0, x.getProperError)(r);
					if (t.cancelled) throw t;
					await $({ ...e, err: t });
				}
			}
			async function eu(e) {
				let t = o.err;
				try {
					const e = await i.routeLoader.whenEntrypoint("/_app");
					if ("error" in e) throw e.error;
					const { component: t, exports: r } = e;
					(f = t),
						r &&
							r.reportWebVitals &&
							(d = (e) => {
								let t,
									{
										id: n,
										name: o,
										startTime: a,
										value: i,
										duration: l,
										entryType: u,
										entries: s,
										attribution: c,
									} = e,
									f =
										Date.now() +
										"-" +
										(Math.floor(Math.random() * (9e12 - 1)) + 1e12);
								s && s.length && (t = s[0].startTime);
								const d = {
									id: n || f,
									name: o,
									startTime: a || t,
									value: null == i ? l : i,
									label:
										"mark" === u || "measure" === u ? "custom" : "web-vital",
								};
								c && (d.attribution = c), r.reportWebVitals(d);
							});
					const n = await i.routeLoader.whenEntrypoint(o.page);
					if ("error" in n) throw n.error;
					p = n.component;
				} catch (e) {
					t = (0, x.getProperError)(e);
				}
				window.__NEXT_PRELOADREADY &&
					(await window.__NEXT_PRELOADREADY(o.dynamicIds)),
					(n = (0, N.createRouter)(o.page, o.query, a, {
						initialProps: o.props,
						pageLoader: i,
						App: f,
						Component: p,
						wrapApp: K,
						err: t,
						isFallback: !!o.isFallback,
						subscription: (e, t, r) =>
							el(Object.assign({}, e, { App: t, scroll: r })),
						locale: o.locale,
						locales: o.locales,
						defaultLocale: h,
						domainLocales: o.domainLocales,
						isPreview: o.isPreview,
					})),
					(G = await n._initialMatchesMiddlewarePromise);
				const r = {
					App: f,
					initial: !0,
					Component: p,
					props: o.props,
					err: t,
					isHydratePass: !0,
				};
				(null == e ? void 0 : e.beforeRender) && (await e.beforeRender()),
					el(r);
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9051: (e, t, r) => {
			var n = r(2272);
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => m, defaultHead: () => d });
			const o = r(1532),
				a = r(8781),
				i = r(5640),
				l = a._(r(148)),
				u = o._(r(4868)),
				s = r(4731),
				c = r(9283),
				f = r(4259);
			function d(e) {
				void 0 === e && (e = !1);
				const t = [(0, i.jsx)("meta", { charSet: "utf-8" }, "charset")];
				return (
					e ||
						t.push(
							(0, i.jsx)(
								"meta",
								{ name: "viewport", content: "width=device-width" },
								"viewport",
							),
						),
					t
				);
			}
			function p(e, t) {
				return "string" == typeof t || "number" == typeof t
					? e
					: t.type === l.default.Fragment
						? e.concat(
								l.default.Children.toArray(t.props.children).reduce(
									(e, t) =>
										"string" == typeof t || "number" == typeof t
											? e
											: e.concat(t),
									[],
								),
							)
						: e.concat(t);
			}
			r(7811);
			const h = ["name", "httpEquiv", "charSet", "itemProp"];
			function _(e, t) {
				const { inAmpMode: r } = t;
				return e
					.reduce(p, [])
					.reverse()
					.concat(d(r).reverse())
					.filter(
						(() => {
							const e = new Set(),
								t = new Set(),
								r = new Set(),
								n = {};
							return (o) => {
								let a = !0,
									i = !1;
								if (
									o.key &&
									"number" != typeof o.key &&
									o.key.indexOf("$") > 0
								) {
									i = !0;
									const t = o.key.slice(o.key.indexOf("$") + 1);
									e.has(t) ? (a = !1) : e.add(t);
								}
								switch (o.type) {
									case "title":
									case "base":
										t.has(o.type) ? (a = !1) : t.add(o.type);
										break;
									case "meta":
										for (let e = 0, t = h.length; e < t; e++) {
											const t = h[e];
											if (o.props.hasOwnProperty(t)) {
												if ("charSet" === t) r.has(t) ? (a = !1) : r.add(t);
												else {
													const e = o.props[t],
														r = n[t] || new Set();
													("name" !== t || !i) && r.has(e)
														? (a = !1)
														: (r.add(e), (n[t] = r));
												}
											}
										}
								}
								return a;
							};
						})(),
					)
					.reverse()
					.map((e, t) => {
						const o = e.key || t;
						if (
							n.env.__NEXT_OPTIMIZE_FONTS &&
							!r &&
							"link" === e.type &&
							e.props.href &&
							[
								"https://fonts.googleapis.com/css",
								"https://use.typekit.net/",
							].some((t) => e.props.href.startsWith(t))
						) {
							const t = { ...(e.props || {}) };
							return (
								(t["data-href"] = t.href),
								(t.href = void 0),
								(t["data-optimized-fonts"] = !0),
								l.default.cloneElement(e, t)
							);
						}
						return l.default.cloneElement(e, { key: o });
					});
			}
			const m = (e) => {
				const { children: t } = e,
					r = (0, l.useContext)(s.AmpStateContext),
					n = (0, l.useContext)(c.HeadManagerContext);
				return (0, i.jsx)(u.default, {
					reduceComponentsToState: _,
					headManager: n,
					inAmpMode: (0, f.isInAmpMode)(r),
					children: t,
				});
			};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9069: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => i });
			const n = r(1532)._(r(8771));
			class o {
				end(e) {
					if ("ended" === this.state.state)
						throw Object.defineProperty(
							Error("Span has already ended"),
							"__NEXT_ERROR_CODE",
							{ value: "E17", enumerable: !1, configurable: !0 },
						);
					(this.state = {
						state: "ended",
						endTime: null != e ? e : Date.now(),
					}),
						this.onSpanEnd(this);
				}
				constructor(e, t, r) {
					var n, o;
					(this.name = e),
						(this.attributes = null != (n = t.attributes) ? n : {}),
						(this.startTime = null != (o = t.startTime) ? o : Date.now()),
						(this.onSpanEnd = r),
						(this.state = { state: "inprogress" });
				}
			}
			class a {
				startSpan(e, t) {
					return new o(e, t, this.handleSpanEnd);
				}
				onSpanEnd(e) {
					return (
						this._emitter.on("spanend", e),
						() => {
							this._emitter.off("spanend", e);
						}
					);
				}
				constructor() {
					(this._emitter = (0, n.default)()),
						(this.handleSpanEnd = (e) => {
							this._emitter.emit("spanend", e);
						});
				}
			}
			const i = new a();
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9283: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "HeadManagerContext", {
					enumerable: !0,
					get: () => n,
				});
			const n = r(1532)._(r(148)).default.createContext({});
		},
		9307: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { cancelIdleCallback: () => n, requestIdleCallback: () => r });
			const r =
					("undefined" != typeof self &&
						self.requestIdleCallback &&
						self.requestIdleCallback.bind(window)) ||
					((e) => {
						const t = Date.now();
						return self.setTimeout(() => {
							e({
								didTimeout: !1,
								timeRemaining: () => Math.max(0, 50 - (Date.now() - t)),
							});
						}, 1);
					}),
				n =
					("undefined" != typeof self &&
						self.cancelIdleCallback &&
						self.cancelIdleCallback.bind(window)) ||
					((e) => clearTimeout(e));
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9343: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "setAttributesFromProps", {
					enumerable: !0,
					get: () => a,
				});
			const r = {
					acceptCharset: "accept-charset",
					className: "class",
					htmlFor: "for",
					httpEquiv: "http-equiv",
					noModule: "noModule",
				},
				n = [
					"onLoad",
					"onReady",
					"dangerouslySetInnerHTML",
					"children",
					"onError",
					"strategy",
					"stylesheets",
				];
			function o(e) {
				return ["async", "defer", "noModule"].includes(e);
			}
			function a(e, t) {
				for (const [a, i] of Object.entries(t)) {
					if (!t.hasOwnProperty(a) || n.includes(a) || void 0 === i) continue;
					const l = r[a] || a.toLowerCase();
					"SCRIPT" === e.tagName && o(l)
						? (e[l] = !!i)
						: e.setAttribute(l, String(i)),
						(!1 === i ||
							("SCRIPT" === e.tagName && o(l) && (!i || "false" === i))) &&
							(e.setAttribute(l, ""), e.removeAttribute(l));
				}
			}
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9372: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "detectDomainLocale", {
					enumerable: !0,
					get: () => r,
				});
			const r = () => {
				for (var e = arguments.length, t = Array(e), r = 0; r < e; r++)
					t[r] = arguments[r];
			};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		9702: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }), r(58);
			const n = r(8881);
			(window.next = {
				version: n.version,
				get router() {
					return n.router;
				},
				emitter: n.emitter,
			}),
				(0, n.initialize)({})
					.then(() => (0, n.hydrate)())
					.catch(console.error),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		9861: (e, t, r) => {
			function n(e) {
				return e;
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "removeBasePath", {
					enumerable: !0,
					get: () => n,
				}),
				r(8603),
				("function" == typeof t.default ||
					("object" == typeof t.default && null !== t.default)) &&
					void 0 === t.default.__esModule &&
					(Object.defineProperty(t.default, "__esModule", { value: !0 }),
					Object.assign(t.default, t),
					(e.exports = t.default));
		},
		9925: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "addPathPrefix", {
					enumerable: !0,
					get: () => o,
				});
			const n = r(8618);
			function o(e, t) {
				if (!e.startsWith("/") || !t) return e;
				const { pathname: r, query: o, hash: a } = (0, n.parsePath)(e);
				return "" + t + r + o + a;
			}
		},
		9987: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { normalizeAppPath: () => a, normalizeRscURL: () => i });
			const n = r(8318),
				o = r(1806);
			function a(e) {
				return (0, n.ensureLeadingSlash)(
					e
						.split("/")
						.reduce(
							(e, t, r, n) =>
								!t ||
								(0, o.isGroupSegment)(t) ||
								"@" === t[0] ||
								(("page" === t || "route" === t) && r === n.length - 1)
									? e
									: e + "/" + t,
							"",
						),
				);
			}
			function i(e) {
				return e.replace(/\.rsc($|\?)/, "$1");
			}
		},
	},
	(e) => {
		var t = (t) => e((e.s = t));
		e.O(0, [593], () => t(9702)), (_N_E = e.O());
	},
]);
