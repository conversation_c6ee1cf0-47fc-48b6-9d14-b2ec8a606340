(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[974],
	{
		415: (e, s, a) => {
			Promise.resolve().then(a.bind(a, 7812));
		},
		900: (e, s, a) => {
			a.d(s, { p: () => i });
			var r = a(8081),
				t = a(2149),
				l = a(7687);
			const i = t.forwardRef((e, s) => {
				const { className: a, type: t, ...i } = e;
				return (0, r.jsx)("input", {
					type: t,
					className: (0, l.cn)(
						"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						a,
					),
					ref: s,
					...i,
				});
			});
			i.displayName = "Input";
		},
		5160: (e, s, a) => {
			a.d(s, { $: () => c, r: () => o });
			var r = a(8081),
				t = a(2149),
				l = a(3629),
				i = a(3484),
				n = a(7687);
			const o = (0, i.F)(
					"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
					{
						variants: {
							variant: {
								default:
									"bg-primary text-primary-foreground hover:bg-primary/90",
								destructive:
									"bg-destructive text-destructive-foreground hover:bg-destructive/90",
								outline:
									"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
								secondary:
									"bg-secondary text-secondary-foreground hover:bg-secondary/80",
								ghost: "hover:bg-accent hover:text-accent-foreground",
								link: "text-primary underline-offset-4 hover:underline",
							},
							size: {
								default: "h-10 px-4 py-2",
								sm: "h-9 rounded-md px-3",
								lg: "h-11 rounded-md px-8",
								icon: "h-10 w-10",
							},
						},
						defaultVariants: { variant: "default", size: "default" },
					},
				),
				c = t.forwardRef((e, s) => {
					const {
							className: a,
							variant: t,
							size: i,
							asChild: c = !1,
							...d
						} = e,
						m = c ? l.DX : "button";
					return (0, r.jsx)(m, {
						className: (0, n.cn)(o({ variant: t, size: i, className: a })),
						ref: s,
						...d,
					});
				});
			c.displayName = "Button";
		},
		7687: (e, s, a) => {
			a.d(s, { cn: () => l });
			var r = a(6522),
				t = a(4483);
			function l() {
				for (var e = arguments.length, s = Array(e), a = 0; a < e; a++)
					s[a] = arguments[a];
				return (0, t.QP)((0, r.$)(s));
			}
		},
		7812: (e, s, a) => {
			a.d(s, { default: () => $ });
			var r = a(8081),
				t = a(2149),
				l = a(5160),
				i = a(900),
				n = a(9176),
				o = a(3484),
				c = a(7687);
			const d = (0, o.F)(
					"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
				),
				m = t.forwardRef((e, s) => {
					const { className: a, ...t } = e;
					return (0, r.jsx)(n.b, {
						ref: s,
						className: (0, c.cn)(d(), a),
						...t,
					});
				});
			m.displayName = n.b.displayName;
			var x = a(7278);
			const h = x.bL,
				u = t.forwardRef((e, s) => {
					const { className: a, ...t } = e;
					return (0, r.jsx)(x.B8, {
						ref: s,
						className: (0, c.cn)(
							"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
							a,
						),
						...t,
					});
				});
			u.displayName = x.B8.displayName;
			const p = t.forwardRef((e, s) => {
				const { className: a, ...t } = e;
				return (0, r.jsx)(x.l9, {
					ref: s,
					className: (0, c.cn)(
						"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
						a,
					),
					...t,
				});
			});
			p.displayName = x.l9.displayName;
			const g = t.forwardRef((e, s) => {
				const { className: a, ...t } = e;
				return (0, r.jsx)(x.UC, {
					ref: s,
					className: (0, c.cn)(
						"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
						a,
					),
					...t,
				});
			});
			g.displayName = x.UC.displayName;
			var f = a(1337),
				v = a(2522);
			const j = f.bL,
				b = f.l9,
				N = f.ZL;
			f.bm;
			const y = t.forwardRef((e, s) => {
				const { className: a, ...t } = e;
				return (0, r.jsx)(f.hJ, {
					ref: s,
					className: (0, c.cn)(
						"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
						a,
					),
					...t,
				});
			});
			y.displayName = f.hJ.displayName;
			const w = t.forwardRef((e, s) => {
				const { className: a, children: t, ...l } = e;
				return (0, r.jsxs)(N, {
					children: [
						(0, r.jsx)(y, {}),
						(0, r.jsxs)(f.UC, {
							ref: s,
							className: (0, c.cn)(
								"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
								a,
							),
							...l,
							children: [
								t,
								(0, r.jsxs)(f.bm, {
									className:
										"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
									children: [
										(0, r.jsx)(v.A, { className: "h-4 w-4" }),
										(0, r.jsx)("span", {
											className: "sr-only",
											children: "Close",
										}),
									],
								}),
							],
						}),
					],
				});
			});
			w.displayName = f.UC.displayName;
			const k = (e) => {
				const { className: s, ...a } = e;
				return (0, r.jsx)("div", {
					className: (0, c.cn)(
						"flex flex-col space-y-1.5 text-center sm:text-left",
						s,
					),
					...a,
				});
			};
			k.displayName = "DialogHeader";
			const C = t.forwardRef((e, s) => {
				const { className: a, ...t } = e;
				return (0, r.jsx)(f.hE, {
					ref: s,
					className: (0, c.cn)(
						"text-lg font-semibold leading-none tracking-tight",
						a,
					),
					...t,
				});
			});
			(C.displayName = f.hE.displayName),
				(t.forwardRef((e, s) => {
					const { className: a, ...t } = e;
					return (0, r.jsx)(f.VY, {
						ref: s,
						className: (0, c.cn)("text-sm text-muted-foreground", a),
						...t,
					});
				}).displayName = f.VY.displayName);
			var F = a(2950),
				S = a(6546),
				A = a(4150),
				q = a(1511),
				P = a(7950),
				I = a.n(P),
				R = a(1737),
				D = a(9651),
				E = a(2783),
				B = a(6003);
			function $() {
				const [e, s] = (0, t.useState)(null),
					[a, n] = (0, t.useState)("creative"),
					[o, c] = (0, t.useState)(""),
					[d, x] = (0, t.useState)(""),
					[f, v] = (0, t.useState)(null),
					N = (0, D.useRouter)(),
					y = (0, E.As)(),
					P = (e) => {
						e.preventDefault(),
							"creative" === a
								? N.push("/search-projects")
								: N.push("/search-creatives"),
							s(null);
					},
					$ = async (e) => {
						e.preventDefault(), v(null);
						try {
							const e = await (0, B.eJ)(y, o, d);
							console.log("Signup successful:", e.user), s(null), c(""), x("");
						} catch (e) {
							console.error("Signup error:", e), v(e.message);
						}
					};
				return (0, r.jsxs)("div", {
					className: "min-h-screen bg-white",
					children: [
						(0, r.jsx)("header", {
							className: "border-b border-gray-100",
							children: (0, r.jsxs)("div", {
								className:
									"max-w-7xl mx-auto px-6 py-6 flex items-center justify-between",
								children: [
									(0, r.jsxs)("nav", {
										className: "flex items-center space-x-8",
										children: [
											(0, r.jsx)(I(), {
												href: "#",
												className:
													"text-gray-900 hover:text-gray-600 transition-colors",
												children: "Work",
											}),
											(0, r.jsx)(I(), {
												href: "#",
												className:
													"text-gray-900 hover:text-gray-600 transition-colors",
												children: "About",
											}),
											(0, r.jsx)(I(), {
												href: "#",
												className:
													"text-gray-900 hover:text-gray-600 transition-colors",
												children: "creatives",
											}),
											(0, r.jsx)(I(), {
												href: "#",
												className:
													"text-gray-900 hover:text-gray-600 transition-colors",
												children: "Bookers",
											}),
											(0, r.jsx)(F.A, {
												className:
													"h-5 w-5 text-gray-400 cursor-pointer hover:text-gray-600",
											}),
										],
									}),
									(0, r.jsxs)("div", {
										className: "flex items-center space-x-6",
										children: [
											(0, r.jsx)("span", {
												className: "text-2xl font-light tracking-wide",
												children: "DUA",
											}),
											(0, r.jsxs)("div", {
												className: "flex items-center space-x-4",
												children: [
													(0, r.jsxs)(j, {
														open: "login" === e,
														onOpenChange: (e) => s(e ? "login" : null),
														children: [
															(0, r.jsx)(b, {
																asChild: !0,
																children: (0, r.jsx)(l.$, {
																	variant: "ghost",
																	className: "text-sm",
																	children: "Login",
																}),
															}),
															(0, r.jsxs)(w, {
																className: "sm:max-w-md",
																children: [
																	(0, r.jsx)(k, {
																		children: (0, r.jsx)(C, {
																			className: "text-center",
																			children: "Welcome back",
																		}),
																	}),
																	(0, r.jsxs)(h, {
																		value: a,
																		onValueChange: (e) => n(e),
																		children: [
																			(0, r.jsxs)(u, {
																				className: "grid w-full grid-cols-2",
																				children: [
																					(0, r.jsx)(p, {
																						value: "creative",
																						children: "Artist",
																					}),
																					(0, r.jsx)(p, {
																						value: "booker",
																						children: "Booker",
																					}),
																				],
																			}),
																			(0, r.jsx)(g, {
																				value: "creative",
																				className: "space-y-4",
																				children: (0, r.jsxs)("form", {
																					onSubmit: P,
																					className: "space-y-4",
																					children: [
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "email",
																									children: "Email",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "email",
																									type: "email",
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "password",
																									children: "Password",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "password",
																									type: "password",
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsx)(l.$, {
																							type: "submit",
																							className: "w-full",
																							children: "Sign In",
																						}),
																					],
																				}),
																			}),
																			(0, r.jsx)(g, {
																				value: "booker",
																				className: "space-y-4",
																				children: (0, r.jsxs)("form", {
																					onSubmit: P,
																					className: "space-y-4",
																					children: [
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "booker-email",
																									children: "Email",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "booker-email",
																									type: "email",
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "booker-password",
																									children: "Password",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "booker-password",
																									type: "password",
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsx)(l.$, {
																							type: "submit",
																							className: "w-full",
																							children: "Sign In",
																						}),
																					],
																				}),
																			}),
																		],
																	}),
																],
															}),
														],
													}),
													(0, r.jsxs)(j, {
														open: "signup" === e,
														onOpenChange: (e) => s(e ? "signup" : null),
														children: [
															(0, r.jsx)(b, {
																asChild: !0,
																children: (0, r.jsx)(l.$, {
																	className: "text-sm",
																	children: "Join",
																}),
															}),
															(0, r.jsxs)(w, {
																className: "sm:max-w-md",
																children: [
																	(0, r.jsx)(k, {
																		children: (0, r.jsx)(C, {
																			className: "text-center",
																			children: "Join DUA",
																		}),
																	}),
																	(0, r.jsxs)(h, {
																		value: a,
																		onValueChange: (e) => n(e),
																		children: [
																			(0, r.jsxs)(u, {
																				className: "grid w-full grid-cols-2",
																				children: [
																					(0, r.jsx)(p, {
																						value: "creative",
																						children: "Artist",
																					}),
																					(0, r.jsx)(p, {
																						value: "booker",
																						children: "Booker",
																					}),
																				],
																			}),
																			(0, r.jsx)(g, {
																				value: "creative",
																				className: "space-y-4",
																				children: (0, r.jsxs)("form", {
																					onSubmit: $,
																					className: "space-y-4",
																					children: [
																						(0, r.jsxs)("div", {
																							className:
																								"grid grid-cols-2 gap-4",
																							children: [
																								(0, r.jsxs)("div", {
																									className: "space-y-2",
																									children: [
																										(0, r.jsx)(m, {
																											htmlFor: "first-name",
																											children: "First Name",
																										}),
																										(0, r.jsx)(i.p, {
																											id: "first-name",
																											required: !0,
																										}),
																									],
																								}),
																								(0, r.jsxs)("div", {
																									className: "space-y-2",
																									children: [
																										(0, r.jsx)(m, {
																											htmlFor: "last-name",
																											children: "Last Name",
																										}),
																										(0, r.jsx)(i.p, {
																											id: "last-name",
																											required: !0,
																										}),
																									],
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "creative-email",
																									children: "Email",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "creative-email",
																									type: "email",
																									value: o,
																									onChange: (e) =>
																										c(e.target.value),
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "creative-password",
																									children: "Password",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "creative-password",
																									type: "password",
																									value: d,
																									onChange: (e) =>
																										x(e.target.value),
																									required: !0,
																								}),
																							],
																						}),
																						f &&
																							(0, r.jsx)("p", {
																								className:
																									"text-red-500 text-sm",
																								children: f,
																							}),
																						(0, r.jsx)(l.$, {
																							type: "submit",
																							className: "w-full",
																							children: "Create Account",
																						}),
																					],
																				}),
																			}),
																			(0, r.jsx)(g, {
																				value: "booker",
																				className: "space-y-4",
																				children: (0, r.jsxs)("form", {
																					onSubmit: $,
																					className: "space-y-4",
																					children: [
																						(0, r.jsxs)("div", {
																							className:
																								"grid grid-cols-2 gap-4",
																							children: [
																								(0, r.jsxs)("div", {
																									className: "space-y-2",
																									children: [
																										(0, r.jsx)(m, {
																											htmlFor:
																												"booker-first-name",
																											children: "First Name",
																										}),
																										(0, r.jsx)(i.p, {
																											id: "booker-first-name",
																											required: !0,
																										}),
																									],
																								}),
																								(0, r.jsxs)("div", {
																									className: "space-y-2",
																									children: [
																										(0, r.jsx)(m, {
																											htmlFor:
																												"booker-last-name",
																											children: "Last Name",
																										}),
																										(0, r.jsx)(i.p, {
																											id: "booker-last-name",
																											required: !0,
																										}),
																									],
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor: "company",
																									children: "Company",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "company",
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor:
																										"booker-signup-email",
																									children: "Email",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "booker-signup-email",
																									type: "email",
																									value: o,
																									onChange: (e) =>
																										c(e.target.value),
																									required: !0,
																								}),
																							],
																						}),
																						(0, r.jsxs)("div", {
																							className: "space-y-2",
																							children: [
																								(0, r.jsx)(m, {
																									htmlFor:
																										"booker-signup-password",
																									children: "Password",
																								}),
																								(0, r.jsx)(i.p, {
																									id: "booker-signup-password",
																									type: "password",
																									value: d,
																									onChange: (e) =>
																										x(e.target.value),
																									required: !0,
																								}),
																							],
																						}),
																						f &&
																							(0, r.jsx)("p", {
																								className:
																									"text-red-500 text-sm",
																								children: f,
																							}),
																						(0, r.jsx)(l.$, {
																							type: "submit",
																							className: "w-full",
																							children: "Create Account",
																						}),
																					],
																				}),
																			}),
																		],
																	}),
																],
															}),
														],
													}),
												],
											}),
										],
									}),
								],
							}),
						}),
						(0, r.jsxs)("main", {
							className: "max-w-7xl mx-auto px-6 py-12",
							children: [
								(0, r.jsxs)("div", {
									className: "grid grid-cols-1 lg:grid-cols-3 gap-8 h-[80vh]",
									children: [
										(0, r.jsxs)("div", {
											className:
												"lg:col-span-2 relative group cursor-pointer overflow-hidden",
											children: [
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-gradient-to-br from-slate-900 to-slate-700",
												}),
												(0, r.jsx)("div", {
													className: "absolute inset-0 bg-black/20",
												}),
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-105",
													style: {
														backgroundImage:
															"url('/placeholder.svg?height=600&width=800')",
													},
												}),
												(0, r.jsxs)("div", {
													className:
														"relative h-full flex flex-col justify-end p-12 text-white",
													children: [
														(0, r.jsxs)("h1", {
															className:
																"text-5xl lg:text-6xl font-light mb-4 leading-tight",
															children: [
																"New Standard for Creatives,",
																(0, r.jsx)("br", {}),
																"Connect",
																(0, r.jsx)("br", {}),
																"Create",
																(0, r.jsx)("br", {}),
																"Collaborate",
															],
														}),
														(0, r.jsx)("p", {
															className: "text-xl font-light opacity-90",
															children:
																"The platform for creative professionals",
														}),
														(0, r.jsx)("div", {
															className: "mt-6",
															children: (0, r.jsx)(R.$n, {
																children: "Get Started with DUA",
															}),
														}),
													],
												}),
											],
										}),
										(0, r.jsxs)("div", {
											className: "space-y-6",
											children: [
												(0, r.jsxs)("div", {
													className:
														"relative group cursor-pointer overflow-hidden h-64",
													children: [
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600",
														}),
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
															style: {
																backgroundImage:
																	"url('/placeholder.svg?height=300&width=400')",
															},
														}),
														(0, r.jsxs)("div", {
															className:
																"relative h-full flex flex-col justify-end p-8 text-white",
															children: [
																(0, r.jsxs)("div", {
																	className: "flex items-center mb-3",
																	children: [
																		(0, r.jsx)(S.A, {
																			className: "h-6 w-6 mr-3",
																		}),
																		(0, r.jsx)("span", {
																			className: "text-lg font-medium",
																			children: "For creatives",
																		}),
																	],
																}),
																(0, r.jsx)("p", {
																	className:
																		"text-sm opacity-90 leading-relaxed",
																	children:
																		"Showcase your portfolio and connect with opportunities",
																}),
															],
														}),
													],
												}),
												(0, r.jsxs)("div", {
													className:
														"relative group cursor-pointer overflow-hidden h-64",
													children: [
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-gradient-to-br from-emerald-600 to-teal-600",
														}),
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
															style: {
																backgroundImage:
																	"url('/placeholder.svg?height=300&width=400')",
															},
														}),
														(0, r.jsxs)("div", {
															className:
																"relative h-full flex flex-col justify-end p-8 text-white",
															children: [
																(0, r.jsxs)("div", {
																	className: "flex items-center mb-3",
																	children: [
																		(0, r.jsx)(A.A, {
																			className: "h-6 w-6 mr-3",
																		}),
																		(0, r.jsx)("span", {
																			className: "text-lg font-medium",
																			children: "For Bookers",
																		}),
																	],
																}),
																(0, r.jsx)("p", {
																	className:
																		"text-sm opacity-90 leading-relaxed",
																	children:
																		"Discover and hire exceptional creative talent",
																}),
															],
														}),
													],
												}),
												(0, r.jsxs)("div", {
													className:
														"relative group cursor-pointer overflow-hidden h-64",
													children: [
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-gradient-to-br from-amber-600 to-yellow-600",
														}),
														(0, r.jsx)("div", {
															className:
																"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
															style: {
																backgroundImage:
																	"url('/placeholder.svg?height=300&width=400')",
															},
														}),
														(0, r.jsxs)("div", {
															className:
																"relative h-full flex flex-col justify-end p-8 text-white",
															children: [
																(0, r.jsxs)("div", {
																	className: "flex items-center mb-3",
																	children: [
																		(0, r.jsx)(q.A, {
																			className: "h-6 w-6 mr-3",
																		}),
																		(0, r.jsx)("span", {
																			className: "text-lg font-medium",
																			children: "Payment Guaranteed",
																		}),
																	],
																}),
																(0, r.jsx)("p", {
																	className:
																		"text-sm opacity-90 leading-relaxed",
																	children:
																		"Secure payments with full protection for all transactions",
																}),
															],
														}),
													],
												}),
											],
										}),
									],
								}),
								(0, r.jsxs)("div", {
									className:
										"mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",
									children: [
										(0, r.jsxs)("div", {
											className:
												"relative group cursor-pointer overflow-hidden h-48",
											children: [
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500",
												}),
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
													style: {
														backgroundImage:
															"url('/placeholder.svg?height=200&width=300')",
													},
												}),
												(0, r.jsxs)("div", {
													className:
														"relative h-full flex flex-col justify-end p-6 text-white",
													children: [
														(0, r.jsx)("h3", {
															className: "text-lg font-medium mb-2",
															children: "Photography",
														}),
														(0, r.jsx)("p", {
															className: "text-sm opacity-90",
															children: "Professional photographers",
														}),
													],
												}),
											],
										}),
										(0, r.jsxs)("div", {
											className:
												"relative group cursor-pointer overflow-hidden h-48",
											children: [
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-gradient-to-br from-pink-500 to-rose-500",
												}),
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
													style: {
														backgroundImage:
															"url('/placeholder.svg?height=200&width=300')",
													},
												}),
												(0, r.jsxs)("div", {
													className:
														"relative h-full flex flex-col justify-end p-6 text-white",
													children: [
														(0, r.jsx)("h3", {
															className: "text-lg font-medium mb-2",
															children: "Design",
														}),
														(0, r.jsx)("p", {
															className: "text-sm opacity-90",
															children: "Creative visual designers",
														}),
													],
												}),
											],
										}),
										(0, r.jsxs)("div", {
											className:
												"relative group cursor-pointer overflow-hidden h-48",
											children: [
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-gradient-to-br from-indigo-500 to-blue-500",
												}),
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
													style: {
														backgroundImage:
															"url('/placeholder.svg?height=200&width=300')",
													},
												}),
												(0, r.jsxs)("div", {
													className:
														"relative h-full flex flex-col justify-end p-6 text-white",
													children: [
														(0, r.jsx)("h3", {
															className: "text-lg font-medium mb-2",
															children: "Video",
														}),
														(0, r.jsx)("p", {
															className: "text-sm opacity-90",
															children: "Video production experts",
														}),
													],
												}),
											],
										}),
										(0, r.jsxs)("div", {
											className:
												"relative group cursor-pointer overflow-hidden h-48",
											children: [
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-gradient-to-br from-violet-500 to-purple-500",
												}),
												(0, r.jsx)("div", {
													className:
														"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
													style: {
														backgroundImage:
															"url('/placeholder.svg?height=200&width=300')",
													},
												}),
												(0, r.jsxs)("div", {
													className:
														"relative h-full flex flex-col justify-end p-6 text-white",
													children: [
														(0, r.jsx)("h3", {
															className: "text-lg font-medium mb-2",
															children: "Content",
														}),
														(0, r.jsx)("p", {
															className: "text-sm opacity-90",
															children: "Content creators & writers",
														}),
													],
												}),
											],
										}),
									],
								}),
							],
						}),
						(0, r.jsx)("footer", {
							className: "border-t border-gray-100 mt-20",
							children: (0, r.jsxs)("div", {
								className: "max-w-7xl mx-auto px-6 py-12",
								children: [
									(0, r.jsxs)("div", {
										className: "grid grid-cols-1 md:grid-cols-4 gap-8",
										children: [
											(0, r.jsxs)("div", {
												children: [
													(0, r.jsx)("h3", {
														className: "font-medium mb-4",
														children: "Platform",
													}),
													(0, r.jsxs)("ul", {
														className: "space-y-2 text-sm text-gray-600",
														children: [
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "How it works",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Pricing",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Success stories",
																}),
															}),
														],
													}),
												],
											}),
											(0, r.jsxs)("div", {
												children: [
													(0, r.jsx)("h3", {
														className: "font-medium mb-4",
														children: "For creatives",
													}),
													(0, r.jsxs)("ul", {
														className: "space-y-2 text-sm text-gray-600",
														children: [
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Create portfolio",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Find work",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Resources",
																}),
															}),
														],
													}),
												],
											}),
											(0, r.jsxs)("div", {
												children: [
													(0, r.jsx)("h3", {
														className: "font-medium mb-4",
														children: "For Bookers",
													}),
													(0, r.jsxs)("ul", {
														className: "space-y-2 text-sm text-gray-600",
														children: [
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Find talent",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Post projects",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Hiring guide",
																}),
															}),
														],
													}),
												],
											}),
											(0, r.jsxs)("div", {
												children: [
													(0, r.jsx)("h3", {
														className: "font-medium mb-4",
														children: "Support",
													}),
													(0, r.jsxs)("ul", {
														className: "space-y-2 text-sm text-gray-600",
														children: [
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Help center",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Contact",
																}),
															}),
															(0, r.jsx)("li", {
																children: (0, r.jsx)(I(), {
																	href: "#",
																	className: "hover:text-gray-900",
																	children: "Privacy",
																}),
															}),
														],
													}),
												],
											}),
										],
									}),
									(0, r.jsx)("div", {
										className:
											"border-t border-gray-100 mt-8 pt-8 text-center text-sm text-gray-500",
										children: (0, r.jsx)("p", {
											children: "\xa9 2024 DUA. All rights reserved.",
										}),
									}),
								],
							}),
						}),
						"login" === e &&
							(0, r.jsx)("div", {
								className:
									"fixed inset-0 flex items-center justify-center bg-black/40 z-50",
								children: (0, r.jsxs)("form", {
									onSubmit: P,
									className:
										"bg-white p-8 rounded shadow-lg flex flex-col gap-4 min-w-[300px]",
									children: [
										(0, r.jsx)("h2", {
											className: "text-xl font-light mb-2",
											children: "Sign In",
										}),
										(0, r.jsxs)("div", {
											className: "flex gap-4 mb-2",
											children: [
												(0, r.jsxs)("label", {
													children: [
														(0, r.jsx)("input", {
															type: "radio",
															name: "userType",
															value: "creative",
															checked: "creative" === a,
															onChange: () => n("creative"),
														}),
														"Creative",
													],
												}),
												(0, r.jsxs)("label", {
													children: [
														(0, r.jsx)("input", {
															type: "radio",
															name: "userType",
															value: "booker",
															checked: "booker" === a,
															onChange: () => n("booker"),
														}),
														"Booker",
													],
												}),
											],
										}),
										(0, r.jsx)("input", {
											type: "text",
											placeholder: "Email",
											className: "border p-2 rounded",
											required: !0,
										}),
										(0, r.jsx)("input", {
											type: "password",
											placeholder: "Password",
											className: "border p-2 rounded",
											required: !0,
										}),
										(0, r.jsx)("button", {
											type: "submit",
											className: "bg-black text-white py-2 rounded font-light",
											children: "Sign In",
										}),
										(0, r.jsx)("button", {
											type: "button",
											className: "text-xs text-gray-500 mt-2",
											onClick: () => s(null),
											children: "Cancel",
										}),
									],
								}),
							}),
					],
				});
			}
		},
	},
	(e) => {
		var s = (s) => e((e.s = s));
		e.O(0, [113, 315, 85, 874, 637, 475, 566, 737, 497, 954, 358], () =>
			s(415),
		),
			(_N_E = e.O());
	},
]);
