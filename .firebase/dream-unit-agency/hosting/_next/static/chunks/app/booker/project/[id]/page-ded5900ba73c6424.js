(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[483],
	{
		7971: (e, t, r) => {
			Promise.resolve().then(r.bind(r, 8819));
		},
		8819: (e, t, r) => {
			r.r(t), r.d(t, { default: () => w });
			var s = r(8081),
				l = r(2149),
				a = r(5186),
				i = r(7950),
				d = r.n(i),
				n = r(9651),
				c = r(1018);
			const o = (0, c.A)("ArrowLeft", [
					["path", { d: "m12 19-7-7 7-7", key: "1l729n" }],
					["path", { d: "M19 12H5", key: "x3x0zl" }],
				]),
				x = (0, c.A)("CircleX", [
					["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
					["path", { d: "m15 9-6 6", key: "1uzhvr" }],
					["path", { d: "m9 9 6 6", key: "z0biqf" }],
				]);
			var g = r(6425),
				h = r(5160),
				u = r(900),
				v = r(7687);
			const p = l.forwardRef((e, t) => {
				const { className: r, ...l } = e;
				return (0, s.jsx)("textarea", {
					className: (0, v.cn)(
						"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						r,
					),
					ref: t,
					...l,
				});
			});
			p.displayName = "Textarea";
			var m = r(5904),
				f = r(3771),
				j = r(7753),
				b = r(5993);
			const N = (e) => {
					if (!e) return "bg-gray-100 text-gray-800";
					switch (e.toLowerCase()) {
						case "active":
						case "confirmed":
						case "completed":
						case "accepted":
							return "bg-green-100 text-green-800";
						case "pending":
							return "bg-yellow-100 text-yellow-800";
						case "needed":
							return "bg-orange-100 text-orange-800";
						case "declined":
						case "cancelled":
							return "bg-red-100 text-red-800";
						case "draft":
						case "planning":
							return "bg-blue-100 text-blue-800";
						default:
							return "bg-gray-100 text-gray-800";
					}
				},
				y = [
					"Photography",
					"Videography",
					"Event Coverage",
					"Social Media Content",
					"Brand Identity",
					"Music Video Production",
					"Other",
				],
				w = (e) => {
					var t, r;
					const { params: i } = e,
						c = (0, n.useRouter)(),
						v = i.id,
						[w, k] = (0, l.useState)(() => f.R4.find((e) => e.id === v)),
						[C, O] = (0, l.useState)(!1),
						[S, B] = (0, l.useState)({}),
						[D, T] = (0, l.useState)(""),
						E = (0, l.useCallback)((e, t, r) => {
							const s = r.filter(
								(r) =>
									r.projectTitle === t &&
									r.projectType
										.toLowerCase()
										.includes(e.toLowerCase().split(" ")[0]),
							);
							return s.length > 0
								? "$"
										.concat(
											Math.min(...s.map((e) => e.amount)).toLocaleString(),
											" - $",
										)
										.concat(
											Math.max(...s.map((e) => e.amount)).toLocaleString(),
										)
								: "-";
						}, []),
						L = (0, l.useCallback)((e, t, r) => {
							const s = r.filter(
									(r) =>
										r.projectTitle === t &&
										r.projectType
											.toLowerCase()
											.includes(e.toLowerCase().split(" ")[0]),
								),
								l = s.filter((e) => "accepted" === e.status),
								a = s.filter((e) => "pending" === e.status),
								i = s.filter((e) => "declined" === e.status);
							return l.length > 0
								? "Confirmed"
								: a.length > 0
									? "Pending"
									: i.length > 0 && s.length === i.length
										? "Declined"
										: "Needed";
						}, []);
					(0, l.useEffect)(() => {
						const e = f.R4.find((e) => e.id === v);
						k(e),
							e
								? B({
										...e,
										budget: e.budget.toString(),
										creativesNeeded: [...(e.creativesNeeded || [])],
										roleBudgetOverrides: { ...(e.roleBudgetOverrides || {}) },
										roleStatusOverrides: { ...(e.roleStatusOverrides || {}) },
									})
								: O(!1);
					}, [v]);
					const R = (e, t) => {
							B((r) => ({ ...r, [e]: t }));
						},
						A = (e, t) => {
							("" === t || (!isNaN(Number(t)) && Number(t) >= 0)) &&
								B((r) => ({ ...r, [e]: t }));
						},
						P = (e) => {
							const t = (S.creativesNeeded || [])[e];
							B((r) => {
								const s = { ...(r.roleBudgetOverrides || {}) };
								delete s[t];
								const l = { ...(r.roleStatusOverrides || {}) };
								return (
									delete l[t],
									{
										...r,
										creativesNeeded: (r.creativesNeeded || []).filter(
											(t, r) => r !== e,
										),
										roleBudgetOverrides: s,
										roleStatusOverrides: l,
									}
								);
							});
						},
						$ = (e, t) => {
							B((r) => ({
								...r,
								roleBudgetOverrides: {
									...(r.roleBudgetOverrides || {}),
									[e]: t,
								},
							}));
						},
						z = (e, t) => {
							B((r) => ({
								...r,
								roleStatusOverrides: {
									...(r.roleStatusOverrides || {}),
									[e]: t,
								},
							}));
						};
					return w
						? (C ? S.budget : w.budget.toString(),
							(0, s.jsxs)("div", {
								className: "min-h-screen bg-white",
								children: [
									(0, s.jsx)(j.A, {}),
									(0, s.jsx)(b.A, {
										children: (0, s.jsxs)("div", {
											className: "py-8",
											children: [
												(0, s.jsxs)("div", {
													className: "flex justify-between items-center mb-6",
													children: [
														(0, s.jsxs)(h.$, {
															onClick: () => c.back(),
															variant: "outline",
															className: "font-light tracking-wide",
															children: [
																(0, s.jsx)(o, { className: "w-4 h-4 mr-2" }),
																" Back to Profile",
															],
														}),
														C
															? (0, s.jsxs)("div", {
																	className: "flex gap-3",
																	children: [
																		(0, s.jsx)(h.$, {
																			onClick: () => {
																				O(!1),
																					w &&
																						B({
																							...w,
																							budget: w.budget.toString(),
																							creativesNeeded: [
																								...(w.creativesNeeded || []),
																							],
																							roleBudgetOverrides: {
																								...(w.roleBudgetOverrides ||
																									{}),
																							},
																							roleStatusOverrides: {
																								...(w.roleStatusOverrides ||
																									{}),
																							},
																						});
																			},
																			variant: "outline",
																			className: "font-light tracking-wide",
																			children: "Cancel",
																		}),
																		(0, s.jsx)(h.$, {
																			onClick: () => {
																				if (!w || !S) return;
																				const e = {
																					...w,
																					title: S.title || w.title,
																					client: S.client || w.client,
																					budget: S.budget
																						? Number.parseFloat(S.budget)
																						: w.budget,
																					projectType:
																						S.projectType || w.projectType,
																					startDate: S.startDate || w.startDate,
																					endDate: S.endDate || w.endDate,
																					location: S.location || w.location,
																					description:
																						S.description || w.description,
																					creativesNeeded:
																						S.creativesNeeded ||
																						w.creativesNeeded,
																					roleBudgetOverrides:
																						S.roleBudgetOverrides ||
																						w.roleBudgetOverrides,
																					roleStatusOverrides:
																						S.roleStatusOverrides ||
																						w.roleStatusOverrides,
																				};
																				k(e);
																				const t = f.R4.findIndex(
																					(e) => e.id === v,
																				);
																				-1 !== t && (f.R4[t] = e), O(!1);
																			},
																			variant: "default",
																			className:
																				"font-light tracking-wide bg-green-600 hover:bg-green-700 text-white",
																			children: "Save Changes",
																		}),
																	],
																})
															: (0, s.jsx)(h.$, {
																	onClick: () => {
																		if (!C && w) {
																			const e = w.creativesNeeded || [],
																				t = {},
																				r = {};
																			e.forEach((e) => {
																				var s, l, a, i;
																				(t[e] =
																					null !==
																						(a =
																							null ===
																								(s = w.roleBudgetOverrides) ||
																							void 0 === s
																								? void 0
																								: s[e]) && void 0 !== a
																						? a
																						: E(e, w.title, f.Ex)),
																					(r[e] =
																						null !==
																							(i =
																								null ===
																									(l = w.roleStatusOverrides) ||
																								void 0 === l
																									? void 0
																									: l[e]) && void 0 !== i
																							? i
																							: L(e, w.title, f.Ex));
																			}),
																				B({
																					...w,
																					budget: w.budget.toString(),
																					creativesNeeded: [...e],
																					roleBudgetOverrides: t,
																					roleStatusOverrides: r,
																				});
																		} else
																			C &&
																				w &&
																				B({
																					...w,
																					budget: w.budget.toString(),
																					creativesNeeded: [
																						...(w.creativesNeeded || []),
																					],
																					roleBudgetOverrides: {
																						...(w.roleBudgetOverrides || {}),
																					},
																					roleStatusOverrides: {
																						...(w.roleStatusOverrides || {}),
																					},
																				});
																		O(!C);
																	},
																	variant: "default",
																	className:
																		"font-light tracking-wide bg-black text-white hover:bg-gray-800",
																	children: "Edit Project",
																}),
													],
												}),
												(0, s.jsxs)("div", {
													className:
														"bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row",
													children: [
														(0, s.jsx)("div", {
															className:
																"w-full md:w-1/3 relative min-h-[300px] md:min-h-0 bg-gray-100",
															children: (0, s.jsx)(a.default, {
																src: w.src || "/placeholder.svg",
																alt: w.alt || w.title,
																fill: !0,
																className: "object-cover",
															}),
														}),
														(0, s.jsxs)("div", {
															className: "flex-1 flex flex-col",
															children: [
																(0, s.jsx)("div", {
																	className:
																		"flex items-center justify-between p-6 border-b border-gray-200",
																	children: C
																		? (0, s.jsx)(u.p, {
																				value: S.title || "",
																				onChange: (e) =>
																					R("title", e.target.value),
																				placeholder: "Project Title",
																				className:
																					"text-2xl font-light tracking-wide text-gray-800 border-b-2 border-gray-300 focus:border-blue-500 py-1",
																			})
																		: (0, s.jsx)("h1", {
																				className:
																					"text-2xl font-light tracking-wide text-gray-800",
																				children: w.title,
																			}),
																}),
																(0, s.jsxs)("div", {
																	className:
																		"flex-1 p-6 overflow-y-auto space-y-5",
																	children: [
																		(0, s.jsx)("div", {
																			children: (0, s.jsx)("span", {
																				className:
																					"px-3 py-1 text-xs font-light tracking-wide rounded ".concat(
																						"active" === w.status
																							? "bg-blue-100 text-blue-800"
																							: "completed" === w.status
																								? "bg-green-100 text-green-800"
																								: "bg-yellow-100 text-yellow-800",
																						"\n                  ",
																					),
																				children:
																					w.status.charAt(0).toUpperCase() +
																					w.status.slice(1),
																			}),
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Client",
																				}),
																				C
																					? (0, s.jsx)(u.p, {
																							value: S.client || "",
																							onChange: (e) =>
																								R("client", e.target.value),
																							placeholder: "Client Name",
																							className: "font-light",
																						})
																					: (0, s.jsx)("p", {
																							className:
																								"font-light text-gray-700",
																							children: w.client,
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Budget (USD)",
																				}),
																				C
																					? (0, s.jsx)(u.p, {
																							type: "text",
																							value: S.budget || "",
																							onChange: (e) =>
																								A("budget", e.target.value),
																							placeholder: "15000",
																							className: "font-light",
																						})
																					: (0, s.jsxs)("p", {
																							className:
																								"font-light text-gray-700",
																							children: [
																								"$",
																								w.budget.toLocaleString(),
																							],
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Project Type",
																				}),
																				C
																					? (0, s.jsxs)(m.l6, {
																							value: S.projectType || "",
																							onValueChange: (e) =>
																								R("projectType", e),
																							children: [
																								(0, s.jsx)(m.bq, {
																									className: "font-light",
																									children: (0, s.jsx)(m.yv, {
																										placeholder: "Select Type",
																									}),
																								}),
																								(0, s.jsx)(m.gC, {
																									children: y.map((e) =>
																										(0, s.jsx)(
																											m.eb,
																											{ value: e, children: e },
																											e,
																										),
																									),
																								}),
																							],
																						})
																					: (0, s.jsx)("p", {
																							className:
																								"font-light text-gray-700",
																							children: w.projectType,
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Timeline",
																				}),
																				C
																					? (0, s.jsxs)("div", {
																							className:
																								"flex gap-4 items-center",
																							children: [
																								(0, s.jsx)(u.p, {
																									type: "date",
																									value: S.startDate || "",
																									onChange: (e) =>
																										R(
																											"startDate",
																											e.target.value,
																										),
																									className: "font-light",
																								}),
																								(0, s.jsx)("span", {
																									children: "to",
																								}),
																								(0, s.jsx)(u.p, {
																									type: "date",
																									value: S.endDate || "",
																									onChange: (e) =>
																										R(
																											"endDate",
																											e.target.value,
																										),
																									className: "font-light",
																								}),
																							],
																						})
																					: (0, s.jsxs)("p", {
																							className:
																								"font-light text-gray-700",
																							children: [
																								w.startDate,
																								w.endDate &&
																								w.endDate !== w.startDate
																									? " to ".concat(w.endDate)
																									: "",
																							],
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Location",
																				}),
																				C
																					? (0, s.jsx)(u.p, {
																							value: S.location || "",
																							onChange: (e) =>
																								R("location", e.target.value),
																							placeholder:
																								"e.g., Downtown Studio, Los Angeles",
																							className: "font-light",
																						})
																					: (0, s.jsx)("p", {
																							className:
																								"font-light text-gray-700",
																							children: w.location,
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-sm font-light text-gray-500 tracking-wide mb-1",
																					children: "Description",
																				}),
																				C
																					? (0, s.jsx)(p, {
																							value: S.description || "",
																							onChange: (e) =>
																								R(
																									"description",
																									e.target.value,
																								),
																							placeholder:
																								"Project description...",
																							className:
																								"font-light min-h-[100px]",
																						})
																					: (0, s.jsx)("p", {
																							className:
																								"text-gray-700 font-light whitespace-pre-wrap",
																							children: w.description,
																						}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className:
																				"border-t border-gray-200 pt-5 mt-5",
																			children: [
																				(0, s.jsx)("h3", {
																					className:
																						"text-base font-light text-gray-600 tracking-wide mb-4",
																					children: "Project Staffing Status",
																				}),
																				C &&
																					(0, s.jsxs)("div", {
																						className:
																							"mb-4 p-3 border rounded-md bg-gray-50",
																						children: [
																							(0, s.jsx)("h4", {
																								className:
																									"text-sm font-medium text-gray-700 mb-2",
																								children: "Edit Roles:",
																							}),
																							(S.creativesNeeded || []).map(
																								(e, t) =>
																									(0, s.jsxs)(
																										"div",
																										{
																											className:
																												"flex items-center justify-between mb-2 p-2 border rounded bg-white",
																											children: [
																												(0, s.jsx)("span", {
																													className:
																														"text-sm text-gray-800",
																													children: e,
																												}),
																												(0, s.jsxs)(h.$, {
																													variant: "ghost",
																													size: "sm",
																													onClick: () => P(t),
																													className:
																														"text-red-500 hover:text-red-700",
																													children: [
																														(0, s.jsx)(x, {
																															className:
																																"w-4 h-4 mr-1",
																														}),
																														" Remove",
																													],
																												}),
																											],
																										},
																										t,
																									),
																							),
																							(0, s.jsxs)("div", {
																								className:
																									"flex items-center gap-2 mt-3",
																								children: [
																									(0, s.jsx)(u.p, {
																										value: D,
																										onChange: (e) =>
																											T(e.target.value),
																										placeholder:
																											"New Role Name",
																										className: "flex-grow",
																									}),
																									(0, s.jsxs)(h.$, {
																										onClick: () => {
																											if ("" !== D.trim()) {
																												const e = D.trim();
																												B((t) => ({
																													...t,
																													creativesNeeded: [
																														...(t.creativesNeeded ||
																															[]),
																														e,
																													],
																													roleBudgetOverrides: {
																														...(t.roleBudgetOverrides ||
																															{}),
																														[e]: "-",
																													},
																													roleStatusOverrides: {
																														...(t.roleStatusOverrides ||
																															{}),
																														[e]: "Needed",
																													},
																												})),
																													T("");
																											}
																										},
																										variant: "outline",
																										size: "sm",
																										children: [
																											(0, s.jsx)(g.A, {
																												className:
																													"w-4 h-4 mr-1",
																											}),
																											" Add Role",
																										],
																									}),
																								],
																							}),
																						],
																					}),
																				(0, s.jsx)("div", {
																					className: "overflow-x-auto",
																					children: (0, s.jsxs)("table", {
																						className: "w-full text-sm",
																						children: [
																							(0, s.jsx)("thead", {
																								className:
																									"border-b border-gray-200",
																								children: (0, s.jsxs)("tr", {
																									children: [
																										(0, s.jsx)("th", {
																											className:
																												"text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																											children: "Role",
																										}),
																										(0, s.jsx)("th", {
																											className:
																												"text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																											children: "Offers Sent",
																										}),
																										(0, s.jsx)("th", {
																											className:
																												"text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																											children: "Budget Range",
																										}),
																										(0, s.jsx)("th", {
																											className:
																												"text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																											children: "Status",
																										}),
																									],
																								}),
																							}),
																							(0, s.jsxs)("tbody", {
																								className:
																									"divide-y divide-gray-100",
																								children: [
																									(C
																										? S.creativesNeeded || []
																										: w.creativesNeeded || []
																									).map((e, t) => {
																										var r, l, a, i, n, c, o, x;
																										let g, v;
																										C
																											? ((g =
																													(null ===
																														(a =
																															S.roleBudgetOverrides) ||
																													void 0 === a
																														? void 0
																														: a[e]) || "-"),
																												null ===
																													(i =
																														S.roleStatusOverrides) ||
																													void 0 === i ||
																													i[e])
																											: ((g =
																													null !==
																														(o =
																															null ===
																																(n =
																																	w.roleBudgetOverrides) ||
																															void 0 === n
																																? void 0
																																: n[e]) &&
																													void 0 !== o
																														? o
																														: E(
																																e,
																																w.title,
																																f.Ex,
																															)),
																												(null !==
																													(x =
																														null ===
																															(c =
																																w.roleStatusOverrides) ||
																														void 0 === c
																															? void 0
																															: c[e]) &&
																													void 0 !== x) ||
																													L(e, w.title, f.Ex));
																										const p = f.Ex.filter(
																											(t) =>
																												t.projectTitle ===
																													w.title &&
																												t.projectType
																													.toLowerCase()
																													.includes(
																														e
																															.toLowerCase()
																															.split(" ")[0],
																													),
																										);
																										return (0, s.jsxs)(
																											"tr",
																											{
																												className:
																													"hover:bg-gray-50 transition-colors",
																												children: [
																													(0, s.jsx)("td", {
																														className:
																															"py-3 px-2 font-light text-gray-900 whitespace-nowrap",
																														children: e,
																													}),
																													(0, s.jsx)("td", {
																														className:
																															"py-3 px-2",
																														children: (0,
																														s.jsx)("div", {
																															className:
																																"flex flex-wrap gap-1",
																															children:
																																p.length > 0
																																	? p.map(
																																			(e, t) =>
																																				(0,
																																				s.jsxs)(
																																					d(),
																																					{
																																						href: "/creative/".concat(
																																							e.artistId,
																																						),
																																						className:
																																							"flex items-center gap-1 bg-gray-100 hover:bg-gray-200 rounded-full px-2 py-1 border border-gray-200 transition-colors cursor-pointer",
																																						children:
																																							[
																																								(0,
																																								s.jsx)(
																																									"div",
																																									{
																																										className:
																																											"w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden",
																																										children:
																																											(0,
																																											s.jsx)(
																																												"span",
																																												{
																																													className:
																																														"text-[10px] font-light text-gray-600",
																																													children:
																																														e.artistName
																																															.split(
																																																" ",
																																															)
																																															.map(
																																																(
																																																	e,
																																																) =>
																																																	e[0],
																																															)
																																															.join(
																																																"",
																																															),
																																												},
																																											),
																																									},
																																								),
																																								(0,
																																								s.jsx)(
																																									"span",
																																									{
																																										className:
																																											"text-xs text-gray-700 mr-1 font-light",
																																										children:
																																											e.artistName.split(
																																												" ",
																																											)[0],
																																									},
																																								),
																																								(0,
																																								s.jsx)(
																																									"span",
																																									{
																																										className:
																																											"px-1.5 py-0.5 text-[10px] font-light rounded-full ".concat(
																																												"accepted" ===
																																													e.status
																																													? "bg-green-500 text-white"
																																													: "declined" ===
																																															e.status
																																														? "bg-red-500 text-white"
																																														: "expired" ===
																																																e.status
																																															? "bg-gray-500 text-white"
																																															: "bg-yellow-500 text-white",
																																												"\n                                            ",
																																											),
																																										children:
																																											"accepted" ===
																																											e.status
																																												? "✓"
																																												: "declined" ===
																																														e.status
																																													? "✗"
																																													: "expired" ===
																																															e.status
																																														? "⏰"
																																														: "⏳",
																																									},
																																								),
																																							],
																																					},
																																					t,
																																				),
																																		)
																																	: (0, s.jsxs)(
																																			d(),
																																			{
																																				href: "/?role="
																																					.concat(
																																						encodeURIComponent(
																																							e,
																																						),
																																						"&project=",
																																					)
																																					.concat(
																																						encodeURIComponent(
																																							w.title,
																																						),
																																						"&location=",
																																					)
																																					.concat(
																																						encodeURIComponent(
																																							w.location ||
																																								"",
																																						),
																																						"&budget=",
																																					)
																																					.concat(
																																						w.budget,
																																						"&startDate=",
																																					)
																																					.concat(
																																						w.startDate ||
																																							"",
																																						"&endDate=",
																																					)
																																					.concat(
																																						w.endDate ||
																																							"",
																																					),
																																				className:
																																					"inline-flex items-center gap-1 bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 rounded px-3 py-1 border border-blue-200 transition-colors text-xs font-light tracking-wide",
																																				children:
																																					[
																																						(0,
																																						s.jsx)(
																																							"svg",
																																							{
																																								className:
																																									"w-3 h-3",
																																								fill: "none",
																																								stroke:
																																									"currentColor",
																																								viewBox:
																																									"0 0 24 24",
																																								children:
																																									(0,
																																									s.jsx)(
																																										"path",
																																										{
																																											strokeLinecap:
																																												"round",
																																											strokeLinejoin:
																																												"round",
																																											strokeWidth: 2,
																																											d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
																																										},
																																									),
																																							},
																																						),
																																						"Search ",
																																						e.toUpperCase(),
																																					],
																																			},
																																		),
																														}),
																													}),
																													(0, s.jsx)("td", {
																														className:
																															"py-3 px-2 text-right font-light text-gray-900 whitespace-nowrap",
																														children: C
																															? (0, s.jsx)(
																																	u.p,
																																	{
																																		value:
																																			(null ===
																																				(r =
																																					S.roleBudgetOverrides) ||
																																			void 0 ===
																																				r
																																				? void 0
																																				: r[
																																						e
																																					]) ||
																																			"",
																																		onChange: (
																																			t,
																																		) =>
																																			$(
																																				e,
																																				t.target
																																					.value,
																																			),
																																		placeholder:
																																			"e.g., $1k-$1.5k or -",
																																		className:
																																			"font-light text-sm py-1 px-2 min-w-[120px] text-right",
																																	},
																																)
																															: g,
																													}),
																													(0, s.jsx)("td", {
																														className:
																															"px-6 py-4 whitespace-nowrap text-sm",
																														children: C
																															? (0, s.jsxs)(
																																	m.l6,
																																	{
																																		value:
																																			(null ===
																																				(l =
																																					S.roleStatusOverrides) ||
																																			void 0 ===
																																				l
																																				? void 0
																																				: l[
																																						e
																																					]) ||
																																			"Needed",
																																		onValueChange:
																																			(t) =>
																																				z(e, t),
																																		children: [
																																			(0,
																																			s.jsx)(
																																				m.bq,
																																				{
																																					className:
																																						"w-full text-xs",
																																					children:
																																						(0,
																																						s.jsx)(
																																							m.yv,
																																							{},
																																						),
																																				},
																																			),
																																			(0,
																																			s.jsxs)(
																																				m.gC,
																																				{
																																					children:
																																						[
																																							(0,
																																							s.jsx)(
																																								m.eb,
																																								{
																																									value:
																																										"Needed",
																																									children:
																																										"Needed",
																																								},
																																							),
																																							(0,
																																							s.jsx)(
																																								m.eb,
																																								{
																																									value:
																																										"Pending",
																																									children:
																																										"Pending",
																																								},
																																							),
																																							(0,
																																							s.jsx)(
																																								m.eb,
																																								{
																																									value:
																																										"Confirmed",
																																									children:
																																										"Confirmed",
																																								},
																																							),
																																							(0,
																																							s.jsx)(
																																								m.eb,
																																								{
																																									value:
																																										"Declined",
																																									children:
																																										"Declined",
																																								},
																																							),
																																						],
																																				},
																																			),
																																		],
																																	},
																																)
																															: (() => {
																																	var t;
																																	const r =
																																		(null ===
																																			(t =
																																				w.roleStatusOverrides) ||
																																		void 0 === t
																																			? void 0
																																			: t[e]) ||
																																		L(
																																			e,
																																			w.title,
																																			f.Ex,
																																		);
																																	return "Needed" ===
																																		r
																																		? (0,
																																			s.jsx)(
																																				h.$,
																																				{
																																					variant:
																																						"outline",
																																					size: "sm",
																																					onClick:
																																						() =>
																																							console.log(
																																								"Apply button clicked for ".concat(
																																									e,
																																								),
																																							),
																																					children:
																																						"Apply",
																																				},
																																			)
																																		: (0,
																																			s.jsx)(
																																				"span",
																																				{
																																					className:
																																						"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(
																																							N(
																																								r,
																																							),
																																						),
																																					children:
																																						r,
																																				},
																																			);
																																})(),
																													}),
																												],
																											},
																											e + t,
																										);
																									}),
																									0 ===
																										(C
																											? S.creativesNeeded || []
																											: w.creativesNeeded || []
																										).length &&
																										(0, s.jsx)("tr", {
																											children: (0, s.jsxs)(
																												"td",
																												{
																													colSpan: 4,
																													className:
																														"py-6 text-center text-gray-500 font-light",
																													children: [
																														"No creative requirements specified. ",
																														C &&
																															"Add roles using the form above.",
																													],
																												},
																											),
																										}),
																								],
																							}),
																						],
																					}),
																				}),
																				(0, s.jsxs)("div", {
																					className:
																						"mt-4 flex justify-between text-xs text-gray-500 font-light",
																					children: [
																						(0, s.jsxs)("span", {
																							children: [
																								C
																									? (null ===
																											(t = S.creativesNeeded) ||
																										void 0 === t
																											? void 0
																											: t.length) || 0
																									: (null ===
																											(r = w.creativesNeeded) ||
																										void 0 === r
																											? void 0
																											: r.length) || 0,
																								" roles needed",
																							],
																						}),
																						(0, s.jsxs)("span", {
																							children: [
																								f.Ex.filter(
																									(e) =>
																										e.projectTitle === w.title,
																								).length,
																								" offers sent",
																							],
																						}),
																					],
																				}),
																			],
																		}),
																	],
																}),
															],
														}),
													],
												}),
											],
										}),
									}),
								],
							}))
						: (0, s.jsxs)(s.Fragment, {
								children: [
									(0, s.jsx)(j.A, {}),
									(0, s.jsx)(b.A, {
										children: (0, s.jsxs)("div", {
											className: "text-center py-10",
											children: [
												(0, s.jsx)("h1", {
													className: "text-2xl font-light mb-4",
													children: "Project Not Found",
												}),
												(0, s.jsxs)(h.$, {
													onClick: () => c.back(),
													variant: "outline",
													children: [
														(0, s.jsx)(o, { className: "w-4 h-4 mr-2" }),
														" Go Back",
													],
												}),
											],
										}),
									}),
								],
							});
				};
		},
	},
	(e) => {
		var t = (t) => e((e.s = t));
		e.O(0, [874, 637, 186, 398, 285, 497, 954, 358], () => t(7971)),
			(_N_E = e.O());
	},
]);
