(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[665],
	{
		245: (e, t, a) => {
			a.d(t, {
				KT: () => r,
				R4: () => n,
				TZ: () => o,
				Tg: () => l,
				Wm: () => c,
				qD: () => i,
				xk: () => s,
			});
			const s = {
					1: {
						name: "ALEX JOHNSON",
						type: "PHOTOGRAPHER",
						location: "New York, NY",
						followers: 1234,
						following: 567,
						bio: "Professional photographer specializing in portraits and fashion",
					},
					2: {
						name: "SARAH WILSON",
						type: "MODEL",
						location: "Los Angeles, CA",
						followers: 2456,
						following: 432,
						bio: "Fashion and commercial model",
					},
					3: {
						name: "MICHAEL CHEN",
						type: "VIDEOGRAPHER",
						location: "Chicago, IL",
						followers: 987,
						following: 234,
						bio: "Documentary and commercial videographer",
					},
					4: {
						name: "<PERSON><PERSON><PERSON> DAVIS",
						type: "MAKEUP ARTIST",
						location: "Miami, FL",
						followers: 1567,
						following: 345,
						bio: "Editorial and bridal makeup creative",
					},
					5: {
						name: "DAVID RODRIGUEZ",
						type: "PHOTOGRAPHER",
						location: "Austin, TX",
						followers: 2134,
						following: 678,
						bio: "Product and architecture photographer",
					},
				},
				i = [
					{
						id: "1",
						src: "/assets/creatives_portfolio/crt1.png",
						alt: "Sunset beach",
						likes: 324,
						comments: 18,
						position: 0,
					},
					{
						id: "2",
						src: "/assets/creatives_portfolio/crt2.png",
						alt: "Pasta dinner",
						likes: 189,
						comments: 12,
						position: 1,
					},
					{
						id: "3",
						src: "/assets/creatives_portfolio/crt3.png",
						alt: "Mountain view",
						likes: 456,
						comments: 23,
						position: 2,
					},
					{
						id: "4",
						src: "/assets/creatives_portfolio/crt4.png",
						alt: "Morning coffee",
						likes: 203,
						comments: 15,
						position: 3,
					},
					{
						id: "5",
						src: "/assets/creatives_portfolio/crt5.png",
						alt: "City lights",
						likes: 378,
						comments: 26,
						position: 4,
					},
					{
						id: "6",
						src: "/assets/creatives_portfolio/crt6.png",
						alt: "Golden retriever",
						likes: 567,
						comments: 41,
						position: 5,
					},
					{
						id: "7",
						src: "/assets/creatives_portfolio/crt8.png",
						alt: "Sushi dinner",
						likes: 234,
						comments: 19,
						position: 6,
					},
					{
						id: "8",
						src: "/assets/creatives_portfolio/crt7.png",
						alt: "Road trip",
						likes: 292,
						comments: 17,
						position: 7,
					},
					{
						id: "9",
						src: "/placeholder.svg?height=400&width=400&text=Colorful flower garden in spring",
						alt: "Spring flowers",
						likes: 445,
						comments: 31,
						position: 8,
					},
				],
				l = [
					{
						id: "job1",
						title: "Wedding Photography",
						client: "Sarah & Mike Johnson",
						date: "2024-01-15",
						time: "2:00 PM",
						location: "Central Park, NYC",
						status: "confirmed",
						type: "photography",
					},
					{
						id: "job2",
						title: "Corporate Headshots",
						client: "Tech Solutions Inc.",
						date: "2024-01-18",
						time: "10:00 AM",
						location: "Downtown Office",
						status: "pending",
						type: "photography",
					},
					{
						id: "job3",
						title: "Product Photography",
						client: "Fashion Brand Co.",
						date: "2024-01-22",
						time: "1:00 PM",
						location: "Studio A",
						status: "confirmed",
						type: "photography",
					},
					{
						id: "job4",
						title: "Event Coverage",
						client: "Marketing Agency",
						date: "2024-01-25",
						time: "6:00 PM",
						location: "Convention Center",
						status: "confirmed",
						type: "photography",
					},
				],
				r = [
					{
						id: "payment1",
						jobId: "job5",
						jobTitle: "Wedding Photography",
						client: "Jennifer & David Smith",
						amount: 2500,
						date: "2023-12-20",
						status: "paid",
						invoiceNumber: "INV-2023-001",
					},
					{
						id: "payment2",
						jobId: "job6",
						jobTitle: "Corporate Event",
						client: "Global Tech Inc.",
						amount: 1800,
						date: "2023-12-05",
						status: "paid",
						invoiceNumber: "INV-2023-002",
					},
					{
						id: "payment3",
						jobId: "job7",
						jobTitle: "Product Shoot",
						client: "Luxury Brands Co.",
						amount: 1200,
						date: "2023-11-28",
						status: "paid",
						invoiceNumber: "INV-2023-003",
					},
				],
				n = [
					{
						id: "project1",
						title: "Summer Lookbook",
						client: "Zara",
						projectType: "fashion",
						startDate: "2024-07-01",
						endDate: "2024-07-15",
						location: "Miami Beach",
						budget: 2e4,
						description: "Create a summer lookbook for Zara's new collection.",
						creativesNeeded: [
							"Photographer",
							"Model",
							"Makeup Artist",
							"Stylist",
						],
					},
					{
						id: "project2",
						title: "Corporate Headshots",
						client: "Google",
						projectType: "corporate",
						startDate: "2024-08-01",
						endDate: "2024-08-05",
						location: "Mountain View",
						budget: 1e4,
						description: "Take corporate headshots for Google's employees.",
						creativesNeeded: ["Photographer"],
					},
					{
						id: "project3",
						title: "Wedding Photography",
						client: "John and Jane Doe",
						projectType: "wedding",
						startDate: "2024-09-01",
						endDate: "2024-09-01",
						location: "Central Park",
						budget: 5e3,
						description: "Photograph John and Jane Doe's wedding.",
						creativesNeeded: ["Photographer"],
					},
				],
				o = [
					{
						id: "offer_rec1",
						projectTitle: "Summer Glow Campaign",
						clientName: "Sunlight Beauty Co.",
						role: "Lead Photographer",
						amount: 2200,
						offerDate: "2024-03-10",
						status: "pending",
						projectType: "Beauty Product Shoot",
						location: "Miami Beach, FL",
						description:
							"Looking for a photographer with a bright and airy style for our new sunscreen line.",
						isSentByMe: !1,
					},
					{
						id: "offer_rec2",
						projectTitle: "Urban Explorers Video",
						clientName: "City Adventures Magazine",
						role: "Videographer",
						amount: 1800,
						offerDate: "2024-03-05",
						status: "accepted",
						projectType: "Travel Documentary Short",
						location: "Various, NYC",
						description:
							"Short documentary piece following urban explorers. Drone skills a plus.",
						isSentByMe: !1,
					},
					{
						id: "offer_rec3",
						projectTitle: "Tech Conference Live Model",
						clientName: "Innovate Corp",
						role: "Promotional Model",
						amount: 750,
						offerDate: "2024-02-28",
						status: "declined",
						projectType: "Tech Event",
						location: "San Francisco, CA",
						description:
							"Need engaging models for our booth at the upcoming Innovate Summit.",
						isSentByMe: !1,
					},
				],
				c = [
					{
						id: "offer_sent1",
						projectTitle: "Indie Band Music Video",
						clientName: "The Wandering Souls (Band)",
						role: "Director of Photography",
						amount: 1500,
						offerDate: "2024-03-12",
						status: "pending",
						projectType: "Music Video",
						location: "Austin, TX",
						description:
							"Proposal to shoot and direct the photography for your upcoming music video.",
						isSentByMe: !0,
					},
					{
						id: "offer_sent2",
						projectTitle: "Artisan Bakery Branding",
						clientName: "The Sweet Spot Bakery",
						role: "Food Photographer",
						amount: 900,
						offerDate: "2024-03-02",
						status: "expired",
						projectType: "Branding & Lifestyle",
						location: "Portland, OR",
						description:
							"Offered to create a series of lifestyle and product shots for their new website.",
						isSentByMe: !0,
					},
					{
						id: "offer_sent3",
						projectTitle: "Local Cafe Social Media Content",
						clientName: "Corner Brew Cafe",
						role: "Content Creator (Photo/Video)",
						amount: 600,
						offerDate: "2024-02-20",
						status: "negotiating",
						projectType: "Social Media Marketing",
						location: "Local",
						description:
							"Proposed a monthly retainer for creating engaging social media content.",
						isSentByMe: !0,
					},
				];
		},
		5180: (e, t, a) => {
			Promise.resolve().then(a.bind(a, 8672));
		},
		8381: (e, t, a) => {
			a.d(t, { A: () => s });
			const s = (0, a(1018).A)("Ellipsis", [
				["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }],
				["circle", { cx: "19", cy: "12", r: "1", key: "1wjl8i" }],
				["circle", { cx: "5", cy: "12", r: "1", key: "1pcz8c" }],
			]);
		},
		8672: (e, t, a) => {
			a.d(t, { default: () => Z });
			var s = a(8081),
				i = a(2149),
				l = a(1018);
			const r = (0, l.A)("CreditCard", [
				[
					"rect",
					{ width: "20", height: "14", x: "2", y: "5", rx: "2", key: "ynyp8z" },
				],
				["line", { x1: "2", x2: "22", y1: "10", y2: "10", key: "1b3vmo" }],
			]);
			var n = a(5160),
				o = a(9651),
				c = a(3771),
				d = a(245);
			const x = () => {
					const [e, t] = (0, i.useState)(null),
						[a, s] = (0, i.useState)(!1);
					return {
						selectedProject: e,
						openProjectModal: (e) => {
							t(e);
						},
						closeProjectModal: () => {
							t(null);
						},
						showPaymentModal: a,
						openPaymentModal: () => {
							s(!0);
						},
						closePaymentModal: () => {
							s(!1);
						},
					};
				},
				h = (e) => {
					const { projects: t, setProjects: a, initialMergeCount: s = 0 } = e,
						[l, r] = (0, i.useState)(new Set()),
						[n, o] = (0, i.useState)(!1),
						[c, d] = (0, i.useState)(s),
						x = (0, i.useCallback)(() => {
							o((e) => !e), r(new Set());
						}, []),
						h = (0, i.useCallback)(
							(e) => {
								n &&
									r((t) => {
										const a = new Set(t);
										return a.has(e) ? a.delete(e) : a.add(e), a;
									});
							},
							[n],
						),
						g = (0, i.useCallback)(() => {
							r(new Set());
						}, []),
						m = (0, i.useCallback)(
							(e) => {
								if (l.size < 2) return;
								const s = t.filter((e) => l.has(e.id));
								if (0 === s.length) return;
								const i = Math.min(...s.map((e) => e.position || 0)),
									n = s.some((e) => "text" === e.type),
									x = {
										id: "merged-".concat(Date.now(), "-").concat(c),
										src: "/placeholder.svg?height=800&width=800&text=Merged Projects",
										alt: "Merged: ".concat(
											s.map((e) => e.title.substring(0, 10)).join(", "),
											"...",
										),
										title: "Merged: ".concat(s.map((e) => e.title).join(" + ")),
										client: "Multiple Clients",
										budget: s.reduce((e, t) => e + t.budget, 0),
										status: "active",
										size: e,
										isMerged: !0,
										originalProjects: [...s],
										position: i,
										type: n ? "text" : "image",
										textContent: n
											? s
													.map((e) =>
														"text" === e.type
															? e.textContent || "Empty text"
															: e.title,
													)
													.join("\n\n") || "Merged content"
											: void 0,
										backgroundColor: n ? "#f3f4f6" : void 0,
									};
								a(
									[...t.filter((e) => !l.has(e.id)), x].sort(
										(e, t) => (e.position || 0) - (t.position || 0),
									),
								),
									r(new Set()),
									o(!1),
									d((e) => e + 1);
							},
							[t, l, a, c],
						),
						p = (0, i.useCallback)(
							(e, s) => {
								if ((s.stopPropagation(), !e.isMerged || !e.originalProjects))
									return;
								const i = e.position || 0,
									l = e.originalProjects.map((e, t) => ({
										...e,
										position: void 0 !== e.position ? e.position : i + t,
										isMerged: !1,
										size: e.size || "standard",
									}));
								a(
									[...t.filter((t) => t.id !== e.id), ...l].sort(
										(e, t) => (e.position || 0) - (t.position || 0),
									),
								);
							},
							[t, a],
						);
					return {
						selectedTiles: l,
						isSelectionMode: n,
						mergeCount: c,
						toggleTileSelection: h,
						clearSelection: g,
						toggleSelectionMode: x,
						mergeTiles: m,
						splitTile: p,
						convertToTextTile: (0, i.useCallback)(
							(e) => {
								a((t) =>
									t.map((t) =>
										t.id === e
											? {
													...t,
													type: "text",
													textContent: t.textContent || "Click to edit text",
													backgroundColor: t.backgroundColor || "#f3f4f6",
													src: void 0,
													alt: t.alt || "Text tile",
												}
											: t,
									),
								);
							},
							[a],
						),
						convertToImageTile: (0, i.useCallback)(
							(e) => {
								a((t) =>
									t.map((t) =>
										t.id === e
											? {
													...t,
													type: "image",
													textContent: void 0,
													backgroundColor: void 0,
													src:
														t.src ||
														"/placeholder.svg?height=400&width=400&text=Image",
													alt: t.alt || "Image tile",
												}
											: t,
									),
								);
							},
							[a],
						),
					};
				},
				g = (e) => {
					const { setProjects: t } = e,
						[a, s] = (0, i.useState)(null),
						[l, r] = (0, i.useState)(""),
						n = (0, i.useCallback)((e) => {
							const t =
								arguments.length > 1 && void 0 !== arguments[1]
									? arguments[1]
									: "";
							s(e), r(t);
						}, []),
						o = (0, i.useCallback)((e) => {
							r(e);
						}, []),
						c = (0, i.useCallback)(() => {
							a &&
								(t((e) =>
									e.map((e) =>
										e.id === a ? { ...e, textContent: l || "Empty text" } : e,
									),
								),
								s(null),
								r(""));
						}, [a, l, t]);
					return {
						editingTile: a,
						tempText: l,
						startEditingText: n,
						handleSetTempText: o,
						saveTextEdit: c,
						cancelTextEdit: (0, i.useCallback)(() => {
							s(null), r("");
						}, []),
					};
				},
				m = { rating: 0, review: "" },
				p = () => {
					const [e, t] = (0, i.useState)(!1),
						[a, s] = (0, i.useState)(null),
						[l, r] = (0, i.useState)(m),
						n = (0, i.useCallback)((e) => {
							s(e), r({ rating: e.rating || 0, review: e.review || "" }), t(!0);
						}, []),
						o = (0, i.useCallback)(() => {
							t(!1), s(null), r(m);
						}, []),
						c = (0, i.useCallback)((e, t) => {
							r((a) => ({ ...a, [e]: t }));
						}, []),
						d = (0, i.useCallback)(
							(e) => {
								e.preventDefault(),
									console.log(
										"Review submitted:",
										l,
										"for collaboration:",
										null == a ? void 0 : a.id,
									),
									alert("Review updated successfully! (Placeholder)"),
									o();
							},
							[l, a, o],
						);
					return {
						showReviewModal: e,
						selectedCollaboration: a,
						reviewForm: l,
						openReviewModal: n,
						closeReviewModal: o,
						updateReviewFormField: c,
						handleReviewSubmit: d,
					};
				},
				u = {
					title: "",
					client: "",
					budget: "",
					projectType: "",
					startDate: "",
					endDate: "",
					location: "",
					description: "",
					creativesNeeded: [],
					type: "image",
					size: "standard",
					isMerged: !1,
					textContent: "",
					backgroundColor: "#f3f4f6",
				},
				f = (e) => {
					const { projects: t, setProjects: a } = e,
						[s, l] = (0, i.useState)(!1),
						[r, n] = (0, i.useState)(null),
						[o, c] = (0, i.useState)(u),
						d = (0, i.useCallback)((e) => {
							e
								? (n(e),
									c({
										...u,
										...e,
										id: e.id,
										budget: e.budget.toString(),
										title: e.title || "",
										client: e.client || "",
										projectType: e.projectType || "",
										startDate: e.startDate || "",
										endDate: e.endDate || "",
										location: e.location || "",
										description: e.description || "",
										creativesNeeded: e.creativesNeeded || [],
										type: e.type || "image",
										size: e.size || "standard",
										isMerged: "boolean" == typeof e.isMerged && e.isMerged,
										textContent:
											e.textContent || ("text" === e.type && e.title) || "",
										backgroundColor:
											e.backgroundColor ||
											("text" === e.type ? "#f3f4f6" : void 0),
									}))
								: (n(null), c(u)),
								l(!0);
						}, []),
						x = (0, i.useCallback)(() => {
							l(!1), n(null), c(u);
						}, []),
						h = (0, i.useCallback)((e, t) => {
							c((a) => ({ ...a, [e]: t }));
						}, []),
						g = (0, i.useCallback)(() => {
							const e = prompt(
								"Enter creative role (e.g., Photographer, Model, etc.):",
							);
							e &&
								e.trim() &&
								c((t) => ({
									...t,
									creativesNeeded: [...t.creativesNeeded, e.trim()],
								}));
						}, []),
						m = (0, i.useCallback)((e) => {
							c((t) => ({
								...t,
								creativesNeeded: t.creativesNeeded.filter((t, a) => a !== e),
							}));
						}, []),
						p = (0, i.useCallback)(
							(e) => {
								if ((e.preventDefault(), r)) {
									const e = {
										...r,
										...o,
										budget: Number.parseInt(o.budget, 10) || 0,
									};
									"text" === e.type
										? ((e.src = void 0),
											(e.alt = void 0),
											void 0 === e.textContent &&
												(e.textContent = e.title || ""),
											void 0 === e.backgroundColor &&
												(e.backgroundColor = "#f3f4f6"))
										: "image" !== e.type ||
											((e.textContent = void 0),
											(e.backgroundColor = void 0),
											e.src ||
												(e.src =
													"/placeholder.svg?height=400&width=400&text=" +
													encodeURIComponent(e.title || "Project")),
											e.alt || (e.alt = e.title || "Project Image")),
										a((t) => t.map((t) => (t.id === r.id ? e : t))),
										alert("Project updated successfully! (Placeholder)");
								} else {
									const e = {
										id: "project-".concat(Date.now()),
										title: o.title,
										client: o.client,
										budget: Number.parseInt(o.budget, 10) || 0,
										projectType: o.projectType,
										startDate: o.startDate,
										endDate: o.endDate,
										location: o.location,
										description: o.description,
										creativesNeeded: o.creativesNeeded,
										type: o.type || "image",
										size: o.size || "standard",
										isMerged: "boolean" == typeof o.isMerged && o.isMerged,
										textContent:
											"text" === o.type
												? o.textContent || o.title || ""
												: void 0,
										backgroundColor:
											"text" === o.type
												? o.backgroundColor || "#f3f4f6"
												: void 0,
										src:
											"image" === o.type
												? "/placeholder.svg?height=400&width=400&text=" +
													encodeURIComponent(o.title || "New Project")
												: void 0,
										alt: "image" === o.type ? o.title || "New Project" : void 0,
										status: "planning",
										position: t.length,
									};
									a((t) => [...t, e]),
										alert("Project added successfully! (Placeholder)");
								}
								x();
							},
							[o, t, a, x, r],
						);
					return {
						showAddProjectModal: s,
						editingProject: r,
						newProjectForm: o,
						openAddProjectModal: d,
						closeAddProjectModal: x,
						updateNewProjectFormField: h,
						handleAddProjectSubmit: p,
						addArtistRoleToForm: g,
						removeArtistRoleFromForm: m,
					};
				};
			var j = a(7753);
			const y = (e) => {
					const {
						name: t,
						type: a,
						projectCount: i,
						offersSentCount: l,
						collaborationsCount: r,
						membersCount: n,
					} = e;
					return (0, s.jsxs)("div", {
						className: "text-center mb-12",
						children: [
							(0, s.jsx)("h2", {
								className: "text-3xl font-light tracking-wide mb-3",
								children: t,
							}),
							(0, s.jsx)("p", {
								className: "text-gray-600 font-light tracking-wide mb-6",
								children: a,
							}),
							(0, s.jsxs)("div", {
								className: "flex justify-center gap-12 text-sm font-light",
								children: [
									(0, s.jsxs)("span", {
										children: [
											(0, s.jsx)("strong", {
												className: "font-normal",
												children: i,
											}),
											" Projects",
										],
									}),
									(0, s.jsxs)("span", {
										children: [
											(0, s.jsx)("strong", {
												className: "font-normal",
												children: l,
											}),
											" Offers Sent",
										],
									}),
									(0, s.jsxs)("span", {
										children: [
											(0, s.jsx)("strong", {
												className: "font-normal",
												children: r,
											}),
											" Collaborations",
										],
									}),
									(0, s.jsxs)("span", {
										children: [
											(0, s.jsx)("strong", {
												className: "font-normal",
												children: n,
											}),
											" Members",
										],
									}),
								],
							}),
						],
					});
				},
				b = [
					{
						id: "act1",
						userName: "Alice Wonderland",
						action: "invited",
						target: "Bob The Builder",
						timestamp: "2 hours ago",
						status: "active",
					},
					{
						id: "act2",
						userName: "Bob The Builder",
						action: "updated settings for",
						target: "Spring Fashion Campaign",
						timestamp: "1 day ago",
						status: "completed",
					},
					{
						id: "act3",
						userName: "Charlie Chaplin",
						action: "viewed",
						target: "Product Launch Event",
						timestamp: "3 days ago",
						status: "completed",
					},
					{
						id: "act4",
						userName: "Alice Wonderland",
						action: "changed permission for",
						target: "Diana Prince to Viewer",
						timestamp: "5 days ago",
						status: "completed",
					},
				],
				v = (e) => {
					const { activeTab: t } = e;
					return (0, s.jsxs)("div", {
						className: "bg-gray-900 rounded p-8 mb-12",
						children: [
							(0, s.jsxs)("div", {
								className: "flex justify-between items-center mb-6",
								children: [
									(0, s.jsx)("span", {
										className: "text-white text-sm font-light tracking-wide",
										children:
											"budget" === t
												? "Budget Breakdown"
												: "members" === t
													? "Latest Member Activity"
													: "Project Timeline",
									}),
									"members" !== t &&
										(0, s.jsxs)("div", {
											className: "flex items-center gap-6",
											children: [
												(0, s.jsxs)("span", {
													className: "flex items-center gap-2",
													children: [
														(0, s.jsx)("div", {
															className: "w-2 h-2 rounded-full bg-blue-400",
														}),
														(0, s.jsx)("span", {
															className:
																"text-gray-400 text-xs font-light tracking-wide",
															children: "Active",
														}),
													],
												}),
												(0, s.jsxs)("span", {
													className: "flex items-center gap-2",
													children: [
														(0, s.jsx)("div", {
															className: "w-2 h-2 rounded-full bg-green-400",
														}),
														(0, s.jsx)("span", {
															className:
																"text-gray-400 text-xs font-light tracking-wide",
															children: "Completed",
														}),
													],
												}),
											],
										}),
								],
							}),
							"budget" === t
								? (0, s.jsx)("div", {
										className: "h-48 relative",
										children: (0, s.jsxs)("svg", {
											className: "w-full h-full",
											viewBox: "0 0 400 200",
											children: [
												(0, s.jsx)("defs", {
													children: (0, s.jsx)("pattern", {
														id: "grid",
														width: "40",
														height: "20",
														patternUnits: "userSpaceOnUse",
														children: (0, s.jsx)("path", {
															d: "M 40 0 L 0 0 0 20",
															fill: "none",
															stroke: "#374151",
															strokeWidth: "0.5",
															opacity: "0.3",
														}),
													}),
												}),
												(0, s.jsx)("rect", {
													width: "100%",
													height: "100%",
													fill: "url(#grid)",
												}),
												(0, s.jsx)("line", {
													x1: "20",
													y1: "180",
													x2: "380",
													y2: "180",
													stroke: "#4b5563",
													strokeWidth: "1",
												}),
												(0, s.jsx)("line", {
													x1: "20",
													y1: "20",
													x2: "20",
													y2: "180",
													stroke: "#4b5563",
													strokeWidth: "1",
												}),
												[
													{
														x: 40,
														width: 20,
														height: 70,
														amount: 3500,
														category: "Photography",
													},
													{
														x: 80,
														width: 20,
														height: 56,
														amount: 2800,
														category: "Videography",
													},
													{
														x: 120,
														width: 20,
														height: 24,
														amount: 1200,
														category: "Makeup",
													},
													{
														x: 160,
														width: 20,
														height: 80,
														amount: 4e3,
														category: "Modeling",
													},
													{
														x: 200,
														width: 20,
														height: 60,
														amount: 3e3,
														category: "Styling",
													},
													{
														x: 240,
														width: 20,
														height: 40,
														amount: 2e3,
														category: "Location",
													},
													{
														x: 280,
														width: 20,
														height: 100,
														amount: 5e3,
														category: "Equipment",
													},
													{
														x: 320,
														width: 20,
														height: 32,
														amount: 1600,
														category: "Catering",
													},
												].map((e, t) =>
													(0, s.jsxs)(
														"g",
														{
															className: "group",
															children: [
																(0, s.jsx)("rect", {
																	x: e.x,
																	y: 180 - e.height,
																	width: e.width,
																	height: e.height,
																	fill: "#3b82f6",
																	className:
																		"hover:fill-blue-600 transition-colors cursor-pointer",
																	children: (0, s.jsxs)("title", {
																		children: [
																			"$",
																			e.amount.toLocaleString(),
																			" - ",
																			e.category,
																		],
																	}),
																}),
																(0, s.jsxs)("text", {
																	x: e.x + e.width / 2,
																	y: 175 - e.height,
																	fill: "#1e40af",
																	fontSize: "10",
																	textAnchor: "middle",
																	className:
																		"opacity-0 group-hover:opacity-100 transition-opacity",
																	children: ["$", e.amount],
																}),
															],
														},
														t,
													),
												),
												(0, s.jsx)("text", {
													x: "15",
													y: "30",
													fill: "#9ca3af",
													fontSize: "10",
													textAnchor: "end",
													children: "$5K",
												}),
												(0, s.jsx)("text", {
													x: "15",
													y: "80",
													fill: "#9ca3af",
													fontSize: "10",
													textAnchor: "end",
													children: "$4K",
												}),
												(0, s.jsx)("text", {
													x: "15",
													y: "130",
													fill: "#9ca3af",
													fontSize: "10",
													textAnchor: "end",
													children: "$2K",
												}),
												(0, s.jsx)("text", {
													x: "15",
													y: "180",
													fill: "#9ca3af",
													fontSize: "10",
													textAnchor: "end",
													children: "$0",
												}),
												(0, s.jsx)("text", {
													x: "50",
													y: "195",
													fill: "#9ca3af",
													fontSize: "8",
													textAnchor: "middle",
													children: "Photo",
												}),
												(0, s.jsx)("text", {
													x: "130",
													y: "195",
													fill: "#9ca3af",
													fontSize: "8",
													textAnchor: "middle",
													children: "Makeup",
												}),
												(0, s.jsx)("text", {
													x: "210",
													y: "195",
													fill: "#9ca3af",
													fontSize: "8",
													textAnchor: "middle",
													children: "Styling",
												}),
												(0, s.jsx)("text", {
													x: "290",
													y: "195",
													fill: "#9ca3af",
													fontSize: "8",
													textAnchor: "middle",
													children: "Equipment",
												}),
											],
										}),
									})
								: "members" === t
									? (0, s.jsx)("div", {
											className:
												"h-auto min-h-48 relative text-gray-300 p-4 text-sm font-light",
											children:
												b.length > 0
													? (0, s.jsx)("ul", {
															className: "space-y-3",
															children: b.map((e) =>
																(0, s.jsxs)(
																	"li",
																	{
																		className: "flex items-start",
																		children: [
																			(0, s.jsx)("span", {
																				className:
																					"text-xs text-gray-500 whitespace-nowrap mr-4 w-24 text-right",
																				children: e.timestamp,
																			}),
																			(0, s.jsxs)("div", {
																				className: "flex-grow ".concat(
																					"active" === e.status
																						? "text-blue-400"
																						: "text-green-400",
																				),
																				children: [
																					(0, s.jsx)("span", {
																						className: "font-medium",
																						children: e.userName,
																					}),
																					(0, s.jsxs)("span", {
																						className: "".concat(
																							"active" === e.status
																								? "text-blue-300"
																								: "text-green-300",
																							" opacity-80",
																						),
																						children: [" ", e.action, " "],
																					}),
																					e.target &&
																						(0, s.jsx)("span", {
																							className: "font-normal ".concat(
																								"active" === e.status
																									? "text-blue-400"
																									: "text-green-400",
																							),
																							children: e.target,
																						}),
																				],
																			}),
																		],
																	},
																	e.id,
																),
															),
														})
													: (0, s.jsx)("p", {
															className: "text-center text-gray-400",
															children: "No recent member activity.",
														}),
										})
									: (0, s.jsx)("div", {
											className: "grid grid-cols-3 gap-8",
											children: ["Jan", "Feb", "Mar"].map((e, t) =>
												(0, s.jsxs)(
													"div",
													{
														children: [
															(0, s.jsx)("div", {
																className:
																	"text-center text-gray-400 text-xs mb-4 font-light tracking-wide",
																children: e,
															}),
															(0, s.jsx)("div", {
																className: "grid grid-cols-7 gap-1 mb-3",
																children: [
																	"Sun",
																	"Mon",
																	"Tue",
																	"Wed",
																	"Thu",
																	"Fri",
																	"Sat",
																].map((e) =>
																	(0, s.jsx)(
																		"div",
																		{
																			className:
																				"text-center text-gray-500 text-xs font-light tracking-wide",
																			children: e,
																		},
																		e,
																	),
																),
															}),
															(0, s.jsx)("div", {
																className: "grid grid-cols-7 gap-1",
																children: [...Array(31)].map((t, a) => {
																	const i = Math.random() > 0.8,
																		l = Math.random() > 0.6;
																	return (0, s.jsx)(
																		"div",
																		{
																			className:
																				"w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ".concat(
																					i
																						? l
																							? "bg-green-900 hover:bg-green-800 text-green-200"
																							: "bg-blue-900 hover:bg-blue-800 text-blue-200"
																						: "bg-gray-800 hover:bg-gray-700 text-gray-400",
																					" cursor-pointer transition-colors",
																				),
																			title: ""
																				.concat(e, " ")
																				.concat(a + 1, ": ")
																				.concat(
																					i
																						? l
																							? "Completed"
																							: "Active"
																						: "Available",
																				),
																			children: a + 1,
																		},
																		"".concat(e, "-day-").concat(a + 1),
																	);
																}),
															}),
														],
													},
													e,
												),
											),
										}),
						],
					});
				},
				N = (e) => {
					const {
						activeTab: t,
						onTabChange: a,
						projectCount: i,
						offersSentCount: l,
						totalBudgetFormatted: r,
						archiveCount: o,
						membersCount: c,
						isSelectionMode: d,
						onOpenAddProjectModal: x,
						onMergeTiles: h,
						onClearSelection: g,
						onConvertToText: m,
						onConvertToImage: p,
						selectedTilesCount: u,
					} = e;
					return (0, s.jsx)("div", {
						className: "border-b border-gray-100",
						children: (0, s.jsxs)("div", {
							className: "max-w-4xl mx-auto px-6 py-8",
							children: [
								(0, s.jsxs)("div", {
									className: "flex items-center justify-center gap-12 mb-8",
									children: [
										(0, s.jsxs)("button", {
											onClick: () => a("projects"),
											className: "text-sm font-light tracking-wide ".concat(
												"projects" === t ? "text-black" : "text-gray-400",
											),
											children: ["Projects (", i, ")"],
										}),
										(0, s.jsxs)("button", {
											onClick: () => a("offers"),
											className: "text-sm font-light tracking-wide ".concat(
												"offers" === t ? "text-black" : "text-gray-400",
											),
											children: ["Offers Sent (", l, ")"],
										}),
										(0, s.jsxs)("button", {
											onClick: () => a("budget"),
											className: "text-sm font-light tracking-wide ".concat(
												"budget" === t ? "text-black" : "text-gray-400",
											),
											children: ["Budget (", r, ")"],
										}),
										(0, s.jsxs)("button", {
											onClick: () => a("archive"),
											className: "text-sm font-light tracking-wide ".concat(
												"archive" === t ? "text-black" : "text-gray-400",
											),
											children: ["Archive (", o, ")"],
										}),
										(0, s.jsxs)("button", {
											onClick: () => a("members"),
											className: "text-sm font-light tracking-wide ".concat(
												"members" === t ? "text-black" : "text-gray-400",
											),
											children: ["Members (", c, ")"],
										}),
									],
								}),
								"projects" === t &&
									(0, s.jsxs)("div", {
										className: "flex items-center justify-center gap-4",
										children: [
											(0, s.jsx)(n.$, {
												variant: "outline",
												size: "sm",
												className: "text-xs font-light tracking-wide",
												onClick: x,
												children: "+ Add Project",
											}),
											d &&
												(0, s.jsxs)(s.Fragment, {
													children: [
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: () => h("horizontal"),
															disabled: u < 2,
															children: "Merge H",
														}),
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: () => h("vertical"),
															disabled: u < 2,
															children: "Merge V",
														}),
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: () => h("large"),
															disabled: u < 2,
															children: "Merge L",
														}),
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: m,
															disabled: 0 === u,
															children: "To Text",
														}),
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: p,
															disabled: 0 === u,
															children: "To Image",
														}),
														(0, s.jsx)(n.$, {
															variant: "outline",
															size: "sm",
															onClick: g,
															disabled: 0 === u,
															children: "Clear Sel.",
														}),
													],
												}),
										],
									}),
							],
						}),
					});
				};
			var w = a(5186),
				k = a(7950),
				C = a.n(k),
				S = a(8381);
			const M = (e) => {
					const {
						selectedProject: t,
						offersSent: a,
						onClose: i,
						onEditProject: l,
					} = e;
					return t
						? (0, s.jsx)("div", {
								className:
									"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4",
								children: (0, s.jsxs)("div", {
									className:
										"bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row",
									children: [
										(0, s.jsx)("div", {
											className:
												"w-full md:w-80 relative min-h-[250px] md:min-h-0",
											children: (0, s.jsx)(w.default, {
												src: t.src || "/placeholder.svg",
												alt: t.alt,
												fill: !0,
												className: "object-cover",
											}),
										}),
										(0, s.jsxs)("div", {
											className: "flex-1 flex flex-col",
											children: [
												(0, s.jsxs)("div", {
													className:
														"flex items-center justify-between p-4 border-b border-gray-100",
													children: [
														(0, s.jsx)("div", {
															className: "flex items-center gap-3",
															children: (0, s.jsx)("span", {
																className: "font-light tracking-wide",
																children: t.title,
															}),
														}),
														(0, s.jsx)("button", {
															onClick: i,
															children: (0, s.jsx)(S.A, {
																className: "w-5 h-5",
															}),
														}),
													],
												}),
												(0, s.jsx)("div", {
													className: "flex-1 p-4 overflow-y-auto",
													children: (0, s.jsxs)("div", {
														className: "space-y-4",
														children: [
															(0, s.jsx)("div", {
																className: "mb-4",
																children: (0, s.jsx)("span", {
																	className:
																		"px-3 py-1 text-xs font-light tracking-wide rounded ".concat(
																			"active" === t.status
																				? "bg-blue-100 text-blue-800"
																				: "completed" === t.status
																					? "bg-green-100 text-green-800"
																					: "bg-yellow-100 text-yellow-800",
																		),
																	children:
																		t.status.charAt(0).toUpperCase() +
																		t.status.slice(1),
																}),
															}),
															(0, s.jsxs)("div", {
																children: [
																	(0, s.jsx)("h3", {
																		className:
																			"text-sm font-light text-gray-500 tracking-wide mb-1",
																		children: "Client",
																	}),
																	(0, s.jsx)("p", {
																		className: "font-light",
																		children: t.client,
																	}),
																],
															}),
															(0, s.jsxs)("div", {
																children: [
																	(0, s.jsx)("h3", {
																		className:
																			"text-sm font-light text-gray-500 tracking-wide mb-1",
																		children: "Budget",
																	}),
																	(0, s.jsxs)("p", {
																		className: "font-light",
																		children: ["$", t.budget.toLocaleString()],
																	}),
																],
															}),
															t.projectType &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Project Type",
																		}),
																		(0, s.jsx)("p", {
																			className: "font-light",
																			children: t.projectType,
																		}),
																	],
																}),
															t.startDate &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Timeline",
																		}),
																		(0, s.jsxs)("p", {
																			className: "font-light",
																			children: [
																				t.startDate,
																				t.endDate && t.endDate !== t.startDate
																					? " to ".concat(t.endDate)
																					: "",
																			],
																		}),
																	],
																}),
															t.location &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Location",
																		}),
																		(0, s.jsx)("p", {
																			className: "font-light",
																			children: t.location,
																		}),
																	],
																}),
															t.description &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Description",
																		}),
																		(0, s.jsx)("p", {
																			className: "text-gray-700 font-light",
																			children: t.description,
																		}),
																	],
																}),
															(0, s.jsxs)("div", {
																className: "border-t border-gray-100 pt-4",
																children: [
																	(0, s.jsx)("h3", {
																		className:
																			"text-sm font-light text-gray-500 tracking-wide mb-3",
																		children: "Project Staffing Status",
																	}),
																	(0, s.jsx)("div", {
																		className: "overflow-x-auto",
																		children: (0, s.jsxs)("table", {
																			className: "w-full text-sm",
																			children: [
																				(0, s.jsx)("thead", {
																					children: (0, s.jsxs)("tr", {
																						className:
																							"border-b border-gray-100",
																						children: [
																							(0, s.jsx)("th", {
																								className:
																									"text-left py-2 text-xs font-light text-gray-500 tracking-wide",
																								children: "Role",
																							}),
																							(0, s.jsx)("th", {
																								className:
																									"text-left py-2 text-xs font-light text-gray-500 tracking-wide",
																								children: "Offers Sent",
																							}),
																							(0, s.jsx)("th", {
																								className:
																									"text-right py-2 text-xs font-light text-gray-500 tracking-wide",
																								children: "Budget Range",
																							}),
																							(0, s.jsx)("th", {
																								className:
																									"text-right py-2 text-xs font-light text-gray-500 tracking-wide",
																								children: "Status",
																							}),
																						],
																					}),
																				}),
																				(0, s.jsx)("tbody", {
																					className: "divide-y divide-gray-100",
																					children:
																						t.creativesNeeded &&
																						t.creativesNeeded.length > 0
																							? t.creativesNeeded.map(
																									(e, i) => {
																										const l = a.filter(
																												(a) =>
																													a.projectTitle ===
																														t.title &&
																													a.projectType
																														.toLowerCase()
																														.includes(
																															e
																																.toLowerCase()
																																.split(" ")[0],
																														),
																											),
																											r = l.filter(
																												(e) =>
																													"accepted" ===
																													e.status,
																											),
																											n = l.filter(
																												(e) =>
																													"pending" ===
																													e.status,
																											),
																											o = l.filter(
																												(e) =>
																													"declined" ===
																													e.status,
																											),
																											c =
																												r.length > 0
																													? {
																															status:
																																"Confirmed",
																															color:
																																"bg-green-100 text-green-800",
																														}
																													: n.length > 0
																														? {
																																status:
																																	"Pending",
																																color:
																																	"bg-yellow-100 text-yellow-800",
																															}
																														: o.length > 0 &&
																																l.length ===
																																	o.length
																															? {
																																	status:
																																		"Declined",
																																	color:
																																		"bg-red-100 text-red-800",
																																}
																															: {
																																	status:
																																		"Needed",
																																	color:
																																		"bg-orange-100 text-orange-800",
																																},
																											d =
																												l.length > 0
																													? "$"
																															.concat(
																																Math.min(
																																	...l.map(
																																		(e) =>
																																			e.amount,
																																	),
																																).toLocaleString(),
																																" - $",
																															)
																															.concat(
																																Math.max(
																																	...l.map(
																																		(e) =>
																																			e.amount,
																																	),
																																).toLocaleString(),
																															)
																													: "-";
																										return (0, s.jsxs)(
																											"tr",
																											{
																												className:
																													"hover:bg-gray-50",
																												children: [
																													(0, s.jsx)("td", {
																														className:
																															"py-3 font-light text-gray-900",
																														children: e,
																													}),
																													(0, s.jsx)("td", {
																														className: "py-3",
																														children: (0,
																														s.jsx)("div", {
																															className:
																																"flex flex-wrap gap-1",
																															children:
																																l.length > 0
																																	? l.map(
																																			(e, t) =>
																																				(0,
																																				s.jsxs)(
																																					C(),
																																					{
																																						href: "/creative/".concat(
																																							e.artistId,
																																						),
																																						className:
																																							"flex items-center gap-1 bg-gray-50 hover:bg-gray-100 rounded-full px-2 py-1 border transition-colors cursor-pointer",
																																						children:
																																							[
																																								(0,
																																								s.jsx)(
																																									"div",
																																									{
																																										className:
																																											"w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center",
																																										children:
																																											(0,
																																											s.jsx)(
																																												"span",
																																												{
																																													className:
																																														"text-xs font-light text-gray-600",
																																													children:
																																														e.artistName
																																															.split(
																																																" ",
																																															)
																																															.map(
																																																(
																																																	e,
																																																) =>
																																																	e[0],
																																															)
																																															.join(
																																																"",
																																															),
																																												},
																																											),
																																									},
																																								),
																																								(0,
																																								s.jsx)(
																																									"span",
																																									{
																																										className:
																																											"text-xs text-gray-900 mr-1 font-light",
																																										children:
																																											e.artistName.split(
																																												" ",
																																											)[0],
																																									},
																																								),
																																								(0,
																																								s.jsx)(
																																									"span",
																																									{
																																										className:
																																											"px-1 py-0.5 text-xs font-light rounded-full ".concat(
																																												"accepted" ===
																																													e.status
																																													? "bg-green-500 text-white"
																																													: "declined" ===
																																															e.status
																																														? "bg-red-500 text-white"
																																														: "expired" ===
																																																e.status
																																															? "bg-gray-500 text-white"
																																															: "bg-yellow-500 text-white",
																																											),
																																										children:
																																											"accepted" ===
																																											e.status
																																												? "✓"
																																												: "declined" ===
																																														e.status
																																													? "✗"
																																													: "expired" ===
																																															e.status
																																														? "⏰"
																																														: "⏳",
																																									},
																																								),
																																							],
																																					},
																																					t,
																																				),
																																		)
																																	: (0, s.jsxs)(
																																			C(),
																																			{
																																				href: "/?role="
																																					.concat(
																																						encodeURIComponent(
																																							e,
																																						),
																																						"&project=",
																																					)
																																					.concat(
																																						encodeURIComponent(
																																							t.title,
																																						),
																																						"&location=",
																																					)
																																					.concat(
																																						encodeURIComponent(
																																							t.location ||
																																								"",
																																						),
																																						"&budget=",
																																					)
																																					.concat(
																																						t.budget,
																																						"&startDate=",
																																					)
																																					.concat(
																																						t.startDate,
																																						"&endDate=",
																																					)
																																					.concat(
																																						t.endDate,
																																					),
																																				className:
																																					"inline-flex items-center gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 hover:text-blue-800 rounded px-3 py-1 border border-blue-200 transition-colors text-xs font-light tracking-wide",
																																				children:
																																					[
																																						(0,
																																						s.jsx)(
																																							"svg",
																																							{
																																								className:
																																									"w-3 h-3",
																																								fill: "none",
																																								stroke:
																																									"currentColor",
																																								viewBox:
																																									"0 0 24 24",
																																								children:
																																									(0,
																																									s.jsx)(
																																										"path",
																																										{
																																											strokeLinecap:
																																												"round",
																																											strokeLinejoin:
																																												"round",
																																											strokeWidth: 2,
																																											d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
																																										},
																																									),
																																							},
																																						),
																																						"Search ",
																																						e.toUpperCase(),
																																					],
																																			},
																																		),
																														}),
																													}),
																													(0, s.jsx)("td", {
																														className:
																															"py-3 text-right font-light text-gray-900",
																														children: d,
																													}),
																													(0, s.jsx)("td", {
																														className:
																															"py-3 text-right",
																														children: (0,
																														s.jsx)("span", {
																															className:
																																"px-2 py-1 text-xs font-light tracking-wide rounded-full ".concat(
																																	c.color,
																																),
																															children:
																																c.status,
																														}),
																													}),
																												],
																											},
																											i,
																										);
																									},
																								)
																							: (0, s.jsx)("tr", {
																									children: (0, s.jsx)("td", {
																										colSpan: 4,
																										className:
																											"py-6 text-center text-gray-500",
																										children: (0, s.jsx)("p", {
																											className:
																												"text-sm font-light",
																											children:
																												"No creative requirements specified",
																										}),
																									}),
																								}),
																				}),
																			],
																		}),
																	}),
																	(0, s.jsxs)("div", {
																		className:
																			"mt-4 flex justify-between text-xs text-gray-500 font-light",
																		children: [
																			(0, s.jsx)("span", {
																				children: t.creativesNeeded
																					? "".concat(
																							t.creativesNeeded.length,
																							" roles needed",
																						)
																					: "0 roles needed",
																			}),
																			(0, s.jsxs)("span", {
																				children: [
																					a.filter(
																						(e) => e.projectTitle === t.title,
																					).length,
																					" offers sent",
																				],
																			}),
																		],
																	}),
																],
															}),
														],
													}),
												}),
												(0, s.jsx)("div", {
													className: "border-t border-gray-100 p-4",
													children: (0, s.jsx)("div", {
														className: "flex gap-2",
														children: (0, s.jsx)(n.$, {
															variant: "outline",
															className: "flex-1 font-light tracking-wide",
															onClick: () => l(t),
															children: "Edit Project",
														}),
													}),
												}),
											],
										}),
									],
								}),
							})
						: null;
				},
				T = (e) => {
					const {
						showModal: t,
						onClose: a,
						formData: i,
						onFormChange: l,
						onSubmit: r,
						onAddArtistRole: o,
						onRemoveArtistRole: c,
						isEditing: d,
					} = e;
					return t
						? (0, s.jsx)("div", {
								className:
									"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
								children: (0, s.jsxs)("div", {
									className:
										"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
									children: [
										(0, s.jsxs)("div", {
											className:
												"flex items-center justify-between p-6 border-b border-gray-100",
											children: [
												(0, s.jsx)("h2", {
													className: "text-xl font-light tracking-wide",
													children: d ? "Edit Project" : "Add New Project",
												}),
												(0, s.jsx)("button", {
													onClick: a,
													children: (0, s.jsx)(S.A, { className: "w-5 h-5" }),
												}),
											],
										}),
										(0, s.jsxs)("form", {
											onSubmit: r,
											className: "p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
											children: [
												(0, s.jsxs)("div", {
													className: "space-y-6",
													children: [
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Project Title *",
																}),
																(0, s.jsx)("input", {
																	type: "text",
																	required: !0,
																	value: i.title,
																	onChange: (e) => l("title", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder: "e.g., Summer Fashion Campaign",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Client *",
																}),
																(0, s.jsx)("input", {
																	type: "text",
																	required: !0,
																	value: i.client,
																	onChange: (e) => l("client", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder: "e.g., Nike",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Budget (USD) *",
																}),
																(0, s.jsx)("input", {
																	type: "number",
																	required: !0,
																	value: i.budget,
																	onChange: (e) => l("budget", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder: "15000",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Project Type *",
																}),
																(0, s.jsxs)("select", {
																	required: !0,
																	value: i.projectType,
																	onChange: (e) =>
																		l("projectType", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	children: [
																		(0, s.jsx)("option", {
																			value: "",
																			children: "Select Type",
																		}),
																		(0, s.jsx)("option", {
																			value: "Photography",
																			children: "Photography",
																		}),
																		(0, s.jsx)("option", {
																			value: "Videography",
																			children: "Videography",
																		}),
																		(0, s.jsx)("option", {
																			value: "Event Coverage",
																			children: "Event Coverage",
																		}),
																		(0, s.jsx)("option", {
																			value: "Social Media Content",
																			children: "Social Media Content",
																		}),
																		(0, s.jsx)("option", {
																			value: "Brand Identity",
																			children: "Brand Identity",
																		}),
																		(0, s.jsx)("option", {
																			value: "Music Video Production",
																			children: "Music Video Production",
																		}),
																		(0, s.jsx)("option", {
																			value: "Other",
																			children: "Other",
																		}),
																	],
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Start Date *",
																}),
																(0, s.jsx)("input", {
																	type: "date",
																	required: !0,
																	value: i.startDate,
																	onChange: (e) =>
																		l("startDate", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "End Date",
																}),
																(0, s.jsx)("input", {
																	type: "date",
																	value: i.endDate,
																	onChange: (e) => l("endDate", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Location *",
																}),
																(0, s.jsx)("input", {
																	type: "text",
																	required: !0,
																	value: i.location,
																	onChange: (e) =>
																		l("location", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder: "e.g., Los Angeles, CA",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Description",
																}),
																(0, s.jsx)("textarea", {
																	rows: 3,
																	value: i.description,
																	onChange: (e) =>
																		l("description", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder:
																		"Describe your project requirements and vision...",
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsxs)("div", {
																	className:
																		"flex items-center justify-between mb-4",
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-lg font-light tracking-wide",
																			children: "creatives Needed",
																		}),
																		(0, s.jsx)(n.$, {
																			type: "button",
																			variant: "outline",
																			size: "sm",
																			onClick: o,
																			className:
																				"text-xs font-light tracking-wide",
																			children: "+ Add Role",
																		}),
																	],
																}),
																(0, s.jsxs)("div", {
																	className: "flex flex-wrap gap-2",
																	children: [
																		i.creativesNeeded.map((e, t) =>
																			(0, s.jsxs)(
																				"div",
																				{
																					className:
																						"flex items-center gap-2 bg-gray-100 rounded-full px-3 py-1",
																					children: [
																						(0, s.jsx)("span", {
																							className: "text-sm font-light",
																							children: e,
																						}),
																						(0, s.jsx)("button", {
																							type: "button",
																							onClick: () => c(t),
																							className:
																								"text-red-500 hover:text-red-700",
																							children: "\xd7",
																						}),
																					],
																				},
																				t,
																			),
																		),
																		0 === i.creativesNeeded.length &&
																			(0, s.jsx)("p", {
																				className:
																					"text-gray-500 text-sm font-light",
																				children:
																					"No creative roles added yet.",
																			}),
																	],
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													className:
														"flex gap-4 mt-8 pt-6 border-t border-gray-100",
													children: [
														(0, s.jsx)(n.$, {
															type: "button",
															variant: "outline",
															onClick: a,
															className: "flex-1 font-light tracking-wide",
															children: "Cancel",
														}),
														(0, s.jsx)(n.$, {
															type: "submit",
															className:
																"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
															children: d ? "Save Changes" : "Create Project",
														}),
													],
												}),
											],
										}),
									],
								}),
							})
						: null;
				},
				P = (0, l.A)("Star", [
					[
						"path",
						{
							d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",
							key: "r04s7s",
						},
					],
				]),
				A = (e) => {
					const {
						showModal: t,
						selectedCollaboration: a,
						reviewForm: i,
						onClose: l,
						onFormChange: r,
						onSubmit: o,
					} = e;
					return t && a
						? (0, s.jsx)("div", {
								className:
									"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
								children: (0, s.jsxs)("div", {
									className:
										"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
									children: [
										(0, s.jsxs)("div", {
											className:
												"flex items-center justify-between p-6 border-b border-gray-100",
											children: [
												(0, s.jsx)("h2", {
													className: "text-xl font-light tracking-wide",
													children: "Review Artist",
												}),
												(0, s.jsx)("button", {
													onClick: l,
													children: (0, s.jsx)(S.A, { className: "w-5 h-5" }),
												}),
											],
										}),
										(0, s.jsxs)("form", {
											onSubmit: o,
											className: "p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
											children: [
												(0, s.jsxs)("div", {
													className: "space-y-6",
													children: [
														(0, s.jsxs)("div", {
															className: "flex items-center gap-4",
															children: [
																(0, s.jsx)(w.default, {
																	src: a.artistAvatar || "/placeholder.svg",
																	alt: a.artistName,
																	width: 60,
																	height: 60,
																	className: "rounded-full object-cover",
																}),
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className: "font-light text-lg",
																			children: a.artistName,
																		}),
																		(0, s.jsx)("p", {
																			className: "text-gray-600 font-light",
																			children: a.projectTitle,
																		}),
																	],
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Rating *",
																}),
																(0, s.jsx)("div", {
																	className: "flex gap-2",
																	children: [1, 2, 3, 4, 5].map((e) =>
																		(0, s.jsx)(
																			"button",
																			{
																				type: "button",
																				onClick: () => r("rating", e),
																				className: "focus:outline-none",
																				children: (0, s.jsx)(P, {
																					className: "w-8 h-8 ".concat(
																						e <= i.rating
																							? "fill-yellow-400 text-yellow-400"
																							: "text-gray-300 hover:text-yellow-300",
																						" transition-colors",
																					),
																				}),
																			},
																			e,
																		),
																	),
																}),
															],
														}),
														(0, s.jsxs)("div", {
															children: [
																(0, s.jsx)("label", {
																	className:
																		"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																	children: "Review",
																}),
																(0, s.jsx)("textarea", {
																	rows: 4,
																	value: i.review,
																	onChange: (e) => r("review", e.target.value),
																	className:
																		"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	placeholder:
																		"Share your experience working with this creative...",
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													className:
														"flex gap-4 mt-8 pt-6 border-t border-gray-100",
													children: [
														(0, s.jsx)(n.$, {
															type: "button",
															variant: "outline",
															onClick: l,
															className: "flex-1 font-light tracking-wide",
															children: "Cancel",
														}),
														(0, s.jsx)(n.$, {
															type: "submit",
															className:
																"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
															children: "Save Review",
														}),
													],
												}),
											],
										}),
									],
								}),
							})
						: null;
				},
				D = (0, l.A)("Trash2", [
					["path", { d: "M3 6h18", key: "d0wm0j" }],
					[
						"path",
						{ d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6", key: "4alrt4" },
					],
					["path", { d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2", key: "v07s0e" }],
					["line", { x1: "10", x2: "10", y1: "11", y2: "17", key: "1uufr5" }],
					["line", { x1: "14", x2: "14", y1: "11", y2: "17", key: "xtxkd" }],
				]),
				L = (0, l.A)("Plus", [
					["path", { d: "M5 12h14", key: "1ays0h" }],
					["path", { d: "M12 5v14", key: "s699le" }],
				]),
				z = [
					{
						id: "pm_1",
						type: "Visa",
						last4: "1234",
						expiry: "08/25",
						isDefault: !0,
					},
					{
						id: "pm_2",
						type: "Mastercard",
						last4: "5678",
						expiry: "12/26",
						isDefault: !1,
					},
					{
						id: "pm_3",
						type: "Amex",
						last4: "9012",
						expiry: "03/24",
						isDefault: !1,
					},
				],
				E = (e) => {
					const { showModal: t, onClose: a } = e;
					return t
						? (0, s.jsx)("div", {
								className:
									"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
								children: (0, s.jsxs)("div", {
									className:
										"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
									children: [
										(0, s.jsxs)("div", {
											className:
												"flex items-center justify-between p-6 border-b border-gray-100",
											children: [
												(0, s.jsx)("h2", {
													className: "text-xl font-light tracking-wide",
													children: "Manage Payment Methods",
												}),
												(0, s.jsx)("button", {
													onClick: a,
													children: (0, s.jsx)(S.A, { className: "w-5 h-5" }),
												}),
											],
										}),
										(0, s.jsxs)("div", {
											className: "p-6 overflow-y-auto max-h-[calc(90vh-180px)]",
											children: [
												" ",
												(0, s.jsxs)("div", {
													className: "space-y-4 mb-6",
													children: [
														z.map((e) =>
															(0, s.jsxs)(
																"div",
																{
																	className:
																		"border border-gray-200 rounded p-4 flex items-center justify-between hover:shadow-sm transition-shadow",
																	children: [
																		(0, s.jsxs)("div", {
																			className: "flex items-center gap-3",
																			children: [
																				(0, s.jsx)(r, {
																					className: "w-6 h-6 ".concat(
																						e.isDefault
																							? "text-blue-600"
																							: "text-gray-500",
																					),
																				}),
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsxs)("span", {
																							className:
																								"font-light tracking-wide text-sm text-gray-800",
																							children: [
																								e.type,
																								" ending in ",
																								e.last4,
																							],
																						}),
																						(0, s.jsxs)("span", {
																							className:
																								"block text-xs text-gray-500 font-light",
																							children: ["Expires ", e.expiry],
																						}),
																					],
																				}),
																				e.isDefault &&
																					(0, s.jsx)("span", {
																						className:
																							"px-2 py-0.5 text-xs font-light tracking-wide bg-blue-100 text-blue-700 rounded-full",
																						children: "Default",
																					}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className: "flex items-center gap-2",
																			children: [
																				!e.isDefault &&
																					(0, s.jsx)(n.$, {
																						variant: "outline",
																						size: "sm",
																						className:
																							"text-xs font-light tracking-wide",
																						children: "Set as Default",
																					}),
																				(0, s.jsx)(n.$, {
																					variant: "ghost",
																					size: "sm",
																					className:
																						"text-red-500 hover:text-red-700",
																					children: (0, s.jsx)(D, {
																						className: "w-4 h-4",
																					}),
																				}),
																			],
																		}),
																	],
																},
																e.id,
															),
														),
														0 === z.length &&
															(0, s.jsx)("p", {
																className:
																	"text-center text-gray-500 font-light py-8",
																children: "No payment methods saved.",
															}),
													],
												}),
											],
										}),
										(0, s.jsx)("div", {
											className:
												"p-6 border-t border-gray-100 flex justify-end",
											children: (0, s.jsxs)(n.$, {
												className:
													"bg-black text-white hover:bg-gray-800 font-light tracking-wide",
												children: [
													(0, s.jsx)(L, { className: "w-4 h-4 mr-2" }),
													"Add New Card",
												],
											}),
										}),
									],
								}),
							})
						: null;
				};
			var B = a(9805);
			const I = (e) => {
					const {
						projects: t,
						selectedTiles: a,
						isSelectionMode: i,
						editingTile: l,
						tempText: r,
						onTileClick: o,
						onStartEditingText: c,
						onSetTempText: d,
						onSaveTextEdit: x,
						onCancelTextEdit: h,
						onSplitTile: g,
					} = e;
					return (0, s.jsx)("div", {
						className: "grid grid-cols-3 gap-2",
						children: t.map((e) =>
							(0, s.jsxs)(
								"div",
								{
									className: "relative group cursor-pointer aspect-[3/4] "
										.concat(a.has(e.id) ? "ring-2 ring-black" : "", " ")
										.concat(i ? "hover:ring-1 hover:ring-gray-400" : ""),
									onClick: () => o(e),
									children: [
										"text" === e.type
											? (0, s.jsx)("div", {
													className:
														"w-full h-full flex items-center justify-center p-4 text-center relative",
													style: {
														backgroundColor: e.backgroundColor || "#f3f4f6",
													},
													children:
														l === e.id
															? (0, s.jsxs)("div", {
																	className: "w-full h-full flex flex-col",
																	onClick: (e) => e.stopPropagation(),
																	children: [
																		(0, s.jsx)("textarea", {
																			value: r,
																			onChange: (e) => d(e.target.value),
																			className:
																				"flex-1 w-full p-2 border-none outline-none resize-none bg-transparent text-center font-light",
																			placeholder: "Enter your text...",
																			autoFocus: !0,
																			onClick: (e) => e.stopPropagation(),
																		}),
																		(0, s.jsxs)("div", {
																			className: "flex gap-1 mt-2",
																			children: [
																				(0, s.jsx)("button", {
																					onClick: (e) => {
																						e.stopPropagation(), x();
																					},
																					className:
																						"px-2 py-1 bg-black text-white text-xs rounded font-light",
																					children: "Save",
																				}),
																				(0, s.jsx)("button", {
																					onClick: (e) => {
																						e.stopPropagation(), h();
																					},
																					className:
																						"px-2 py-1 bg-gray-500 text-white text-xs rounded font-light",
																					children: "Cancel",
																				}),
																			],
																		}),
																	],
																})
															: (0, s.jsx)("p", {
																	className:
																		"text-gray-800 font-light break-words cursor-text z-20 relative whitespace-pre-line",
																	onClick: (t) => {
																		t.stopPropagation(),
																			i || c(e.id, e.textContent || "");
																	},
																	children:
																		e.textContent || "Click to edit text",
																}),
												})
											: (0, s.jsx)(w.default, {
													src: e.src || "/placeholder.svg",
													alt: e.alt,
													fill: !0,
													className: "object-cover",
													sizes:
														"(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw",
												}),
										(0, s.jsxs)("div", {
											className:
												"absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm p-3 border-t border-gray-100",
											children: [
												(0, s.jsx)("h3", {
													className:
														"font-light text-sm tracking-wide mb-1 truncate",
													children: e.title,
												}),
												(0, s.jsx)("p", {
													className:
														"text-xs text-gray-600 mb-1 truncate font-light",
													children: e.client,
												}),
												(0, s.jsxs)("div", {
													className:
														"flex items-center justify-between text-xs text-gray-500",
													children: [
														(0, s.jsx)("span", {
															className: "truncate font-light",
															children: e.projectType || "Creative Project",
														}),
														(0, s.jsxs)("span", {
															className: "font-light text-black",
															children: ["$", (e.budget / 1e3).toFixed(0), "K"],
														}),
													],
												}),
												e.location &&
													(0, s.jsx)("p", {
														className:
															"text-xs text-gray-500 mt-1 truncate font-light",
														children: e.location,
													}),
											],
										}),
										(0, s.jsx)("div", {
											className: "absolute top-2 right-2 flex gap-1 z-20",
											children: (0, s.jsx)("div", {
												className:
													"px-2 py-1 text-xs font-light tracking-wide rounded ".concat(
														"active" === e.status
															? "bg-blue-100 text-blue-800"
															: "completed" === e.status
																? "bg-green-100 text-green-800"
																: "bg-yellow-100 text-yellow-800",
													),
												children:
													e.status.charAt(0).toUpperCase() + e.status.slice(1),
											}),
										}),
										(0, s.jsxs)("div", {
											className: "absolute top-2 left-2 flex gap-1 z-20",
											children: [
												e.isMerged &&
													(0, s.jsx)("div", {
														className: "bg-black text-white rounded-full p-1",
														title: "Merged Tile",
														children: (0, s.jsx)(B.A, { className: "w-3 h-3" }),
													}),
												"text" === e.type &&
													(0, s.jsx)("div", {
														className: "bg-black text-white rounded-full p-1",
														title: "Text Tile",
														children: (0, s.jsx)("span", {
															className: "text-xs font-light",
															children: "T",
														}),
													}),
											],
										}),
										("text" !== e.type || i) &&
											(0, s.jsx)("div", {
												className:
													"absolute inset-0 bottom-20 bg-black/0 hover:bg-black/30 transition-all duration-200 flex items-center justify-center opacity-0 ".concat(
														l === e.id
															? "opacity-0 pointer-events-none"
															: "group-hover:opacity-100",
														" z-10",
													),
												children: i
													? (0, s.jsx)("div", {
															className:
																"text-white font-light text-lg drop-shadow-lg tracking-wide",
															children: a.has(e.id) ? "✓ Selected" : "Select",
														})
													: (0, s.jsxs)("div", {
															className:
																"flex flex-col items-center gap-2 text-white",
															children: [
																(0, s.jsxs)("div", {
																	className: "text-center",
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"font-light text-lg drop-shadow-lg",
																			children: e.title,
																		}),
																		(0, s.jsxs)("p", {
																			className:
																				"text-sm drop-shadow-lg font-light",
																			children: [
																				"$",
																				e.budget.toLocaleString(),
																			],
																		}),
																	],
																}),
																e.isMerged &&
																	(0, s.jsx)(n.$, {
																		size: "sm",
																		variant: "secondary",
																		onClick: (t) => g(e, t),
																		className:
																			"bg-white/80 hover:bg-white text-black text-xs font-light tracking-wide",
																		children: "Split",
																	}),
															],
														}),
											}),
									],
								},
								e.id,
							),
						),
					});
				},
				$ = (e) => {
					const { offers: t } = e;
					return 0 === t.length
						? (0, s.jsx)("div", {
								className: "text-center py-12",
								children: (0, s.jsx)("p", {
									className: "text-gray-500 font-light tracking-wide",
									children: "No offers have been sent yet.",
								}),
							})
						: (0, s.jsxs)("div", {
								className: "space-y-6",
								children: [
									(0, s.jsx)("h2", {
										className:
											"text-xl font-light tracking-wide text-center mb-12",
										children: "Offers Sent to creatives",
									}),
									t.map((e) =>
										(0, s.jsx)(
											"div",
											{
												className:
													"border border-gray-100 p-6 hover:shadow-sm transition-shadow",
												children: (0, s.jsxs)("div", {
													className: "flex items-start justify-between",
													children: [
														(0, s.jsxs)("div", {
															className: "flex-1",
															children: [
																(0, s.jsx)("h3", {
																	className:
																		"font-light tracking-wide text-lg mb-2",
																	children: e.projectTitle,
																}),
																(0, s.jsxs)("p", {
																	className:
																		"text-gray-600 mb-2 font-light tracking-wide",
																	children: [
																		"Artist:",
																		(0, s.jsx)(C(), {
																			href: "/creative/".concat(e.artistId),
																			className:
																				"text-blue-600 hover:text-blue-800 underline ml-1",
																			children: e.artistName,
																		}),
																	],
																}),
																(0, s.jsxs)("div", {
																	className:
																		"flex items-center gap-6 text-sm text-gray-500 font-light tracking-wide",
																	children: [
																		(0, s.jsx)("span", {
																			children: e.projectType,
																		}),
																		(0, s.jsxs)("span", {
																			children: [
																				"$",
																				e.amount.toLocaleString(),
																			],
																		}),
																		(0, s.jsx)("span", {
																			children: e.location,
																		}),
																		(0, s.jsx)("span", { children: e.date }),
																	],
																}),
															],
														}),
														(0, s.jsx)("div", {
															className: "text-right",
															children: (0, s.jsx)("span", {
																className:
																	"px-3 py-1 text-xs font-light tracking-wide ".concat(
																		"accepted" === e.status
																			? "bg-green-100 text-green-800"
																			: "declined" === e.status
																				? "bg-red-100 text-red-800"
																				: "expired" === e.status
																					? "bg-gray-100 text-gray-800"
																					: "bg-yellow-100 text-yellow-800",
																	),
																children:
																	e.status.charAt(0).toUpperCase() +
																	e.status.slice(1),
															}),
														}),
													],
												}),
											},
											e.id,
										),
									),
								],
							});
				};
			var R = a(6752);
			const _ = (e) => {
				const {
						totalBudget: t = 0,
						depositedBudget: a = 0,
						budgetLeft: i = 0,
						projects: l = [],
						transactions: r = [],
					} = e,
					n = (e) => {
						switch (e) {
							case "deposit":
								return "text-green-600";
							case "payment":
							case "fee":
								return "text-red-600";
							case "refund":
								return "text-blue-600";
							default:
								return "text-gray-800";
						}
					},
					o = (e) => {
						if ("payment" === e.type && e.creativeName) {
							let t = "Paid to: ".concat(e.creativeName);
							return e.projectName && (t += " for ".concat(e.projectName)), t;
						}
						return e.description;
					};
				return (0, s.jsxs)("div", {
					className: "py-8",
					children: [
						(0, s.jsxs)("div", {
							className:
								"grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-16 px-4",
							children: [
								(0, s.jsxs)("div", {
									children: [
										(0, s.jsx)("h2", {
											className:
												"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
											children: "Deposited Budget",
										}),
										(0, s.jsxs)("div", {
											className: "text-4xl font-light text-green-600 mb-2",
											children: ["$", (a || 0).toLocaleString()],
										}),
									],
								}),
								(0, s.jsxs)("div", {
									children: [
										(0, s.jsx)("h2", {
											className:
												"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
											children: "Total Allocated",
										}),
										(0, s.jsxs)("div", {
											className: "text-4xl font-light text-blue-600 mb-2",
											children: ["$", (t || 0).toLocaleString()],
										}),
										(0, s.jsx)("p", {
											className:
												"text-gray-500 text-sm font-light tracking-wide",
											children: "Across All Projects",
										}),
									],
								}),
								(0, s.jsxs)("div", {
									children: [
										(0, s.jsx)("h2", {
											className:
												"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
											children: "Budget Left",
										}),
										(0, s.jsxs)("div", {
											className: "text-4xl font-light text-orange-500 mb-2",
											children: ["$", (i || 0).toLocaleString()],
										}),
									],
								}),
							],
						}),
						l.length > 0 &&
							(0, s.jsxs)("div", {
								className: "mb-16 px-4",
								children: [
									(0, s.jsx)("h3", {
										className:
											"text-2xl font-light tracking-wide mb-6 text-center text-gray-700",
										children: "Project Allocations",
									}),
									(0, s.jsx)("div", {
										className: "bg-white shadow overflow-hidden rounded-lg",
										children: (0, s.jsx)("ul", {
											className: "divide-y divide-gray-200",
											children: l.map((e) =>
												(0, s.jsx)(
													"li",
													{
														className:
															"px-6 py-4 hover:bg-gray-50 transition-colors",
														children: (0, s.jsxs)("div", {
															className: "flex items-center justify-between",
															children: [
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("p", {
																			className:
																				"text-md font-medium text-gray-800 truncate",
																			children: e.name,
																		}),
																		e.status &&
																			(0, s.jsxs)("p", {
																				className: "text-xs text-gray-500",
																				children: ["Status: ", e.status],
																			}),
																	],
																}),
																(0, s.jsxs)("div", {
																	className: "text-md text-blue-600 font-light",
																	children: [
																		"$",
																		e.allocatedBudget.toLocaleString(),
																	],
																}),
															],
														}),
													},
													e.id,
												),
											),
										}),
									}),
								],
							}),
						r.length > 0 &&
							(0, s.jsxs)("div", {
								className: "px-4 mt-16",
								children: [
									(0, s.jsx)("h3", {
										className:
											"text-2xl font-light tracking-wide mb-6 text-center text-gray-700",
										children: "Payment History",
									}),
									(0, s.jsx)("div", {
										className: "bg-white shadow overflow-hidden rounded-lg",
										children: (0, s.jsxs)("table", {
											className: "min-w-full divide-y divide-gray-200",
											children: [
												(0, s.jsx)("thead", {
													className: "bg-gray-50",
													children: (0, s.jsxs)("tr", {
														children: [
															(0, s.jsx)("th", {
																scope: "col",
																className:
																	"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
																children: "Date",
															}),
															(0, s.jsx)("th", {
																scope: "col",
																className:
																	"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
																children: "Details",
															}),
															(0, s.jsx)("th", {
																scope: "col",
																className:
																	"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
																children: "Amount",
															}),
														],
													}),
												}),
												(0, s.jsx)("tbody", {
													className: "bg-white divide-y divide-gray-200",
													children: r.map((e) =>
														(0, s.jsxs)(
															"tr",
															{
																className: "hover:bg-gray-50 transition-colors",
																children: [
																	(0, s.jsx)("td", {
																		className:
																			"px-6 py-4 whitespace-nowrap text-sm text-gray-500",
																		children: (0, R.GP)(
																			new Date(e.date),
																			"MMM dd, yyyy",
																		),
																	}),
																	(0, s.jsx)("td", {
																		className:
																			"px-6 py-4 text-sm text-gray-800",
																		children: o(e),
																	}),
																	(0, s.jsxs)("td", {
																		className:
																			"px-6 py-4 whitespace-nowrap text-sm text-right font-medium ".concat(
																				n(e.type),
																			),
																		children: [
																			"deposit" === e.type ||
																			"refund" === e.type
																				? "+"
																				: "-",
																			"$",
																			Math.abs(e.amount).toLocaleString(),
																		],
																	}),
																],
															},
															e.id,
														),
													),
												}),
											],
										}),
									}),
								],
							}),
						0 === l.length &&
							0 === r.length &&
							(0, s.jsx)("div", {
								className: "text-center text-gray-400 font-light mt-12 px-4",
								children: (0, s.jsx)("p", {
									children:
										"No project allocations or transaction history to display.",
								}),
							}),
					],
				});
			};
			var O = a(2950);
			const V = (0, l.A)("PenLine", [
				["path", { d: "M12 20h9", key: "t2du7b" }],
				[
					"path",
					{
						d: "M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",
						key: "1ykcvy",
					},
				],
			]);
			var F = a(900),
				U = a(5904);
			const H = "__ALL_TYPES__",
				W = (e) => {
					const { collaborations: t, onOpenReviewModal: a } = e,
						[l, r] = (0, i.useState)(""),
						[o, c] = (0, i.useState)(""),
						d = (0, i.useMemo)(
							() =>
								Array.from(
									new Set(
										t
											.map((e) => e.projectType)
											.filter((e) => e && "" !== e.trim()),
									),
								).sort(),
							[t],
						),
						x = t.filter((e) => {
							const t = l.toLowerCase(),
								a =
									"" === t ||
									e.projectTitle.toLowerCase().includes(t) ||
									e.artistName.toLowerCase().includes(t),
								s = "" === o || e.projectType === o;
							return a && s;
						});
					return 0 === t.length
						? (0, s.jsx)("div", {
								className: "text-center py-12",
								children: (0, s.jsx)("p", {
									className: "text-gray-500 font-light tracking-wide",
									children: "No past collaborations to display.",
								}),
							})
						: (0, s.jsxs)("div", {
								children: [
									(0, s.jsx)("h2", {
										className:
											"text-xl font-light tracking-wide text-center mb-6",
										children: "Past Collaborations",
									}),
									(0, s.jsxs)("div", {
										className:
											"mb-8 max-w-xl mx-auto flex flex-col md:flex-row gap-4 items-center",
										children: [
											(0, s.jsxs)("div", {
												className: "relative flex-grow w-full md:w-auto",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
														children: (0, s.jsx)(O.A, {
															className: "h-4 w-4 text-gray-400",
														}),
													}),
													(0, s.jsx)(F.p, {
														type: "text",
														placeholder: "Search by project or creative...",
														value: l,
														onChange: (e) => r(e.target.value),
														className:
															"w-full pl-10 pr-4 py-2 h-10 border-gray-200 rounded-md font-light focus:border-gray-300 focus:ring-0",
													}),
												],
											}),
											(0, s.jsx)("div", {
												className: "w-full md:w-auto md:min-w-[200px]",
												children: (0, s.jsxs)(U.l6, {
													value: o,
													onValueChange: (e) => {
														e === H ? c("") : c(e);
													},
													children: [
														(0, s.jsx)(U.bq, {
															className:
																"w-full h-10 border-gray-200 rounded-md font-light focus:ring-0",
															children: (0, s.jsx)(U.yv, {
																placeholder: "Filter by type...",
															}),
														}),
														(0, s.jsxs)(U.gC, {
															children: [
																(0, s.jsx)(U.eb, {
																	value: H,
																	className: "font-light",
																	children: "All Types",
																}),
																d.map((e) =>
																	(0, s.jsx)(
																		U.eb,
																		{
																			value: e,
																			className: "font-light",
																			children: e,
																		},
																		e,
																	),
																),
															],
														}),
													],
												}),
											}),
										],
									}),
									0 === x.length &&
										(l || o) &&
										(0, s.jsx)("div", {
											className: "text-center py-12",
											children: (0, s.jsx)("p", {
												className: "text-gray-500 font-light tracking-wide",
												children:
													"No collaborations found matching your criteria.",
											}),
										}),
									(0, s.jsx)("div", {
										className:
											"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",
										children: x.map((e) =>
											(0, s.jsxs)(
												"div",
												{
													className:
														"group bg-white rounded-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300 ease-in-out flex flex-col overflow-hidden",
													children: [
														(0, s.jsx)("div", {
															className:
																"relative aspect-[3/4] w-full overflow-hidden bg-gray-50",
															children: (0, s.jsx)(w.default, {
																src: e.artistAvatar || "/placeholder.svg",
																alt: e.artistName,
																fill: !0,
																className:
																	"object-cover transition-transform duration-300 group-hover:scale-105",
															}),
														}),
														(0, s.jsxs)("div", {
															className: "p-4 flex flex-col flex-grow",
															children: [
																(0, s.jsx)("h3", {
																	className:
																		"text-base font-light tracking-wide text-gray-800 mb-1 truncate",
																	children: e.projectTitle,
																}),
																(0, s.jsxs)("p", {
																	className:
																		"text-sm text-gray-500 font-light mb-2",
																	children: [
																		"Artist: ",
																		(0, s.jsx)("span", {
																			className: "text-gray-700",
																			children: e.artistName,
																		}),
																	],
																}),
																(0, s.jsxs)("div", {
																	className:
																		"text-xs text-gray-400 font-light mb-3 space-y-0.5",
																	children: [
																		(0, s.jsxs)("p", {
																			children: ["Type: ", e.projectType],
																		}),
																		(0, s.jsxs)("p", {
																			children: [
																				"Budget: $",
																				e.budget.toLocaleString(),
																			],
																		}),
																		(0, s.jsxs)("p", {
																			children: [
																				"Completed: ",
																				e.completedDate,
																			],
																		}),
																	],
																}),
																(0, s.jsxs)("div", {
																	className: "flex items-center gap-1 mb-2",
																	children: [
																		[1, 2, 3, 4, 5].map((t) =>
																			(0, s.jsx)(
																				P,
																				{
																					className: "w-3.5 h-3.5 ".concat(
																						t <= (e.rating || 0)
																							? "fill-yellow-400 text-yellow-400"
																							: "text-gray-300",
																					),
																				},
																				t,
																			),
																		),
																		e.rating
																			? (0, s.jsxs)("span", {
																					className:
																						"text-xs text-gray-500 ml-1 font-light",
																					children: ["(", e.rating, "/5)"],
																				})
																			: (0, s.jsx)("span", {
																					className:
																						"text-xs text-gray-400 font-light",
																					children: "Not rated",
																				}),
																	],
																}),
																e.review &&
																	(0, s.jsxs)("p", {
																		className:
																			"text-xs text-gray-600 italic font-light mb-3 flex-grow leading-relaxed line-clamp-3",
																		children: ['"', e.review, '"'],
																	}),
																!e.review &&
																	(0, s.jsx)("div", {
																		className: "flex-grow min-h-[2rem]",
																	}),
																(0, s.jsx)("div", {
																	className: "mt-auto pt-3",
																	children: (0, s.jsxs)(n.$, {
																		variant: "outline",
																		size: "sm",
																		onClick: () => a(e),
																		className:
																			"w-full text-xs font-light tracking-wide hover:border-gray-400",
																		children: [
																			(0, s.jsx)(V, {
																				className: "w-3 h-3 mr-1.5",
																			}),
																			e.review || e.rating
																				? "Edit Review"
																				: "Add Review",
																		],
																	}),
																}),
															],
														}),
													],
												},
												e.id,
											),
										),
									}),
								],
							});
				};
			var q = a(5993),
				G = a(6425);
			const J = (e) => {
					const { members: t, onInviteMember: a } = e;
					return (0, s.jsxs)("div", {
						className: "py-8",
						children: [
							(0, s.jsxs)("div", {
								className:
									"flex justify-between items-center mb-6 px-4 md:px-0",
								children: [
									(0, s.jsx)("h2", {
										className:
											"text-2xl font-light tracking-wide text-gray-700",
										children: "Members",
									}),
									(0, s.jsxs)(n.$, {
										variant: "outline",
										size: "sm",
										onClick: a,
										className: "text-xs font-light tracking-wide",
										children: [
											(0, s.jsx)(G.A, { className: "w-4 h-4 mr-2" }),
											"+ Member",
										],
									}),
								],
							}),
							t.length > 0
								? (0, s.jsx)("ul", {
										className: "space-y-2",
										children: t.map((e) =>
											(0, s.jsxs)(
												"li",
												{
													className:
														"p-3 border border-gray-200 rounded-md text-sm bg-white shadow-sm hover:shadow-md transition-shadow",
													children: [
														(0, s.jsx)("span", {
															className: "font-medium text-gray-800",
															children: e.name,
														}),
														" - ",
														(0, s.jsx)("span", {
															className: "capitalize text-gray-600",
															children: e.permission,
														}),
													],
												},
												e.id,
											),
										),
									})
								: (0, s.jsxs)("div", {
										className: "text-center text-gray-500 font-light py-10",
										children: [
											(0, s.jsx)("p", {
												children:
													"No members have been added to this booker profile yet.",
											}),
											(0, s.jsxs)(n.$, {
												variant: "default",
												size: "sm",
												onClick: a,
												className: "mt-4 text-xs font-light tracking-wide",
												children: [
													(0, s.jsx)(G.A, { className: "w-4 h-4 mr-2" }),
													"Invite First Member",
												],
											}),
										],
									}),
						],
					});
				},
				K = [
					{
						id: "m1",
						name: "Alice Wonderland",
						avatarUrl: "/avatars/alice.png",
						permission: "admin",
					},
					{
						id: "m2",
						name: "Bob The Builder",
						avatarUrl: "/avatars/bob.png",
						permission: "editor",
					},
					{
						id: "m3",
						name: "Charlie Chaplin",
						avatarUrl: "/avatars/charlie.png",
						permission: "viewer",
					},
					{ id: "m4", name: "Diana Prince", permission: "viewer" },
				];
			function Z(e) {
				const { bookerId: t = "1" } = e,
					a = (0, o.useRouter)(),
					[l, m] = (0, i.useState)(c.R4),
					[u, b] = (0, i.useState)(new Set()),
					[w, k] = (0, i.useState)("projects"),
					[C, S] = (0, i.useState)(K),
					{
						selectedProject: P,
						openProjectModal: D,
						closeProjectModal: L,
						showPaymentModal: z,
						openPaymentModal: B,
						closePaymentModal: R,
					} = x(),
					{
						selectedTiles: O,
						isSelectionMode: V,
						toggleTileSelection: F,
						clearSelection: U,
						mergeTiles: H,
						splitTile: G,
						convertToTextTile: Z,
						convertToImageTile: Y,
					} = h({ projects: l, setProjects: m }),
					{
						editingTile: X,
						tempText: Q,
						startEditingText: ee,
						handleSetTempText: et,
						saveTextEdit: ea,
						cancelTextEdit: es,
					} = g({ setProjects: m }),
					{
						showReviewModal: ei,
						selectedCollaboration: el,
						reviewForm: er,
						openReviewModal: en,
						closeReviewModal: eo,
						updateReviewFormField: ec,
						handleReviewSubmit: ed,
					} = p(),
					{
						showAddProjectModal: ex,
						editingProject: eh,
						newProjectForm: eg,
						openAddProjectModal: em,
						closeAddProjectModal: ep,
						updateNewProjectFormField: eu,
						handleAddProjectSubmit: ef,
						addArtistRoleToForm: ej,
						removeArtistRoleFromForm: ey,
					} = f({ projects: l, setProjects: m }),
					eb = c.gL.reduce((e, t) => e + t.amount, 0),
					ev = c.Qh[t] || c.Qh["1"],
					eN = d.R4.map((e) => ({
						id: e.id,
						name: e.title,
						allocatedBudget: e.budget,
						status: "Active",
					})),
					ew = d.KT.map((e) => ({
						id: e.id,
						date: e.date,
						description: e.jobTitle,
						amount: e.amount,
						type: e.status,
						creativeName: void 0,
						projectName: void 0,
					})),
					ek = eN.reduce((e, t) => e + t.allocatedBudget, 0);
				return (0, s.jsxs)("div", {
					className: "min-h-screen bg-white font-light",
					children: [
						(0, s.jsx)(j.A, {}),
						(0, s.jsx)("div", {
							className: "border-b border-gray-100",
							children: (0, s.jsxs)(q.A, {
								children: [
									(0, s.jsx)(y, {
										name: ev.name,
										type: ev.type,
										projectCount: l.length,
										offersSentCount: c.Ex.length,
										collaborationsCount: c.Zd.length,
										membersCount: C.length,
									}),
									(0, s.jsx)(v, { activeTab: w }),
									"budget" === w &&
										(0, s.jsx)("div", {
											className: "text-center",
											children: (0, s.jsxs)(n.$, {
												onClick: B,
												className:
													"bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide",
												children: [
													(0, s.jsx)(r, { className: "w-4 h-4 mr-2" }),
													"Manage Payment Methods",
												],
											}),
										}),
								],
							}),
						}),
						(0, s.jsx)(N, {
							activeTab: w,
							onTabChange: k,
							projectCount: l.length,
							offersSentCount: c.Ex.length,
							totalBudgetFormatted: "$".concat(eb.toLocaleString()),
							archiveCount: c.Zd.length,
							membersCount: C.length,
							isSelectionMode: V,
							onOpenAddProjectModal: em,
							onMergeTiles: H,
							onClearSelection: U,
							onConvertToText: () => {
								Array.from(O).forEach(Z), U();
							},
							onConvertToImage: () => {
								Array.from(O).forEach(Y), U();
							},
							selectedTilesCount: O.size,
						}),
						(0, s.jsx)(q.A, {
							children:
								"projects" === w
									? (0, s.jsx)(I, {
											projects: l,
											selectedTiles: O,
											isSelectionMode: V,
											editingTile: X,
											tempText: Q,
											onTileClick: (e) => {
												if ("text" === e.type && X !== e.id && !V) {
													ee(e.id, e.textContent || "");
													return;
												}
												V ? F(e.id) : a.push("/booker/project/".concat(e.id));
											},
											onStartEditingText: ee,
											onSetTempText: et,
											onSaveTextEdit: ea,
											onCancelTextEdit: es,
											onSplitTile: G,
										})
									: "offers" === w
										? (0, s.jsx)($, { offers: c.Ex })
										: "budget" === w
											? (0, s.jsx)(_, {
													totalBudget: ek,
													depositedBudget: 15e4,
													budgetLeft: 15e4 - ek,
													projects: eN,
													transactions: ew,
												})
											: "members" === w
												? (0, s.jsx)(J, {
														members: C,
														onInviteMember: () => {
															console.log(
																"Invite new member button clicked. TODO: Implement invitation modal.",
															),
																alert(
																	"Placeholder: Invite new member functionality to be implemented.",
																);
														},
													})
												: (0, s.jsx)(W, {
														collaborations: c.Zd,
														onOpenReviewModal: en,
													}),
						}),
						P &&
							(0, s.jsx)(M, {
								selectedProject: P,
								offersSent: c.Ex,
								onClose: L,
								onEditProject: (e) => {
									em(e), L();
								},
							}),
						(0, s.jsx)(T, {
							showModal: ex,
							onClose: ep,
							formData: eg,
							onFormChange: eu,
							onSubmit: ef,
							onAddArtistRole: ej,
							onRemoveArtistRole: ey,
							isEditing: !!eh,
						}),
						(0, s.jsx)(A, {
							showModal: ei,
							selectedCollaboration: el,
							reviewForm: er,
							onClose: eo,
							onFormChange: ec,
							onSubmit: ed,
						}),
						(0, s.jsx)(E, { showModal: z, onClose: R }),
					],
				});
			}
		},
		9805: (e, t, a) => {
			a.d(t, { A: () => s });
			const s = (0, a(1018).A)("Scissors", [
				["circle", { cx: "6", cy: "6", r: "3", key: "1lh9wr" }],
				["path", { d: "M8.12 8.12 12 12", key: "1alkpv" }],
				["path", { d: "M20 4 8.12 15.88", key: "xgtan2" }],
				["circle", { cx: "6", cy: "18", r: "3", key: "fqmcym" }],
				["path", { d: "M14.8 14.8 20 20", key: "ptml3r" }],
			]);
		},
	},
	(e) => {
		var t = (t) => e((e.s = t));
		e.O(0, [874, 637, 186, 398, 752, 285, 497, 954, 358], () => t(5180)),
			(_N_E = e.O());
	},
]);
