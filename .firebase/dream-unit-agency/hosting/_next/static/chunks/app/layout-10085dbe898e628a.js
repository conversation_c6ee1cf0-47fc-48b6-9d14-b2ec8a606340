(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[177],
	{
		6146: () => {},
		6990: (e, r, a) => {
			a.r(r), a.d(r, { default: () => m });
			var i = a(8081);
			a(6146);
			var n = a(1737),
				s = a(2783),
				c = a(6003),
				t = a(8357),
				l = a(1400),
				d = a(5029),
				o = a(5036);
			const h = {
				apiKey: "AIzaSyAR2vW70A7yX95L1RSUi2hRSuh9HU0FXqM",
				authDomain: "dream-unit-agency.firebaseapp.com",
				projectId: "dream-unit-agency",
				storageBucket: "dream-unit-agency.firebasestorage.app",
				messagingSenderId: "364954502659",
				appId: "1:364954502659:web:c0ea1247be97d8fdf13036",
			};
			o.env.NEXT_PUBLIC_USE_EMULATORS;
			const u = (e) => {
					const { children: r } = e,
						a = (0, s.sh)(),
						n = (0, c.xI)(a),
						o = (0, t.aU)(a),
						h = (0, l.Uz)(a),
						u = (0, d.c7)(a);
					return (0, i.jsx)(s.OJ, {
						sdk: n,
						children: (0, i.jsx)(s.z_, {
							sdk: o,
							children: (0, i.jsx)(s.VB, {
								sdk: h,
								children: (0, i.jsx)(s.Lg, { sdk: u, children: r }),
							}),
						}),
					});
				},
				p = (e) => {
					const { children: r } = e;
					return h &&
						h.apiKey &&
						"YOUR_API_KEY" !== h.apiKey &&
						h.projectId &&
						"YOUR_PROJECT_ID" !== h.projectId
						? (0, i.jsx)(s.qB, {
								firebaseConfig: h,
								children: (0, i.jsx)(u, { children: r }),
							})
						: (console.warn(
								"Firebase configuration is incomplete or using placeholder values. Ensure your .env.local file is set up with your Firebase project's credentials. Firebase services may not initialize correctly. The app might rely on mock data mechanisms.",
							),
							(0, i.jsx)(i.Fragment, { children: r }));
				};
			function m(e) {
				const { children: r } = e;
				return (0, i.jsx)("html", {
					lang: "en",
					children: (0, i.jsx)("body", {
						children: (0, i.jsx)(p, {
							children: (0, i.jsx)(n.NP, {
								defaultTheme: n.D7.LIGHT,
								children: r,
							}),
						}),
					}),
				});
			}
		},
		7745: (e, r, a) => {
			Promise.resolve().then(a.bind(a, 6990));
		},
	},
	(e) => {
		var r = (r) => e((e.s = r));
		e.O(0, [979, 113, 315, 85, 475, 442, 737, 497, 954, 358], () => r(7745)),
			(_N_E = e.O());
	},
]);
