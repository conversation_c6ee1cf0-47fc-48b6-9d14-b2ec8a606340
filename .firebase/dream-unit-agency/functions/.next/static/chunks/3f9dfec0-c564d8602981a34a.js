(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[113],
	{
		3003: (e, t, n) => {
			n.d(t, { aU: () => of });
			var r,
				i,
				s,
				a,
				o = n(8339),
				l = n(8101),
				u = n(1176),
				h = n(854),
				c = n(8958),
				d = n(3854),
				f = n(5036),
				m = n(5677).B<PERSON>er;
			const g = "@firebase/firestore";
			class p {
				constructor(e) {
					this.uid = e;
				}
				isAuthenticated() {
					return null != this.uid;
				}
				toKey() {
					return this.isAuthenticated() ? "uid:" + this.uid : "anonymous-user";
				}
				isEqual(e) {
					return e.uid === this.uid;
				}
			}
			(p.UNAUTHENTICATED = new p(null)),
				(p.GOOGLE_CREDENTIALS = new p("google-credentials-uid")),
				(p.FIRST_PARTY = new p("first-party-uid")),
				(p.MOCK_USER = new p("mock-user"));
			let y = "10.14.0",
				w = new u.Vy("@firebase/firestore");
			function v() {
				return w.logLevel;
			}
			function I(e, ...t) {
				if (w.logLevel <= u.$b.DEBUG) {
					const n = t.map(b);
					w.debug(`Firestore (${y}): ${e}`, ...n);
				}
			}
			function T(e, ...t) {
				if (w.logLevel <= u.$b.ERROR) {
					const n = t.map(b);
					w.error(`Firestore (${y}): ${e}`, ...n);
				}
			}
			function E(e, ...t) {
				if (w.logLevel <= u.$b.WARN) {
					const n = t.map(b);
					w.warn(`Firestore (${y}): ${e}`, ...n);
				}
			}
			function b(e) {
				if ("string" == typeof e) return e;
				try {
					return JSON.stringify(e);
				} catch (t) {
					return e;
				}
			}
			function _(e = "Unexpected state") {
				const t = `FIRESTORE (${y}) INTERNAL ASSERTION FAILED: ` + e;
				throw (T(t), Error(t));
			}
			const S = {
				OK: "ok",
				CANCELLED: "cancelled",
				UNKNOWN: "unknown",
				INVALID_ARGUMENT: "invalid-argument",
				DEADLINE_EXCEEDED: "deadline-exceeded",
				NOT_FOUND: "not-found",
				ALREADY_EXISTS: "already-exists",
				PERMISSION_DENIED: "permission-denied",
				UNAUTHENTICATED: "unauthenticated",
				RESOURCE_EXHAUSTED: "resource-exhausted",
				FAILED_PRECONDITION: "failed-precondition",
				ABORTED: "aborted",
				OUT_OF_RANGE: "out-of-range",
				UNIMPLEMENTED: "unimplemented",
				INTERNAL: "internal",
				UNAVAILABLE: "unavailable",
				DATA_LOSS: "data-loss",
			};
			class x extends h.g {
				constructor(e, t) {
					super(e, t),
						(this.code = e),
						(this.message = t),
						(this.toString = () =>
							`${this.name}: [code=${this.code}]: ${this.message}`);
				}
			}
			class D {
				constructor() {
					this.promise = new Promise((e, t) => {
						(this.resolve = e), (this.reject = t);
					});
				}
			}
			class C {
				constructor(e, t) {
					(this.user = t),
						(this.type = "OAuth"),
						(this.headers = new Map()),
						this.headers.set("Authorization", `Bearer ${e}`);
				}
			}
			class N {
				getToken() {
					return Promise.resolve(null);
				}
				invalidateToken() {}
				start(e, t) {
					e.enqueueRetryable(() => t(p.UNAUTHENTICATED));
				}
				shutdown() {}
			}
			class k {
				constructor(e) {
					(this.token = e), (this.changeListener = null);
				}
				getToken() {
					return Promise.resolve(this.token);
				}
				invalidateToken() {}
				start(e, t) {
					(this.changeListener = t),
						e.enqueueRetryable(() => t(this.token.user));
				}
				shutdown() {
					this.changeListener = null;
				}
			}
			class A {
				constructor(e) {
					(this.t = e),
						(this.currentUser = p.UNAUTHENTICATED),
						(this.i = 0),
						(this.forceRefresh = !1),
						(this.auth = null);
				}
				start(e, t) {
					void 0 === this.o || _();
					let n = this.i,
						r = (e) =>
							this.i !== n ? ((n = this.i), t(e)) : Promise.resolve(),
						i = new D();
					this.o = () => {
						this.i++,
							(this.currentUser = this.u()),
							i.resolve(),
							(i = new D()),
							e.enqueueRetryable(() => r(this.currentUser));
					};
					const s = () => {
							const t = i;
							e.enqueueRetryable(async () => {
								await t.promise, await r(this.currentUser);
							});
						},
						a = (e) => {
							I("FirebaseAuthCredentialsProvider", "Auth detected"),
								(this.auth = e),
								this.o && (this.auth.addAuthTokenListener(this.o), s());
						};
					this.t.onInit((e) => a(e)),
						setTimeout(() => {
							if (!this.auth) {
								const e = this.t.getImmediate({ optional: !0 });
								e
									? a(e)
									: (I(
											"FirebaseAuthCredentialsProvider",
											"Auth not yet detected",
										),
										i.resolve(),
										(i = new D()));
							}
						}, 0),
						s();
				}
				getToken() {
					const e = this.i,
						t = this.forceRefresh;
					return (
						(this.forceRefresh = !1),
						this.auth
							? this.auth
									.getToken(t)
									.then((t) =>
										this.i !== e
											? (I(
													"FirebaseAuthCredentialsProvider",
													"getToken aborted due to token change.",
												),
												this.getToken())
											: t
												? ("string" == typeof t.accessToken || _(),
													new C(t.accessToken, this.currentUser))
												: null,
									)
							: Promise.resolve(null)
					);
				}
				invalidateToken() {
					this.forceRefresh = !0;
				}
				shutdown() {
					this.auth && this.o && this.auth.removeAuthTokenListener(this.o),
						(this.o = void 0);
				}
				u() {
					const e = this.auth && this.auth.getUid();
					return null === e || "string" == typeof e || _(), new p(e);
				}
			}
			class V {
				constructor(e, t, n) {
					(this.l = e),
						(this.h = t),
						(this.P = n),
						(this.type = "FirstParty"),
						(this.user = p.FIRST_PARTY),
						(this.I = new Map());
				}
				T() {
					return this.P ? this.P() : null;
				}
				get headers() {
					this.I.set("X-Goog-AuthUser", this.l);
					const e = this.T();
					return (
						e && this.I.set("Authorization", e),
						this.h && this.I.set("X-Goog-Iam-Authorization-Token", this.h),
						this.I
					);
				}
			}
			class R {
				constructor(e, t, n) {
					(this.l = e), (this.h = t), (this.P = n);
				}
				getToken() {
					return Promise.resolve(new V(this.l, this.h, this.P));
				}
				start(e, t) {
					e.enqueueRetryable(() => t(p.FIRST_PARTY));
				}
				shutdown() {}
				invalidateToken() {}
			}
			class O {
				constructor(e) {
					(this.value = e),
						(this.type = "AppCheck"),
						(this.headers = new Map()),
						e &&
							e.length > 0 &&
							this.headers.set("x-firebase-appcheck", this.value);
				}
			}
			class M {
				constructor(e) {
					(this.A = e),
						(this.forceRefresh = !1),
						(this.appCheck = null),
						(this.R = null);
				}
				start(e, t) {
					void 0 === this.o || _();
					const n = (e) => {
						null != e.error &&
							I(
								"FirebaseAppCheckTokenProvider",
								`Error getting App Check token; using placeholder token instead. Error: ${e.error.message}`,
							);
						const n = e.token !== this.R;
						return (
							(this.R = e.token),
							I(
								"FirebaseAppCheckTokenProvider",
								`Received ${n ? "new" : "existing"} token.`,
							),
							n ? t(e.token) : Promise.resolve()
						);
					};
					this.o = (t) => {
						e.enqueueRetryable(() => n(t));
					};
					const r = (e) => {
						I("FirebaseAppCheckTokenProvider", "AppCheck detected"),
							(this.appCheck = e),
							this.o && this.appCheck.addTokenListener(this.o);
					};
					this.A.onInit((e) => r(e)),
						setTimeout(() => {
							if (!this.appCheck) {
								const e = this.A.getImmediate({ optional: !0 });
								e
									? r(e)
									: I(
											"FirebaseAppCheckTokenProvider",
											"AppCheck not yet detected",
										);
							}
						}, 0);
				}
				getToken() {
					const e = this.forceRefresh;
					return (
						(this.forceRefresh = !1),
						this.appCheck
							? this.appCheck
									.getToken(e)
									.then((e) =>
										e
											? ("string" == typeof e.token || _(),
												(this.R = e.token),
												new O(e.token))
											: null,
									)
							: Promise.resolve(null)
					);
				}
				invalidateToken() {
					this.forceRefresh = !0;
				}
				shutdown() {
					this.appCheck && this.o && this.appCheck.removeTokenListener(this.o),
						(this.o = void 0);
				}
			}
			class F {
				static newId() {
					let e =
							"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",
						t = Math.floor(256 / e.length) * e.length,
						n = "";
					while (n.length < 20) {
						const r = ((e) => {
							const t =
									"undefined" != typeof self && (self.crypto || self.msCrypto),
								n = new Uint8Array(40);
							if (t && "function" == typeof t.getRandomValues)
								t.getRandomValues(n);
							else
								for (let e = 0; e < 40; e++)
									n[e] = Math.floor(256 * Math.random());
							return n;
						})(40);
						for (let i = 0; i < r.length; ++i)
							n.length < 20 && r[i] < t && (n += e.charAt(r[i] % e.length));
					}
					return n;
				}
			}
			function L(e, t) {
				return e < t ? -1 : +(e > t);
			}
			function P(e, t, n) {
				return e.length === t.length && e.every((e, r) => n(e, t[r]));
			}
			class U {
				constructor(e, t) {
					if (((this.seconds = e), (this.nanoseconds = t), t < 0 || t >= 1e9))
						throw new x(
							S.INVALID_ARGUMENT,
							"Timestamp nanoseconds out of range: " + t,
						);
					if (e < -0xe7791f700 || e >= 0x3afff44180)
						throw new x(
							S.INVALID_ARGUMENT,
							"Timestamp seconds out of range: " + e,
						);
				}
				static now() {
					return U.fromMillis(Date.now());
				}
				static fromDate(e) {
					return U.fromMillis(e.getTime());
				}
				static fromMillis(e) {
					const t = Math.floor(e / 1e3),
						n = Math.floor(1e6 * (e - 1e3 * t));
					return new U(t, n);
				}
				toDate() {
					return new Date(this.toMillis());
				}
				toMillis() {
					return 1e3 * this.seconds + this.nanoseconds / 1e6;
				}
				_compareTo(e) {
					return this.seconds === e.seconds
						? L(this.nanoseconds, e.nanoseconds)
						: L(this.seconds, e.seconds);
				}
				isEqual(e) {
					return (
						e.seconds === this.seconds && e.nanoseconds === this.nanoseconds
					);
				}
				toString() {
					return (
						"Timestamp(seconds=" +
						this.seconds +
						", nanoseconds=" +
						this.nanoseconds +
						")"
					);
				}
				toJSON() {
					return { seconds: this.seconds, nanoseconds: this.nanoseconds };
				}
				valueOf() {
					return (
						String(this.seconds - -0xe7791f700).padStart(12, "0") +
						"." +
						String(this.nanoseconds).padStart(9, "0")
					);
				}
			}
			class q {
				constructor(e) {
					this.timestamp = e;
				}
				static fromTimestamp(e) {
					return new q(e);
				}
				static min() {
					return new q(new U(0, 0));
				}
				static max() {
					return new q(new U(0x3afff4417f, 0x3b9ac9ff));
				}
				compareTo(e) {
					return this.timestamp._compareTo(e.timestamp);
				}
				isEqual(e) {
					return this.timestamp.isEqual(e.timestamp);
				}
				toMicroseconds() {
					return (
						1e6 * this.timestamp.seconds + this.timestamp.nanoseconds / 1e3
					);
				}
				toString() {
					return "SnapshotVersion(" + this.timestamp.toString() + ")";
				}
				toTimestamp() {
					return this.timestamp;
				}
			}
			class B {
				constructor(e, t, n) {
					void 0 === t ? (t = 0) : t > e.length && _(),
						void 0 === n ? (n = e.length - t) : n > e.length - t && _(),
						(this.segments = e),
						(this.offset = t),
						(this.len = n);
				}
				get length() {
					return this.len;
				}
				isEqual(e) {
					return 0 === B.comparator(this, e);
				}
				child(e) {
					const t = this.segments.slice(this.offset, this.limit());
					return (
						e instanceof B
							? e.forEach((e) => {
									t.push(e);
								})
							: t.push(e),
						this.construct(t)
					);
				}
				limit() {
					return this.offset + this.length;
				}
				popFirst(e) {
					return (
						(e = void 0 === e ? 1 : e),
						this.construct(this.segments, this.offset + e, this.length - e)
					);
				}
				popLast() {
					return this.construct(this.segments, this.offset, this.length - 1);
				}
				firstSegment() {
					return this.segments[this.offset];
				}
				lastSegment() {
					return this.get(this.length - 1);
				}
				get(e) {
					return this.segments[this.offset + e];
				}
				isEmpty() {
					return 0 === this.length;
				}
				isPrefixOf(e) {
					if (e.length < this.length) return !1;
					for (let t = 0; t < this.length; t++)
						if (this.get(t) !== e.get(t)) return !1;
					return !0;
				}
				isImmediateParentOf(e) {
					if (this.length + 1 !== e.length) return !1;
					for (let t = 0; t < this.length; t++)
						if (this.get(t) !== e.get(t)) return !1;
					return !0;
				}
				forEach(e) {
					for (let t = this.offset, n = this.limit(); t < n; t++)
						e(this.segments[t]);
				}
				toArray() {
					return this.segments.slice(this.offset, this.limit());
				}
				static comparator(e, t) {
					const n = Math.min(e.length, t.length);
					for (let r = 0; r < n; r++) {
						const n = e.get(r),
							i = t.get(r);
						if (n < i) return -1;
						if (n > i) return 1;
					}
					return e.length < t.length ? -1 : +(e.length > t.length);
				}
			}
			class K extends B {
				construct(e, t, n) {
					return new K(e, t, n);
				}
				canonicalString() {
					return this.toArray().join("/");
				}
				toString() {
					return this.canonicalString();
				}
				toUriEncodedString() {
					return this.toArray().map(encodeURIComponent).join("/");
				}
				static fromString(...e) {
					const t = [];
					for (const n of e) {
						if (n.indexOf("//") >= 0)
							throw new x(
								S.INVALID_ARGUMENT,
								`Invalid segment (${n}). Paths must not contain // in them.`,
							);
						t.push(...n.split("/").filter((e) => e.length > 0));
					}
					return new K(t);
				}
				static emptyPath() {
					return new K([]);
				}
			}
			const z = /^[_a-zA-Z][_a-zA-Z0-9]*$/;
			class G extends B {
				construct(e, t, n) {
					return new G(e, t, n);
				}
				static isValidIdentifier(e) {
					return z.test(e);
				}
				canonicalString() {
					return this.toArray()
						.map(
							(e) => (
								(e = e.replace(/\\/g, "\\\\").replace(/`/g, "\\`")),
								G.isValidIdentifier(e) || (e = "`" + e + "`"),
								e
							),
						)
						.join(".");
				}
				toString() {
					return this.canonicalString();
				}
				isKeyField() {
					return 1 === this.length && "__name__" === this.get(0);
				}
				static keyField() {
					return new G(["__name__"]);
				}
				static fromServerFormat(e) {
					let t = [],
						n = "",
						r = 0,
						i = () => {
							if (0 === n.length)
								throw new x(
									S.INVALID_ARGUMENT,
									`Invalid field path (${e}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,
								);
							t.push(n), (n = "");
						},
						s = !1;
					while (r < e.length) {
						const t = e[r];
						if ("\\" === t) {
							if (r + 1 === e.length)
								throw new x(
									S.INVALID_ARGUMENT,
									"Path has trailing escape character: " + e,
								);
							const t = e[r + 1];
							if ("\\" !== t && "." !== t && "`" !== t)
								throw new x(
									S.INVALID_ARGUMENT,
									"Path has invalid escape sequence: " + e,
								);
							(n += t), (r += 2);
						} else "`" === t ? (s = !s) : "." !== t || s ? (n += t) : i(), r++;
					}
					if ((i(), s))
						throw new x(S.INVALID_ARGUMENT, "Unterminated ` in path: " + e);
					return new G(t);
				}
				static emptyPath() {
					return new G([]);
				}
			}
			class $ {
				constructor(e) {
					this.path = e;
				}
				static fromPath(e) {
					return new $(K.fromString(e));
				}
				static fromName(e) {
					return new $(K.fromString(e).popFirst(5));
				}
				static empty() {
					return new $(K.emptyPath());
				}
				get collectionGroup() {
					return this.path.popLast().lastSegment();
				}
				hasCollectionId(e) {
					return (
						this.path.length >= 2 && this.path.get(this.path.length - 2) === e
					);
				}
				getCollectionGroup() {
					return this.path.get(this.path.length - 2);
				}
				getCollectionPath() {
					return this.path.popLast();
				}
				isEqual(e) {
					return null !== e && 0 === K.comparator(this.path, e.path);
				}
				toString() {
					return this.path.toString();
				}
				static comparator(e, t) {
					return K.comparator(e.path, t.path);
				}
				static isDocumentKey(e) {
					return e.length % 2 == 0;
				}
				static fromSegments(e) {
					return new $(new K(e.slice()));
				}
			}
			class Q {
				constructor(e, t, n, r) {
					(this.indexId = e),
						(this.collectionGroup = t),
						(this.fields = n),
						(this.indexState = r);
				}
			}
			function j(e) {
				return e.fields.find((e) => 2 === e.kind);
			}
			function W(e) {
				return e.fields.filter((e) => 2 !== e.kind);
			}
			Q.UNKNOWN_ID = -1;
			class J {
				constructor(e, t) {
					(this.fieldPath = e), (this.kind = t);
				}
			}
			class H {
				constructor(e, t) {
					(this.sequenceNumber = e), (this.offset = t);
				}
				static empty() {
					return new H(0, Z.min());
				}
			}
			function Y(e, t) {
				const n = e.toTimestamp().seconds,
					r = e.toTimestamp().nanoseconds + 1;
				return new Z(
					q.fromTimestamp(1e9 === r ? new U(n + 1, 0) : new U(n, r)),
					$.empty(),
					t,
				);
			}
			function X(e) {
				return new Z(e.readTime, e.key, -1);
			}
			class Z {
				constructor(e, t, n) {
					(this.readTime = e),
						(this.documentKey = t),
						(this.largestBatchId = n);
				}
				static min() {
					return new Z(q.min(), $.empty(), -1);
				}
				static max() {
					return new Z(q.max(), $.empty(), -1);
				}
			}
			function ee(e, t) {
				let n = e.readTime.compareTo(t.readTime);
				return 0 !== n
					? n
					: 0 !== (n = $.comparator(e.documentKey, t.documentKey))
						? n
						: L(e.largestBatchId, t.largestBatchId);
			}
			const et =
				"The current tab is not in the required state to perform this operation. It might be necessary to refresh the browser tab.";
			class en {
				constructor() {
					this.onCommittedListeners = [];
				}
				addOnCommittedListener(e) {
					this.onCommittedListeners.push(e);
				}
				raiseOnCommittedEvent() {
					this.onCommittedListeners.forEach((e) => e());
				}
			}
			async function er(e) {
				if (e.code !== S.FAILED_PRECONDITION || e.message !== et) throw e;
				I("LocalStore", "Unexpectedly lost primary lease");
			}
			class ei {
				constructor(e) {
					(this.nextCallback = null),
						(this.catchCallback = null),
						(this.result = void 0),
						(this.error = void 0),
						(this.isDone = !1),
						(this.callbackAttached = !1),
						e(
							(e) => {
								(this.isDone = !0),
									(this.result = e),
									this.nextCallback && this.nextCallback(e);
							},
							(e) => {
								(this.isDone = !0),
									(this.error = e),
									this.catchCallback && this.catchCallback(e);
							},
						);
				}
				catch(e) {
					return this.next(void 0, e);
				}
				next(e, t) {
					return (
						this.callbackAttached && _(),
						(this.callbackAttached = !0),
						this.isDone
							? this.error
								? this.wrapFailure(t, this.error)
								: this.wrapSuccess(e, this.result)
							: new ei((n, r) => {
									(this.nextCallback = (t) => {
										this.wrapSuccess(e, t).next(n, r);
									}),
										(this.catchCallback = (e) => {
											this.wrapFailure(t, e).next(n, r);
										});
								})
					);
				}
				toPromise() {
					return new Promise((e, t) => {
						this.next(e, t);
					});
				}
				wrapUserFunction(e) {
					try {
						const t = e();
						return t instanceof ei ? t : ei.resolve(t);
					} catch (e) {
						return ei.reject(e);
					}
				}
				wrapSuccess(e, t) {
					return e ? this.wrapUserFunction(() => e(t)) : ei.resolve(t);
				}
				wrapFailure(e, t) {
					return e ? this.wrapUserFunction(() => e(t)) : ei.reject(t);
				}
				static resolve(e) {
					return new ei((t, n) => {
						t(e);
					});
				}
				static reject(e) {
					return new ei((t, n) => {
						n(e);
					});
				}
				static waitFor(e) {
					return new ei((t, n) => {
						let r = 0,
							i = 0,
							s = !1;
						e.forEach((e) => {
							++r,
								e.next(
									() => {
										++i, s && i === r && t();
									},
									(e) => n(e),
								);
						}),
							(s = !0),
							i === r && t();
					});
				}
				static or(e) {
					let t = ei.resolve(!1);
					for (const n of e) t = t.next((e) => (e ? ei.resolve(e) : n()));
					return t;
				}
				static forEach(e, t) {
					const n = [];
					return (
						e.forEach((e, r) => {
							n.push(t.call(this, e, r));
						}),
						this.waitFor(n)
					);
				}
				static mapArray(e, t) {
					return new ei((n, r) => {
						let i = e.length,
							s = Array(i),
							a = 0;
						for (let o = 0; o < i; o++) {
							const l = o;
							t(e[l]).next(
								(e) => {
									(s[l] = e), ++a === i && n(s);
								},
								(e) => r(e),
							);
						}
					});
				}
				static doWhile(e, t) {
					return new ei((n, r) => {
						const i = () => {
							!0 === e()
								? t().next(() => {
										i();
									}, r)
								: n();
						};
						i();
					});
				}
			}
			class es {
				constructor(e, t) {
					(this.action = e),
						(this.transaction = t),
						(this.aborted = !1),
						(this.V = new D()),
						(this.transaction.oncomplete = () => {
							this.V.resolve();
						}),
						(this.transaction.onabort = () => {
							t.error ? this.V.reject(new eu(e, t.error)) : this.V.resolve();
						}),
						(this.transaction.onerror = (t) => {
							const n = em(t.target.error);
							this.V.reject(new eu(e, n));
						});
				}
				static open(e, t, n, r) {
					try {
						return new es(t, e.transaction(r, n));
					} catch (e) {
						throw new eu(t, e);
					}
				}
				get m() {
					return this.V.promise;
				}
				abort(e) {
					e && this.V.reject(e),
						this.aborted ||
							(I(
								"SimpleDb",
								"Aborting transaction:",
								e ? e.message : "Client-initiated abort",
							),
							(this.aborted = !0),
							this.transaction.abort());
				}
				g() {
					const e = this.transaction;
					this.aborted || "function" != typeof e.commit || e.commit();
				}
				store(e) {
					return new ec(this.transaction.objectStore(e));
				}
			}
			class ea {
				constructor(e, t, n) {
					(this.name = e),
						(this.version = t),
						(this.p = n),
						12.2 === ea.S((0, h.ZQ)()) &&
							T(
								"Firestore persistence suffers from a bug in iOS 12.2 Safari that may cause your app to stop working. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.",
							);
				}
				static delete(e) {
					return (
						I("SimpleDb", "Removing database:", e),
						ed(window.indexedDB.deleteDatabase(e)).toPromise()
					);
				}
				static D() {
					if (!(0, h.zW)()) return !1;
					if (ea.v()) return !0;
					const e = (0, h.ZQ)(),
						t = ea.S(e),
						n = eo(e);
					return !(
						e.indexOf("MSIE ") > 0 ||
						e.indexOf("Trident/") > 0 ||
						e.indexOf("Edge/") > 0 ||
						(0 < t && t < 10) ||
						(0 < n && n < 4.5)
					);
				}
				static v() {
					var e;
					return (
						void 0 !== f &&
						"YES" ===
							(null === (e = f.__PRIVATE_env) || void 0 === e ? void 0 : e.C)
					);
				}
				static F(e, t) {
					return e.store(t);
				}
				static S(e) {
					const t = e.match(/i(?:phone|pad|pod) os ([\d_]+)/i);
					return Number(t ? t[1].split("_").slice(0, 2).join(".") : "-1");
				}
				async M(e) {
					return (
						this.db ||
							(I("SimpleDb", "Opening database:", this.name),
							(this.db = await new Promise((t, n) => {
								const r = indexedDB.open(this.name, this.version);
								(r.onsuccess = (e) => {
									t(e.target.result);
								}),
									(r.onblocked = () => {
										n(
											new eu(
												e,
												"Cannot upgrade IndexedDB schema while another tab is open. Close all tabs that access Firestore and reload this page to proceed.",
											),
										);
									}),
									(r.onerror = (t) => {
										const r = t.target.error;
										"VersionError" === r.name
											? n(
													new x(
														S.FAILED_PRECONDITION,
														"A newer version of the Firestore SDK was previously used and so the persisted data is not compatible with the version of the SDK you are now using. The SDK will operate with persistence disabled. If you need persistence, please re-upgrade to a newer version of the SDK or else clear the persisted IndexedDB data for your app to start fresh.",
													),
												)
											: "InvalidStateError" === r.name
												? n(
														new x(
															S.FAILED_PRECONDITION,
															"Unable to open an IndexedDB connection. This could be due to running in a private browsing session on a browser whose private browsing sessions do not support IndexedDB: " +
																r,
														),
													)
												: n(new eu(e, r));
									}),
									(r.onupgradeneeded = (e) => {
										I(
											"SimpleDb",
											'Database "' +
												this.name +
												'" requires upgrade from version:',
											e.oldVersion,
										);
										const t = e.target.result;
										this.p
											.O(t, r.transaction, e.oldVersion, this.version)
											.next(() => {
												I(
													"SimpleDb",
													"Database upgrade to version " +
														this.version +
														" complete",
												);
											});
									});
							}))),
						this.N && (this.db.onversionchange = (e) => this.N(e)),
						this.db
					);
				}
				L(e) {
					(this.N = e), this.db && (this.db.onversionchange = (t) => e(t));
				}
				async runTransaction(e, t, n, r) {
					let i = "readonly" === t,
						s = 0;
					for (;;) {
						++s;
						try {
							this.db = await this.M(e);
							const t = es.open(this.db, e, i ? "readonly" : "readwrite", n),
								s = r(t)
									.next((e) => (t.g(), e))
									.catch((e) => (t.abort(e), ei.reject(e)))
									.toPromise();
							return s.catch(() => {}), await t.m, s;
						} catch (t) {
							const e = "FirebaseError" !== t.name && s < 3;
							if (
								(I(
									"SimpleDb",
									"Transaction failed with error:",
									t.message,
									"Retrying:",
									e,
								),
								this.close(),
								!e)
							)
								return Promise.reject(t);
						}
					}
				}
				close() {
					this.db && this.db.close(), (this.db = void 0);
				}
			}
			function eo(e) {
				const t = e.match(/Android ([\d.]+)/i);
				return Number(t ? t[1].split(".").slice(0, 2).join(".") : "-1");
			}
			class el {
				constructor(e) {
					(this.B = e), (this.k = !1), (this.q = null);
				}
				get isDone() {
					return this.k;
				}
				get K() {
					return this.q;
				}
				set cursor(e) {
					this.B = e;
				}
				done() {
					this.k = !0;
				}
				$(e) {
					this.q = e;
				}
				delete() {
					return ed(this.B.delete());
				}
			}
			class eu extends x {
				constructor(e, t) {
					super(S.UNAVAILABLE, `IndexedDB transaction '${e}' failed: ${t}`),
						(this.name = "IndexedDbTransactionError");
				}
			}
			function eh(e) {
				return "IndexedDbTransactionError" === e.name;
			}
			class ec {
				constructor(e) {
					this.store = e;
				}
				put(e, t) {
					let n;
					return (
						void 0 !== t
							? (I("SimpleDb", "PUT", this.store.name, e, t),
								(n = this.store.put(t, e)))
							: (I("SimpleDb", "PUT", this.store.name, "<auto-key>", e),
								(n = this.store.put(e))),
						ed(n)
					);
				}
				add(e) {
					return (
						I("SimpleDb", "ADD", this.store.name, e, e), ed(this.store.add(e))
					);
				}
				get(e) {
					return ed(this.store.get(e)).next(
						(t) => (
							void 0 === t && (t = null),
							I("SimpleDb", "GET", this.store.name, e, t),
							t
						),
					);
				}
				delete(e) {
					return (
						I("SimpleDb", "DELETE", this.store.name, e),
						ed(this.store.delete(e))
					);
				}
				count() {
					return (
						I("SimpleDb", "COUNT", this.store.name), ed(this.store.count())
					);
				}
				U(e, t) {
					const n = this.options(e, t),
						r = n.index ? this.store.index(n.index) : this.store;
					if ("function" == typeof r.getAll) {
						const e = r.getAll(n.range);
						return new ei((t, n) => {
							(e.onerror = (e) => {
								n(e.target.error);
							}),
								(e.onsuccess = (e) => {
									t(e.target.result);
								});
						});
					}
					{
						const e = this.cursor(n),
							t = [];
						return this.W(e, (e, n) => {
							t.push(n);
						}).next(() => t);
					}
				}
				G(e, t) {
					const n = this.store.getAll(e, null === t ? void 0 : t);
					return new ei((e, t) => {
						(n.onerror = (e) => {
							t(e.target.error);
						}),
							(n.onsuccess = (t) => {
								e(t.target.result);
							});
					});
				}
				j(e, t) {
					I("SimpleDb", "DELETE ALL", this.store.name);
					const n = this.options(e, t);
					n.H = !1;
					const r = this.cursor(n);
					return this.W(r, (e, t, n) => n.delete());
				}
				J(e, t) {
					let n;
					t ? (n = e) : ((n = {}), (t = e));
					const r = this.cursor(n);
					return this.W(r, t);
				}
				Y(e) {
					const t = this.cursor({});
					return new ei((n, r) => {
						(t.onerror = (e) => {
							r(em(e.target.error));
						}),
							(t.onsuccess = (t) => {
								const r = t.target.result;
								r
									? e(r.primaryKey, r.value).next((e) => {
											e ? r.continue() : n();
										})
									: n();
							});
					});
				}
				W(e, t) {
					const n = [];
					return new ei((r, i) => {
						(e.onerror = (e) => {
							i(e.target.error);
						}),
							(e.onsuccess = (e) => {
								const i = e.target.result;
								if (!i) return void r();
								const s = new el(i),
									a = t(i.primaryKey, i.value, s);
								if (a instanceof ei) {
									const e = a.catch((e) => (s.done(), ei.reject(e)));
									n.push(e);
								}
								s.isDone ? r() : null === s.K ? i.continue() : i.continue(s.K);
							});
					}).next(() => ei.waitFor(n));
				}
				options(e, t) {
					let n;
					return (
						void 0 !== e && ("string" == typeof e ? (n = e) : (t = e)),
						{ index: n, range: t }
					);
				}
				cursor(e) {
					let t = "next";
					if ((e.reverse && (t = "prev"), e.index)) {
						const n = this.store.index(e.index);
						return e.H ? n.openKeyCursor(e.range, t) : n.openCursor(e.range, t);
					}
					return this.store.openCursor(e.range, t);
				}
			}
			function ed(e) {
				return new ei((t, n) => {
					(e.onsuccess = (e) => {
						t(e.target.result);
					}),
						(e.onerror = (e) => {
							n(em(e.target.error));
						});
				});
			}
			let ef = !1;
			function em(e) {
				const t = ea.S((0, h.ZQ)());
				if (t >= 12.2 && t < 13) {
					const t =
						"An internal error was encountered in the Indexed Database server";
					if (e.message.indexOf(t) >= 0) {
						const e = new x(
							"internal",
							`IOS_INDEXEDDB_BUG1: IndexedDb has thrown '${t}'. This is likely due to an unavoidable bug in iOS. See https://stackoverflow.com/q/56496296/110915 for details and a potential workaround.`,
						);
						return (
							ef ||
								((ef = !0),
								setTimeout(() => {
									throw e;
								}, 0)),
							e
						);
					}
				}
				return e;
			}
			class eg {
				constructor(e, t) {
					(this.asyncQueue = e), (this.Z = t), (this.task = null);
				}
				start() {
					this.X(15e3);
				}
				stop() {
					this.task && (this.task.cancel(), (this.task = null));
				}
				get started() {
					return null !== this.task;
				}
				X(e) {
					I("IndexBackfiller", `Scheduled in ${e}ms`),
						(this.task = this.asyncQueue.enqueueAfterDelay(
							"index_backfill",
							e,
							async () => {
								this.task = null;
								try {
									I(
										"IndexBackfiller",
										`Documents written: ${await this.Z.ee()}`,
									);
								} catch (e) {
									eh(e)
										? I(
												"IndexBackfiller",
												"Ignoring IndexedDB error during index backfill: ",
												e,
											)
										: await er(e);
								}
								await this.X(6e4);
							},
						));
				}
			}
			class ep {
				constructor(e, t) {
					(this.localStore = e), (this.persistence = t);
				}
				async ee(e = 50) {
					return this.persistence.runTransaction(
						"Backfill Indexes",
						"readwrite-primary",
						(t) => this.te(t, e),
					);
				}
				te(e, t) {
					let n = new Set(),
						r = t,
						i = !0;
					return ei
						.doWhile(
							() => !0 === i && r > 0,
							() =>
								this.localStore.indexManager
									.getNextCollectionGroupToUpdate(e)
									.next((t) => {
										if (null !== t && !n.has(t))
											return (
												I("IndexBackfiller", `Processing collection: ${t}`),
												this.ne(e, t, r).next((e) => {
													(r -= e), n.add(t);
												})
											);
										i = !1;
									}),
						)
						.next(() => t - r);
				}
				ne(e, t, n) {
					return this.localStore.indexManager
						.getMinOffsetFromCollectionGroup(e, t)
						.next((r) =>
							this.localStore.localDocuments
								.getNextDocuments(e, t, r, n)
								.next((n) => {
									const i = n.changes;
									return this.localStore.indexManager
										.updateIndexEntries(e, i)
										.next(() => this.re(r, n))
										.next(
											(n) => (
												I("IndexBackfiller", `Updating offset: ${n}`),
												this.localStore.indexManager.updateCollectionGroup(
													e,
													t,
													n,
												)
											),
										)
										.next(() => i.size);
								}),
						);
				}
				re(e, t) {
					let n = e;
					return (
						t.changes.forEach((e, t) => {
							const r = X(t);
							ee(r, n) > 0 && (n = r);
						}),
						new Z(
							n.readTime,
							n.documentKey,
							Math.max(t.batchId, e.largestBatchId),
						)
					);
				}
			}
			class ey {
				constructor(e, t) {
					(this.previousValue = e),
						t &&
							((t.sequenceNumberHandler = (e) => this.ie(e)),
							(this.se = (e) => t.writeSequenceNumber(e)));
				}
				ie(e) {
					return (
						(this.previousValue = Math.max(e, this.previousValue)),
						this.previousValue
					);
				}
				next() {
					const e = ++this.previousValue;
					return this.se && this.se(e), e;
				}
			}
			function ew(e) {
				return null == e;
			}
			function ev(e) {
				return 0 === e && 1 / e == -1 / 0;
			}
			function eI(e) {
				return (
					"number" == typeof e &&
					Number.isInteger(e) &&
					!ev(e) &&
					e <= Number.MAX_SAFE_INTEGER &&
					e >= Number.MIN_SAFE_INTEGER
				);
			}
			function eT(e) {
				let t = "";
				for (let n = 0; n < e.length; n++)
					t.length > 0 && (t += "\x01\x01"),
						(t = ((e, t) => {
							let n = t,
								r = e.length;
							for (let t = 0; t < r; t++) {
								const r = e.charAt(t);
								switch (r) {
									case "\0":
										n += "\x01\x10";
										break;
									case "\x01":
										n += "\x01\x11";
										break;
									default:
										n += r;
								}
							}
							return n;
						})(e.get(n), t));
				return t + "\x01\x01";
			}
			ey.oe = -1;
			function eE(e) {
				const t = e.length;
				if ((t >= 2 || _(), 2 === t))
					return (
						("\x01" === e.charAt(0) && "\x01" === e.charAt(1)) || _(),
						K.emptyPath()
					);
				let n = t - 2,
					r = [],
					i = "";
				for (let s = 0; s < t; ) {
					const t = e.indexOf("\x01", s);
					switch (((t < 0 || t > n) && _(), e.charAt(t + 1))) {
						case "\x01":
							let a;
							const o = e.substring(s, t);
							0 === i.length ? (a = o) : ((i += o), (a = i), (i = "")),
								r.push(a);
							break;
						case "\x10":
							(i += e.substring(s, t)), (i += "\0");
							break;
						case "\x11":
							i += e.substring(s, t + 1);
							break;
						default:
							_();
					}
					s = t + 2;
				}
				return new K(r);
			}
			const eb = ["userId", "batchId"],
				e_ = {},
				eS = ["prefixPath", "collectionGroup", "readTime", "documentId"],
				ex = ["prefixPath", "collectionGroup", "documentId"],
				eD = ["collectionGroup", "readTime", "prefixPath", "documentId"],
				eC = ["canonicalId", "targetId"],
				eN = ["targetId", "path"],
				ek = ["path", "targetId"],
				eA = ["collectionId", "parent"],
				eV = ["indexId", "uid"],
				eR = ["uid", "sequenceNumber"],
				eO = [
					"indexId",
					"uid",
					"arrayValue",
					"directionalValue",
					"orderedDocumentKey",
					"documentKey",
				],
				eM = ["indexId", "uid", "orderedDocumentKey"],
				eF = ["userId", "collectionPath", "documentId"],
				eL = ["userId", "collectionPath", "largestBatchId"],
				eP = ["userId", "collectionGroup", "largestBatchId"],
				eU = [
					"mutationQueues",
					"mutations",
					"documentMutations",
					"remoteDocuments",
					"targets",
					"owner",
					"targetGlobal",
					"targetDocuments",
					"clientMetadata",
					"remoteDocumentGlobal",
					"collectionParents",
					"bundles",
					"namedQueries",
				],
				eq = [...eU, "documentOverlays"],
				eB = [
					"mutationQueues",
					"mutations",
					"documentMutations",
					"remoteDocumentsV14",
					"targets",
					"owner",
					"targetGlobal",
					"targetDocuments",
					"clientMetadata",
					"remoteDocumentGlobal",
					"collectionParents",
					"bundles",
					"namedQueries",
					"documentOverlays",
				],
				eK = [...eB, "indexConfiguration", "indexState", "indexEntries"],
				ez = [...eK, "globals"];
			class eG extends en {
				constructor(e, t) {
					super(), (this._e = e), (this.currentSequenceNumber = t);
				}
			}
			function e$(e, t) {
				return ea.F(e._e, t);
			}
			function eQ(e) {
				let t = 0;
				for (const n in e) Object.prototype.hasOwnProperty.call(e, n) && t++;
				return t;
			}
			function ej(e, t) {
				for (const n in e)
					Object.prototype.hasOwnProperty.call(e, n) && t(n, e[n]);
			}
			function eW(e) {
				for (const t in e)
					if (Object.prototype.hasOwnProperty.call(e, t)) return !1;
				return !0;
			}
			class eJ {
				constructor(e, t) {
					(this.comparator = e), (this.root = t || eY.EMPTY);
				}
				insert(e, t) {
					return new eJ(
						this.comparator,
						this.root
							.insert(e, t, this.comparator)
							.copy(null, null, eY.BLACK, null, null),
					);
				}
				remove(e) {
					return new eJ(
						this.comparator,
						this.root
							.remove(e, this.comparator)
							.copy(null, null, eY.BLACK, null, null),
					);
				}
				get(e) {
					let t = this.root;
					while (!t.isEmpty()) {
						const n = this.comparator(e, t.key);
						if (0 === n) return t.value;
						n < 0 ? (t = t.left) : n > 0 && (t = t.right);
					}
					return null;
				}
				indexOf(e) {
					let t = 0,
						n = this.root;
					while (!n.isEmpty()) {
						const r = this.comparator(e, n.key);
						if (0 === r) return t + n.left.size;
						r < 0 ? (n = n.left) : ((t += n.left.size + 1), (n = n.right));
					}
					return -1;
				}
				isEmpty() {
					return this.root.isEmpty();
				}
				get size() {
					return this.root.size;
				}
				minKey() {
					return this.root.minKey();
				}
				maxKey() {
					return this.root.maxKey();
				}
				inorderTraversal(e) {
					return this.root.inorderTraversal(e);
				}
				forEach(e) {
					this.inorderTraversal((t, n) => (e(t, n), !1));
				}
				toString() {
					const e = [];
					return (
						this.inorderTraversal((t, n) => (e.push(`${t}:${n}`), !1)),
						`{${e.join(", ")}}`
					);
				}
				reverseTraversal(e) {
					return this.root.reverseTraversal(e);
				}
				getIterator() {
					return new eH(this.root, null, this.comparator, !1);
				}
				getIteratorFrom(e) {
					return new eH(this.root, e, this.comparator, !1);
				}
				getReverseIterator() {
					return new eH(this.root, null, this.comparator, !0);
				}
				getReverseIteratorFrom(e) {
					return new eH(this.root, e, this.comparator, !0);
				}
			}
			class eH {
				constructor(e, t, n, r) {
					(this.isReverse = r), (this.nodeStack = []);
					let i = 1;
					while (!e.isEmpty())
						if (((i = t ? n(e.key, t) : 1), t && r && (i *= -1), i < 0))
							e = this.isReverse ? e.left : e.right;
						else {
							if (0 === i) {
								this.nodeStack.push(e);
								break;
							}
							this.nodeStack.push(e), (e = this.isReverse ? e.right : e.left);
						}
				}
				getNext() {
					let e = this.nodeStack.pop(),
						t = { key: e.key, value: e.value };
					if (this.isReverse)
						for (e = e.left; !e.isEmpty(); )
							this.nodeStack.push(e), (e = e.right);
					else
						for (e = e.right; !e.isEmpty(); )
							this.nodeStack.push(e), (e = e.left);
					return t;
				}
				hasNext() {
					return this.nodeStack.length > 0;
				}
				peek() {
					if (0 === this.nodeStack.length) return null;
					const e = this.nodeStack[this.nodeStack.length - 1];
					return { key: e.key, value: e.value };
				}
			}
			class eY {
				constructor(e, t, n, r, i) {
					(this.key = e),
						(this.value = t),
						(this.color = null != n ? n : eY.RED),
						(this.left = null != r ? r : eY.EMPTY),
						(this.right = null != i ? i : eY.EMPTY),
						(this.size = this.left.size + 1 + this.right.size);
				}
				copy(e, t, n, r, i) {
					return new eY(
						null != e ? e : this.key,
						null != t ? t : this.value,
						null != n ? n : this.color,
						null != r ? r : this.left,
						null != i ? i : this.right,
					);
				}
				isEmpty() {
					return !1;
				}
				inorderTraversal(e) {
					return (
						this.left.inorderTraversal(e) ||
						e(this.key, this.value) ||
						this.right.inorderTraversal(e)
					);
				}
				reverseTraversal(e) {
					return (
						this.right.reverseTraversal(e) ||
						e(this.key, this.value) ||
						this.left.reverseTraversal(e)
					);
				}
				min() {
					return this.left.isEmpty() ? this : this.left.min();
				}
				minKey() {
					return this.min().key;
				}
				maxKey() {
					return this.right.isEmpty() ? this.key : this.right.maxKey();
				}
				insert(e, t, n) {
					let r = this,
						i = n(e, r.key);
					return (r =
						i < 0
							? r.copy(null, null, null, r.left.insert(e, t, n), null)
							: 0 === i
								? r.copy(null, t, null, null, null)
								: r.copy(
										null,
										null,
										null,
										null,
										r.right.insert(e, t, n),
									)).fixUp();
				}
				removeMin() {
					if (this.left.isEmpty()) return eY.EMPTY;
					let e = this;
					return (
						e.left.isRed() || e.left.left.isRed() || (e = e.moveRedLeft()),
						(e = e.copy(null, null, null, e.left.removeMin(), null)).fixUp()
					);
				}
				remove(e, t) {
					let n,
						r = this;
					if (0 > t(e, r.key))
						r.left.isEmpty() ||
							r.left.isRed() ||
							r.left.left.isRed() ||
							(r = r.moveRedLeft()),
							(r = r.copy(null, null, null, r.left.remove(e, t), null));
					else {
						if (
							(r.left.isRed() && (r = r.rotateRight()),
							r.right.isEmpty() ||
								r.right.isRed() ||
								r.right.left.isRed() ||
								(r = r.moveRedRight()),
							0 === t(e, r.key))
						) {
							if (r.right.isEmpty()) return eY.EMPTY;
							(n = r.right.min()),
								(r = r.copy(n.key, n.value, null, null, r.right.removeMin()));
						}
						r = r.copy(null, null, null, null, r.right.remove(e, t));
					}
					return r.fixUp();
				}
				isRed() {
					return this.color;
				}
				fixUp() {
					let e = this;
					return (
						e.right.isRed() && !e.left.isRed() && (e = e.rotateLeft()),
						e.left.isRed() && e.left.left.isRed() && (e = e.rotateRight()),
						e.left.isRed() && e.right.isRed() && (e = e.colorFlip()),
						e
					);
				}
				moveRedLeft() {
					let e = this.colorFlip();
					return (
						e.right.left.isRed() &&
							(e = (e = (e = e.copy(
								null,
								null,
								null,
								null,
								e.right.rotateRight(),
							)).rotateLeft()).colorFlip()),
						e
					);
				}
				moveRedRight() {
					let e = this.colorFlip();
					return (
						e.left.left.isRed() && (e = (e = e.rotateRight()).colorFlip()), e
					);
				}
				rotateLeft() {
					const e = this.copy(null, null, eY.RED, null, this.right.left);
					return this.right.copy(null, null, this.color, e, null);
				}
				rotateRight() {
					const e = this.copy(null, null, eY.RED, this.left.right, null);
					return this.left.copy(null, null, this.color, null, e);
				}
				colorFlip() {
					const e = this.left.copy(null, null, !this.left.color, null, null),
						t = this.right.copy(null, null, !this.right.color, null, null);
					return this.copy(null, null, !this.color, e, t);
				}
				checkMaxDepth() {
					return Math.pow(2, this.check()) <= this.size + 1;
				}
				check() {
					if ((this.isRed() && this.left.isRed()) || this.right.isRed())
						throw _();
					const e = this.left.check();
					if (e !== this.right.check()) throw _();
					return e + +!this.isRed();
				}
			}
			(eY.EMPTY = null),
				(eY.RED = !0),
				(eY.BLACK = !1),
				(eY.EMPTY = new (class {
					constructor() {
						this.size = 0;
					}
					get key() {
						throw _();
					}
					get value() {
						throw _();
					}
					get color() {
						throw _();
					}
					get left() {
						throw _();
					}
					get right() {
						throw _();
					}
					copy(e, t, n, r, i) {
						return this;
					}
					insert(e, t, n) {
						return new eY(e, t);
					}
					remove(e, t) {
						return this;
					}
					isEmpty() {
						return !0;
					}
					inorderTraversal(e) {
						return !1;
					}
					reverseTraversal(e) {
						return !1;
					}
					minKey() {
						return null;
					}
					maxKey() {
						return null;
					}
					isRed() {
						return !1;
					}
					checkMaxDepth() {
						return !0;
					}
					check() {
						return 0;
					}
				})());
			class eX {
				constructor(e) {
					(this.comparator = e), (this.data = new eJ(this.comparator));
				}
				has(e) {
					return null !== this.data.get(e);
				}
				first() {
					return this.data.minKey();
				}
				last() {
					return this.data.maxKey();
				}
				get size() {
					return this.data.size;
				}
				indexOf(e) {
					return this.data.indexOf(e);
				}
				forEach(e) {
					this.data.inorderTraversal((t, n) => (e(t), !1));
				}
				forEachInRange(e, t) {
					const n = this.data.getIteratorFrom(e[0]);
					while (n.hasNext()) {
						const r = n.getNext();
						if (this.comparator(r.key, e[1]) >= 0) return;
						t(r.key);
					}
				}
				forEachWhile(e, t) {
					let n;
					for (
						n =
							void 0 !== t
								? this.data.getIteratorFrom(t)
								: this.data.getIterator();
						n.hasNext();
					)
						if (!e(n.getNext().key)) return;
				}
				firstAfterOrEqual(e) {
					const t = this.data.getIteratorFrom(e);
					return t.hasNext() ? t.getNext().key : null;
				}
				getIterator() {
					return new eZ(this.data.getIterator());
				}
				getIteratorFrom(e) {
					return new eZ(this.data.getIteratorFrom(e));
				}
				add(e) {
					return this.copy(this.data.remove(e).insert(e, !0));
				}
				delete(e) {
					return this.has(e) ? this.copy(this.data.remove(e)) : this;
				}
				isEmpty() {
					return this.data.isEmpty();
				}
				unionWith(e) {
					let t = this;
					return (
						t.size < e.size && ((t = e), (e = this)),
						e.forEach((e) => {
							t = t.add(e);
						}),
						t
					);
				}
				isEqual(e) {
					if (!(e instanceof eX) || this.size !== e.size) return !1;
					const t = this.data.getIterator(),
						n = e.data.getIterator();
					while (t.hasNext()) {
						const e = t.getNext().key,
							r = n.getNext().key;
						if (0 !== this.comparator(e, r)) return !1;
					}
					return !0;
				}
				toArray() {
					const e = [];
					return (
						this.forEach((t) => {
							e.push(t);
						}),
						e
					);
				}
				toString() {
					const e = [];
					return (
						this.forEach((t) => e.push(t)), "SortedSet(" + e.toString() + ")"
					);
				}
				copy(e) {
					const t = new eX(this.comparator);
					return (t.data = e), t;
				}
			}
			class eZ {
				constructor(e) {
					this.iter = e;
				}
				getNext() {
					return this.iter.getNext().key;
				}
				hasNext() {
					return this.iter.hasNext();
				}
			}
			function e0(e) {
				return e.hasNext() ? e.getNext() : void 0;
			}
			class e1 {
				constructor(e) {
					(this.fields = e), e.sort(G.comparator);
				}
				static empty() {
					return new e1([]);
				}
				unionWith(e) {
					let t = new eX(G.comparator);
					for (const e of this.fields) t = t.add(e);
					for (const n of e) t = t.add(n);
					return new e1(t.toArray());
				}
				covers(e) {
					for (const t of this.fields) if (t.isPrefixOf(e)) return !0;
					return !1;
				}
				isEqual(e) {
					return P(this.fields, e.fields, (e, t) => e.isEqual(t));
				}
			}
			class e2 extends Error {
				constructor() {
					super(...arguments), (this.name = "Base64DecodeError");
				}
			}
			class e5 {
				constructor(e) {
					this.binaryString = e;
				}
				static fromBase64String(e) {
					return new e5(
						((e) => {
							try {
								return atob(e);
							} catch (e) {
								throw "undefined" != typeof DOMException &&
									e instanceof DOMException
									? new e2("Invalid base64 string: " + e)
									: e;
							}
						})(e),
					);
				}
				static fromUint8Array(e) {
					return new e5(
						((e) => {
							let t = "";
							for (let n = 0; n < e.length; ++n) t += String.fromCharCode(e[n]);
							return t;
						})(e),
					);
				}
				[Symbol.iterator]() {
					let e = 0;
					return {
						next: () =>
							e < this.binaryString.length
								? { value: this.binaryString.charCodeAt(e++), done: !1 }
								: { value: void 0, done: !0 },
					};
				}
				toBase64() {
					return btoa(this.binaryString);
				}
				toUint8Array() {
					return ((e) => {
						const t = new Uint8Array(e.length);
						for (let n = 0; n < e.length; n++) t[n] = e.charCodeAt(n);
						return t;
					})(this.binaryString);
				}
				approximateByteSize() {
					return 2 * this.binaryString.length;
				}
				compareTo(e) {
					return L(this.binaryString, e.binaryString);
				}
				isEqual(e) {
					return this.binaryString === e.binaryString;
				}
			}
			e5.EMPTY_BYTE_STRING = new e5("");
			const e3 = new RegExp(/^\d{4}-\d\d-\d\dT\d\d:\d\d:\d\d(?:\.(\d+))?Z$/);
			function e8(e) {
				if ((e || _(), "string" == typeof e)) {
					let t = 0,
						n = e3.exec(e);
					if ((n || _(), n[1])) {
						let e = n[1];
						t = Number((e = (e + "000000000").substr(0, 9)));
					}
					return { seconds: Math.floor(new Date(e).getTime() / 1e3), nanos: t };
				}
				return { seconds: e4(e.seconds), nanos: e4(e.nanos) };
			}
			function e4(e) {
				return "number" == typeof e ? e : "string" == typeof e ? Number(e) : 0;
			}
			function e6(e) {
				return "string" == typeof e
					? e5.fromBase64String(e)
					: e5.fromUint8Array(e);
			}
			function e9(e) {
				var t, n;
				return (
					"server_timestamp" ===
					(null ===
						(n = (
							(null === (t = null == e ? void 0 : e.mapValue) || void 0 === t
								? void 0
								: t.fields) || {}
						).__type__) || void 0 === n
						? void 0
						: n.stringValue)
				);
			}
			function e7(e) {
				const t = e.mapValue.fields.__previous_value__;
				return e9(t) ? e7(t) : t;
			}
			function te(e) {
				const t = e8(e.mapValue.fields.__local_write_time__.timestampValue);
				return new U(t.seconds, t.nanos);
			}
			class tt {
				constructor(e, t, n, r, i, s, a, o, l) {
					(this.databaseId = e),
						(this.appId = t),
						(this.persistenceKey = n),
						(this.host = r),
						(this.ssl = i),
						(this.forceLongPolling = s),
						(this.autoDetectLongPolling = a),
						(this.longPollingOptions = o),
						(this.useFetchStreams = l);
				}
			}
			class tn {
				constructor(e, t) {
					(this.projectId = e), (this.database = t || "(default)");
				}
				static empty() {
					return new tn("", "");
				}
				get isDefaultDatabase() {
					return "(default)" === this.database;
				}
				isEqual(e) {
					return (
						e instanceof tn &&
						e.projectId === this.projectId &&
						e.database === this.database
					);
				}
			}
			const tr = {
					mapValue: { fields: { __type__: { stringValue: "__max__" } } },
				},
				ti = { nullValue: "NULL_VALUE" };
			function ts(e) {
				return "nullValue" in e
					? 0
					: "booleanValue" in e
						? 1
						: "integerValue" in e || "doubleValue" in e
							? 2
							: "timestampValue" in e
								? 3
								: "stringValue" in e
									? 5
									: "bytesValue" in e
										? 6
										: "referenceValue" in e
											? 7
											: "geoPointValue" in e
												? 8
												: "arrayValue" in e
													? 9
													: "mapValue" in e
														? e9(e)
															? 4
															: tI(e)
																? 0x1fffffffffffff
																: tw(e)
																	? 10
																	: 11
														: _();
			}
			function ta(e, t) {
				if (e === t) return !0;
				const n = ts(e);
				if (n !== ts(t)) return !1;
				switch (n) {
					case 0:
					case 0x1fffffffffffff:
						return !0;
					case 1:
						return e.booleanValue === t.booleanValue;
					case 4:
						return te(e).isEqual(te(t));
					case 3:
						return ((e, t) => {
							if (
								"string" == typeof e.timestampValue &&
								"string" == typeof t.timestampValue &&
								e.timestampValue.length === t.timestampValue.length
							)
								return e.timestampValue === t.timestampValue;
							const n = e8(e.timestampValue),
								r = e8(t.timestampValue);
							return n.seconds === r.seconds && n.nanos === r.nanos;
						})(e, t);
					case 5:
						return e.stringValue === t.stringValue;
					case 6:
						return e6(e.bytesValue).isEqual(e6(t.bytesValue));
					case 7:
						return e.referenceValue === t.referenceValue;
					case 8:
						return (
							e4(e.geoPointValue.latitude) === e4(t.geoPointValue.latitude) &&
							e4(e.geoPointValue.longitude) === e4(t.geoPointValue.longitude)
						);
					case 2:
						return ((e, t) => {
							if ("integerValue" in e && "integerValue" in t)
								return e4(e.integerValue) === e4(t.integerValue);
							if ("doubleValue" in e && "doubleValue" in t) {
								const n = e4(e.doubleValue),
									r = e4(t.doubleValue);
								return n === r ? ev(n) === ev(r) : isNaN(n) && isNaN(r);
							}
							return !1;
						})(e, t);
					case 9:
						return P(e.arrayValue.values || [], t.arrayValue.values || [], ta);
					case 10:
					case 11:
						return ((e, t) => {
							const n = e.mapValue.fields || {},
								r = t.mapValue.fields || {};
							if (eQ(n) !== eQ(r)) return !1;
							for (const e in n)
								if (n.hasOwnProperty(e) && (void 0 === r[e] || !ta(n[e], r[e])))
									return !1;
							return !0;
						})(e, t);
					default:
						return _();
				}
			}
			function to(e, t) {
				return void 0 !== (e.values || []).find((e) => ta(e, t));
			}
			function tl(e, t) {
				if (e === t) return 0;
				const n = ts(e),
					r = ts(t);
				if (n !== r) return L(n, r);
				switch (n) {
					case 0:
					case 0x1fffffffffffff:
						return 0;
					case 1:
						return L(e.booleanValue, t.booleanValue);
					case 2:
						return ((e, t) => {
							const n = e4(e.integerValue || e.doubleValue),
								r = e4(t.integerValue || t.doubleValue);
							return n < r
								? -1
								: n > r
									? 1
									: n === r
										? 0
										: isNaN(n)
											? isNaN(r)
												? 0
												: -1
											: 1;
						})(e, t);
					case 3:
						return tu(e.timestampValue, t.timestampValue);
					case 4:
						return tu(te(e), te(t));
					case 5:
						return L(e.stringValue, t.stringValue);
					case 6:
						return ((e, t) => {
							const n = e6(e),
								r = e6(t);
							return n.compareTo(r);
						})(e.bytesValue, t.bytesValue);
					case 7:
						return ((e, t) => {
							const n = e.split("/"),
								r = t.split("/");
							for (let e = 0; e < n.length && e < r.length; e++) {
								const t = L(n[e], r[e]);
								if (0 !== t) return t;
							}
							return L(n.length, r.length);
						})(e.referenceValue, t.referenceValue);
					case 8:
						return ((e, t) => {
							const n = L(e4(e.latitude), e4(t.latitude));
							return 0 !== n ? n : L(e4(e.longitude), e4(t.longitude));
						})(e.geoPointValue, t.geoPointValue);
					case 9:
						return th(e.arrayValue, t.arrayValue);
					case 10:
						return ((e, t) => {
							var n, r, i, s;
							const a = e.fields || {},
								o = t.fields || {},
								l =
									null === (n = a.value) || void 0 === n
										? void 0
										: n.arrayValue,
								u =
									null === (r = o.value) || void 0 === r
										? void 0
										: r.arrayValue,
								h = L(
									(null === (i = null == l ? void 0 : l.values) || void 0 === i
										? void 0
										: i.length) || 0,
									(null === (s = null == u ? void 0 : u.values) || void 0 === s
										? void 0
										: s.length) || 0,
								);
							return 0 !== h ? h : th(l, u);
						})(e.mapValue, t.mapValue);
					case 11:
						return ((e, t) => {
							if (e === tr.mapValue && t === tr.mapValue) return 0;
							if (e === tr.mapValue) return 1;
							if (t === tr.mapValue) return -1;
							const n = e.fields || {},
								r = Object.keys(n),
								i = t.fields || {},
								s = Object.keys(i);
							r.sort(), s.sort();
							for (let e = 0; e < r.length && e < s.length; ++e) {
								const t = L(r[e], s[e]);
								if (0 !== t) return t;
								const a = tl(n[r[e]], i[s[e]]);
								if (0 !== a) return a;
							}
							return L(r.length, s.length);
						})(e.mapValue, t.mapValue);
					default:
						throw _();
				}
			}
			function tu(e, t) {
				if (
					"string" == typeof e &&
					"string" == typeof t &&
					e.length === t.length
				)
					return L(e, t);
				const n = e8(e),
					r = e8(t),
					i = L(n.seconds, r.seconds);
				return 0 !== i ? i : L(n.nanos, r.nanos);
			}
			function th(e, t) {
				const n = e.values || [],
					r = t.values || [];
				for (let e = 0; e < n.length && e < r.length; ++e) {
					const t = tl(n[e], r[e]);
					if (t) return t;
				}
				return L(n.length, r.length);
			}
			function tc(e) {
				var t, n;
				return "nullValue" in e
					? "null"
					: "booleanValue" in e
						? "" + e.booleanValue
						: "integerValue" in e
							? "" + e.integerValue
							: "doubleValue" in e
								? "" + e.doubleValue
								: "timestampValue" in e
									? ((e) => {
											const t = e8(e);
											return `time(${t.seconds},${t.nanos})`;
										})(e.timestampValue)
									: "stringValue" in e
										? e.stringValue
										: "bytesValue" in e
											? e6(e.bytesValue).toBase64()
											: "referenceValue" in e
												? ((t = e.referenceValue), $.fromName(t).toString())
												: "geoPointValue" in e
													? ((n = e.geoPointValue),
														`geo(${n.latitude},${n.longitude})`)
													: "arrayValue" in e
														? ((e) => {
																let t = "[",
																	n = !0;
																for (const r of e.values || [])
																	n ? (n = !1) : (t += ","), (t += tc(r));
																return t + "]";
															})(e.arrayValue)
														: "mapValue" in e
															? ((e) => {
																	let t = Object.keys(e.fields || {}).sort(),
																		n = "{",
																		r = !0;
																	for (const i of t)
																		r ? (r = !1) : (n += ","),
																			(n += `${i}:${tc(e.fields[i])}`);
																	return n + "}";
																})(e.mapValue)
															: _();
			}
			function td(e, t) {
				return {
					referenceValue: `projects/${e.projectId}/databases/${e.database}/documents/${t.path.canonicalString()}`,
				};
			}
			function tf(e) {
				return !!e && "integerValue" in e;
			}
			function tm(e) {
				return !!e && "arrayValue" in e;
			}
			function tg(e) {
				return !!e && "nullValue" in e;
			}
			function tp(e) {
				return !!e && "doubleValue" in e && isNaN(Number(e.doubleValue));
			}
			function ty(e) {
				return !!e && "mapValue" in e;
			}
			function tw(e) {
				var t, n;
				return (
					"__vector__" ===
					(null ===
						(n = (
							(null === (t = null == e ? void 0 : e.mapValue) || void 0 === t
								? void 0
								: t.fields) || {}
						).__type__) || void 0 === n
						? void 0
						: n.stringValue)
				);
			}
			function tv(e) {
				if (e.geoPointValue)
					return { geoPointValue: Object.assign({}, e.geoPointValue) };
				if (e.timestampValue && "object" == typeof e.timestampValue)
					return { timestampValue: Object.assign({}, e.timestampValue) };
				if (e.mapValue) {
					const t = { mapValue: { fields: {} } };
					return (
						ej(e.mapValue.fields, (e, n) => (t.mapValue.fields[e] = tv(n))), t
					);
				}
				if (e.arrayValue) {
					const t = { arrayValue: { values: [] } };
					for (let n = 0; n < (e.arrayValue.values || []).length; ++n)
						t.arrayValue.values[n] = tv(e.arrayValue.values[n]);
					return t;
				}
				return Object.assign({}, e);
			}
			function tI(e) {
				return (
					"__max__" ===
					(((e.mapValue || {}).fields || {}).__type__ || {}).stringValue
				);
			}
			const tT = {
				mapValue: {
					fields: {
						__type__: { stringValue: "__vector__" },
						value: { arrayValue: {} },
					},
				},
			};
			function tE(e, t) {
				const n = tl(e.value, t.value);
				return 0 !== n
					? n
					: e.inclusive && !t.inclusive
						? -1
						: !e.inclusive && t.inclusive
							? 1
							: 0;
			}
			function tb(e, t) {
				const n = tl(e.value, t.value);
				return 0 !== n
					? n
					: e.inclusive && !t.inclusive
						? 1
						: !e.inclusive && t.inclusive
							? -1
							: 0;
			}
			class t_ {
				constructor(e) {
					this.value = e;
				}
				static empty() {
					return new t_({ mapValue: {} });
				}
				field(e) {
					if (e.isEmpty()) return this.value;
					{
						let t = this.value;
						for (let n = 0; n < e.length - 1; ++n)
							if (!ty((t = (t.mapValue.fields || {})[e.get(n)]))) return null;
						return (t = (t.mapValue.fields || {})[e.lastSegment()]) || null;
					}
				}
				set(e, t) {
					this.getFieldsMap(e.popLast())[e.lastSegment()] = tv(t);
				}
				setAll(e) {
					let t = G.emptyPath(),
						n = {},
						r = [];
					e.forEach((e, i) => {
						if (!t.isImmediateParentOf(i)) {
							const e = this.getFieldsMap(t);
							this.applyChanges(e, n, r), (n = {}), (r = []), (t = i.popLast());
						}
						e ? (n[i.lastSegment()] = tv(e)) : r.push(i.lastSegment());
					});
					const i = this.getFieldsMap(t);
					this.applyChanges(i, n, r);
				}
				delete(e) {
					const t = this.field(e.popLast());
					ty(t) &&
						t.mapValue.fields &&
						delete t.mapValue.fields[e.lastSegment()];
				}
				isEqual(e) {
					return ta(this.value, e.value);
				}
				getFieldsMap(e) {
					let t = this.value;
					t.mapValue.fields || (t.mapValue = { fields: {} });
					for (let n = 0; n < e.length; ++n) {
						let r = t.mapValue.fields[e.get(n)];
						(ty(r) && r.mapValue.fields) ||
							((r = { mapValue: { fields: {} } }),
							(t.mapValue.fields[e.get(n)] = r)),
							(t = r);
					}
					return t.mapValue.fields;
				}
				applyChanges(e, t, n) {
					for (const r of (ej(t, (t, n) => (e[t] = n)), n)) delete e[r];
				}
				clone() {
					return new t_(tv(this.value));
				}
			}
			class tS {
				constructor(e, t, n, r, i, s, a) {
					(this.key = e),
						(this.documentType = t),
						(this.version = n),
						(this.readTime = r),
						(this.createTime = i),
						(this.data = s),
						(this.documentState = a);
				}
				static newInvalidDocument(e) {
					return new tS(e, 0, q.min(), q.min(), q.min(), t_.empty(), 0);
				}
				static newFoundDocument(e, t, n, r) {
					return new tS(e, 1, t, q.min(), n, r, 0);
				}
				static newNoDocument(e, t) {
					return new tS(e, 2, t, q.min(), q.min(), t_.empty(), 0);
				}
				static newUnknownDocument(e, t) {
					return new tS(e, 3, t, q.min(), q.min(), t_.empty(), 2);
				}
				convertToFoundDocument(e, t) {
					return (
						this.createTime.isEqual(q.min()) &&
							(2 === this.documentType || 0 === this.documentType) &&
							(this.createTime = e),
						(this.version = e),
						(this.documentType = 1),
						(this.data = t),
						(this.documentState = 0),
						this
					);
				}
				convertToNoDocument(e) {
					return (
						(this.version = e),
						(this.documentType = 2),
						(this.data = t_.empty()),
						(this.documentState = 0),
						this
					);
				}
				convertToUnknownDocument(e) {
					return (
						(this.version = e),
						(this.documentType = 3),
						(this.data = t_.empty()),
						(this.documentState = 2),
						this
					);
				}
				setHasCommittedMutations() {
					return (this.documentState = 2), this;
				}
				setHasLocalMutations() {
					return (this.documentState = 1), (this.version = q.min()), this;
				}
				setReadTime(e) {
					return (this.readTime = e), this;
				}
				get hasLocalMutations() {
					return 1 === this.documentState;
				}
				get hasCommittedMutations() {
					return 2 === this.documentState;
				}
				get hasPendingWrites() {
					return this.hasLocalMutations || this.hasCommittedMutations;
				}
				isValidDocument() {
					return 0 !== this.documentType;
				}
				isFoundDocument() {
					return 1 === this.documentType;
				}
				isNoDocument() {
					return 2 === this.documentType;
				}
				isUnknownDocument() {
					return 3 === this.documentType;
				}
				isEqual(e) {
					return (
						e instanceof tS &&
						this.key.isEqual(e.key) &&
						this.version.isEqual(e.version) &&
						this.documentType === e.documentType &&
						this.documentState === e.documentState &&
						this.data.isEqual(e.data)
					);
				}
				mutableCopy() {
					return new tS(
						this.key,
						this.documentType,
						this.version,
						this.readTime,
						this.createTime,
						this.data.clone(),
						this.documentState,
					);
				}
				toString() {
					return `Document(${this.key}, ${this.version}, ${JSON.stringify(this.data.value)}, {createTime: ${this.createTime}}), {documentType: ${this.documentType}}), {documentState: ${this.documentState}})`;
				}
			}
			class tx {
				constructor(e, t) {
					(this.position = e), (this.inclusive = t);
				}
			}
			function tD(e, t, n) {
				let r = 0;
				for (let i = 0; i < e.position.length; i++) {
					const s = t[i],
						a = e.position[i];
					if (
						((r = s.field.isKeyField()
							? $.comparator($.fromName(a.referenceValue), n.key)
							: tl(a, n.data.field(s.field))),
						"desc" === s.dir && (r *= -1),
						0 !== r)
					)
						break;
				}
				return r;
			}
			function tC(e, t) {
				if (null === e) return null === t;
				if (
					null === t ||
					e.inclusive !== t.inclusive ||
					e.position.length !== t.position.length
				)
					return !1;
				for (let n = 0; n < e.position.length; n++)
					if (!ta(e.position[n], t.position[n])) return !1;
				return !0;
			}
			class tN {
				constructor(e, t = "asc") {
					(this.field = e), (this.dir = t);
				}
			}
			class tk {}
			class tA extends tk {
				constructor(e, t, n) {
					super(), (this.field = e), (this.op = t), (this.value = n);
				}
				static create(e, t, n) {
					return e.isKeyField()
						? "in" === t || "not-in" === t
							? this.createKeyFieldInFilter(e, t, n)
							: new tP(e, t, n)
						: "array-contains" === t
							? new tK(e, n)
							: "in" === t
								? new tz(e, n)
								: "not-in" === t
									? new tG(e, n)
									: "array-contains-any" === t
										? new t$(e, n)
										: new tA(e, t, n);
				}
				static createKeyFieldInFilter(e, t, n) {
					return "in" === t ? new tU(e, n) : new tq(e, n);
				}
				matches(e) {
					const t = e.data.field(this.field);
					return "!=" === this.op
						? null !== t && this.matchesComparison(tl(t, this.value))
						: null !== t &&
								ts(this.value) === ts(t) &&
								this.matchesComparison(tl(t, this.value));
				}
				matchesComparison(e) {
					switch (this.op) {
						case "<":
							return e < 0;
						case "<=":
							return e <= 0;
						case "==":
							return 0 === e;
						case "!=":
							return 0 !== e;
						case ">":
							return e > 0;
						case ">=":
							return e >= 0;
						default:
							return _();
					}
				}
				isInequality() {
					return ["<", "<=", ">", ">=", "!=", "not-in"].indexOf(this.op) >= 0;
				}
				getFlattenedFilters() {
					return [this];
				}
				getFilters() {
					return [this];
				}
			}
			class tV extends tk {
				constructor(e, t) {
					super(), (this.filters = e), (this.op = t), (this.ae = null);
				}
				static create(e, t) {
					return new tV(e, t);
				}
				matches(e) {
					return tR(this)
						? void 0 === this.filters.find((t) => !t.matches(e))
						: void 0 !== this.filters.find((t) => t.matches(e));
				}
				getFlattenedFilters() {
					return (
						null !== this.ae ||
							(this.ae = this.filters.reduce(
								(e, t) => e.concat(t.getFlattenedFilters()),
								[],
							)),
						this.ae
					);
				}
				getFilters() {
					return Object.assign([], this.filters);
				}
			}
			function tR(e) {
				return "and" === e.op;
			}
			function tO(e) {
				return "or" === e.op;
			}
			function tM(e) {
				return tF(e) && tR(e);
			}
			function tF(e) {
				for (const t of e.filters) if (t instanceof tV) return !1;
				return !0;
			}
			function tL(e, t) {
				const n = e.filters.concat(t);
				return tV.create(n, e.op);
			}
			class tP extends tA {
				constructor(e, t, n) {
					super(e, t, n), (this.key = $.fromName(n.referenceValue));
				}
				matches(e) {
					const t = $.comparator(e.key, this.key);
					return this.matchesComparison(t);
				}
			}
			class tU extends tA {
				constructor(e, t) {
					super(e, "in", t), (this.keys = tB("in", t));
				}
				matches(e) {
					return this.keys.some((t) => t.isEqual(e.key));
				}
			}
			class tq extends tA {
				constructor(e, t) {
					super(e, "not-in", t), (this.keys = tB("not-in", t));
				}
				matches(e) {
					return !this.keys.some((t) => t.isEqual(e.key));
				}
			}
			function tB(e, t) {
				var n;
				return (
					(null === (n = t.arrayValue) || void 0 === n ? void 0 : n.values) ||
					[]
				).map((e) => $.fromName(e.referenceValue));
			}
			class tK extends tA {
				constructor(e, t) {
					super(e, "array-contains", t);
				}
				matches(e) {
					const t = e.data.field(this.field);
					return tm(t) && to(t.arrayValue, this.value);
				}
			}
			class tz extends tA {
				constructor(e, t) {
					super(e, "in", t);
				}
				matches(e) {
					const t = e.data.field(this.field);
					return null !== t && to(this.value.arrayValue, t);
				}
			}
			class tG extends tA {
				constructor(e, t) {
					super(e, "not-in", t);
				}
				matches(e) {
					if (to(this.value.arrayValue, { nullValue: "NULL_VALUE" })) return !1;
					const t = e.data.field(this.field);
					return null !== t && !to(this.value.arrayValue, t);
				}
			}
			class t$ extends tA {
				constructor(e, t) {
					super(e, "array-contains-any", t);
				}
				matches(e) {
					const t = e.data.field(this.field);
					return (
						!(!tm(t) || !t.arrayValue.values) &&
						t.arrayValue.values.some((e) => to(this.value.arrayValue, e))
					);
				}
			}
			class tQ {
				constructor(e, t = null, n = [], r = [], i = null, s = null, a = null) {
					(this.path = e),
						(this.collectionGroup = t),
						(this.orderBy = n),
						(this.filters = r),
						(this.limit = i),
						(this.startAt = s),
						(this.endAt = a),
						(this.ue = null);
				}
			}
			function tj(e, t = null, n = [], r = [], i = null, s = null, a = null) {
				return new tQ(e, t, n, r, i, s, a);
			}
			function tW(e) {
				if (null === e.ue) {
					let t = e.path.canonicalString();
					null !== e.collectionGroup && (t += "|cg:" + e.collectionGroup),
						(t += "|f:"),
						(t += e.filters
							.map((e) =>
								(function e(t) {
									if (t instanceof tA)
										return (
											t.field.canonicalString() + t.op.toString() + tc(t.value)
										);
									if (tM(t)) return t.filters.map((t) => e(t)).join(",");
									{
										const n = t.filters.map((t) => e(t)).join(",");
										return `${t.op}(${n})`;
									}
								})(e),
							)
							.join(",")),
						(t += "|ob:"),
						(t += e.orderBy
							.map((e) => e.field.canonicalString() + e.dir)
							.join(",")),
						ew(e.limit) || ((t += "|l:"), (t += e.limit)),
						e.startAt &&
							((t += "|lb:"),
							(t += e.startAt.inclusive ? "b:" : "a:"),
							(t += e.startAt.position.map((e) => tc(e)).join(","))),
						e.endAt &&
							((t += "|ub:"),
							(t += e.endAt.inclusive ? "a:" : "b:"),
							(t += e.endAt.position.map((e) => tc(e)).join(","))),
						(e.ue = t);
				}
				return e.ue;
			}
			function tJ(e, t) {
				if (e.limit !== t.limit || e.orderBy.length !== t.orderBy.length)
					return !1;
				for (let i = 0; i < e.orderBy.length; i++) {
					var n, r;
					if (
						((n = e.orderBy[i]),
						(r = t.orderBy[i]),
						!(n.dir === r.dir && n.field.isEqual(r.field)))
					)
						return !1;
				}
				if (e.filters.length !== t.filters.length) return !1;
				for (let n = 0; n < e.filters.length; n++)
					if (
						!(function e(t, n) {
							return t instanceof tA
								? n instanceof tA &&
										t.op === n.op &&
										t.field.isEqual(n.field) &&
										ta(t.value, n.value)
								: t instanceof tV
									? n instanceof tV &&
										t.op === n.op &&
										t.filters.length === n.filters.length &&
										t.filters.reduce((t, r, i) => t && e(r, n.filters[i]), !0)
									: void _();
						})(e.filters[n], t.filters[n])
					)
						return !1;
				return (
					e.collectionGroup === t.collectionGroup &&
					!!e.path.isEqual(t.path) &&
					!!tC(e.startAt, t.startAt) &&
					tC(e.endAt, t.endAt)
				);
			}
			function tH(e) {
				return (
					$.isDocumentKey(e.path) &&
					null === e.collectionGroup &&
					0 === e.filters.length
				);
			}
			function tY(e, t) {
				return e.filters.filter((e) => e instanceof tA && e.field.isEqual(t));
			}
			function tX(e, t, n) {
				let r = ti,
					i = !0;
				for (const n of tY(e, t)) {
					let e = ti,
						t = !0;
					switch (n.op) {
						case "<":
						case "<=":
							var s;
							e =
								"nullValue" in (s = n.value)
									? ti
									: "booleanValue" in s
										? { booleanValue: !1 }
										: "integerValue" in s || "doubleValue" in s
											? { doubleValue: Number.NaN }
											: "timestampValue" in s
												? {
														timestampValue: {
															seconds: Number.MIN_SAFE_INTEGER,
														},
													}
												: "stringValue" in s
													? { stringValue: "" }
													: "bytesValue" in s
														? { bytesValue: "" }
														: "referenceValue" in s
															? td(tn.empty(), $.empty())
															: "geoPointValue" in s
																? {
																		geoPointValue: {
																			latitude: -90,
																			longitude: -180,
																		},
																	}
																: "arrayValue" in s
																	? { arrayValue: {} }
																	: "mapValue" in s
																		? tw(s)
																			? tT
																			: { mapValue: {} }
																		: _();
							break;
						case "==":
						case "in":
						case ">=":
							e = n.value;
							break;
						case ">":
							(e = n.value), (t = !1);
							break;
						case "!=":
						case "not-in":
							e = ti;
					}
					0 > tE({ value: r, inclusive: i }, { value: e, inclusive: t }) &&
						((r = e), (i = t));
				}
				if (null !== n) {
					for (let s = 0; s < e.orderBy.length; ++s)
						if (e.orderBy[s].field.isEqual(t)) {
							const e = n.position[s];
							0 >
								tE(
									{ value: r, inclusive: i },
									{ value: e, inclusive: n.inclusive },
								) && ((r = e), (i = n.inclusive));
							break;
						}
				}
				return { value: r, inclusive: i };
			}
			function tZ(e, t, n) {
				let r = tr,
					i = !0;
				for (const n of tY(e, t)) {
					let e = tr,
						t = !0;
					switch (n.op) {
						case ">=":
						case ">":
							var s;
							(e =
								"nullValue" in (s = n.value)
									? { booleanValue: !1 }
									: "booleanValue" in s
										? { doubleValue: Number.NaN }
										: "integerValue" in s || "doubleValue" in s
											? { timestampValue: { seconds: Number.MIN_SAFE_INTEGER } }
											: "timestampValue" in s
												? { stringValue: "" }
												: "stringValue" in s
													? { bytesValue: "" }
													: "bytesValue" in s
														? td(tn.empty(), $.empty())
														: "referenceValue" in s
															? {
																	geoPointValue: {
																		latitude: -90,
																		longitude: -180,
																	},
																}
															: "geoPointValue" in s
																? { arrayValue: {} }
																: "arrayValue" in s
																	? tT
																	: "mapValue" in s
																		? tw(s)
																			? { mapValue: {} }
																			: tr
																		: _()),
								(t = !1);
							break;
						case "==":
						case "in":
						case "<=":
							e = n.value;
							break;
						case "<":
							(e = n.value), (t = !1);
							break;
						case "!=":
						case "not-in":
							e = tr;
					}
					tb({ value: r, inclusive: i }, { value: e, inclusive: t }) > 0 &&
						((r = e), (i = t));
				}
				if (null !== n) {
					for (let s = 0; s < e.orderBy.length; ++s)
						if (e.orderBy[s].field.isEqual(t)) {
							const e = n.position[s];
							tb(
								{ value: r, inclusive: i },
								{ value: e, inclusive: n.inclusive },
							) > 0 && ((r = e), (i = n.inclusive));
							break;
						}
				}
				return { value: r, inclusive: i };
			}
			class t0 {
				constructor(
					e,
					t = null,
					n = [],
					r = [],
					i = null,
					s = "F",
					a = null,
					o = null,
				) {
					(this.path = e),
						(this.collectionGroup = t),
						(this.explicitOrderBy = n),
						(this.filters = r),
						(this.limit = i),
						(this.limitType = s),
						(this.startAt = a),
						(this.endAt = o),
						(this.ce = null),
						(this.le = null),
						(this.he = null),
						this.startAt,
						this.endAt;
				}
			}
			function t1(e) {
				return new t0(e);
			}
			function t2(e) {
				return (
					0 === e.filters.length &&
					null === e.limit &&
					null == e.startAt &&
					null == e.endAt &&
					(0 === e.explicitOrderBy.length ||
						(1 === e.explicitOrderBy.length &&
							e.explicitOrderBy[0].field.isKeyField()))
				);
			}
			function t5(e) {
				return null !== e.collectionGroup;
			}
			function t3(e) {
				if (null === e.ce) {
					let t;
					e.ce = [];
					const n = new Set();
					for (const t of e.explicitOrderBy)
						e.ce.push(t), n.add(t.field.canonicalString());
					const r =
						e.explicitOrderBy.length > 0
							? e.explicitOrderBy[e.explicitOrderBy.length - 1].dir
							: "asc";
					((t = new eX(G.comparator)),
					e.filters.forEach((e) => {
						e.getFlattenedFilters().forEach((e) => {
							e.isInequality() && (t = t.add(e.field));
						});
					}),
					t).forEach((t) => {
						n.has(t.canonicalString()) ||
							t.isKeyField() ||
							e.ce.push(new tN(t, r));
					}),
						n.has(G.keyField().canonicalString()) ||
							e.ce.push(new tN(G.keyField(), r));
				}
				return e.ce;
			}
			function t8(e) {
				return e.le || (e.le = t4(e, t3(e))), e.le;
			}
			function t4(e, t) {
				if ("F" === e.limitType)
					return tj(
						e.path,
						e.collectionGroup,
						t,
						e.filters,
						e.limit,
						e.startAt,
						e.endAt,
					);
				{
					t = t.map((e) => {
						const t = "desc" === e.dir ? "asc" : "desc";
						return new tN(e.field, t);
					});
					const n = e.endAt
							? new tx(e.endAt.position, e.endAt.inclusive)
							: null,
						r = e.startAt
							? new tx(e.startAt.position, e.startAt.inclusive)
							: null;
					return tj(e.path, e.collectionGroup, t, e.filters, e.limit, n, r);
				}
			}
			function t6(e, t) {
				const n = e.filters.concat([t]);
				return new t0(
					e.path,
					e.collectionGroup,
					e.explicitOrderBy.slice(),
					n,
					e.limit,
					e.limitType,
					e.startAt,
					e.endAt,
				);
			}
			function t9(e, t, n) {
				return new t0(
					e.path,
					e.collectionGroup,
					e.explicitOrderBy.slice(),
					e.filters.slice(),
					t,
					n,
					e.startAt,
					e.endAt,
				);
			}
			function t7(e, t) {
				return tJ(t8(e), t8(t)) && e.limitType === t.limitType;
			}
			function ne(e) {
				return `${tW(t8(e))}|lt:${e.limitType}`;
			}
			function nt(e) {
				var t;
				let n;
				return `Query(target=${
					((n = (t = t8(e)).path.canonicalString()),
					null !== t.collectionGroup &&
						(n += " collectionGroup=" + t.collectionGroup),
					t.filters.length > 0 &&
						(n += `, filters: [${t.filters
							.map((e) =>
								(function e(t) {
									return t instanceof tA
										? `${t.field.canonicalString()} ${t.op} ${tc(t.value)}`
										: t instanceof tV
											? t.op.toString() +
												" {" +
												t.getFilters().map(e).join(" ,") +
												"}"
											: "Filter";
								})(e),
							)
							.join(", ")}]`),
					ew(t.limit) || (n += ", limit: " + t.limit),
					t.orderBy.length > 0 &&
						(n += `, orderBy: [${t.orderBy.map((e) => `${e.field.canonicalString()} (${e.dir})`).join(", ")}]`),
					t.startAt &&
						((n += ", startAt: "),
						(n += t.startAt.inclusive ? "b:" : "a:"),
						(n += t.startAt.position.map((e) => tc(e)).join(","))),
					t.endAt &&
						((n += ", endAt: "),
						(n += t.endAt.inclusive ? "a:" : "b:"),
						(n += t.endAt.position.map((e) => tc(e)).join(","))),
					`Target(${n})`)
				}; limitType=${e.limitType})`;
			}
			function nn(e, t) {
				return (
					t.isFoundDocument() &&
					((e, t) => {
						const n = t.key.path;
						return null !== e.collectionGroup
							? t.key.hasCollectionId(e.collectionGroup) && e.path.isPrefixOf(n)
							: $.isDocumentKey(e.path)
								? e.path.isEqual(n)
								: e.path.isImmediateParentOf(n);
					})(e, t) &&
					((e, t) => {
						for (const n of t3(e))
							if (!n.field.isKeyField() && null === t.data.field(n.field))
								return !1;
						return !0;
					})(e, t) &&
					((e, t) => {
						for (const n of e.filters) if (!n.matches(t)) return !1;
						return !0;
					})(e, t) &&
					(!e.startAt ||
						!!((e, t, n) => {
							const r = tD(e, t, n);
							return e.inclusive ? r <= 0 : r < 0;
						})(e.startAt, t3(e), t)) &&
					(!e.endAt ||
						!!((e, t, n) => {
							const r = tD(e, t, n);
							return e.inclusive ? r >= 0 : r > 0;
						})(e.endAt, t3(e), t))
				);
			}
			function nr(e) {
				return (
					e.collectionGroup ||
					(e.path.length % 2 == 1
						? e.path.lastSegment()
						: e.path.get(e.path.length - 2))
				);
			}
			function ni(e) {
				return (t, n) => {
					let r = !1;
					for (const i of t3(e)) {
						const e = ((e, t, n) => {
							const r = e.field.isKeyField()
								? $.comparator(t.key, n.key)
								: ((e, t, n) => {
										const r = t.data.field(e),
											i = n.data.field(e);
										return null !== r && null !== i ? tl(r, i) : _();
									})(e.field, t, n);
							switch (e.dir) {
								case "asc":
									return r;
								case "desc":
									return -1 * r;
								default:
									return _();
							}
						})(i, t, n);
						if (0 !== e) return e;
						r = r || i.field.isKeyField();
					}
					return 0;
				};
			}
			class ns {
				constructor(e, t) {
					(this.mapKeyFn = e),
						(this.equalsFn = t),
						(this.inner = {}),
						(this.innerSize = 0);
				}
				get(e) {
					const t = this.mapKeyFn(e),
						n = this.inner[t];
					if (void 0 !== n) {
						for (const [t, r] of n) if (this.equalsFn(t, e)) return r;
					}
				}
				has(e) {
					return void 0 !== this.get(e);
				}
				set(e, t) {
					const n = this.mapKeyFn(e),
						r = this.inner[n];
					if (void 0 === r)
						return (this.inner[n] = [[e, t]]), void this.innerSize++;
					for (let n = 0; n < r.length; n++)
						if (this.equalsFn(r[n][0], e)) return void (r[n] = [e, t]);
					r.push([e, t]), this.innerSize++;
				}
				delete(e) {
					const t = this.mapKeyFn(e),
						n = this.inner[t];
					if (void 0 === n) return !1;
					for (let r = 0; r < n.length; r++)
						if (this.equalsFn(n[r][0], e))
							return (
								1 === n.length ? delete this.inner[t] : n.splice(r, 1),
								this.innerSize--,
								!0
							);
					return !1;
				}
				forEach(e) {
					ej(this.inner, (t, n) => {
						for (const [t, r] of n) e(t, r);
					});
				}
				isEmpty() {
					return eW(this.inner);
				}
				size() {
					return this.innerSize;
				}
			}
			const na = new eJ($.comparator),
				no = new eJ($.comparator);
			function nl(...e) {
				let t = no;
				for (const n of e) t = t.insert(n.key, n);
				return t;
			}
			function nu(e) {
				let t = no;
				return e.forEach((e, n) => (t = t.insert(e, n.overlayedDocument))), t;
			}
			function nh() {
				return new ns(
					(e) => e.toString(),
					(e, t) => e.isEqual(t),
				);
			}
			const nc = new eJ($.comparator),
				nd = new eX($.comparator);
			function nf(...e) {
				let t = nd;
				for (const n of e) t = t.add(n);
				return t;
			}
			const nm = new eX(L);
			function ng(e, t) {
				if (e.useProto3Json) {
					if (isNaN(t)) return { doubleValue: "NaN" };
					if (t === 1 / 0) return { doubleValue: "Infinity" };
					if (t === -1 / 0) return { doubleValue: "-Infinity" };
				}
				return { doubleValue: ev(t) ? "-0" : t };
			}
			function np(e) {
				return { integerValue: "" + e };
			}
			function ny(e, t) {
				return eI(t) ? np(t) : ng(e, t);
			}
			class nw {
				constructor() {
					this._ = void 0;
				}
			}
			function nv(e, t) {
				return e instanceof nS
					? tf(t) || (t && "doubleValue" in t)
						? t
						: { integerValue: 0 }
					: null;
			}
			class nI extends nw {}
			class nT extends nw {
				constructor(e) {
					super(), (this.elements = e);
				}
			}
			function nE(e, t) {
				const n = nD(t);
				for (const t of e.elements) n.some((e) => ta(e, t)) || n.push(t);
				return { arrayValue: { values: n } };
			}
			class nb extends nw {
				constructor(e) {
					super(), (this.elements = e);
				}
			}
			function n_(e, t) {
				let n = nD(t);
				for (const t of e.elements) n = n.filter((e) => !ta(e, t));
				return { arrayValue: { values: n } };
			}
			class nS extends nw {
				constructor(e, t) {
					super(), (this.serializer = e), (this.Pe = t);
				}
			}
			function nx(e) {
				return e4(e.integerValue || e.doubleValue);
			}
			function nD(e) {
				return tm(e) && e.arrayValue.values ? e.arrayValue.values.slice() : [];
			}
			class nC {
				constructor(e, t) {
					(this.field = e), (this.transform = t);
				}
			}
			class nN {
				constructor(e, t) {
					(this.version = e), (this.transformResults = t);
				}
			}
			class nk {
				constructor(e, t) {
					(this.updateTime = e), (this.exists = t);
				}
				static none() {
					return new nk();
				}
				static exists(e) {
					return new nk(void 0, e);
				}
				static updateTime(e) {
					return new nk(e);
				}
				get isNone() {
					return void 0 === this.updateTime && void 0 === this.exists;
				}
				isEqual(e) {
					return (
						this.exists === e.exists &&
						(this.updateTime
							? !!e.updateTime && this.updateTime.isEqual(e.updateTime)
							: !e.updateTime)
					);
				}
			}
			function nA(e, t) {
				return void 0 !== e.updateTime
					? t.isFoundDocument() && t.version.isEqual(e.updateTime)
					: void 0 === e.exists || e.exists === t.isFoundDocument();
			}
			class nV {}
			function nR(e, t) {
				if (!e.hasLocalMutations || (t && 0 === t.fields.length)) return null;
				if (null === t)
					return e.isNoDocument()
						? new nB(e.key, nk.none())
						: new nF(e.key, e.data, nk.none());
				{
					let n = e.data,
						r = t_.empty(),
						i = new eX(G.comparator);
					for (let e of t.fields)
						if (!i.has(e)) {
							let t = n.field(e);
							null === t &&
								e.length > 1 &&
								((e = e.popLast()), (t = n.field(e))),
								null === t ? r.delete(e) : r.set(e, t),
								(i = i.add(e));
						}
					return new nL(e.key, r, new e1(i.toArray()), nk.none());
				}
			}
			function nO(e, t, n, r) {
				return e instanceof nF
					? ((e, t, n, r) => {
							if (!nA(e.precondition, t)) return n;
							const i = e.value.clone(),
								s = nq(e.fieldTransforms, r, t);
							return (
								i.setAll(s),
								t.convertToFoundDocument(t.version, i).setHasLocalMutations(),
								null
							);
						})(e, t, n, r)
					: e instanceof nL
						? ((e, t, n, r) => {
								if (!nA(e.precondition, t)) return n;
								const i = nq(e.fieldTransforms, r, t),
									s = t.data;
								return (s.setAll(nP(e)),
								s.setAll(i),
								t.convertToFoundDocument(t.version, s).setHasLocalMutations(),
								null === n)
									? null
									: n
											.unionWith(e.fieldMask.fields)
											.unionWith(e.fieldTransforms.map((e) => e.field));
							})(e, t, n, r)
						: nA(e.precondition, t)
							? (t.convertToNoDocument(t.version).setHasLocalMutations(), null)
							: n;
			}
			function nM(e, t) {
				var n, r;
				return (
					e.type === t.type &&
					!!e.key.isEqual(t.key) &&
					!!e.precondition.isEqual(t.precondition) &&
					((n = e.fieldTransforms),
					(r = t.fieldTransforms),
					!!(
						(void 0 === n && void 0 === r) ||
						(!(!n || !r) &&
							P(n, r, (e, t) => {
								var n, r;
								return (
									e.field.isEqual(t.field) &&
									((n = e.transform),
									(r = t.transform),
									(n instanceof nT && r instanceof nT) ||
									(n instanceof nb && r instanceof nb)
										? P(n.elements, r.elements, ta)
										: n instanceof nS && r instanceof nS
											? ta(n.Pe, r.Pe)
											: n instanceof nI && r instanceof nI)
								);
							}))
					)) &&
					(0 === e.type
						? e.value.isEqual(t.value)
						: 1 !== e.type ||
							(e.data.isEqual(t.data) && e.fieldMask.isEqual(t.fieldMask)))
				);
			}
			class nF extends nV {
				constructor(e, t, n, r = []) {
					super(),
						(this.key = e),
						(this.value = t),
						(this.precondition = n),
						(this.fieldTransforms = r),
						(this.type = 0);
				}
				getFieldMask() {
					return null;
				}
			}
			class nL extends nV {
				constructor(e, t, n, r, i = []) {
					super(),
						(this.key = e),
						(this.data = t),
						(this.fieldMask = n),
						(this.precondition = r),
						(this.fieldTransforms = i),
						(this.type = 1);
				}
				getFieldMask() {
					return this.fieldMask;
				}
			}
			function nP(e) {
				const t = new Map();
				return (
					e.fieldMask.fields.forEach((n) => {
						if (!n.isEmpty()) {
							const r = e.data.field(n);
							t.set(n, r);
						}
					}),
					t
				);
			}
			function nU(e, t, n) {
				var r;
				const i = new Map();
				e.length === n.length || _();
				for (let s = 0; s < n.length; s++) {
					const a = e[s],
						o = a.transform,
						l = t.data.field(a.field);
					i.set(
						a.field,
						((r = n[s]),
						o instanceof nT ? nE(o, l) : o instanceof nb ? n_(o, l) : r),
					);
				}
				return i;
			}
			function nq(e, t, n) {
				const r = new Map();
				for (const i of e) {
					const e = i.transform,
						s = n.data.field(i.field);
					r.set(
						i.field,
						e instanceof nI
							? ((e, t) => {
									const n = {
										fields: {
											__type__: { stringValue: "server_timestamp" },
											__local_write_time__: {
												timestampValue: {
													seconds: e.seconds,
													nanos: e.nanoseconds,
												},
											},
										},
									};
									return (
										t && e9(t) && (t = e7(t)),
										t && (n.fields.__previous_value__ = t),
										{ mapValue: n }
									);
								})(t, s)
							: e instanceof nT
								? nE(e, s)
								: e instanceof nb
									? n_(e, s)
									: ((e, t) => {
											const n = nv(e, t),
												r = nx(n) + nx(e.Pe);
											return tf(n) && tf(e.Pe) ? np(r) : ng(e.serializer, r);
										})(e, s),
					);
				}
				return r;
			}
			class nB extends nV {
				constructor(e, t) {
					super(),
						(this.key = e),
						(this.precondition = t),
						(this.type = 2),
						(this.fieldTransforms = []);
				}
				getFieldMask() {
					return null;
				}
			}
			class nK extends nV {
				constructor(e, t) {
					super(),
						(this.key = e),
						(this.precondition = t),
						(this.type = 3),
						(this.fieldTransforms = []);
				}
				getFieldMask() {
					return null;
				}
			}
			class nz {
				constructor(e, t, n, r) {
					(this.batchId = e),
						(this.localWriteTime = t),
						(this.baseMutations = n),
						(this.mutations = r);
				}
				applyToRemoteDocument(e, t) {
					const n = t.mutationResults;
					for (let t = 0; t < this.mutations.length; t++) {
						const i = this.mutations[t];
						if (i.key.isEqual(e.key)) {
							var r;
							(r = n[t]),
								i instanceof nF
									? ((e, t, n) => {
											const r = e.value.clone(),
												i = nU(e.fieldTransforms, t, n.transformResults);
											r.setAll(i),
												t
													.convertToFoundDocument(n.version, r)
													.setHasCommittedMutations();
										})(i, e, r)
									: i instanceof nL
										? ((e, t, n) => {
												if (!nA(e.precondition, t))
													return void t.convertToUnknownDocument(n.version);
												const r = nU(e.fieldTransforms, t, n.transformResults),
													i = t.data;
												i.setAll(nP(e)),
													i.setAll(r),
													t
														.convertToFoundDocument(n.version, i)
														.setHasCommittedMutations();
											})(i, e, r)
										: ((e, t, n) => {
												t.convertToNoDocument(
													n.version,
												).setHasCommittedMutations();
											})(0, e, r);
						}
					}
				}
				applyToLocalView(e, t) {
					for (const n of this.baseMutations)
						n.key.isEqual(e.key) && (t = nO(n, e, t, this.localWriteTime));
					for (const n of this.mutations)
						n.key.isEqual(e.key) && (t = nO(n, e, t, this.localWriteTime));
					return t;
				}
				applyToLocalDocumentSet(e, t) {
					const n = nh();
					return (
						this.mutations.forEach((r) => {
							let i = e.get(r.key),
								s = i.overlayedDocument,
								a = this.applyToLocalView(s, i.mutatedFields),
								o = nR(s, (a = t.has(r.key) ? null : a));
							null !== o && n.set(r.key, o),
								s.isValidDocument() || s.convertToNoDocument(q.min());
						}),
						n
					);
				}
				keys() {
					return this.mutations.reduce((e, t) => e.add(t.key), nf());
				}
				isEqual(e) {
					return (
						this.batchId === e.batchId &&
						P(this.mutations, e.mutations, (e, t) => nM(e, t)) &&
						P(this.baseMutations, e.baseMutations, (e, t) => nM(e, t))
					);
				}
			}
			class nG {
				constructor(e, t, n, r) {
					(this.batch = e),
						(this.commitVersion = t),
						(this.mutationResults = n),
						(this.docVersions = r);
				}
				static from(e, t, n) {
					e.mutations.length === n.length || _();
					let r = nc,
						i = e.mutations;
					for (let e = 0; e < i.length; e++)
						r = r.insert(i[e].key, n[e].version);
					return new nG(e, t, n, r);
				}
			}
			class n$ {
				constructor(e, t) {
					(this.largestBatchId = e), (this.mutation = t);
				}
				getKey() {
					return this.mutation.key;
				}
				isEqual(e) {
					return null !== e && this.mutation === e.mutation;
				}
				toString() {
					return `Overlay{
      largestBatchId: ${this.largestBatchId},
      mutation: ${this.mutation.toString()}
    }`;
				}
			}
			class nQ {
				constructor(e, t) {
					(this.count = e), (this.unchangedNames = t);
				}
			}
			function nj(e) {
				switch (e) {
					default:
						return _();
					case S.CANCELLED:
					case S.UNKNOWN:
					case S.DEADLINE_EXCEEDED:
					case S.RESOURCE_EXHAUSTED:
					case S.INTERNAL:
					case S.UNAVAILABLE:
					case S.UNAUTHENTICATED:
						return !1;
					case S.INVALID_ARGUMENT:
					case S.NOT_FOUND:
					case S.ALREADY_EXISTS:
					case S.PERMISSION_DENIED:
					case S.FAILED_PRECONDITION:
					case S.ABORTED:
					case S.OUT_OF_RANGE:
					case S.UNIMPLEMENTED:
					case S.DATA_LOSS:
						return !0;
				}
			}
			function nW(e) {
				if (void 0 === e) return T("GRPC error has no .code"), S.UNKNOWN;
				switch (e) {
					case r.OK:
						return S.OK;
					case r.CANCELLED:
						return S.CANCELLED;
					case r.UNKNOWN:
						return S.UNKNOWN;
					case r.DEADLINE_EXCEEDED:
						return S.DEADLINE_EXCEEDED;
					case r.RESOURCE_EXHAUSTED:
						return S.RESOURCE_EXHAUSTED;
					case r.INTERNAL:
						return S.INTERNAL;
					case r.UNAVAILABLE:
						return S.UNAVAILABLE;
					case r.UNAUTHENTICATED:
						return S.UNAUTHENTICATED;
					case r.INVALID_ARGUMENT:
						return S.INVALID_ARGUMENT;
					case r.NOT_FOUND:
						return S.NOT_FOUND;
					case r.ALREADY_EXISTS:
						return S.ALREADY_EXISTS;
					case r.PERMISSION_DENIED:
						return S.PERMISSION_DENIED;
					case r.FAILED_PRECONDITION:
						return S.FAILED_PRECONDITION;
					case r.ABORTED:
						return S.ABORTED;
					case r.OUT_OF_RANGE:
						return S.OUT_OF_RANGE;
					case r.UNIMPLEMENTED:
						return S.UNIMPLEMENTED;
					case r.DATA_LOSS:
						return S.DATA_LOSS;
					default:
						return _();
				}
			}
			((i = r || (r = {}))[(i.OK = 0)] = "OK"),
				(i[(i.CANCELLED = 1)] = "CANCELLED"),
				(i[(i.UNKNOWN = 2)] = "UNKNOWN"),
				(i[(i.INVALID_ARGUMENT = 3)] = "INVALID_ARGUMENT"),
				(i[(i.DEADLINE_EXCEEDED = 4)] = "DEADLINE_EXCEEDED"),
				(i[(i.NOT_FOUND = 5)] = "NOT_FOUND"),
				(i[(i.ALREADY_EXISTS = 6)] = "ALREADY_EXISTS"),
				(i[(i.PERMISSION_DENIED = 7)] = "PERMISSION_DENIED"),
				(i[(i.UNAUTHENTICATED = 16)] = "UNAUTHENTICATED"),
				(i[(i.RESOURCE_EXHAUSTED = 8)] = "RESOURCE_EXHAUSTED"),
				(i[(i.FAILED_PRECONDITION = 9)] = "FAILED_PRECONDITION"),
				(i[(i.ABORTED = 10)] = "ABORTED"),
				(i[(i.OUT_OF_RANGE = 11)] = "OUT_OF_RANGE"),
				(i[(i.UNIMPLEMENTED = 12)] = "UNIMPLEMENTED"),
				(i[(i.INTERNAL = 13)] = "INTERNAL"),
				(i[(i.UNAVAILABLE = 14)] = "UNAVAILABLE"),
				(i[(i.DATA_LOSS = 15)] = "DATA_LOSS");
			const nJ = new c.jz([0xffffffff, 0xffffffff], 0);
			function nH(e) {
				const t = new TextEncoder().encode(e),
					n = new c.VV();
				return n.update(t), new Uint8Array(n.digest());
			}
			function nY(e) {
				const t = new DataView(e.buffer),
					n = t.getUint32(0, !0),
					r = t.getUint32(4, !0),
					i = t.getUint32(8, !0),
					s = t.getUint32(12, !0);
				return [new c.jz([n, r], 0), new c.jz([i, s], 0)];
			}
			class nX {
				constructor(e, t, n) {
					if (
						((this.bitmap = e),
						(this.padding = t),
						(this.hashCount = n),
						t < 0 || t >= 8)
					)
						throw new nZ(`Invalid padding: ${t}`);
					if (n < 0 || (e.length > 0 && 0 === this.hashCount))
						throw new nZ(`Invalid hash count: ${n}`);
					if (0 === e.length && 0 !== t)
						throw new nZ(`Invalid padding when bitmap length is 0: ${t}`);
					(this.Ie = 8 * e.length - t), (this.Te = c.jz.fromNumber(this.Ie));
				}
				Ee(e, t, n) {
					let r = e.add(t.multiply(c.jz.fromNumber(n)));
					return (
						1 === r.compare(nJ) &&
							(r = new c.jz([r.getBits(0), r.getBits(1)], 0)),
						r.modulo(this.Te).toNumber()
					);
				}
				de(e) {
					return 0 != (this.bitmap[Math.floor(e / 8)] & (1 << (e % 8)));
				}
				mightContain(e) {
					if (0 === this.Ie) return !1;
					const [t, n] = nY(nH(e));
					for (let e = 0; e < this.hashCount; e++) {
						const r = this.Ee(t, n, e);
						if (!this.de(r)) return !1;
					}
					return !0;
				}
				static create(e, t, n) {
					const r = new nX(
						new Uint8Array(Math.ceil(e / 8)),
						e % 8 == 0 ? 0 : 8 - (e % 8),
						t,
					);
					return n.forEach((e) => r.insert(e)), r;
				}
				insert(e) {
					if (0 === this.Ie) return;
					const [t, n] = nY(nH(e));
					for (let e = 0; e < this.hashCount; e++) {
						const r = this.Ee(t, n, e);
						this.Ae(r);
					}
				}
				Ae(e) {
					const t = Math.floor(e / 8);
					this.bitmap[t] |= 1 << (e % 8);
				}
			}
			class nZ extends Error {
				constructor() {
					super(...arguments), (this.name = "BloomFilterError");
				}
			}
			class n0 {
				constructor(e, t, n, r, i) {
					(this.snapshotVersion = e),
						(this.targetChanges = t),
						(this.targetMismatches = n),
						(this.documentUpdates = r),
						(this.resolvedLimboDocuments = i);
				}
				static createSynthesizedRemoteEventForCurrentChange(e, t, n) {
					const r = new Map();
					return (
						r.set(e, n1.createSynthesizedTargetChangeForCurrentChange(e, t, n)),
						new n0(q.min(), r, new eJ(L), na, nf())
					);
				}
			}
			class n1 {
				constructor(e, t, n, r, i) {
					(this.resumeToken = e),
						(this.current = t),
						(this.addedDocuments = n),
						(this.modifiedDocuments = r),
						(this.removedDocuments = i);
				}
				static createSynthesizedTargetChangeForCurrentChange(e, t, n) {
					return new n1(n, t, nf(), nf(), nf());
				}
			}
			class n2 {
				constructor(e, t, n, r) {
					(this.Re = e),
						(this.removedTargetIds = t),
						(this.key = n),
						(this.Ve = r);
				}
			}
			class n5 {
				constructor(e, t) {
					(this.targetId = e), (this.me = t);
				}
			}
			class n3 {
				constructor(e, t, n = e5.EMPTY_BYTE_STRING, r = null) {
					(this.state = e),
						(this.targetIds = t),
						(this.resumeToken = n),
						(this.cause = r);
				}
			}
			class n8 {
				constructor() {
					(this.fe = 0),
						(this.ge = n9()),
						(this.pe = e5.EMPTY_BYTE_STRING),
						(this.ye = !1),
						(this.we = !0);
				}
				get current() {
					return this.ye;
				}
				get resumeToken() {
					return this.pe;
				}
				get Se() {
					return 0 !== this.fe;
				}
				get be() {
					return this.we;
				}
				De(e) {
					e.approximateByteSize() > 0 && ((this.we = !0), (this.pe = e));
				}
				ve() {
					let e = nf(),
						t = nf(),
						n = nf();
					return (
						this.ge.forEach((r, i) => {
							switch (i) {
								case 0:
									e = e.add(r);
									break;
								case 2:
									t = t.add(r);
									break;
								case 1:
									n = n.add(r);
									break;
								default:
									_();
							}
						}),
						new n1(this.pe, this.ye, e, t, n)
					);
				}
				Ce() {
					(this.we = !1), (this.ge = n9());
				}
				Fe(e, t) {
					(this.we = !0), (this.ge = this.ge.insert(e, t));
				}
				Me(e) {
					(this.we = !0), (this.ge = this.ge.remove(e));
				}
				xe() {
					this.fe += 1;
				}
				Oe() {
					(this.fe -= 1), this.fe >= 0 || _();
				}
				Ne() {
					(this.we = !0), (this.ye = !0);
				}
			}
			class n4 {
				constructor(e) {
					(this.Le = e),
						(this.Be = new Map()),
						(this.ke = na),
						(this.qe = n6()),
						(this.Qe = new eJ(L));
				}
				Ke(e) {
					for (const t of e.Re)
						e.Ve && e.Ve.isFoundDocument()
							? this.$e(t, e.Ve)
							: this.Ue(t, e.key, e.Ve);
					for (const t of e.removedTargetIds) this.Ue(t, e.key, e.Ve);
				}
				We(e) {
					this.forEachTarget(e, (t) => {
						const n = this.Ge(t);
						switch (e.state) {
							case 0:
								this.ze(t) && n.De(e.resumeToken);
								break;
							case 1:
								n.Oe(), n.Se || n.Ce(), n.De(e.resumeToken);
								break;
							case 2:
								n.Oe(), n.Se || this.removeTarget(t);
								break;
							case 3:
								this.ze(t) && (n.Ne(), n.De(e.resumeToken));
								break;
							case 4:
								this.ze(t) && (this.je(t), n.De(e.resumeToken));
								break;
							default:
								_();
						}
					});
				}
				forEachTarget(e, t) {
					e.targetIds.length > 0
						? e.targetIds.forEach(t)
						: this.Be.forEach((e, n) => {
								this.ze(n) && t(n);
							});
				}
				He(e) {
					const t = e.targetId,
						n = e.me.count,
						r = this.Je(t);
					if (r) {
						const i = r.target;
						if (tH(i)) {
							if (0 === n) {
								const e = new $(i.path);
								this.Ue(t, e, tS.newNoDocument(e, q.min()));
							} else 1 === n || _();
						} else {
							const r = this.Ye(t);
							if (r !== n) {
								const n = this.Ze(e),
									i = n ? this.Xe(n, e, r) : 1;
								0 !== i &&
									(this.je(t),
									(this.Qe = this.Qe.insert(
										t,
										2 === i
											? "TargetPurposeExistenceFilterMismatchBloom"
											: "TargetPurposeExistenceFilterMismatch",
									)));
							}
						}
					}
				}
				Ze(e) {
					let t, n;
					const r = e.me.unchangedNames;
					if (!r || !r.bits) return null;
					const {
						bits: { bitmap: i = "", padding: s = 0 },
						hashCount: a = 0,
					} = r;
					try {
						t = e6(i).toUint8Array();
					} catch (e) {
						if (e instanceof e2)
							return (
								E(
									"Decoding the base64 bloom filter in existence filter failed (" +
										e.message +
										"); ignoring the bloom filter and falling back to full re-query.",
								),
								null
							);
						throw e;
					}
					try {
						n = new nX(t, s, a);
					} catch (e) {
						return (
							E(
								e instanceof nZ
									? "BloomFilter error: "
									: "Applying bloom filter failed: ",
								e,
							),
							null
						);
					}
					return 0 === n.Ie ? null : n;
				}
				Xe(e, t, n) {
					return 2 * (t.me.count !== n - this.nt(e, t.targetId));
				}
				nt(e, t) {
					let n = this.Le.getRemoteKeysForTarget(t),
						r = 0;
					return (
						n.forEach((n) => {
							const i = this.Le.tt(),
								s = `projects/${i.projectId}/databases/${i.database}/documents/${n.path.canonicalString()}`;
							e.mightContain(s) || (this.Ue(t, n, null), r++);
						}),
						r
					);
				}
				rt(e) {
					const t = new Map();
					this.Be.forEach((n, r) => {
						const i = this.Je(r);
						if (i) {
							if (n.current && tH(i.target)) {
								const t = new $(i.target.path);
								null !== this.ke.get(t) ||
									this.it(r, t) ||
									this.Ue(r, t, tS.newNoDocument(t, e));
							}
							n.be && (t.set(r, n.ve()), n.Ce());
						}
					});
					let n = nf();
					this.qe.forEach((e, t) => {
						let r = !0;
						t.forEachWhile((e) => {
							const t = this.Je(e);
							return (
								!t ||
								"TargetPurposeLimboResolution" === t.purpose ||
								((r = !1), !1)
							);
						}),
							r && (n = n.add(e));
					}),
						this.ke.forEach((t, n) => n.setReadTime(e));
					const r = new n0(e, t, this.Qe, this.ke, n);
					return (this.ke = na), (this.qe = n6()), (this.Qe = new eJ(L)), r;
				}
				$e(e, t) {
					if (!this.ze(e)) return;
					const n = 2 * !!this.it(e, t.key);
					this.Ge(e).Fe(t.key, n),
						(this.ke = this.ke.insert(t.key, t)),
						(this.qe = this.qe.insert(t.key, this.st(t.key).add(e)));
				}
				Ue(e, t, n) {
					if (!this.ze(e)) return;
					const r = this.Ge(e);
					this.it(e, t) ? r.Fe(t, 1) : r.Me(t),
						(this.qe = this.qe.insert(t, this.st(t).delete(e))),
						n && (this.ke = this.ke.insert(t, n));
				}
				removeTarget(e) {
					this.Be.delete(e);
				}
				Ye(e) {
					const t = this.Ge(e).ve();
					return (
						this.Le.getRemoteKeysForTarget(e).size +
						t.addedDocuments.size -
						t.removedDocuments.size
					);
				}
				xe(e) {
					this.Ge(e).xe();
				}
				Ge(e) {
					let t = this.Be.get(e);
					return t || ((t = new n8()), this.Be.set(e, t)), t;
				}
				st(e) {
					let t = this.qe.get(e);
					return t || ((t = new eX(L)), (this.qe = this.qe.insert(e, t))), t;
				}
				ze(e) {
					const t = null !== this.Je(e);
					return (
						t || I("WatchChangeAggregator", "Detected inactive target", e), t
					);
				}
				Je(e) {
					const t = this.Be.get(e);
					return t && t.Se ? null : this.Le.ot(e);
				}
				je(e) {
					this.Be.set(e, new n8()),
						this.Le.getRemoteKeysForTarget(e).forEach((t) => {
							this.Ue(e, t, null);
						});
				}
				it(e, t) {
					return this.Le.getRemoteKeysForTarget(e).has(t);
				}
			}
			function n6() {
				return new eJ($.comparator);
			}
			function n9() {
				return new eJ($.comparator);
			}
			const n7 = { asc: "ASCENDING", desc: "DESCENDING" },
				re = {
					"<": "LESS_THAN",
					"<=": "LESS_THAN_OR_EQUAL",
					">": "GREATER_THAN",
					">=": "GREATER_THAN_OR_EQUAL",
					"==": "EQUAL",
					"!=": "NOT_EQUAL",
					"array-contains": "ARRAY_CONTAINS",
					in: "IN",
					"not-in": "NOT_IN",
					"array-contains-any": "ARRAY_CONTAINS_ANY",
				},
				rt = { and: "AND", or: "OR" };
			class rn {
				constructor(e, t) {
					(this.databaseId = e), (this.useProto3Json = t);
				}
			}
			function rr(e, t) {
				return e.useProto3Json || ew(t) ? t : { value: t };
			}
			function ri(e, t) {
				return e.useProto3Json
					? `${new Date(1e3 * t.seconds).toISOString().replace(/\.\d*/, "").replace("Z", "")}.${("000000000" + t.nanoseconds).slice(-9)}Z`
					: { seconds: "" + t.seconds, nanos: t.nanoseconds };
			}
			function rs(e, t) {
				return e.useProto3Json ? t.toBase64() : t.toUint8Array();
			}
			function ra(e) {
				return (
					e || _(),
					q.fromTimestamp(
						((e) => {
							const t = e8(e);
							return new U(t.seconds, t.nanos);
						})(e),
					)
				);
			}
			function ro(e, t) {
				return rl(e, t).canonicalString();
			}
			function rl(e, t) {
				const n = new K([
					"projects",
					e.projectId,
					"databases",
					e.database,
				]).child("documents");
				return void 0 === t ? n : n.child(t);
			}
			function ru(e) {
				const t = K.fromString(e);
				return rS(t) || _(), t;
			}
			function rh(e, t) {
				return ro(e.databaseId, t.path);
			}
			function rc(e, t) {
				const n = ru(t);
				if (n.get(1) !== e.databaseId.projectId)
					throw new x(
						S.INVALID_ARGUMENT,
						"Tried to deserialize key from different project: " +
							n.get(1) +
							" vs " +
							e.databaseId.projectId,
					);
				if (n.get(3) !== e.databaseId.database)
					throw new x(
						S.INVALID_ARGUMENT,
						"Tried to deserialize key from different database: " +
							n.get(3) +
							" vs " +
							e.databaseId.database,
					);
				return new $(rg(n));
			}
			function rd(e, t) {
				return ro(e.databaseId, t);
			}
			function rf(e) {
				const t = ru(e);
				return 4 === t.length ? K.emptyPath() : rg(t);
			}
			function rm(e) {
				return new K([
					"projects",
					e.databaseId.projectId,
					"databases",
					e.databaseId.database,
				]).canonicalString();
			}
			function rg(e) {
				return (e.length > 4 && "documents" === e.get(4)) || _(), e.popFirst(5);
			}
			function rp(e, t, n) {
				return { name: rh(e, t), fields: n.value.mapValue.fields };
			}
			function ry(e, t, n) {
				const r = rc(e, t.name),
					i = ra(t.updateTime),
					s = t.createTime ? ra(t.createTime) : q.min(),
					a = new t_({ mapValue: { fields: t.fields } }),
					o = tS.newFoundDocument(r, i, s, a);
				return (
					n && o.setHasCommittedMutations(),
					n ? o.setHasCommittedMutations() : o
				);
			}
			function rw(e, t) {
				var n;
				let r;
				if (t instanceof nF) r = { update: rp(e, t.key, t.value) };
				else if (t instanceof nB) r = { delete: rh(e, t.key) };
				else if (t instanceof nL)
					r = {
						update: rp(e, t.key, t.data),
						updateMask: ((e) => {
							const t = [];
							return (
								e.fields.forEach((e) => t.push(e.canonicalString())),
								{ fieldPaths: t }
							);
						})(t.fieldMask),
					};
				else {
					if (!(t instanceof nK)) return _();
					r = { verify: rh(e, t.key) };
				}
				return (
					t.fieldTransforms.length > 0 &&
						(r.updateTransforms = t.fieldTransforms.map((e) =>
							((e, t) => {
								const n = t.transform;
								if (n instanceof nI)
									return {
										fieldPath: t.field.canonicalString(),
										setToServerValue: "REQUEST_TIME",
									};
								if (n instanceof nT)
									return {
										fieldPath: t.field.canonicalString(),
										appendMissingElements: { values: n.elements },
									};
								if (n instanceof nb)
									return {
										fieldPath: t.field.canonicalString(),
										removeAllFromArray: { values: n.elements },
									};
								if (n instanceof nS)
									return {
										fieldPath: t.field.canonicalString(),
										increment: n.Pe,
									};
								throw _();
							})(0, e),
						)),
					t.precondition.isNone ||
						(r.currentDocument =
							void 0 !== (n = t.precondition).updateTime
								? { updateTime: ri(e, n.updateTime.toTimestamp()) }
								: void 0 !== n.exists
									? { exists: n.exists }
									: _()),
					r
				);
			}
			function rv(e, t) {
				var n;
				const r = t.currentDocument
						? void 0 !== (n = t.currentDocument).updateTime
							? nk.updateTime(ra(n.updateTime))
							: void 0 !== n.exists
								? nk.exists(n.exists)
								: nk.none()
						: nk.none(),
					i = t.updateTransforms
						? t.updateTransforms.map((t) => {
								var n, r;
								let i;
								return (
									(n = e),
									(i = null),
									"setToServerValue" in (r = t)
										? ("REQUEST_TIME" === r.setToServerValue || _(),
											(i = new nI()))
										: "appendMissingElements" in r
											? (i = new nT(r.appendMissingElements.values || []))
											: "removeAllFromArray" in r
												? (i = new nb(r.removeAllFromArray.values || []))
												: "increment" in r
													? (i = new nS(n, r.increment))
													: _(),
									new nC(G.fromServerFormat(r.fieldPath), i)
								);
							})
						: [];
				if (t.update) {
					t.update.name;
					const n = rc(e, t.update.name),
						s = new t_({ mapValue: { fields: t.update.fields } });
					return t.updateMask
						? new nL(
								n,
								s,
								new e1(
									(t.updateMask.fieldPaths || []).map((e) =>
										G.fromServerFormat(e),
									),
								),
								r,
								i,
							)
						: new nF(n, s, r, i);
				}
				return t.delete
					? new nB(rc(e, t.delete), r)
					: t.verify
						? new nK(rc(e, t.verify), r)
						: _();
			}
			function rI(e, t) {
				return { documents: [rd(e, t.path)] };
			}
			function rT(e, t) {
				var n, r;
				let i;
				const s = { structuredQuery: {} },
					a = t.path;
				null !== t.collectionGroup
					? ((i = a),
						(s.structuredQuery.from = [
							{ collectionId: t.collectionGroup, allDescendants: !0 },
						]))
					: ((i = a.popLast()),
						(s.structuredQuery.from = [{ collectionId: a.lastSegment() }])),
					(s.parent = rd(e, i));
				const o = ((e) => {
					if (0 !== e.length)
						return (function e(t) {
							return t instanceof tA
								? ((e) => {
										if ("==" === e.op) {
											if (tp(e.value))
												return {
													unaryFilter: { field: rb(e.field), op: "IS_NAN" },
												};
											if (tg(e.value))
												return {
													unaryFilter: { field: rb(e.field), op: "IS_NULL" },
												};
										} else if ("!=" === e.op) {
											if (tp(e.value))
												return {
													unaryFilter: { field: rb(e.field), op: "IS_NOT_NAN" },
												};
											if (tg(e.value))
												return {
													unaryFilter: {
														field: rb(e.field),
														op: "IS_NOT_NULL",
													},
												};
										}
										return {
											fieldFilter: {
												field: rb(e.field),
												op: re[e.op],
												value: e.value,
											},
										};
									})(t)
								: t instanceof tV
									? ((t) => {
											const n = t.getFilters().map((t) => e(t));
											return 1 === n.length
												? n[0]
												: { compositeFilter: { op: rt[t.op], filters: n } };
										})(t)
									: _();
						})(tV.create(e, "and"));
				})(t.filters);
				o && (s.structuredQuery.where = o);
				const l = ((e) => {
					if (0 !== e.length)
						return e.map((e) => ({ field: rb(e.field), direction: n7[e.dir] }));
				})(t.orderBy);
				l && (s.structuredQuery.orderBy = l);
				const u = rr(e, t.limit);
				return (
					null !== u && (s.structuredQuery.limit = u),
					t.startAt &&
						(s.structuredQuery.startAt = {
							before: (n = t.startAt).inclusive,
							values: n.position,
						}),
					t.endAt &&
						(s.structuredQuery.endAt = {
							before: !(r = t.endAt).inclusive,
							values: r.position,
						}),
					{ _t: s, parent: i }
				);
			}
			function rE(e) {
				var t;
				let n,
					r = rf(e.parent),
					i = e.structuredQuery,
					s = i.from ? i.from.length : 0,
					a = null;
				if (s > 0) {
					1 === s || _();
					const e = i.from[0];
					e.allDescendants
						? (a = e.collectionId)
						: (r = r.child(e.collectionId));
				}
				let o = [];
				i.where &&
					(o = ((e) => {
						const t = (function e(t) {
							return void 0 !== t.unaryFilter
								? ((e) => {
										switch (e.unaryFilter.op) {
											case "IS_NAN":
												const t = r_(e.unaryFilter.field);
												return tA.create(t, "==", { doubleValue: Number.NaN });
											case "IS_NULL":
												const n = r_(e.unaryFilter.field);
												return tA.create(n, "==", { nullValue: "NULL_VALUE" });
											case "IS_NOT_NAN":
												const r = r_(e.unaryFilter.field);
												return tA.create(r, "!=", { doubleValue: Number.NaN });
											case "IS_NOT_NULL":
												const i = r_(e.unaryFilter.field);
												return tA.create(i, "!=", { nullValue: "NULL_VALUE" });
											default:
												return _();
										}
									})(t)
								: void 0 !== t.fieldFilter
									? tA.create(
											r_(t.fieldFilter.field),
											((e) => {
												switch (e) {
													case "EQUAL":
														return "==";
													case "NOT_EQUAL":
														return "!=";
													case "GREATER_THAN":
														return ">";
													case "GREATER_THAN_OR_EQUAL":
														return ">=";
													case "LESS_THAN":
														return "<";
													case "LESS_THAN_OR_EQUAL":
														return "<=";
													case "ARRAY_CONTAINS":
														return "array-contains";
													case "IN":
														return "in";
													case "NOT_IN":
														return "not-in";
													case "ARRAY_CONTAINS_ANY":
														return "array-contains-any";
													default:
														return _();
												}
											})(t.fieldFilter.op),
											t.fieldFilter.value,
										)
									: void 0 !== t.compositeFilter
										? tV.create(
												t.compositeFilter.filters.map((t) => e(t)),
												((e) => {
													switch (e) {
														case "AND":
															return "and";
														case "OR":
															return "or";
														default:
															return _();
													}
												})(t.compositeFilter.op),
											)
										: _();
						})(e);
						return t instanceof tV && tM(t) ? t.getFilters() : [t];
					})(i.where));
				let l = [];
				i.orderBy &&
					(l = i.orderBy.map(
						(e) =>
							new tN(
								r_(e.field),
								((e) => {
									switch (e) {
										case "ASCENDING":
											return "asc";
										case "DESCENDING":
											return "desc";
										default:
											return;
									}
								})(e.direction),
							),
					));
				let u = null;
				i.limit &&
					(u = ew((n = "object" == typeof (t = i.limit) ? t.value : t))
						? null
						: n);
				let h = null;
				i.startAt &&
					(h = ((e) => {
						const t = !!e.before;
						return new tx(e.values || [], t);
					})(i.startAt));
				let c = null;
				return (
					i.endAt &&
						(c = ((e) => {
							const t = !e.before;
							return new tx(e.values || [], t);
						})(i.endAt)),
					new t0(r, a, l, o, u, "F", h, c)
				);
			}
			function rb(e) {
				return { fieldPath: e.canonicalString() };
			}
			function r_(e) {
				return G.fromServerFormat(e.fieldPath);
			}
			function rS(e) {
				return (
					e.length >= 4 && "projects" === e.get(0) && "databases" === e.get(2)
				);
			}
			class rx {
				constructor(
					e,
					t,
					n,
					r,
					i = q.min(),
					s = q.min(),
					a = e5.EMPTY_BYTE_STRING,
					o = null,
				) {
					(this.target = e),
						(this.targetId = t),
						(this.purpose = n),
						(this.sequenceNumber = r),
						(this.snapshotVersion = i),
						(this.lastLimboFreeSnapshotVersion = s),
						(this.resumeToken = a),
						(this.expectedCount = o);
				}
				withSequenceNumber(e) {
					return new rx(
						this.target,
						this.targetId,
						this.purpose,
						e,
						this.snapshotVersion,
						this.lastLimboFreeSnapshotVersion,
						this.resumeToken,
						this.expectedCount,
					);
				}
				withResumeToken(e, t) {
					return new rx(
						this.target,
						this.targetId,
						this.purpose,
						this.sequenceNumber,
						t,
						this.lastLimboFreeSnapshotVersion,
						e,
						null,
					);
				}
				withExpectedCount(e) {
					return new rx(
						this.target,
						this.targetId,
						this.purpose,
						this.sequenceNumber,
						this.snapshotVersion,
						this.lastLimboFreeSnapshotVersion,
						this.resumeToken,
						e,
					);
				}
				withLastLimboFreeSnapshotVersion(e) {
					return new rx(
						this.target,
						this.targetId,
						this.purpose,
						this.sequenceNumber,
						this.snapshotVersion,
						e,
						this.resumeToken,
						this.expectedCount,
					);
				}
			}
			class rD {
				constructor(e) {
					this.ct = e;
				}
			}
			function rC(e, t) {
				const n = t.key,
					r = {
						prefixPath: n.getCollectionPath().popLast().toArray(),
						collectionGroup: n.collectionGroup,
						documentId: n.path.lastSegment(),
						readTime: rN(t.readTime),
						hasCommittedMutations: t.hasCommittedMutations,
					};
				if (t.isFoundDocument()) {
					var i;
					r.document = {
						name: rh((i = e.ct), t.key),
						fields: t.data.value.mapValue.fields,
						updateTime: ri(i, t.version.toTimestamp()),
						createTime: ri(i, t.createTime.toTimestamp()),
					};
				} else if (t.isNoDocument())
					r.noDocument = { path: n.path.toArray(), readTime: rk(t.version) };
				else {
					if (!t.isUnknownDocument()) return _();
					r.unknownDocument = {
						path: n.path.toArray(),
						version: rk(t.version),
					};
				}
				return r;
			}
			function rN(e) {
				const t = e.toTimestamp();
				return [t.seconds, t.nanoseconds];
			}
			function rk(e) {
				const t = e.toTimestamp();
				return { seconds: t.seconds, nanoseconds: t.nanoseconds };
			}
			function rA(e) {
				const t = new U(e.seconds, e.nanoseconds);
				return q.fromTimestamp(t);
			}
			function rV(e, t) {
				const n = (t.baseMutations || []).map((t) => rv(e.ct, t));
				for (let e = 0; e < t.mutations.length - 1; ++e) {
					const n = t.mutations[e];
					e + 1 < t.mutations.length &&
						void 0 !== t.mutations[e + 1].transform &&
						((n.updateTransforms =
							t.mutations[e + 1].transform.fieldTransforms),
						t.mutations.splice(e + 1, 1),
						++e);
				}
				const r = t.mutations.map((t) => rv(e.ct, t)),
					i = U.fromMillis(t.localWriteTimeMs);
				return new nz(t.batchId, i, n, r);
			}
			function rR(e) {
				var t;
				const n = rA(e.readTime),
					r =
						void 0 !== e.lastLimboFreeSnapshotVersion
							? rA(e.lastLimboFreeSnapshotVersion)
							: q.min();
				return new rx(
					void 0 !== e.query.documents
						? (1 === (t = e.query).documents.length || _(),
							t8(t1(rf(t.documents[0]))))
						: t8(rE(e.query)),
					e.targetId,
					"TargetPurposeListen",
					e.lastListenSequenceNumber,
					n,
					r,
					e5.fromBase64String(e.resumeToken),
				);
			}
			function rO(e, t) {
				let n;
				const r = rk(t.snapshotVersion),
					i = rk(t.lastLimboFreeSnapshotVersion);
				n = tH(t.target) ? rI(e.ct, t.target) : rT(e.ct, t.target)._t;
				const s = t.resumeToken.toBase64();
				return {
					targetId: t.targetId,
					canonicalId: tW(t.target),
					readTime: r,
					resumeToken: s,
					lastListenSequenceNumber: t.sequenceNumber,
					lastLimboFreeSnapshotVersion: i,
					query: n,
				};
			}
			function rM(e) {
				const t = rE({ parent: e.parent, structuredQuery: e.structuredQuery });
				return "LAST" === e.limitType ? t9(t, t.limit, "L") : t;
			}
			function rF(e, t) {
				return new n$(t.largestBatchId, rv(e.ct, t.overlayMutation));
			}
			function rL(e, t) {
				const n = t.path.lastSegment();
				return [e, eT(t.path.popLast()), n];
			}
			function rP(e, t, n, r) {
				return {
					indexId: e,
					uid: t,
					sequenceNumber: n,
					readTime: rk(r.readTime),
					documentKey: eT(r.documentKey.path),
					largestBatchId: r.largestBatchId,
				};
			}
			class rU {
				getBundleMetadata(e, t) {
					return rq(e)
						.get(t)
						.next((e) => {
							if (e)
								return {
									id: e.bundleId,
									createTime: rA(e.createTime),
									version: e.version,
								};
						});
				}
				saveBundleMetadata(e, t) {
					return rq(e).put({
						bundleId: t.id,
						createTime: rk(ra(t.createTime)),
						version: t.version,
					});
				}
				getNamedQuery(e, t) {
					return rB(e)
						.get(t)
						.next((e) => {
							if (e)
								return {
									name: e.name,
									query: rM(e.bundledQuery),
									readTime: rA(e.readTime),
								};
						});
				}
				saveNamedQuery(e, t) {
					return rB(e).put({
						name: t.name,
						readTime: rk(ra(t.readTime)),
						bundledQuery: t.bundledQuery,
					});
				}
			}
			function rq(e) {
				return e$(e, "bundles");
			}
			function rB(e) {
				return e$(e, "namedQueries");
			}
			class rK {
				constructor(e, t) {
					(this.serializer = e), (this.userId = t);
				}
				static lt(e, t) {
					return new rK(e, t.uid || "");
				}
				getOverlay(e, t) {
					return rz(e)
						.get(rL(this.userId, t))
						.next((e) => (e ? rF(this.serializer, e) : null));
				}
				getOverlays(e, t) {
					const n = nh();
					return ei
						.forEach(t, (t) =>
							this.getOverlay(e, t).next((e) => {
								null !== e && n.set(t, e);
							}),
						)
						.next(() => n);
				}
				saveOverlays(e, t, n) {
					const r = [];
					return (
						n.forEach((n, i) => {
							const s = new n$(t, i);
							r.push(this.ht(e, s));
						}),
						ei.waitFor(r)
					);
				}
				removeOverlaysForBatchId(e, t, n) {
					const r = new Set();
					t.forEach((e) => r.add(eT(e.getCollectionPath())));
					const i = [];
					return (
						r.forEach((t) => {
							const r = IDBKeyRange.bound(
								[this.userId, t, n],
								[this.userId, t, n + 1],
								!1,
								!0,
							);
							i.push(rz(e).j("collectionPathOverlayIndex", r));
						}),
						ei.waitFor(i)
					);
				}
				getOverlaysForCollection(e, t, n) {
					const r = nh(),
						i = eT(t),
						s = IDBKeyRange.bound(
							[this.userId, i, n],
							[this.userId, i, Number.POSITIVE_INFINITY],
							!0,
						);
					return rz(e)
						.U("collectionPathOverlayIndex", s)
						.next((e) => {
							for (const t of e) {
								const e = rF(this.serializer, t);
								r.set(e.getKey(), e);
							}
							return r;
						});
				}
				getOverlaysForCollectionGroup(e, t, n, r) {
					let i;
					const s = nh(),
						a = IDBKeyRange.bound(
							[this.userId, t, n],
							[this.userId, t, Number.POSITIVE_INFINITY],
							!0,
						);
					return rz(e)
						.J(
							{ index: "collectionGroupOverlayIndex", range: a },
							(e, t, n) => {
								const a = rF(this.serializer, t);
								s.size() < r || a.largestBatchId === i
									? (s.set(a.getKey(), a), (i = a.largestBatchId))
									: n.done();
							},
						)
						.next(() => s);
				}
				ht(e, t) {
					return rz(e).put(
						((e, t, n) => {
							const [r, i, s] = rL(t, n.mutation.key);
							return {
								userId: t,
								collectionPath: i,
								documentId: s,
								collectionGroup: n.mutation.key.getCollectionGroup(),
								largestBatchId: n.largestBatchId,
								overlayMutation: rw(e.ct, n.mutation),
							};
						})(this.serializer, this.userId, t),
					);
				}
			}
			function rz(e) {
				return e$(e, "documentOverlays");
			}
			class rG {
				Pt(e) {
					return e$(e, "globals");
				}
				getSessionToken(e) {
					return this.Pt(e)
						.get("sessionToken")
						.next((e) => {
							const t = null == e ? void 0 : e.value;
							return t ? e5.fromUint8Array(t) : e5.EMPTY_BYTE_STRING;
						});
				}
				setSessionToken(e, t) {
					return this.Pt(e).put({
						name: "sessionToken",
						value: t.toUint8Array(),
					});
				}
			}
			class r$ {
				constructor() {}
				It(e, t) {
					this.Tt(e, t), t.Et();
				}
				Tt(e, t) {
					if ("nullValue" in e) this.dt(t, 5);
					else if ("booleanValue" in e) this.dt(t, 10), t.At(+!!e.booleanValue);
					else if ("integerValue" in e)
						this.dt(t, 15), t.At(e4(e.integerValue));
					else if ("doubleValue" in e) {
						const n = e4(e.doubleValue);
						isNaN(n)
							? this.dt(t, 13)
							: (this.dt(t, 15), ev(n) ? t.At(0) : t.At(n));
					} else if ("timestampValue" in e) {
						let n = e.timestampValue;
						this.dt(t, 20),
							"string" == typeof n && (n = e8(n)),
							t.Rt(`${n.seconds || ""}`),
							t.At(n.nanos || 0);
					} else if ("stringValue" in e) this.Vt(e.stringValue, t), this.ft(t);
					else if ("bytesValue" in e)
						this.dt(t, 30), t.gt(e6(e.bytesValue)), this.ft(t);
					else if ("referenceValue" in e) this.yt(e.referenceValue, t);
					else if ("geoPointValue" in e) {
						const n = e.geoPointValue;
						this.dt(t, 45), t.At(n.latitude || 0), t.At(n.longitude || 0);
					} else
						"mapValue" in e
							? tI(e)
								? this.dt(t, Number.MAX_SAFE_INTEGER)
								: tw(e)
									? this.wt(e.mapValue, t)
									: (this.St(e.mapValue, t), this.ft(t))
							: "arrayValue" in e
								? (this.bt(e.arrayValue, t), this.ft(t))
								: _();
				}
				Vt(e, t) {
					this.dt(t, 25), this.Dt(e, t);
				}
				Dt(e, t) {
					t.Rt(e);
				}
				St(e, t) {
					const n = e.fields || {};
					for (const e of (this.dt(t, 55), Object.keys(n)))
						this.Vt(e, t), this.Tt(n[e], t);
				}
				wt(e, t) {
					var n, r;
					const i = e.fields || {};
					this.dt(t, 53);
					const s = "value",
						a =
							(null ===
								(r =
									null === (n = i[s].arrayValue) || void 0 === n
										? void 0
										: n.values) || void 0 === r
								? void 0
								: r.length) || 0;
					this.dt(t, 15), t.At(e4(a)), this.Vt(s, t), this.Tt(i[s], t);
				}
				bt(e, t) {
					const n = e.values || [];
					for (const e of (this.dt(t, 50), n)) this.Tt(e, t);
				}
				yt(e, t) {
					this.dt(t, 37),
						$.fromName(e).path.forEach((e) => {
							this.dt(t, 60), this.Dt(e, t);
						});
				}
				dt(e, t) {
					e.At(t);
				}
				ft(e) {
					e.At(2);
				}
			}
			function rQ(e) {
				return Math.ceil(
					(64 -
						((e) => {
							let t = 0;
							for (let n = 0; n < 8; ++n) {
								const r = ((e) => {
									if (0 === e) return 8;
									let t = 0;
									return (
										e >> 4 == 0 && ((t += 4), (e <<= 4)),
										e >> 6 == 0 && ((t += 2), (e <<= 2)),
										e >> 7 == 0 && (t += 1),
										t
									);
								})(255 & e[n]);
								if (((t += r), 8 !== r)) break;
							}
							return t;
						})(e)) /
						8,
				);
			}
			r$.vt = new r$();
			class rj {
				constructor() {
					(this.buffer = new Uint8Array(1024)), (this.position = 0);
				}
				Ct(e) {
					let t = e[Symbol.iterator](),
						n = t.next();
					while (!n.done) this.Ft(n.value), (n = t.next());
					this.Mt();
				}
				xt(e) {
					let t = e[Symbol.iterator](),
						n = t.next();
					while (!n.done) this.Ot(n.value), (n = t.next());
					this.Nt();
				}
				Lt(e) {
					for (const t of e) {
						const e = t.charCodeAt(0);
						if (e < 128) this.Ft(e);
						else if (e < 2048)
							this.Ft(960 | (e >>> 6)), this.Ft(128 | (63 & e));
						else if (t < "\ud800" || "\udbff" < t)
							this.Ft(480 | (e >>> 12)),
								this.Ft(128 | (63 & (e >>> 6))),
								this.Ft(128 | (63 & e));
						else {
							const e = t.codePointAt(0);
							this.Ft(240 | (e >>> 18)),
								this.Ft(128 | (63 & (e >>> 12))),
								this.Ft(128 | (63 & (e >>> 6))),
								this.Ft(128 | (63 & e));
						}
					}
					this.Mt();
				}
				Bt(e) {
					for (const t of e) {
						const e = t.charCodeAt(0);
						if (e < 128) this.Ot(e);
						else if (e < 2048)
							this.Ot(960 | (e >>> 6)), this.Ot(128 | (63 & e));
						else if (t < "\ud800" || "\udbff" < t)
							this.Ot(480 | (e >>> 12)),
								this.Ot(128 | (63 & (e >>> 6))),
								this.Ot(128 | (63 & e));
						else {
							const e = t.codePointAt(0);
							this.Ot(240 | (e >>> 18)),
								this.Ot(128 | (63 & (e >>> 12))),
								this.Ot(128 | (63 & (e >>> 6))),
								this.Ot(128 | (63 & e));
						}
					}
					this.Nt();
				}
				kt(e) {
					const t = this.qt(e),
						n = rQ(t);
					this.Qt(1 + n), (this.buffer[this.position++] = 255 & n);
					for (let e = t.length - n; e < t.length; ++e)
						this.buffer[this.position++] = 255 & t[e];
				}
				Kt(e) {
					const t = this.qt(e),
						n = rQ(t);
					this.Qt(1 + n), (this.buffer[this.position++] = ~(255 & n));
					for (let e = t.length - n; e < t.length; ++e)
						this.buffer[this.position++] = ~(255 & t[e]);
				}
				$t() {
					this.Ut(255), this.Ut(255);
				}
				Wt() {
					this.Gt(255), this.Gt(255);
				}
				reset() {
					this.position = 0;
				}
				seed(e) {
					this.Qt(e.length),
						this.buffer.set(e, this.position),
						(this.position += e.length);
				}
				zt() {
					return this.buffer.slice(0, this.position);
				}
				qt(e) {
					const t = ((e) => {
							const t = new DataView(new ArrayBuffer(8));
							return t.setFloat64(0, e, !1), new Uint8Array(t.buffer);
						})(e),
						n = 0 != (128 & t[0]);
					t[0] ^= n ? 255 : 128;
					for (let e = 1; e < t.length; ++e) t[e] ^= 255 * !!n;
					return t;
				}
				Ft(e) {
					const t = 255 & e;
					0 === t
						? (this.Ut(0), this.Ut(255))
						: 255 === t
							? (this.Ut(255), this.Ut(0))
							: this.Ut(t);
				}
				Ot(e) {
					const t = 255 & e;
					0 === t
						? (this.Gt(0), this.Gt(255))
						: 255 === t
							? (this.Gt(255), this.Gt(0))
							: this.Gt(e);
				}
				Mt() {
					this.Ut(0), this.Ut(1);
				}
				Nt() {
					this.Gt(0), this.Gt(1);
				}
				Ut(e) {
					this.Qt(1), (this.buffer[this.position++] = e);
				}
				Gt(e) {
					this.Qt(1), (this.buffer[this.position++] = ~e);
				}
				Qt(e) {
					const t = e + this.position;
					if (t <= this.buffer.length) return;
					let n = 2 * this.buffer.length;
					n < t && (n = t);
					const r = new Uint8Array(n);
					r.set(this.buffer), (this.buffer = r);
				}
			}
			class rW {
				constructor(e) {
					this.jt = e;
				}
				gt(e) {
					this.jt.Ct(e);
				}
				Rt(e) {
					this.jt.Lt(e);
				}
				At(e) {
					this.jt.kt(e);
				}
				Et() {
					this.jt.$t();
				}
			}
			class rJ {
				constructor(e) {
					this.jt = e;
				}
				gt(e) {
					this.jt.xt(e);
				}
				Rt(e) {
					this.jt.Bt(e);
				}
				At(e) {
					this.jt.Kt(e);
				}
				Et() {
					this.jt.Wt();
				}
			}
			class rH {
				constructor() {
					(this.jt = new rj()),
						(this.Ht = new rW(this.jt)),
						(this.Jt = new rJ(this.jt));
				}
				seed(e) {
					this.jt.seed(e);
				}
				Yt(e) {
					return 0 === e ? this.Ht : this.Jt;
				}
				zt() {
					return this.jt.zt();
				}
				reset() {
					this.jt.reset();
				}
			}
			class rY {
				constructor(e, t, n, r) {
					(this.indexId = e),
						(this.documentKey = t),
						(this.arrayValue = n),
						(this.directionalValue = r);
				}
				Zt() {
					const e = this.directionalValue.length,
						t = 0 === e || 255 === this.directionalValue[e - 1] ? e + 1 : e,
						n = new Uint8Array(t);
					return (
						n.set(this.directionalValue, 0),
						t !== e
							? n.set([0], this.directionalValue.length)
							: ++n[n.length - 1],
						new rY(this.indexId, this.documentKey, this.arrayValue, n)
					);
				}
			}
			function rX(e, t) {
				let n = e.indexId - t.indexId;
				return 0 !== n
					? n
					: 0 !== (n = rZ(e.arrayValue, t.arrayValue))
						? n
						: 0 !== (n = rZ(e.directionalValue, t.directionalValue))
							? n
							: $.comparator(e.documentKey, t.documentKey);
			}
			function rZ(e, t) {
				for (let n = 0; n < e.length && n < t.length; ++n) {
					const r = e[n] - t[n];
					if (0 !== r) return r;
				}
				return e.length - t.length;
			}
			class r0 {
				constructor(e) {
					for (const t of ((this.Xt = new eX((e, t) =>
						G.comparator(e.field, t.field),
					)),
					(this.collectionId =
						null != e.collectionGroup
							? e.collectionGroup
							: e.path.lastSegment()),
					(this.en = e.orderBy),
					(this.tn = []),
					e.filters))
						t.isInequality() ? (this.Xt = this.Xt.add(t)) : this.tn.push(t);
				}
				get nn() {
					return this.Xt.size > 1;
				}
				rn(e) {
					if ((e.collectionGroup === this.collectionId || _(), this.nn))
						return !1;
					const t = j(e);
					if (void 0 !== t && !this.sn(t)) return !1;
					let n = W(e),
						r = new Set(),
						i = 0,
						s = 0;
					for (; i < n.length && this.sn(n[i]); ++i)
						r = r.add(n[i].fieldPath.canonicalString());
					if (i === n.length) return !0;
					if (this.Xt.size > 0) {
						const e = this.Xt.getIterator().getNext();
						if (!r.has(e.field.canonicalString())) {
							const t = n[i];
							if (!this.on(e, t) || !this._n(this.en[s++], t)) return !1;
						}
						++i;
					}
					for (; i < n.length; ++i) {
						const e = n[i];
						if (s >= this.en.length || !this._n(this.en[s++], e)) return !1;
					}
					return !0;
				}
				an() {
					if (this.nn) return null;
					let e = new eX(G.comparator),
						t = [];
					for (const n of this.tn)
						if (!n.field.isKeyField()) {
							if ("array-contains" === n.op || "array-contains-any" === n.op)
								t.push(new J(n.field, 2));
							else {
								if (e.has(n.field)) continue;
								(e = e.add(n.field)), t.push(new J(n.field, 0));
							}
						}
					for (const n of this.en)
						n.field.isKeyField() ||
							e.has(n.field) ||
							((e = e.add(n.field)),
							t.push(new J(n.field, +("asc" !== n.dir))));
					return new Q(Q.UNKNOWN_ID, this.collectionId, t, H.empty());
				}
				sn(e) {
					for (const t of this.tn) if (this.on(t, e)) return !0;
					return !1;
				}
				on(e, t) {
					if (void 0 === e || !e.field.isEqual(t.fieldPath)) return !1;
					const n = "array-contains" === e.op || "array-contains-any" === e.op;
					return (2 === t.kind) === n;
				}
				_n(e, t) {
					return (
						!!e.field.isEqual(t.fieldPath) &&
						((0 === t.kind && "asc" === e.dir) ||
							(1 === t.kind && "desc" === e.dir))
					);
				}
			}
			function r1(e) {
				return e instanceof tA;
			}
			function r2(e) {
				return e instanceof tV && tM(e);
			}
			function r5(e) {
				return (
					r1(e) ||
					r2(e) ||
					((e) => {
						if (e instanceof tV && tO(e)) {
							for (const t of e.getFilters()) if (!r1(t) && !r2(t)) return !1;
							return !0;
						}
						return !1;
					})(e)
				);
			}
			function r3(e, t) {
				return (
					e instanceof tA || e instanceof tV || _(),
					t instanceof tA || t instanceof tV || _(),
					r4(
						e instanceof tA
							? t instanceof tA
								? tV.create([e, t], "and")
								: r8(e, t)
							: t instanceof tA
								? r8(t, e)
								: ((e, t) => {
										if (
											((e.filters.length > 0 && t.filters.length > 0) || _(),
											tR(e) && tR(t))
										)
											return tL(e, t.getFilters());
										const n = tO(e) ? e : t,
											r = tO(e) ? t : e,
											i = n.filters.map((e) => r3(e, r));
										return tV.create(i, "or");
									})(e, t),
					)
				);
			}
			function r8(e, t) {
				if (tR(t)) return tL(t, e.getFilters());
				{
					const n = t.filters.map((t) => r3(e, t));
					return tV.create(n, "or");
				}
			}
			function r4(e) {
				if ((e instanceof tA || e instanceof tV || _(), e instanceof tA))
					return e;
				const t = e.getFilters();
				if (1 === t.length) return r4(t[0]);
				if (tF(e)) return e;
				const n = t.map((e) => r4(e)),
					r = [];
				return (
					n.forEach((t) => {
						t instanceof tA
							? r.push(t)
							: t instanceof tV &&
								(t.op === e.op ? r.push(...t.filters) : r.push(t));
					}),
					1 === r.length ? r[0] : tV.create(r, e.op)
				);
			}
			class r6 {
				constructor() {
					this.un = new r9();
				}
				addToCollectionParentIndex(e, t) {
					return this.un.add(t), ei.resolve();
				}
				getCollectionParents(e, t) {
					return ei.resolve(this.un.getEntries(t));
				}
				addFieldIndex(e, t) {
					return ei.resolve();
				}
				deleteFieldIndex(e, t) {
					return ei.resolve();
				}
				deleteAllFieldIndexes(e) {
					return ei.resolve();
				}
				createTargetIndexes(e, t) {
					return ei.resolve();
				}
				getDocumentsMatchingTarget(e, t) {
					return ei.resolve(null);
				}
				getIndexType(e, t) {
					return ei.resolve(0);
				}
				getFieldIndexes(e, t) {
					return ei.resolve([]);
				}
				getNextCollectionGroupToUpdate(e) {
					return ei.resolve(null);
				}
				getMinOffset(e, t) {
					return ei.resolve(Z.min());
				}
				getMinOffsetFromCollectionGroup(e, t) {
					return ei.resolve(Z.min());
				}
				updateCollectionGroup(e, t, n) {
					return ei.resolve();
				}
				updateIndexEntries(e, t) {
					return ei.resolve();
				}
			}
			class r9 {
				constructor() {
					this.index = {};
				}
				add(e) {
					const t = e.lastSegment(),
						n = e.popLast(),
						r = this.index[t] || new eX(K.comparator),
						i = !r.has(n);
					return (this.index[t] = r.add(n)), i;
				}
				has(e) {
					const t = e.lastSegment(),
						n = e.popLast(),
						r = this.index[t];
					return r && r.has(n);
				}
				getEntries(e) {
					return (this.index[e] || new eX(K.comparator)).toArray();
				}
			}
			const r7 = new Uint8Array(0);
			class ie {
				constructor(e, t) {
					(this.databaseId = t),
						(this.cn = new r9()),
						(this.ln = new ns(
							(e) => tW(e),
							(e, t) => tJ(e, t),
						)),
						(this.uid = e.uid || "");
				}
				addToCollectionParentIndex(e, t) {
					if (!this.cn.has(t)) {
						const n = t.lastSegment(),
							r = t.popLast();
						e.addOnCommittedListener(() => {
							this.cn.add(t);
						});
						const i = { collectionId: n, parent: eT(r) };
						return it(e).put(i);
					}
					return ei.resolve();
				}
				getCollectionParents(e, t) {
					const n = [],
						r = IDBKeyRange.bound([t, ""], [t + "\0", ""], !1, !0);
					return it(e)
						.U(r)
						.next((e) => {
							for (const r of e) {
								if (r.collectionId !== t) break;
								n.push(eE(r.parent));
							}
							return n;
						});
				}
				addFieldIndex(e, t) {
					const n = ii(e),
						r = {
							indexId: t.indexId,
							collectionGroup: t.collectionGroup,
							fields: t.fields.map((e) => [
								e.fieldPath.canonicalString(),
								e.kind,
							]),
						};
					delete r.indexId;
					const i = n.add(r);
					if (t.indexState) {
						const n = is(e);
						return i.next((e) => {
							n.put(
								rP(
									e,
									this.uid,
									t.indexState.sequenceNumber,
									t.indexState.offset,
								),
							);
						});
					}
					return i.next();
				}
				deleteFieldIndex(e, t) {
					const n = ii(e),
						r = is(e),
						i = ir(e);
					return n
						.delete(t.indexId)
						.next(() =>
							r.delete(IDBKeyRange.bound([t.indexId], [t.indexId + 1], !1, !0)),
						)
						.next(() =>
							i.delete(IDBKeyRange.bound([t.indexId], [t.indexId + 1], !1, !0)),
						);
				}
				deleteAllFieldIndexes(e) {
					const t = ii(e),
						n = ir(e),
						r = is(e);
					return t
						.j()
						.next(() => n.j())
						.next(() => r.j());
				}
				createTargetIndexes(e, t) {
					return ei.forEach(this.hn(t), (t) =>
						this.getIndexType(e, t).next((n) => {
							if (0 === n || 1 === n) {
								const n = new r0(t).an();
								if (null != n) return this.addFieldIndex(e, n);
							}
						}),
					);
				}
				getDocumentsMatchingTarget(e, t) {
					let n = ir(e),
						r = !0,
						i = new Map();
					return ei
						.forEach(this.hn(t), (t) =>
							this.Pn(e, t).next((e) => {
								r && (r = !!e), i.set(t, e);
							}),
						)
						.next(() => {
							if (r) {
								let e = nf(),
									r = [];
								return ei
									.forEach(i, (i, s) => {
										I(
											"IndexedDbIndexManager",
											`Using index id=${i.indexId}|cg=${i.collectionGroup}|f=${i.fields.map((e) => `${e.fieldPath}:${e.kind}`).join(",")} to execute ${tW(t)}`,
										);
										const a = ((e, t) => {
												const n = j(t);
												if (void 0 === n) return null;
												for (const t of tY(e, n.fieldPath))
													switch (t.op) {
														case "array-contains-any":
															return t.value.arrayValue.values || [];
														case "array-contains":
															return [t.value];
													}
												return null;
											})(s, i),
											o = ((e, t) => {
												const n = new Map();
												for (const r of W(t))
													for (const t of tY(e, r.fieldPath))
														switch (t.op) {
															case "==":
															case "in":
																n.set(r.fieldPath.canonicalString(), t.value);
																break;
															case "not-in":
															case "!=":
																return (
																	n.set(r.fieldPath.canonicalString(), t.value),
																	Array.from(n.values())
																);
														}
												return null;
											})(s, i),
											l = ((e, t) => {
												let n = [],
													r = !0;
												for (const i of W(t)) {
													const t =
														0 === i.kind
															? tX(e, i.fieldPath, e.startAt)
															: tZ(e, i.fieldPath, e.startAt);
													n.push(t.value), r && (r = t.inclusive);
												}
												return new tx(n, r);
											})(s, i),
											u = ((e, t) => {
												let n = [],
													r = !0;
												for (const i of W(t)) {
													const t =
														0 === i.kind
															? tZ(e, i.fieldPath, e.endAt)
															: tX(e, i.fieldPath, e.endAt);
													n.push(t.value), r && (r = t.inclusive);
												}
												return new tx(n, r);
											})(s, i),
											h = this.In(i, s, l),
											c = this.In(i, s, u),
											d = this.Tn(i, s, o),
											f = this.En(
												i.indexId,
												a,
												h,
												l.inclusive,
												c,
												u.inclusive,
												d,
											);
										return ei.forEach(f, (i) =>
											n.G(i, t.limit).next((t) => {
												t.forEach((t) => {
													const n = $.fromSegments(t.documentKey);
													e.has(n) || ((e = e.add(n)), r.push(n));
												});
											}),
										);
									})
									.next(() => r);
							}
							return ei.resolve(null);
						});
				}
				hn(e) {
					let t = this.ln.get(e);
					return (
						t ||
							((t =
								0 === e.filters.length
									? [e]
									: ((e) => {
											if (0 === e.getFilters().length) return [];
											const t = (function e(t) {
												if (
													(t instanceof tA || t instanceof tV || _(),
													t instanceof tA)
												)
													return t;
												if (1 === t.filters.length) return e(t.filters[0]);
												let n = t.filters.map((t) => e(t)),
													r = tV.create(n, t.op);
												return r5((r = r4(r)))
													? r
													: (r instanceof tV || _(),
														tR(r) || _(),
														r.filters.length > 1 || _(),
														r.filters.reduce((e, t) => r3(e, t)));
											})(
												(function e(t) {
													var n, r;
													if (
														(t instanceof tA || t instanceof tV || _(),
														t instanceof tA)
													) {
														if (t instanceof tz) {
															const e =
																(null ===
																	(r =
																		null === (n = t.value.arrayValue) ||
																		void 0 === n
																			? void 0
																			: n.values) || void 0 === r
																	? void 0
																	: r.map((e) =>
																			tA.create(t.field, "==", e),
																		)) || [];
															return tV.create(e, "or");
														}
														return t;
													}
													const i = t.filters.map((t) => e(t));
													return tV.create(i, t.op);
												})(e),
											);
											return (
												r5(t) || _(), r1(t) || r2(t) ? [t] : t.getFilters()
											);
										})(tV.create(e.filters, "and")).map((t) =>
											tj(
												e.path,
												e.collectionGroup,
												e.orderBy,
												t.getFilters(),
												e.limit,
												e.startAt,
												e.endAt,
											),
										)),
							this.ln.set(e, t)),
						t
					);
				}
				En(e, t, n, r, i, s, a) {
					const o = (null != t ? t.length : 1) * Math.max(n.length, i.length),
						l = o / (null != t ? t.length : 1),
						u = [];
					for (let h = 0; h < o; ++h) {
						const o = t ? this.dn(t[h / l]) : r7,
							c = this.An(e, o, n[h % l], r),
							d = this.Rn(e, o, i[h % l], s),
							f = a.map((t) => this.An(e, o, t, !0));
						u.push(...this.createRange(c, d, f));
					}
					return u;
				}
				An(e, t, n, r) {
					const i = new rY(e, $.empty(), t, n);
					return r ? i : i.Zt();
				}
				Rn(e, t, n, r) {
					const i = new rY(e, $.empty(), t, n);
					return r ? i.Zt() : i;
				}
				Pn(e, t) {
					const n = new r0(t),
						r =
							null != t.collectionGroup
								? t.collectionGroup
								: t.path.lastSegment();
					return this.getFieldIndexes(e, r).next((e) => {
						let t = null;
						for (const r of e)
							n.rn(r) && (!t || r.fields.length > t.fields.length) && (t = r);
						return t;
					});
				}
				getIndexType(e, t) {
					let n = 2,
						r = this.hn(t);
					return ei
						.forEach(r, (t) =>
							this.Pn(e, t).next((e) => {
								e
									? 0 !== n &&
										e.fields.length <
											((e) => {
												let t = new eX(G.comparator),
													n = !1;
												for (const r of e.filters)
													for (const e of r.getFlattenedFilters())
														e.field.isKeyField() ||
															("array-contains" === e.op ||
															"array-contains-any" === e.op
																? (n = !0)
																: (t = t.add(e.field)));
												for (const n of e.orderBy)
													n.field.isKeyField() || (t = t.add(n.field));
												return t.size + +!!n;
											})(t) &&
										(n = 1)
									: (n = 0);
							}),
						)
						.next(() => (null !== t.limit && r.length > 1 && 2 === n ? 1 : n));
				}
				Vn(e, t) {
					const n = new rH();
					for (const r of W(e)) {
						const e = t.data.field(r.fieldPath);
						if (null == e) return null;
						const i = n.Yt(r.kind);
						r$.vt.It(e, i);
					}
					return n.zt();
				}
				dn(e) {
					const t = new rH();
					return r$.vt.It(e, t.Yt(0)), t.zt();
				}
				mn(e, t) {
					const n = new rH();
					return (
						r$.vt.It(
							td(this.databaseId, t),
							n.Yt(
								((e) => {
									const t = W(e);
									return 0 === t.length ? 0 : t[t.length - 1].kind;
								})(e),
							),
						),
						n.zt()
					);
				}
				Tn(e, t, n) {
					if (null === n) return [];
					let r = [];
					r.push(new rH());
					let i = 0;
					for (const s of W(e)) {
						const e = n[i++];
						for (const n of r)
							if (this.fn(t, s.fieldPath) && tm(e)) r = this.gn(r, s, e);
							else {
								const t = n.Yt(s.kind);
								r$.vt.It(e, t);
							}
					}
					return this.pn(r);
				}
				In(e, t, n) {
					return this.Tn(e, t, n.position);
				}
				pn(e) {
					const t = [];
					for (let n = 0; n < e.length; ++n) t[n] = e[n].zt();
					return t;
				}
				gn(e, t, n) {
					const r = [...e],
						i = [];
					for (const e of n.arrayValue.values || [])
						for (const n of r) {
							const r = new rH();
							r.seed(n.zt()), r$.vt.It(e, r.Yt(t.kind)), i.push(r);
						}
					return i;
				}
				fn(e, t) {
					return !!e.filters.find(
						(e) =>
							e instanceof tA &&
							e.field.isEqual(t) &&
							("in" === e.op || "not-in" === e.op),
					);
				}
				getFieldIndexes(e, t) {
					const n = ii(e),
						r = is(e);
					return (
						t ? n.U("collectionGroupIndex", IDBKeyRange.bound(t, t)) : n.U()
					).next((e) => {
						const t = [];
						return ei
							.forEach(e, (e) =>
								r.get([e.indexId, this.uid]).next((n) => {
									t.push(
										((e, t) => {
											const n = t
													? new H(
															t.sequenceNumber,
															new Z(
																rA(t.readTime),
																new $(eE(t.documentKey)),
																t.largestBatchId,
															),
														)
													: H.empty(),
												r = e.fields.map(
													([e, t]) => new J(G.fromServerFormat(e), t),
												);
											return new Q(e.indexId, e.collectionGroup, r, n);
										})(e, n),
									);
								}),
							)
							.next(() => t);
					});
				}
				getNextCollectionGroupToUpdate(e) {
					return this.getFieldIndexes(e).next((e) =>
						0 === e.length
							? null
							: (e.sort((e, t) => {
									const n =
										e.indexState.sequenceNumber - t.indexState.sequenceNumber;
									return 0 !== n ? n : L(e.collectionGroup, t.collectionGroup);
								}),
								e[0].collectionGroup),
					);
				}
				updateCollectionGroup(e, t, n) {
					const r = ii(e),
						i = is(e);
					return this.yn(e).next((e) =>
						r
							.U("collectionGroupIndex", IDBKeyRange.bound(t, t))
							.next((t) =>
								ei.forEach(t, (t) => i.put(rP(t.indexId, this.uid, e, n))),
							),
					);
				}
				updateIndexEntries(e, t) {
					const n = new Map();
					return ei.forEach(t, (t, r) => {
						const i = n.get(t.collectionGroup);
						return (
							i ? ei.resolve(i) : this.getFieldIndexes(e, t.collectionGroup)
						).next(
							(i) => (
								n.set(t.collectionGroup, i),
								ei.forEach(i, (n) =>
									this.wn(e, t, n).next((t) => {
										const i = this.Sn(r, n);
										return t.isEqual(i) ? ei.resolve() : this.bn(e, r, n, t, i);
									}),
								)
							),
						);
					});
				}
				Dn(e, t, n, r) {
					return ir(e).put({
						indexId: r.indexId,
						uid: this.uid,
						arrayValue: r.arrayValue,
						directionalValue: r.directionalValue,
						orderedDocumentKey: this.mn(n, t.key),
						documentKey: t.key.path.toArray(),
					});
				}
				vn(e, t, n, r) {
					return ir(e).delete([
						r.indexId,
						this.uid,
						r.arrayValue,
						r.directionalValue,
						this.mn(n, t.key),
						t.key.path.toArray(),
					]);
				}
				wn(e, t, n) {
					let r = ir(e),
						i = new eX(rX);
					return r
						.J(
							{
								index: "documentKeyIndex",
								range: IDBKeyRange.only([n.indexId, this.uid, this.mn(n, t)]),
							},
							(e, r) => {
								i = i.add(
									new rY(n.indexId, t, r.arrayValue, r.directionalValue),
								);
							},
						)
						.next(() => i);
				}
				Sn(e, t) {
					let n = new eX(rX),
						r = this.Vn(t, e);
					if (null == r) return n;
					const i = j(t);
					if (null != i) {
						const s = e.data.field(i.fieldPath);
						if (tm(s))
							for (const i of s.arrayValue.values || [])
								n = n.add(new rY(t.indexId, e.key, this.dn(i), r));
					} else n = n.add(new rY(t.indexId, e.key, r7, r));
					return n;
				}
				bn(e, t, n, r, i) {
					I(
						"IndexedDbIndexManager",
						"Updating index entries for document '%s'",
						t.key,
					);
					const s = [];
					return (
						((e, t, n, r, i) => {
							let s = e.getIterator(),
								a = t.getIterator(),
								o = e0(s),
								l = e0(a);
							while (o || l) {
								let e = !1,
									t = !1;
								if (o && l) {
									const r = n(o, l);
									r < 0 ? (t = !0) : r > 0 && (e = !0);
								} else null != o ? (t = !0) : (e = !0);
								e
									? (r(l), (l = e0(a)))
									: t
										? (i(o), (o = e0(s)))
										: ((o = e0(s)), (l = e0(a)));
							}
						})(
							r,
							i,
							rX,
							(r) => {
								s.push(this.Dn(e, t, n, r));
							},
							(r) => {
								s.push(this.vn(e, t, n, r));
							},
						),
						ei.waitFor(s)
					);
				}
				yn(e) {
					let t = 1;
					return is(e)
						.J(
							{
								index: "sequenceNumberIndex",
								reverse: !0,
								range: IDBKeyRange.upperBound([
									this.uid,
									Number.MAX_SAFE_INTEGER,
								]),
							},
							(e, n, r) => {
								r.done(), (t = n.sequenceNumber + 1);
							},
						)
						.next(() => t);
				}
				createRange(e, t, n) {
					n = n
						.sort((e, t) => rX(e, t))
						.filter((e, t, n) => !t || 0 !== rX(e, n[t - 1]));
					const r = [];
					for (const i of (r.push(e), n)) {
						const n = rX(i, e),
							s = rX(i, t);
						if (0 === n) r[0] = e.Zt();
						else if (n > 0 && s < 0) r.push(i), r.push(i.Zt());
						else if (s > 0) break;
					}
					r.push(t);
					const i = [];
					for (let e = 0; e < r.length; e += 2) {
						if (this.Cn(r[e], r[e + 1])) return [];
						const t = [
								r[e].indexId,
								this.uid,
								r[e].arrayValue,
								r[e].directionalValue,
								r7,
								[],
							],
							n = [
								r[e + 1].indexId,
								this.uid,
								r[e + 1].arrayValue,
								r[e + 1].directionalValue,
								r7,
								[],
							];
						i.push(IDBKeyRange.bound(t, n));
					}
					return i;
				}
				Cn(e, t) {
					return rX(e, t) > 0;
				}
				getMinOffsetFromCollectionGroup(e, t) {
					return this.getFieldIndexes(e, t).next(ia);
				}
				getMinOffset(e, t) {
					return ei
						.mapArray(this.hn(t), (t) => this.Pn(e, t).next((e) => e || _()))
						.next(ia);
				}
			}
			function it(e) {
				return e$(e, "collectionParents");
			}
			function ir(e) {
				return e$(e, "indexEntries");
			}
			function ii(e) {
				return e$(e, "indexConfiguration");
			}
			function is(e) {
				return e$(e, "indexState");
			}
			function ia(e) {
				0 !== e.length || _();
				let t = e[0].indexState.offset,
					n = t.largestBatchId;
				for (let r = 1; r < e.length; r++) {
					const i = e[r].indexState.offset;
					0 > ee(i, t) && (t = i),
						n < i.largestBatchId && (n = i.largestBatchId);
				}
				return new Z(t.readTime, t.documentKey, n);
			}
			const io = {
				didRun: !1,
				sequenceNumbersCollected: 0,
				targetsRemoved: 0,
				documentsRemoved: 0,
			};
			class il {
				constructor(e, t, n) {
					(this.cacheSizeCollectionThreshold = e),
						(this.percentileToCollect = t),
						(this.maximumSequenceNumbersToCollect = n);
				}
				static withCacheSize(e) {
					return new il(
						e,
						il.DEFAULT_COLLECTION_PERCENTILE,
						il.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT,
					);
				}
			}
			function iu(e, t, n) {
				let r = e.store("mutations"),
					i = e.store("documentMutations"),
					s = [],
					a = IDBKeyRange.only(n.batchId),
					o = 0,
					l = r.J({ range: a }, (e, t, n) => (o++, n.delete()));
				s.push(
					l.next(() => {
						1 === o || _();
					}),
				);
				const u = [];
				for (const e of n.mutations) {
					var h, c;
					const r = ((h = e.key.path), (c = n.batchId), [t, eT(h), c]);
					s.push(i.delete(r)), u.push(e.key);
				}
				return ei.waitFor(s).next(() => u);
			}
			function ih(e) {
				let t;
				if (!e) return 0;
				if (e.document) t = e.document;
				else if (e.unknownDocument) t = e.unknownDocument;
				else {
					if (!e.noDocument) throw _();
					t = e.noDocument;
				}
				return JSON.stringify(t).length;
			}
			(il.DEFAULT_COLLECTION_PERCENTILE = 10),
				(il.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT = 1e3),
				(il.DEFAULT = new il(
					0x2800000,
					il.DEFAULT_COLLECTION_PERCENTILE,
					il.DEFAULT_MAX_SEQUENCE_NUMBERS_TO_COLLECT,
				)),
				(il.DISABLED = new il(-1, 0, 0));
			class ic {
				constructor(e, t, n, r) {
					(this.userId = e),
						(this.serializer = t),
						(this.indexManager = n),
						(this.referenceDelegate = r),
						(this.Fn = {});
				}
				static lt(e, t, n, r) {
					return (
						"" !== e.uid || _(),
						new ic(e.isAuthenticated() ? e.uid : "", t, n, r)
					);
				}
				checkEmpty(e) {
					let t = !0,
						n = IDBKeyRange.bound(
							[this.userId, Number.NEGATIVE_INFINITY],
							[this.userId, Number.POSITIVE_INFINITY],
						);
					return im(e)
						.J({ index: "userMutationsIndex", range: n }, (e, n, r) => {
							(t = !1), r.done();
						})
						.next(() => t);
				}
				addMutationBatch(e, t, n, r) {
					const i = ig(e),
						s = im(e);
					return s.add({}).next((a) => {
						var o;
						"number" == typeof a || _();
						let l = new nz(a, t, n, r),
							u = ((e, t, n) => {
								const r = n.baseMutations.map((t) => rw(e.ct, t)),
									i = n.mutations.map((t) => rw(e.ct, t));
								return {
									userId: t,
									batchId: n.batchId,
									localWriteTimeMs: n.localWriteTime.toMillis(),
									baseMutations: r,
									mutations: i,
								};
							})(this.serializer, this.userId, l),
							h = [],
							c = new eX((e, t) => L(e.canonicalString(), t.canonicalString()));
						for (const e of r) {
							const t = ((o = this.userId), [o, eT(e.key.path), a]);
							(c = c.add(e.key.path.popLast())),
								h.push(s.put(u)),
								h.push(i.put(t, e_));
						}
						return (
							c.forEach((t) => {
								h.push(this.indexManager.addToCollectionParentIndex(e, t));
							}),
							e.addOnCommittedListener(() => {
								this.Fn[a] = l.keys();
							}),
							ei.waitFor(h).next(() => l)
						);
					});
				}
				lookupMutationBatch(e, t) {
					return im(e)
						.get(t)
						.next((e) =>
							e
								? (e.userId === this.userId || _(), rV(this.serializer, e))
								: null,
						);
				}
				Mn(e, t) {
					return this.Fn[t]
						? ei.resolve(this.Fn[t])
						: this.lookupMutationBatch(e, t).next((e) => {
								if (e) {
									const n = e.keys();
									return (this.Fn[t] = n), n;
								}
								return null;
							});
				}
				getNextMutationBatchAfterBatchId(e, t) {
					let n = t + 1,
						r = IDBKeyRange.lowerBound([this.userId, n]),
						i = null;
					return im(e)
						.J({ index: "userMutationsIndex", range: r }, (e, t, r) => {
							t.userId === this.userId &&
								(t.batchId >= n || _(), (i = rV(this.serializer, t))),
								r.done();
						})
						.next(() => i);
				}
				getHighestUnacknowledgedBatchId(e) {
					let t = IDBKeyRange.upperBound([
							this.userId,
							Number.POSITIVE_INFINITY,
						]),
						n = -1;
					return im(e)
						.J(
							{ index: "userMutationsIndex", range: t, reverse: !0 },
							(e, t, r) => {
								(n = t.batchId), r.done();
							},
						)
						.next(() => n);
				}
				getAllMutationBatches(e) {
					const t = IDBKeyRange.bound(
						[this.userId, -1],
						[this.userId, Number.POSITIVE_INFINITY],
					);
					return im(e)
						.U("userMutationsIndex", t)
						.next((e) => e.map((e) => rV(this.serializer, e)));
				}
				getAllMutationBatchesAffectingDocumentKey(e, t) {
					const n = [this.userId, eT(t.path)],
						r = IDBKeyRange.lowerBound(n),
						i = [];
					return ig(e)
						.J({ range: r }, (n, r, s) => {
							const [a, o, l] = n,
								u = eE(o);
							if (a === this.userId && t.path.isEqual(u))
								return im(e)
									.get(l)
									.next((e) => {
										if (!e) throw _();
										e.userId === this.userId || _(),
											i.push(rV(this.serializer, e));
									});
							s.done();
						})
						.next(() => i);
				}
				getAllMutationBatchesAffectingDocumentKeys(e, t) {
					let n = new eX(L),
						r = [];
					return (
						t.forEach((t) => {
							const i = [this.userId, eT(t.path)],
								s = IDBKeyRange.lowerBound(i),
								a = ig(e).J({ range: s }, (e, r, i) => {
									const [s, a, o] = e,
										l = eE(a);
									s === this.userId && t.path.isEqual(l)
										? (n = n.add(o))
										: i.done();
								});
							r.push(a);
						}),
						ei.waitFor(r).next(() => this.xn(e, n))
					);
				}
				getAllMutationBatchesAffectingQuery(e, t) {
					let n = t.path,
						r = n.length + 1,
						i = [this.userId, eT(n)],
						s = IDBKeyRange.lowerBound(i),
						a = new eX(L);
					return ig(e)
						.J({ range: s }, (e, t, i) => {
							const [s, o, l] = e,
								u = eE(o);
							s === this.userId && n.isPrefixOf(u)
								? u.length === r && (a = a.add(l))
								: i.done();
						})
						.next(() => this.xn(e, a));
				}
				xn(e, t) {
					const n = [],
						r = [];
					return (
						t.forEach((t) => {
							r.push(
								im(e)
									.get(t)
									.next((e) => {
										if (null === e) throw _();
										e.userId === this.userId || _(),
											n.push(rV(this.serializer, e));
									}),
							);
						}),
						ei.waitFor(r).next(() => n)
					);
				}
				removeMutationBatch(e, t) {
					return iu(e._e, this.userId, t).next(
						(n) => (
							e.addOnCommittedListener(() => {
								this.On(t.batchId);
							}),
							ei.forEach(n, (t) =>
								this.referenceDelegate.markPotentiallyOrphaned(e, t),
							)
						),
					);
				}
				On(e) {
					delete this.Fn[e];
				}
				performConsistencyCheck(e) {
					return this.checkEmpty(e).next((t) => {
						if (!t) return ei.resolve();
						const n = IDBKeyRange.lowerBound([this.userId]),
							r = [];
						return ig(e)
							.J({ range: n }, (e, t, n) => {
								if (e[0] === this.userId) {
									const t = eE(e[1]);
									r.push(t);
								} else n.done();
							})
							.next(() => {
								0 === r.length || _();
							});
					});
				}
				containsKey(e, t) {
					return id(e, this.userId, t);
				}
				Nn(e) {
					return ip(e)
						.get(this.userId)
						.next(
							(e) =>
								e || {
									userId: this.userId,
									lastAcknowledgedBatchId: -1,
									lastStreamToken: "",
								},
						);
				}
			}
			function id(e, t, n) {
				let r = [t, eT(n.path)],
					i = r[1],
					s = IDBKeyRange.lowerBound(r),
					a = !1;
				return ig(e)
					.J({ range: s, H: !0 }, (e, n, r) => {
						const [s, o, l] = e;
						s === t && o === i && (a = !0), r.done();
					})
					.next(() => a);
			}
			function im(e) {
				return e$(e, "mutations");
			}
			function ig(e) {
				return e$(e, "documentMutations");
			}
			function ip(e) {
				return e$(e, "mutationQueues");
			}
			class iy {
				constructor(e) {
					this.Ln = e;
				}
				next() {
					return (this.Ln += 2), this.Ln;
				}
				static Bn() {
					return new iy(0);
				}
				static kn() {
					return new iy(-1);
				}
			}
			class iw {
				constructor(e, t) {
					(this.referenceDelegate = e), (this.serializer = t);
				}
				allocateTargetId(e) {
					return this.qn(e).next((t) => {
						const n = new iy(t.highestTargetId);
						return (
							(t.highestTargetId = n.next()),
							this.Qn(e, t).next(() => t.highestTargetId)
						);
					});
				}
				getLastRemoteSnapshotVersion(e) {
					return this.qn(e).next((e) =>
						q.fromTimestamp(
							new U(
								e.lastRemoteSnapshotVersion.seconds,
								e.lastRemoteSnapshotVersion.nanoseconds,
							),
						),
					);
				}
				getHighestSequenceNumber(e) {
					return this.qn(e).next((e) => e.highestListenSequenceNumber);
				}
				setTargetsMetadata(e, t, n) {
					return this.qn(e).next(
						(r) => (
							(r.highestListenSequenceNumber = t),
							n && (r.lastRemoteSnapshotVersion = n.toTimestamp()),
							t > r.highestListenSequenceNumber &&
								(r.highestListenSequenceNumber = t),
							this.Qn(e, r)
						),
					);
				}
				addTargetData(e, t) {
					return this.Kn(e, t).next(() =>
						this.qn(e).next(
							(n) => ((n.targetCount += 1), this.$n(t, n), this.Qn(e, n)),
						),
					);
				}
				updateTargetData(e, t) {
					return this.Kn(e, t);
				}
				removeTargetData(e, t) {
					return this.removeMatchingKeysForTargetId(e, t.targetId)
						.next(() => iv(e).delete(t.targetId))
						.next(() => this.qn(e))
						.next(
							(t) => (
								t.targetCount > 0 || _(), (t.targetCount -= 1), this.Qn(e, t)
							),
						);
				}
				removeTargets(e, t, n) {
					let r = 0,
						i = [];
					return iv(e)
						.J((s, a) => {
							const o = rR(a);
							o.sequenceNumber <= t &&
								null === n.get(o.targetId) &&
								(r++, i.push(this.removeTargetData(e, o)));
						})
						.next(() => ei.waitFor(i))
						.next(() => r);
				}
				forEachTarget(e, t) {
					return iv(e).J((e, n) => {
						t(rR(n));
					});
				}
				qn(e) {
					return iI(e)
						.get("targetGlobalKey")
						.next((e) => (null !== e || _(), e));
				}
				Qn(e, t) {
					return iI(e).put("targetGlobalKey", t);
				}
				Kn(e, t) {
					return iv(e).put(rO(this.serializer, t));
				}
				$n(e, t) {
					let n = !1;
					return (
						e.targetId > t.highestTargetId &&
							((t.highestTargetId = e.targetId), (n = !0)),
						e.sequenceNumber > t.highestListenSequenceNumber &&
							((t.highestListenSequenceNumber = e.sequenceNumber), (n = !0)),
						n
					);
				}
				getTargetCount(e) {
					return this.qn(e).next((e) => e.targetCount);
				}
				getTargetData(e, t) {
					let n = tW(t),
						r = IDBKeyRange.bound(
							[n, Number.NEGATIVE_INFINITY],
							[n, Number.POSITIVE_INFINITY],
						),
						i = null;
					return iv(e)
						.J({ range: r, index: "queryTargetsIndex" }, (e, n, r) => {
							const s = rR(n);
							tJ(t, s.target) && ((i = s), r.done());
						})
						.next(() => i);
				}
				addMatchingKeys(e, t, n) {
					const r = [],
						i = iT(e);
					return (
						t.forEach((t) => {
							const s = eT(t.path);
							r.push(i.put({ targetId: n, path: s })),
								r.push(this.referenceDelegate.addReference(e, n, t));
						}),
						ei.waitFor(r)
					);
				}
				removeMatchingKeys(e, t, n) {
					const r = iT(e);
					return ei.forEach(t, (t) => {
						const i = eT(t.path);
						return ei.waitFor([
							r.delete([n, i]),
							this.referenceDelegate.removeReference(e, n, t),
						]);
					});
				}
				removeMatchingKeysForTargetId(e, t) {
					const n = iT(e),
						r = IDBKeyRange.bound([t], [t + 1], !1, !0);
					return n.delete(r);
				}
				getMatchingKeysForTargetId(e, t) {
					let n = IDBKeyRange.bound([t], [t + 1], !1, !0),
						r = iT(e),
						i = nf();
					return r
						.J({ range: n, H: !0 }, (e, t, n) => {
							const r = new $(eE(e[1]));
							i = i.add(r);
						})
						.next(() => i);
				}
				containsKey(e, t) {
					let n = eT(t.path),
						r = IDBKeyRange.bound([n], [n + "\0"], !1, !0),
						i = 0;
					return iT(e)
						.J(
							{ index: "documentTargetsIndex", H: !0, range: r },
							([e, t], n, r) => {
								0 !== e && (i++, r.done());
							},
						)
						.next(() => i > 0);
				}
				ot(e, t) {
					return iv(e)
						.get(t)
						.next((e) => (e ? rR(e) : null));
				}
			}
			function iv(e) {
				return e$(e, "targets");
			}
			function iI(e) {
				return e$(e, "targetGlobal");
			}
			function iT(e) {
				return e$(e, "targetDocuments");
			}
			function iE([e, t], [n, r]) {
				const i = L(e, n);
				return 0 === i ? L(t, r) : i;
			}
			class ib {
				constructor(e) {
					(this.Un = e), (this.buffer = new eX(iE)), (this.Wn = 0);
				}
				Gn() {
					return ++this.Wn;
				}
				zn(e) {
					const t = [e, this.Gn()];
					if (this.buffer.size < this.Un) this.buffer = this.buffer.add(t);
					else {
						const e = this.buffer.last();
						0 > iE(t, e) && (this.buffer = this.buffer.delete(e).add(t));
					}
				}
				get maxValue() {
					return this.buffer.last()[0];
				}
			}
			class i_ {
				constructor(e, t, n) {
					(this.garbageCollector = e),
						(this.asyncQueue = t),
						(this.localStore = n),
						(this.jn = null);
				}
				start() {
					-1 !== this.garbageCollector.params.cacheSizeCollectionThreshold &&
						this.Hn(6e4);
				}
				stop() {
					this.jn && (this.jn.cancel(), (this.jn = null));
				}
				get started() {
					return null !== this.jn;
				}
				Hn(e) {
					I("LruGarbageCollector", `Garbage collection scheduled in ${e}ms`),
						(this.jn = this.asyncQueue.enqueueAfterDelay(
							"lru_garbage_collection",
							e,
							async () => {
								this.jn = null;
								try {
									await this.localStore.collectGarbage(this.garbageCollector);
								} catch (e) {
									eh(e)
										? I(
												"LruGarbageCollector",
												"Ignoring IndexedDB error during garbage collection: ",
												e,
											)
										: await er(e);
								}
								await this.Hn(3e5);
							},
						));
				}
			}
			class iS {
				constructor(e, t) {
					(this.Jn = e), (this.params = t);
				}
				calculateTargetCount(e, t) {
					return this.Jn.Yn(e).next((e) => Math.floor((t / 100) * e));
				}
				nthSequenceNumber(e, t) {
					if (0 === t) return ei.resolve(ey.oe);
					const n = new ib(t);
					return this.Jn.forEachTarget(e, (e) => n.zn(e.sequenceNumber))
						.next(() => this.Jn.Zn(e, (e) => n.zn(e)))
						.next(() => n.maxValue);
				}
				removeTargets(e, t, n) {
					return this.Jn.removeTargets(e, t, n);
				}
				removeOrphanedDocuments(e, t) {
					return this.Jn.removeOrphanedDocuments(e, t);
				}
				collect(e, t) {
					return -1 === this.params.cacheSizeCollectionThreshold
						? (I("LruGarbageCollector", "Garbage collection skipped; disabled"),
							ei.resolve(io))
						: this.getCacheSize(e).next((n) =>
								n < this.params.cacheSizeCollectionThreshold
									? (I(
											"LruGarbageCollector",
											`Garbage collection skipped; Cache size ${n} is lower than threshold ${this.params.cacheSizeCollectionThreshold}`,
										),
										io)
									: this.Xn(e, t),
							);
				}
				getCacheSize(e) {
					return this.Jn.getCacheSize(e);
				}
				Xn(e, t) {
					let n, r, i, s, a, o, l;
					const h = Date.now();
					return this.calculateTargetCount(e, this.params.percentileToCollect)
						.next(
							(t) => (
								t > this.params.maximumSequenceNumbersToCollect
									? (I(
											"LruGarbageCollector",
											`Capping sequence numbers to collect down to the maximum of ${this.params.maximumSequenceNumbersToCollect} from ${t}`,
										),
										(r = this.params.maximumSequenceNumbersToCollect))
									: (r = t),
								(s = Date.now()),
								this.nthSequenceNumber(e, r)
							),
						)
						.next(
							(r) => ((n = r), (a = Date.now()), this.removeTargets(e, n, t)),
						)
						.next(
							(t) => (
								(i = t), (o = Date.now()), this.removeOrphanedDocuments(e, n)
							),
						)
						.next(
							(e) => (
								(l = Date.now()),
								v() <= u.$b.DEBUG &&
									I(
										"LruGarbageCollector",
										`LRU Garbage Collection
	Counted targets in ${s - h}ms
	Determined least recently used ${r} in ` +
											(a - s) +
											"ms\n" +
											`	Removed ${i} targets in ` +
											(o - a) +
											"ms\n" +
											`	Removed ${e} documents in ` +
											(l - o) +
											"ms\n" +
											`Total Duration: ${l - h}ms`,
									),
								ei.resolve({
									didRun: !0,
									sequenceNumbersCollected: r,
									targetsRemoved: i,
									documentsRemoved: e,
								})
							),
						);
				}
			}
			class ix {
				constructor(e, t) {
					(this.db = e), (this.garbageCollector = new iS(this, t));
				}
				Yn(e) {
					const t = this.er(e);
					return this.db
						.getTargetCache()
						.getTargetCount(e)
						.next((e) => t.next((t) => e + t));
				}
				er(e) {
					let t = 0;
					return this.Zn(e, (e) => {
						t++;
					}).next(() => t);
				}
				forEachTarget(e, t) {
					return this.db.getTargetCache().forEachTarget(e, t);
				}
				Zn(e, t) {
					return this.tr(e, (e, n) => t(n));
				}
				addReference(e, t, n) {
					return iD(e, n);
				}
				removeReference(e, t, n) {
					return iD(e, n);
				}
				removeTargets(e, t, n) {
					return this.db.getTargetCache().removeTargets(e, t, n);
				}
				markPotentiallyOrphaned(e, t) {
					return iD(e, t);
				}
				nr(e, t) {
					let n;
					return (
						(n = !1),
						ip(e)
							.Y((r) =>
								id(e, r, t).next((e) => (e && (n = !0), ei.resolve(!e))),
							)
							.next(() => n)
					);
				}
				removeOrphanedDocuments(e, t) {
					let n = this.db.getRemoteDocumentCache().newChangeBuffer(),
						r = [],
						i = 0;
					return this.tr(e, (s, a) => {
						if (a <= t) {
							const t = this.nr(e, s).next((t) => {
								if (!t)
									return (
										i++,
										n
											.getEntry(e, s)
											.next(
												() => (
													n.removeEntry(s, q.min()),
													iT(e).delete([0, eT(s.path)])
												),
											)
									);
							});
							r.push(t);
						}
					})
						.next(() => ei.waitFor(r))
						.next(() => n.apply(e))
						.next(() => i);
				}
				removeTarget(e, t) {
					const n = t.withSequenceNumber(e.currentSequenceNumber);
					return this.db.getTargetCache().updateTargetData(e, n);
				}
				updateLimboDocument(e, t) {
					return iD(e, t);
				}
				tr(e, t) {
					let n = iT(e),
						r,
						i = ey.oe;
					return n
						.J(
							{ index: "documentTargetsIndex" },
							([e, n], { path: s, sequenceNumber: a }) => {
								0 === e
									? (i !== ey.oe && t(new $(eE(r)), i), (i = a), (r = s))
									: (i = ey.oe);
							},
						)
						.next(() => {
							i !== ey.oe && t(new $(eE(r)), i);
						});
				}
				getCacheSize(e) {
					return this.db.getRemoteDocumentCache().getSize(e);
				}
			}
			function iD(e, t) {
				var n;
				return iT(e).put(
					((n = e.currentSequenceNumber),
					{ targetId: 0, path: eT(t.path), sequenceNumber: n }),
				);
			}
			class iC {
				constructor() {
					(this.changes = new ns(
						(e) => e.toString(),
						(e, t) => e.isEqual(t),
					)),
						(this.changesApplied = !1);
				}
				addEntry(e) {
					this.assertNotApplied(), this.changes.set(e.key, e);
				}
				removeEntry(e, t) {
					this.assertNotApplied(),
						this.changes.set(e, tS.newInvalidDocument(e).setReadTime(t));
				}
				getEntry(e, t) {
					this.assertNotApplied();
					const n = this.changes.get(t);
					return void 0 !== n ? ei.resolve(n) : this.getFromCache(e, t);
				}
				getEntries(e, t) {
					return this.getAllFromCache(e, t);
				}
				apply(e) {
					return (
						this.assertNotApplied(),
						(this.changesApplied = !0),
						this.applyChanges(e)
					);
				}
				assertNotApplied() {}
			}
			class iN {
				constructor(e) {
					this.serializer = e;
				}
				setIndexManager(e) {
					this.indexManager = e;
				}
				addEntry(e, t, n) {
					return iV(e).put(n);
				}
				removeEntry(e, t, n) {
					return iV(e).delete(
						((e, t) => {
							const n = e.path.toArray();
							return [
								n.slice(0, n.length - 2),
								n[n.length - 2],
								rN(t),
								n[n.length - 1],
							];
						})(t, n),
					);
				}
				updateMetadata(e, t) {
					return this.getMetadata(e).next(
						(n) => ((n.byteSize += t), this.rr(e, n)),
					);
				}
				getEntry(e, t) {
					let n = tS.newInvalidDocument(t);
					return iV(e)
						.J(
							{ index: "documentKeyIndex", range: IDBKeyRange.only(iR(t)) },
							(e, r) => {
								n = this.ir(t, r);
							},
						)
						.next(() => n);
				}
				sr(e, t) {
					let n = { size: 0, document: tS.newInvalidDocument(t) };
					return iV(e)
						.J(
							{ index: "documentKeyIndex", range: IDBKeyRange.only(iR(t)) },
							(e, r) => {
								n = { document: this.ir(t, r), size: ih(r) };
							},
						)
						.next(() => n);
				}
				getEntries(e, t) {
					let n = na;
					return this._r(e, t, (e, t) => {
						const r = this.ir(e, t);
						n = n.insert(e, r);
					}).next(() => n);
				}
				ar(e, t) {
					let n = na,
						r = new eJ($.comparator);
					return this._r(e, t, (e, t) => {
						const i = this.ir(e, t);
						(n = n.insert(e, i)), (r = r.insert(e, ih(t)));
					}).next(() => ({ documents: n, ur: r }));
				}
				_r(e, t, n) {
					if (t.isEmpty()) return ei.resolve();
					let r = new eX(iM);
					t.forEach((e) => (r = r.add(e)));
					let i = IDBKeyRange.bound(iR(r.first()), iR(r.last())),
						s = r.getIterator(),
						a = s.getNext();
					return iV(e)
						.J({ index: "documentKeyIndex", range: i }, (e, t, r) => {
							const i = $.fromSegments([
								...t.prefixPath,
								t.collectionGroup,
								t.documentId,
							]);
							while (a && 0 > iM(a, i)) n(a, null), (a = s.getNext());
							a &&
								a.isEqual(i) &&
								(n(a, t), (a = s.hasNext() ? s.getNext() : null)),
								a ? r.$(iR(a)) : r.done();
						})
						.next(() => {
							while (a) n(a, null), (a = s.hasNext() ? s.getNext() : null);
						});
				}
				getDocumentsMatchingQuery(e, t, n, r, i) {
					const s = t.path,
						a = [
							s.popLast().toArray(),
							s.lastSegment(),
							rN(n.readTime),
							n.documentKey.path.isEmpty()
								? ""
								: n.documentKey.path.lastSegment(),
						],
						o = [
							s.popLast().toArray(),
							s.lastSegment(),
							[Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER],
							"",
						];
					return iV(e)
						.U(IDBKeyRange.bound(a, o, !0))
						.next((e) => {
							null == i || i.incrementDocumentReadCount(e.length);
							let n = na;
							for (const i of e) {
								const e = this.ir(
									$.fromSegments(
										i.prefixPath.concat(i.collectionGroup, i.documentId),
									),
									i,
								);
								e.isFoundDocument() &&
									(nn(t, e) || r.has(e.key)) &&
									(n = n.insert(e.key, e));
							}
							return n;
						});
				}
				getAllFromCollectionGroup(e, t, n, r) {
					let i = na,
						s = iO(t, n),
						a = iO(t, Z.max());
					return iV(e)
						.J(
							{
								index: "collectionGroupIndex",
								range: IDBKeyRange.bound(s, a, !0),
							},
							(e, t, n) => {
								const s = this.ir(
									$.fromSegments(
										t.prefixPath.concat(t.collectionGroup, t.documentId),
									),
									t,
								);
								(i = i.insert(s.key, s)).size === r && n.done();
							},
						)
						.next(() => i);
				}
				newChangeBuffer(e) {
					return new ik(this, !!e && e.trackRemovals);
				}
				getSize(e) {
					return this.getMetadata(e).next((e) => e.byteSize);
				}
				getMetadata(e) {
					return iA(e)
						.get("remoteDocumentGlobalKey")
						.next((e) => (e || _(), e));
				}
				rr(e, t) {
					return iA(e).put("remoteDocumentGlobalKey", t);
				}
				ir(e, t) {
					if (t) {
						const e = ((e, t) => {
							let n;
							if (t.document)
								n = ry(e.ct, t.document, !!t.hasCommittedMutations);
							else if (t.noDocument) {
								const e = $.fromSegments(t.noDocument.path),
									r = rA(t.noDocument.readTime);
								(n = tS.newNoDocument(e, r)),
									t.hasCommittedMutations && n.setHasCommittedMutations();
							} else {
								if (!t.unknownDocument) return _();
								{
									const e = $.fromSegments(t.unknownDocument.path),
										r = rA(t.unknownDocument.version);
									n = tS.newUnknownDocument(e, r);
								}
							}
							return (
								t.readTime &&
									n.setReadTime(
										((e) => {
											const t = new U(e[0], e[1]);
											return q.fromTimestamp(t);
										})(t.readTime),
									),
								n
							);
						})(this.serializer, t);
						if (!(e.isNoDocument() && e.version.isEqual(q.min()))) return e;
					}
					return tS.newInvalidDocument(e);
				}
			}
			class ik extends iC {
				constructor(e, t) {
					super(),
						(this.cr = e),
						(this.trackRemovals = t),
						(this.lr = new ns(
							(e) => e.toString(),
							(e, t) => e.isEqual(t),
						));
				}
				applyChanges(e) {
					let t = [],
						n = 0,
						r = new eX((e, t) => L(e.canonicalString(), t.canonicalString()));
					return (
						this.changes.forEach((i, s) => {
							const a = this.lr.get(i);
							if (
								(t.push(this.cr.removeEntry(e, i, a.readTime)),
								s.isValidDocument())
							) {
								const o = rC(this.cr.serializer, s);
								r = r.add(i.path.popLast());
								const l = ih(o);
								(n += l - a.size), t.push(this.cr.addEntry(e, i, o));
							} else if (((n -= a.size), this.trackRemovals)) {
								const n = rC(
									this.cr.serializer,
									s.convertToNoDocument(q.min()),
								);
								t.push(this.cr.addEntry(e, i, n));
							}
						}),
						r.forEach((n) => {
							t.push(this.cr.indexManager.addToCollectionParentIndex(e, n));
						}),
						t.push(this.cr.updateMetadata(e, n)),
						ei.waitFor(t)
					);
				}
				getFromCache(e, t) {
					return this.cr
						.sr(e, t)
						.next(
							(e) => (
								this.lr.set(t, { size: e.size, readTime: e.document.readTime }),
								e.document
							),
						);
				}
				getAllFromCache(e, t) {
					return this.cr.ar(e, t).next(
						({ documents: e, ur: t }) => (
							t.forEach((t, n) => {
								this.lr.set(t, { size: n, readTime: e.get(t).readTime });
							}),
							e
						),
					);
				}
			}
			function iA(e) {
				return e$(e, "remoteDocumentGlobal");
			}
			function iV(e) {
				return e$(e, "remoteDocumentsV14");
			}
			function iR(e) {
				const t = e.path.toArray();
				return [t.slice(0, t.length - 2), t[t.length - 2], t[t.length - 1]];
			}
			function iO(e, t) {
				const n = t.documentKey.path.toArray();
				return [
					e,
					rN(t.readTime),
					n.slice(0, n.length - 2),
					n.length > 0 ? n[n.length - 1] : "",
				];
			}
			function iM(e, t) {
				let n = e.path.toArray(),
					r = t.path.toArray(),
					i = 0;
				for (let e = 0; e < n.length - 2 && e < r.length - 2; ++e)
					if ((i = L(n[e], r[e]))) return i;
				return (
					(i = L(n.length, r.length)) ||
					(i = L(n[n.length - 2], r[r.length - 2])) ||
					L(n[n.length - 1], r[r.length - 1])
				);
			}
			class iF {
				constructor(e, t) {
					(this.overlayedDocument = e), (this.mutatedFields = t);
				}
			}
			class iL {
				constructor(e, t, n, r) {
					(this.remoteDocumentCache = e),
						(this.mutationQueue = t),
						(this.documentOverlayCache = n),
						(this.indexManager = r);
				}
				getDocument(e, t) {
					let n = null;
					return this.documentOverlayCache
						.getOverlay(e, t)
						.next((r) => ((n = r), this.remoteDocumentCache.getEntry(e, t)))
						.next(
							(e) => (null !== n && nO(n.mutation, e, e1.empty(), U.now()), e),
						);
				}
				getDocuments(e, t) {
					return this.remoteDocumentCache
						.getEntries(e, t)
						.next((t) =>
							this.getLocalViewOfDocuments(e, t, nf()).next(() => t),
						);
				}
				getLocalViewOfDocuments(e, t, n = nf()) {
					const r = nh();
					return this.populateOverlays(e, r, t).next(() =>
						this.computeViews(e, t, r, n).next((e) => {
							let t = nl();
							return (
								e.forEach((e, n) => {
									t = t.insert(e, n.overlayedDocument);
								}),
								t
							);
						}),
					);
				}
				getOverlayedDocuments(e, t) {
					const n = nh();
					return this.populateOverlays(e, n, t).next(() =>
						this.computeViews(e, t, n, nf()),
					);
				}
				populateOverlays(e, t, n) {
					const r = [];
					return (
						n.forEach((e) => {
							t.has(e) || r.push(e);
						}),
						this.documentOverlayCache.getOverlays(e, r).next((e) => {
							e.forEach((e, n) => {
								t.set(e, n);
							});
						})
					);
				}
				computeViews(e, t, n, r) {
					let i = na,
						s = nh(),
						a = nh();
					return (
						t.forEach((e, t) => {
							const a = n.get(t.key);
							r.has(t.key) && (void 0 === a || a.mutation instanceof nL)
								? (i = i.insert(t.key, t))
								: void 0 !== a
									? (s.set(t.key, a.mutation.getFieldMask()),
										nO(a.mutation, t, a.mutation.getFieldMask(), U.now()))
									: s.set(t.key, e1.empty());
						}),
						this.recalculateAndSaveOverlays(e, i).next(
							(e) => (
								e.forEach((e, t) => s.set(e, t)),
								t.forEach((e, t) => {
									var n;
									return a.set(
										e,
										new iF(
											t,
											null !== (n = s.get(e)) && void 0 !== n ? n : null,
										),
									);
								}),
								a
							),
						)
					);
				}
				recalculateAndSaveOverlays(e, t) {
					let n = nh(),
						r = new eJ((e, t) => e - t),
						i = nf();
					return this.mutationQueue
						.getAllMutationBatchesAffectingDocumentKeys(e, t)
						.next((e) => {
							for (const i of e)
								i.keys().forEach((e) => {
									const s = t.get(e);
									if (null === s) return;
									let a = n.get(e) || e1.empty();
									(a = i.applyToLocalView(s, a)), n.set(e, a);
									const o = (r.get(i.batchId) || nf()).add(e);
									r = r.insert(i.batchId, o);
								});
						})
						.next(() => {
							const s = [],
								a = r.getReverseIterator();
							while (a.hasNext()) {
								const r = a.getNext(),
									o = r.key,
									l = r.value,
									u = nh();
								l.forEach((e) => {
									if (!i.has(e)) {
										const r = nR(t.get(e), n.get(e));
										null !== r && u.set(e, r), (i = i.add(e));
									}
								}),
									s.push(this.documentOverlayCache.saveOverlays(e, o, u));
							}
							return ei.waitFor(s);
						})
						.next(() => n);
				}
				recalculateAndSaveOverlaysForDocumentKeys(e, t) {
					return this.remoteDocumentCache
						.getEntries(e, t)
						.next((t) => this.recalculateAndSaveOverlays(e, t));
				}
				getDocumentsMatchingQuery(e, t, n, r) {
					return $.isDocumentKey(t.path) &&
						null === t.collectionGroup &&
						0 === t.filters.length
						? this.getDocumentsMatchingDocumentQuery(e, t.path)
						: t5(t)
							? this.getDocumentsMatchingCollectionGroupQuery(e, t, n, r)
							: this.getDocumentsMatchingCollectionQuery(e, t, n, r);
				}
				getNextDocuments(e, t, n, r) {
					return this.remoteDocumentCache
						.getAllFromCollectionGroup(e, t, n, r)
						.next((i) => {
							let s =
									r - i.size > 0
										? this.documentOverlayCache.getOverlaysForCollectionGroup(
												e,
												t,
												n.largestBatchId,
												r - i.size,
											)
										: ei.resolve(nh()),
								a = -1,
								o = i;
							return s.next((t) =>
								ei
									.forEach(
										t,
										(t, n) => (
											a < n.largestBatchId && (a = n.largestBatchId),
											i.get(t)
												? ei.resolve()
												: this.remoteDocumentCache.getEntry(e, t).next((e) => {
														o = o.insert(t, e);
													})
										),
									)
									.next(() => this.populateOverlays(e, t, i))
									.next(() => this.computeViews(e, o, t, nf()))
									.next((e) => ({ batchId: a, changes: nu(e) })),
							);
						});
				}
				getDocumentsMatchingDocumentQuery(e, t) {
					return this.getDocument(e, new $(t)).next((e) => {
						let t = nl();
						return e.isFoundDocument() && (t = t.insert(e.key, e)), t;
					});
				}
				getDocumentsMatchingCollectionGroupQuery(e, t, n, r) {
					let i = t.collectionGroup,
						s = nl();
					return this.indexManager.getCollectionParents(e, i).next((a) =>
						ei
							.forEach(a, (a) => {
								const o = new t0(
									a.child(i),
									null,
									t.explicitOrderBy.slice(),
									t.filters.slice(),
									t.limit,
									t.limitType,
									t.startAt,
									t.endAt,
								);
								return this.getDocumentsMatchingCollectionQuery(
									e,
									o,
									n,
									r,
								).next((e) => {
									e.forEach((e, t) => {
										s = s.insert(e, t);
									});
								});
							})
							.next(() => s),
					);
				}
				getDocumentsMatchingCollectionQuery(e, t, n, r) {
					let i;
					return this.documentOverlayCache
						.getOverlaysForCollection(e, t.path, n.largestBatchId)
						.next(
							(s) => (
								(i = s),
								this.remoteDocumentCache.getDocumentsMatchingQuery(
									e,
									t,
									n,
									i,
									r,
								)
							),
						)
						.next((e) => {
							i.forEach((t, n) => {
								const r = n.getKey();
								null === e.get(r) &&
									(e = e.insert(r, tS.newInvalidDocument(r)));
							});
							let n = nl();
							return (
								e.forEach((e, r) => {
									const s = i.get(e);
									void 0 !== s && nO(s.mutation, r, e1.empty(), U.now()),
										nn(t, r) && (n = n.insert(e, r));
								}),
								n
							);
						});
				}
			}
			class iP {
				constructor(e) {
					(this.serializer = e), (this.hr = new Map()), (this.Pr = new Map());
				}
				getBundleMetadata(e, t) {
					return ei.resolve(this.hr.get(t));
				}
				saveBundleMetadata(e, t) {
					return (
						this.hr.set(t.id, {
							id: t.id,
							version: t.version,
							createTime: ra(t.createTime),
						}),
						ei.resolve()
					);
				}
				getNamedQuery(e, t) {
					return ei.resolve(this.Pr.get(t));
				}
				saveNamedQuery(e, t) {
					return (
						this.Pr.set(t.name, {
							name: t.name,
							query: rM(t.bundledQuery),
							readTime: ra(t.readTime),
						}),
						ei.resolve()
					);
				}
			}
			class iU {
				constructor() {
					(this.overlays = new eJ($.comparator)), (this.Ir = new Map());
				}
				getOverlay(e, t) {
					return ei.resolve(this.overlays.get(t));
				}
				getOverlays(e, t) {
					const n = nh();
					return ei
						.forEach(t, (t) =>
							this.getOverlay(e, t).next((e) => {
								null !== e && n.set(t, e);
							}),
						)
						.next(() => n);
				}
				saveOverlays(e, t, n) {
					return (
						n.forEach((n, r) => {
							this.ht(e, t, r);
						}),
						ei.resolve()
					);
				}
				removeOverlaysForBatchId(e, t, n) {
					const r = this.Ir.get(n);
					return (
						void 0 !== r &&
							(r.forEach((e) => (this.overlays = this.overlays.remove(e))),
							this.Ir.delete(n)),
						ei.resolve()
					);
				}
				getOverlaysForCollection(e, t, n) {
					const r = nh(),
						i = t.length + 1,
						s = new $(t.child("")),
						a = this.overlays.getIteratorFrom(s);
					while (a.hasNext()) {
						const e = a.getNext().value,
							s = e.getKey();
						if (!t.isPrefixOf(s.path)) break;
						s.path.length === i && e.largestBatchId > n && r.set(e.getKey(), e);
					}
					return ei.resolve(r);
				}
				getOverlaysForCollectionGroup(e, t, n, r) {
					let i = new eJ((e, t) => e - t),
						s = this.overlays.getIterator();
					while (s.hasNext()) {
						const e = s.getNext().value;
						if (e.getKey().getCollectionGroup() === t && e.largestBatchId > n) {
							let t = i.get(e.largestBatchId);
							null === t && ((t = nh()), (i = i.insert(e.largestBatchId, t))),
								t.set(e.getKey(), e);
						}
					}
					const a = nh(),
						o = i.getIterator();
					while (
						o.hasNext() &&
						(o.getNext().value.forEach((e, t) => a.set(e, t)), !(a.size() >= r))
					);
					return ei.resolve(a);
				}
				ht(e, t, n) {
					const r = this.overlays.get(n.key);
					if (null !== r) {
						const e = this.Ir.get(r.largestBatchId).delete(n.key);
						this.Ir.set(r.largestBatchId, e);
					}
					this.overlays = this.overlays.insert(n.key, new n$(t, n));
					let i = this.Ir.get(t);
					void 0 === i && ((i = nf()), this.Ir.set(t, i)),
						this.Ir.set(t, i.add(n.key));
				}
			}
			class iq {
				constructor() {
					this.sessionToken = e5.EMPTY_BYTE_STRING;
				}
				getSessionToken(e) {
					return ei.resolve(this.sessionToken);
				}
				setSessionToken(e, t) {
					return (this.sessionToken = t), ei.resolve();
				}
			}
			class iB {
				constructor() {
					(this.Tr = new eX(iK.Er)), (this.dr = new eX(iK.Ar));
				}
				isEmpty() {
					return this.Tr.isEmpty();
				}
				addReference(e, t) {
					const n = new iK(e, t);
					(this.Tr = this.Tr.add(n)), (this.dr = this.dr.add(n));
				}
				Rr(e, t) {
					e.forEach((e) => this.addReference(e, t));
				}
				removeReference(e, t) {
					this.Vr(new iK(e, t));
				}
				mr(e, t) {
					e.forEach((e) => this.removeReference(e, t));
				}
				gr(e) {
					const t = new $(new K([])),
						n = new iK(t, e),
						r = new iK(t, e + 1),
						i = [];
					return (
						this.dr.forEachInRange([n, r], (e) => {
							this.Vr(e), i.push(e.key);
						}),
						i
					);
				}
				pr() {
					this.Tr.forEach((e) => this.Vr(e));
				}
				Vr(e) {
					(this.Tr = this.Tr.delete(e)), (this.dr = this.dr.delete(e));
				}
				yr(e) {
					let t = new $(new K([])),
						n = new iK(t, e),
						r = new iK(t, e + 1),
						i = nf();
					return (
						this.dr.forEachInRange([n, r], (e) => {
							i = i.add(e.key);
						}),
						i
					);
				}
				containsKey(e) {
					const t = new iK(e, 0),
						n = this.Tr.firstAfterOrEqual(t);
					return null !== n && e.isEqual(n.key);
				}
			}
			class iK {
				constructor(e, t) {
					(this.key = e), (this.wr = t);
				}
				static Er(e, t) {
					return $.comparator(e.key, t.key) || L(e.wr, t.wr);
				}
				static Ar(e, t) {
					return L(e.wr, t.wr) || $.comparator(e.key, t.key);
				}
			}
			class iz {
				constructor(e, t) {
					(this.indexManager = e),
						(this.referenceDelegate = t),
						(this.mutationQueue = []),
						(this.Sr = 1),
						(this.br = new eX(iK.Er));
				}
				checkEmpty(e) {
					return ei.resolve(0 === this.mutationQueue.length);
				}
				addMutationBatch(e, t, n, r) {
					const i = this.Sr;
					this.Sr++,
						this.mutationQueue.length > 0 &&
							this.mutationQueue[this.mutationQueue.length - 1];
					const s = new nz(i, t, n, r);
					for (const t of (this.mutationQueue.push(s), r))
						(this.br = this.br.add(new iK(t.key, i))),
							this.indexManager.addToCollectionParentIndex(
								e,
								t.key.path.popLast(),
							);
					return ei.resolve(s);
				}
				lookupMutationBatch(e, t) {
					return ei.resolve(this.Dr(t));
				}
				getNextMutationBatchAfterBatchId(e, t) {
					const n = this.vr(t + 1),
						r = n < 0 ? 0 : n;
					return ei.resolve(
						this.mutationQueue.length > r ? this.mutationQueue[r] : null,
					);
				}
				getHighestUnacknowledgedBatchId() {
					return ei.resolve(0 === this.mutationQueue.length ? -1 : this.Sr - 1);
				}
				getAllMutationBatches(e) {
					return ei.resolve(this.mutationQueue.slice());
				}
				getAllMutationBatchesAffectingDocumentKey(e, t) {
					const n = new iK(t, 0),
						r = new iK(t, Number.POSITIVE_INFINITY),
						i = [];
					return (
						this.br.forEachInRange([n, r], (e) => {
							const t = this.Dr(e.wr);
							i.push(t);
						}),
						ei.resolve(i)
					);
				}
				getAllMutationBatchesAffectingDocumentKeys(e, t) {
					let n = new eX(L);
					return (
						t.forEach((e) => {
							const t = new iK(e, 0),
								r = new iK(e, Number.POSITIVE_INFINITY);
							this.br.forEachInRange([t, r], (e) => {
								n = n.add(e.wr);
							});
						}),
						ei.resolve(this.Cr(n))
					);
				}
				getAllMutationBatchesAffectingQuery(e, t) {
					let n = t.path,
						r = n.length + 1,
						i = n;
					$.isDocumentKey(i) || (i = i.child(""));
					let s = new iK(new $(i), 0),
						a = new eX(L);
					return (
						this.br.forEachWhile((e) => {
							const t = e.key.path;
							return (
								!!n.isPrefixOf(t) && (t.length === r && (a = a.add(e.wr)), !0)
							);
						}, s),
						ei.resolve(this.Cr(a))
					);
				}
				Cr(e) {
					const t = [];
					return (
						e.forEach((e) => {
							const n = this.Dr(e);
							null !== n && t.push(n);
						}),
						t
					);
				}
				removeMutationBatch(e, t) {
					0 === this.Fr(t.batchId, "removed") || _(),
						this.mutationQueue.shift();
					let n = this.br;
					return ei
						.forEach(t.mutations, (r) => {
							const i = new iK(r.key, t.batchId);
							return (
								(n = n.delete(i)),
								this.referenceDelegate.markPotentiallyOrphaned(e, r.key)
							);
						})
						.next(() => {
							this.br = n;
						});
				}
				On(e) {}
				containsKey(e, t) {
					const n = new iK(t, 0),
						r = this.br.firstAfterOrEqual(n);
					return ei.resolve(t.isEqual(r && r.key));
				}
				performConsistencyCheck(e) {
					return this.mutationQueue.length, ei.resolve();
				}
				Fr(e, t) {
					return this.vr(e);
				}
				vr(e) {
					return 0 === this.mutationQueue.length
						? 0
						: e - this.mutationQueue[0].batchId;
				}
				Dr(e) {
					const t = this.vr(e);
					return t < 0 || t >= this.mutationQueue.length
						? null
						: this.mutationQueue[t];
				}
			}
			class iG {
				constructor(e) {
					(this.Mr = e), (this.docs = new eJ($.comparator)), (this.size = 0);
				}
				setIndexManager(e) {
					this.indexManager = e;
				}
				addEntry(e, t) {
					const n = t.key,
						r = this.docs.get(n),
						i = r ? r.size : 0,
						s = this.Mr(t);
					return (
						(this.docs = this.docs.insert(n, {
							document: t.mutableCopy(),
							size: s,
						})),
						(this.size += s - i),
						this.indexManager.addToCollectionParentIndex(e, n.path.popLast())
					);
				}
				removeEntry(e) {
					const t = this.docs.get(e);
					t && ((this.docs = this.docs.remove(e)), (this.size -= t.size));
				}
				getEntry(e, t) {
					const n = this.docs.get(t);
					return ei.resolve(
						n ? n.document.mutableCopy() : tS.newInvalidDocument(t),
					);
				}
				getEntries(e, t) {
					let n = na;
					return (
						t.forEach((e) => {
							const t = this.docs.get(e);
							n = n.insert(
								e,
								t ? t.document.mutableCopy() : tS.newInvalidDocument(e),
							);
						}),
						ei.resolve(n)
					);
				}
				getDocumentsMatchingQuery(e, t, n, r) {
					let i = na,
						s = t.path,
						a = new $(s.child("")),
						o = this.docs.getIteratorFrom(a);
					while (o.hasNext()) {
						const {
							key: e,
							value: { document: a },
						} = o.getNext();
						if (!s.isPrefixOf(e.path)) break;
						e.path.length > s.length + 1 ||
							0 >= ee(X(a), n) ||
							((r.has(a.key) || nn(t, a)) &&
								(i = i.insert(a.key, a.mutableCopy())));
					}
					return ei.resolve(i);
				}
				getAllFromCollectionGroup(e, t, n, r) {
					_();
				}
				Or(e, t) {
					return ei.forEach(this.docs, (e) => t(e));
				}
				newChangeBuffer(e) {
					return new i$(this);
				}
				getSize(e) {
					return ei.resolve(this.size);
				}
			}
			class i$ extends iC {
				constructor(e) {
					super(), (this.cr = e);
				}
				applyChanges(e) {
					const t = [];
					return (
						this.changes.forEach((n, r) => {
							r.isValidDocument()
								? t.push(this.cr.addEntry(e, r))
								: this.cr.removeEntry(n);
						}),
						ei.waitFor(t)
					);
				}
				getFromCache(e, t) {
					return this.cr.getEntry(e, t);
				}
				getAllFromCache(e, t) {
					return this.cr.getEntries(e, t);
				}
			}
			class iQ {
				constructor(e) {
					(this.persistence = e),
						(this.Nr = new ns((e) => tW(e), tJ)),
						(this.lastRemoteSnapshotVersion = q.min()),
						(this.highestTargetId = 0),
						(this.Lr = 0),
						(this.Br = new iB()),
						(this.targetCount = 0),
						(this.kr = iy.Bn());
				}
				forEachTarget(e, t) {
					return this.Nr.forEach((e, n) => t(n)), ei.resolve();
				}
				getLastRemoteSnapshotVersion(e) {
					return ei.resolve(this.lastRemoteSnapshotVersion);
				}
				getHighestSequenceNumber(e) {
					return ei.resolve(this.Lr);
				}
				allocateTargetId(e) {
					return (
						(this.highestTargetId = this.kr.next()),
						ei.resolve(this.highestTargetId)
					);
				}
				setTargetsMetadata(e, t, n) {
					return (
						n && (this.lastRemoteSnapshotVersion = n),
						t > this.Lr && (this.Lr = t),
						ei.resolve()
					);
				}
				Kn(e) {
					this.Nr.set(e.target, e);
					const t = e.targetId;
					t > this.highestTargetId &&
						((this.kr = new iy(t)), (this.highestTargetId = t)),
						e.sequenceNumber > this.Lr && (this.Lr = e.sequenceNumber);
				}
				addTargetData(e, t) {
					return this.Kn(t), (this.targetCount += 1), ei.resolve();
				}
				updateTargetData(e, t) {
					return this.Kn(t), ei.resolve();
				}
				removeTargetData(e, t) {
					return (
						this.Nr.delete(t.target),
						this.Br.gr(t.targetId),
						(this.targetCount -= 1),
						ei.resolve()
					);
				}
				removeTargets(e, t, n) {
					let r = 0,
						i = [];
					return (
						this.Nr.forEach((s, a) => {
							a.sequenceNumber <= t &&
								null === n.get(a.targetId) &&
								(this.Nr.delete(s),
								i.push(this.removeMatchingKeysForTargetId(e, a.targetId)),
								r++);
						}),
						ei.waitFor(i).next(() => r)
					);
				}
				getTargetCount(e) {
					return ei.resolve(this.targetCount);
				}
				getTargetData(e, t) {
					const n = this.Nr.get(t) || null;
					return ei.resolve(n);
				}
				addMatchingKeys(e, t, n) {
					return this.Br.Rr(t, n), ei.resolve();
				}
				removeMatchingKeys(e, t, n) {
					this.Br.mr(t, n);
					const r = this.persistence.referenceDelegate,
						i = [];
					return (
						r &&
							t.forEach((t) => {
								i.push(r.markPotentiallyOrphaned(e, t));
							}),
						ei.waitFor(i)
					);
				}
				removeMatchingKeysForTargetId(e, t) {
					return this.Br.gr(t), ei.resolve();
				}
				getMatchingKeysForTargetId(e, t) {
					const n = this.Br.yr(t);
					return ei.resolve(n);
				}
				containsKey(e, t) {
					return ei.resolve(this.Br.containsKey(t));
				}
			}
			class ij {
				constructor(e, t) {
					(this.qr = {}),
						(this.overlays = {}),
						(this.Qr = new ey(0)),
						(this.Kr = !1),
						(this.Kr = !0),
						(this.$r = new iq()),
						(this.referenceDelegate = e(this)),
						(this.Ur = new iQ(this)),
						(this.indexManager = new r6()),
						(this.remoteDocumentCache = new iG((e) =>
							this.referenceDelegate.Wr(e),
						)),
						(this.serializer = new rD(t)),
						(this.Gr = new iP(this.serializer));
				}
				start() {
					return Promise.resolve();
				}
				shutdown() {
					return (this.Kr = !1), Promise.resolve();
				}
				get started() {
					return this.Kr;
				}
				setDatabaseDeletedListener() {}
				setNetworkEnabled() {}
				getIndexManager(e) {
					return this.indexManager;
				}
				getDocumentOverlayCache(e) {
					let t = this.overlays[e.toKey()];
					return t || ((t = new iU()), (this.overlays[e.toKey()] = t)), t;
				}
				getMutationQueue(e, t) {
					let n = this.qr[e.toKey()];
					return (
						n ||
							((n = new iz(t, this.referenceDelegate)),
							(this.qr[e.toKey()] = n)),
						n
					);
				}
				getGlobalsCache() {
					return this.$r;
				}
				getTargetCache() {
					return this.Ur;
				}
				getRemoteDocumentCache() {
					return this.remoteDocumentCache;
				}
				getBundleCache() {
					return this.Gr;
				}
				runTransaction(e, t, n) {
					I("MemoryPersistence", "Starting transaction:", e);
					const r = new iW(this.Qr.next());
					return (
						this.referenceDelegate.zr(),
						n(r)
							.next((e) => this.referenceDelegate.jr(r).next(() => e))
							.toPromise()
							.then((e) => (r.raiseOnCommittedEvent(), e))
					);
				}
				Hr(e, t) {
					return ei.or(
						Object.values(this.qr).map((n) => () => n.containsKey(e, t)),
					);
				}
			}
			class iW extends en {
				constructor(e) {
					super(), (this.currentSequenceNumber = e);
				}
			}
			class iJ {
				constructor(e) {
					(this.persistence = e), (this.Jr = new iB()), (this.Yr = null);
				}
				static Zr(e) {
					return new iJ(e);
				}
				get Xr() {
					if (this.Yr) return this.Yr;
					throw _();
				}
				addReference(e, t, n) {
					return (
						this.Jr.addReference(n, t),
						this.Xr.delete(n.toString()),
						ei.resolve()
					);
				}
				removeReference(e, t, n) {
					return (
						this.Jr.removeReference(n, t),
						this.Xr.add(n.toString()),
						ei.resolve()
					);
				}
				markPotentiallyOrphaned(e, t) {
					return this.Xr.add(t.toString()), ei.resolve();
				}
				removeTarget(e, t) {
					this.Jr.gr(t.targetId).forEach((e) => this.Xr.add(e.toString()));
					const n = this.persistence.getTargetCache();
					return n
						.getMatchingKeysForTargetId(e, t.targetId)
						.next((e) => {
							e.forEach((e) => this.Xr.add(e.toString()));
						})
						.next(() => n.removeTargetData(e, t));
				}
				zr() {
					this.Yr = new Set();
				}
				jr(e) {
					const t = this.persistence.getRemoteDocumentCache().newChangeBuffer();
					return ei
						.forEach(this.Xr, (n) => {
							const r = $.fromPath(n);
							return this.ei(e, r).next((e) => {
								e || t.removeEntry(r, q.min());
							});
						})
						.next(() => ((this.Yr = null), t.apply(e)));
				}
				updateLimboDocument(e, t) {
					return this.ei(e, t).next((e) => {
						e ? this.Xr.delete(t.toString()) : this.Xr.add(t.toString());
					});
				}
				Wr(e) {
					return 0;
				}
				ei(e, t) {
					return ei.or([
						() => ei.resolve(this.Jr.containsKey(t)),
						() => this.persistence.getTargetCache().containsKey(e, t),
						() => this.persistence.Hr(e, t),
					]);
				}
			}
			class iH {
				constructor(e, t) {
					(this.persistence = e),
						(this.ti = new ns(
							(e) => eT(e.path),
							(e, t) => e.isEqual(t),
						)),
						(this.garbageCollector = new iS(this, t));
				}
				static Zr(e, t) {
					return new iH(e, t);
				}
				zr() {}
				jr(e) {
					return ei.resolve();
				}
				forEachTarget(e, t) {
					return this.persistence.getTargetCache().forEachTarget(e, t);
				}
				Yn(e) {
					const t = this.er(e);
					return this.persistence
						.getTargetCache()
						.getTargetCount(e)
						.next((e) => t.next((t) => e + t));
				}
				er(e) {
					let t = 0;
					return this.Zn(e, (e) => {
						t++;
					}).next(() => t);
				}
				Zn(e, t) {
					return ei.forEach(this.ti, (n, r) =>
						this.nr(e, n, r).next((e) => (e ? ei.resolve() : t(r))),
					);
				}
				removeTargets(e, t, n) {
					return this.persistence.getTargetCache().removeTargets(e, t, n);
				}
				removeOrphanedDocuments(e, t) {
					let n = 0,
						r = this.persistence.getRemoteDocumentCache(),
						i = r.newChangeBuffer();
					return r
						.Or(e, (r) =>
							this.nr(e, r, t).next((e) => {
								e || (n++, i.removeEntry(r, q.min()));
							}),
						)
						.next(() => i.apply(e))
						.next(() => n);
				}
				markPotentiallyOrphaned(e, t) {
					return this.ti.set(t, e.currentSequenceNumber), ei.resolve();
				}
				removeTarget(e, t) {
					const n = t.withSequenceNumber(e.currentSequenceNumber);
					return this.persistence.getTargetCache().updateTargetData(e, n);
				}
				addReference(e, t, n) {
					return this.ti.set(n, e.currentSequenceNumber), ei.resolve();
				}
				removeReference(e, t, n) {
					return this.ti.set(n, e.currentSequenceNumber), ei.resolve();
				}
				updateLimboDocument(e, t) {
					return this.ti.set(t, e.currentSequenceNumber), ei.resolve();
				}
				Wr(e) {
					let t = e.key.toString().length;
					return (
						e.isFoundDocument() &&
							(t += (function e(t) {
								switch (ts(t)) {
									case 0:
									case 1:
										return 4;
									case 2:
										return 8;
									case 3:
									case 8:
										return 16;
									case 4:
										const n = e7(t);
										return n ? 16 + e(n) : 16;
									case 5:
										return 2 * t.stringValue.length;
									case 6:
										return e6(t.bytesValue).approximateByteSize();
									case 7:
										return t.referenceValue.length;
									case 9:
										return (t.arrayValue.values || []).reduce(
											(t, n) => t + e(n),
											0,
										);
									case 10:
									case 11:
										var r;
										let i;
										return (
											(r = t.mapValue),
											(i = 0),
											ej(r.fields, (t, n) => {
												i += t.length + e(n);
											}),
											i
										);
									default:
										throw _();
								}
							})(e.data.value)),
						t
					);
				}
				nr(e, t, n) {
					return ei.or([
						() => this.persistence.Hr(e, t),
						() => this.persistence.getTargetCache().containsKey(e, t),
						() => {
							const e = this.ti.get(t);
							return ei.resolve(void 0 !== e && e > n);
						},
					]);
				}
				getCacheSize(e) {
					return this.persistence.getRemoteDocumentCache().getSize(e);
				}
			}
			class iY {
				constructor(e) {
					this.serializer = e;
				}
				O(e, t, n, r) {
					const i = new es("createOrUpgrade", t);
					n < 1 &&
						r >= 1 &&
						(!((e) => {
							e.createObjectStore("owner");
						})(e),
						e.createObjectStore("mutationQueues", { keyPath: "userId" }),
						e
							.createObjectStore("mutations", {
								keyPath: "batchId",
								autoIncrement: !0,
							})
							.createIndex("userMutationsIndex", eb, { unique: !0 }),
						e.createObjectStore("documentMutations"),
						iX(e),
						((e) => {
							e.createObjectStore("remoteDocuments");
						})(e));
					let s = ei.resolve();
					return (
						n < 3 &&
							r >= 3 &&
							(0 !== n &&
								(e.deleteObjectStore("targetDocuments"),
								e.deleteObjectStore("targets"),
								e.deleteObjectStore("targetGlobal"),
								iX(e)),
							(s = s.next(() =>
								((e) => {
									const t = e.store("targetGlobal"),
										n = {
											highestTargetId: 0,
											highestListenSequenceNumber: 0,
											lastRemoteSnapshotVersion: q.min().toTimestamp(),
											targetCount: 0,
										};
									return t.put("targetGlobalKey", n);
								})(i),
							))),
						n < 4 &&
							r >= 4 &&
							(0 !== n &&
								(s = s.next(() =>
									i
										.store("mutations")
										.U()
										.next((t) => {
											e.deleteObjectStore("mutations"),
												e
													.createObjectStore("mutations", {
														keyPath: "batchId",
														autoIncrement: !0,
													})
													.createIndex("userMutationsIndex", eb, {
														unique: !0,
													});
											const n = i.store("mutations"),
												r = t.map((e) => n.put(e));
											return ei.waitFor(r);
										}),
								)),
							(s = s.next(() => {
								!((e) => {
									e.createObjectStore("clientMetadata", {
										keyPath: "clientId",
									});
								})(e);
							}))),
						n < 5 && r >= 5 && (s = s.next(() => this.ni(i))),
						n < 6 &&
							r >= 6 &&
							(s = s.next(
								() => (
									((e) => {
										e.createObjectStore("remoteDocumentGlobal");
									})(e),
									this.ri(i)
								),
							)),
						n < 7 && r >= 7 && (s = s.next(() => this.ii(i))),
						n < 8 && r >= 8 && (s = s.next(() => this.si(e, i))),
						n < 9 &&
							r >= 9 &&
							(s = s.next(() => {
								e.objectStoreNames.contains("remoteDocumentChanges") &&
									e.deleteObjectStore("remoteDocumentChanges");
							})),
						n < 10 && r >= 10 && (s = s.next(() => this.oi(i))),
						n < 11 &&
							r >= 11 &&
							(s = s.next(() => {
								((e) => {
									e.createObjectStore("bundles", { keyPath: "bundleId" });
								})(e),
									((e) => {
										e.createObjectStore("namedQueries", { keyPath: "name" });
									})(e);
							})),
						n < 12 &&
							r >= 12 &&
							(s = s.next(() => {
								!((e) => {
									const t = e.createObjectStore("documentOverlays", {
										keyPath: eF,
									});
									t.createIndex("collectionPathOverlayIndex", eL, {
										unique: !1,
									}),
										t.createIndex("collectionGroupOverlayIndex", eP, {
											unique: !1,
										});
								})(e);
							})),
						n < 13 &&
							r >= 13 &&
							(s = s
								.next(() =>
									((e) => {
										const t = e.createObjectStore("remoteDocumentsV14", {
											keyPath: eS,
										});
										t.createIndex("documentKeyIndex", ex),
											t.createIndex("collectionGroupIndex", eD);
									})(e),
								)
								.next(() => this._i(e, i))
								.next(() => e.deleteObjectStore("remoteDocuments"))),
						n < 14 && r >= 14 && (s = s.next(() => this.ai(e, i))),
						n < 15 &&
							r >= 15 &&
							(s = s.next(() => {
								e
									.createObjectStore("indexConfiguration", {
										keyPath: "indexId",
										autoIncrement: !0,
									})
									.createIndex("collectionGroupIndex", "collectionGroup", {
										unique: !1,
									}),
									e
										.createObjectStore("indexState", { keyPath: eV })
										.createIndex("sequenceNumberIndex", eR, { unique: !1 }),
									e
										.createObjectStore("indexEntries", { keyPath: eO })
										.createIndex("documentKeyIndex", eM, { unique: !1 });
							})),
						n < 16 &&
							r >= 16 &&
							(s = s
								.next(() => {
									t.objectStore("indexState").clear();
								})
								.next(() => {
									t.objectStore("indexEntries").clear();
								})),
						n < 17 &&
							r >= 17 &&
							(s = s.next(() => {
								!((e) => {
									e.createObjectStore("globals", { keyPath: "name" });
								})(e);
							})),
						s
					);
				}
				ri(e) {
					let t = 0;
					return e
						.store("remoteDocuments")
						.J((e, n) => {
							t += ih(n);
						})
						.next(() => {
							const n = { byteSize: t };
							return e
								.store("remoteDocumentGlobal")
								.put("remoteDocumentGlobalKey", n);
						});
				}
				ni(e) {
					const t = e.store("mutationQueues"),
						n = e.store("mutations");
					return t.U().next((t) =>
						ei.forEach(t, (t) => {
							const r = IDBKeyRange.bound(
								[t.userId, -1],
								[t.userId, t.lastAcknowledgedBatchId],
							);
							return n.U("userMutationsIndex", r).next((n) =>
								ei.forEach(n, (n) => {
									n.userId === t.userId || _();
									const r = rV(this.serializer, n);
									return iu(e, t.userId, r).next(() => {});
								}),
							);
						}),
					);
				}
				ii(e) {
					const t = e.store("targetDocuments"),
						n = e.store("remoteDocuments");
					return e
						.store("targetGlobal")
						.get("targetGlobalKey")
						.next((e) => {
							const r = [];
							return n
								.J((n, i) => {
									const s = new K(n),
										a = [0, eT(s)];
									r.push(
										t
											.get(a)
											.next((n) =>
												n
													? ei.resolve()
													: t.put({
															targetId: 0,
															path: eT(s),
															sequenceNumber: e.highestListenSequenceNumber,
														}),
											),
									);
								})
								.next(() => ei.waitFor(r));
						});
				}
				si(e, t) {
					e.createObjectStore("collectionParents", { keyPath: eA });
					const n = t.store("collectionParents"),
						r = new r9(),
						i = (e) => {
							if (r.add(e)) {
								const t = e.lastSegment(),
									r = e.popLast();
								return n.put({ collectionId: t, parent: eT(r) });
							}
						};
					return t
						.store("remoteDocuments")
						.J({ H: !0 }, (e, t) => i(new K(e).popLast()))
						.next(() =>
							t
								.store("documentMutations")
								.J({ H: !0 }, ([e, t, n], r) => i(eE(t).popLast())),
						);
				}
				oi(e) {
					const t = e.store("targets");
					return t.J((e, n) => {
						const r = rR(n),
							i = rO(this.serializer, r);
						return t.put(i);
					});
				}
				_i(e, t) {
					const n = t.store("remoteDocuments"),
						r = [];
					return n
						.J((e, n) => {
							const i = t.store("remoteDocumentsV14"),
								s = (
									n.document
										? new $(K.fromString(n.document.name).popFirst(5))
										: n.noDocument
											? $.fromSegments(n.noDocument.path)
											: n.unknownDocument
												? $.fromSegments(n.unknownDocument.path)
												: _()
								).path.toArray(),
								a = {
									prefixPath: s.slice(0, s.length - 2),
									collectionGroup: s[s.length - 2],
									documentId: s[s.length - 1],
									readTime: n.readTime || [0, 0],
									unknownDocument: n.unknownDocument,
									noDocument: n.noDocument,
									document: n.document,
									hasCommittedMutations: !!n.hasCommittedMutations,
								};
							r.push(i.put(a));
						})
						.next(() => ei.waitFor(r));
				}
				ai(e, t) {
					const n = t.store("mutations"),
						r = new iN(this.serializer),
						i = new ij(iJ.Zr, this.serializer.ct);
					return n.U().next((e) => {
						const n = new Map();
						return (
							e.forEach((e) => {
								var t;
								let r =
									null !== (t = n.get(e.userId)) && void 0 !== t ? t : nf();
								rV(this.serializer, e)
									.keys()
									.forEach((e) => (r = r.add(e))),
									n.set(e.userId, r);
							}),
							ei.forEach(n, (e, n) => {
								const s = new p(n),
									a = rK.lt(this.serializer, s),
									o = i.getIndexManager(s);
								return new iL(
									r,
									ic.lt(s, this.serializer, o, i.referenceDelegate),
									a,
									o,
								)
									.recalculateAndSaveOverlaysForDocumentKeys(
										new eG(t, ey.oe),
										e,
									)
									.next();
							})
						);
					});
				}
			}
			function iX(e) {
				e
					.createObjectStore("targetDocuments", { keyPath: eN })
					.createIndex("documentTargetsIndex", ek, { unique: !0 }),
					e
						.createObjectStore("targets", { keyPath: "targetId" })
						.createIndex("queryTargetsIndex", eC, { unique: !0 }),
					e.createObjectStore("targetGlobal");
			}
			const iZ =
				"Failed to obtain exclusive access to the persistence layer. To allow shared access, multi-tab synchronization has to be enabled in all tabs. If you are using `experimentalForceOwningTab:true`, make sure that only one tab has persistence enabled at any given time.";
			class i0 {
				constructor(e, t, n, r, i, s, a, o, l, u, h = 17) {
					if (
						((this.allowTabSynchronization = e),
						(this.persistenceKey = t),
						(this.clientId = n),
						(this.ui = i),
						(this.window = s),
						(this.document = a),
						(this.ci = l),
						(this.li = u),
						(this.hi = h),
						(this.Qr = null),
						(this.Kr = !1),
						(this.isPrimary = !1),
						(this.networkEnabled = !0),
						(this.Pi = null),
						(this.inForeground = !1),
						(this.Ii = null),
						(this.Ti = null),
						(this.Ei = Number.NEGATIVE_INFINITY),
						(this.di = (e) => Promise.resolve()),
						!i0.D())
					)
						throw new x(
							S.UNIMPLEMENTED,
							"This platform is either missing IndexedDB or is known to have an incomplete implementation. Offline persistence has been disabled.",
						);
					(this.referenceDelegate = new ix(this, r)),
						(this.Ai = t + "main"),
						(this.serializer = new rD(o)),
						(this.Ri = new ea(this.Ai, this.hi, new iY(this.serializer))),
						(this.$r = new rG()),
						(this.Ur = new iw(this.referenceDelegate, this.serializer)),
						(this.remoteDocumentCache = new iN(this.serializer)),
						(this.Gr = new rU()),
						this.window && this.window.localStorage
							? (this.Vi = this.window.localStorage)
							: ((this.Vi = null),
								!1 === u &&
									T(
										"IndexedDbPersistence",
										"LocalStorage is unavailable. As a result, persistence may not work reliably. In particular enablePersistence() could fail immediately after refreshing the page.",
									));
				}
				start() {
					return this.mi()
						.then(() => {
							if (!this.isPrimary && !this.allowTabSynchronization)
								throw new x(S.FAILED_PRECONDITION, iZ);
							return (
								this.fi(),
								this.gi(),
								this.pi(),
								this.runTransaction(
									"getHighestListenSequenceNumber",
									"readonly",
									(e) => this.Ur.getHighestSequenceNumber(e),
								)
							);
						})
						.then((e) => {
							this.Qr = new ey(e, this.ci);
						})
						.then(() => {
							this.Kr = !0;
						})
						.catch((e) => (this.Ri && this.Ri.close(), Promise.reject(e)));
				}
				yi(e) {
					return (
						(this.di = async (t) => {
							if (this.started) return e(t);
						}),
						e(this.isPrimary)
					);
				}
				setDatabaseDeletedListener(e) {
					this.Ri.L(async (t) => {
						null === t.newVersion && (await e());
					});
				}
				setNetworkEnabled(e) {
					this.networkEnabled !== e &&
						((this.networkEnabled = e),
						this.ui.enqueueAndForget(async () => {
							this.started && (await this.mi());
						}));
				}
				mi() {
					return this.runTransaction(
						"updateClientMetadataAndTryBecomePrimary",
						"readwrite",
						(e) =>
							i2(e)
								.put({
									clientId: this.clientId,
									updateTimeMs: Date.now(),
									networkEnabled: this.networkEnabled,
									inForeground: this.inForeground,
								})
								.next(() => {
									if (this.isPrimary)
										return this.wi(e).next((e) => {
											e ||
												((this.isPrimary = !1),
												this.ui.enqueueRetryable(() => this.di(!1)));
										});
								})
								.next(() => this.Si(e))
								.next((t) =>
									this.isPrimary && !t
										? this.bi(e).next(() => !1)
										: !!t && this.Di(e).next(() => !0),
								),
					)
						.catch((e) => {
							if (eh(e))
								return (
									I(
										"IndexedDbPersistence",
										"Failed to extend owner lease: ",
										e,
									),
									this.isPrimary
								);
							if (!this.allowTabSynchronization) throw e;
							return (
								I(
									"IndexedDbPersistence",
									"Releasing owner lease after error during lease refresh",
									e,
								),
								!1
							);
						})
						.then((e) => {
							this.isPrimary !== e &&
								this.ui.enqueueRetryable(() => this.di(e)),
								(this.isPrimary = e);
						});
				}
				wi(e) {
					return i1(e)
						.get("owner")
						.next((e) => ei.resolve(this.vi(e)));
				}
				Ci(e) {
					return i2(e).delete(this.clientId);
				}
				async Fi() {
					if (this.isPrimary && !this.Mi(this.Ei, 18e5)) {
						this.Ei = Date.now();
						const e = await this.runTransaction(
							"maybeGarbageCollectMultiClientState",
							"readwrite-primary",
							(e) => {
								const t = e$(e, "clientMetadata");
								return t.U().next((e) => {
									const n = this.xi(e, 18e5),
										r = e.filter((e) => -1 === n.indexOf(e));
									return ei
										.forEach(r, (e) => t.delete(e.clientId))
										.next(() => r);
								});
							},
						).catch(() => []);
						if (this.Vi)
							for (const t of e) this.Vi.removeItem(this.Oi(t.clientId));
					}
				}
				pi() {
					this.Ti = this.ui.enqueueAfterDelay(
						"client_metadata_refresh",
						4e3,
						() =>
							this.mi()
								.then(() => this.Fi())
								.then(() => this.pi()),
					);
				}
				vi(e) {
					return !!e && e.ownerId === this.clientId;
				}
				Si(e) {
					return this.li
						? ei.resolve(!0)
						: i1(e)
								.get("owner")
								.next((t) => {
									if (
										null !== t &&
										this.Mi(t.leaseTimestampMs, 5e3) &&
										!this.Ni(t.ownerId)
									) {
										if (this.vi(t) && this.networkEnabled) return !0;
										if (!this.vi(t)) {
											if (!t.allowTabSynchronization)
												throw new x(S.FAILED_PRECONDITION, iZ);
											return !1;
										}
									}
									return (
										!(!this.networkEnabled || !this.inForeground) ||
										i2(e)
											.U()
											.next(
												(e) =>
													void 0 ===
													this.xi(e, 5e3).find((e) => {
														if (this.clientId !== e.clientId) {
															const t =
																	!this.networkEnabled && e.networkEnabled,
																n = !this.inForeground && e.inForeground,
																r = this.networkEnabled === e.networkEnabled;
															if (t || (n && r)) return !0;
														}
														return !1;
													}),
											)
									);
								})
								.next(
									(e) => (
										this.isPrimary !== e &&
											I(
												"IndexedDbPersistence",
												`Client ${e ? "is" : "is not"} eligible for a primary lease.`,
											),
										e
									),
								);
				}
				async shutdown() {
					(this.Kr = !1),
						this.Li(),
						this.Ti && (this.Ti.cancel(), (this.Ti = null)),
						this.Bi(),
						this.ki(),
						await this.Ri.runTransaction(
							"shutdown",
							"readwrite",
							["owner", "clientMetadata"],
							(e) => {
								const t = new eG(e, ey.oe);
								return this.bi(t).next(() => this.Ci(t));
							},
						),
						this.Ri.close(),
						this.qi();
				}
				xi(e, t) {
					return e.filter(
						(e) => this.Mi(e.updateTimeMs, t) && !this.Ni(e.clientId),
					);
				}
				Qi() {
					return this.runTransaction("getActiveClients", "readonly", (e) =>
						i2(e)
							.U()
							.next((e) => this.xi(e, 18e5).map((e) => e.clientId)),
					);
				}
				get started() {
					return this.Kr;
				}
				getGlobalsCache() {
					return this.$r;
				}
				getMutationQueue(e, t) {
					return ic.lt(e, this.serializer, t, this.referenceDelegate);
				}
				getTargetCache() {
					return this.Ur;
				}
				getRemoteDocumentCache() {
					return this.remoteDocumentCache;
				}
				getIndexManager(e) {
					return new ie(e, this.serializer.ct.databaseId);
				}
				getDocumentOverlayCache(e) {
					return rK.lt(this.serializer, e);
				}
				getBundleCache() {
					return this.Gr;
				}
				runTransaction(e, t, n) {
					var r;
					let i;
					I("IndexedDbPersistence", "Starting transaction:", e);
					const s =
						17 === (r = this.hi)
							? ez
							: 16 === r
								? eK
								: 15 === r
									? eK
									: 14 === r
										? eB
										: 13 === r
											? eB
											: 12 === r
												? eq
												: 11 === r
													? eU
													: void _();
					return this.Ri.runTransaction(
						e,
						"readonly" === t ? "readonly" : "readwrite",
						s,
						(r) => (
							(i = new eG(r, this.Qr ? this.Qr.next() : ey.oe)),
							"readwrite-primary" === t
								? this.wi(i)
										.next((e) => !!e || this.Si(i))
										.next((t) => {
											if (!t)
												throw (
													(T(
														`Failed to obtain primary lease for action '${e}'.`,
													),
													(this.isPrimary = !1),
													this.ui.enqueueRetryable(() => this.di(!1)),
													new x(S.FAILED_PRECONDITION, et))
												);
											return n(i);
										})
										.next((e) => this.Di(i).next(() => e))
								: this.Ki(i).next(() => n(i))
						),
					).then((e) => (i.raiseOnCommittedEvent(), e));
				}
				Ki(e) {
					return i1(e)
						.get("owner")
						.next((e) => {
							if (
								null !== e &&
								this.Mi(e.leaseTimestampMs, 5e3) &&
								!this.Ni(e.ownerId) &&
								!this.vi(e) &&
								!(
									this.li ||
									(this.allowTabSynchronization && e.allowTabSynchronization)
								)
							)
								throw new x(S.FAILED_PRECONDITION, iZ);
						});
				}
				Di(e) {
					const t = {
						ownerId: this.clientId,
						allowTabSynchronization: this.allowTabSynchronization,
						leaseTimestampMs: Date.now(),
					};
					return i1(e).put("owner", t);
				}
				static D() {
					return ea.D();
				}
				bi(e) {
					const t = i1(e);
					return t
						.get("owner")
						.next((e) =>
							this.vi(e)
								? (I("IndexedDbPersistence", "Releasing primary lease."),
									t.delete("owner"))
								: ei.resolve(),
						);
				}
				Mi(e, t) {
					const n = Date.now();
					return (
						!(e < n - t) &&
						(!(e > n) ||
							(T(`Detected an update time that is in the future: ${e} > ${n}`),
							!1))
					);
				}
				fi() {
					null !== this.document &&
						"function" == typeof this.document.addEventListener &&
						((this.Ii = () => {
							this.ui.enqueueAndForget(
								() => (
									(this.inForeground =
										"visible" === this.document.visibilityState),
									this.mi()
								),
							);
						}),
						this.document.addEventListener("visibilitychange", this.Ii),
						(this.inForeground = "visible" === this.document.visibilityState));
				}
				Bi() {
					this.Ii &&
						(this.document.removeEventListener("visibilitychange", this.Ii),
						(this.Ii = null));
				}
				gi() {
					var e;
					"function" ==
						typeof (null === (e = this.window) || void 0 === e
							? void 0
							: e.addEventListener) &&
						((this.Pi = () => {
							this.Li();
							const e = /(?:Version|Mobile)\/1[456]/;
							(0, h.nr)() &&
								(navigator.appVersion.match(e) ||
									navigator.userAgent.match(e)) &&
								this.ui.enterRestrictedMode(!0),
								this.ui.enqueueAndForget(() => this.shutdown());
						}),
						this.window.addEventListener("pagehide", this.Pi));
				}
				ki() {
					this.Pi &&
						(this.window.removeEventListener("pagehide", this.Pi),
						(this.Pi = null));
				}
				Ni(e) {
					var t;
					try {
						const n =
							null !==
							(null === (t = this.Vi) || void 0 === t
								? void 0
								: t.getItem(this.Oi(e)));
						return (
							I(
								"IndexedDbPersistence",
								`Client '${e}' ${n ? "is" : "is not"} zombied in LocalStorage`,
							),
							n
						);
					} catch (e) {
						return (
							T("IndexedDbPersistence", "Failed to get zombied client id.", e),
							!1
						);
					}
				}
				Li() {
					if (this.Vi)
						try {
							this.Vi.setItem(this.Oi(this.clientId), String(Date.now()));
						} catch (e) {
							T("Failed to set zombie client id.", e);
						}
				}
				qi() {
					if (this.Vi)
						try {
							this.Vi.removeItem(this.Oi(this.clientId));
						} catch (e) {}
				}
				Oi(e) {
					return `firestore_zombie_${this.persistenceKey}_${e}`;
				}
			}
			function i1(e) {
				return e$(e, "owner");
			}
			function i2(e) {
				return e$(e, "clientMetadata");
			}
			function i5(e, t) {
				let n = e.projectId;
				return (
					e.isDefaultDatabase || (n += "." + e.database),
					"firestore/" + t + "/" + n + "/"
				);
			}
			class i3 {
				constructor(e, t, n, r) {
					(this.targetId = e),
						(this.fromCache = t),
						(this.$i = n),
						(this.Ui = r);
				}
				static Wi(e, t) {
					let n = nf(),
						r = nf();
					for (const e of t.docChanges)
						switch (e.type) {
							case 0:
								n = n.add(e.doc.key);
								break;
							case 1:
								r = r.add(e.doc.key);
						}
					return new i3(e, t.fromCache, n, r);
				}
			}
			class i8 {
				constructor() {
					this._documentReadCount = 0;
				}
				get documentReadCount() {
					return this._documentReadCount;
				}
				incrementDocumentReadCount(e) {
					this._documentReadCount += e;
				}
			}
			class i4 {
				constructor() {
					(this.Gi = !1),
						(this.zi = !1),
						(this.ji = 100),
						(this.Hi = (0, h.nr)() ? 8 : eo((0, h.ZQ)()) > 0 ? 6 : 4);
				}
				initialize(e, t) {
					(this.Ji = e), (this.indexManager = t), (this.Gi = !0);
				}
				getDocumentsMatchingQuery(e, t, n, r) {
					const i = { result: null };
					return this.Yi(e, t)
						.next((e) => {
							i.result = e;
						})
						.next(() => {
							if (!i.result)
								return this.Zi(e, t, r, n).next((e) => {
									i.result = e;
								});
						})
						.next(() => {
							if (i.result) return;
							const n = new i8();
							return this.Xi(e, t, n).next((r) => {
								if (((i.result = r), this.zi)) return this.es(e, t, n, r.size);
							});
						})
						.next(() => i.result);
				}
				es(e, t, n, r) {
					return n.documentReadCount < this.ji
						? (v() <= u.$b.DEBUG &&
								I(
									"QueryEngine",
									"SDK will not create cache indexes for query:",
									nt(t),
									"since it only creates cache indexes for collection contains",
									"more than or equal to",
									this.ji,
									"documents",
								),
							ei.resolve())
						: (v() <= u.$b.DEBUG &&
								I(
									"QueryEngine",
									"Query:",
									nt(t),
									"scans",
									n.documentReadCount,
									"local documents and returns",
									r,
									"documents as results.",
								),
							n.documentReadCount > this.Hi * r
								? (v() <= u.$b.DEBUG &&
										I(
											"QueryEngine",
											"The SDK decides to create cache indexes for query:",
											nt(t),
											"as using cache indexes may help improve performance.",
										),
									this.indexManager.createTargetIndexes(e, t8(t)))
								: ei.resolve());
				}
				Yi(e, t) {
					if (t2(t)) return ei.resolve(null);
					let n = t8(t);
					return this.indexManager.getIndexType(e, n).next((r) =>
						0 === r
							? null
							: (null !== t.limit &&
									1 === r &&
									(n = t8((t = t9(t, null, "F")))),
								this.indexManager.getDocumentsMatchingTarget(e, n).next((r) => {
									const i = nf(...r);
									return this.Ji.getDocuments(e, i).next((r) =>
										this.indexManager.getMinOffset(e, n).next((n) => {
											const s = this.ts(t, r);
											return this.ns(t, s, i, n.readTime)
												? this.Yi(e, t9(t, null, "F"))
												: this.rs(e, s, t, n);
										}),
									);
								})),
					);
				}
				Zi(e, t, n, r) {
					return t2(t) || r.isEqual(q.min())
						? ei.resolve(null)
						: this.Ji.getDocuments(e, n).next((i) => {
								const s = this.ts(t, i);
								return this.ns(t, s, n, r)
									? ei.resolve(null)
									: (v() <= u.$b.DEBUG &&
											I(
												"QueryEngine",
												"Re-using previous result from %s to execute query: %s",
												r.toString(),
												nt(t),
											),
										this.rs(e, s, t, Y(r, -1)).next((e) => e));
							});
				}
				ts(e, t) {
					let n = new eX(ni(e));
					return (
						t.forEach((t, r) => {
							nn(e, r) && (n = n.add(r));
						}),
						n
					);
				}
				ns(e, t, n, r) {
					if (null === e.limit) return !1;
					if (n.size !== t.size) return !0;
					const i = "F" === e.limitType ? t.last() : t.first();
					return !!i && (i.hasPendingWrites || i.version.compareTo(r) > 0);
				}
				Xi(e, t, n) {
					return (
						v() <= u.$b.DEBUG &&
							I(
								"QueryEngine",
								"Using full collection scan to execute query:",
								nt(t),
							),
						this.Ji.getDocumentsMatchingQuery(e, t, Z.min(), n)
					);
				}
				rs(e, t, n, r) {
					return this.Ji.getDocumentsMatchingQuery(e, n, r).next(
						(e) => (
							t.forEach((t) => {
								e = e.insert(t.key, t);
							}),
							e
						),
					);
				}
			}
			class i6 {
				constructor(e, t, n, r) {
					(this.persistence = e),
						(this.ss = t),
						(this.serializer = r),
						(this.os = new eJ(L)),
						(this._s = new ns((e) => tW(e), tJ)),
						(this.us = new Map()),
						(this.cs = e.getRemoteDocumentCache()),
						(this.Ur = e.getTargetCache()),
						(this.Gr = e.getBundleCache()),
						this.ls(n);
				}
				ls(e) {
					(this.documentOverlayCache =
						this.persistence.getDocumentOverlayCache(e)),
						(this.indexManager = this.persistence.getIndexManager(e)),
						(this.mutationQueue = this.persistence.getMutationQueue(
							e,
							this.indexManager,
						)),
						(this.localDocuments = new iL(
							this.cs,
							this.mutationQueue,
							this.documentOverlayCache,
							this.indexManager,
						)),
						this.cs.setIndexManager(this.indexManager),
						this.ss.initialize(this.localDocuments, this.indexManager);
				}
				collectGarbage(e) {
					return this.persistence.runTransaction(
						"Collect garbage",
						"readwrite-primary",
						(t) => e.collect(t, this.os),
					);
				}
			}
			async function i9(e, t) {
				return await e.persistence.runTransaction(
					"Handle user change",
					"readonly",
					(n) => {
						let r;
						return e.mutationQueue
							.getAllMutationBatches(n)
							.next(
								(i) => (
									(r = i), e.ls(t), e.mutationQueue.getAllMutationBatches(n)
								),
							)
							.next((t) => {
								let i = [],
									s = [],
									a = nf();
								for (const e of r)
									for (const t of (i.push(e.batchId), e.mutations))
										a = a.add(t.key);
								for (const e of t)
									for (const t of (s.push(e.batchId), e.mutations))
										a = a.add(t.key);
								return e.localDocuments
									.getDocuments(n, a)
									.next((e) => ({
										hs: e,
										removedBatchIds: i,
										addedBatchIds: s,
									}));
							});
					},
				);
			}
			function i7(e) {
				return e.persistence.runTransaction(
					"Get last remote snapshot version",
					"readonly",
					(t) => e.Ur.getLastRemoteSnapshotVersion(t),
				);
			}
			function se(e, t, n) {
				let r = nf(),
					i = nf();
				return (
					n.forEach((e) => (r = r.add(e))),
					t.getEntries(e, r).next((e) => {
						let r = na;
						return (
							n.forEach((n, s) => {
								const a = e.get(n);
								s.isFoundDocument() !== a.isFoundDocument() && (i = i.add(n)),
									s.isNoDocument() && s.version.isEqual(q.min())
										? (t.removeEntry(n, s.readTime), (r = r.insert(n, s)))
										: !a.isValidDocument() ||
												s.version.compareTo(a.version) > 0 ||
												(0 === s.version.compareTo(a.version) &&
													a.hasPendingWrites)
											? (t.addEntry(s), (r = r.insert(n, s)))
											: I(
													"LocalStore",
													"Ignoring outdated watch update for ",
													n,
													". Current version:",
													a.version,
													" Watch version:",
													s.version,
												);
							}),
							{ Ps: r, Is: i }
						);
					})
				);
			}
			function st(e, t) {
				return e.persistence
					.runTransaction("Allocate target", "readwrite", (n) => {
						let r;
						return e.Ur.getTargetData(n, t).next((i) =>
							i
								? ((r = i), ei.resolve(r))
								: e.Ur.allocateTargetId(n).next(
										(i) => (
											(r = new rx(
												t,
												i,
												"TargetPurposeListen",
												n.currentSequenceNumber,
											)),
											e.Ur.addTargetData(n, r).next(() => r)
										),
									),
						);
					})
					.then((n) => {
						const r = e.os.get(n.targetId);
						return (
							(null === r ||
								n.snapshotVersion.compareTo(r.snapshotVersion) > 0) &&
								((e.os = e.os.insert(n.targetId, n)), e._s.set(t, n.targetId)),
							n
						);
					});
			}
			async function sn(e, t, n) {
				const r = e.os.get(t);
				try {
					n ||
						(await e.persistence.runTransaction(
							"Release target",
							n ? "readwrite" : "readwrite-primary",
							(t) => e.persistence.referenceDelegate.removeTarget(t, r),
						));
				} catch (e) {
					if (!eh(e)) throw e;
					I(
						"LocalStore",
						`Failed to update sequence numbers for target ${t}: ${e}`,
					);
				}
				(e.os = e.os.remove(t)), e._s.delete(r.target);
			}
			function sr(e, t, n) {
				let r = q.min(),
					i = nf();
				return e.persistence.runTransaction("Execute query", "readwrite", (s) =>
					((e, t, n) => {
						const r = e._s.get(n);
						return void 0 !== r
							? ei.resolve(e.os.get(r))
							: e.Ur.getTargetData(t, n);
					})(e, s, t8(t))
						.next((t) => {
							if (t)
								return (
									(r = t.lastLimboFreeSnapshotVersion),
									e.Ur.getMatchingKeysForTargetId(s, t.targetId).next((e) => {
										i = e;
									})
								);
						})
						.next(() =>
							e.ss.getDocumentsMatchingQuery(
								s,
								t,
								n ? r : q.min(),
								n ? i : nf(),
							),
						)
						.next((n) => (sa(e, nr(t), n), { documents: n, Ts: i })),
				);
			}
			function si(e, t) {
				const n = e.Ur,
					r = e.os.get(t);
				return r
					? Promise.resolve(r.target)
					: e.persistence.runTransaction("Get target data", "readonly", (e) =>
							n.ot(e, t).next((e) => (e ? e.target : null)),
						);
			}
			function ss(e, t) {
				const n = e.us.get(t) || q.min();
				return e.persistence
					.runTransaction("Get new document changes", "readonly", (r) =>
						e.cs.getAllFromCollectionGroup(
							r,
							t,
							Y(n, -1),
							Number.MAX_SAFE_INTEGER,
						),
					)
					.then((n) => (sa(e, t, n), n));
			}
			function sa(e, t, n) {
				let r = e.us.get(t) || q.min();
				n.forEach((e, t) => {
					t.readTime.compareTo(r) > 0 && (r = t.readTime);
				}),
					e.us.set(t, r);
			}
			async function so(e, t, n, r) {
				let i = nf(),
					s = na;
				for (const e of n) {
					const n = t.Es(e.metadata.name);
					e.document && (i = i.add(n));
					const r = t.ds(e);
					r.setReadTime(t.As(e.metadata.readTime)), (s = s.insert(n, r));
				}
				const a = e.cs.newChangeBuffer({ trackRemovals: !0 }),
					o = await st(e, t8(t1(K.fromString(`__bundle__/docs/${r}`))));
				return e.persistence.runTransaction(
					"Apply bundle documents",
					"readwrite",
					(t) =>
						se(t, a, s)
							.next((e) => (a.apply(t), e))
							.next((n) =>
								e.Ur.removeMatchingKeysForTargetId(t, o.targetId)
									.next(() => e.Ur.addMatchingKeys(t, i, o.targetId))
									.next(() =>
										e.localDocuments.getLocalViewOfDocuments(t, n.Ps, n.Is),
									)
									.next(() => n.Ps),
							),
				);
			}
			async function sl(e, t, n = nf()) {
				const r = await st(e, t8(rM(t.bundledQuery)));
				return e.persistence.runTransaction(
					"Save named query",
					"readwrite",
					(i) => {
						const s = ra(t.readTime);
						if (r.snapshotVersion.compareTo(s) >= 0)
							return e.Gr.saveNamedQuery(i, t);
						const a = r.withResumeToken(e5.EMPTY_BYTE_STRING, s);
						return (
							(e.os = e.os.insert(a.targetId, a)),
							e.Ur.updateTargetData(i, a)
								.next(() => e.Ur.removeMatchingKeysForTargetId(i, r.targetId))
								.next(() => e.Ur.addMatchingKeys(i, n, r.targetId))
								.next(() => e.Gr.saveNamedQuery(i, t))
						);
					},
				);
			}
			function su(e, t) {
				return `firestore_clients_${e}_${t}`;
			}
			function sh(e, t, n) {
				let r = `firestore_mutations_${e}_${n}`;
				return t.isAuthenticated() && (r += `_${t.uid}`), r;
			}
			function sc(e, t) {
				return `firestore_targets_${e}_${t}`;
			}
			class sd {
				constructor(e, t, n, r) {
					(this.user = e),
						(this.batchId = t),
						(this.state = n),
						(this.error = r);
				}
				static Rs(e, t, n) {
					let r = JSON.parse(n),
						i,
						s =
							"object" == typeof r &&
							-1 !== ["pending", "acknowledged", "rejected"].indexOf(r.state) &&
							(void 0 === r.error || "object" == typeof r.error);
					return (
						s &&
							r.error &&
							(s =
								"string" == typeof r.error.message &&
								"string" == typeof r.error.code) &&
							(i = new x(r.error.code, r.error.message)),
						s
							? new sd(e, t, r.state, i)
							: (T(
									"SharedClientState",
									`Failed to parse mutation state for ID '${t}': ${n}`,
								),
								null)
					);
				}
				Vs() {
					const e = { state: this.state, updateTimeMs: Date.now() };
					return (
						this.error &&
							(e.error = {
								code: this.error.code,
								message: this.error.message,
							}),
						JSON.stringify(e)
					);
				}
			}
			class sf {
				constructor(e, t, n) {
					(this.targetId = e), (this.state = t), (this.error = n);
				}
				static Rs(e, t) {
					let n = JSON.parse(t),
						r,
						i =
							"object" == typeof n &&
							-1 !== ["not-current", "current", "rejected"].indexOf(n.state) &&
							(void 0 === n.error || "object" == typeof n.error);
					return (
						i &&
							n.error &&
							(i =
								"string" == typeof n.error.message &&
								"string" == typeof n.error.code) &&
							(r = new x(n.error.code, n.error.message)),
						i
							? new sf(e, n.state, r)
							: (T(
									"SharedClientState",
									`Failed to parse target state for ID '${e}': ${t}`,
								),
								null)
					);
				}
				Vs() {
					const e = { state: this.state, updateTimeMs: Date.now() };
					return (
						this.error &&
							(e.error = {
								code: this.error.code,
								message: this.error.message,
							}),
						JSON.stringify(e)
					);
				}
			}
			class sm {
				constructor(e, t) {
					(this.clientId = e), (this.activeTargetIds = t);
				}
				static Rs(e, t) {
					let n = JSON.parse(t),
						r = "object" == typeof n && n.activeTargetIds instanceof Array,
						i = nm;
					for (let e = 0; r && e < n.activeTargetIds.length; ++e)
						(r = eI(n.activeTargetIds[e])), (i = i.add(n.activeTargetIds[e]));
					return r
						? new sm(e, i)
						: (T(
								"SharedClientState",
								`Failed to parse client data for instance '${e}': ${t}`,
							),
							null);
				}
			}
			class sg {
				constructor(e, t) {
					(this.clientId = e), (this.onlineState = t);
				}
				static Rs(e) {
					const t = JSON.parse(e);
					return "object" == typeof t &&
						-1 !== ["Unknown", "Online", "Offline"].indexOf(t.onlineState) &&
						"string" == typeof t.clientId
						? new sg(t.clientId, t.onlineState)
						: (T("SharedClientState", `Failed to parse online state: ${e}`),
							null);
				}
			}
			class sp {
				constructor() {
					this.activeTargetIds = nm;
				}
				fs(e) {
					this.activeTargetIds = this.activeTargetIds.add(e);
				}
				gs(e) {
					this.activeTargetIds = this.activeTargetIds.delete(e);
				}
				Vs() {
					return JSON.stringify({
						activeTargetIds: this.activeTargetIds.toArray(),
						updateTimeMs: Date.now(),
					});
				}
			}
			class sy {
				constructor(e, t, n, r, i) {
					var s, a, o;
					(this.window = e),
						(this.ui = t),
						(this.persistenceKey = n),
						(this.ps = r),
						(this.syncEngine = null),
						(this.onlineStateHandler = null),
						(this.sequenceNumberHandler = null),
						(this.ys = this.ws.bind(this)),
						(this.Ss = new eJ(L)),
						(this.started = !1),
						(this.bs = []);
					const l = n.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
					(this.storage = this.window.localStorage),
						(this.currentUser = i),
						(this.Ds = su(this.persistenceKey, this.ps)),
						(this.vs =
							((s = this.persistenceKey), `firestore_sequence_number_${s}`)),
						(this.Ss = this.Ss.insert(this.ps, new sp())),
						(this.Cs = RegExp(`^firestore_clients_${l}_([^_]*)$`)),
						(this.Fs = RegExp(`^firestore_mutations_${l}_(\\d+)(?:_(.*))?$`)),
						(this.Ms = RegExp(`^firestore_targets_${l}_(\\d+)$`)),
						(this.xs =
							((a = this.persistenceKey), `firestore_online_state_${a}`)),
						(this.Os =
							((o = this.persistenceKey), `firestore_bundle_loaded_v2_${o}`)),
						this.window.addEventListener("storage", this.ys);
				}
				static D(e) {
					return !(!e || !e.localStorage);
				}
				async start() {
					for (const e of await this.syncEngine.Qi()) {
						if (e === this.ps) continue;
						const t = this.getItem(su(this.persistenceKey, e));
						if (t) {
							const n = sm.Rs(e, t);
							n && (this.Ss = this.Ss.insert(n.clientId, n));
						}
					}
					this.Ns();
					const e = this.storage.getItem(this.xs);
					if (e) {
						const t = this.Ls(e);
						t && this.Bs(t);
					}
					for (const e of this.bs) this.ws(e);
					(this.bs = []),
						this.window.addEventListener("pagehide", () => this.shutdown()),
						(this.started = !0);
				}
				writeSequenceNumber(e) {
					this.setItem(this.vs, JSON.stringify(e));
				}
				getAllActiveQueryTargets() {
					return this.ks(this.Ss);
				}
				isActiveQueryTarget(e) {
					let t = !1;
					return (
						this.Ss.forEach((n, r) => {
							r.activeTargetIds.has(e) && (t = !0);
						}),
						t
					);
				}
				addPendingMutation(e) {
					this.qs(e, "pending");
				}
				updateMutationState(e, t, n) {
					this.qs(e, t, n), this.Qs(e);
				}
				addLocalQueryTarget(e, t = !0) {
					let n = "not-current";
					if (this.isActiveQueryTarget(e)) {
						const t = this.storage.getItem(sc(this.persistenceKey, e));
						if (t) {
							const r = sf.Rs(e, t);
							r && (n = r.state);
						}
					}
					return t && this.Ks.fs(e), this.Ns(), n;
				}
				removeLocalQueryTarget(e) {
					this.Ks.gs(e), this.Ns();
				}
				isLocalQueryTarget(e) {
					return this.Ks.activeTargetIds.has(e);
				}
				clearQueryState(e) {
					this.removeItem(sc(this.persistenceKey, e));
				}
				updateQueryState(e, t, n) {
					this.$s(e, t, n);
				}
				handleUserChange(e, t, n) {
					t.forEach((e) => {
						this.Qs(e);
					}),
						(this.currentUser = e),
						n.forEach((e) => {
							this.addPendingMutation(e);
						});
				}
				setOnlineState(e) {
					this.Us(e);
				}
				notifyBundleLoaded(e) {
					this.Ws(e);
				}
				shutdown() {
					this.started &&
						(this.window.removeEventListener("storage", this.ys),
						this.removeItem(this.Ds),
						(this.started = !1));
				}
				getItem(e) {
					const t = this.storage.getItem(e);
					return I("SharedClientState", "READ", e, t), t;
				}
				setItem(e, t) {
					I("SharedClientState", "SET", e, t), this.storage.setItem(e, t);
				}
				removeItem(e) {
					I("SharedClientState", "REMOVE", e), this.storage.removeItem(e);
				}
				ws(e) {
					if (e.storageArea === this.storage) {
						if (
							(I("SharedClientState", "EVENT", e.key, e.newValue),
							e.key === this.Ds)
						)
							return void T(
								"Received WebStorage notification for local change. Another client might have garbage-collected our state",
							);
						this.ui.enqueueRetryable(async () => {
							if (this.started) {
								if (null !== e.key) {
									if (this.Cs.test(e.key)) {
										if (null == e.newValue) {
											const t = this.Gs(e.key);
											return this.zs(t, null);
										}
										{
											const t = this.js(e.key, e.newValue);
											if (t) return this.zs(t.clientId, t);
										}
									} else if (this.Fs.test(e.key)) {
										if (null !== e.newValue) {
											const t = this.Hs(e.key, e.newValue);
											if (t) return this.Js(t);
										}
									} else if (this.Ms.test(e.key)) {
										if (null !== e.newValue) {
											const t = this.Ys(e.key, e.newValue);
											if (t) return this.Zs(t);
										}
									} else if (e.key === this.xs) {
										if (null !== e.newValue) {
											const t = this.Ls(e.newValue);
											if (t) return this.Bs(t);
										}
									} else if (e.key === this.vs) {
										const t = ((e) => {
											let t = ey.oe;
											if (null != e)
												try {
													const n = JSON.parse(e);
													"number" == typeof n || _(), (t = n);
												} catch (e) {
													T(
														"SharedClientState",
														"Failed to read sequence number from WebStorage",
														e,
													);
												}
											return t;
										})(e.newValue);
										t !== ey.oe && this.sequenceNumberHandler(t);
									} else if (e.key === this.Os) {
										const t = this.Xs(e.newValue);
										await Promise.all(t.map((e) => this.syncEngine.eo(e)));
									}
								}
							} else this.bs.push(e);
						});
					}
				}
				get Ks() {
					return this.Ss.get(this.ps);
				}
				Ns() {
					this.setItem(this.Ds, this.Ks.Vs());
				}
				qs(e, t, n) {
					const r = new sd(this.currentUser, e, t, n),
						i = sh(this.persistenceKey, this.currentUser, e);
					this.setItem(i, r.Vs());
				}
				Qs(e) {
					const t = sh(this.persistenceKey, this.currentUser, e);
					this.removeItem(t);
				}
				Us(e) {
					const t = { clientId: this.ps, onlineState: e };
					this.storage.setItem(this.xs, JSON.stringify(t));
				}
				$s(e, t, n) {
					const r = sc(this.persistenceKey, e),
						i = new sf(e, t, n);
					this.setItem(r, i.Vs());
				}
				Ws(e) {
					const t = JSON.stringify(Array.from(e));
					this.setItem(this.Os, t);
				}
				Gs(e) {
					const t = this.Cs.exec(e);
					return t ? t[1] : null;
				}
				js(e, t) {
					const n = this.Gs(e);
					return sm.Rs(n, t);
				}
				Hs(e, t) {
					const n = this.Fs.exec(e),
						r = Number(n[1]),
						i = void 0 !== n[2] ? n[2] : null;
					return sd.Rs(new p(i), r, t);
				}
				Ys(e, t) {
					const n = Number(this.Ms.exec(e)[1]);
					return sf.Rs(n, t);
				}
				Ls(e) {
					return sg.Rs(e);
				}
				Xs(e) {
					return JSON.parse(e);
				}
				async Js(e) {
					if (e.user.uid === this.currentUser.uid)
						return this.syncEngine.no(e.batchId, e.state, e.error);
					I(
						"SharedClientState",
						`Ignoring mutation for non-active user ${e.user.uid}`,
					);
				}
				Zs(e) {
					return this.syncEngine.ro(e.targetId, e.state, e.error);
				}
				zs(e, t) {
					const n = t ? this.Ss.insert(e, t) : this.Ss.remove(e),
						r = this.ks(this.Ss),
						i = this.ks(n),
						s = [],
						a = [];
					return (
						i.forEach((e) => {
							r.has(e) || s.push(e);
						}),
						r.forEach((e) => {
							i.has(e) || a.push(e);
						}),
						this.syncEngine.io(s, a).then(() => {
							this.Ss = n;
						})
					);
				}
				Bs(e) {
					this.Ss.get(e.clientId) && this.onlineStateHandler(e.onlineState);
				}
				ks(e) {
					let t = nm;
					return (
						e.forEach((e, n) => {
							t = t.unionWith(n.activeTargetIds);
						}),
						t
					);
				}
			}
			class sw {
				constructor() {
					(this.so = new sp()),
						(this.oo = {}),
						(this.onlineStateHandler = null),
						(this.sequenceNumberHandler = null);
				}
				addPendingMutation(e) {}
				updateMutationState(e, t, n) {}
				addLocalQueryTarget(e, t = !0) {
					return t && this.so.fs(e), this.oo[e] || "not-current";
				}
				updateQueryState(e, t, n) {
					this.oo[e] = t;
				}
				removeLocalQueryTarget(e) {
					this.so.gs(e);
				}
				isLocalQueryTarget(e) {
					return this.so.activeTargetIds.has(e);
				}
				clearQueryState(e) {
					delete this.oo[e];
				}
				getAllActiveQueryTargets() {
					return this.so.activeTargetIds;
				}
				isActiveQueryTarget(e) {
					return this.so.activeTargetIds.has(e);
				}
				start() {
					return (this.so = new sp()), Promise.resolve();
				}
				handleUserChange(e, t, n) {}
				setOnlineState(e) {}
				shutdown() {}
				writeSequenceNumber(e) {}
				notifyBundleLoaded(e) {}
			}
			class sv {
				_o(e) {}
				shutdown() {}
			}
			class sI {
				constructor() {
					(this.ao = () => this.uo()),
						(this.co = () => this.lo()),
						(this.ho = []),
						this.Po();
				}
				_o(e) {
					this.ho.push(e);
				}
				shutdown() {
					window.removeEventListener("online", this.ao),
						window.removeEventListener("offline", this.co);
				}
				Po() {
					window.addEventListener("online", this.ao),
						window.addEventListener("offline", this.co);
				}
				uo() {
					for (const e of (I(
						"ConnectivityMonitor",
						"Network connectivity changed: AVAILABLE",
					),
					this.ho))
						e(0);
				}
				lo() {
					for (const e of (I(
						"ConnectivityMonitor",
						"Network connectivity changed: UNAVAILABLE",
					),
					this.ho))
						e(1);
				}
				static D() {
					return (
						"undefined" != typeof window &&
						void 0 !== window.addEventListener &&
						void 0 !== window.removeEventListener
					);
				}
			}
			let sT = null;
			function sE() {
				return (
					null === sT
						? (sT = 0x10000000 + Math.round(0x80000000 * Math.random()))
						: sT++,
					"0x" + sT.toString(16)
				);
			}
			const sb = {
				BatchGetDocuments: "batchGet",
				Commit: "commit",
				RunQuery: "runQuery",
				RunAggregationQuery: "runAggregationQuery",
			};
			class s_ {
				constructor(e) {
					(this.Io = e.Io), (this.To = e.To);
				}
				Eo(e) {
					this.Ao = e;
				}
				Ro(e) {
					this.Vo = e;
				}
				mo(e) {
					this.fo = e;
				}
				onMessage(e) {
					this.po = e;
				}
				close() {
					this.To();
				}
				send(e) {
					this.Io(e);
				}
				yo() {
					this.Ao();
				}
				wo() {
					this.Vo();
				}
				So(e) {
					this.fo(e);
				}
				bo(e) {
					this.po(e);
				}
			}
			const sS = "WebChannelConnection";
			class sx extends class {
				constructor(e) {
					(this.databaseInfo = e), (this.databaseId = e.databaseId);
					const t = e.ssl ? "https" : "http",
						n = encodeURIComponent(this.databaseId.projectId),
						r = encodeURIComponent(this.databaseId.database);
					(this.Do = t + "://" + e.host),
						(this.vo = `projects/${n}/databases/${r}`),
						(this.Co =
							"(default)" === this.databaseId.database
								? `project_id=${n}`
								: `project_id=${n}&database_id=${r}`);
				}
				get Fo() {
					return !1;
				}
				Mo(e, t, n, r, i) {
					const s = sE(),
						a = this.xo(e, t.toUriEncodedString());
					I("RestConnection", `Sending RPC '${e}' ${s}:`, a, n);
					const o = {
						"google-cloud-resource-prefix": this.vo,
						"x-goog-request-params": this.Co,
					};
					return (
						this.Oo(o, r, i),
						this.No(e, a, o, n).then(
							(t) => (I("RestConnection", `Received RPC '${e}' ${s}: `, t), t),
							(t) => {
								throw (
									(E(
										"RestConnection",
										`RPC '${e}' ${s} failed with error: `,
										t,
										"url: ",
										a,
										"request:",
										n,
									),
									t)
								);
							},
						)
					);
				}
				Lo(e, t, n, r, i, s) {
					return this.Mo(e, t, n, r, i);
				}
				Oo(e, t, n) {
					(e["X-Goog-Api-Client"] = (() => "gl-js/ fire/" + y)()),
						(e["Content-Type"] = "text/plain"),
						this.databaseInfo.appId &&
							(e["X-Firebase-GMPID"] = this.databaseInfo.appId),
						t && t.headers.forEach((t, n) => (e[n] = t)),
						n && n.headers.forEach((t, n) => (e[n] = t));
				}
				xo(e, t) {
					const n = sb[e];
					return `${this.Do}/v1/${t}:${n}`;
				}
				terminate() {}
			} {
				constructor(e) {
					super(e),
						(this.forceLongPolling = e.forceLongPolling),
						(this.autoDetectLongPolling = e.autoDetectLongPolling),
						(this.useFetchStreams = e.useFetchStreams),
						(this.longPollingOptions = e.longPollingOptions);
				}
				No(e, t, n, r) {
					const i = sE();
					return new Promise((s, a) => {
						const o = new d.ZS();
						o.setWithCredentials(!0),
							o.listenOnce(d.Bx.COMPLETE, () => {
								try {
									switch (o.getLastErrorCode()) {
										case d.O4.NO_ERROR:
											const t = o.getResponseJson();
											I(
												sS,
												`XHR for RPC '${e}' ${i} received:`,
												JSON.stringify(t),
											),
												s(t);
											break;
										case d.O4.TIMEOUT:
											I(sS, `RPC '${e}' ${i} timed out`),
												a(new x(S.DEADLINE_EXCEEDED, "Request time out"));
											break;
										case d.O4.HTTP_ERROR:
											const n = o.getStatus();
											if (
												(I(
													sS,
													`RPC '${e}' ${i} failed with status:`,
													n,
													"response text:",
													o.getResponseText(),
												),
												n > 0)
											) {
												let e = o.getResponseJson();
												Array.isArray(e) && (e = e[0]);
												const t = null == e ? void 0 : e.error;
												if (t && t.status && t.message) {
													const e = ((e) => {
														const t = e.toLowerCase().replace(/_/g, "-");
														return Object.values(S).indexOf(t) >= 0
															? t
															: S.UNKNOWN;
													})(t.status);
													a(new x(e, t.message));
												} else
													a(
														new x(
															S.UNKNOWN,
															"Server responded with status " + o.getStatus(),
														),
													);
											} else a(new x(S.UNAVAILABLE, "Connection failed."));
											break;
										default:
											_();
									}
								} finally {
									I(sS, `RPC '${e}' ${i} completed.`);
								}
							});
						const l = JSON.stringify(r);
						I(sS, `RPC '${e}' ${i} sending request:`, r),
							o.send(t, "POST", l, n, 15);
					});
				}
				Bo(e, t, n) {
					const i = sE(),
						s = [
							this.Do,
							"/",
							"google.firestore.v1.Firestore",
							"/",
							e,
							"/channel",
						],
						a = (0, d.fF)(),
						o = (0, d.Ao)(),
						l = {
							httpSessionIdParam: "gsessionid",
							initMessageHeaders: {},
							messageUrlParams: {
								database: `projects/${this.databaseId.projectId}/databases/${this.databaseId.database}`,
							},
							sendRawJson: !0,
							supportsCrossDomainXhr: !0,
							internalChannelParams: { forwardChannelRequestTimeoutMs: 6e5 },
							forceLongPolling: this.forceLongPolling,
							detectBufferingProxy: this.autoDetectLongPolling,
						},
						u = this.longPollingOptions.timeoutSeconds;
					void 0 !== u && (l.longPollingTimeout = Math.round(1e3 * u)),
						this.useFetchStreams && (l.useFetchStreams = !0),
						this.Oo(l.initMessageHeaders, t, n),
						(l.encodeInitMessageHeaders = !0);
					const h = s.join("");
					I(sS, `Creating RPC '${e}' stream ${i}: ${h}`, l);
					let c = a.createWebChannel(h, l),
						f = !1,
						m = !1,
						g = new s_({
							Io: (t) => {
								m
									? I(
											sS,
											`Not sending because RPC '${e}' stream ${i} is closed:`,
											t,
										)
									: (f ||
											(I(sS, `Opening RPC '${e}' stream ${i} transport.`),
											c.open(),
											(f = !0)),
										I(sS, `RPC '${e}' stream ${i} sending:`, t),
										c.send(t));
							},
							To: () => c.close(),
						}),
						p = (e, t, n) => {
							e.listen(t, (e) => {
								try {
									n(e);
								} catch (e) {
									setTimeout(() => {
										throw e;
									}, 0);
								}
							});
						};
					return (
						p(c, d.iO.EventType.OPEN, () => {
							m || (I(sS, `RPC '${e}' stream ${i} transport opened.`), g.yo());
						}),
						p(c, d.iO.EventType.CLOSE, () => {
							m ||
								((m = !0),
								I(sS, `RPC '${e}' stream ${i} transport closed`),
								g.So());
						}),
						p(c, d.iO.EventType.ERROR, (t) => {
							m ||
								((m = !0),
								E(sS, `RPC '${e}' stream ${i} transport errored:`, t),
								g.So(
									new x(S.UNAVAILABLE, "The operation could not be completed"),
								));
						}),
						p(c, d.iO.EventType.MESSAGE, (t) => {
							var n;
							if (!m) {
								const s = t.data[0];
								s || _();
								const a =
									s.error ||
									(null === (n = s[0]) || void 0 === n ? void 0 : n.error);
								if (a) {
									I(sS, `RPC '${e}' stream ${i} received error:`, a);
									let t = a.status,
										n = ((e) => {
											const t = r[e];
											if (void 0 !== t) return nW(t);
										})(t),
										s = a.message;
									void 0 === n &&
										((n = S.INTERNAL),
										(s =
											"Unknown error status: " +
											t +
											" with message " +
											a.message)),
										(m = !0),
										g.So(new x(n, s)),
										c.close();
								} else I(sS, `RPC '${e}' stream ${i} received:`, s), g.bo(s);
							}
						}),
						p(o, d.Jh.STAT_EVENT, (t) => {
							t.stat === d.ro.PROXY
								? I(sS, `RPC '${e}' stream ${i} detected buffering proxy`)
								: t.stat === d.ro.NOPROXY &&
									I(sS, `RPC '${e}' stream ${i} detected no buffering proxy`);
						}),
						setTimeout(() => {
							g.wo();
						}, 0),
						g
					);
				}
			}
			function sD() {
				return "undefined" != typeof window ? window : null;
			}
			function sC() {
				return "undefined" != typeof document ? document : null;
			}
			function sN(e) {
				return new rn(e, !0);
			}
			class sk {
				constructor(e, t, n = 1e3, r = 1.5, i = 6e4) {
					(this.ui = e),
						(this.timerId = t),
						(this.ko = n),
						(this.qo = r),
						(this.Qo = i),
						(this.Ko = 0),
						(this.$o = null),
						(this.Uo = Date.now()),
						this.reset();
				}
				reset() {
					this.Ko = 0;
				}
				Wo() {
					this.Ko = this.Qo;
				}
				Go(e) {
					this.cancel();
					const t = Math.floor(this.Ko + this.zo()),
						n = Math.max(0, Date.now() - this.Uo),
						r = Math.max(0, t - n);
					r > 0 &&
						I(
							"ExponentialBackoff",
							`Backing off for ${r} ms (base delay: ${this.Ko} ms, delay with jitter: ${t} ms, last attempt: ${n} ms ago)`,
						),
						(this.$o = this.ui.enqueueAfterDelay(
							this.timerId,
							r,
							() => ((this.Uo = Date.now()), e()),
						)),
						(this.Ko *= this.qo),
						this.Ko < this.ko && (this.Ko = this.ko),
						this.Ko > this.Qo && (this.Ko = this.Qo);
				}
				jo() {
					null !== this.$o && (this.$o.skipDelay(), (this.$o = null));
				}
				cancel() {
					null !== this.$o && (this.$o.cancel(), (this.$o = null));
				}
				zo() {
					return (Math.random() - 0.5) * this.Ko;
				}
			}
			class sA {
				constructor(e, t, n, r, i, s, a, o) {
					(this.ui = e),
						(this.Ho = n),
						(this.Jo = r),
						(this.connection = i),
						(this.authCredentialsProvider = s),
						(this.appCheckCredentialsProvider = a),
						(this.listener = o),
						(this.state = 0),
						(this.Yo = 0),
						(this.Zo = null),
						(this.Xo = null),
						(this.stream = null),
						(this.e_ = 0),
						(this.t_ = new sk(e, t));
				}
				n_() {
					return 1 === this.state || 5 === this.state || this.r_();
				}
				r_() {
					return 2 === this.state || 3 === this.state;
				}
				start() {
					(this.e_ = 0), 4 !== this.state ? this.auth() : this.i_();
				}
				async stop() {
					this.n_() && (await this.close(0));
				}
				s_() {
					(this.state = 0), this.t_.reset();
				}
				o_() {
					this.r_() &&
						null === this.Zo &&
						(this.Zo = this.ui.enqueueAfterDelay(this.Ho, 6e4, () =>
							this.__(),
						));
				}
				a_(e) {
					this.u_(), this.stream.send(e);
				}
				async __() {
					if (this.r_()) return this.close(0);
				}
				u_() {
					this.Zo && (this.Zo.cancel(), (this.Zo = null));
				}
				c_() {
					this.Xo && (this.Xo.cancel(), (this.Xo = null));
				}
				async close(e, t) {
					this.u_(),
						this.c_(),
						this.t_.cancel(),
						this.Yo++,
						4 !== e
							? this.t_.reset()
							: t && t.code === S.RESOURCE_EXHAUSTED
								? (T(t.toString()),
									T(
										"Using maximum backoff delay to prevent overloading the backend.",
									),
									this.t_.Wo())
								: t &&
									t.code === S.UNAUTHENTICATED &&
									3 !== this.state &&
									(this.authCredentialsProvider.invalidateToken(),
									this.appCheckCredentialsProvider.invalidateToken()),
						null !== this.stream &&
							(this.l_(), this.stream.close(), (this.stream = null)),
						(this.state = e),
						await this.listener.mo(t);
				}
				l_() {}
				auth() {
					this.state = 1;
					const e = this.h_(this.Yo),
						t = this.Yo;
					Promise.all([
						this.authCredentialsProvider.getToken(),
						this.appCheckCredentialsProvider.getToken(),
					]).then(
						([e, n]) => {
							this.Yo === t && this.P_(e, n);
						},
						(t) => {
							e(() => {
								const e = new x(
									S.UNKNOWN,
									"Fetching auth token failed: " + t.message,
								);
								return this.I_(e);
							});
						},
					);
				}
				P_(e, t) {
					const n = this.h_(this.Yo);
					(this.stream = this.T_(e, t)),
						this.stream.Eo(() => {
							n(() => this.listener.Eo());
						}),
						this.stream.Ro(() => {
							n(
								() => (
									(this.state = 2),
									(this.Xo = this.ui.enqueueAfterDelay(
										this.Jo,
										1e4,
										() => (this.r_() && (this.state = 3), Promise.resolve()),
									)),
									this.listener.Ro()
								),
							);
						}),
						this.stream.mo((e) => {
							n(() => this.I_(e));
						}),
						this.stream.onMessage((e) => {
							n(() => (1 == ++this.e_ ? this.E_(e) : this.onNext(e)));
						});
				}
				i_() {
					(this.state = 5),
						this.t_.Go(async () => {
							(this.state = 0), this.start();
						});
				}
				I_(e) {
					return (
						I("PersistentStream", `close with error: ${e}`),
						(this.stream = null),
						this.close(4, e)
					);
				}
				h_(e) {
					return (t) => {
						this.ui.enqueueAndForget(() =>
							this.Yo === e
								? t()
								: (I(
										"PersistentStream",
										"stream callback skipped by getCloseGuardedDispatcher.",
									),
									Promise.resolve()),
						);
					};
				}
			}
			class sV extends sA {
				constructor(e, t, n, r, i, s) {
					super(
						e,
						"listen_stream_connection_backoff",
						"listen_stream_idle",
						"health_check_timeout",
						t,
						n,
						r,
						s,
					),
						(this.serializer = i);
				}
				T_(e, t) {
					return this.connection.Bo("Listen", e, t);
				}
				E_(e) {
					return this.onNext(e);
				}
				onNext(e) {
					this.t_.reset();
					const t = ((e, t) => {
							let n;
							if ("targetChange" in t) {
								var r, i;
								t.targetChange;
								const s =
										"NO_CHANGE" ===
										(r = t.targetChange.targetChangeType || "NO_CHANGE")
											? 0
											: "ADD" === r
												? 1
												: "REMOVE" === r
													? 2
													: "CURRENT" === r
														? 3
														: "RESET" === r
															? 4
															: _(),
									a = t.targetChange.targetIds || [],
									o =
										((i = t.targetChange.resumeToken),
										e.useProto3Json
											? (void 0 === i || "string" == typeof i || _(),
												e5.fromBase64String(i || ""))
											: (void 0 === i ||
													i instanceof m ||
													i instanceof Uint8Array ||
													_(),
												e5.fromUint8Array(i || new Uint8Array()))),
									l = t.targetChange.cause;
								n = new n3(
									s,
									a,
									o,
									(l &&
										new x(
											void 0 === l.code ? S.UNKNOWN : nW(l.code),
											l.message || "",
										)) ||
										null,
								);
							} else if ("documentChange" in t) {
								t.documentChange;
								const r = t.documentChange;
								r.document, r.document.name, r.document.updateTime;
								const i = rc(e, r.document.name),
									s = ra(r.document.updateTime),
									a = r.document.createTime
										? ra(r.document.createTime)
										: q.min(),
									o = new t_({ mapValue: { fields: r.document.fields } }),
									l = tS.newFoundDocument(i, s, a, o);
								n = new n2(
									r.targetIds || [],
									r.removedTargetIds || [],
									l.key,
									l,
								);
							} else if ("documentDelete" in t) {
								t.documentDelete;
								const r = t.documentDelete;
								r.document;
								const i = rc(e, r.document),
									s = r.readTime ? ra(r.readTime) : q.min(),
									a = tS.newNoDocument(i, s);
								n = new n2([], r.removedTargetIds || [], a.key, a);
							} else if ("documentRemove" in t) {
								t.documentRemove;
								const r = t.documentRemove;
								r.document;
								const i = rc(e, r.document);
								n = new n2([], r.removedTargetIds || [], i, null);
							} else {
								if (!("filter" in t)) return _();
								{
									t.filter;
									const e = t.filter;
									e.targetId;
									const { count: r = 0, unchangedNames: i } = e,
										s = new nQ(r, i);
									n = new n5(e.targetId, s);
								}
							}
							return n;
						})(this.serializer, e),
						n = ((e) => {
							if (!("targetChange" in e)) return q.min();
							const t = e.targetChange;
							return t.targetIds && t.targetIds.length
								? q.min()
								: t.readTime
									? ra(t.readTime)
									: q.min();
						})(e);
					return this.listener.d_(t, n);
				}
				A_(e) {
					const t = {};
					(t.database = rm(this.serializer)),
						(t.addTarget = ((e, t) => {
							let n;
							const r = t.target;
							if (
								(((n = tH(r)
									? { documents: rI(e, r) }
									: { query: rT(e, r)._t }).targetId = t.targetId),
								t.resumeToken.approximateByteSize() > 0)
							) {
								n.resumeToken = rs(e, t.resumeToken);
								const r = rr(e, t.expectedCount);
								null !== r && (n.expectedCount = r);
							} else if (t.snapshotVersion.compareTo(q.min()) > 0) {
								n.readTime = ri(e, t.snapshotVersion.toTimestamp());
								const r = rr(e, t.expectedCount);
								null !== r && (n.expectedCount = r);
							}
							return n;
						})(this.serializer, e));
					const n = ((e, t) => {
						const n = ((e) => {
							switch (e) {
								case "TargetPurposeListen":
									return null;
								case "TargetPurposeExistenceFilterMismatch":
									return "existence-filter-mismatch";
								case "TargetPurposeExistenceFilterMismatchBloom":
									return "existence-filter-mismatch-bloom";
								case "TargetPurposeLimboResolution":
									return "limbo-document";
								default:
									return _();
							}
						})(t.purpose);
						return null == n ? null : { "goog-listen-tags": n };
					})(this.serializer, e);
					n && (t.labels = n), this.a_(t);
				}
				R_(e) {
					const t = {};
					(t.database = rm(this.serializer)), (t.removeTarget = e), this.a_(t);
				}
			}
			class sR extends sA {
				constructor(e, t, n, r, i, s) {
					super(
						e,
						"write_stream_connection_backoff",
						"write_stream_idle",
						"health_check_timeout",
						t,
						n,
						r,
						s,
					),
						(this.serializer = i);
				}
				get V_() {
					return this.e_ > 0;
				}
				start() {
					(this.lastStreamToken = void 0), super.start();
				}
				l_() {
					this.V_ && this.m_([]);
				}
				T_(e, t) {
					return this.connection.Bo("Write", e, t);
				}
				E_(e) {
					return (
						e.streamToken || _(),
						(this.lastStreamToken = e.streamToken),
						e.writeResults && 0 !== e.writeResults.length && _(),
						this.listener.f_()
					);
				}
				onNext(e) {
					var t, n;
					e.streamToken || _(),
						(this.lastStreamToken = e.streamToken),
						this.t_.reset();
					const r =
							((t = e.writeResults),
							(n = e.commitTime),
							t && t.length > 0
								? (void 0 !== n || _(),
									t.map((e) => {
										let t;
										return (
											(t = e.updateTime ? ra(e.updateTime) : ra(n)).isEqual(
												q.min(),
											) && (t = ra(n)),
											new nN(t, e.transformResults || [])
										);
									}))
								: []),
						i = ra(e.commitTime);
					return this.listener.g_(i, r);
				}
				p_() {
					const e = {};
					(e.database = rm(this.serializer)), this.a_(e);
				}
				m_(e) {
					const t = {
						streamToken: this.lastStreamToken,
						writes: e.map((e) => rw(this.serializer, e)),
					};
					this.a_(t);
				}
			}
			class sO extends class {} {
				constructor(e, t, n, r) {
					super(),
						(this.authCredentials = e),
						(this.appCheckCredentials = t),
						(this.connection = n),
						(this.serializer = r),
						(this.y_ = !1);
				}
				w_() {
					if (this.y_)
						throw new x(
							S.FAILED_PRECONDITION,
							"The client has already been terminated.",
						);
				}
				Mo(e, t, n, r) {
					return (
						this.w_(),
						Promise.all([
							this.authCredentials.getToken(),
							this.appCheckCredentials.getToken(),
						])
							.then(([i, s]) => this.connection.Mo(e, rl(t, n), r, i, s))
							.catch((e) => {
								throw "FirebaseError" === e.name
									? (e.code === S.UNAUTHENTICATED &&
											(this.authCredentials.invalidateToken(),
											this.appCheckCredentials.invalidateToken()),
										e)
									: new x(S.UNKNOWN, e.toString());
							})
					);
				}
				Lo(e, t, n, r, i) {
					return (
						this.w_(),
						Promise.all([
							this.authCredentials.getToken(),
							this.appCheckCredentials.getToken(),
						])
							.then(([s, a]) => this.connection.Lo(e, rl(t, n), r, s, a, i))
							.catch((e) => {
								throw "FirebaseError" === e.name
									? (e.code === S.UNAUTHENTICATED &&
											(this.authCredentials.invalidateToken(),
											this.appCheckCredentials.invalidateToken()),
										e)
									: new x(S.UNKNOWN, e.toString());
							})
					);
				}
				terminate() {
					(this.y_ = !0), this.connection.terminate();
				}
			}
			class sM {
				constructor(e, t) {
					(this.asyncQueue = e),
						(this.onlineStateHandler = t),
						(this.state = "Unknown"),
						(this.S_ = 0),
						(this.b_ = null),
						(this.D_ = !0);
				}
				v_() {
					0 === this.S_ &&
						(this.C_("Unknown"),
						(this.b_ = this.asyncQueue.enqueueAfterDelay(
							"online_state_timeout",
							1e4,
							() => (
								(this.b_ = null),
								this.F_("Backend didn't respond within 10 seconds."),
								this.C_("Offline"),
								Promise.resolve()
							),
						)));
				}
				M_(e) {
					"Online" === this.state
						? this.C_("Unknown")
						: (this.S_++,
							this.S_ >= 1 &&
								(this.x_(),
								this.F_(
									`Connection failed 1 times. Most recent error: ${e.toString()}`,
								),
								this.C_("Offline")));
				}
				set(e) {
					this.x_(),
						(this.S_ = 0),
						"Online" === e && (this.D_ = !1),
						this.C_(e);
				}
				C_(e) {
					e !== this.state && ((this.state = e), this.onlineStateHandler(e));
				}
				F_(e) {
					const t = `Could not reach Cloud Firestore backend. ${e}
This typically indicates that your device does not have a healthy Internet connection at the moment. The client will operate in offline mode until it is able to successfully connect to the backend.`;
					this.D_ ? (T(t), (this.D_ = !1)) : I("OnlineStateTracker", t);
				}
				x_() {
					null !== this.b_ && (this.b_.cancel(), (this.b_ = null));
				}
			}
			class sF {
				constructor(e, t, n, r, i) {
					(this.localStore = e),
						(this.datastore = t),
						(this.asyncQueue = n),
						(this.remoteSyncer = {}),
						(this.O_ = []),
						(this.N_ = new Map()),
						(this.L_ = new Set()),
						(this.B_ = []),
						(this.k_ = i),
						this.k_._o((e) => {
							n.enqueueAndForget(async () => {
								s$(this) &&
									(I(
										"RemoteStore",
										"Restarting streams for network reachability change.",
									),
									await (async (e) => {
										e.L_.add(4),
											await sP(e),
											e.q_.set("Unknown"),
											e.L_.delete(4),
											await sL(e);
									})(this));
							});
						}),
						(this.q_ = new sM(n, r));
				}
			}
			async function sL(e) {
				if (s$(e)) for (const t of e.B_) await t(!0);
			}
			async function sP(e) {
				for (const t of e.B_) await t(!1);
			}
			function sU(e, t) {
				e.N_.has(t.targetId) ||
					(e.N_.set(t.targetId, t), sG(e) ? sz(e) : s6(e).r_() && sB(e, t));
			}
			function sq(e, t) {
				const n = s6(e);
				e.N_.delete(t),
					n.r_() && sK(e, t),
					0 === e.N_.size && (n.r_() ? n.o_() : s$(e) && e.q_.set("Unknown"));
			}
			function sB(e, t) {
				if (
					(e.Q_.xe(t.targetId),
					t.resumeToken.approximateByteSize() > 0 ||
						t.snapshotVersion.compareTo(q.min()) > 0)
				) {
					const n = e.remoteSyncer.getRemoteKeysForTarget(t.targetId).size;
					t = t.withExpectedCount(n);
				}
				s6(e).A_(t);
			}
			function sK(e, t) {
				e.Q_.xe(t), s6(e).R_(t);
			}
			function sz(e) {
				(e.Q_ = new n4({
					getRemoteKeysForTarget: (t) =>
						e.remoteSyncer.getRemoteKeysForTarget(t),
					ot: (t) => e.N_.get(t) || null,
					tt: () => e.datastore.serializer.databaseId,
				})),
					s6(e).start(),
					e.q_.v_();
			}
			function sG(e) {
				return s$(e) && !s6(e).n_() && e.N_.size > 0;
			}
			function s$(e) {
				return 0 === e.L_.size;
			}
			async function sQ(e) {
				e.q_.set("Online");
			}
			async function sj(e) {
				e.N_.forEach((t, n) => {
					sB(e, t);
				});
			}
			async function sW(e, t) {
				(e.Q_ = void 0), sG(e) ? (e.q_.M_(t), sz(e)) : e.q_.set("Unknown");
			}
			async function sJ(e, t, n) {
				if ((e.q_.set("Online"), t instanceof n3 && 2 === t.state && t.cause))
					try {
						await (async (e, t) => {
							const n = t.cause;
							for (const r of t.targetIds)
								e.N_.has(r) &&
									(await e.remoteSyncer.rejectListen(r, n),
									e.N_.delete(r),
									e.Q_.removeTarget(r));
						})(e, t);
					} catch (n) {
						I(
							"RemoteStore",
							"Failed to remove targets %s: %s ",
							t.targetIds.join(","),
							n,
						),
							await sH(e, n);
					}
				else if (
					(t instanceof n2
						? e.Q_.Ke(t)
						: t instanceof n5
							? e.Q_.He(t)
							: e.Q_.We(t),
					!n.isEqual(q.min()))
				)
					try {
						const t = await i7(e.localStore);
						n.compareTo(t) >= 0 &&
							(await ((e, t) => {
								const n = e.Q_.rt(t);
								return (
									n.targetChanges.forEach((n, r) => {
										if (n.resumeToken.approximateByteSize() > 0) {
											const i = e.N_.get(r);
											i && e.N_.set(r, i.withResumeToken(n.resumeToken, t));
										}
									}),
									n.targetMismatches.forEach((t, n) => {
										const r = e.N_.get(t);
										if (!r) return;
										e.N_.set(
											t,
											r.withResumeToken(
												e5.EMPTY_BYTE_STRING,
												r.snapshotVersion,
											),
										),
											sK(e, t);
										const i = new rx(r.target, t, n, r.sequenceNumber);
										sB(e, i);
									}),
									e.remoteSyncer.applyRemoteEvent(n)
								);
							})(e, n));
					} catch (t) {
						I("RemoteStore", "Failed to raise snapshot:", t), await sH(e, t);
					}
			}
			async function sH(e, t, n) {
				if (!eh(t)) throw t;
				e.L_.add(1),
					await sP(e),
					e.q_.set("Offline"),
					n || (n = () => i7(e.localStore)),
					e.asyncQueue.enqueueRetryable(async () => {
						I("RemoteStore", "Retrying IndexedDB access"),
							await n(),
							e.L_.delete(1),
							await sL(e);
					});
			}
			function sY(e, t) {
				return t().catch((n) => sH(e, n, t));
			}
			async function sX(e) {
				var t;
				let n = s9(e),
					r = e.O_.length > 0 ? e.O_[e.O_.length - 1].batchId : -1;
				while (s$((t = e)) && t.O_.length < 10)
					try {
						const t = await ((e, t) =>
							e.persistence.runTransaction(
								"Get next mutation batch",
								"readonly",
								(n) => (
									void 0 === t && (t = -1),
									e.mutationQueue.getNextMutationBatchAfterBatchId(n, t)
								),
							))(e.localStore, r);
						if (null === t) {
							0 === e.O_.length && n.o_();
							break;
						}
						(r = t.batchId),
							((e, t) => {
								e.O_.push(t);
								const n = s9(e);
								n.r_() && n.V_ && n.m_(t.mutations);
							})(e, t);
					} catch (t) {
						await sH(e, t);
					}
				sZ(e) && s0(e);
			}
			function sZ(e) {
				return s$(e) && !s9(e).n_() && e.O_.length > 0;
			}
			function s0(e) {
				s9(e).start();
			}
			async function s1(e) {
				s9(e).p_();
			}
			async function s2(e) {
				const t = s9(e);
				for (const n of e.O_) t.m_(n.mutations);
			}
			async function s5(e, t, n) {
				const r = e.O_.shift(),
					i = nG.from(r, t, n);
				await sY(e, () => e.remoteSyncer.applySuccessfulWrite(i)), await sX(e);
			}
			async function s3(e, t) {
				t &&
					s9(e).V_ &&
					(await (async (e, t) => {
						var n;
						if (nj((n = t.code)) && n !== S.ABORTED) {
							const n = e.O_.shift();
							s9(e).s_(),
								await sY(e, () =>
									e.remoteSyncer.rejectFailedWrite(n.batchId, t),
								),
								await sX(e);
						}
					})(e, t)),
					sZ(e) && s0(e);
			}
			async function s8(e, t) {
				e.asyncQueue.verifyOperationInProgress(),
					I("RemoteStore", "RemoteStore received new credentials");
				const n = s$(e);
				e.L_.add(3),
					await sP(e),
					n && e.q_.set("Unknown"),
					await e.remoteSyncer.handleCredentialChange(t),
					e.L_.delete(3),
					await sL(e);
			}
			async function s4(e, t) {
				t
					? (e.L_.delete(2), await sL(e))
					: t || (e.L_.add(2), await sP(e), e.q_.set("Unknown"));
			}
			function s6(e) {
				var t, n, r;
				return (
					e.K_ ||
						((t = e.datastore),
						(n = e.asyncQueue),
						(r = {
							Eo: sQ.bind(null, e),
							Ro: sj.bind(null, e),
							mo: sW.bind(null, e),
							d_: sJ.bind(null, e),
						}),
						t.w_(),
						(e.K_ = new sV(
							n,
							t.connection,
							t.authCredentials,
							t.appCheckCredentials,
							t.serializer,
							r,
						)),
						e.B_.push(async (t) => {
							t
								? (e.K_.s_(), sG(e) ? sz(e) : e.q_.set("Unknown"))
								: (await e.K_.stop(), (e.Q_ = void 0));
						})),
					e.K_
				);
			}
			function s9(e) {
				var t, n, r;
				return (
					e.U_ ||
						((t = e.datastore),
						(n = e.asyncQueue),
						(r = {
							Eo: () => Promise.resolve(),
							Ro: s1.bind(null, e),
							mo: s3.bind(null, e),
							f_: s2.bind(null, e),
							g_: s5.bind(null, e),
						}),
						t.w_(),
						(e.U_ = new sR(
							n,
							t.connection,
							t.authCredentials,
							t.appCheckCredentials,
							t.serializer,
							r,
						)),
						e.B_.push(async (t) => {
							t
								? (e.U_.s_(), await sX(e))
								: (await e.U_.stop(),
									e.O_.length > 0 &&
										(I(
											"RemoteStore",
											`Stopping write stream with ${e.O_.length} pending writes`,
										),
										(e.O_ = [])));
						})),
					e.U_
				);
			}
			class s7 {
				constructor(e, t, n, r, i) {
					(this.asyncQueue = e),
						(this.timerId = t),
						(this.targetTimeMs = n),
						(this.op = r),
						(this.removalCallback = i),
						(this.deferred = new D()),
						(this.then = this.deferred.promise.then.bind(
							this.deferred.promise,
						)),
						this.deferred.promise.catch((e) => {});
				}
				get promise() {
					return this.deferred.promise;
				}
				static createAndSchedule(e, t, n, r, i) {
					const s = new s7(e, t, Date.now() + n, r, i);
					return s.start(n), s;
				}
				start(e) {
					this.timerHandle = setTimeout(() => this.handleDelayElapsed(), e);
				}
				skipDelay() {
					return this.handleDelayElapsed();
				}
				cancel(e) {
					null !== this.timerHandle &&
						(this.clearTimeout(),
						this.deferred.reject(
							new x(S.CANCELLED, "Operation cancelled" + (e ? ": " + e : "")),
						));
				}
				handleDelayElapsed() {
					this.asyncQueue.enqueueAndForget(() =>
						null !== this.timerHandle
							? (this.clearTimeout(),
								this.op().then((e) => this.deferred.resolve(e)))
							: Promise.resolve(),
					);
				}
				clearTimeout() {
					null !== this.timerHandle &&
						(this.removalCallback(this),
						clearTimeout(this.timerHandle),
						(this.timerHandle = null));
				}
			}
			function ae(e, t) {
				if ((T("AsyncQueue", `${t}: ${e}`), eh(e)))
					return new x(S.UNAVAILABLE, `${t}: ${e}`);
				throw e;
			}
			class at {
				constructor(e) {
					(this.comparator = e
						? (t, n) => e(t, n) || $.comparator(t.key, n.key)
						: (e, t) => $.comparator(e.key, t.key)),
						(this.keyedMap = nl()),
						(this.sortedSet = new eJ(this.comparator));
				}
				static emptySet(e) {
					return new at(e.comparator);
				}
				has(e) {
					return null != this.keyedMap.get(e);
				}
				get(e) {
					return this.keyedMap.get(e);
				}
				first() {
					return this.sortedSet.minKey();
				}
				last() {
					return this.sortedSet.maxKey();
				}
				isEmpty() {
					return this.sortedSet.isEmpty();
				}
				indexOf(e) {
					const t = this.keyedMap.get(e);
					return t ? this.sortedSet.indexOf(t) : -1;
				}
				get size() {
					return this.sortedSet.size;
				}
				forEach(e) {
					this.sortedSet.inorderTraversal((t, n) => (e(t), !1));
				}
				add(e) {
					const t = this.delete(e.key);
					return t.copy(
						t.keyedMap.insert(e.key, e),
						t.sortedSet.insert(e, null),
					);
				}
				delete(e) {
					const t = this.get(e);
					return t
						? this.copy(this.keyedMap.remove(e), this.sortedSet.remove(t))
						: this;
				}
				isEqual(e) {
					if (!(e instanceof at) || this.size !== e.size) return !1;
					const t = this.sortedSet.getIterator(),
						n = e.sortedSet.getIterator();
					while (t.hasNext()) {
						const e = t.getNext().key,
							r = n.getNext().key;
						if (!e.isEqual(r)) return !1;
					}
					return !0;
				}
				toString() {
					const e = [];
					return (
						this.forEach((t) => {
							e.push(t.toString());
						}),
						0 === e.length
							? "DocumentSet ()"
							: "DocumentSet (\n  " + e.join("  \n") + "\n)"
					);
				}
				copy(e, t) {
					const n = new at();
					return (
						(n.comparator = this.comparator),
						(n.keyedMap = e),
						(n.sortedSet = t),
						n
					);
				}
			}
			class an {
				constructor() {
					this.W_ = new eJ($.comparator);
				}
				track(e) {
					const t = e.doc.key,
						n = this.W_.get(t);
					n
						? 0 !== e.type && 3 === n.type
							? (this.W_ = this.W_.insert(t, e))
							: 3 === e.type && 1 !== n.type
								? (this.W_ = this.W_.insert(t, { type: n.type, doc: e.doc }))
								: 2 === e.type && 2 === n.type
									? (this.W_ = this.W_.insert(t, { type: 2, doc: e.doc }))
									: 2 === e.type && 0 === n.type
										? (this.W_ = this.W_.insert(t, { type: 0, doc: e.doc }))
										: 1 === e.type && 0 === n.type
											? (this.W_ = this.W_.remove(t))
											: 1 === e.type && 2 === n.type
												? (this.W_ = this.W_.insert(t, { type: 1, doc: n.doc }))
												: 0 === e.type && 1 === n.type
													? (this.W_ = this.W_.insert(t, {
															type: 2,
															doc: e.doc,
														}))
													: _()
						: (this.W_ = this.W_.insert(t, e));
				}
				G_() {
					const e = [];
					return (
						this.W_.inorderTraversal((t, n) => {
							e.push(n);
						}),
						e
					);
				}
			}
			class ar {
				constructor(e, t, n, r, i, s, a, o, l) {
					(this.query = e),
						(this.docs = t),
						(this.oldDocs = n),
						(this.docChanges = r),
						(this.mutatedKeys = i),
						(this.fromCache = s),
						(this.syncStateChanged = a),
						(this.excludesMetadataChanges = o),
						(this.hasCachedResults = l);
				}
				static fromInitialDocuments(e, t, n, r, i) {
					const s = [];
					return (
						t.forEach((e) => {
							s.push({ type: 0, doc: e });
						}),
						new ar(e, t, at.emptySet(t), s, n, r, !0, !1, i)
					);
				}
				get hasPendingWrites() {
					return !this.mutatedKeys.isEmpty();
				}
				isEqual(e) {
					if (
						!(
							this.fromCache === e.fromCache &&
							this.hasCachedResults === e.hasCachedResults &&
							this.syncStateChanged === e.syncStateChanged &&
							this.mutatedKeys.isEqual(e.mutatedKeys) &&
							t7(this.query, e.query) &&
							this.docs.isEqual(e.docs) &&
							this.oldDocs.isEqual(e.oldDocs)
						)
					)
						return !1;
					const t = this.docChanges,
						n = e.docChanges;
					if (t.length !== n.length) return !1;
					for (let e = 0; e < t.length; e++)
						if (t[e].type !== n[e].type || !t[e].doc.isEqual(n[e].doc))
							return !1;
					return !0;
				}
			}
			class ai {
				constructor() {
					(this.z_ = void 0), (this.j_ = []);
				}
				H_() {
					return this.j_.some((e) => e.J_());
				}
			}
			class as {
				constructor() {
					(this.queries = aa()),
						(this.onlineState = "Unknown"),
						(this.Y_ = new Set());
				}
				terminate() {
					!((e, t) => {
						const n = e.queries;
						(e.queries = aa()),
							n.forEach((e, n) => {
								for (const e of n.j_) e.onError(t);
							});
					})(this, new x(S.ABORTED, "Firestore shutting down"));
				}
			}
			function aa() {
				return new ns((e) => ne(e), t7);
			}
			async function ao(e, t) {
				let n = 3,
					r = t.query,
					i = e.queries.get(r);
				i ? !i.H_() && t.J_() && (n = 2) : ((i = new ai()), (n = +!t.J_()));
				try {
					switch (n) {
						case 0:
							i.z_ = await e.onListen(r, !0);
							break;
						case 1:
							i.z_ = await e.onListen(r, !1);
							break;
						case 2:
							await e.onFirstRemoteStoreListen(r);
					}
				} catch (n) {
					const e = ae(n, `Initialization of query '${nt(t.query)}' failed`);
					return void t.onError(e);
				}
				e.queries.set(r, i),
					i.j_.push(t),
					t.Z_(e.onlineState),
					i.z_ && t.X_(i.z_) && ac(e);
			}
			async function al(e, t) {
				let n = t.query,
					r = 3,
					i = e.queries.get(n);
				if (i) {
					const e = i.j_.indexOf(t);
					e >= 0 &&
						(i.j_.splice(e, 1),
						0 === i.j_.length ? (r = +!t.J_()) : !i.H_() && t.J_() && (r = 2));
				}
				switch (r) {
					case 0:
						return e.queries.delete(n), e.onUnlisten(n, !0);
					case 1:
						return e.queries.delete(n), e.onUnlisten(n, !1);
					case 2:
						return e.onLastRemoteStoreUnlisten(n);
					default:
						return;
				}
			}
			function au(e, t) {
				let n = !1;
				for (const r of t) {
					const t = r.query,
						i = e.queries.get(t);
					if (i) {
						for (const e of i.j_) e.X_(r) && (n = !0);
						i.z_ = r;
					}
				}
				n && ac(e);
			}
			function ah(e, t, n) {
				const r = e.queries.get(t);
				if (r) for (const e of r.j_) e.onError(n);
				e.queries.delete(t);
			}
			function ac(e) {
				e.Y_.forEach((e) => {
					e.next();
				});
			}
			((a = s || (s = {})).ea = "default"), (a.Cache = "cache");
			class ad {
				constructor(e, t, n) {
					(this.query = e),
						(this.ta = t),
						(this.na = !1),
						(this.ra = null),
						(this.onlineState = "Unknown"),
						(this.options = n || {});
				}
				X_(e) {
					if (!this.options.includeMetadataChanges) {
						const t = [];
						for (const n of e.docChanges) 3 !== n.type && t.push(n);
						e = new ar(
							e.query,
							e.docs,
							e.oldDocs,
							t,
							e.mutatedKeys,
							e.fromCache,
							e.syncStateChanged,
							!0,
							e.hasCachedResults,
						);
					}
					let t = !1;
					return (
						this.na
							? this.ia(e) && (this.ta.next(e), (t = !0))
							: this.sa(e, this.onlineState) && (this.oa(e), (t = !0)),
						(this.ra = e),
						t
					);
				}
				onError(e) {
					this.ta.error(e);
				}
				Z_(e) {
					this.onlineState = e;
					let t = !1;
					return (
						this.ra &&
							!this.na &&
							this.sa(this.ra, e) &&
							(this.oa(this.ra), (t = !0)),
						t
					);
				}
				sa(e, t) {
					return (
						!(e.fromCache && this.J_()) ||
						((!this.options._a || "Offline" === t) &&
							(!e.docs.isEmpty() || e.hasCachedResults || "Offline" === t))
					);
				}
				ia(e) {
					if (e.docChanges.length > 0) return !0;
					const t = this.ra && this.ra.hasPendingWrites !== e.hasPendingWrites;
					return (
						!(!e.syncStateChanged && !t) &&
						!0 === this.options.includeMetadataChanges
					);
				}
				oa(e) {
					(e = ar.fromInitialDocuments(
						e.query,
						e.docs,
						e.mutatedKeys,
						e.fromCache,
						e.hasCachedResults,
					)),
						(this.na = !0),
						this.ta.next(e);
				}
				J_() {
					return this.options.source !== s.Cache;
				}
			}
			class af {
				constructor(e, t) {
					(this.aa = e), (this.byteLength = t);
				}
				ua() {
					return "metadata" in this.aa;
				}
			}
			class am {
				constructor(e) {
					this.serializer = e;
				}
				Es(e) {
					return rc(this.serializer, e);
				}
				ds(e) {
					return e.metadata.exists
						? ry(this.serializer, e.document, !1)
						: tS.newNoDocument(
								this.Es(e.metadata.name),
								this.As(e.metadata.readTime),
							);
				}
				As(e) {
					return ra(e);
				}
			}
			class ag {
				constructor(e) {
					this.key = e;
				}
			}
			class ap {
				constructor(e) {
					this.key = e;
				}
			}
			class ay {
				constructor(e, t) {
					(this.query = e),
						(this.Ta = t),
						(this.Ea = null),
						(this.hasCachedResults = !1),
						(this.current = !1),
						(this.da = nf()),
						(this.mutatedKeys = nf()),
						(this.Aa = ni(e)),
						(this.Ra = new at(this.Aa));
				}
				get Va() {
					return this.Ta;
				}
				ma(e, t) {
					let n = t ? t.fa : new an(),
						r = t ? t.Ra : this.Ra,
						i = t ? t.mutatedKeys : this.mutatedKeys,
						s = r,
						a = !1,
						o =
							"F" === this.query.limitType && r.size === this.query.limit
								? r.last()
								: null,
						l =
							"L" === this.query.limitType && r.size === this.query.limit
								? r.first()
								: null;
					if (
						(e.inorderTraversal((e, t) => {
							let u = r.get(e),
								h = nn(this.query, t) ? t : null,
								c = !!u && this.mutatedKeys.has(u.key),
								d =
									!!h &&
									(h.hasLocalMutations ||
										(this.mutatedKeys.has(h.key) && h.hasCommittedMutations)),
								f = !1;
							u && h
								? u.data.isEqual(h.data)
									? c !== d && (n.track({ type: 3, doc: h }), (f = !0))
									: this.ga(u, h) ||
										(n.track({ type: 2, doc: h }),
										(f = !0),
										((o && this.Aa(h, o) > 0) || (l && 0 > this.Aa(h, l))) &&
											(a = !0))
								: !u && h
									? (n.track({ type: 0, doc: h }), (f = !0))
									: u &&
										!h &&
										(n.track({ type: 1, doc: u }),
										(f = !0),
										(o || l) && (a = !0)),
								f &&
									(h
										? ((s = s.add(h)), (i = d ? i.add(e) : i.delete(e)))
										: ((s = s.delete(e)), (i = i.delete(e))));
						}),
						null !== this.query.limit)
					)
						while (s.size > this.query.limit) {
							const e = "F" === this.query.limitType ? s.last() : s.first();
							(s = s.delete(e.key)),
								(i = i.delete(e.key)),
								n.track({ type: 1, doc: e });
						}
					return { Ra: s, fa: n, ns: a, mutatedKeys: i };
				}
				ga(e, t) {
					return (
						e.hasLocalMutations &&
						t.hasCommittedMutations &&
						!t.hasLocalMutations
					);
				}
				applyChanges(e, t, n, r) {
					const i = this.Ra;
					(this.Ra = e.Ra), (this.mutatedKeys = e.mutatedKeys);
					const s = e.fa.G_();
					s.sort(
						(e, t) =>
							((e, t) => {
								const n = (e) => {
									switch (e) {
										case 0:
											return 1;
										case 2:
										case 3:
											return 2;
										case 1:
											return 0;
										default:
											return _();
									}
								};
								return n(e) - n(t);
							})(e.type, t.type) || this.Aa(e.doc, t.doc),
					),
						this.pa(n),
						(r = null != r && r);
					const a = t && !r ? this.ya() : [],
						o = 0 === this.da.size && this.current && !r ? 1 : 0,
						l = o !== this.Ea;
					return ((this.Ea = o), 0 !== s.length || l)
						? {
								snapshot: new ar(
									this.query,
									e.Ra,
									i,
									s,
									e.mutatedKeys,
									0 === o,
									l,
									!1,
									!!n && n.resumeToken.approximateByteSize() > 0,
								),
								wa: a,
							}
						: { wa: a };
				}
				Z_(e) {
					return this.current && "Offline" === e
						? ((this.current = !1),
							this.applyChanges(
								{
									Ra: this.Ra,
									fa: new an(),
									mutatedKeys: this.mutatedKeys,
									ns: !1,
								},
								!1,
							))
						: { wa: [] };
				}
				Sa(e) {
					return (
						!this.Ta.has(e) &&
						!!this.Ra.has(e) &&
						!this.Ra.get(e).hasLocalMutations
					);
				}
				pa(e) {
					e &&
						(e.addedDocuments.forEach((e) => (this.Ta = this.Ta.add(e))),
						e.modifiedDocuments.forEach((e) => {}),
						e.removedDocuments.forEach((e) => (this.Ta = this.Ta.delete(e))),
						(this.current = e.current));
				}
				ya() {
					if (!this.current) return [];
					const e = this.da;
					(this.da = nf()),
						this.Ra.forEach((e) => {
							this.Sa(e.key) && (this.da = this.da.add(e.key));
						});
					const t = [];
					return (
						e.forEach((e) => {
							this.da.has(e) || t.push(new ap(e));
						}),
						this.da.forEach((n) => {
							e.has(n) || t.push(new ag(n));
						}),
						t
					);
				}
				ba(e) {
					(this.Ta = e.Ts), (this.da = nf());
					const t = this.ma(e.documents);
					return this.applyChanges(t, !0);
				}
				Da() {
					return ar.fromInitialDocuments(
						this.query,
						this.Ra,
						this.mutatedKeys,
						0 === this.Ea,
						this.hasCachedResults,
					);
				}
			}
			class aw {
				constructor(e, t, n) {
					(this.query = e), (this.targetId = t), (this.view = n);
				}
			}
			class av {
				constructor(e) {
					(this.key = e), (this.va = !1);
				}
			}
			class aI {
				constructor(e, t, n, r, i, s) {
					(this.localStore = e),
						(this.remoteStore = t),
						(this.eventManager = n),
						(this.sharedClientState = r),
						(this.currentUser = i),
						(this.maxConcurrentLimboResolutions = s),
						(this.Ca = {}),
						(this.Fa = new ns((e) => ne(e), t7)),
						(this.Ma = new Map()),
						(this.xa = new Set()),
						(this.Oa = new eJ($.comparator)),
						(this.Na = new Map()),
						(this.La = new iB()),
						(this.Ba = {}),
						(this.ka = new Map()),
						(this.qa = iy.kn()),
						(this.onlineState = "Unknown"),
						(this.Qa = void 0);
				}
				get isPrimaryClient() {
					return !0 === this.Qa;
				}
			}
			async function aT(e, t, n = !0) {
				let r;
				const i = aY(e),
					s = i.Fa.get(t);
				return (
					s
						? (i.sharedClientState.addLocalQueryTarget(s.targetId),
							(r = s.view.Da()))
						: (r = await ab(i, t, n, !0)),
					r
				);
			}
			async function aE(e, t) {
				const n = aY(e);
				await ab(n, t, !0, !1);
			}
			async function ab(e, t, n, r) {
				let i;
				const s = await st(e.localStore, t8(t)),
					a = s.targetId,
					o = e.sharedClientState.addLocalQueryTarget(a, n);
				return (
					r && (i = await a_(e, t, a, "current" === o, s.resumeToken)),
					e.isPrimaryClient && n && sU(e.remoteStore, s),
					i
				);
			}
			async function a_(e, t, n, r, i) {
				e.Ka = (t, n, r) =>
					(async (e, t, n, r) => {
						let i = t.view.ma(n);
						i.ns &&
							(i = await sr(e.localStore, t.query, !1).then(
								({ documents: e }) => t.view.ma(e, i),
							));
						const s = r && r.targetChanges.get(t.targetId),
							a = r && null != r.targetMismatches.get(t.targetId),
							o = t.view.applyChanges(i, e.isPrimaryClient, s, a);
						return aL(e, t.targetId, o.wa), o.snapshot;
					})(e, t, n, r);
				const s = await sr(e.localStore, t, !0),
					a = new ay(t, s.Ts),
					o = a.ma(s.documents),
					l = n1.createSynthesizedTargetChangeForCurrentChange(
						n,
						r && "Offline" !== e.onlineState,
						i,
					),
					u = a.applyChanges(o, e.isPrimaryClient, l);
				aL(e, n, u.wa);
				const h = new aw(t, n, a);
				return (
					e.Fa.set(t, h),
					e.Ma.has(n) ? e.Ma.get(n).push(t) : e.Ma.set(n, [t]),
					u.snapshot
				);
			}
			async function aS(e, t, n) {
				const r = e.Fa.get(t),
					i = e.Ma.get(r.targetId);
				if (i.length > 1)
					return (
						e.Ma.set(
							r.targetId,
							i.filter((e) => !t7(e, t)),
						),
						void e.Fa.delete(t)
					);
				e.isPrimaryClient
					? (e.sharedClientState.removeLocalQueryTarget(r.targetId),
						e.sharedClientState.isActiveQueryTarget(r.targetId) ||
							(await sn(e.localStore, r.targetId, !1)
								.then(() => {
									e.sharedClientState.clearQueryState(r.targetId),
										n && sq(e.remoteStore, r.targetId),
										aM(e, r.targetId);
								})
								.catch(er)))
					: (aM(e, r.targetId), await sn(e.localStore, r.targetId, !0));
			}
			async function ax(e, t) {
				const n = e.Fa.get(t),
					r = e.Ma.get(n.targetId);
				e.isPrimaryClient &&
					1 === r.length &&
					(e.sharedClientState.removeLocalQueryTarget(n.targetId),
					sq(e.remoteStore, n.targetId));
			}
			async function aD(e, t, n) {
				const r = aX(e);
				try {
					var i;
					let e;
					const s = await ((e, t) => {
						let n, r;
						const i = U.now(),
							s = t.reduce((e, t) => e.add(t.key), nf());
						return e.persistence
							.runTransaction("Locally write mutations", "readwrite", (a) => {
								let o = na,
									l = nf();
								return e.cs
									.getEntries(a, s)
									.next((e) => {
										(o = e).forEach((e, t) => {
											t.isValidDocument() || (l = l.add(e));
										});
									})
									.next(() => e.localDocuments.getOverlayedDocuments(a, o))
									.next((r) => {
										n = r;
										const s = [];
										for (const e of t) {
											const t = ((e, t) => {
												let n = null;
												for (const r of e.fieldTransforms) {
													const e = t.data.field(r.field),
														i = nv(r.transform, e || null);
													null != i &&
														(null === n && (n = t_.empty()), n.set(r.field, i));
												}
												return n || null;
											})(e, n.get(e.key).overlayedDocument);
											null != t &&
												s.push(
													new nL(
														e.key,
														t,
														(function e(t) {
															const n = [];
															return (
																ej(t.fields, (t, r) => {
																	const i = new G([t]);
																	if (ty(r)) {
																		const t = e(r.mapValue).fields;
																		if (0 === t.length) n.push(i);
																		else for (const e of t) n.push(i.child(e));
																	} else n.push(i);
																}),
																new e1(n)
															);
														})(t.value.mapValue),
														nk.exists(!0),
													),
												);
										}
										return e.mutationQueue.addMutationBatch(a, i, s, t);
									})
									.next((t) => {
										r = t;
										const i = t.applyToLocalDocumentSet(n, l);
										return e.documentOverlayCache.saveOverlays(a, t.batchId, i);
									});
							})
							.then(() => ({ batchId: r.batchId, changes: nu(n) }));
					})(r.localStore, t);
					r.sharedClientState.addPendingMutation(s.batchId),
						(i = s.batchId),
						(e = r.Ba[r.currentUser.toKey()]) || (e = new eJ(L)),
						(e = e.insert(i, n)),
						(r.Ba[r.currentUser.toKey()] = e),
						await aU(r, s.changes),
						await sX(r.remoteStore);
				} catch (t) {
					const e = ae(t, "Failed to persist write");
					n.reject(e);
				}
			}
			async function aC(e, t) {
				try {
					const n = await ((e, t) => {
						let n = t.snapshotVersion,
							r = e.os;
						return e.persistence
							.runTransaction(
								"Apply remote event",
								"readwrite-primary",
								(i) => {
									const s = e.cs.newChangeBuffer({ trackRemovals: !0 });
									r = e.os;
									const a = [];
									t.targetChanges.forEach((s, o) => {
										var l;
										const u = r.get(o);
										if (!u) return;
										a.push(
											e.Ur.removeMatchingKeys(i, s.removedDocuments, o).next(
												() => e.Ur.addMatchingKeys(i, s.addedDocuments, o),
											),
										);
										let h = u.withSequenceNumber(i.currentSequenceNumber);
										null !== t.targetMismatches.get(o)
											? (h = h
													.withResumeToken(e5.EMPTY_BYTE_STRING, q.min())
													.withLastLimboFreeSnapshotVersion(q.min()))
											: s.resumeToken.approximateByteSize() > 0 &&
												(h = h.withResumeToken(s.resumeToken, n)),
											(r = r.insert(o, h)),
											(l = h),
											(0 === u.resumeToken.approximateByteSize() ||
												l.snapshotVersion.toMicroseconds() -
													u.snapshotVersion.toMicroseconds() >=
													3e8 ||
												s.addedDocuments.size +
													s.modifiedDocuments.size +
													s.removedDocuments.size >
													0) &&
												a.push(e.Ur.updateTargetData(i, h));
									});
									let o = na,
										l = nf();
									if (
										(t.documentUpdates.forEach((n) => {
											t.resolvedLimboDocuments.has(n) &&
												a.push(
													e.persistence.referenceDelegate.updateLimboDocument(
														i,
														n,
													),
												);
										}),
										a.push(
											se(i, s, t.documentUpdates).next((e) => {
												(o = e.Ps), (l = e.Is);
											}),
										),
										!n.isEqual(q.min()))
									) {
										const t = e.Ur.getLastRemoteSnapshotVersion(i).next((t) =>
											e.Ur.setTargetsMetadata(i, i.currentSequenceNumber, n),
										);
										a.push(t);
									}
									return ei
										.waitFor(a)
										.next(() => s.apply(i))
										.next(() =>
											e.localDocuments.getLocalViewOfDocuments(i, o, l),
										)
										.next(() => o);
								},
							)
							.then((t) => ((e.os = r), t));
					})(e.localStore, t);
					t.targetChanges.forEach((t, n) => {
						const r = e.Na.get(n);
						r &&
							(t.addedDocuments.size +
								t.modifiedDocuments.size +
								t.removedDocuments.size <=
								1 || _(),
							t.addedDocuments.size > 0
								? (r.va = !0)
								: t.modifiedDocuments.size > 0
									? r.va || _()
									: t.removedDocuments.size > 0 && (r.va || _(), (r.va = !1)));
					}),
						await aU(e, n, t);
				} catch (e) {
					await er(e);
				}
			}
			function aN(e, t, n) {
				var r;
				if ((e.isPrimaryClient && 0 === n) || (!e.isPrimaryClient && 1 === n)) {
					let n;
					const i = [];
					e.Fa.forEach((e, n) => {
						const r = n.view.Z_(t);
						r.snapshot && i.push(r.snapshot);
					}),
						((r = e.eventManager).onlineState = t),
						(n = !1),
						r.queries.forEach((e, r) => {
							for (const e of r.j_) e.Z_(t) && (n = !0);
						}),
						n && ac(r),
						i.length && e.Ca.d_(i),
						(e.onlineState = t),
						e.isPrimaryClient && e.sharedClientState.setOnlineState(t);
				}
			}
			async function ak(e, t, n) {
				e.sharedClientState.updateQueryState(t, "rejected", n);
				const r = e.Na.get(t),
					i = r && r.key;
				if (i) {
					let n = new eJ($.comparator);
					n = n.insert(i, tS.newNoDocument(i, q.min()));
					const r = nf().add(i),
						s = new n0(q.min(), new Map(), new eJ(L), n, r);
					await aC(e, s), (e.Oa = e.Oa.remove(i)), e.Na.delete(t), aP(e);
				} else
					await sn(e.localStore, t, !1)
						.then(() => aM(e, t, n))
						.catch(er);
			}
			async function aA(e, t) {
				var n;
				const r = t.batch.batchId;
				try {
					const i = await (n = e.localStore).persistence.runTransaction(
						"Acknowledge batch",
						"readwrite-primary",
						(e) => {
							const r = t.batch.keys(),
								i = n.cs.newChangeBuffer({ trackRemovals: !0 });
							return ((e, t, n, r) => {
								let i = n.batch,
									s = i.keys(),
									a = ei.resolve();
								return (
									s.forEach((e) => {
										a = a
											.next(() => r.getEntry(t, e))
											.next((t) => {
												const s = n.docVersions.get(e);
												null !== s || _(),
													0 > t.version.compareTo(s) &&
														(i.applyToRemoteDocument(t, n),
														t.isValidDocument() &&
															(t.setReadTime(n.commitVersion), r.addEntry(t)));
											});
									}),
									a.next(() => e.mutationQueue.removeMutationBatch(t, i))
								);
							})(n, e, t, i)
								.next(() => i.apply(e))
								.next(() => n.mutationQueue.performConsistencyCheck(e))
								.next(() =>
									n.documentOverlayCache.removeOverlaysForBatchId(
										e,
										r,
										t.batch.batchId,
									),
								)
								.next(() =>
									n.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(
										e,
										((e) => {
											let t = nf();
											for (let n = 0; n < e.mutationResults.length; ++n)
												e.mutationResults[n].transformResults.length > 0 &&
													(t = t.add(e.batch.mutations[n].key));
											return t;
										})(t),
									),
								)
								.next(() => n.localDocuments.getDocuments(e, r));
						},
					);
					aO(e, r, null),
						aR(e, r),
						e.sharedClientState.updateMutationState(r, "acknowledged"),
						await aU(e, i);
				} catch (e) {
					await er(e);
				}
			}
			async function aV(e, t, n) {
				var r;
				try {
					const i = await (r = e.localStore).persistence.runTransaction(
						"Reject batch",
						"readwrite-primary",
						(e) => {
							let n;
							return r.mutationQueue
								.lookupMutationBatch(e, t)
								.next(
									(t) => (
										null !== t || _(),
										(n = t.keys()),
										r.mutationQueue.removeMutationBatch(e, t)
									),
								)
								.next(() => r.mutationQueue.performConsistencyCheck(e))
								.next(() =>
									r.documentOverlayCache.removeOverlaysForBatchId(e, n, t),
								)
								.next(() =>
									r.localDocuments.recalculateAndSaveOverlaysForDocumentKeys(
										e,
										n,
									),
								)
								.next(() => r.localDocuments.getDocuments(e, n));
						},
					);
					aO(e, t, n),
						aR(e, t),
						e.sharedClientState.updateMutationState(t, "rejected", n),
						await aU(e, i);
				} catch (e) {
					await er(e);
				}
			}
			function aR(e, t) {
				(e.ka.get(t) || []).forEach((e) => {
					e.resolve();
				}),
					e.ka.delete(t);
			}
			function aO(e, t, n) {
				let r = e.Ba[e.currentUser.toKey()];
				if (r) {
					const i = r.get(t);
					i && (n ? i.reject(n) : i.resolve(), (r = r.remove(t))),
						(e.Ba[e.currentUser.toKey()] = r);
				}
			}
			function aM(e, t, n = null) {
				for (const r of (e.sharedClientState.removeLocalQueryTarget(t),
				e.Ma.get(t)))
					e.Fa.delete(r), n && e.Ca.$a(r, n);
				e.Ma.delete(t),
					e.isPrimaryClient &&
						e.La.gr(t).forEach((t) => {
							e.La.containsKey(t) || aF(e, t);
						});
			}
			function aF(e, t) {
				e.xa.delete(t.path.canonicalString());
				const n = e.Oa.get(t);
				null !== n &&
					(sq(e.remoteStore, n),
					(e.Oa = e.Oa.remove(t)),
					e.Na.delete(n),
					aP(e));
			}
			function aL(e, t, n) {
				for (const r of n)
					r instanceof ag
						? (e.La.addReference(r.key, t),
							((e, t) => {
								const n = t.key,
									r = n.path.canonicalString();
								e.Oa.get(n) ||
									e.xa.has(r) ||
									(I("SyncEngine", "New document in limbo: " + n),
									e.xa.add(r),
									aP(e));
							})(e, r))
						: r instanceof ap
							? (I("SyncEngine", "Document no longer in limbo: " + r.key),
								e.La.removeReference(r.key, t),
								e.La.containsKey(r.key) || aF(e, r.key))
							: _();
			}
			function aP(e) {
				while (e.xa.size > 0 && e.Oa.size < e.maxConcurrentLimboResolutions) {
					const t = e.xa.values().next().value;
					e.xa.delete(t);
					const n = new $(K.fromString(t)),
						r = e.qa.next();
					e.Na.set(r, new av(n)),
						(e.Oa = e.Oa.insert(n, r)),
						sU(
							e.remoteStore,
							new rx(t8(t1(n.path)), r, "TargetPurposeLimboResolution", ey.oe),
						);
				}
			}
			async function aU(e, t, n) {
				const r = [],
					i = [],
					s = [];
				e.Fa.isEmpty() ||
					(e.Fa.forEach((a, o) => {
						s.push(
							e.Ka(o, t, n).then((t) => {
								var s;
								if ((t || n) && e.isPrimaryClient) {
									const r = t
										? !t.fromCache
										: null ===
													(s =
														null == n
															? void 0
															: n.targetChanges.get(o.targetId)) || void 0 === s
											? void 0
											: s.current;
									e.sharedClientState.updateQueryState(
										o.targetId,
										r ? "current" : "not-current",
									);
								}
								if (t) {
									r.push(t);
									const e = i3.Wi(o.targetId, t);
									i.push(e);
								}
							}),
						);
					}),
					await Promise.all(s),
					e.Ca.d_(r),
					await (async (e, t) => {
						try {
							await e.persistence.runTransaction(
								"notifyLocalViewChanges",
								"readwrite",
								(n) =>
									ei.forEach(t, (t) =>
										ei
											.forEach(t.$i, (r) =>
												e.persistence.referenceDelegate.addReference(
													n,
													t.targetId,
													r,
												),
											)
											.next(() =>
												ei.forEach(t.Ui, (r) =>
													e.persistence.referenceDelegate.removeReference(
														n,
														t.targetId,
														r,
													),
												),
											),
									),
							);
						} catch (e) {
							if (!eh(e)) throw e;
							I("LocalStore", "Failed to update sequence numbers: " + e);
						}
						for (const n of t) {
							const t = n.targetId;
							if (!n.fromCache) {
								const n = e.os.get(t),
									r = n.snapshotVersion,
									i = n.withLastLimboFreeSnapshotVersion(r);
								e.os = e.os.insert(t, i);
							}
						}
					})(e.localStore, i));
			}
			async function aq(e, t) {
				if (!e.currentUser.isEqual(t)) {
					I("SyncEngine", "User change. New user:", t.toKey());
					const n = await i9(e.localStore, t);
					(e.currentUser = t),
						e.ka.forEach((e) => {
							e.forEach((e) => {
								e.reject(
									new x(
										S.CANCELLED,
										"'waitForPendingWrites' promise is rejected due to a user change.",
									),
								);
							});
						}),
						e.ka.clear(),
						e.sharedClientState.handleUserChange(
							t,
							n.removedBatchIds,
							n.addedBatchIds,
						),
						await aU(e, n.hs);
				}
			}
			function aB(e, t) {
				const n = e.Na.get(t);
				if (n && n.va) return nf().add(n.key);
				{
					let n = nf(),
						r = e.Ma.get(t);
					if (!r) return n;
					for (const t of r) {
						const r = e.Fa.get(t);
						n = n.unionWith(r.view.Va);
					}
					return n;
				}
			}
			async function aK(e, t) {
				const n = await sr(e.localStore, t.query, !0),
					r = t.view.ba(n);
				return e.isPrimaryClient && aL(e, t.targetId, r.wa), r;
			}
			async function az(e, t) {
				return ss(e.localStore, t).then((t) => aU(e, t));
			}
			async function aG(e, t, n, r) {
				const i = await ((e, t) => {
					const n = e.mutationQueue;
					return e.persistence.runTransaction(
						"Lookup mutation documents",
						"readonly",
						(r) =>
							n
								.Mn(r, t)
								.next((t) =>
									t ? e.localDocuments.getDocuments(r, t) : ei.resolve(null),
								),
					);
				})(e.localStore, t);
				null !== i
					? ("pending" === n
							? await sX(e.remoteStore)
							: "acknowledged" === n || "rejected" === n
								? (aO(e, t, r || null),
									aR(e, t),
									((e, t) => {
										e.mutationQueue.On(t);
									})(e.localStore, t))
								: _(),
						await aU(e, i))
					: I("SyncEngine", "Cannot apply mutation batch with id: " + t);
			}
			async function a$(e, t) {
				if ((aY(e), aX(e), !0 === t && !0 !== e.Qa)) {
					const t = e.sharedClientState.getAllActiveQueryTargets(),
						n = await aQ(e, t.toArray());
					for (const t of ((e.Qa = !0), await s4(e.remoteStore, !0), n))
						sU(e.remoteStore, t);
				} else if (!1 === t && !1 !== e.Qa) {
					let t = [],
						n = Promise.resolve();
					e.Ma.forEach((r, i) => {
						e.sharedClientState.isLocalQueryTarget(i)
							? t.push(i)
							: (n = n.then(() => (aM(e, i), sn(e.localStore, i, !0)))),
							sq(e.remoteStore, i);
					}),
						await n,
						await aQ(e, t),
						e.Na.forEach((t, n) => {
							sq(e.remoteStore, n);
						}),
						e.La.pr(),
						(e.Na = new Map()),
						(e.Oa = new eJ($.comparator)),
						(e.Qa = !1),
						await s4(e.remoteStore, !1);
				}
			}
			async function aQ(e, t, n) {
				const r = [],
					i = [];
				for (const n of t) {
					let t;
					const s = e.Ma.get(n);
					if (s && 0 !== s.length)
						for (const n of ((t = await st(e.localStore, t8(s[0]))), s)) {
							const t = e.Fa.get(n),
								r = await aK(e, t);
							r.snapshot && i.push(r.snapshot);
						}
					else {
						const r = await si(e.localStore, n);
						(t = await st(e.localStore, r)),
							await a_(e, aj(r), n, !1, t.resumeToken);
					}
					r.push(t);
				}
				return e.Ca.d_(i), r;
			}
			function aj(e) {
				var t, n, r, i, s;
				return (
					(t = e.path),
					(n = e.collectionGroup),
					(r = e.orderBy),
					(i = e.filters),
					(s = e.limit),
					new t0(t, n, r, i, s, "F", e.startAt, e.endAt)
				);
			}
			function aW(e) {
				return e.localStore.persistence.Qi();
			}
			async function aJ(e, t, n, r) {
				if (e.Qa)
					return void I(
						"SyncEngine",
						"Ignoring unexpected query state notification.",
					);
				const i = e.Ma.get(t);
				if (i && i.length > 0)
					switch (n) {
						case "current":
						case "not-current": {
							const r = await ss(e.localStore, nr(i[0])),
								s = n0.createSynthesizedRemoteEventForCurrentChange(
									t,
									"current" === n,
									e5.EMPTY_BYTE_STRING,
								);
							await aU(e, r, s);
							break;
						}
						case "rejected":
							await sn(e.localStore, t, !0), aM(e, t, r);
							break;
						default:
							_();
					}
			}
			async function aH(e, t, n) {
				const r = aY(e);
				if (r.Qa) {
					for (const e of t) {
						if (r.Ma.has(e) && r.sharedClientState.isActiveQueryTarget(e)) {
							I("SyncEngine", "Adding an already active target " + e);
							continue;
						}
						const t = await si(r.localStore, e),
							n = await st(r.localStore, t);
						await a_(r, aj(t), n.targetId, !1, n.resumeToken),
							sU(r.remoteStore, n);
					}
					for (const e of n)
						r.Ma.has(e) &&
							(await sn(r.localStore, e, !1)
								.then(() => {
									sq(r.remoteStore, e), aM(r, e);
								})
								.catch(er));
				}
			}
			function aY(e) {
				return (
					(e.remoteStore.remoteSyncer.applyRemoteEvent = aC.bind(null, e)),
					(e.remoteStore.remoteSyncer.getRemoteKeysForTarget = aB.bind(
						null,
						e,
					)),
					(e.remoteStore.remoteSyncer.rejectListen = ak.bind(null, e)),
					(e.Ca.d_ = au.bind(null, e.eventManager)),
					(e.Ca.$a = ah.bind(null, e.eventManager)),
					e
				);
			}
			function aX(e) {
				return (
					(e.remoteStore.remoteSyncer.applySuccessfulWrite = aA.bind(null, e)),
					(e.remoteStore.remoteSyncer.rejectFailedWrite = aV.bind(null, e)),
					e
				);
			}
			class aZ {
				constructor() {
					(this.kind = "memory"), (this.synchronizeTabs = !1);
				}
				async initialize(e) {
					(this.serializer = sN(e.databaseInfo.databaseId)),
						(this.sharedClientState = this.Wa(e)),
						(this.persistence = this.Ga(e)),
						await this.persistence.start(),
						(this.localStore = this.za(e)),
						(this.gcScheduler = this.ja(e, this.localStore)),
						(this.indexBackfillerScheduler = this.Ha(e, this.localStore));
				}
				ja(e, t) {
					return null;
				}
				Ha(e, t) {
					return null;
				}
				za(e) {
					var t, n;
					return (
						(t = this.persistence),
						(n = new i4()),
						new i6(t, n, e.initialUser, this.serializer)
					);
				}
				Ga(e) {
					return new ij(iJ.Zr, this.serializer);
				}
				Wa(e) {
					return new sw();
				}
				async terminate() {
					var e, t;
					null === (e = this.gcScheduler) || void 0 === e || e.stop(),
						null === (t = this.indexBackfillerScheduler) ||
							void 0 === t ||
							t.stop(),
						this.sharedClientState.shutdown(),
						await this.persistence.shutdown();
				}
			}
			aZ.provider = { build: () => new aZ() };
			class a0 extends aZ {
				constructor(e) {
					super(), (this.cacheSizeBytes = e);
				}
				ja(e, t) {
					return (
						this.persistence.referenceDelegate instanceof iH || _(),
						new i_(
							this.persistence.referenceDelegate.garbageCollector,
							e.asyncQueue,
							t,
						)
					);
				}
				Ga(e) {
					const t =
						void 0 !== this.cacheSizeBytes
							? il.withCacheSize(this.cacheSizeBytes)
							: il.DEFAULT;
					return new ij((e) => iH.Zr(e, t), this.serializer);
				}
			}
			class a1 extends aZ {
				constructor(e, t, n) {
					super(),
						(this.Ja = e),
						(this.cacheSizeBytes = t),
						(this.forceOwnership = n),
						(this.kind = "persistent"),
						(this.synchronizeTabs = !1);
				}
				async initialize(e) {
					await super.initialize(e),
						await this.Ja.initialize(this, e),
						await aX(this.Ja.syncEngine),
						await sX(this.Ja.remoteStore),
						await this.persistence.yi(
							() => (
								this.gcScheduler &&
									!this.gcScheduler.started &&
									this.gcScheduler.start(),
								this.indexBackfillerScheduler &&
									!this.indexBackfillerScheduler.started &&
									this.indexBackfillerScheduler.start(),
								Promise.resolve()
							),
						);
				}
				za(e) {
					var t, n;
					return (
						(t = this.persistence),
						(n = new i4()),
						new i6(t, n, e.initialUser, this.serializer)
					);
				}
				ja(e, t) {
					return new i_(
						this.persistence.referenceDelegate.garbageCollector,
						e.asyncQueue,
						t,
					);
				}
				Ha(e, t) {
					const n = new ep(t, this.persistence);
					return new eg(e.asyncQueue, n);
				}
				Ga(e) {
					const t = i5(
							e.databaseInfo.databaseId,
							e.databaseInfo.persistenceKey,
						),
						n =
							void 0 !== this.cacheSizeBytes
								? il.withCacheSize(this.cacheSizeBytes)
								: il.DEFAULT;
					return new i0(
						this.synchronizeTabs,
						t,
						e.clientId,
						n,
						e.asyncQueue,
						sD(),
						sC(),
						this.serializer,
						this.sharedClientState,
						!!this.forceOwnership,
					);
				}
				Wa(e) {
					return new sw();
				}
			}
			class a2 extends a1 {
				constructor(e, t) {
					super(e, t, !1),
						(this.Ja = e),
						(this.cacheSizeBytes = t),
						(this.synchronizeTabs = !0);
				}
				async initialize(e) {
					await super.initialize(e);
					const t = this.Ja.syncEngine;
					this.sharedClientState instanceof sy &&
						((this.sharedClientState.syncEngine = {
							no: aG.bind(null, t),
							ro: aJ.bind(null, t),
							io: aH.bind(null, t),
							Qi: aW.bind(null, t),
							eo: az.bind(null, t),
						}),
						await this.sharedClientState.start()),
						await this.persistence.yi(async (e) => {
							await a$(this.Ja.syncEngine, e),
								this.gcScheduler &&
									(e && !this.gcScheduler.started
										? this.gcScheduler.start()
										: e || this.gcScheduler.stop()),
								this.indexBackfillerScheduler &&
									(e && !this.indexBackfillerScheduler.started
										? this.indexBackfillerScheduler.start()
										: e || this.indexBackfillerScheduler.stop());
						});
				}
				Wa(e) {
					const t = sD();
					if (!sy.D(t))
						throw new x(
							S.UNIMPLEMENTED,
							"IndexedDB persistence is only available on platforms that support LocalStorage.",
						);
					const n = i5(
						e.databaseInfo.databaseId,
						e.databaseInfo.persistenceKey,
					);
					return new sy(t, e.asyncQueue, n, e.clientId, e.initialUser);
				}
			}
			class a5 {
				async initialize(e, t) {
					this.localStore ||
						((this.localStore = e.localStore),
						(this.sharedClientState = e.sharedClientState),
						(this.datastore = this.createDatastore(t)),
						(this.remoteStore = this.createRemoteStore(t)),
						(this.eventManager = this.createEventManager(t)),
						(this.syncEngine = this.createSyncEngine(t, !e.synchronizeTabs)),
						(this.sharedClientState.onlineStateHandler = (e) =>
							aN(this.syncEngine, e, 1)),
						(this.remoteStore.remoteSyncer.handleCredentialChange = aq.bind(
							null,
							this.syncEngine,
						)),
						await s4(this.remoteStore, this.syncEngine.isPrimaryClient));
				}
				createEventManager(e) {
					return new as();
				}
				createDatastore(e) {
					var t;
					const n = sN(e.databaseInfo.databaseId),
						r = new sx(e.databaseInfo);
					return (
						(t = e.authCredentials), new sO(t, e.appCheckCredentials, r, n)
					);
				}
				createRemoteStore(e) {
					var t, n;
					return (
						(t = this.localStore),
						(n = this.datastore),
						new sF(
							t,
							n,
							e.asyncQueue,
							(e) => aN(this.syncEngine, e, 0),
							sI.D() ? new sI() : new sv(),
						)
					);
				}
				createSyncEngine(e, t) {
					return ((e, t, n, r, i, s, a) => {
						const o = new aI(e, t, n, r, i, s);
						return a && (o.Qa = !0), o;
					})(
						this.localStore,
						this.remoteStore,
						this.eventManager,
						this.sharedClientState,
						e.initialUser,
						e.maxConcurrentLimboResolutions,
						t,
					);
				}
				async terminate() {
					var e, t;
					await (async (e) => {
						I("RemoteStore", "RemoteStore shutting down."),
							e.L_.add(5),
							await sP(e),
							e.k_.shutdown(),
							e.q_.set("Unknown");
					})(this.remoteStore),
						null === (e = this.datastore) || void 0 === e || e.terminate(),
						null === (t = this.eventManager) || void 0 === t || t.terminate();
				}
			}
			a5.provider = { build: () => new a5() };
			class a3 {
				constructor(e) {
					(this.observer = e), (this.muted = !1);
				}
				next(e) {
					this.muted || (this.observer.next && this.Ya(this.observer.next, e));
				}
				error(e) {
					this.muted ||
						(this.observer.error
							? this.Ya(this.observer.error, e)
							: T("Uncaught Error in snapshot listener:", e.toString()));
				}
				Za() {
					this.muted = !0;
				}
				Ya(e, t) {
					setTimeout(() => {
						this.muted || e(t);
					}, 0);
				}
			}
			class a8 {
				constructor(e) {
					(this.datastore = e),
						(this.readVersions = new Map()),
						(this.mutations = []),
						(this.committed = !1),
						(this.lastTransactionError = null),
						(this.writtenDocs = new Set());
				}
				async lookup(e) {
					if ((this.ensureCommitNotCalled(), this.mutations.length > 0))
						throw (
							((this.lastTransactionError = new x(
								S.INVALID_ARGUMENT,
								"Firestore transactions require all reads to be executed before all writes.",
							)),
							this.lastTransactionError)
						);
					const t = await (async (e, t) => {
						const n = { documents: t.map((t) => rh(e.serializer, t)) },
							r = await e.Lo(
								"BatchGetDocuments",
								e.serializer.databaseId,
								K.emptyPath(),
								n,
								t.length,
							),
							i = new Map();
						r.forEach((t) => {
							var n;
							const r =
								((n = e.serializer),
								"found" in t
									? ((e, t) => {
											t.found || _(), t.found.name, t.found.updateTime;
											const n = rc(e, t.found.name),
												r = ra(t.found.updateTime),
												i = t.found.createTime
													? ra(t.found.createTime)
													: q.min(),
												s = new t_({ mapValue: { fields: t.found.fields } });
											return tS.newFoundDocument(n, r, i, s);
										})(n, t)
									: "missing" in t
										? ((e, t) => {
												t.missing || _(), t.readTime || _();
												const n = rc(e, t.missing),
													r = ra(t.readTime);
												return tS.newNoDocument(n, r);
											})(n, t)
										: _());
							i.set(r.key.toString(), r);
						});
						const s = [];
						return (
							t.forEach((e) => {
								const t = i.get(e.toString());
								t || _(), s.push(t);
							}),
							s
						);
					})(this.datastore, e);
					return t.forEach((e) => this.recordVersion(e)), t;
				}
				set(e, t) {
					this.write(t.toMutation(e, this.precondition(e))),
						this.writtenDocs.add(e.toString());
				}
				update(e, t) {
					try {
						this.write(t.toMutation(e, this.preconditionForUpdate(e)));
					} catch (e) {
						this.lastTransactionError = e;
					}
					this.writtenDocs.add(e.toString());
				}
				delete(e) {
					this.write(new nB(e, this.precondition(e))),
						this.writtenDocs.add(e.toString());
				}
				async commit() {
					if ((this.ensureCommitNotCalled(), this.lastTransactionError))
						throw this.lastTransactionError;
					const e = this.readVersions;
					this.mutations.forEach((t) => {
						e.delete(t.key.toString());
					}),
						e.forEach((e, t) => {
							const n = $.fromPath(t);
							this.mutations.push(new nK(n, this.precondition(n)));
						}),
						await (async (e, t) => {
							const n = { writes: t.map((t) => rw(e.serializer, t)) };
							await e.Mo("Commit", e.serializer.databaseId, K.emptyPath(), n);
						})(this.datastore, this.mutations),
						(this.committed = !0);
				}
				recordVersion(e) {
					let t;
					if (e.isFoundDocument()) t = e.version;
					else {
						if (!e.isNoDocument()) throw _();
						t = q.min();
					}
					const n = this.readVersions.get(e.key.toString());
					if (n) {
						if (!t.isEqual(n))
							throw new x(
								S.ABORTED,
								"Document version changed between two reads.",
							);
					} else this.readVersions.set(e.key.toString(), t);
				}
				precondition(e) {
					const t = this.readVersions.get(e.toString());
					return !this.writtenDocs.has(e.toString()) && t
						? t.isEqual(q.min())
							? nk.exists(!1)
							: nk.updateTime(t)
						: nk.none();
				}
				preconditionForUpdate(e) {
					const t = this.readVersions.get(e.toString());
					if (!this.writtenDocs.has(e.toString()) && t) {
						if (t.isEqual(q.min()))
							throw new x(
								S.INVALID_ARGUMENT,
								"Can't update a document that doesn't exist.",
							);
						return nk.updateTime(t);
					}
					return nk.exists(!0);
				}
				write(e) {
					this.ensureCommitNotCalled(), this.mutations.push(e);
				}
				ensureCommitNotCalled() {}
			}
			class a4 {
				constructor(e, t, n, r, i) {
					(this.authCredentials = e),
						(this.appCheckCredentials = t),
						(this.asyncQueue = n),
						(this.databaseInfo = r),
						(this.user = p.UNAUTHENTICATED),
						(this.clientId = F.newId()),
						(this.authCredentialListener = () => Promise.resolve()),
						(this.appCheckCredentialListener = () => Promise.resolve()),
						(this._uninitializedComponentsProvider = i),
						this.authCredentials.start(n, async (e) => {
							I("FirestoreClient", "Received user=", e.uid),
								await this.authCredentialListener(e),
								(this.user = e);
						}),
						this.appCheckCredentials.start(
							n,
							(e) => (
								I("FirestoreClient", "Received new app check token=", e),
								this.appCheckCredentialListener(e, this.user)
							),
						);
				}
				get configuration() {
					return {
						asyncQueue: this.asyncQueue,
						databaseInfo: this.databaseInfo,
						clientId: this.clientId,
						authCredentials: this.authCredentials,
						appCheckCredentials: this.appCheckCredentials,
						initialUser: this.user,
						maxConcurrentLimboResolutions: 100,
					};
				}
				setCredentialChangeListener(e) {
					this.authCredentialListener = e;
				}
				setAppCheckTokenChangeListener(e) {
					this.appCheckCredentialListener = e;
				}
				terminate() {
					this.asyncQueue.enterRestrictedMode();
					const e = new D();
					return (
						this.asyncQueue.enqueueAndForgetEvenWhileRestricted(async () => {
							try {
								this._onlineComponents &&
									(await this._onlineComponents.terminate()),
									this._offlineComponents &&
										(await this._offlineComponents.terminate()),
									this.authCredentials.shutdown(),
									this.appCheckCredentials.shutdown(),
									e.resolve();
							} catch (n) {
								const t = ae(n, "Failed to shutdown persistence");
								e.reject(t);
							}
						}),
						e.promise
					);
				}
			}
			async function a6(e, t) {
				e.asyncQueue.verifyOperationInProgress(),
					I("FirestoreClient", "Initializing OfflineComponentProvider");
				const n = e.configuration;
				await t.initialize(n);
				let r = n.initialUser;
				e.setCredentialChangeListener(async (e) => {
					r.isEqual(e) || (await i9(t.localStore, e), (r = e));
				}),
					t.persistence.setDatabaseDeletedListener(() => e.terminate()),
					(e._offlineComponents = t);
			}
			async function a9(e, t) {
				e.asyncQueue.verifyOperationInProgress();
				const n = await a7(e);
				I("FirestoreClient", "Initializing OnlineComponentProvider"),
					await t.initialize(n, e.configuration),
					e.setCredentialChangeListener((e) => s8(t.remoteStore, e)),
					e.setAppCheckTokenChangeListener((e, n) => s8(t.remoteStore, n)),
					(e._onlineComponents = t);
			}
			async function a7(e) {
				if (!e._offlineComponents) {
					if (e._uninitializedComponentsProvider) {
						I(
							"FirestoreClient",
							"Using user provided OfflineComponentProvider",
						);
						try {
							await a6(e, e._uninitializedComponentsProvider._offline);
						} catch (t) {
							if (
								!("FirebaseError" === t.name
									? t.code === S.FAILED_PRECONDITION ||
										t.code === S.UNIMPLEMENTED
									: !(
											"undefined" != typeof DOMException &&
											t instanceof DOMException
										) ||
										22 === t.code ||
										20 === t.code ||
										11 === t.code)
							)
								throw t;
							E(
								"Error using user provided cache. Falling back to memory cache: " +
									t,
							),
								await a6(e, new aZ());
						}
					} else
						I("FirestoreClient", "Using default OfflineComponentProvider"),
							await a6(e, new aZ());
				}
				return e._offlineComponents;
			}
			async function oe(e) {
				return (
					e._onlineComponents ||
						(e._uninitializedComponentsProvider
							? (I(
									"FirestoreClient",
									"Using user provided OnlineComponentProvider",
								),
								await a9(e, e._uninitializedComponentsProvider._online))
							: (I("FirestoreClient", "Using default OnlineComponentProvider"),
								await a9(e, new a5()))),
					e._onlineComponents
				);
			}
			async function ot(e) {
				const t = await oe(e),
					n = t.eventManager;
				return (
					(n.onListen = aT.bind(null, t.syncEngine)),
					(n.onUnlisten = aS.bind(null, t.syncEngine)),
					(n.onFirstRemoteStoreListen = aE.bind(null, t.syncEngine)),
					(n.onLastRemoteStoreUnlisten = ax.bind(null, t.syncEngine)),
					n
				);
			}
			function on(e) {
				const t = {};
				return (
					void 0 !== e.timeoutSeconds && (t.timeoutSeconds = e.timeoutSeconds),
					t
				);
			}
			const or = new Map();
			function oi(e) {
				if (void 0 === e) return "undefined";
				if (null === e) return "null";
				if ("string" == typeof e)
					return (
						e.length > 20 && (e = `${e.substring(0, 20)}...`), JSON.stringify(e)
					);
				if ("number" == typeof e || "boolean" == typeof e) return "" + e;
				if ("object" == typeof e) {
					if (e instanceof Array) return "an array";
					{
						var t;
						const n = (t = e).constructor ? t.constructor.name : null;
						return n ? `a custom ${n} object` : "an object";
					}
				}
				return "function" == typeof e ? "a function" : _();
			}
			function os(e, t) {
				if (("_delegate" in e && (e = e._delegate), !(e instanceof t))) {
					if (t.name === e.constructor.name)
						throw new x(
							S.INVALID_ARGUMENT,
							"Type does not match the expected instance. Did you pass a reference from a different Firestore SDK?",
						);
					{
						const n = oi(e);
						throw new x(
							S.INVALID_ARGUMENT,
							`Expected type '${t.name}', but it was: ${n}`,
						);
					}
				}
				return e;
			}
			class oa {
				constructor(e) {
					var t, n;
					if (void 0 === e.host) {
						if (void 0 !== e.ssl)
							throw new x(
								S.INVALID_ARGUMENT,
								"Can't provide ssl option if host option is not set",
							);
						(this.host = "firestore.googleapis.com"), (this.ssl = !0);
					} else
						(this.host = e.host),
							(this.ssl = null === (t = e.ssl) || void 0 === t || t);
					if (
						((this.credentials = e.credentials),
						(this.ignoreUndefinedProperties = !!e.ignoreUndefinedProperties),
						(this.localCache = e.localCache),
						void 0 === e.cacheSizeBytes)
					)
						this.cacheSizeBytes = 0x2800000;
					else {
						if (-1 !== e.cacheSizeBytes && e.cacheSizeBytes < 1048576)
							throw new x(
								S.INVALID_ARGUMENT,
								"cacheSizeBytes must be at least 1048576",
							);
						this.cacheSizeBytes = e.cacheSizeBytes;
					}
					((e, t, n, r) => {
						if (!0 === t && !0 === r)
							throw new x(
								S.INVALID_ARGUMENT,
								`${e} and ${n} cannot be used together.`,
							);
					})(
						"experimentalForceLongPolling",
						e.experimentalForceLongPolling,
						"experimentalAutoDetectLongPolling",
						e.experimentalAutoDetectLongPolling,
					),
						(this.experimentalForceLongPolling =
							!!e.experimentalForceLongPolling),
						this.experimentalForceLongPolling
							? (this.experimentalAutoDetectLongPolling = !1)
							: void 0 === e.experimentalAutoDetectLongPolling
								? (this.experimentalAutoDetectLongPolling = !0)
								: (this.experimentalAutoDetectLongPolling =
										!!e.experimentalAutoDetectLongPolling),
						(this.experimentalLongPollingOptions = on(
							null !== (n = e.experimentalLongPollingOptions) && void 0 !== n
								? n
								: {},
						)),
						((e) => {
							if (void 0 !== e.timeoutSeconds) {
								if (isNaN(e.timeoutSeconds))
									throw new x(
										S.INVALID_ARGUMENT,
										`invalid long polling timeout: ${e.timeoutSeconds} (must not be NaN)`,
									);
								if (e.timeoutSeconds < 5)
									throw new x(
										S.INVALID_ARGUMENT,
										`invalid long polling timeout: ${e.timeoutSeconds} (minimum allowed value is 5)`,
									);
								if (e.timeoutSeconds > 30)
									throw new x(
										S.INVALID_ARGUMENT,
										`invalid long polling timeout: ${e.timeoutSeconds} (maximum allowed value is 30)`,
									);
							}
						})(this.experimentalLongPollingOptions),
						(this.useFetchStreams = !!e.useFetchStreams);
				}
				isEqual(e) {
					var t, n;
					return (
						this.host === e.host &&
						this.ssl === e.ssl &&
						this.credentials === e.credentials &&
						this.cacheSizeBytes === e.cacheSizeBytes &&
						this.experimentalForceLongPolling ===
							e.experimentalForceLongPolling &&
						this.experimentalAutoDetectLongPolling ===
							e.experimentalAutoDetectLongPolling &&
						((t = this.experimentalLongPollingOptions),
						(n = e.experimentalLongPollingOptions),
						t.timeoutSeconds === n.timeoutSeconds) &&
						this.ignoreUndefinedProperties === e.ignoreUndefinedProperties &&
						this.useFetchStreams === e.useFetchStreams
					);
				}
			}
			class oo {
				constructor(e, t, n, r) {
					(this._authCredentials = e),
						(this._appCheckCredentials = t),
						(this._databaseId = n),
						(this._app = r),
						(this.type = "firestore-lite"),
						(this._persistenceKey = "(lite)"),
						(this._settings = new oa({})),
						(this._settingsFrozen = !1),
						(this._terminateTask = "notTerminated");
				}
				get app() {
					if (!this._app)
						throw new x(
							S.FAILED_PRECONDITION,
							"Firestore was not initialized using the Firebase SDK. 'app' is not available",
						);
					return this._app;
				}
				get _initialized() {
					return this._settingsFrozen;
				}
				get _terminated() {
					return "notTerminated" !== this._terminateTask;
				}
				_setSettings(e) {
					if (this._settingsFrozen)
						throw new x(
							S.FAILED_PRECONDITION,
							"Firestore has already been started and its settings can no longer be changed. You can only modify settings before calling any other methods on a Firestore object.",
						);
					(this._settings = new oa(e)),
						void 0 !== e.credentials &&
							(this._authCredentials = ((e) => {
								if (!e) return new N();
								switch (e.type) {
									case "firstParty":
										return new R(
											e.sessionIndex || "0",
											e.iamToken || null,
											e.authTokenFactory || null,
										);
									case "provider":
										return e.client;
									default:
										throw new x(
											S.INVALID_ARGUMENT,
											"makeAuthCredentialsProvider failed due to invalid credential type",
										);
								}
							})(e.credentials));
				}
				_getSettings() {
					return this._settings;
				}
				_freezeSettings() {
					return (this._settingsFrozen = !0), this._settings;
				}
				_delete() {
					return (
						"notTerminated" === this._terminateTask &&
							(this._terminateTask = this._terminate()),
						this._terminateTask
					);
				}
				async _restart() {
					"notTerminated" === this._terminateTask
						? await this._terminate()
						: (this._terminateTask = "notTerminated");
				}
				toJSON() {
					return {
						app: this._app,
						databaseId: this._databaseId,
						settings: this._settings,
					};
				}
				_terminate() {
					return (
						((e) => {
							const t = or.get(e);
							t &&
								(I("ComponentProvider", "Removing Datastore"),
								or.delete(e),
								t.terminate());
						})(this),
						Promise.resolve()
					);
				}
			}
			class ol {
				constructor(e, t, n) {
					(this.converter = t),
						(this._query = n),
						(this.type = "query"),
						(this.firestore = e);
				}
				withConverter(e) {
					return new ol(this.firestore, e, this._query);
				}
			}
			class ou {
				constructor(e, t, n) {
					(this.converter = t),
						(this._key = n),
						(this.type = "document"),
						(this.firestore = e);
				}
				get _path() {
					return this._key.path;
				}
				get id() {
					return this._key.path.lastSegment();
				}
				get path() {
					return this._key.path.canonicalString();
				}
				get parent() {
					return new oh(
						this.firestore,
						this.converter,
						this._key.path.popLast(),
					);
				}
				withConverter(e) {
					return new ou(this.firestore, e, this._key);
				}
			}
			class oh extends ol {
				constructor(e, t, n) {
					super(e, t, t1(n)), (this._path = n), (this.type = "collection");
				}
				get id() {
					return this._query.path.lastSegment();
				}
				get path() {
					return this._query.path.canonicalString();
				}
				get parent() {
					const e = this._path.popLast();
					return e.isEmpty() ? null : new ou(this.firestore, null, new $(e));
				}
				withConverter(e) {
					return new oh(this.firestore, e, this._path);
				}
			}
			class oc {
				constructor(e = Promise.resolve()) {
					(this.Pu = []),
						(this.Iu = !1),
						(this.Tu = []),
						(this.Eu = null),
						(this.du = !1),
						(this.Au = !1),
						(this.Ru = []),
						(this.t_ = new sk(this, "async_queue_retry")),
						(this.Vu = () => {
							const e = sC();
							e &&
								I(
									"AsyncQueue",
									"Visibility state changed to " + e.visibilityState,
								),
								this.t_.jo();
						}),
						(this.mu = e);
					const t = sC();
					t &&
						"function" == typeof t.addEventListener &&
						t.addEventListener("visibilitychange", this.Vu);
				}
				get isShuttingDown() {
					return this.Iu;
				}
				enqueueAndForget(e) {
					this.enqueue(e);
				}
				enqueueAndForgetEvenWhileRestricted(e) {
					this.fu(), this.gu(e);
				}
				enterRestrictedMode(e) {
					if (!this.Iu) {
						(this.Iu = !0), (this.Au = e || !1);
						const t = sC();
						t &&
							"function" == typeof t.removeEventListener &&
							t.removeEventListener("visibilitychange", this.Vu);
					}
				}
				enqueue(e) {
					if ((this.fu(), this.Iu)) return new Promise(() => {});
					const t = new D();
					return this.gu(() =>
						this.Iu && this.Au
							? Promise.resolve()
							: (e().then(t.resolve, t.reject), t.promise),
					).then(() => t.promise);
				}
				enqueueRetryable(e) {
					this.enqueueAndForget(() => (this.Pu.push(e), this.pu()));
				}
				async pu() {
					if (0 !== this.Pu.length) {
						try {
							await this.Pu[0](), this.Pu.shift(), this.t_.reset();
						} catch (e) {
							if (!eh(e)) throw e;
							I("AsyncQueue", "Operation failed with retryable error: " + e);
						}
						this.Pu.length > 0 && this.t_.Go(() => this.pu());
					}
				}
				gu(e) {
					const t = this.mu.then(
						() => (
							(this.du = !0),
							e()
								.catch((e) => {
									let t;
									throw (
										((this.Eu = e),
										(this.du = !1),
										T(
											"INTERNAL UNHANDLED ERROR: ",
											((t = e.message || ""),
											e.stack &&
												(t = e.stack.includes(e.message)
													? e.stack
													: e.message + "\n" + e.stack),
											t),
										),
										e)
									);
								})
								.then((e) => ((this.du = !1), e))
						),
					);
					return (this.mu = t), t;
				}
				enqueueAfterDelay(e, t, n) {
					this.fu(), this.Ru.indexOf(e) > -1 && (t = 0);
					const r = s7.createAndSchedule(this, e, t, n, (e) => this.yu(e));
					return this.Tu.push(r), r;
				}
				fu() {
					this.Eu && _();
				}
				verifyOperationInProgress() {}
				async wu() {
					let e;
					do (e = this.mu), await e;
					while (e !== this.mu);
				}
				Su(e) {
					for (const t of this.Tu) if (t.timerId === e) return !0;
					return !1;
				}
				bu(e) {
					return this.wu().then(() => {
						for (const t of (this.Tu.sort(
							(e, t) => e.targetTimeMs - t.targetTimeMs,
						),
						this.Tu))
							if ((t.skipDelay(), "all" !== e && t.timerId === e)) break;
						return this.wu();
					});
				}
				Du(e) {
					this.Ru.push(e);
				}
				yu(e) {
					const t = this.Tu.indexOf(e);
					this.Tu.splice(t, 1);
				}
			}
			class od extends oo {
				constructor(e, t, n, r) {
					super(e, t, n, r),
						(this.type = "firestore"),
						(this._queue = new oc()),
						(this._persistenceKey =
							(null == r ? void 0 : r.name) || "[DEFAULT]");
				}
				async _terminate() {
					if (this._firestoreClient) {
						const e = this._firestoreClient.terminate();
						(this._queue = new oc(e)),
							(this._firestoreClient = void 0),
							await e;
					}
				}
			}
			function of(e, t) {
				const n = "object" == typeof e ? e : (0, o.Sx)(),
					r = (0, o.j6)(n, "firestore").getImmediate({
						identifier: "string" == typeof e ? e : t || "(default)",
					});
				if (!r._initialized) {
					const e = (0, h.yU)("firestore");
					e &&
						((e, t, n, r = {}) => {
							var i;
							const s = (e = os(e, oo))._getSettings(),
								a = `${t}:${n}`;
							if (
								("firestore.googleapis.com" !== s.host &&
									s.host !== a &&
									E(
										"Host has been set in both settings() and connectFirestoreEmulator(), emulator host will be used.",
									),
								e._setSettings(
									Object.assign(Object.assign({}, s), { host: a, ssl: !1 }),
								),
								r.mockUserToken)
							) {
								let t, n;
								if ("string" == typeof r.mockUserToken)
									(t = r.mockUserToken), (n = p.MOCK_USER);
								else {
									t = (0, h.Fy)(
										r.mockUserToken,
										null === (i = e._app) || void 0 === i
											? void 0
											: i.options.projectId,
									);
									const s = r.mockUserToken.sub || r.mockUserToken.user_id;
									if (!s)
										throw new x(
											S.INVALID_ARGUMENT,
											"mockUserToken must contain 'sub' or 'user_id' field!",
										);
									n = new p(s);
								}
								e._authCredentials = new k(new C(t, n));
							}
						})(r, ...e);
				}
				return r;
			}
			function om(e) {
				if (e._terminated)
					throw new x(
						S.FAILED_PRECONDITION,
						"The client has already been terminated.",
					);
				return e._firestoreClient || og(e), e._firestoreClient;
			}
			function og(e) {
				var t, n, r, i, s;
				const a = e._freezeSettings(),
					o =
						((i = e._databaseId),
						(s =
							(null === (t = e._app) || void 0 === t
								? void 0
								: t.options.appId) || ""),
						new tt(
							i,
							s,
							e._persistenceKey,
							a.host,
							a.ssl,
							a.experimentalForceLongPolling,
							a.experimentalAutoDetectLongPolling,
							on(a.experimentalLongPollingOptions),
							a.useFetchStreams,
						));
				e._componentsProvider ||
					((null === (n = a.localCache) || void 0 === n
						? void 0
						: n._offlineComponentProvider) &&
						(null === (r = a.localCache) || void 0 === r
							? void 0
							: r._onlineComponentProvider) &&
						(e._componentsProvider = {
							_offline: a.localCache._offlineComponentProvider,
							_online: a.localCache._onlineComponentProvider,
						})),
					(e._firestoreClient = new a4(
						e._authCredentials,
						e._appCheckCredentials,
						e._queue,
						o,
						e._componentsProvider &&
							((e) => {
								const t = null == e ? void 0 : e._online.build();
								return {
									_offline: null == e ? void 0 : e._offline.build(t),
									_online: t,
								};
							})(e._componentsProvider),
					));
			}
			class op {
				constructor(e) {
					this._byteString = e;
				}
				static fromBase64String(e) {
					try {
						return new op(e5.fromBase64String(e));
					} catch (e) {
						throw new x(
							S.INVALID_ARGUMENT,
							"Failed to construct data from Base64 string: " + e,
						);
					}
				}
				static fromUint8Array(e) {
					return new op(e5.fromUint8Array(e));
				}
				toBase64() {
					return this._byteString.toBase64();
				}
				toUint8Array() {
					return this._byteString.toUint8Array();
				}
				toString() {
					return "Bytes(base64: " + this.toBase64() + ")";
				}
				isEqual(e) {
					return this._byteString.isEqual(e._byteString);
				}
			}
			class oy {
				constructor(...e) {
					for (let t = 0; t < e.length; ++t)
						if (0 === e[t].length)
							throw new x(
								S.INVALID_ARGUMENT,
								"Invalid field name at argument $(i + 1). Field names must not be empty.",
							);
					this._internalPath = new G(e);
				}
				isEqual(e) {
					return this._internalPath.isEqual(e._internalPath);
				}
			}
			class ow {
				constructor(e) {
					this._methodName = e;
				}
			}
			class ov {
				constructor(e, t) {
					if (!isFinite(e) || e < -90 || e > 90)
						throw new x(
							S.INVALID_ARGUMENT,
							"Latitude must be a number between -90 and 90, but was: " + e,
						);
					if (!isFinite(t) || t < -180 || t > 180)
						throw new x(
							S.INVALID_ARGUMENT,
							"Longitude must be a number between -180 and 180, but was: " + t,
						);
					(this._lat = e), (this._long = t);
				}
				get latitude() {
					return this._lat;
				}
				get longitude() {
					return this._long;
				}
				isEqual(e) {
					return this._lat === e._lat && this._long === e._long;
				}
				toJSON() {
					return { latitude: this._lat, longitude: this._long };
				}
				_compareTo(e) {
					return L(this._lat, e._lat) || L(this._long, e._long);
				}
			}
			class oI {
				constructor(e) {
					this._values = (e || []).map((e) => e);
				}
				toArray() {
					return this._values.map((e) => e);
				}
				isEqual(e) {
					return ((e, t) => {
						if (e.length !== t.length) return !1;
						for (let n = 0; n < e.length; ++n) if (e[n] !== t[n]) return !1;
						return !0;
					})(this._values, e._values);
				}
			}
			const oT = /^__.*__$/;
			class oE {
				constructor(e, t, n) {
					(this.data = e), (this.fieldMask = t), (this.fieldTransforms = n);
				}
				toMutation(e, t) {
					return null !== this.fieldMask
						? new nL(e, this.data, this.fieldMask, t, this.fieldTransforms)
						: new nF(e, this.data, t, this.fieldTransforms);
				}
			}
			class ob {
				constructor(e, t, n) {
					(this.data = e), (this.fieldMask = t), (this.fieldTransforms = n);
				}
				toMutation(e, t) {
					return new nL(e, this.data, this.fieldMask, t, this.fieldTransforms);
				}
			}
			function o_(e) {
				switch (e) {
					case 0:
					case 2:
					case 1:
						return !0;
					case 3:
					case 4:
						return !1;
					default:
						throw _();
				}
			}
			class oS {
				constructor(e, t, n, r, i, s) {
					(this.settings = e),
						(this.databaseId = t),
						(this.serializer = n),
						(this.ignoreUndefinedProperties = r),
						void 0 === i && this.vu(),
						(this.fieldTransforms = i || []),
						(this.fieldMask = s || []);
				}
				get path() {
					return this.settings.path;
				}
				get Cu() {
					return this.settings.Cu;
				}
				Fu(e) {
					return new oS(
						Object.assign(Object.assign({}, this.settings), e),
						this.databaseId,
						this.serializer,
						this.ignoreUndefinedProperties,
						this.fieldTransforms,
						this.fieldMask,
					);
				}
				Mu(e) {
					var t;
					const n =
							null === (t = this.path) || void 0 === t ? void 0 : t.child(e),
						r = this.Fu({ path: n, xu: !1 });
					return r.Ou(e), r;
				}
				Nu(e) {
					var t;
					const n =
							null === (t = this.path) || void 0 === t ? void 0 : t.child(e),
						r = this.Fu({ path: n, xu: !1 });
					return r.vu(), r;
				}
				Lu(e) {
					return this.Fu({ path: void 0, xu: !0 });
				}
				Bu(e) {
					return o$(
						e,
						this.settings.methodName,
						this.settings.ku || !1,
						this.path,
						this.settings.qu,
					);
				}
				contains(e) {
					return (
						void 0 !== this.fieldMask.find((t) => e.isPrefixOf(t)) ||
						void 0 !== this.fieldTransforms.find((t) => e.isPrefixOf(t.field))
					);
				}
				vu() {
					if (this.path)
						for (let e = 0; e < this.path.length; e++)
							this.Ou(this.path.get(e));
				}
				Ou(e) {
					if (0 === e.length)
						throw this.Bu("Document fields must not be empty");
					if (o_(this.Cu) && oT.test(e))
						throw this.Bu('Document fields cannot begin and end with "__"');
				}
			}
			class ox {
				constructor(e, t, n) {
					(this.databaseId = e),
						(this.ignoreUndefinedProperties = t),
						(this.serializer = n || sN(e));
				}
				Qu(e, t, n, r = !1) {
					return new oS(
						{ Cu: e, methodName: t, qu: n, path: G.emptyPath(), xu: !1, ku: r },
						this.databaseId,
						this.serializer,
						this.ignoreUndefinedProperties,
					);
				}
			}
			function oD(e) {
				const t = e._freezeSettings(),
					n = sN(e._databaseId);
				return new ox(e._databaseId, !!t.ignoreUndefinedProperties, n);
			}
			function oC(e, t, n, r, i, s = {}) {
				let a, o;
				const l = e.Qu(s.merge || s.mergeFields ? 2 : 0, t, n, i);
				oB("Data must be an object, but it was:", l, r);
				const u = oU(r, l);
				if (s.merge) (a = new e1(l.fieldMask)), (o = l.fieldTransforms);
				else if (s.mergeFields) {
					const e = [];
					for (const r of s.mergeFields) {
						const i = oK(t, r, n);
						if (!l.contains(i))
							throw new x(
								S.INVALID_ARGUMENT,
								`Field '${i}' is specified in your field mask but missing from your input data.`,
							);
						oQ(e, i) || e.push(i);
					}
					(a = new e1(e)),
						(o = l.fieldTransforms.filter((e) => a.covers(e.field)));
				} else (a = null), (o = l.fieldTransforms);
				return new oE(new t_(u), a, o);
			}
			class oN extends ow {
				_toFieldTransform(e) {
					if (2 !== e.Cu)
						throw 1 === e.Cu
							? e.Bu(
									`${this._methodName}() can only appear at the top level of your update data`,
								)
							: e.Bu(
									`${this._methodName}() cannot be used with set() unless you pass {merge:true}`,
								);
					return e.fieldMask.push(e.path), null;
				}
				isEqual(e) {
					return e instanceof oN;
				}
			}
			function ok(e, t, n) {
				return new oS(
					{ Cu: 3, qu: t.settings.qu, methodName: e._methodName, xu: n },
					t.databaseId,
					t.serializer,
					t.ignoreUndefinedProperties,
				);
			}
			class oA extends null {
				_toFieldTransform(e) {
					return new nC(e.path, new nI());
				}
				isEqual(e) {
					return e instanceof oA;
				}
			}
			class oV extends ow {
				constructor(e, t) {
					super(e), (this.Ku = t);
				}
				_toFieldTransform(e) {
					const t = ok(this, e, !0),
						n = new nT(this.Ku.map((e) => oP(e, t)));
					return new nC(e.path, n);
				}
				isEqual(e) {
					return e instanceof oV && (0, h.bD)(this.Ku, e.Ku);
				}
			}
			class oR extends ow {
				constructor(e, t) {
					super(e), (this.Ku = t);
				}
				_toFieldTransform(e) {
					const t = ok(this, e, !0),
						n = new nb(this.Ku.map((e) => oP(e, t)));
					return new nC(e.path, n);
				}
				isEqual(e) {
					return e instanceof oR && (0, h.bD)(this.Ku, e.Ku);
				}
			}
			class oO extends ow {
				constructor(e, t) {
					super(e), (this.$u = t);
				}
				_toFieldTransform(e) {
					const t = new nS(e.serializer, ny(e.serializer, this.$u));
					return new nC(e.path, t);
				}
				isEqual(e) {
					return e instanceof oO && this.$u === e.$u;
				}
			}
			function oM(e, t, n, r) {
				const i = e.Qu(1, t, n);
				oB("Data must be an object, but it was:", i, r);
				const s = [],
					a = t_.empty();
				return (
					ej(r, (e, r) => {
						const o = oG(t, e, n);
						r = (0, h.Ku)(r);
						const l = i.Nu(o);
						if (r instanceof oN) s.push(o);
						else {
							const e = oP(r, l);
							null != e && (s.push(o), a.set(o, e));
						}
					}),
					new ob(a, new e1(s), i.fieldTransforms)
				);
			}
			function oF(e, t, n, r, i, s) {
				const a = e.Qu(1, t, n),
					o = [oK(t, r, n)],
					l = [i];
				if (s.length % 2 != 0)
					throw new x(
						S.INVALID_ARGUMENT,
						`Function ${t}() needs to be called with an even number of arguments that alternate between field names and values.`,
					);
				for (let e = 0; e < s.length; e += 2)
					o.push(oK(t, s[e])), l.push(s[e + 1]);
				const u = [],
					c = t_.empty();
				for (let e = o.length - 1; e >= 0; --e)
					if (!oQ(u, o[e])) {
						let t = o[e],
							n = l[e];
						n = (0, h.Ku)(n);
						const r = a.Nu(t);
						if (n instanceof oN) u.push(t);
						else {
							const e = oP(n, r);
							null != e && (u.push(t), c.set(t, e));
						}
					}
				return new ob(c, new e1(u), a.fieldTransforms);
			}
			function oL(e, t, n, r = !1) {
				return oP(n, e.Qu(r ? 4 : 3, t));
			}
			function oP(e, t) {
				if (oq((e = (0, h.Ku)(e))))
					return oB("Unsupported field value:", t, e), oU(e, t);
				if (e instanceof ow)
					return (
						((e, t) => {
							if (!o_(t.Cu))
								throw t.Bu(
									`${e._methodName}() can only be used with update() and set()`,
								);
							if (!t.path)
								throw t.Bu(
									`${e._methodName}() is not currently supported inside arrays`,
								);
							const n = e._toFieldTransform(t);
							n && t.fieldTransforms.push(n);
						})(e, t),
						null
					);
				if (void 0 === e && t.ignoreUndefinedProperties) return null;
				if ((t.path && t.fieldMask.push(t.path), e instanceof Array)) {
					if (t.settings.xu && 4 !== t.Cu)
						throw t.Bu("Nested arrays are not supported");
					return ((e, t) => {
						let n = [],
							r = 0;
						for (const i of e) {
							let e = oP(i, t.Lu(r));
							null == e && (e = { nullValue: "NULL_VALUE" }), n.push(e), r++;
						}
						return { arrayValue: { values: n } };
					})(e, t);
				}
				return ((e, t) => {
					if (null === (e = (0, h.Ku)(e))) return { nullValue: "NULL_VALUE" };
					if ("number" == typeof e) return ny(t.serializer, e);
					if ("boolean" == typeof e) return { booleanValue: e };
					if ("string" == typeof e) return { stringValue: e };
					if (e instanceof Date) {
						const n = U.fromDate(e);
						return { timestampValue: ri(t.serializer, n) };
					}
					if (e instanceof U) {
						const n = new U(e.seconds, 1e3 * Math.floor(e.nanoseconds / 1e3));
						return { timestampValue: ri(t.serializer, n) };
					}
					if (e instanceof ov)
						return {
							geoPointValue: { latitude: e.latitude, longitude: e.longitude },
						};
					if (e instanceof op)
						return { bytesValue: rs(t.serializer, e._byteString) };
					if (e instanceof ou) {
						const n = t.databaseId,
							r = e.firestore._databaseId;
						if (!r.isEqual(n))
							throw t.Bu(
								`Document reference is for database ${r.projectId}/${r.database} but should be for database ${n.projectId}/${n.database}`,
							);
						return {
							referenceValue: ro(
								e.firestore._databaseId || t.databaseId,
								e._key.path,
							),
						};
					}
					if (e instanceof oI)
						return {
							mapValue: {
								fields: {
									__type__: { stringValue: "__vector__" },
									value: {
										arrayValue: {
											values: e.toArray().map((e) => {
												if ("number" != typeof e)
													throw t.Bu(
														"VectorValues must only contain numeric values.",
													);
												return ng(t.serializer, e);
											}),
										},
									},
								},
							},
						};
					throw t.Bu(`Unsupported field value: ${oi(e)}`);
				})(e, t);
			}
			function oU(e, t) {
				const n = {};
				return (
					eW(e)
						? t.path && t.path.length > 0 && t.fieldMask.push(t.path)
						: ej(e, (e, r) => {
								const i = oP(r, t.Mu(e));
								null != i && (n[e] = i);
							}),
					{ mapValue: { fields: n } }
				);
			}
			function oq(e) {
				return !(
					"object" != typeof e ||
					null === e ||
					e instanceof Array ||
					e instanceof Date ||
					e instanceof U ||
					e instanceof ov ||
					e instanceof op ||
					e instanceof ou ||
					e instanceof ow ||
					e instanceof oI
				);
			}
			function oB(e, t, n) {
				if (
					!oq(n) ||
					"object" != typeof n ||
					null === n ||
					(Object.getPrototypeOf(n) !== Object.prototype &&
						null !== Object.getPrototypeOf(n))
				) {
					const r = oi(n);
					throw "an object" === r
						? t.Bu(e + " a custom object")
						: t.Bu(e + " " + r);
				}
			}
			function oK(e, t, n) {
				if ((t = (0, h.Ku)(t)) instanceof oy) return t._internalPath;
				if ("string" == typeof t) return oG(e, t);
				throw o$(
					"Field path arguments must be of type string or ",
					e,
					!1,
					void 0,
					n,
				);
			}
			const oz = /[~\*\/\[\]]/;
			function oG(e, t, n) {
				if (t.search(oz) >= 0)
					throw o$(
						`Invalid field path (${t}). Paths must not contain '~', '*', '/', '[', or ']'`,
						e,
						!1,
						void 0,
						n,
					);
				try {
					return new oy(...t.split("."))._internalPath;
				} catch (r) {
					throw o$(
						`Invalid field path (${t}). Paths must not be empty, begin with '.', end with '.', or contain '..'`,
						e,
						!1,
						void 0,
						n,
					);
				}
			}
			function o$(e, t, n, r, i) {
				let s = r && !r.isEmpty(),
					a = void 0 !== i,
					o = `Function ${t}() called with invalid data`;
				n && (o += " (via `toFirestore()`)"), (o += ". ");
				let l = "";
				return (
					(s || a) &&
						((l += " (found"),
						s && (l += ` in field ${r}`),
						a && (l += ` in document ${i}`),
						(l += ")")),
					new x(S.INVALID_ARGUMENT, o + e + l)
				);
			}
			function oQ(e, t) {
				return e.some((e) => e.isEqual(t));
			}
			class oj {
				constructor(e, t, n, r, i) {
					(this._firestore = e),
						(this._userDataWriter = t),
						(this._key = n),
						(this._document = r),
						(this._converter = i);
				}
				get id() {
					return this._key.path.lastSegment();
				}
				get ref() {
					return new ou(this._firestore, this._converter, this._key);
				}
				exists() {
					return null !== this._document;
				}
				data() {
					if (this._document) {
						if (this._converter) {
							const e = new oW(
								this._firestore,
								this._userDataWriter,
								this._key,
								this._document,
								null,
							);
							return this._converter.fromFirestore(e);
						}
						return this._userDataWriter.convertValue(this._document.data.value);
					}
				}
				get(e) {
					if (this._document) {
						const t = this._document.data.field(oJ("DocumentSnapshot.get", e));
						if (null !== t) return this._userDataWriter.convertValue(t);
					}
				}
			}
			class oW extends oj {
				data() {
					return super.data();
				}
			}
			function oJ(e, t) {
				return "string" == typeof t
					? oG(e, t)
					: t instanceof oy
						? t._internalPath
						: t._delegate._internalPath;
			}
			class oH {}
			class oY extends oH {}
			class oX extends oY {
				constructor(e, t, n) {
					super(),
						(this._field = e),
						(this._op = t),
						(this._value = n),
						(this.type = "where");
				}
				static _create(e, t, n) {
					return new oX(e, t, n);
				}
				_apply(e) {
					const t = this._parse(e);
					return (
						o6(e._query, t), new ol(e.firestore, e.converter, t6(e._query, t))
					);
				}
				_parse(e) {
					const t = oD(e.firestore);
					return ((e, t, n, r, i, s, a) => {
						let o;
						if (i.isKeyField()) {
							if ("array-contains" === s || "array-contains-any" === s)
								throw new x(
									S.INVALID_ARGUMENT,
									`Invalid Query. You can't perform '${s}' queries on documentId().`,
								);
							if ("in" === s || "not-in" === s) {
								o4(a, s);
								const t = [];
								for (const n of a) t.push(o8(r, e, n));
								o = { arrayValue: { values: t } };
							} else o = o8(r, e, a);
						} else
							("in" !== s && "not-in" !== s && "array-contains-any" !== s) ||
								o4(a, s),
								(o = oL(n, t, a, "in" === s || "not-in" === s));
						return tA.create(i, s, o);
					})(
						e._query,
						"where",
						t,
						e.firestore._databaseId,
						this._field,
						this._op,
						this._value,
					);
				}
			}
			class oZ extends oH {
				constructor(e, t) {
					super(), (this.type = e), (this._queryConstraints = t);
				}
				static _create(e, t) {
					return new oZ(e, t);
				}
				_parse(e) {
					const t = this._queryConstraints
						.map((t) => t._parse(e))
						.filter((e) => e.getFilters().length > 0);
					return 1 === t.length ? t[0] : tV.create(t, this._getOperator());
				}
				_apply(e) {
					const t = this._parse(e);
					return 0 === t.getFilters().length
						? e
						: (((e, t) => {
								let n = e;
								for (const e of t.getFlattenedFilters())
									o6(n, e), (n = t6(n, e));
							})(e._query, t),
							new ol(e.firestore, e.converter, t6(e._query, t)));
				}
				_getQueryConstraints() {
					return this._queryConstraints;
				}
				_getOperator() {
					return "and" === this.type ? "and" : "or";
				}
			}
			class o0 extends oY {
				constructor(e, t) {
					super(),
						(this._field = e),
						(this._direction = t),
						(this.type = "orderBy");
				}
				static _create(e, t) {
					return new o0(e, t);
				}
				_apply(e) {
					const t = ((e, t, n) => {
						if (null !== e.startAt)
							throw new x(
								S.INVALID_ARGUMENT,
								"Invalid query. You must not call startAt() or startAfter() before calling orderBy().",
							);
						if (null !== e.endAt)
							throw new x(
								S.INVALID_ARGUMENT,
								"Invalid query. You must not call endAt() or endBefore() before calling orderBy().",
							);
						return new tN(t, n);
					})(e._query, this._field, this._direction);
					return new ol(
						e.firestore,
						e.converter,
						((e, t) => {
							const n = e.explicitOrderBy.concat([t]);
							return new t0(
								e.path,
								e.collectionGroup,
								n,
								e.filters.slice(),
								e.limit,
								e.limitType,
								e.startAt,
								e.endAt,
							);
						})(e._query, t),
					);
				}
			}
			class o1 extends oY {
				constructor(e, t, n) {
					super(), (this.type = e), (this._limit = t), (this._limitType = n);
				}
				static _create(e, t, n) {
					return new o1(e, t, n);
				}
				_apply(e) {
					return new ol(
						e.firestore,
						e.converter,
						t9(e._query, this._limit, this._limitType),
					);
				}
			}
			class o2 extends oY {
				constructor(e, t, n) {
					super(),
						(this.type = e),
						(this._docOrFields = t),
						(this._inclusive = n);
				}
				static _create(e, t, n) {
					return new o2(e, t, n);
				}
				_apply(e) {
					var t;
					const n = o3(e, this.type, this._docOrFields, this._inclusive);
					return new ol(
						e.firestore,
						e.converter,
						new t0(
							(t = e._query).path,
							t.collectionGroup,
							t.explicitOrderBy.slice(),
							t.filters.slice(),
							t.limit,
							t.limitType,
							n,
							t.endAt,
						),
					);
				}
			}
			class o5 extends oY {
				constructor(e, t, n) {
					super(),
						(this.type = e),
						(this._docOrFields = t),
						(this._inclusive = n);
				}
				static _create(e, t, n) {
					return new o5(e, t, n);
				}
				_apply(e) {
					var t;
					const n = o3(e, this.type, this._docOrFields, this._inclusive);
					return new ol(
						e.firestore,
						e.converter,
						new t0(
							(t = e._query).path,
							t.collectionGroup,
							t.explicitOrderBy.slice(),
							t.filters.slice(),
							t.limit,
							t.limitType,
							t.startAt,
							n,
						),
					);
				}
			}
			function o3(e, t, n, r) {
				if (((n[0] = (0, h.Ku)(n[0])), n[0] instanceof oj))
					return ((e, t, n, r, i) => {
						if (!r)
							throw new x(
								S.NOT_FOUND,
								`Can't use a DocumentSnapshot that doesn't exist for ${n}().`,
							);
						const s = [];
						for (const n of t3(e))
							if (n.field.isKeyField()) s.push(td(t, r.key));
							else {
								const e = r.data.field(n.field);
								if (e9(e))
									throw new x(
										S.INVALID_ARGUMENT,
										'Invalid query. You are trying to start or end a query using a document for which the field "' +
											n.field +
											'" is an uncommitted server timestamp. (Since the value of this field is unknown, you cannot start/end a query with it.)',
									);
								if (null === e) {
									const e = n.field.canonicalString();
									throw new x(
										S.INVALID_ARGUMENT,
										`Invalid query. You are trying to start or end a query using a document for which the field '${e}' (used as the orderBy) does not exist.`,
									);
								}
								s.push(e);
							}
						return new tx(s, i);
					})(e._query, e.firestore._databaseId, t, n[0]._document, r);
				{
					const i = oD(e.firestore);
					return ((e, t, n, r, i, s) => {
						const a = e.explicitOrderBy;
						if (i.length > a.length)
							throw new x(
								S.INVALID_ARGUMENT,
								`Too many arguments provided to ${r}(). The number of arguments must be less than or equal to the number of orderBy() clauses`,
							);
						const o = [];
						for (let s = 0; s < i.length; s++) {
							const l = i[s];
							if (a[s].field.isKeyField()) {
								if ("string" != typeof l)
									throw new x(
										S.INVALID_ARGUMENT,
										`Invalid query. Expected a string for document ID in ${r}(), but got a ${typeof l}`,
									);
								if (!t5(e) && -1 !== l.indexOf("/"))
									throw new x(
										S.INVALID_ARGUMENT,
										`Invalid query. When querying a collection and ordering by documentId(), the value passed to ${r}() must be a plain document ID, but '${l}' contains a slash.`,
									);
								const n = e.path.child(K.fromString(l));
								if (!$.isDocumentKey(n))
									throw new x(
										S.INVALID_ARGUMENT,
										`Invalid query. When querying a collection group and ordering by documentId(), the value passed to ${r}() must result in a valid document path, but '${n}' is not because it contains an odd number of segments.`,
									);
								const i = new $(n);
								o.push(td(t, i));
							} else {
								const e = oL(n, r, l);
								o.push(e);
							}
						}
						return new tx(o, s);
					})(e._query, e.firestore._databaseId, i, t, n, r);
				}
			}
			function o8(e, t, n) {
				if ("string" == typeof (n = (0, h.Ku)(n))) {
					if ("" === n)
						throw new x(
							S.INVALID_ARGUMENT,
							"Invalid query. When querying with documentId(), you must provide a valid document ID, but it was an empty string.",
						);
					if (!t5(t) && -1 !== n.indexOf("/"))
						throw new x(
							S.INVALID_ARGUMENT,
							`Invalid query. When querying a collection by documentId(), you must provide a plain document ID, but '${n}' contains a '/' character.`,
						);
					const r = t.path.child(K.fromString(n));
					if (!$.isDocumentKey(r))
						throw new x(
							S.INVALID_ARGUMENT,
							`Invalid query. When querying a collection group by documentId(), the value provided must result in a valid document path, but '${r}' is not because it has an odd number of segments (${r.length}).`,
						);
					return td(e, new $(r));
				}
				if (n instanceof ou) return td(e, n._key);
				throw new x(
					S.INVALID_ARGUMENT,
					`Invalid query. When querying with documentId(), you must provide a valid string or a DocumentReference, but it was: ${oi(n)}.`,
				);
			}
			function o4(e, t) {
				if (!Array.isArray(e) || 0 === e.length)
					throw new x(
						S.INVALID_ARGUMENT,
						`Invalid Query. A non-empty array is required for '${t.toString()}' filters.`,
					);
			}
			function o6(e, t) {
				const n = ((e, t) => {
					for (const n of e)
						for (const e of n.getFlattenedFilters())
							if (t.indexOf(e.op) >= 0) return e.op;
					return null;
				})(
					e.filters,
					((e) => {
						switch (e) {
							case "!=":
								return ["!=", "not-in"];
							case "array-contains-any":
							case "in":
								return ["not-in"];
							case "not-in":
								return ["array-contains-any", "in", "not-in", "!="];
							default:
								return [];
						}
					})(t.op),
				);
				if (null !== n)
					throw n === t.op
						? new x(
								S.INVALID_ARGUMENT,
								`Invalid query. You cannot use more than one '${t.op.toString()}' filter.`,
							)
						: new x(
								S.INVALID_ARGUMENT,
								`Invalid query. You cannot use '${t.op.toString()}' filters with '${n.toString()}' filters.`,
							);
			}
			class o9 {
				convertValue(e, t = "none") {
					switch (ts(e)) {
						case 0:
							return null;
						case 1:
							return e.booleanValue;
						case 2:
							return e4(e.integerValue || e.doubleValue);
						case 3:
							return this.convertTimestamp(e.timestampValue);
						case 4:
							return this.convertServerTimestamp(e, t);
						case 5:
							return e.stringValue;
						case 6:
							return this.convertBytes(e6(e.bytesValue));
						case 7:
							return this.convertReference(e.referenceValue);
						case 8:
							return this.convertGeoPoint(e.geoPointValue);
						case 9:
							return this.convertArray(e.arrayValue, t);
						case 11:
							return this.convertObject(e.mapValue, t);
						case 10:
							return this.convertVectorValue(e.mapValue);
						default:
							throw _();
					}
				}
				convertObject(e, t) {
					return this.convertObjectMap(e.fields, t);
				}
				convertObjectMap(e, t = "none") {
					const n = {};
					return (
						ej(e, (e, r) => {
							n[e] = this.convertValue(r, t);
						}),
						n
					);
				}
				convertVectorValue(e) {
					var t, n, r;
					return new oI(
						null ===
							(r =
								null ===
									(n =
										null === (t = e.fields) || void 0 === t
											? void 0
											: t.value.arrayValue) || void 0 === n
									? void 0
									: n.values) || void 0 === r
							? void 0
							: r.map((e) => e4(e.doubleValue)),
					);
				}
				convertGeoPoint(e) {
					return new ov(e4(e.latitude), e4(e.longitude));
				}
				convertArray(e, t) {
					return (e.values || []).map((e) => this.convertValue(e, t));
				}
				convertServerTimestamp(e, t) {
					switch (t) {
						case "previous":
							const n = e7(e);
							return null == n ? null : this.convertValue(n, t);
						case "estimate":
							return this.convertTimestamp(te(e));
						default:
							return null;
					}
				}
				convertTimestamp(e) {
					const t = e8(e);
					return new U(t.seconds, t.nanos);
				}
				convertDocumentKey(e, t) {
					const n = K.fromString(e);
					rS(n) || _();
					const r = new tn(n.get(1), n.get(3)),
						i = new $(n.popFirst(5));
					return (
						r.isEqual(t) ||
							T(
								`Document ${i} contains a document reference within a different database (${r.projectId}/${r.database}) which is not supported. It will be treated as a reference in the current database (${t.projectId}/${t.database}) instead.`,
							),
						i
					);
				}
			}
			function o7(e, t, n) {
				return e
					? n && (n.merge || n.mergeFields)
						? e.toFirestore(t, n)
						: e.toFirestore(t)
					: t;
			}
			class le extends o9 {
				constructor(e) {
					super(), (this.firestore = e);
				}
				convertBytes(e) {
					return new op(e);
				}
				convertReference(e) {
					const t = this.convertDocumentKey(e, this.firestore._databaseId);
					return new ou(this.firestore, null, t);
				}
			}
			class lt {
				constructor(e, t) {
					(this.hasPendingWrites = e), (this.fromCache = t);
				}
				isEqual(e) {
					return (
						this.hasPendingWrites === e.hasPendingWrites &&
						this.fromCache === e.fromCache
					);
				}
			}
			class ln extends oj {
				constructor(e, t, n, r, i, s) {
					super(e, t, n, r, s),
						(this._firestore = e),
						(this._firestoreImpl = e),
						(this.metadata = i);
				}
				exists() {
					return super.exists();
				}
				data(e = {}) {
					if (this._document) {
						if (this._converter) {
							const t = new lr(
								this._firestore,
								this._userDataWriter,
								this._key,
								this._document,
								this.metadata,
								null,
							);
							return this._converter.fromFirestore(t, e);
						}
						return this._userDataWriter.convertValue(
							this._document.data.value,
							e.serverTimestamps,
						);
					}
				}
				get(e, t = {}) {
					if (this._document) {
						const n = this._document.data.field(oJ("DocumentSnapshot.get", e));
						if (null !== n)
							return this._userDataWriter.convertValue(n, t.serverTimestamps);
					}
				}
			}
			class lr extends ln {
				data(e = {}) {
					return super.data(e);
				}
			}
			class li extends o9 {
				constructor(e) {
					super(), (this.firestore = e);
				}
				convertBytes(e) {
					return new op(e);
				}
				convertReference(e) {
					const t = this.convertDocumentKey(e, this.firestore._databaseId);
					return new ou(this.firestore, null, t);
				}
			}
			class ls {
				constructor(e) {
					(this.forceOwnership = e), (this.kind = "persistentSingleTab");
				}
				toJSON() {
					return { kind: this.kind };
				}
				_initialize(e) {
					(this._onlineComponentProvider = a5.provider),
						(this._offlineComponentProvider = {
							build: (t) =>
								new a1(
									t,
									null == e ? void 0 : e.cacheSizeBytes,
									this.forceOwnership,
								),
						});
				}
			}
			function la(e, t) {
				if ((e = (0, h.Ku)(e)).firestore !== t)
					throw new x(
						S.INVALID_ARGUMENT,
						"Provided document reference is from a different Firestore instance.",
					);
				return e;
			}
			new WeakMap(),
				!((e, t = !0) => {
					(y = o.MF),
						(0, o.om)(
							new l.uA(
								"firestore",
								(e, { instanceIdentifier: n, options: r }) => {
									const i = e.getProvider("app").getImmediate(),
										s = new od(
											new A(e.getProvider("auth-internal")),
											new M(e.getProvider("app-check-internal")),
											((e, t) => {
												if (
													!Object.prototype.hasOwnProperty.apply(e.options, [
														"projectId",
													])
												)
													throw new x(
														S.INVALID_ARGUMENT,
														'"projectId" not provided in firebase.initializeApp.',
													);
												return new tn(e.options.projectId, t);
											})(i, n),
											i,
										);
									return (
										(r = Object.assign({ useFetchStreams: t }, r)),
										s._setSettings(r),
										s
									);
								},
								"PUBLIC",
							).setMultipleInstances(!0),
						),
						(0, o.KO)(g, "4.7.3", void 0),
						(0, o.KO)(g, "4.7.3", "esm2017");
				})();
		},
	},
]);
