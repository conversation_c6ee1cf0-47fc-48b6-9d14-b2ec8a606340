(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[716],
	{
		245: (e, t, i) => {
			i.d(t, {
				KT: () => r,
				R4: () => o,
				TZ: () => n,
				Tg: () => l,
				Wm: () => c,
				qD: () => s,
				xk: () => a,
			});
			const a = {
					1: {
						name: "ALEX JOHNSON",
						type: "PHOTOGRAPHER",
						location: "New York, NY",
						followers: 1234,
						following: 567,
						bio: "Professional photographer specializing in portraits and fashion",
					},
					2: {
						name: "SARAH WILSON",
						type: "MODEL",
						location: "Los Angeles, CA",
						followers: 2456,
						following: 432,
						bio: "Fashion and commercial model",
					},
					3: {
						name: "MICHAEL CHEN",
						type: "VIDEOGRAPHER",
						location: "Chicago, IL",
						followers: 987,
						following: 234,
						bio: "Documentary and commercial videographer",
					},
					4: {
						name: "<PERSON><PERSON><PERSON> DAVIS",
						type: "MAKEUP ARTIST",
						location: "Miami, FL",
						followers: 1567,
						following: 345,
						bio: "Editorial and bridal makeup creative",
					},
					5: {
						name: "DAVID RODRIGUEZ",
						type: "PHOTOGRAPHER",
						location: "Austin, TX",
						followers: 2134,
						following: 678,
						bio: "Product and architecture photographer",
					},
				},
				s = [
					{
						id: "1",
						src: "/assets/creatives_portfolio/crt1.png",
						alt: "Sunset beach",
						likes: 324,
						comments: 18,
						position: 0,
					},
					{
						id: "2",
						src: "/assets/creatives_portfolio/crt2.png",
						alt: "Pasta dinner",
						likes: 189,
						comments: 12,
						position: 1,
					},
					{
						id: "3",
						src: "/assets/creatives_portfolio/crt3.png",
						alt: "Mountain view",
						likes: 456,
						comments: 23,
						position: 2,
					},
					{
						id: "4",
						src: "/assets/creatives_portfolio/crt4.png",
						alt: "Morning coffee",
						likes: 203,
						comments: 15,
						position: 3,
					},
					{
						id: "5",
						src: "/assets/creatives_portfolio/crt5.png",
						alt: "City lights",
						likes: 378,
						comments: 26,
						position: 4,
					},
					{
						id: "6",
						src: "/assets/creatives_portfolio/crt6.png",
						alt: "Golden retriever",
						likes: 567,
						comments: 41,
						position: 5,
					},
					{
						id: "7",
						src: "/assets/creatives_portfolio/crt8.png",
						alt: "Sushi dinner",
						likes: 234,
						comments: 19,
						position: 6,
					},
					{
						id: "8",
						src: "/assets/creatives_portfolio/crt7.png",
						alt: "Road trip",
						likes: 292,
						comments: 17,
						position: 7,
					},
					{
						id: "9",
						src: "/placeholder.svg?height=400&width=400&text=Colorful flower garden in spring",
						alt: "Spring flowers",
						likes: 445,
						comments: 31,
						position: 8,
					},
				],
				l = [
					{
						id: "job1",
						title: "Wedding Photography",
						client: "Sarah & Mike Johnson",
						date: "2024-01-15",
						time: "2:00 PM",
						location: "Central Park, NYC",
						status: "confirmed",
						type: "photography",
					},
					{
						id: "job2",
						title: "Corporate Headshots",
						client: "Tech Solutions Inc.",
						date: "2024-01-18",
						time: "10:00 AM",
						location: "Downtown Office",
						status: "pending",
						type: "photography",
					},
					{
						id: "job3",
						title: "Product Photography",
						client: "Fashion Brand Co.",
						date: "2024-01-22",
						time: "1:00 PM",
						location: "Studio A",
						status: "confirmed",
						type: "photography",
					},
					{
						id: "job4",
						title: "Event Coverage",
						client: "Marketing Agency",
						date: "2024-01-25",
						time: "6:00 PM",
						location: "Convention Center",
						status: "confirmed",
						type: "photography",
					},
				],
				r = [
					{
						id: "payment1",
						jobId: "job5",
						jobTitle: "Wedding Photography",
						client: "Jennifer & David Smith",
						amount: 2500,
						date: "2023-12-20",
						status: "paid",
						invoiceNumber: "INV-2023-001",
					},
					{
						id: "payment2",
						jobId: "job6",
						jobTitle: "Corporate Event",
						client: "Global Tech Inc.",
						amount: 1800,
						date: "2023-12-05",
						status: "paid",
						invoiceNumber: "INV-2023-002",
					},
					{
						id: "payment3",
						jobId: "job7",
						jobTitle: "Product Shoot",
						client: "Luxury Brands Co.",
						amount: 1200,
						date: "2023-11-28",
						status: "paid",
						invoiceNumber: "INV-2023-003",
					},
				],
				o = [
					{
						id: "project1",
						title: "Summer Lookbook",
						client: "Zara",
						projectType: "fashion",
						startDate: "2024-07-01",
						endDate: "2024-07-15",
						location: "Miami Beach",
						budget: 2e4,
						description: "Create a summer lookbook for Zara's new collection.",
						creativesNeeded: [
							"Photographer",
							"Model",
							"Makeup Artist",
							"Stylist",
						],
					},
					{
						id: "project2",
						title: "Corporate Headshots",
						client: "Google",
						projectType: "corporate",
						startDate: "2024-08-01",
						endDate: "2024-08-05",
						location: "Mountain View",
						budget: 1e4,
						description: "Take corporate headshots for Google's employees.",
						creativesNeeded: ["Photographer"],
					},
					{
						id: "project3",
						title: "Wedding Photography",
						client: "John and Jane Doe",
						projectType: "wedding",
						startDate: "2024-09-01",
						endDate: "2024-09-01",
						location: "Central Park",
						budget: 5e3,
						description: "Photograph John and Jane Doe's wedding.",
						creativesNeeded: ["Photographer"],
					},
				],
				n = [
					{
						id: "offer_rec1",
						projectTitle: "Summer Glow Campaign",
						clientName: "Sunlight Beauty Co.",
						role: "Lead Photographer",
						amount: 2200,
						offerDate: "2024-03-10",
						status: "pending",
						projectType: "Beauty Product Shoot",
						location: "Miami Beach, FL",
						description:
							"Looking for a photographer with a bright and airy style for our new sunscreen line.",
						isSentByMe: !1,
					},
					{
						id: "offer_rec2",
						projectTitle: "Urban Explorers Video",
						clientName: "City Adventures Magazine",
						role: "Videographer",
						amount: 1800,
						offerDate: "2024-03-05",
						status: "accepted",
						projectType: "Travel Documentary Short",
						location: "Various, NYC",
						description:
							"Short documentary piece following urban explorers. Drone skills a plus.",
						isSentByMe: !1,
					},
					{
						id: "offer_rec3",
						projectTitle: "Tech Conference Live Model",
						clientName: "Innovate Corp",
						role: "Promotional Model",
						amount: 750,
						offerDate: "2024-02-28",
						status: "declined",
						projectType: "Tech Event",
						location: "San Francisco, CA",
						description:
							"Need engaging models for our booth at the upcoming Innovate Summit.",
						isSentByMe: !1,
					},
				],
				c = [
					{
						id: "offer_sent1",
						projectTitle: "Indie Band Music Video",
						clientName: "The Wandering Souls (Band)",
						role: "Director of Photography",
						amount: 1500,
						offerDate: "2024-03-12",
						status: "pending",
						projectType: "Music Video",
						location: "Austin, TX",
						description:
							"Proposal to shoot and direct the photography for your upcoming music video.",
						isSentByMe: !0,
					},
					{
						id: "offer_sent2",
						projectTitle: "Artisan Bakery Branding",
						clientName: "The Sweet Spot Bakery",
						role: "Food Photographer",
						amount: 900,
						offerDate: "2024-03-02",
						status: "expired",
						projectType: "Branding & Lifestyle",
						location: "Portland, OR",
						description:
							"Offered to create a series of lifestyle and product shots for their new website.",
						isSentByMe: !0,
					},
					{
						id: "offer_sent3",
						projectTitle: "Local Cafe Social Media Content",
						clientName: "Corner Brew Cafe",
						role: "Content Creator (Photo/Video)",
						amount: 600,
						offerDate: "2024-02-20",
						status: "negotiating",
						projectType: "Social Media Marketing",
						location: "Local",
						description:
							"Proposed a monthly retainer for creating engaging social media content.",
						isSentByMe: !0,
					},
				];
		},
		602: (e, t, i) => {
			i.d(t, { default: () => O });
			var a = i(8081),
				s = i(2149),
				l = i(245);
			const r = (e) => {
				const { creative: t, photoCount: i } = e;
				return (0, a.jsx)("div", {
					className: "border-b border-gray-100",
					children: (0, a.jsx)("div", {
						className: "max-w-4xl mx-auto px-6 py-16",
						children: (0, a.jsxs)("div", {
							className: "text-center mb-12",
							children: [
								(0, a.jsx)("h2", {
									className: "text-3xl font-light tracking-wide mb-3",
									children: t.name,
								}),
								(0, a.jsx)("p", {
									className: "text-gray-600 font-light tracking-wide mb-6",
									children: t.type,
								}),
								" ",
								(0, a.jsxs)("div", {
									className: "flex justify-center gap-12 text-sm font-light",
									children: [
										(0, a.jsxs)("span", {
											children: [
												(0, a.jsx)("strong", {
													className: "font-normal",
													children: i,
												}),
												" Works",
											],
										}),
										(0, a.jsxs)("span", {
											children: [
												(0, a.jsx)("strong", {
													className: "font-normal",
													children: t.followers,
												}),
												" Followers",
											],
										}),
										(0, a.jsxs)("span", {
											children: [
												(0, a.jsx)("strong", {
													className: "font-normal",
													children: t.following,
												}),
												" Following",
											],
										}),
									],
								}),
							],
						}),
					}),
				});
			};
			var o = i(5160);
			const n = (e) => (e < 640 ? 1 : e < 1024 ? 2 : 3),
				c = () => {
					const [e, t] = (0, s.useState)(() => n(window.innerWidth));
					return (
						(0, s.useEffect)(() => {
							const e = () => {
								t(n(window.innerWidth));
							};
							return (
								window.addEventListener("resize", e),
								e(),
								() => window.removeEventListener("resize", e)
							);
						}, []),
						e
					);
				},
				d = (e) => {
					const { activeTab: t, onOpenBookingModal: i } = e,
						s = c(),
						l = [
							"Jan",
							"Feb",
							"Mar",
							"Apr",
							"May",
							"Jun",
							"Jul",
							"Aug",
							"Sep",
							"Oct",
							"Nov",
							"Dec",
						].slice(0, s);
					return (0, a.jsx)("div", {
						className: "border-b border-gray-100",
						children: (0, a.jsxs)("div", {
							className: "max-w-4xl mx-auto px-6 py-16",
							children: [
								(0, a.jsxs)("div", {
									className: "bg-gray-900 rounded p-8 mb-12",
									children: [
										(0, a.jsxs)("div", {
											className: "flex justify-between items-center mb-6",
											children: [
												(0, a.jsx)("span", {
													className:
														"text-white text-sm font-light tracking-wide",
													children:
														"income" === t
															? "Income Over Time"
															: "Availability",
												}),
												(0, a.jsxs)("div", {
													className: "flex items-center gap-6",
													children: [
														(0, a.jsxs)("span", {
															className: "flex items-center gap-2",
															children: [
																(0, a.jsx)("div", {
																	className:
																		"w-2 h-2 rounded-full bg-green-400",
																}),
																(0, a.jsx)("span", {
																	className:
																		"text-gray-400 text-xs font-light tracking-wide",
																	children: "Available",
																}),
															],
														}),
														(0, a.jsxs)("span", {
															className: "flex items-center gap-2",
															children: [
																(0, a.jsx)("div", {
																	className: "w-2 h-2 rounded-full bg-red-400",
																}),
																(0, a.jsx)("span", {
																	className:
																		"text-gray-400 text-xs font-light tracking-wide",
																	children: "Booked",
																}),
															],
														}),
													],
												}),
											],
										}),
										"income" === t
											? (0, a.jsx)("div", {
													className: "h-48 relative",
													children: (0, a.jsxs)("svg", {
														className: "w-full h-full",
														viewBox: "0 0 400 200",
														children: [
															(0, a.jsx)("defs", {
																children: (0, a.jsx)("pattern", {
																	id: "grid",
																	width: "40",
																	height: "20",
																	patternUnits: "userSpaceOnUse",
																	children: (0, a.jsx)("path", {
																		d: "M 40 0 L 0 0 0 20",
																		fill: "none",
																		stroke: "#374151",
																		strokeWidth: "0.5",
																		opacity: "0.3",
																	}),
																}),
															}),
															(0, a.jsx)("rect", {
																width: "100%",
																height: "100%",
																fill: "url(#grid)",
															}),
															(0, a.jsx)("line", {
																x1: "20",
																y1: "180",
																x2: "380",
																y2: "180",
																stroke: "#4b5563",
																strokeWidth: "1",
															}),
															(0, a.jsx)("line", {
																x1: "20",
																y1: "20",
																x2: "20",
																y2: "180",
																stroke: "#4b5563",
																strokeWidth: "1",
															}),
															[
																{
																	x: 40,
																	width: 20,
																	height: 60,
																	amount: 1200,
																	date: "Jan 15",
																},
																{
																	x: 80,
																	width: 20,
																	height: 90,
																	amount: 1800,
																	date: "Feb 3",
																},
																{
																	x: 120,
																	width: 20,
																	height: 125,
																	amount: 2500,
																	date: "Mar 22",
																},
																{
																	x: 160,
																	width: 20,
																	height: 50,
																	amount: 1e3,
																	date: "Apr 10",
																},
																{
																	x: 200,
																	width: 20,
																	height: 75,
																	amount: 1500,
																	date: "May 5",
																},
																{
																	x: 240,
																	width: 20,
																	height: 110,
																	amount: 2200,
																	date: "Jun 18",
																},
																{
																	x: 280,
																	width: 20,
																	height: 40,
																	amount: 800,
																	date: "Jul 7",
																},
																{
																	x: 320,
																	width: 20,
																	height: 100,
																	amount: 2e3,
																	date: "Aug 29",
																},
																{
																	x: 360,
																	width: 20,
																	height: 80,
																	amount: 1600,
																	date: "Sep 14",
																},
															].map((e, t) =>
																(0, a.jsxs)(
																	"g",
																	{
																		className: "group",
																		children: [
																			(0, a.jsx)("rect", {
																				x: e.x,
																				y: 180 - e.height,
																				width: e.width,
																				height: e.height,
																				fill: "#10b981",
																				className:
																					"hover:fill-green-600 transition-colors cursor-pointer",
																				children: (0, a.jsxs)("title", {
																					children: [
																						"$",
																						e.amount.toLocaleString(),
																						" - ",
																						e.date,
																					],
																				}),
																			}),
																			(0, a.jsxs)("text", {
																				x: e.x + e.width / 2,
																				y: 175 - e.height,
																				fill: "#065f46",
																				fontSize: "10",
																				textAnchor: "middle",
																				className:
																					"opacity-0 group-hover:opacity-100 transition-opacity",
																				children: ["$", e.amount],
																			}),
																		],
																	},
																	t,
																),
															),
															(0, a.jsx)("text", {
																x: "15",
																y: "30",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "end",
																children: "$2.5K",
															}),
															(0, a.jsx)("text", {
																x: "15",
																y: "80",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "end",
																children: "$2K",
															}),
															(0, a.jsx)("text", {
																x: "15",
																y: "130",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "end",
																children: "$1K",
															}),
															(0, a.jsx)("text", {
																x: "15",
																y: "180",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "end",
																children: "$0",
															}),
															(0, a.jsx)("text", {
																x: "50",
																y: "195",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "middle",
																children: "Jan",
															}),
															(0, a.jsx)("text", {
																x: "130",
																y: "195",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "middle",
																children: "Mar",
															}),
															(0, a.jsx)("text", {
																x: "210",
																y: "195",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "middle",
																children: "May",
															}),
															(0, a.jsx)("text", {
																x: "290",
																y: "195",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "middle",
																children: "Jul",
															}),
															(0, a.jsx)("text", {
																x: "370",
																y: "195",
																fill: "#9ca3af",
																fontSize: "10",
																textAnchor: "middle",
																children: "Sep",
															}),
														],
													}),
												})
											: (0, a.jsx)("div", {
													className: "grid grid-cols-".concat(s, " gap-8"),
													children: l.map((e, t) =>
														(0, a.jsxs)(
															"div",
															{
																children: [
																	(0, a.jsx)("div", {
																		className:
																			"text-center text-gray-400 text-xs mb-4 font-light tracking-wide",
																		children: e,
																	}),
																	(0, a.jsx)("div", {
																		className: "grid grid-cols-7 gap-1 mb-3",
																		children: [
																			"Sun",
																			"Mon",
																			"Tue",
																			"Wed",
																			"Thu",
																			"Fri",
																			"Sat",
																		].map((e) =>
																			(0, a.jsx)(
																				"div",
																				{
																					className:
																						"text-center text-gray-500 text-xs font-light tracking-wide",
																					children: e,
																				},
																				e,
																			),
																		),
																	}),
																	(0, a.jsx)("div", {
																		className: "grid grid-cols-7 gap-1",
																		children: [...Array(31)].map((t, i) => {
																			const s = Math.random() > 0.7;
																			return (0, a.jsx)(
																				"div",
																				{
																					className:
																						"w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ".concat(
																							s
																								? "bg-red-900 hover:bg-red-800 text-red-200"
																								: "bg-green-900 hover:bg-green-800 text-green-200",
																							" cursor-pointer transition-colors",
																						),
																					title: ""
																						.concat(e, " ")
																						.concat(i + 1, ": ")
																						.concat(s ? "Booked" : "Available"),
																					children: i + 1,
																				},
																				"".concat(e, "-day-").concat(i + 1),
																			);
																		}),
																	}),
																],
															},
															e,
														),
													),
												}),
									],
								}),
								(0, a.jsx)("div", {
									className: "text-center",
									children: (0, a.jsx)(o.$, {
										onClick: i,
										className:
											"bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide",
										children: "Send Booking Offer",
									}),
								}),
							],
						}),
					});
				};
			var x = i(1018);
			const g = (0, x.A)("ArrowRight", [
					["path", { d: "M5 12h14", key: "1ays0h" }],
					["path", { d: "m12 5 7 7-7 7", key: "xquz4c" }],
				]),
				h = (0, x.A)("ArrowDown", [
					["path", { d: "M12 5v14", key: "s699le" }],
					["path", { d: "m19 12-7 7-7-7", key: "1idqje" }],
				]),
				m = (0, x.A)("Maximize2", [
					["polyline", { points: "15 3 21 3 21 9", key: "mznyad" }],
					["polyline", { points: "9 21 3 21 3 15", key: "1avn1i" }],
					["line", { x1: "21", x2: "14", y1: "3", y2: "10", key: "ota7mn" }],
					["line", { x1: "3", x2: "10", y1: "21", y2: "14", key: "1atl0r" }],
				]),
				p = (e) => {
					const {
						activeTab: t,
						onTabChange: i,
						photoCount: s,
						assignmentCount: l,
						totalIncomeFormatted: r,
						archiveCount: n,
						isSelectionMode: c,
						onToggleSelectionMode: d,
						onMergeTiles: x,
						onClearSelection: p,
						onConvertToText: u,
						onConvertToImage: f,
						selectedTilesCount: y,
					} = e;
					return (0, a.jsx)("div", {
						className: "border-b border-gray-100",
						children: (0, a.jsxs)("div", {
							className: "max-w-4xl mx-auto px-6 py-8",
							children: [
								(0, a.jsxs)("div", {
									className: "flex items-center justify-center gap-12 mb-8",
									children: [
										(0, a.jsxs)("button", {
											onClick: () => i("photos"),
											className: "text-sm font-light tracking-wide ".concat(
												"photos" === t ? "text-black" : "text-gray-400",
											),
											children: ["Work (", s, ")"],
										}),
										(0, a.jsxs)("button", {
											onClick: () => i("assignments"),
											className: "text-sm font-light tracking-wide ".concat(
												"assignments" === t ? "text-black" : "text-gray-400",
											),
											children: ["Assignments (", l, ")"],
										}),
										(0, a.jsxs)("button", {
											onClick: () => i("income"),
											className: "text-sm font-light tracking-wide ".concat(
												"income" === t ? "text-black" : "text-gray-400",
											),
											children: ["Income (", r, ")"],
										}),
										(0, a.jsxs)("button", {
											onClick: () => i("archive"),
											className: "text-sm font-light tracking-wide ".concat(
												"archive" === t ? "text-black" : "text-gray-400",
											),
											children: ["Archive (", n, ")"],
										}),
									],
								}),
								"photos" === t &&
									d &&
									x &&
									p &&
									u &&
									f &&
									(0, a.jsxs)("div", {
										className: "flex items-center justify-center gap-4",
										children: [
											(0, a.jsx)(o.$, {
												variant: c ? "default" : "outline",
												size: "sm",
												onClick: d,
												className: "text-xs font-light tracking-wide",
												children: c ? "Exit Selection" : "Select Tiles",
											}),
											c &&
												(0, a.jsxs)(a.Fragment, {
													children: [
														(0, a.jsxs)(o.$, {
															variant: "outline",
															size: "sm",
															onClick: () => x("horizontal"),
															disabled: 2 > (y || 0),
															className: "text-xs font-light tracking-wide",
															children: [
																(0, a.jsx)(g, { className: "w-3 h-3 mr-1" }),
																"Horizontal",
															],
														}),
														(0, a.jsxs)(o.$, {
															variant: "outline",
															size: "sm",
															onClick: () => x("vertical"),
															disabled: 2 > (y || 0),
															className: "text-xs font-light tracking-wide",
															children: [
																(0, a.jsx)(h, { className: "w-3 h-3 mr-1" }),
																"Vertical",
															],
														}),
														(y || 0) >= 4 &&
															(0, a.jsxs)(o.$, {
																variant: "outline",
																size: "sm",
																onClick: () => x("large"),
																className: "text-xs font-light tracking-wide",
																children: [
																	(0, a.jsx)(m, { className: "w-3 h-3 mr-1" }),
																	"Large",
																],
															}),
														(0, a.jsx)(o.$, {
															variant: "outline",
															size: "sm",
															onClick: p,
															disabled: 0 === (y || 0),
															className: "text-xs font-light tracking-wide",
															children: "Clear",
														}),
														(0, a.jsx)(o.$, {
															variant: "outline",
															size: "sm",
															onClick: u,
															disabled: 0 === (y || 0),
															className: "text-xs font-light tracking-wide",
															children: "Convert to Text",
														}),
														(0, a.jsx)(o.$, {
															variant: "outline",
															size: "sm",
															onClick: f,
															disabled: 0 === (y || 0),
															className: "text-xs font-light tracking-wide",
															children: "Convert to Image",
														}),
													],
												}),
										],
									}),
							],
						}),
					});
				};
			var u = i(5186),
				f = i(9805);
			const y = (e) => {
					switch (e) {
						case "horizontal":
							return "col-span-2 row-span-1";
						case "vertical":
							return "col-span-1 row-span-2";
						case "large":
							return "col-span-2 row-span-2";
						default:
							return "col-span-1 row-span-1";
					}
				},
				b = (e) => {
					const {
							photos: t,
							selectedTiles: i,
							isSelectionMode: s,
							editingTile: l,
							tempText: r,
							onOpenPhoto: o,
							onToggleTileSelection: n,
							onStartEditingText: c,
							onSetTempText: d,
							onSaveTextEdit: x,
							onCancelTextEdit: g,
							splitTile: h,
							tempBackgroundColor: m,
							onSetTempBackgroundColor: p,
							tempFontSize: b,
							onSetTempFontSize: j,
							tempFontFamily: v,
							onSetTempFontFamily: w,
						} = e,
						k = [
							"Arial",
							"Verdana",
							"Georgia",
							"Times New Roman",
							"Courier New",
						],
						N = ["12px", "16px", "20px", "24px", "32px"];
					return (0, a.jsx)("div", {
						className: "grid grid-cols-3 gap-2 auto-rows-[200px]",
						children: t.map((e) =>
							(0, a.jsxs)(
								"div",
								{
									className: "relative group cursor-pointer "
										.concat(y(e.size), " ")
										.concat(i.has(e.id) ? "ring-2 ring-black" : "", " ")
										.concat(s ? "hover:ring-1 hover:ring-gray-400" : ""),
									onClick: () => {
										if ("text" === e.type && l !== e.id && !s) {
											c(e.id, e);
											return;
										}
										return s ? n(e.id) : o(e);
									},
									children: [
										"text" === e.type
											? (0, a.jsx)("div", {
													className:
														"w-full h-full flex items-center justify-center p-4 text-center relative",
													style: {
														backgroundColor:
															l === e.id ? m : e.backgroundColor || "#f3f4f6",
													},
													children:
														l === e.id
															? (0, a.jsxs)("div", {
																	className: "w-full h-full flex flex-col",
																	onClick: (e) => e.stopPropagation(),
																	children: [
																		(0, a.jsx)("textarea", {
																			value: r,
																			onChange: (e) => d(e.target.value),
																			className:
																				"flex-1 w-full p-2 border-none outline-none resize-none bg-transparent text-center font-light",
																			placeholder: "Enter your text...",
																			autoFocus: !0,
																			onClick: (e) => e.stopPropagation(),
																			style: {
																				backgroundColor: m,
																				fontSize: b,
																				fontFamily: v,
																				color: "#000000",
																			},
																		}),
																		(0, a.jsxs)("div", {
																			className: "grid grid-cols-3 gap-2 mt-2",
																			children: [
																				(0, a.jsxs)("div", {
																					children: [
																						(0, a.jsx)("label", {
																							htmlFor: "bgColor-".concat(e.id),
																							className:
																								"block text-xs font-light text-gray-600 mb-1",
																							children: "BG",
																						}),
																						(0, a.jsx)("input", {
																							type: "color",
																							id: "bgColor-".concat(e.id),
																							value: m,
																							onChange: (e) =>
																								p(e.target.value),
																							className:
																								"w-full h-8 p-0 border-none rounded cursor-pointer",
																						}),
																					],
																				}),
																				(0, a.jsxs)("div", {
																					children: [
																						(0, a.jsx)("label", {
																							htmlFor: "fontSize-".concat(e.id),
																							className:
																								"block text-xs font-light text-gray-600 mb-1",
																							children: "Size",
																						}),
																						(0, a.jsx)("select", {
																							id: "fontSize-".concat(e.id),
																							value: b,
																							onChange: (e) =>
																								j(e.target.value),
																							className:
																								"w-full p-1 border border-gray-300 rounded text-xs font-light h-8",
																							children: N.map((e) =>
																								(0, a.jsx)(
																									"option",
																									{ value: e, children: e },
																									e,
																								),
																							),
																						}),
																					],
																				}),
																				(0, a.jsxs)("div", {
																					children: [
																						(0, a.jsx)("label", {
																							htmlFor: "fontFamily-".concat(
																								e.id,
																							),
																							className:
																								"block text-xs font-light text-gray-600 mb-1",
																							children: "Font",
																						}),
																						(0, a.jsx)("select", {
																							id: "fontFamily-".concat(e.id),
																							value: v,
																							onChange: (e) =>
																								w(e.target.value),
																							className:
																								"w-full p-1 border border-gray-300 rounded text-xs font-light h-8 truncate",
																							children: k.map((e) =>
																								(0, a.jsx)(
																									"option",
																									{ value: e, children: e },
																									e,
																								),
																							),
																						}),
																					],
																				}),
																			],
																		}),
																		(0, a.jsxs)("div", {
																			className: "flex gap-1 mt-2",
																			children: [
																				(0, a.jsx)("button", {
																					onClick: (e) => {
																						e.stopPropagation(), x();
																					},
																					className:
																						"px-2 py-1 bg-black text-white text-xs rounded font-light",
																					children: "Save",
																				}),
																				(0, a.jsx)("button", {
																					onClick: (e) => {
																						e.stopPropagation(), g();
																					},
																					className:
																						"px-2 py-1 bg-gray-500 text-white text-xs rounded font-light",
																					children: "Cancel",
																				}),
																			],
																		}),
																	],
																})
															: (0, a.jsx)("p", {
																	className:
																		"text-gray-800 font-light break-words cursor-text z-20 relative whitespace-pre-line",
																	style: {
																		backgroundColor:
																			e.backgroundColor || "#f3f4f6",
																		fontSize: e.fontSize || "16px",
																		fontFamily: e.fontFamily || "Arial",
																		color: "#000000",
																	},
																	onClick: (t) => {
																		t.stopPropagation(), c(e.id, e);
																	},
																	children:
																		e.textContent || "Click to edit text",
																}),
												})
											: (0, a.jsx)(u.default, {
													src: e.src || "/placeholder.svg",
													alt: e.alt,
													fill: !0,
													className: "object-cover",
													sizes:
														"(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw",
												}),
										(0, a.jsxs)("div", {
											className: "absolute top-2 left-2 flex gap-1 z-20",
											children: [
												e.isMerged &&
													(0, a.jsx)("div", {
														className:
															"bg-black text-white rounded-full p-1 cursor-pointer",
														onClick: (t) => {
															t.stopPropagation(), h(e);
														},
														children: (0, a.jsx)(f.A, { className: "w-3 h-3" }),
													}),
												"text" === e.type &&
													s &&
													(0, a.jsx)("div", {
														className: "bg-black text-white rounded-full p-1",
														title: "Text Tile",
														children: (0, a.jsx)("span", {
															className: "text-xs font-light",
															children: "T",
														}),
													}),
											],
										}),
									],
								},
								e.id,
							),
						),
					});
				},
				j = (e) => {
					let {
							viewMode: t,
							onViewModeChange: i,
							jobs: s,
							receivedOffers: l,
							sentOffers: r,
						} = e,
						n = "Upcoming Assignments",
						c = s;
					"receivedOffers" === t
						? ((n = "Offers Received"), (c = l))
						: "sentOffers" === t && ((n = "Offers Sent"), (c = r));
					const d = (e) => {
						switch (e) {
							case "confirmed":
							case "accepted":
								return "bg-green-100 text-green-800";
							case "pending":
							case "negotiating":
								return "bg-yellow-100 text-yellow-800";
							case "declined":
							case "expired":
							case "withdrawn":
								return "bg-red-100 text-red-800";
							case "completed":
								return "bg-blue-100 text-blue-800";
							default:
								return "bg-gray-100 text-gray-800";
						}
					};
					return (0, a.jsxs)("div", {
						className: "space-y-6",
						children: [
							(0, a.jsxs)("div", {
								className: "flex justify-center gap-4 mb-8",
								children: [
									(0, a.jsxs)(o.$, {
										variant: "upcoming" === t ? "default" : "outline",
										onClick: () => i("upcoming"),
										className: "font-light tracking-wide",
										children: ["Upcoming Assignments (", s.length, ")"],
									}),
									(0, a.jsxs)(o.$, {
										variant: "receivedOffers" === t ? "default" : "outline",
										onClick: () => i("receivedOffers"),
										className: "font-light tracking-wide",
										children: ["Offers Received (", l.length, ")"],
									}),
									(0, a.jsxs)(o.$, {
										variant: "sentOffers" === t ? "default" : "outline",
										onClick: () => i("sentOffers"),
										className: "font-light tracking-wide",
										children: ["Offers Sent (", r.length, ")"],
									}),
								],
							}),
							(0, a.jsx)("h2", {
								className: "text-xl font-light tracking-wide text-center mb-12",
								children: n,
							}),
							0 === c.length
								? (0, a.jsx)("p", {
										className: "text-center text-gray-500 font-light",
										children: "No items to display in this view.",
									})
								: c.map((e) =>
										"time" in e
											? (0, a.jsx)(
													"div",
													{
														className:
															"border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg",
														children: (0, a.jsxs)("div", {
															className: "flex items-start justify-between",
															children: [
																(0, a.jsxs)("div", {
																	className: "flex-1",
																	children: [
																		(0, a.jsx)("h3", {
																			className:
																				"font-light tracking-wide text-lg mb-2",
																			children: e.title,
																		}),
																		(0, a.jsxs)("p", {
																			className:
																				"text-gray-600 mb-1 font-light tracking-wide",
																			children: ["Client: ", e.client],
																		}),
																		(0, a.jsxs)("div", {
																			className:
																				"flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2",
																			children: [
																				(0, a.jsxs)("span", {
																					children: ["Date: ", e.date],
																				}),
																				(0, a.jsxs)("span", {
																					children: ["Time: ", e.time],
																				}),
																				(0, a.jsxs)("span", {
																					children: ["Location: ", e.location],
																				}),
																				(0, a.jsxs)("span", {
																					children: ["Type: ", e.type],
																				}),
																			],
																		}),
																	],
																}),
																(0, a.jsx)("div", {
																	className: "text-right ml-4 flex-shrink-0",
																	children: (0, a.jsx)("span", {
																		className:
																			"px-3 py-1 text-xs font-light tracking-wide rounded-full ".concat(
																				d(e.status),
																			),
																		children:
																			e.status.charAt(0).toUpperCase() +
																			e.status.slice(1),
																	}),
																}),
															],
														}),
													},
													e.id,
												)
											: (0, a.jsx)(
													"div",
													{
														className:
															"border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg",
														children: (0, a.jsxs)("div", {
															className: "flex items-start justify-between",
															children: [
																(0, a.jsxs)("div", {
																	className: "flex-1",
																	children: [
																		(0, a.jsx)("h3", {
																			className:
																				"font-light tracking-wide text-lg mb-2",
																			children: e.projectTitle,
																		}),
																		(0, a.jsxs)("p", {
																			className:
																				"text-gray-600 mb-1 font-light tracking-wide",
																			children: [
																				e.isSentByMe ? "To: " : "From: ",
																				" ",
																				e.clientName,
																			],
																		}),
																		(0, a.jsxs)("p", {
																			className:
																				"text-gray-600 mb-1 font-light tracking-wide",
																			children: ["Role: ", e.role],
																		}),
																		(0, a.jsxs)("p", {
																			className:
																				"text-gray-600 mb-1 font-light tracking-wide",
																			children: [
																				"Amount: $",
																				e.amount.toLocaleString(),
																			],
																		}),
																		(0, a.jsxs)("div", {
																			className:
																				"flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2",
																			children: [
																				(0, a.jsxs)("span", {
																					children: ["Offered: ", e.offerDate],
																				}),
																				(0, a.jsxs)("span", {
																					children: ["Type: ", e.projectType],
																				}),
																				e.location &&
																					(0, a.jsxs)("span", {
																						children: [
																							"Location: ",
																							e.location,
																						],
																					}),
																			],
																		}),
																		e.description &&
																			(0, a.jsxs)("p", {
																				className:
																					"text-xs text-gray-500 mt-2 font-light italic",
																				children: ["Note: ", e.description],
																			}),
																	],
																}),
																(0, a.jsx)("div", {
																	className: "text-right ml-4 flex-shrink-0",
																	children: (0, a.jsx)("span", {
																		className:
																			"px-3 py-1 text-xs font-light tracking-wide rounded-full ".concat(
																				d(e.status),
																			),
																		children:
																			e.status.charAt(0).toUpperCase() +
																			e.status.slice(1),
																	}),
																}),
															],
														}),
													},
													e.id,
												),
									),
						],
					});
				},
				v = (e) => {
					const { payments: t, totalIncome: i, onViewInvoice: s } = e;
					return (0, a.jsxs)("div", {
						className: "space-y-8",
						children: [
							(0, a.jsxs)("div", {
								className: "text-center mb-12",
								children: [
									(0, a.jsx)("h2", {
										className: "text-xl font-light tracking-wide mb-6",
										children: "Total Income",
									}),
									(0, a.jsxs)("div", {
										className: "text-4xl font-light text-green-600 mb-3",
										children: ["$", i.toLocaleString()],
									}),
									(0, a.jsx)("p", {
										className: "text-gray-600 font-light tracking-wide",
										children: "Lifetime Earnings",
									}),
								],
							}),
							(0, a.jsx)("div", {
								className:
									"overflow-x-auto overflow-y-auto max-h-96 border border-gray-100 rounded",
								children: (0, a.jsxs)("table", {
									className: "w-full",
									children: [
										(0, a.jsx)("thead", {
											className: "bg-gray-50 sticky top-0",
											children: (0, a.jsxs)("tr", {
												children: [
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Job",
													}),
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Client",
													}),
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Amount",
													}),
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Date",
													}),
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Status",
													}),
													(0, a.jsx)("th", {
														className:
															"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
														children: "Invoice",
													}),
												],
											}),
										}),
										(0, a.jsx)("tbody", {
											className: "bg-white divide-y divide-gray-100",
											children: t.map((e) =>
												(0, a.jsxs)(
													"tr",
													{
														className: "hover:bg-gray-50",
														children: [
															(0, a.jsx)("td", {
																className:
																	"px-6 py-4 whitespace-nowrap text-sm font-light text-gray-900",
																children: e.jobTitle,
															}),
															(0, a.jsx)("td", {
																className:
																	"px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light",
																children: e.client,
															}),
															(0, a.jsxs)("td", {
																className:
																	"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-light",
																children: ["$", e.amount.toLocaleString()],
															}),
															(0, a.jsx)("td", {
																className:
																	"px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light",
																children: e.date,
															}),
															(0, a.jsx)("td", {
																className: "px-6 py-4 whitespace-nowrap",
																children: (0, a.jsx)("span", {
																	className:
																		"px-2 py-1 text-xs font-light tracking-wide rounded-full ".concat(
																			"paid" === e.status
																				? "bg-green-100 text-green-800"
																				: "bg-yellow-100 text-yellow-800",
																		),
																	children:
																		e.status.charAt(0).toUpperCase() +
																		e.status.slice(1),
																}),
															}),
															(0, a.jsx)("td", {
																className:
																	"px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-light",
																children: (0, a.jsx)("button", {
																	onClick: () => s(e.invoiceNumber),
																	className: "hover:text-blue-800 underline",
																	children: e.invoiceNumber,
																}),
															}),
														],
													},
													e.id,
												),
											),
										}),
									],
								}),
							}),
						],
					});
				},
				w = (e) => {
					const { payments: t } = e;
					return (0, a.jsxs)("div", {
						className: "space-y-6",
						children: [
							(0, a.jsx)("h2", {
								className: "text-xl font-light tracking-wide text-center mb-12",
								children: "Completed Work",
							}),
							t.map((e) =>
								(0, a.jsx)(
									"div",
									{
										className:
											"border border-gray-100 p-6 hover:shadow-sm transition-shadow",
										children: (0, a.jsxs)("div", {
											className: "flex items-start justify-between",
											children: [
												(0, a.jsxs)("div", {
													className: "flex-1",
													children: [
														(0, a.jsx)("h3", {
															className:
																"font-light tracking-wide text-lg mb-2",
															children: e.jobTitle,
														}),
														(0, a.jsxs)("p", {
															className:
																"text-gray-600 mb-2 font-light tracking-wide",
															children: ["Client: ", e.client],
														}),
														(0, a.jsx)("div", {
															className:
																"text-sm text-gray-500 font-light tracking-wide",
															children: e.date,
														}),
													],
												}),
												(0, a.jsx)("div", {
													className: "text-right",
													children: (0, a.jsx)("span", {
														className:
															"px-3 py-1 text-xs font-light tracking-wide ".concat(
																"paid" === e.status
																	? "bg-green-100 text-green-800"
																	: "bg-yellow-100 text-yellow-800",
															),
														children:
															e.status.charAt(0).toUpperCase() +
															e.status.slice(1),
													}),
												}),
											],
										}),
									},
									e.id,
								),
							),
						],
					});
				};
			var k = i(8381);
			const N = (e) => {
					var t, i;
					const {
						show: s,
						onClose: l,
						bookingForm: r,
						onFormChange: n,
						onFormProjectSelect: c,
						onSubmit: d,
						projects: x,
						isSubmitting: g,
					} = e;
					return s
						? (0, a.jsx)("div", {
								className:
									"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
								children: (0, a.jsxs)("div", {
									className:
										"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
									children: [
										(0, a.jsxs)("div", {
											className:
												"flex items-center justify-between p-6 border-b border-gray-100",
											children: [
												(0, a.jsx)("h2", {
													className: "text-xl font-light tracking-wide",
													children: "Send Booking Offer",
												}),
												(0, a.jsx)("button", {
													onClick: l,
													children: (0, a.jsx)(k.A, { className: "w-5 h-5" }),
												}),
											],
										}),
										(0, a.jsxs)("form", {
											onSubmit: d,
											className: "p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
											children: [
												(0, a.jsxs)("div", {
													className: "space-y-6",
													children: [
														(0, a.jsxs)("div", {
															children: [
																(0, a.jsx)("h3", {
																	className:
																		"text-lg font-light tracking-wide mb-4",
																	children: "Select Project",
																}),
																(0, a.jsxs)("div", {
																	className: "mb-4",
																	children: [
																		(0, a.jsx)("label", {
																			className:
																				"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																			children: "Project *",
																		}),
																		(0, a.jsxs)("select", {
																			required: !0,
																			value: r.projectId || "",
																			onChange: (e) => {
																				c(
																					x.find(
																						(t) => t.id === e.target.value,
																					),
																				);
																			},
																			className:
																				"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																			children: [
																				(0, a.jsx)("option", {
																					value: "",
																					children: "Select a project",
																				}),
																				x.map((e) =>
																					(0, a.jsxs)(
																						"option",
																						{
																							value: e.id,
																							children: [
																								e.title,
																								" - ",
																								e.client,
																							],
																						},
																						e.id,
																					),
																				),
																			],
																		}),
																	],
																}),
															],
														}),
														r.projectId &&
															(0, a.jsxs)("div", {
																children: [
																	(0, a.jsx)("h3", {
																		className:
																			"text-lg font-light tracking-wide mb-4",
																		children: "Project Details",
																	}),
																	(0, a.jsxs)("div", {
																		className:
																			"grid grid-cols-1 md:grid-cols-2 gap-4",
																		children: [
																			(0, a.jsxs)("div", {
																				children: [
																					(0, a.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Project Title",
																					}),
																					(0, a.jsx)("input", {
																						type: "text",
																						value: r.projectTitle,
																						readOnly: !0,
																						className:
																							"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																					}),
																				],
																			}),
																			(0, a.jsxs)("div", {
																				children: [
																					(0, a.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Project Type",
																					}),
																					(0, a.jsx)("input", {
																						type: "text",
																						value: r.projectType,
																						readOnly: !0,
																						className:
																							"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																					}),
																				],
																			}),
																		],
																	}),
																	(0, a.jsxs)("div", {
																		className:
																			"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",
																		children: [
																			(0, a.jsxs)("div", {
																				children: [
																					(0, a.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Start Date",
																					}),
																					(0, a.jsx)("input", {
																						type: "text",
																						value: r.startDate,
																						readOnly: !0,
																						className:
																							"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																					}),
																				],
																			}),
																			(0, a.jsxs)("div", {
																				children: [
																					(0, a.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "End Date",
																					}),
																					(0, a.jsx)("input", {
																						type: "text",
																						value: r.endDate,
																						readOnly: !0,
																						className:
																							"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																					}),
																				],
																			}),
																			(0, a.jsxs)("div", {
																				children: [
																					(0, a.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Budget (USD)",
																					}),
																					(0, a.jsx)("input", {
																						type: "text",
																						value: "$".concat(
																							Number.parseInt(
																								r.budget || "0",
																							).toLocaleString(),
																						),
																						readOnly: !0,
																						className:
																							"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																					}),
																				],
																			}),
																		],
																	}),
																	(0, a.jsxs)("div", {
																		className: "mt-4",
																		children: [
																			(0, a.jsx)("label", {
																				className:
																					"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																				children: "Location",
																			}),
																			(0, a.jsx)("input", {
																				type: "text",
																				value: r.location,
																				readOnly: !0,
																				className:
																					"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																			}),
																		],
																	}),
																	(0, a.jsxs)("div", {
																		className: "mt-4",
																		children: [
																			(0, a.jsx)("label", {
																				className:
																					"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																				children: "Description",
																			}),
																			(0, a.jsx)("textarea", {
																				rows: 3,
																				value: r.description,
																				readOnly: !0,
																				className:
																					"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																			}),
																		],
																	}),
																],
															}),
														r.projectId &&
															(0, a.jsxs)("div", {
																children: [
																	(0, a.jsx)("h3", {
																		className:
																			"text-lg font-light tracking-wide mb-4",
																		children: "Offer Details",
																	}),
																	(0, a.jsxs)("div", {
																		className: "mb-4",
																		children: [
																			(0, a.jsx)("label", {
																				className:
																					"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																				children: "Role *",
																			}),
																			(0, a.jsxs)("select", {
																				required: !0,
																				value: r.role || "",
																				onChange: (e) =>
																					n("role", e.target.value),
																				className:
																					"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																				children: [
																					(0, a.jsx)("option", {
																						value: "",
																						children: "Select Role",
																					}),
																					(null ===
																						(i = x.find(
																							(e) => e.id === r.projectId,
																						)) || void 0 === i
																						? void 0
																						: null ===
																									(t = i.creativesNeeded) ||
																								void 0 === t
																							? void 0
																							: t.map((e, t) =>
																									(0, a.jsx)(
																										"option",
																										{ value: e, children: e },
																										t,
																									),
																								)) ||
																						(0, a.jsxs)(a.Fragment, {
																							children: [
																								(0, a.jsx)("option", {
																									value: "Photographer",
																									children: "Photographer",
																								}),
																								(0, a.jsx)("option", {
																									value: "Videographer",
																									children: "Videographer",
																								}),
																								(0, a.jsx)("option", {
																									value: "Model",
																									children: "Model",
																								}),
																								(0, a.jsx)("option", {
																									value: "Makeup Artist",
																									children: "Makeup Artist",
																								}),
																								(0, a.jsx)("option", {
																									value: "Hair Stylist",
																									children: "Hair Stylist",
																								}),
																								(0, a.jsx)("option", {
																									value: "Stylist",
																									children: "Stylist",
																								}),
																							],
																						}),
																				],
																			}),
																		],
																	}),
																	(0, a.jsxs)("div", {
																		className: "mt-4",
																		children: [
																			(0, a.jsx)("label", {
																				className:
																					"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																				children: "Additional Notes",
																			}),
																			(0, a.jsx)("textarea", {
																				rows: 3,
																				value: r.notes || "",
																				onChange: (e) =>
																					n("notes", e.target.value),
																				className:
																					"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																				placeholder:
																					"Any specific requirements or details for this creative...",
																			}),
																		],
																	}),
																],
															}),
														(0, a.jsxs)("div", {
															children: [
																" ",
																(0, a.jsx)("h3", {
																	className:
																		"text-lg font-light tracking-wide mb-4",
																	children: "Rate Information",
																}),
																(0, a.jsxs)("div", {
																	className:
																		"grid grid-cols-1 md:grid-cols-2 gap-4",
																	children: [
																		(0, a.jsxs)("div", {
																			children: [
																				(0, a.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Proposed Rate *",
																				}),
																				(0, a.jsx)("input", {
																					type: "number",
																					required: !0,
																					value: r.proposedRate,
																					onChange: (e) =>
																						n("proposedRate", e.target.value),
																					className:
																						"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																					placeholder: "1500",
																				}),
																			],
																		}),
																		(0, a.jsxs)("div", {
																			children: [
																				(0, a.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Rate Type *",
																				}),
																				(0, a.jsxs)("select", {
																					required: !0,
																					value: r.rateType,
																					onChange: (e) =>
																						n("rateType", e.target.value),
																					className:
																						"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																					children: [
																						(0, a.jsx)("option", {
																							value: "",
																							children: "Select Rate Type",
																						}),
																						(0, a.jsx)("option", {
																							value: "hourly",
																							children: "Per Hour",
																						}),
																						(0, a.jsx)("option", {
																							value: "daily",
																							children: "Per Day",
																						}),
																						(0, a.jsx)("option", {
																							value: "project",
																							children: "Per Project",
																						}),
																						(0, a.jsx)("option", {
																							value: "weekly",
																							children: "Per Week",
																						}),
																						(0, a.jsx)("option", {
																							value: "monthly",
																							children: "Per Month",
																						}),
																					],
																				}),
																			],
																		}),
																	],
																}),
															],
														}),
													],
												}),
												(0, a.jsxs)("div", {
													className:
														"flex gap-4 mt-8 pt-6 border-t border-gray-100",
													children: [
														(0, a.jsx)(o.$, {
															type: "button",
															variant: "outline",
															onClick: l,
															className: "flex-1 font-light tracking-wide",
															disabled: g,
															children: "Cancel",
														}),
														(0, a.jsxs)(o.$, {
															type: "submit",
															className:
																"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
															disabled: !r.projectId || g,
															children: [g ? "Sending..." : "Send Offer", " "],
														}),
													],
												}),
											],
										}),
									],
								}),
							})
						: null;
				},
				C = (0, x.A)("Bookmark", [
					[
						"path",
						{
							d: "m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",
							key: "1fy3hk",
						},
					],
				]),
				S = (e) => {
					const { photo: t, onClose: i, likedPhotos: s, onToggleLike: l } = e;
					return t
						? (0, a.jsx)("div", {
								className:
									"fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm",
								children: (0, a.jsxs)("div", {
									className:
										"bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row shadow-xl",
									children: [
										(0, a.jsx)("div", {
											className: "flex-1 relative min-h-[300px]",
											children: (0, a.jsx)(u.default, {
												src: t.src || "/placeholder.svg",
												alt: t.alt,
												fill: !0,
												className: "object-cover",
											}),
										}),
										(0, a.jsxs)("div", {
											className: "w-full md:w-80 flex flex-col",
											children: [
												(0, a.jsxs)("div", {
													className:
														"flex items-center justify-between p-4 border-b border-gray-100",
													children: [
														(0, a.jsx)("div", {
															className: "flex items-center gap-3",
															children: (0, a.jsx)("span", {
																className: "font-light tracking-wide text-lg",
																children: t.alt,
															}),
														}),
														(0, a.jsx)("button", {
															onClick: i,
															"aria-label": "Close modal",
															children: (0, a.jsx)(k.A, {
																className: "w-5 h-5",
															}),
														}),
													],
												}),
												(0, a.jsx)("div", {
													className: "flex-1 p-4 overflow-y-auto",
												}),
												(0, a.jsx)("div", {
													className: "border-t border-gray-100 p-4",
													children: (0, a.jsx)("div", {
														className: "flex items-center justify-end mb-3",
														children: (0, a.jsx)(C, { className: "w-6 h-6" }),
													}),
												}),
											],
										}),
									],
								}),
							})
						: null;
				},
				T = () => {
					const [e, t] = (0, s.useState)(new Set()),
						[i, a] = (0, s.useState)(!1),
						l = (0, s.useCallback)(
							(e) => {
								i &&
									t((t) => {
										const i = new Set(t);
										return i.has(e) ? i.delete(e) : i.add(e), i;
									});
							},
							[i],
						),
						r = (0, s.useCallback)(() => {
							t(new Set());
						}, []),
						o = (0, s.useCallback)(() => {
							a((e) => {
								const i = !e;
								return i || t(new Set()), i;
							});
						}, []);
					return {
						selectedTiles: e,
						isSelectionMode: i,
						selectedTilesCount: e.size,
						toggleTileSelection: l,
						clearSelection: r,
						toggleSelectionMode: o,
					};
				},
				A = (e) => {
					const { setPhotos: t } = e,
						[i, a] = (0, s.useState)(null),
						[l, r] = (0, s.useState)(""),
						[o, n] = (0, s.useState)("#f3f4f6"),
						[c, d] = (0, s.useState)("16px"),
						[x, g] = (0, s.useState)("Arial"),
						h = (0, s.useCallback)((e, t) => {
							a(e),
								r(t.textContent || ""),
								n(t.backgroundColor || "#f3f4f6"),
								d(t.fontSize || "16px"),
								g(t.fontFamily || "Arial");
						}, []),
						m = (0, s.useCallback)((e) => {
							r(e);
						}, []),
						p = (0, s.useCallback)((e) => {
							n(e);
						}, []),
						u = (0, s.useCallback)((e) => {
							d(e);
						}, []),
						f = (0, s.useCallback)((e) => {
							g(e);
						}, []),
						y = (0, s.useCallback)(() => {
							i &&
								(t((e) =>
									e.map((e) =>
										e.id === i
											? {
													...e,
													textContent: l || "Empty text",
													backgroundColor: o,
													fontSize: c,
													fontFamily: x,
												}
											: e,
									),
								),
								a(null));
						}, [i, l, o, c, x, t]);
					return {
						editingTile: i,
						tempText: l,
						startEditingText: h,
						handleSetTempText: m,
						saveTextEdit: y,
						cancelTextEdit: (0, s.useCallback)(() => {
							a(null), r(""), n("#f3f4f6"), d("16px"), g("Arial");
						}, []),
						tempBackgroundColor: o,
						handleSetTempBackgroundColor: p,
						tempFontSize: c,
						handleSetTempFontSize: u,
						tempFontFamily: x,
						handleSetTempFontFamily: f,
					};
				},
				M = (e) => {
					const {
							photos: t,
							setPhotos: i,
							selectedTiles: a,
							clearSelection: l,
							isSelectionMode: r,
							toggleSelectionMode: o,
						} = e,
						n = (0, s.useCallback)(
							(e) => {
								let s, n, c, d;
								if (a.size < 2) {
									console.warn(
										"Merge attempt with fewer than 2 tiles selected.",
									);
									return;
								}
								const x = t
									.filter((e) => a.has(e.id))
									.sort((e, t) => (e.position || 0) - (t.position || 0));
								if (0 === x.length) {
									console.error(
										"Selected photos array is empty despite selectedTiles having items.",
									);
									return;
								}
								"large" === e &&
									(console.log("[Large Merge Debug] Initiating large merge."),
									console.log(
										"[Large Merge Debug] Selected tiles count (from selectedTiles.size):",
										a.size,
									),
									console.log(
										"[Large Merge Debug] Selected photos array length:",
										x.length,
									),
									console.log(
										"[Large Merge Debug] Selected photo IDs:",
										x.map((e) => e.id),
									),
									console.log(
										"[Large Merge Debug] Selected photo positions:",
										x.map((e) => e.position),
									));
								let g = x[0],
									h = x.find((e) => "image" === e.type || void 0 === e.type),
									m = "Merged content";
								h
									? ((s = "image"),
										(n = h.src),
										(m = h.alt),
										(c = void 0),
										(d = void 0))
									: ((s = "text"),
										(n = "/placeholder.svg?text=Merged+Text"),
										(m = "Merged text content"),
										(c =
											x.map((e) => e.textContent || "").join("\n") ||
											"Merged text"),
										(d = g.backgroundColor || "#f3f4f6")),
									"large" === e &&
										(console.log(
											"[Large Merge Debug] Leftmost overall photo ID for position:",
											g.id,
											"pos:",
											g.position,
										),
										console.log(
											"[Large Merge Debug] First potential image for content ID:",
											h ? h.id : "None",
										),
										console.log(
											"[Large Merge Debug] Determined mergedType:",
											s,
											"finalSrc:",
											n,
										));
								const p = g.position || 0,
									u = {
										id: "merged-".concat(Date.now()),
										src: n,
										alt: m,
										likes: x.reduce((e, t) => e + (t.likes || 0), 0),
										comments: x.reduce((e, t) => e + (t.comments || 0), 0),
										size: e,
										isMerged: !0,
										originalPhotos: [...x],
										position: p,
										type: s,
										textContent: c,
										backgroundColor: d,
									};
								"large" === e &&
									console.log(
										"[Large Merge Debug] Created mergedPhoto object:",
										JSON.parse(JSON.stringify(u)),
									);
								const f = [...t.filter((e) => !a.has(e.id)), u].sort(
									(e, t) => (e.position || 0) - (t.position || 0),
								);
								"large" === e &&
									console.log(
										"[Large Merge Debug] New photos array length:",
										f.length,
									),
									i(f),
									l(),
									r && o(),
									"large" === e &&
										console.log(
											"[Large Merge Debug] Large merge process completed.",
										);
							},
							[t, a, i, l, r, o],
						),
						c = (0, s.useCallback)(
							(e) => {
								if (!e.isMerged || !e.originalPhotos) return;
								const a = e.position || 0;
								i(
									[
										...t.filter((t) => t.id !== e.id),
										...e.originalPhotos.map((e) => ({
											...e,
											position: null != e.position ? e.position : a,
											isMerged: !1,
											size: e.size || "standard",
											src: e.src,
										})),
									].sort((e, t) => (e.position || 0) - (t.position || 0)),
								);
							},
							[t, i],
						);
					return {
						mergeTiles: n,
						splitTile: c,
						convertToTextTile: (0, s.useCallback)(
							(e) => {
								i((t) =>
									t.map((t) =>
										t.id === e
											? {
													...t,
													type: "text",
													textContent: t.textContent || "Click to edit text",
													backgroundColor: t.backgroundColor || "#f3f4f6",
													src: "/placeholder.svg?text=Text+Content",
													fontSize: t.fontSize || "16px",
													fontFamily: t.fontFamily || "Arial",
												}
											: t,
									),
								);
							},
							[i],
						),
						convertToImageTile: (0, s.useCallback)(
							(e) => {
								i((t) =>
									t.map((t) => {
										if (t.id === e) {
											var i, a;
											const e =
												null === (a = t.originalPhotos) || void 0 === a
													? void 0
													: null === (i = a.find((e) => "image" === e.type)) ||
															void 0 === i
														? void 0
														: i.src;
											return {
												...t,
												type: "image",
												textContent: void 0,
												backgroundColor: void 0,
												src: t.src || e || "/placeholder.svg?text=Image",
											};
										}
										return t;
									}),
								);
							},
							[i],
						),
					};
				},
				D = {
					projectId: "",
					projectTitle: "",
					projectType: "",
					startDate: "",
					endDate: "",
					location: "",
					budget: "",
					description: "",
					role: "",
					proposedRate: "",
					rateType: "",
					notes: "",
				},
				P = () => {
					const [e, t] = (0, s.useState)(!1),
						[i, a] = (0, s.useState)(D),
						[l, r] = (0, s.useState)(!1),
						o = (0, s.useCallback)(() => {
							t(!0);
						}, []),
						n = (0, s.useCallback)(() => {
							t(!1), a(D);
						}, []),
						c = (0, s.useCallback)((e, t) => {
							a((i) => ({ ...i, [e]: t }));
						}, []),
						d = (0, s.useCallback)((e) => {
							e
								? a({
										...D,
										projectId: e.id,
										projectTitle: e.title,
										projectType: e.projectType || "",
										startDate: e.startDate || "",
										endDate: e.endDate || "",
										location: e.location || "",
										budget: e.budget.toString(),
										description: e.description || "",
									})
								: a(D);
						}, []),
						x = (0, s.useCallback)(
							(e) => {
								e.preventDefault(),
									l ||
										(r(!0),
										setTimeout(() => {
											console.log(
												"Booking offer submitted (simulated API call finished):",
												i,
											),
												n(),
												r(!1);
										}, 1500));
							},
							[i, n, l],
						);
					return {
						showBookingModal: e,
						bookingForm: i,
						isSubmitting: l,
						openBookingModal: o,
						closeBookingModal: n,
						updateBookingFormField: c,
						handleBookingFormProjectSelect: d,
						handleBookingSubmit: x,
					};
				},
				z = (e) => {
					const { initialLikedPhotos: t, onToggleLike: i } = e,
						[a, l] = (0, s.useState)(null);
					return {
						selectedPhoto: a,
						openPhotoDetail: (0, s.useCallback)((e) => {
							l(e);
						}, []),
						closePhotoDetail: (0, s.useCallback)(() => {
							l(null);
						}, []),
					};
				};
			var L = i(7753);
			function O(e) {
				const { artistId: t = "1" } = e,
					[i, o] = (0, s.useState)(l.qD),
					[n, c] = (0, s.useState)(new Set()),
					[x, g] = (0, s.useState)("photos"),
					[h, m] = (0, s.useState)(null),
					[u, f] = (0, s.useState)("upcoming"),
					{
						selectedTiles: y,
						isSelectionMode: k,
						selectedTilesCount: C,
						toggleTileSelection: D,
						clearSelection: O,
						toggleSelectionMode: I,
					} = T(),
					{
						editingTile: F,
						tempText: E,
						startEditingText: B,
						handleSetTempText: R,
						saveTextEdit: $,
						cancelTextEdit: _,
						tempBackgroundColor: V,
						handleSetTempBackgroundColor: J,
						tempFontSize: W,
						handleSetTempFontSize: H,
						tempFontFamily: U,
						handleSetTempFontFamily: q,
					} = A({ setPhotos: o }),
					{
						mergeTiles: G,
						splitTile: K,
						convertToTextTile: Z,
						convertToImageTile: X,
					} = M({
						photos: i,
						setPhotos: o,
						selectedTiles: y,
						clearSelection: O,
						isSelectionMode: k,
						toggleSelectionMode: I,
					}),
					{
						showBookingModal: Y,
						bookingForm: Q,
						openBookingModal: ee,
						closeBookingModal: et,
						updateBookingFormField: ei,
						handleBookingFormProjectSelect: ea,
						handleBookingSubmit: es,
						isSubmitting: el,
					} = P(),
					er = l.KT.reduce((e, t) => e + t.amount, 0),
					eo = (e) => {
						c((t) => {
							const i = new Set(t);
							return i.has(e) ? i.delete(e) : i.add(e), i;
						});
					},
					{
						selectedPhoto: en,
						openPhotoDetail: ec,
						closePhotoDetail: ed,
					} = z({ initialLikedPhotos: n, onToggleLike: eo }),
					ex = (0, s.useCallback)((e) => {
						m(e);
					}, []);
				(0, s.useCallback)(() => {
					m(null);
				}, []),
					(0, s.useEffect)(() => {
						console.log("Current photos:", i),
							console.log("Selected tiles:", Array.from(y));
					}, [i, y]);
				const eg = l.xk[t] || l.xk["1"];
				return (0, a.jsxs)("div", {
					className: "min-h-screen bg-white font-light",
					children: [
						(0, a.jsx)(L.A, {}),
						(0, a.jsx)(r, { creative: eg, photoCount: i.length }),
						(0, a.jsx)(d, { activeTab: x, onOpenBookingModal: ee }),
						(0, a.jsx)(p, {
							activeTab: x,
							onTabChange: g,
							photoCount: i.length,
							assignmentCount: l.Tg.length,
							totalIncomeFormatted: "$".concat(er.toLocaleString()),
							archiveCount: l.KT.length,
							isSelectionMode: k,
							onToggleSelectionMode: I,
							onMergeTiles: G,
							onClearSelection: O,
							onConvertToText: () => {
								Array.from(y).forEach((e) => Z(e)), O();
							},
							onConvertToImage: () => {
								Array.from(y).forEach((e) => X(e)), O();
							},
							selectedTilesCount: C,
						}),
						(0, a.jsxs)("div", {
							className: "max-w-4xl mx-auto px-6 py-16",
							children: [
								"photos" === x &&
									(0, a.jsx)(b, {
										photos: i,
										selectedTiles: y,
										isSelectionMode: k,
										editingTile: F,
										tempText: E,
										onOpenPhoto: (e) => {
											k || ec(e);
										},
										onToggleTileSelection: D,
										onStartEditingText: B,
										onSetTempText: R,
										onSaveTextEdit: $,
										onCancelTextEdit: _,
										splitTile: K,
										tempBackgroundColor: V,
										onSetTempBackgroundColor: J,
										tempFontSize: W,
										onSetTempFontSize: H,
										tempFontFamily: U,
										onSetTempFontFamily: q,
									}),
								"assignments" === x &&
									(0, a.jsx)(j, {
										viewMode: u,
										onViewModeChange: f,
										jobs: l.Tg,
										receivedOffers: l.TZ,
										sentOffers: l.Wm,
									}),
								"income" === x &&
									(0, a.jsx)(v, {
										payments: l.KT,
										totalIncome: er,
										onViewInvoice: ex,
									}),
								"archive" === x && (0, a.jsx)(w, { payments: l.KT }),
							],
						}),
						(0, a.jsx)(N, {
							show: Y,
							onClose: et,
							bookingForm: Q,
							onFormChange: ei,
							onFormProjectSelect: ea,
							onSubmit: es,
							projects: l.R4,
							isSubmitting: el,
						}),
						en &&
							(0, a.jsx)(S, {
								photo: en,
								onClose: ed,
								likedPhotos: n,
								onToggleLike: eo,
							}),
					],
				});
			}
		},
		1153: (e, t, i) => {
			i.d(t, { A: () => a });
			const a = (0, i(1018).A)("Menu", [
				["line", { x1: "4", x2: "20", y1: "12", y2: "12", key: "1e0a9i" }],
				["line", { x1: "4", x2: "20", y1: "6", y2: "6", key: "1owob3" }],
				["line", { x1: "4", x2: "20", y1: "18", y2: "18", key: "yk5zj1" }],
			]);
		},
		2950: (e, t, i) => {
			i.d(t, { A: () => a });
			const a = (0, i(1018).A)("Search", [
				["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
				["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
			]);
		},
		5160: (e, t, i) => {
			i.d(t, { $: () => c, r: () => n });
			var a = i(8081),
				s = i(2149),
				l = i(3629),
				r = i(3484),
				o = i(7687);
			const n = (0, r.F)(
					"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
					{
						variants: {
							variant: {
								default:
									"bg-primary text-primary-foreground hover:bg-primary/90",
								destructive:
									"bg-destructive text-destructive-foreground hover:bg-destructive/90",
								outline:
									"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
								secondary:
									"bg-secondary text-secondary-foreground hover:bg-secondary/80",
								ghost: "hover:bg-accent hover:text-accent-foreground",
								link: "text-primary underline-offset-4 hover:underline",
							},
							size: {
								default: "h-10 px-4 py-2",
								sm: "h-9 rounded-md px-3",
								lg: "h-11 rounded-md px-8",
								icon: "h-10 w-10",
							},
						},
						defaultVariants: { variant: "default", size: "default" },
					},
				),
				c = s.forwardRef((e, t) => {
					const {
							className: i,
							variant: s,
							size: r,
							asChild: c = !1,
							...d
						} = e,
						x = c ? l.DX : "button";
					return (0, a.jsx)(x, {
						className: (0, o.cn)(n({ variant: s, size: r, className: i })),
						ref: t,
						...d,
					});
				});
			c.displayName = "Button";
		},
		7687: (e, t, i) => {
			i.d(t, { cn: () => l });
			var a = i(6522),
				s = i(4483);
			function l() {
				for (var e = arguments.length, t = Array(e), i = 0; i < e; i++)
					t[i] = arguments[i];
				return (0, s.QP)((0, a.$)(t));
			}
		},
		7753: (e, t, i) => {
			i.d(t, { A: () => c });
			var a = i(8081),
				s = i(7950),
				l = i.n(s),
				r = i(5160),
				o = i(2950),
				n = i(1153);
			const c = () =>
				(0, a.jsx)("header", {
					className: "border-b border-gray-100",
					children: (0, a.jsx)("div", {
						className: "max-w-4xl mx-auto px-6 py-6",
						children: (0, a.jsxs)("div", {
							className: "flex items-center justify-between",
							children: [
								(0, a.jsx)(l(), {
									href: "/",
									className: "text-2xl font-light tracking-wide",
									children: "DUA",
								}),
								(0, a.jsxs)("nav", {
									className: "hidden md:flex items-center space-x-8 text-sm",
									children: [
										(0, a.jsx)(l(), {
											href: "/",
											className:
												"text-gray-600 hover:text-black transition-colors",
											children: "Search",
										}),
										(0, a.jsx)("span", {
											className: "text-black",
											children: "Portfolio",
										}),
										(0, a.jsx)("span", {
											className: "text-gray-600",
											children: "About",
										}),
										(0, a.jsx)("span", {
											className: "text-gray-600",
											children: "Contact",
										}),
										(0, a.jsx)(o.A, { className: "w-4 h-4 text-gray-600" }),
									],
								}),
								(0, a.jsx)(r.$, {
									variant: "ghost",
									size: "icon",
									className: "md:hidden",
									children: (0, a.jsx)(n.A, { className: "h-5 w-5" }),
								}),
							],
						}),
					}),
				});
		},
		8381: (e, t, i) => {
			i.d(t, { A: () => a });
			const a = (0, i(1018).A)("Ellipsis", [
				["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }],
				["circle", { cx: "19", cy: "12", r: "1", key: "1wjl8i" }],
				["circle", { cx: "5", cy: "12", r: "1", key: "1pcz8c" }],
			]);
		},
		9731: (e, t, i) => {
			Promise.resolve().then(i.bind(i, 602));
		},
		9805: (e, t, i) => {
			i.d(t, { A: () => a });
			const a = (0, i(1018).A)("Scissors", [
				["circle", { cx: "6", cy: "6", r: "3", key: "1lh9wr" }],
				["path", { d: "M8.12 8.12 12 12", key: "1alkpv" }],
				["path", { d: "M20 4 8.12 15.88", key: "xgtan2" }],
				["circle", { cx: "6", cy: "18", r: "3", key: "fqmcym" }],
				["path", { d: "M14.8 14.8 20 20", key: "ptml3r" }],
			]);
		},
	},
	(e) => {
		var t = (t) => e((e.s = t));
		e.O(0, [874, 186, 497, 954, 358], () => t(9731)), (_N_E = e.O());
	},
]);
