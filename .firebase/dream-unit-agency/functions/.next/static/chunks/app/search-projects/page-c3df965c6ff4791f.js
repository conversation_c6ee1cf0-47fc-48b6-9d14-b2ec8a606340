(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[988],
	{
		567: (e, t, a) => {
			a.r(t), a.d(t, { default: () => y });
			var r = a(8081),
				o = a(2149),
				s = a(5160),
				n = a(900),
				i = a(5904),
				l = a(6518),
				d = a(6319),
				c = a(6752),
				m = a(5186),
				u = a(5516),
				p = a(7950),
				f = a.n(p),
				g = a(7687);
			const h = [
					"FASHION EDITORIAL",
					"COMMERCIAL AD CAMPAIGN",
					"MUSIC VIDEO PRODUCTION",
					"DOCUMENTARY FILM",
					"EVENT PHOTOGRAPHY/VIDEOGRAPHY",
					"BRAND COLLABORATION",
					"PRODUCT SHOOT",
					"CORPORATE VIDEO",
				],
				x = [
					{
						id: "proj1",
						title: "Urban Dreams Fashion Editorial",
						description:
							"A cutting-edge fashion shoot exploring modern urban aesthetics. Looking for a dynamic team.",
						projectType: "FASHION EDITORIAL",
						location: "New York, NY",
						coordinates: { lat: 40.7128, lng: -74.006 },
						startDate: "2024-07-10",
						endDate: "2024-07-12",
						projectImageUrl: "/assets/mood_boards/mb1.png",
						clientOrBrandName: "Vogue Magazine",
						budgetOrCompensation: "$5,000 - $8,000",
					},
					{
						id: "proj2",
						title: "EcoGadget Launch Campaign",
						description:
							"Commercial ad campaign for a new sustainable tech gadget. Seeking videographer and models.",
						projectType: "COMMERCIAL AD CAMPAIGN",
						location: "San Francisco, CA",
						coordinates: { lat: 37.7749, lng: -122.4194 },
						startDate: "2024-08-01",
						endDate: "2024-08-05",
						projectImageUrl: "/assets/mood_boards/mb2.png",
						clientOrBrandName: "GreenTech Inc.",
						budgetOrCompensation: "Est. $15,000",
					},
					{
						id: "proj3",
						title: "Indie Artist Music Video",
						description:
							"Creative music video for an up-and-coming indie pop creative. Theme: Surreal Nostalgia.",
						projectType: "MUSIC VIDEO PRODUCTION",
						location: "Los Angeles, CA",
						coordinates: { lat: 34.0522, lng: -118.2437 },
						startDate: "2024-07-20",
						endDate: "2024-07-22",
						projectImageUrl: "/assets/mood_boards/mb3.png",
						clientOrBrandName: "Luna Bloom (Artist)",
						budgetOrCompensation: "Profit Share + Expenses",
					},
					{
						id: "proj4",
						title: "Wildlife Conservation Documentary",
						description:
							"Feature documentary on local wildlife conservation efforts. Requires experienced wildlife photographer.",
						projectType: "DOCUMENTARY FILM",
						location: "Denver, CO",
						coordinates: { lat: 39.7392, lng: -104.9903 },
						startDate: "2024-09-01",
						endDate: "2024-10-15",
						projectImageUrl: "/assets/mood_boards/mb4.png",
						clientOrBrandName: "Nature's Voice Foundation",
						budgetOrCompensation: "Grant Funded",
					},
					{
						id: "proj5",
						title: "Tech Conference Highlights",
						description:
							"Event photography and videography for a major tech conference. Fast turnaround needed.",
						projectType: "EVENT PHOTOGRAPHY/VIDEOGRAPHY",
						location: "Austin, TX",
						coordinates: { lat: 30.2672, lng: -97.7431 },
						startDate: "2024-07-25",
						endDate: "2024-07-27",
						projectImageUrl: "/assets/mood_boards/mb5.png",
						clientOrBrandName: "Innovate Summit",
						budgetOrCompensation: "$3,000",
					},
					{
						id: "proj6",
						title: "Summer Collection Brand Collab",
						description:
							"Lifestyle brand looking for models and a photographer for their summer collection launch.",
						projectType: "BRAND COLLABORATION",
						location: "Miami, FL",
						coordinates: { lat: 25.7617, lng: -80.1918 },
						startDate: "2024-07-01",
						endDate: "2024-07-05",
						projectImageUrl: "/assets/mood_boards/mb6.png",
						clientOrBrandName: "SunKissed Apparel",
						budgetOrCompensation: "Product + $1,500",
					},
					{
						id: "proj7",
						title: "Artisanal Coffee Product Shoot",
						description:
							"High-quality product photography for a new line of artisanal coffee beans.",
						projectType: "PRODUCT SHOOT",
						location: "Portland, OR",
						coordinates: { lat: 45.5051, lng: -122.675 },
						startDate: "2024-08-10",
						endDate: "2024-08-11",
						projectImageUrl: "/assets/mood_boards/mb7.png",
						clientOrBrandName: "Roast & Co.",
						budgetOrCompensation: "$1,200",
					},
					{
						id: "proj8",
						title: "Startup Success Story Video",
						description:
							"Corporate video showcasing the journey and success of a local tech startup.",
						projectType: "CORPORATE VIDEO",
						location: "Seattle, WA",
						coordinates: { lat: 47.6062, lng: -122.3321 },
						startDate: "2024-09-15",
						endDate: "2024-09-18",
						projectImageUrl: "/assets/mood_boards/mb8.png",
						clientOrBrandName: "Innovate Solutions Ltd.",
						budgetOrCompensation: "$4,500",
					},
				];
			function y() {
				const [e, t] = (0, o.useState)(),
					[a, p] = (0, o.useState)(""),
					[y, b] = (0, o.useState)(""),
					[v, j] = (0, o.useState)(""),
					[w, N] = (0, o.useState)(!1),
					[C, O] = (0, o.useState)([]),
					[I, D] = (0, o.useState)([]),
					A = (0, o.useMemo)(
						() =>
							x.filter((t) => {
								const r =
										"" === a ||
										t.title.toLowerCase().includes(a.toLowerCase()) ||
										t.description.toLowerCase().includes(a.toLowerCase()) ||
										t.location.toLowerCase().includes(a.toLowerCase()),
									o = "" === y || "all" === y || t.projectType === y;
								return (
									r &&
									o &&
									(() => {
										if (!e || (!e.from && !e.to)) return !0;
										if (e.from && !e.to) return new Date(t.endDate) >= e.from;
										if (!e.from && e.to) return new Date(t.startDate) <= e.to;
										if (e.from && e.to) {
											const a = new Date(t.startDate),
												r = new Date(t.endDate);
											return a <= e.to && r >= e.from;
										}
										return !0;
									})()
								);
							}),
						[x, a, y, e],
					);
				(0, o.useMemo)(() => {
					D(
						A.map((e, t) => ({
							...e,
							display: !0,
							colSpan: 1,
							rowSpan: 1,
							isPrimaryOfMerge: !1,
							constituentIds: [e.id],
							originalIndex: t,
						})),
					),
						O([]);
				}, [A]);
				const S = (e) => {
						w &&
							O((t) => (t.includes(e) ? t.filter((t) => t !== e) : [...t, e]));
					},
					L = (e) => {
						if (2 !== C.length) {
							alert("Please select exactly two tiles to merge.");
							return;
						}
						const [t, a] = C,
							r = I.find((e) => e.id === t),
							o = I.find((e) => e.id === a);
						if (!r || !o) {
							console.error("Selected projects not found in display config.");
							return;
						}
						const s = r.originalIndex < o.originalIndex ? r : o,
							n = r.originalIndex < o.originalIndex ? o : r;
						if (s.constituentIds.some((e) => n.constituentIds.includes(e))) {
							alert(
								"Cannot merge tiles that are already part of the same group or a tile with itself.",
							),
								O([]);
							return;
						}
						D((t) =>
							t.map((t) =>
								t.id === s.id
									? {
											...t,
											colSpan:
												"horizontal" === e ? s.colSpan + n.colSpan : s.colSpan,
											rowSpan:
												"vertical" === e ? s.rowSpan + n.rowSpan : s.rowSpan,
											isPrimaryOfMerge: !0,
											constituentIds: Array.from(
												new Set([...s.constituentIds, ...n.constituentIds]),
											),
											display: !0,
										}
									: t.id === n.id
										? { ...t, display: !1 }
										: t,
							),
						),
							O([]),
							console.log(
								""
									.concat(e, " merge: Primary: ")
									.concat(s.id, ", Secondary: ")
									.concat(n.id),
							);
					},
					P = (e) => {
						D((t) => {
							const a = t.find((t) => t.id === e && t.isPrimaryOfMerge);
							if (!a || !a.constituentIds) return t;
							const r = a.constituentIds;
							return t.map((e) =>
								r.includes(e.id)
									? {
											...(A.find((t) => t.id === e.id) || e),
											id: e.id,
											display: !0,
											colSpan: 1,
											rowSpan: 1,
											isPrimaryOfMerge: !1,
											constituentIds: [e.id],
											originalIndex: e.originalIndex,
										}
									: e,
							);
						}),
							O([]),
							console.log("Unmerged group associated with: ".concat(e));
					};
				return (0, r.jsxs)("div", {
					className: "min-h-screen bg-background flex flex-col",
					children: [
						(0, r.jsx)("header", {
							className: "bg-white border-b border-gray-200 sticky top-0 z-40",
							children: (0, r.jsx)("div", {
								className: "max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8",
								children: (0, r.jsxs)("div", {
									className: "flex items-center justify-between h-16",
									children: [
										(0, r.jsx)("div", {
											className: "flex items-center",
											children: (0, r.jsx)(f(), {
												href: "/",
												className:
													"text-2xl font-light tracking-wide text-gray-800",
												children: "DUA",
											}),
										}),
										(0, r.jsx)("div", {
											className: "flex items-center",
											children: (0, r.jsx)(f(), {
												href: "/creative/1",
												className:
													"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors",
												children: "Dashboard",
											}),
										}),
									],
								}),
							}),
						}),
						(0, r.jsx)("main", {
							className: "flex-1 overflow-y-auto p-6",
							children: (0, r.jsxs)("div", {
								className: "max-w-screen-xl mx-auto",
								children: [
									(0, r.jsxs)("div", {
										className: "mb-8 p-6 bg-white rounded-lg shadow",
										children: [
											(0, r.jsxs)("div", {
												className:
													"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end",
												children: [
													(0, r.jsx)(n.p, {
														placeholder: "Search by title, desc, or location",
														value: a,
														onChange: (e) => p(e.target.value),
														className: "border-gray-200 bg-white font-light",
													}),
													(0, r.jsxs)(i.l6, {
														value: y,
														onValueChange: b,
														children: [
															(0, r.jsx)(i.bq, {
																className:
																	"border-gray-200 bg-white font-light",
																children: (0, r.jsx)(i.yv, {
																	placeholder: "All Project Types",
																}),
															}),
															(0, r.jsxs)(i.gC, {
																children: [
																	(0, r.jsx)(i.eb, {
																		value: "all",
																		children: "All Project Types",
																	}),
																	h.map((e) =>
																		(0, r.jsx)(
																			i.eb,
																			{
																				value: e,
																				className: "font-light",
																				children: e,
																			},
																			e,
																		),
																	),
																],
															}),
														],
													}),
													(0, r.jsx)(n.p, {
														placeholder: "Location (e.g., City, State)",
														value: v,
														onChange: (e) => j(e.target.value),
														className: "border-gray-200 bg-white font-light",
													}),
													(0, r.jsxs)(d.AM, {
														children: [
															(0, r.jsx)(d.Wv, {
																asChild: !0,
																children: (0, r.jsxs)(s.$, {
																	variant: "outline",
																	className:
																		"justify-start text-left font-light border-gray-200 bg-white w-full",
																	children: [
																		(0, r.jsx)(u.A, {
																			className: "mr-2 h-4 w-4",
																		}),
																		(null == e ? void 0 : e.from)
																			? e.to
																				? (0, r.jsxs)(r.Fragment, {
																						children: [
																							(0, c.GP)(e.from, "LLL dd, y"),
																							" - ",
																							(0, c.GP)(e.to, "LLL dd, y"),
																						],
																					})
																				: (0, c.GP)(e.from, "LLL dd, y")
																			: (0, r.jsx)("span", {
																					children: "Select date range",
																				}),
																	],
																}),
															}),
															(0, r.jsx)(d.hl, {
																className: "w-auto p-0",
																align: "start",
																children: (0, r.jsx)(l.V, {
																	initialFocus: !0,
																	mode: "range",
																	defaultMonth: null == e ? void 0 : e.from,
																	selected: e,
																	onSelect: t,
																	numberOfMonths: 2,
																}),
															}),
														],
													}),
												],
											}),
											(0, r.jsxs)("div", {
												className: "flex justify-end space-x-2 mt-6",
												children: [
													(0, r.jsx)(s.$, {
														variant: w ? "destructive" : "outline",
														onClick: () => N(!w),
														children: w ? "Exit Edit Mode" : "Enter Edit Mode",
													}),
													w &&
														2 === C.length &&
														(0, r.jsxs)(r.Fragment, {
															children: [
																(0, r.jsx)(s.$, {
																	onClick: () => L("horizontal"),
																	size: "sm",
																	children: "Merge Horizontally",
																}),
																(0, r.jsx)(s.$, {
																	onClick: () => L("vertical"),
																	size: "sm",
																	children: "Merge Vertically",
																}),
															],
														}),
												],
											}),
										],
									}),
									(0, r.jsx)("div", {
										className:
											"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",
										children: I.filter((e) => e.display).map((e) =>
											(0, r.jsxs)(
												"div",
												{
													className: (0, g.cn)(
														"group",
														w ? "cursor-pointer" : "",
														w &&
															C.includes(e.id) &&
															"ring-2 ring-blue-500 ring-offset-2 shadow-lg",
													),
													onClick: () => S(e.id),
													style: {
														gridColumn: "span ".concat(e.colSpan),
														gridRow: "span ".concat(e.rowSpan),
														minWidth: 0,
														overflow: "hidden",
														position: "relative",
													},
													children: [
														(0, r.jsx)(f(), {
															href: "/booker/project/".concat(e.id),
															onClick: (e) => {
																w && e.preventDefault();
															},
															className: "block h-full w-full",
															style: { minWidth: 0 },
															children: (0, r.jsxs)("div", {
																className:
																	"relative mb-4 h-full flex flex-col w-full",
																style: { minWidth: 0 },
																children: [
																	(0, r.jsx)("div", {
																		className: "relative w-full",
																		style: {
																			paddingBottom: "75%",
																			overflow: "hidden",
																		},
																		children: (0, r.jsx)(m.default, {
																			src:
																				e.projectImageUrl || "/placeholder.svg",
																			alt: e.title,
																			fill: !0,
																			className:
																				"object-cover absolute top-0 left-0 w-full h-full transition-transform duration-500 group-hover:scale-105",
																		}),
																	}),
																	(0, r.jsxs)("div", {
																		className: "p-2 space-y-1 flex-grow w-full",
																		style: { minWidth: 0, overflow: "hidden" },
																		children: [
																			(0, r.jsx)("div", {
																				className:
																					"text-xs text-gray-400 font-light tracking-wide w-full",
																				children: e.projectType,
																			}),
																			(0, r.jsx)("h3", {
																				className:
																					"font-light text-black tracking-wide break-words w-full",
																				style: {
																					overflow: "hidden",
																					textOverflow: "ellipsis",
																					whiteSpace: "normal",
																				},
																				children: e.title,
																			}),
																			(0, r.jsx)("p", {
																				className:
																					"text-xs text-gray-600 font-light h-10 break-words w-full",
																				children: e.description,
																			}),
																			(0, r.jsx)("div", {
																				className:
																					"text-xs text-gray-500 font-light w-full",
																				children: e.location,
																			}),
																			(0, r.jsxs)("div", {
																				className:
																					"text-xs text-gray-500 font-light w-full",
																				children: [
																					(0, c.GP)(
																						new Date(e.startDate),
																						"MMM dd, yyyy",
																					),
																					" - ",
																					(0, c.GP)(
																						new Date(e.endDate),
																						"MMM dd, yyyy",
																					),
																				],
																			}),
																		],
																	}),
																],
															}),
														}),
														w &&
															e.isPrimaryOfMerge &&
															(0, r.jsx)(s.$, {
																variant: "destructive",
																size: "sm",
																className:
																	"absolute top-1 right-1 z-10 opacity-80 hover:opacity-100 p-1 h-auto leading-none",
																onClick: (t) => {
																	t.stopPropagation(), P(e.id);
																},
																children: "Unmerge",
															}),
													],
												},
												e.id,
											),
										),
									}),
									0 === I.filter((e) => e.display).length &&
										(0, r.jsxs)("div", {
											className: "text-center py-24",
											children: [
												(0, r.jsx)("h3", {
													className: "text-xl font-light text-gray-900 mb-2",
													children: "No projects found",
												}),
												(0, r.jsx)("p", {
													className: "text-gray-500 font-light",
													children: "Try adjusting your search criteria",
												}),
											],
										}),
								],
							}),
						}),
					],
				});
			}
		},
		900: (e, t, a) => {
			a.d(t, { p: () => n });
			var r = a(8081),
				o = a(2149),
				s = a(7687);
			const n = o.forwardRef((e, t) => {
				const { className: a, type: o, ...n } = e;
				return (0, r.jsx)("input", {
					type: o,
					className: (0, s.cn)(
						"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						a,
					),
					ref: t,
					...n,
				});
			});
			n.displayName = "Input";
		},
		1524: (e, t, a) => {
			Promise.resolve().then(a.bind(a, 567));
		},
		5160: (e, t, a) => {
			a.d(t, { $: () => d, r: () => l });
			var r = a(8081),
				o = a(2149),
				s = a(3629),
				n = a(3484),
				i = a(7687);
			const l = (0, n.F)(
					"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
					{
						variants: {
							variant: {
								default:
									"bg-primary text-primary-foreground hover:bg-primary/90",
								destructive:
									"bg-destructive text-destructive-foreground hover:bg-destructive/90",
								outline:
									"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
								secondary:
									"bg-secondary text-secondary-foreground hover:bg-secondary/80",
								ghost: "hover:bg-accent hover:text-accent-foreground",
								link: "text-primary underline-offset-4 hover:underline",
							},
							size: {
								default: "h-10 px-4 py-2",
								sm: "h-9 rounded-md px-3",
								lg: "h-11 rounded-md px-8",
								icon: "h-10 w-10",
							},
						},
						defaultVariants: { variant: "default", size: "default" },
					},
				),
				d = o.forwardRef((e, t) => {
					const {
							className: a,
							variant: o,
							size: n,
							asChild: d = !1,
							...c
						} = e,
						m = d ? s.DX : "button";
					return (0, r.jsx)(m, {
						className: (0, i.cn)(l({ variant: o, size: n, className: a })),
						ref: t,
						...c,
					});
				});
			d.displayName = "Button";
		},
		5904: (e, t, a) => {
			a.d(t, {
				bq: () => u,
				eb: () => h,
				gC: () => g,
				l6: () => c,
				yv: () => m,
			});
			var r = a(8081),
				o = a(2149),
				s = a(4290),
				n = a(392),
				i = a(6801),
				l = a(6722),
				d = a(7687);
			const c = s.bL;
			s.YJ;
			const m = s.WT,
				u = o.forwardRef((e, t) => {
					const { className: a, children: o, ...i } = e;
					return (0, r.jsxs)(s.l9, {
						ref: t,
						className: (0, d.cn)(
							"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
							a,
						),
						...i,
						children: [
							o,
							(0, r.jsx)(s.In, {
								asChild: !0,
								children: (0, r.jsx)(n.A, { className: "h-4 w-4 opacity-50" }),
							}),
						],
					});
				});
			u.displayName = s.l9.displayName;
			const p = o.forwardRef((e, t) => {
				const { className: a, ...o } = e;
				return (0, r.jsx)(s.PP, {
					ref: t,
					className: (0, d.cn)(
						"flex cursor-default items-center justify-center py-1",
						a,
					),
					...o,
					children: (0, r.jsx)(i.A, { className: "h-4 w-4" }),
				});
			});
			p.displayName = s.PP.displayName;
			const f = o.forwardRef((e, t) => {
				const { className: a, ...o } = e;
				return (0, r.jsx)(s.wn, {
					ref: t,
					className: (0, d.cn)(
						"flex cursor-default items-center justify-center py-1",
						a,
					),
					...o,
					children: (0, r.jsx)(n.A, { className: "h-4 w-4" }),
				});
			});
			f.displayName = s.wn.displayName;
			const g = o.forwardRef((e, t) => {
				const { className: a, children: o, position: n = "popper", ...i } = e;
				return (0, r.jsx)(s.ZL, {
					children: (0, r.jsxs)(s.UC, {
						ref: t,
						className: (0, d.cn)(
							"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
							"popper" === n &&
								"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
							a,
						),
						position: n,
						...i,
						children: [
							(0, r.jsx)(p, {}),
							(0, r.jsx)(s.LM, {
								className: (0, d.cn)(
									"p-1",
									"popper" === n &&
										"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]",
								),
								children: o,
							}),
							(0, r.jsx)(f, {}),
						],
					}),
				});
			});
			(g.displayName = s.UC.displayName),
				(o.forwardRef((e, t) => {
					const { className: a, ...o } = e;
					return (0, r.jsx)(s.JU, {
						ref: t,
						className: (0, d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold", a),
						...o,
					});
				}).displayName = s.JU.displayName);
			const h = o.forwardRef((e, t) => {
				const { className: a, children: o, ...n } = e;
				return (0, r.jsxs)(s.q7, {
					ref: t,
					className: (0, d.cn)(
						"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
						a,
					),
					...n,
					children: [
						(0, r.jsx)("span", {
							className:
								"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
							children: (0, r.jsx)(s.VF, {
								children: (0, r.jsx)(l.A, { className: "h-4 w-4" }),
							}),
						}),
						(0, r.jsx)(s.p4, { children: o }),
					],
				});
			});
			(h.displayName = s.q7.displayName),
				(o.forwardRef((e, t) => {
					const { className: a, ...o } = e;
					return (0, r.jsx)(s.wv, {
						ref: t,
						className: (0, d.cn)("-mx-1 my-1 h-px bg-muted", a),
						...o,
					});
				}).displayName = s.wv.displayName);
		},
		6319: (e, t, a) => {
			a.d(t, { AM: () => i, Wv: () => l, hl: () => d });
			var r = a(8081),
				o = a(2149),
				s = a(29),
				n = a(7687);
			const i = s.bL,
				l = s.l9,
				d = o.forwardRef((e, t) => {
					const {
						className: a,
						align: o = "center",
						sideOffset: i = 4,
						...l
					} = e;
					return (0, r.jsx)(s.ZL, {
						children: (0, r.jsx)(s.UC, {
							ref: t,
							align: o,
							sideOffset: i,
							className: (0, n.cn)(
								"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
								a,
							),
							...l,
						}),
					});
				});
			d.displayName = s.UC.displayName;
		},
		6518: (e, t, a) => {
			a.d(t, { V: () => d });
			var r = a(8081);
			a(2149);
			var o = a(2297),
				s = a(3602),
				n = a(2248),
				i = a(7687),
				l = a(5160);
			function d(e) {
				const {
					className: t,
					classNames: a,
					showOutsideDays: d = !0,
					...c
				} = e;
				return (0, r.jsx)(n.hv, {
					showOutsideDays: d,
					className: (0, i.cn)("p-3", t),
					classNames: {
						months:
							"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
						month: "space-y-4",
						caption: "flex justify-center pt-1 relative items-center",
						caption_label: "text-sm font-medium",
						nav: "space-x-1 flex items-center",
						nav_button: (0, i.cn)(
							(0, l.r)({ variant: "outline" }),
							"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
						),
						nav_button_previous: "absolute left-1",
						nav_button_next: "absolute right-1",
						table: "w-full border-collapse space-y-1",
						head_row: "flex",
						head_cell:
							"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
						row: "flex w-full mt-2",
						cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
						day: (0, i.cn)(
							(0, l.r)({ variant: "ghost" }),
							"h-9 w-9 p-0 font-normal aria-selected:opacity-100",
						),
						day_range_end: "day-range-end",
						day_selected:
							"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
						day_today: "bg-accent text-accent-foreground",
						day_outside:
							"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
						day_disabled: "text-muted-foreground opacity-50",
						day_range_middle:
							"aria-selected:bg-accent aria-selected:text-accent-foreground",
						day_hidden: "invisible",
						...a,
					},
					components: {
						IconLeft: (e) => {
							const { ...t } = e;
							return (0, r.jsx)(o.A, { className: "h-4 w-4" });
						},
						IconRight: (e) => {
							const { ...t } = e;
							return (0, r.jsx)(s.A, { className: "h-4 w-4" });
						},
					},
					...c,
				});
			}
			d.displayName = "Calendar";
		},
		7687: (e, t, a) => {
			a.d(t, { cn: () => s });
			var r = a(6522),
				o = a(4483);
			function s() {
				for (var e = arguments.length, t = Array(e), a = 0; a < e; a++)
					t[a] = arguments[a];
				return (0, o.QP)((0, r.$)(t));
			}
		},
	},
	(e) => {
		var t = (t) => e((e.s = t));
		e.O(0, [874, 637, 186, 398, 752, 216, 497, 954, 358], () => t(1524)),
			(_N_E = e.O());
	},
]);
