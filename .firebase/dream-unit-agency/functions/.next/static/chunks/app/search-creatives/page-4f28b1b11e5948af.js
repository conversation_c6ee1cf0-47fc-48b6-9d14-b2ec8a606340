(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[444],
	{
		134: (e, a, t) => {
			Promise.resolve().then(t.bind(t, 9233));
		},
		900: (e, a, t) => {
			t.d(a, { p: () => l });
			var r = t(8081),
				s = t(2149),
				i = t(7687);
			const l = s.forwardRef((e, a) => {
				const { className: t, type: s, ...l } = e;
				return (0, r.jsx)("input", {
					type: s,
					className: (0, i.cn)(
						"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
						t,
					),
					ref: a,
					...l,
				});
			});
			l.displayName = "Input";
		},
		1153: (e, a, t) => {
			t.d(a, { A: () => r });
			const r = (0, t(1018).A)("Menu", [
				["line", { x1: "4", x2: "20", y1: "12", y2: "12", key: "1e0a9i" }],
				["line", { x1: "4", x2: "20", y1: "6", y2: "6", key: "1owob3" }],
				["line", { x1: "4", x2: "20", y1: "18", y2: "18", key: "yk5zj1" }],
			]);
		},
		2950: (e, a, t) => {
			t.d(a, { A: () => r });
			const r = (0, t(1018).A)("Search", [
				["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
				["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
			]);
		},
		5160: (e, a, t) => {
			t.d(a, { $: () => n, r: () => d });
			var r = t(8081),
				s = t(2149),
				i = t(3629),
				l = t(3484),
				o = t(7687);
			const d = (0, l.F)(
					"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
					{
						variants: {
							variant: {
								default:
									"bg-primary text-primary-foreground hover:bg-primary/90",
								destructive:
									"bg-destructive text-destructive-foreground hover:bg-destructive/90",
								outline:
									"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
								secondary:
									"bg-secondary text-secondary-foreground hover:bg-secondary/80",
								ghost: "hover:bg-accent hover:text-accent-foreground",
								link: "text-primary underline-offset-4 hover:underline",
							},
							size: {
								default: "h-10 px-4 py-2",
								sm: "h-9 rounded-md px-3",
								lg: "h-11 rounded-md px-8",
								icon: "h-10 w-10",
							},
						},
						defaultVariants: { variant: "default", size: "default" },
					},
				),
				n = s.forwardRef((e, a) => {
					const {
							className: t,
							variant: s,
							size: l,
							asChild: n = !1,
							...c
						} = e,
						m = n ? i.DX : "button";
					return (0, r.jsx)(m, {
						className: (0, o.cn)(d({ variant: s, size: l, className: t })),
						ref: a,
						...c,
					});
				});
			n.displayName = "Button";
		},
		5904: (e, a, t) => {
			t.d(a, {
				bq: () => u,
				eb: () => g,
				gC: () => p,
				l6: () => c,
				yv: () => m,
			});
			var r = t(8081),
				s = t(2149),
				i = t(4290),
				l = t(392),
				o = t(6801),
				d = t(6722),
				n = t(7687);
			const c = i.bL;
			i.YJ;
			const m = i.WT,
				u = s.forwardRef((e, a) => {
					const { className: t, children: s, ...o } = e;
					return (0, r.jsxs)(i.l9, {
						ref: a,
						className: (0, n.cn)(
							"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
							t,
						),
						...o,
						children: [
							s,
							(0, r.jsx)(i.In, {
								asChild: !0,
								children: (0, r.jsx)(l.A, { className: "h-4 w-4 opacity-50" }),
							}),
						],
					});
				});
			u.displayName = i.l9.displayName;
			const h = s.forwardRef((e, a) => {
				const { className: t, ...s } = e;
				return (0, r.jsx)(i.PP, {
					ref: a,
					className: (0, n.cn)(
						"flex cursor-default items-center justify-center py-1",
						t,
					),
					...s,
					children: (0, r.jsx)(o.A, { className: "h-4 w-4" }),
				});
			});
			h.displayName = i.PP.displayName;
			const f = s.forwardRef((e, a) => {
				const { className: t, ...s } = e;
				return (0, r.jsx)(i.wn, {
					ref: a,
					className: (0, n.cn)(
						"flex cursor-default items-center justify-center py-1",
						t,
					),
					...s,
					children: (0, r.jsx)(l.A, { className: "h-4 w-4" }),
				});
			});
			f.displayName = i.wn.displayName;
			const p = s.forwardRef((e, a) => {
				const { className: t, children: s, position: l = "popper", ...o } = e;
				return (0, r.jsx)(i.ZL, {
					children: (0, r.jsxs)(i.UC, {
						ref: a,
						className: (0, n.cn)(
							"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
							"popper" === l &&
								"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
							t,
						),
						position: l,
						...o,
						children: [
							(0, r.jsx)(h, {}),
							(0, r.jsx)(i.LM, {
								className: (0, n.cn)(
									"p-1",
									"popper" === l &&
										"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]",
								),
								children: s,
							}),
							(0, r.jsx)(f, {}),
						],
					}),
				});
			});
			(p.displayName = i.UC.displayName),
				(s.forwardRef((e, a) => {
					const { className: t, ...s } = e;
					return (0, r.jsx)(i.JU, {
						ref: a,
						className: (0, n.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold", t),
						...s,
					});
				}).displayName = i.JU.displayName);
			const g = s.forwardRef((e, a) => {
				const { className: t, children: s, ...l } = e;
				return (0, r.jsxs)(i.q7, {
					ref: a,
					className: (0, n.cn)(
						"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
						t,
					),
					...l,
					children: [
						(0, r.jsx)("span", {
							className:
								"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
							children: (0, r.jsx)(i.VF, {
								children: (0, r.jsx)(d.A, { className: "h-4 w-4" }),
							}),
						}),
						(0, r.jsx)(i.p4, { children: s }),
					],
				});
			});
			(g.displayName = i.q7.displayName),
				(s.forwardRef((e, a) => {
					const { className: t, ...s } = e;
					return (0, r.jsx)(i.wv, {
						ref: a,
						className: (0, n.cn)("-mx-1 my-1 h-px bg-muted", t),
						...s,
					});
				}).displayName = i.wv.displayName);
		},
		6319: (e, a, t) => {
			t.d(a, { AM: () => o, Wv: () => d, hl: () => n });
			var r = t(8081),
				s = t(2149),
				i = t(29),
				l = t(7687);
			const o = i.bL,
				d = i.l9,
				n = s.forwardRef((e, a) => {
					const {
						className: t,
						align: s = "center",
						sideOffset: o = 4,
						...d
					} = e;
					return (0, r.jsx)(i.ZL, {
						children: (0, r.jsx)(i.UC, {
							ref: a,
							align: s,
							sideOffset: o,
							className: (0, l.cn)(
								"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
								t,
							),
							...d,
						}),
					});
				});
			n.displayName = i.UC.displayName;
		},
		6518: (e, a, t) => {
			t.d(a, { V: () => n });
			var r = t(8081);
			t(2149);
			var s = t(2297),
				i = t(3602),
				l = t(2248),
				o = t(7687),
				d = t(5160);
			function n(e) {
				const {
					className: a,
					classNames: t,
					showOutsideDays: n = !0,
					...c
				} = e;
				return (0, r.jsx)(l.hv, {
					showOutsideDays: n,
					className: (0, o.cn)("p-3", a),
					classNames: {
						months:
							"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
						month: "space-y-4",
						caption: "flex justify-center pt-1 relative items-center",
						caption_label: "text-sm font-medium",
						nav: "space-x-1 flex items-center",
						nav_button: (0, o.cn)(
							(0, d.r)({ variant: "outline" }),
							"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
						),
						nav_button_previous: "absolute left-1",
						nav_button_next: "absolute right-1",
						table: "w-full border-collapse space-y-1",
						head_row: "flex",
						head_cell:
							"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
						row: "flex w-full mt-2",
						cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
						day: (0, o.cn)(
							(0, d.r)({ variant: "ghost" }),
							"h-9 w-9 p-0 font-normal aria-selected:opacity-100",
						),
						day_range_end: "day-range-end",
						day_selected:
							"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
						day_today: "bg-accent text-accent-foreground",
						day_outside:
							"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
						day_disabled: "text-muted-foreground opacity-50",
						day_range_middle:
							"aria-selected:bg-accent aria-selected:text-accent-foreground",
						day_hidden: "invisible",
						...t,
					},
					components: {
						IconLeft: (e) => {
							const { ...a } = e;
							return (0, r.jsx)(s.A, { className: "h-4 w-4" });
						},
						IconRight: (e) => {
							const { ...a } = e;
							return (0, r.jsx)(i.A, { className: "h-4 w-4" });
						},
					},
					...c,
				});
			}
			n.displayName = "Calendar";
		},
		7687: (e, a, t) => {
			t.d(a, { cn: () => i });
			var r = t(6522),
				s = t(4483);
			function i() {
				for (var e = arguments.length, a = Array(e), t = 0; t < e; t++)
					a[t] = arguments[t];
				return (0, s.QP)((0, r.$)(a));
			}
		},
		9233: (e, a, t) => {
			t.d(a, { default: () => v });
			var r = t(8081),
				s = t(2149),
				i = t(5160),
				l = t(900),
				o = t(5904),
				d = t(6518),
				n = t(6319),
				c = t(6752),
				m = t(5186),
				u = t(2950),
				h = t(1153),
				f = t(5516),
				p = t(7950),
				g = t.n(p);
			const x = [
					"PHOTOGRAPHER",
					"VIDEOGRAPHER",
					"MODEL",
					"MAKEUP ARTIST",
					"HAIR STYLIST",
					"FASHION STYLIST",
					"SET DESIGNER",
					"PROP MASTER",
					"LIGHTING TECHNICIAN",
					"SOUND ENGINEER",
				],
				y = [
					{
						id: "1",
						name: "ALEX JOHNSON",
						type: "PHOTOGRAPHER",
						location: "New York, NY",
						coordinates: { lat: 40.7128, lng: -74.006 },
						avatar: "/assets/creatives_portfolio/crt1.png",
						availability: {
							available: !0,
							dates: [
								"2024-06-02",
								"2024-06-03",
								"2024-06-05",
								"2024-06-08",
								"2024-06-09",
							],
						},
						specialties: ["Portrait", "Fashion", "Commercial"],
						featured: !0,
					},
					{
						id: "2",
						name: "SARAH WILSON",
						type: "MODEL",
						location: "Los Angeles, CA",
						coordinates: { lat: 34.0522, lng: -118.2437 },
						avatar: "/assets/creatives_portfolio/crt2.png",
						availability: {
							available: !0,
							dates: ["2024-06-01", "2024-06-04", "2024-06-07", "2024-06-10"],
						},
						specialties: ["Fashion", "Commercial", "Runway"],
						featured: !0,
					},
					{
						id: "3",
						name: "MICHAEL CHEN",
						type: "VIDEOGRAPHER",
						location: "Chicago, IL",
						coordinates: { lat: 41.8781, lng: -87.6298 },
						avatar: "/assets/creatives_portfolio/crt3.png",
						availability: {
							available: !1,
							dates: ["2024-06-15", "2024-06-16", "2024-06-17"],
						},
						specialties: ["Documentary", "Commercial", "Music Videos"],
						featured: !1,
					},
					{
						id: "4",
						name: "EMMA DAVIS",
						type: "MAKEUP ARTIST",
						location: "Miami, FL",
						coordinates: { lat: 25.7617, lng: -80.1918 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Emma Davis",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-04",
								"2024-06-05",
							],
						},
						specialties: ["Editorial", "Bridal", "SFX"],
						featured: !1,
					},
					{
						id: "5",
						name: "DAVID RODRIGUEZ",
						type: "PHOTOGRAPHER",
						location: "Austin, TX",
						coordinates: { lat: 30.2672, lng: -97.7431 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of David Rodriguez",
						availability: {
							available: !0,
							dates: [
								"2024-06-02",
								"2024-06-03",
								"2024-06-04",
								"2024-06-08",
								"2024-06-09",
							],
						},
						specialties: ["Product", "Architecture", "Landscape"],
						featured: !0,
					},
					{
						id: "6",
						name: "LISA THOMPSON",
						type: "HAIR STYLIST",
						location: "Seattle, WA",
						coordinates: { lat: 47.6062, lng: -122.3321 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Lisa Thompson",
						availability: {
							available: !0,
							dates: ["2024-06-01", "2024-06-05", "2024-06-06", "2024-06-07"],
						},
						specialties: ["Editorial", "Wedding", "Film"],
						featured: !1,
					},
					{
						id: "7",
						name: "JAMES ANDERSON",
						type: "FASHION STYLIST",
						location: "Portland, OR",
						coordinates: { lat: 45.5051, lng: -122.675 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of James Anderson",
						availability: {
							available: !1,
							dates: ["2024-06-20", "2024-06-21", "2024-06-22"],
						},
						specialties: ["Editorial", "Commercial", "Celebrity"],
						featured: !1,
					},
					{
						id: "8",
						name: "MARIA GARCIA",
						type: "MODEL",
						location: "San Francisco, CA",
						coordinates: { lat: 37.7749, lng: -122.4194 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Maria Garcia",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-09",
								"2024-06-10",
							],
						},
						specialties: ["Editorial", "Runway", "Fit"],
						featured: !0,
					},
					{
						id: "9",
						name: "ROBERT KLEIN",
						type: "PHOTOGRAPHER",
						location: "Boston, MA",
						coordinates: { lat: 42.3601, lng: -71.0589 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Robert Klein",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-09",
								"2024-06-10",
							],
						},
						specialties: ["Street", "Documentary", "Portrait"],
						featured: !1,
					},
					{
						id: "10",
						name: "NINA PATEL",
						type: "MAKEUP ARTIST",
						location: "Denver, CO",
						coordinates: { lat: 39.7392, lng: -104.9903 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Nina Patel",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-09",
								"2024-06-10",
							],
						},
						specialties: ["Beauty", "Editorial", "Commercial"],
						featured: !1,
					},
					{
						id: "11",
						name: "CARLOS MENDEZ",
						type: "VIDEOGRAPHER",
						location: "Phoenix, AZ",
						coordinates: { lat: 33.4484, lng: -112.074 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Carlos Mendez",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-09",
								"2024-06-10",
							],
						},
						specialties: ["Wedding", "Corporate", "Event"],
						featured: !1,
					},
					{
						id: "12",
						name: "SOPHIE LAURENT",
						type: "MODEL",
						location: "Nashville, TN",
						coordinates: { lat: 36.1627, lng: -86.7816 },
						avatar:
							"/placeholder.svg?height=600&width=400&text=Portrait of Sophie Laurent",
						availability: {
							available: !0,
							dates: [
								"2024-06-01",
								"2024-06-02",
								"2024-06-03",
								"2024-06-09",
								"2024-06-10",
							],
						},
						specialties: ["Commercial", "Lifestyle", "Beauty"],
						featured: !1,
					},
				];
			function v() {
				const [e, a] = (0, s.useState)(new Date()),
					[t, p] = (0, s.useState)(""),
					[v, b] = (0, s.useState)(""),
					[N, w] = (0, s.useState)(""),
					[j, A] = (0, s.useState)(!1),
					E = y.filter((e) => {
						const a =
								"" === t ||
								e.name.toLowerCase().includes(t.toLowerCase()) ||
								e.location.toLowerCase().includes(t.toLowerCase()) ||
								e.specialties.some((e) =>
									e.toLowerCase().includes(t.toLowerCase()),
								),
							r = "" === v || e.type === v,
							s = !j || e.availability.available;
						return a && r && s;
					}),
					R = (a) => {
						if (!e) return !1;
						const t = (0, c.GP)(e, "yyyy-MM-dd");
						return a.availability.dates.includes(t);
					};
				return (0, r.jsxs)("div", {
					className: "min-h-screen bg-white font-light",
					children: [
						(0, r.jsx)("header", {
							className: "border-b border-gray-100",
							children: (0, r.jsx)("div", {
								className: "max-w-7xl mx-auto px-6 py-6",
								children: (0, r.jsxs)("div", {
									className: "flex items-center justify-between",
									children: [
										(0, r.jsx)(g(), {
											href: "/",
											className: "text-2xl font-light tracking-wide",
											children: "DUA",
										}),
										(0, r.jsxs)("nav", {
											className:
												"hidden md:flex items-center space-x-8 text-sm",
											children: [
												(0, r.jsx)("span", {
													className: "text-black",
													children: "Search",
												}),
												(0, r.jsx)(g(), {
													href: "/booker/1",
													className:
														"text-gray-600 hover:text-black transition-colors",
													children: "Dashboard",
												}),
												(0, r.jsx)("span", {
													className: "text-gray-600",
													children: "About",
												}),
												(0, r.jsx)("span", {
													className: "text-gray-600",
													children: "Contact",
												}),
												(0, r.jsx)(u.A, { className: "w-4 h-4 text-gray-600" }),
											],
										}),
										(0, r.jsx)(i.$, {
											variant: "ghost",
											size: "icon",
											className: "md:hidden",
											children: (0, r.jsx)(h.A, { className: "h-5 w-5" }),
										}),
									],
								}),
							}),
						}),
						(0, r.jsx)("div", {
							className: "border-b border-gray-100 bg-gray-50/30",
							children: (0, r.jsxs)("div", {
								className: "max-w-7xl mx-auto px-6 py-8",
								children: [
									(0, r.jsxs)("div", {
										className: "grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",
										children: [
											(0, r.jsx)(l.p, {
												placeholder: "Search by name or location",
												value: t,
												onChange: (e) => p(e.target.value),
												className: "border-gray-200 bg-white font-light",
											}),
											(0, r.jsxs)(o.l6, {
												value: v,
												onValueChange: b,
												children: [
													(0, r.jsx)(o.bq, {
														className: "border-gray-200 bg-white font-light",
														children: (0, r.jsx)(o.yv, {
															placeholder: "All Types",
														}),
													}),
													(0, r.jsxs)(o.gC, {
														children: [
															(0, r.jsx)(o.eb, {
																value: "all",
																children: "All Types",
															}),
															x.map((e) =>
																(0, r.jsx)(
																	o.eb,
																	{
																		value: e,
																		className: "font-light",
																		children: e,
																	},
																	e,
																),
															),
														],
													}),
												],
											}),
											(0, r.jsx)(l.p, {
												placeholder: "Location",
												value: N,
												onChange: (e) => w(e.target.value),
												className: "border-gray-200 bg-white font-light",
											}),
											(0, r.jsxs)(n.AM, {
												children: [
													(0, r.jsx)(n.Wv, {
														asChild: !0,
														children: (0, r.jsxs)(i.$, {
															variant: "outline",
															className:
																"justify-start text-left font-light border-gray-200 bg-white",
															children: [
																(0, r.jsx)(f.A, { className: "mr-2 h-4 w-4" }),
																e
																	? (0, c.GP)(e, "MMM dd, yyyy")
																	: (0, r.jsx)("span", {
																			children: "Select date",
																		}),
															],
														}),
													}),
													(0, r.jsx)(n.hl, {
														className: "w-auto p-0",
														children: (0, r.jsx)(d.V, {
															mode: "single",
															selected: e,
															onSelect: a,
															initialFocus: !0,
														}),
													}),
												],
											}),
										],
									}),
									(0, r.jsxs)("div", {
										className: "flex items-center",
										children: [
											(0, r.jsx)("input", {
												type: "checkbox",
												id: "available",
												checked: j,
												onChange: () => A(!j),
												className: "mr-2",
											}),
											(0, r.jsx)("label", {
												htmlFor: "available",
												className: "text-sm text-gray-600 font-light",
												children: "Show only available on selected date",
											}),
										],
									}),
								],
							}),
						}),
						(0, r.jsxs)("div", {
							className: "max-w-7xl mx-auto px-6 py-16",
							children: [
								(0, r.jsx)("div", {
									className:
										"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",
									children: E.map((e) =>
										(0, r.jsx)(
											g(),
											{
												href: "/creative/".concat(e.id),
												children: (0, r.jsxs)("div", {
													className: "group cursor-pointer",
													children: [
														(0, r.jsxs)("div", {
															className:
																"relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100",
															children: [
																(0, r.jsx)(m.default, {
																	src: e.avatar || "/placeholder.svg",
																	alt: e.name,
																	fill: !0,
																	className:
																		"object-cover transition-transform duration-500 group-hover:scale-105",
																}),
																R(e) &&
																	(0, r.jsx)("div", {
																		className:
																			"absolute top-4 right-4 w-3 h-3 bg-green-500 rounded-full",
																	}),
															],
														}),
														(0, r.jsxs)("div", {
															className: "space-y-1",
															children: [
																(0, r.jsx)("div", {
																	className:
																		"text-xs text-gray-400 font-light tracking-wide",
																	children: e.type,
																}),
																(0, r.jsx)("h3", {
																	className:
																		"font-light text-black tracking-wide",
																	children: e.name,
																}),
																(0, r.jsx)("div", {
																	className: "text-xs text-gray-500 font-light",
																	children: e.location,
																}),
															],
														}),
													],
												}),
											},
											e.id,
										),
									),
								}),
								0 === E.length &&
									(0, r.jsxs)("div", {
										className: "text-center py-24",
										children: [
											(0, r.jsx)("h3", {
												className: "text-xl font-light text-gray-900 mb-2",
												children: "No creatives found",
											}),
											(0, r.jsx)("p", {
												className: "text-gray-500 font-light",
												children: "Try adjusting your search criteria",
											}),
										],
									}),
							],
						}),
					],
				});
			}
		},
	},
	(e) => {
		var a = (a) => e((e.s = a));
		e.O(0, [874, 637, 186, 398, 752, 216, 497, 954, 358], () => a(134)),
			(_N_E = e.O());
	},
]);
