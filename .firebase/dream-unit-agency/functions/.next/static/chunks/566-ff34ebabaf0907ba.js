(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[566],
	{
		1337: (e, t, n) => {
			n.d(t, {
				UC: () => et,
				VY: () => er,
				ZL: () => $,
				bL: () => Y,
				bm: () => eo,
				hE: () => en,
				hJ: () => ee,
				l9: () => Q,
			});
			var r = n(2149),
				o = n(493),
				a = n(8735),
				i = n(4507),
				l = n(1470),
				s = n(7522),
				u = n(8548),
				d = n(7654),
				c = n(2619),
				f = n(9935),
				p = n(6330),
				m = n(1391),
				v = n(8426),
				g = n(5703),
				y = n(3629),
				h = n(8081),
				b = "Dialog",
				[w, D] = (0, i.A)(b),
				[x, R] = w(b),
				N = (e) => {
					const {
							__scopeDialog: t,
							children: n,
							open: o,
							defaultOpen: a,
							onOpenChange: i,
							modal: u = !0,
						} = e,
						d = r.useRef(null),
						c = r.useRef(null),
						[f = !1, p] = (0, s.i)({ prop: o, defaultProp: a, onChange: i });
					return (0, h.jsx)(x, {
						scope: t,
						triggerRef: d,
						contentRef: c,
						contentId: (0, l.B)(),
						titleId: (0, l.B)(),
						descriptionId: (0, l.B)(),
						open: f,
						onOpenChange: p,
						onOpenToggle: r.useCallback(() => p((e) => !e), [p]),
						modal: u,
						children: n,
					});
				};
			N.displayName = b;
			var I = "DialogTrigger",
				A = r.forwardRef((e, t) => {
					const { __scopeDialog: n, ...r } = e,
						i = R(I, n),
						l = (0, a.s)(t, i.triggerRef);
					return (0, h.jsx)(p.sG.button, {
						type: "button",
						"aria-haspopup": "dialog",
						"aria-expanded": i.open,
						"aria-controls": i.contentId,
						"data-state": q(i.open),
						...r,
						ref: l,
						onClick: (0, o.m)(e.onClick, i.onOpenToggle),
					});
				});
			A.displayName = I;
			var C = "DialogPortal",
				[j, E] = w(C, { forceMount: void 0 }),
				F = (e) => {
					const {
							__scopeDialog: t,
							forceMount: n,
							children: o,
							container: a,
						} = e,
						i = R(C, t);
					return (0, h.jsx)(j, {
						scope: t,
						forceMount: n,
						children: r.Children.map(o, (e) =>
							(0, h.jsx)(f.C, {
								present: n || i.open,
								children: (0, h.jsx)(c.Z, {
									asChild: !0,
									container: a,
									children: e,
								}),
							}),
						),
					});
				};
			F.displayName = C;
			var T = "DialogOverlay",
				M = r.forwardRef((e, t) => {
					const n = E(T, e.__scopeDialog),
						{ forceMount: r = n.forceMount, ...o } = e,
						a = R(T, e.__scopeDialog);
					return a.modal
						? (0, h.jsx)(f.C, {
								present: r || a.open,
								children: (0, h.jsx)(O, { ...o, ref: t }),
							})
						: null;
				});
			M.displayName = T;
			var O = r.forwardRef((e, t) => {
					const { __scopeDialog: n, ...r } = e,
						o = R(T, n);
					return (0, h.jsx)(v.A, {
						as: y.DX,
						allowPinchZoom: !0,
						shards: [o.contentRef],
						children: (0, h.jsx)(p.sG.div, {
							"data-state": q(o.open),
							...r,
							ref: t,
							style: { pointerEvents: "auto", ...r.style },
						}),
					});
				}),
				k = "DialogContent",
				_ = r.forwardRef((e, t) => {
					const n = E(k, e.__scopeDialog),
						{ forceMount: r = n.forceMount, ...o } = e,
						a = R(k, e.__scopeDialog);
					return (0, h.jsx)(f.C, {
						present: r || a.open,
						children: a.modal
							? (0, h.jsx)(P, { ...o, ref: t })
							: (0, h.jsx)(U, { ...o, ref: t }),
					});
				});
			_.displayName = k;
			var P = r.forwardRef((e, t) => {
					const n = R(k, e.__scopeDialog),
						i = r.useRef(null),
						l = (0, a.s)(t, n.contentRef, i);
					return (
						r.useEffect(() => {
							const e = i.current;
							if (e) return (0, g.Eq)(e);
						}, []),
						(0, h.jsx)(G, {
							...e,
							ref: l,
							trapFocus: n.open,
							disableOutsidePointerEvents: !0,
							onCloseAutoFocus: (0, o.m)(e.onCloseAutoFocus, (e) => {
								var t;
								e.preventDefault(),
									null === (t = n.triggerRef.current) ||
										void 0 === t ||
										t.focus();
							}),
							onPointerDownOutside: (0, o.m)(e.onPointerDownOutside, (e) => {
								const t = e.detail.originalEvent,
									n = 0 === t.button && !0 === t.ctrlKey;
								(2 === t.button || n) && e.preventDefault();
							}),
							onFocusOutside: (0, o.m)(e.onFocusOutside, (e) =>
								e.preventDefault(),
							),
						})
					);
				}),
				U = r.forwardRef((e, t) => {
					const n = R(k, e.__scopeDialog),
						o = r.useRef(!1),
						a = r.useRef(!1);
					return (0, h.jsx)(G, {
						...e,
						ref: t,
						trapFocus: !1,
						disableOutsidePointerEvents: !1,
						onCloseAutoFocus: (t) => {
							var r, i;
							null === (r = e.onCloseAutoFocus) || void 0 === r || r.call(e, t),
								t.defaultPrevented ||
									(o.current ||
										null === (i = n.triggerRef.current) ||
										void 0 === i ||
										i.focus(),
									t.preventDefault()),
								(o.current = !1),
								(a.current = !1);
						},
						onInteractOutside: (t) => {
							var r, i;
							null === (r = e.onInteractOutside) ||
								void 0 === r ||
								r.call(e, t),
								t.defaultPrevented ||
									((o.current = !0),
									"pointerdown" !== t.detail.originalEvent.type ||
										(a.current = !0));
							const l = t.target;
							(null === (i = n.triggerRef.current) || void 0 === i
								? void 0
								: i.contains(l)) && t.preventDefault(),
								"focusin" === t.detail.originalEvent.type &&
									a.current &&
									t.preventDefault();
						},
					});
				}),
				G = r.forwardRef((e, t) => {
					const {
							__scopeDialog: n,
							trapFocus: o,
							onOpenAutoFocus: i,
							onCloseAutoFocus: l,
							...s
						} = e,
						c = R(k, n),
						f = r.useRef(null),
						p = (0, a.s)(t, f);
					return (
						(0, m.Oh)(),
						(0, h.jsxs)(h.Fragment, {
							children: [
								(0, h.jsx)(d.n, {
									asChild: !0,
									loop: !0,
									trapped: o,
									onMountAutoFocus: i,
									onUnmountAutoFocus: l,
									children: (0, h.jsx)(u.qW, {
										role: "dialog",
										id: c.contentId,
										"aria-describedby": c.descriptionId,
										"aria-labelledby": c.titleId,
										"data-state": q(c.open),
										...s,
										ref: p,
										onDismiss: () => c.onOpenChange(!1),
									}),
								}),
								(0, h.jsxs)(h.Fragment, {
									children: [
										(0, h.jsx)(X, { titleId: c.titleId }),
										(0, h.jsx)(J, {
											contentRef: f,
											descriptionId: c.descriptionId,
										}),
									],
								}),
							],
						})
					);
				}),
				L = "DialogTitle",
				S = r.forwardRef((e, t) => {
					const { __scopeDialog: n, ...r } = e,
						o = R(L, n);
					return (0, h.jsx)(p.sG.h2, { id: o.titleId, ...r, ref: t });
				});
			S.displayName = L;
			var B = "DialogDescription",
				K = r.forwardRef((e, t) => {
					const { __scopeDialog: n, ...r } = e,
						o = R(B, n);
					return (0, h.jsx)(p.sG.p, { id: o.descriptionId, ...r, ref: t });
				});
			K.displayName = B;
			var V = "DialogClose",
				W = r.forwardRef((e, t) => {
					const { __scopeDialog: n, ...r } = e,
						a = R(V, n);
					return (0, h.jsx)(p.sG.button, {
						type: "button",
						...r,
						ref: t,
						onClick: (0, o.m)(e.onClick, () => a.onOpenChange(!1)),
					});
				});
			function q(e) {
				return e ? "open" : "closed";
			}
			W.displayName = V;
			var H = "DialogTitleWarning",
				[z, Z] = (0, i.q)(H, {
					contentName: k,
					titleName: L,
					docsSlug: "dialog",
				}),
				X = (e) => {
					const { titleId: t } = e,
						n = Z(H),
						o = "`"
							.concat(n.contentName, "` requires a `")
							.concat(
								n.titleName,
								"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `",
							)
							.concat(
								n.titleName,
								"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/",
							)
							.concat(n.docsSlug);
					return (
						r.useEffect(() => {
							t && !document.getElementById(t) && console.error(o);
						}, [o, t]),
						null
					);
				},
				J = (e) => {
					const { contentRef: t, descriptionId: n } = e,
						o = Z("DialogDescriptionWarning"),
						a =
							"Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(
								o.contentName,
								"}.",
							);
					return (
						r.useEffect(() => {
							var e;
							const r =
								null === (e = t.current) || void 0 === e
									? void 0
									: e.getAttribute("aria-describedby");
							n && r && !document.getElementById(n) && console.warn(a);
						}, [a, t, n]),
						null
					);
				},
				Y = N,
				Q = A,
				$ = F,
				ee = M,
				et = _,
				en = S,
				er = K,
				eo = W;
		},
		1511: (e, t, n) => {
			n.d(t, { A: () => r });
			const r = (0, n(1018).A)("Shield", [
				[
					"path",
					{
						d: "M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",
						key: "oel41y",
					},
				],
			]);
		},
		2522: (e, t, n) => {
			n.d(t, { A: () => r });
			const r = (0, n(1018).A)("X", [
				["path", { d: "M18 6 6 18", key: "1bl5f8" }],
				["path", { d: "m6 6 12 12", key: "d8bk6v" }],
			]);
		},
		2950: (e, t, n) => {
			n.d(t, { A: () => r });
			const r = (0, n(1018).A)("Search", [
				["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
				["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
			]);
		},
		4150: (e, t, n) => {
			n.d(t, { A: () => r });
			const r = (0, n(1018).A)("Briefcase", [
				[
					"path",
					{ d: "M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16", key: "jecpp" },
				],
				[
					"rect",
					{ width: "20", height: "14", x: "2", y: "6", rx: "2", key: "i6l2r4" },
				],
			]);
		},
		6546: (e, t, n) => {
			n.d(t, { A: () => r });
			const r = (0, n(1018).A)("User", [
				[
					"path",
					{ d: "M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2", key: "975kel" },
				],
				["circle", { cx: "12", cy: "7", r: "4", key: "17ys0d" }],
			]);
		},
		7278: (e, t, n) => {
			n.d(t, { UC: () => X, B8: () => z, bL: () => H, l9: () => Z });
			var r = n(2149),
				o = n(493),
				a = n(4507),
				i = n(9491),
				l = n(8735),
				s = n(1470),
				u = n(6330),
				d = n(2566),
				c = n(7522),
				f = n(1484),
				p = n(8081),
				m = "rovingFocusGroup.onEntryFocus",
				v = { bubbles: !1, cancelable: !0 },
				g = "RovingFocusGroup",
				[y, h, b] = (0, i.N)(g),
				[w, D] = (0, a.A)(g, [b]),
				[x, R] = w(g),
				N = r.forwardRef((e, t) =>
					(0, p.jsx)(y.Provider, {
						scope: e.__scopeRovingFocusGroup,
						children: (0, p.jsx)(y.Slot, {
							scope: e.__scopeRovingFocusGroup,
							children: (0, p.jsx)(I, { ...e, ref: t }),
						}),
					}),
				);
			N.displayName = g;
			var I = r.forwardRef((e, t) => {
					const {
							__scopeRovingFocusGroup: n,
							orientation: a,
							loop: i = !1,
							dir: s,
							currentTabStopId: g,
							defaultCurrentTabStopId: y,
							onCurrentTabStopIdChange: b,
							onEntryFocus: w,
							preventScrollOnEntryFocus: D = !1,
							...R
						} = e,
						N = r.useRef(null),
						I = (0, l.s)(t, N),
						A = (0, f.jH)(s),
						[C = null, j] = (0, c.i)({ prop: g, defaultProp: y, onChange: b }),
						[F, T] = r.useState(!1),
						M = (0, d.c)(w),
						O = h(n),
						k = r.useRef(!1),
						[_, P] = r.useState(0);
					return (
						r.useEffect(() => {
							const e = N.current;
							if (e)
								return (
									e.addEventListener(m, M), () => e.removeEventListener(m, M)
								);
						}, [M]),
						(0, p.jsx)(x, {
							scope: n,
							orientation: a,
							dir: A,
							loop: i,
							currentTabStopId: C,
							onItemFocus: r.useCallback((e) => j(e), [j]),
							onItemShiftTab: r.useCallback(() => T(!0), []),
							onFocusableItemAdd: r.useCallback(() => P((e) => e + 1), []),
							onFocusableItemRemove: r.useCallback(() => P((e) => e - 1), []),
							children: (0, p.jsx)(u.sG.div, {
								tabIndex: F || 0 === _ ? -1 : 0,
								"data-orientation": a,
								...R,
								ref: I,
								style: { outline: "none", ...e.style },
								onMouseDown: (0, o.m)(e.onMouseDown, () => {
									k.current = !0;
								}),
								onFocus: (0, o.m)(e.onFocus, (e) => {
									const t = !k.current;
									if (e.target === e.currentTarget && t && !F) {
										const t = new CustomEvent(m, v);
										if (
											(e.currentTarget.dispatchEvent(t), !t.defaultPrevented)
										) {
											const e = O().filter((e) => e.focusable);
											E(
												[
													e.find((e) => e.active),
													e.find((e) => e.id === C),
													...e,
												]
													.filter(Boolean)
													.map((e) => e.ref.current),
												D,
											);
										}
									}
									k.current = !1;
								}),
								onBlur: (0, o.m)(e.onBlur, () => T(!1)),
							}),
						})
					);
				}),
				A = "RovingFocusGroupItem",
				C = r.forwardRef((e, t) => {
					const {
							__scopeRovingFocusGroup: n,
							focusable: a = !0,
							active: i = !1,
							tabStopId: l,
							...d
						} = e,
						c = (0, s.B)(),
						f = l || c,
						m = R(A, n),
						v = m.currentTabStopId === f,
						g = h(n),
						{ onFocusableItemAdd: b, onFocusableItemRemove: w } = m;
					return (
						r.useEffect(() => {
							if (a) return b(), () => w();
						}, [a, b, w]),
						(0, p.jsx)(y.ItemSlot, {
							scope: n,
							id: f,
							focusable: a,
							active: i,
							children: (0, p.jsx)(u.sG.span, {
								tabIndex: v ? 0 : -1,
								"data-orientation": m.orientation,
								...d,
								ref: t,
								onMouseDown: (0, o.m)(e.onMouseDown, (e) => {
									a ? m.onItemFocus(f) : e.preventDefault();
								}),
								onFocus: (0, o.m)(e.onFocus, () => m.onItemFocus(f)),
								onKeyDown: (0, o.m)(e.onKeyDown, (e) => {
									if ("Tab" === e.key && e.shiftKey) {
										m.onItemShiftTab();
										return;
									}
									if (e.target !== e.currentTarget) return;
									const t = ((e, t, n) => {
										var r;
										const o =
											((r = e.key),
											"rtl" !== n
												? r
												: "ArrowLeft" === r
													? "ArrowRight"
													: "ArrowRight" === r
														? "ArrowLeft"
														: r);
										if (
											!(
												"vertical" === t &&
												["ArrowLeft", "ArrowRight"].includes(o)
											) &&
											!(
												"horizontal" === t &&
												["ArrowUp", "ArrowDown"].includes(o)
											)
										)
											return j[o];
									})(e, m.orientation, m.dir);
									if (void 0 !== t) {
										if (e.metaKey || e.ctrlKey || e.altKey || e.shiftKey)
											return;
										e.preventDefault();
										let n = g()
											.filter((e) => e.focusable)
											.map((e) => e.ref.current);
										if ("last" === t) n.reverse();
										else if ("prev" === t || "next" === t) {
											"prev" === t && n.reverse();
											const r = n.indexOf(e.currentTarget);
											n = m.loop
												? ((e, t) => e.map((n, r) => e[(t + r) % e.length]))(
														n,
														r + 1,
													)
												: n.slice(r + 1);
										}
										setTimeout(() => E(n));
									}
								}),
							}),
						})
					);
				});
			C.displayName = A;
			var j = {
				ArrowLeft: "prev",
				ArrowUp: "prev",
				ArrowRight: "next",
				ArrowDown: "next",
				PageUp: "first",
				Home: "first",
				PageDown: "last",
				End: "last",
			};
			function E(e) {
				const t =
						arguments.length > 1 && void 0 !== arguments[1] && arguments[1],
					n = document.activeElement;
				for (const r of e)
					if (
						r === n ||
						(r.focus({ preventScroll: t }), document.activeElement !== n)
					)
						return;
			}
			var F = n(9935),
				T = "Tabs",
				[M, O] = (0, a.A)(T, [D]),
				k = D(),
				[_, P] = M(T),
				U = r.forwardRef((e, t) => {
					const {
							__scopeTabs: n,
							value: r,
							onValueChange: o,
							defaultValue: a,
							orientation: i = "horizontal",
							dir: l,
							activationMode: d = "automatic",
							...m
						} = e,
						v = (0, f.jH)(l),
						[g, y] = (0, c.i)({ prop: r, onChange: o, defaultProp: a });
					return (0, p.jsx)(_, {
						scope: n,
						baseId: (0, s.B)(),
						value: g,
						onValueChange: y,
						orientation: i,
						dir: v,
						activationMode: d,
						children: (0, p.jsx)(u.sG.div, {
							dir: v,
							"data-orientation": i,
							...m,
							ref: t,
						}),
					});
				});
			U.displayName = T;
			var G = "TabsList",
				L = r.forwardRef((e, t) => {
					const { __scopeTabs: n, loop: r = !0, ...o } = e,
						a = P(G, n),
						i = k(n);
					return (0, p.jsx)(N, {
						asChild: !0,
						...i,
						orientation: a.orientation,
						dir: a.dir,
						loop: r,
						children: (0, p.jsx)(u.sG.div, {
							role: "tablist",
							"aria-orientation": a.orientation,
							...o,
							ref: t,
						}),
					});
				});
			L.displayName = G;
			var S = "TabsTrigger",
				B = r.forwardRef((e, t) => {
					const { __scopeTabs: n, value: r, disabled: a = !1, ...i } = e,
						l = P(S, n),
						s = k(n),
						d = W(l.baseId, r),
						c = q(l.baseId, r),
						f = r === l.value;
					return (0, p.jsx)(C, {
						asChild: !0,
						...s,
						focusable: !a,
						active: f,
						children: (0, p.jsx)(u.sG.button, {
							type: "button",
							role: "tab",
							"aria-selected": f,
							"aria-controls": c,
							"data-state": f ? "active" : "inactive",
							"data-disabled": a ? "" : void 0,
							disabled: a,
							id: d,
							...i,
							ref: t,
							onMouseDown: (0, o.m)(e.onMouseDown, (e) => {
								a || 0 !== e.button || !1 !== e.ctrlKey
									? e.preventDefault()
									: l.onValueChange(r);
							}),
							onKeyDown: (0, o.m)(e.onKeyDown, (e) => {
								[" ", "Enter"].includes(e.key) && l.onValueChange(r);
							}),
							onFocus: (0, o.m)(e.onFocus, () => {
								const e = "manual" !== l.activationMode;
								f || a || !e || l.onValueChange(r);
							}),
						}),
					});
				});
			B.displayName = S;
			var K = "TabsContent",
				V = r.forwardRef((e, t) => {
					const {
							__scopeTabs: n,
							value: o,
							forceMount: a,
							children: i,
							...l
						} = e,
						s = P(K, n),
						d = W(s.baseId, o),
						c = q(s.baseId, o),
						f = o === s.value,
						m = r.useRef(f);
					return (
						r.useEffect(() => {
							const e = requestAnimationFrame(() => (m.current = !1));
							return () => cancelAnimationFrame(e);
						}, []),
						(0, p.jsx)(F.C, {
							present: a || f,
							children: (n) => {
								const { present: r } = n;
								return (0, p.jsx)(u.sG.div, {
									"data-state": f ? "active" : "inactive",
									"data-orientation": s.orientation,
									role: "tabpanel",
									"aria-labelledby": d,
									hidden: !r,
									id: c,
									tabIndex: 0,
									...l,
									ref: t,
									style: {
										...e.style,
										animationDuration: m.current ? "0s" : void 0,
									},
									children: r && i,
								});
							},
						})
					);
				});
			function W(e, t) {
				return "".concat(e, "-trigger-").concat(t);
			}
			function q(e, t) {
				return "".concat(e, "-content-").concat(t);
			}
			V.displayName = K;
			var H = U,
				z = L,
				Z = B,
				X = V;
		},
		9176: (e, t, n) => {
			n.d(t, { b: () => l });
			var r = n(2149),
				o = n(6330),
				a = n(8081),
				i = r.forwardRef((e, t) =>
					(0, a.jsx)(o.sG.label, {
						...e,
						ref: t,
						onMouseDown: (t) => {
							var n;
							t.target.closest("button, input, select, textarea") ||
								(null === (n = e.onMouseDown) || void 0 === n || n.call(e, t),
								!t.defaultPrevented && t.detail > 1 && t.preventDefault());
						},
					}),
				);
			i.displayName = "Label";
			var l = i;
		},
		9651: (e, t, n) => {
			var r = n(4571);
			n.o(r, "useRouter") && n.d(t, { useRouter: () => r.useRouter });
		},
		9935: (e, t, n) => {
			n.d(t, { C: () => i });
			var r = n(2149),
				o = n(8735),
				a = n(5544),
				i = (e) => {
					const { present: t, children: n } = e,
						i = ((e) => {
							var t, n;
							const [o, i] = r.useState(),
								s = r.useRef({}),
								u = r.useRef(e),
								d = r.useRef("none"),
								[c, f] =
									((t = e ? "mounted" : "unmounted"),
									(n = {
										mounted: {
											UNMOUNT: "unmounted",
											ANIMATION_OUT: "unmountSuspended",
										},
										unmountSuspended: {
											MOUNT: "mounted",
											ANIMATION_END: "unmounted",
										},
										unmounted: { MOUNT: "mounted" },
									}),
									r.useReducer((e, t) => {
										const r = n[e][t];
										return null != r ? r : e;
									}, t));
							return (
								r.useEffect(() => {
									const e = l(s.current);
									d.current = "mounted" === c ? e : "none";
								}, [c]),
								(0, a.N)(() => {
									const t = s.current,
										n = u.current;
									if (n !== e) {
										const r = d.current,
											o = l(t);
										e
											? f("MOUNT")
											: "none" === o ||
													(null == t ? void 0 : t.display) === "none"
												? f("UNMOUNT")
												: n && r !== o
													? f("ANIMATION_OUT")
													: f("UNMOUNT"),
											(u.current = e);
									}
								}, [e, f]),
								(0, a.N)(() => {
									if (o) {
										var e;
										let t;
										const n =
												null !== (e = o.ownerDocument.defaultView) &&
												void 0 !== e
													? e
													: window,
											r = (e) => {
												const r = l(s.current).includes(e.animationName);
												if (
													e.target === o &&
													r &&
													(f("ANIMATION_END"), !u.current)
												) {
													const e = o.style.animationFillMode;
													(o.style.animationFillMode = "forwards"),
														(t = n.setTimeout(() => {
															"forwards" === o.style.animationFillMode &&
																(o.style.animationFillMode = e);
														}));
												}
											},
											a = (e) => {
												e.target === o && (d.current = l(s.current));
											};
										return (
											o.addEventListener("animationstart", a),
											o.addEventListener("animationcancel", r),
											o.addEventListener("animationend", r),
											() => {
												n.clearTimeout(t),
													o.removeEventListener("animationstart", a),
													o.removeEventListener("animationcancel", r),
													o.removeEventListener("animationend", r);
											}
										);
									}
									f("ANIMATION_END");
								}, [o, f]),
								{
									isPresent: ["mounted", "unmountSuspended"].includes(c),
									ref: r.useCallback((e) => {
										e && (s.current = getComputedStyle(e)), i(e);
									}, []),
								}
							);
						})(t),
						s =
							"function" == typeof n
								? n({ present: i.isPresent })
								: r.Children.only(n),
						u = (0, o.s)(
							i.ref,
							((e) => {
								var t, n;
								let r =
										null ===
											(t = Object.getOwnPropertyDescriptor(e.props, "ref")) ||
										void 0 === t
											? void 0
											: t.get,
									o = r && "isReactWarning" in r && r.isReactWarning;
								return o
									? e.ref
									: (o =
												(r =
													null ===
														(n = Object.getOwnPropertyDescriptor(e, "ref")) ||
													void 0 === n
														? void 0
														: n.get) &&
												"isReactWarning" in r &&
												r.isReactWarning)
										? e.props.ref
										: e.props.ref || e.ref;
							})(s),
						);
					return "function" == typeof n || i.isPresent
						? r.cloneElement(s, { ref: u })
						: null;
				};
			function l(e) {
				return (null == e ? void 0 : e.animationName) || "none";
			}
			i.displayName = "Presence";
		},
	},
]);
