(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[874],
	{
		473: (e, r, t) => {
			Object.defineProperty(r, "__esModule", { value: !0 }),
				!((e, r) => {
					for (var t in r)
						Object.defineProperty(e, t, { enumerable: !0, get: r[t] });
				})(r, {
					formatUrl: () => l,
					formatWithValidation: () => i,
					urlObjectKeys: () => a,
				});
			const o = t(3340)._(t(4951)),
				n = /https?|ftp|gopher|file/;
			function l(e) {
				let { auth: r, hostname: t } = e,
					l = e.protocol || "",
					a = e.pathname || "",
					i = e.hash || "",
					s = e.query || "",
					c = !1;
				(r = r ? encodeURIComponent(r).replace(/%3A/i, ":") + "@" : ""),
					e.host
						? (c = r + e.host)
						: t &&
							((c = r + (~t.indexOf(":") ? "[" + t + "]" : t)),
							e.port && (c += ":" + e.port)),
					s &&
						"object" == typeof s &&
						(s = String(o.urlQueryToSearchParams(s)));
				let d = e.search || (s && "?" + s) || "";
				return (
					l && !l.endsWith(":") && (l += ":"),
					e.slashes || ((!l || n.test(l)) && !1 !== c)
						? ((c = "//" + (c || "")), a && "/" !== a[0] && (a = "/" + a))
						: c || (c = ""),
					i && "#" !== i[0] && (i = "#" + i),
					d && "?" !== d[0] && (d = "?" + d),
					"" +
						l +
						c +
						(a = a.replace(/[?#]/g, encodeURIComponent)) +
						(d = d.replace("#", "%23")) +
						i
				);
			}
			const a = [
				"auth",
				"hash",
				"host",
				"hostname",
				"href",
				"path",
				"pathname",
				"port",
				"protocol",
				"query",
				"search",
				"slashes",
			];
			function i(e) {
				return l(e);
			}
		},
		1018: (e, r, t) => {
			t.d(r, { A: () => s });
			var o = t(2149);
			const n = (e) => e.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase(),
				l = () => {
					for (var e = arguments.length, r = Array(e), t = 0; t < e; t++)
						r[t] = arguments[t];
					return r
						.filter((e, r, t) => !!e && "" !== e.trim() && t.indexOf(e) === r)
						.join(" ")
						.trim();
				};
			var a = {
				xmlns: "http://www.w3.org/2000/svg",
				width: 24,
				height: 24,
				viewBox: "0 0 24 24",
				fill: "none",
				stroke: "currentColor",
				strokeWidth: 2,
				strokeLinecap: "round",
				strokeLinejoin: "round",
			};
			const i = (0, o.forwardRef)((e, r) => {
					const {
						color: t = "currentColor",
						size: n = 24,
						strokeWidth: i = 2,
						absoluteStrokeWidth: s,
						className: c = "",
						children: d,
						iconNode: u,
						...p
					} = e;
					return (0, o.createElement)(
						"svg",
						{
							ref: r,
							...a,
							width: n,
							height: n,
							stroke: t,
							strokeWidth: s ? (24 * Number(i)) / Number(n) : i,
							className: l("lucide", c),
							...p,
						},
						[
							...u.map((e) => {
								const [r, t] = e;
								return (0, o.createElement)(r, t);
							}),
							...(Array.isArray(d) ? d : [d]),
						],
					);
				}),
				s = (e, r) => {
					const t = (0, o.forwardRef)((t, a) => {
						const { className: s, ...c } = t;
						return (0, o.createElement)(i, {
							ref: a,
							iconNode: r,
							className: l("lucide-".concat(n(e)), s),
							...c,
						});
					});
					return (t.displayName = "".concat(e)), t;
				};
		},
		1747: (e, r) => {
			Object.defineProperty(r, "__esModule", { value: !0 }),
				!((e, r) => {
					for (var t in r)
						Object.defineProperty(e, t, { enumerable: !0, get: r[t] });
				})(r, {
					DecodeError: () => b,
					MiddlewareNotFoundError: () => y,
					MissingStaticPage: () => h,
					NormalizeError: () => m,
					PageNotFoundError: () => g,
					SP: () => p,
					ST: () => f,
					WEB_VITALS: () => t,
					execOnce: () => o,
					getDisplayName: () => s,
					getLocationOrigin: () => a,
					getURL: () => i,
					isAbsoluteUrl: () => l,
					isResSent: () => c,
					loadGetInitialProps: () => u,
					normalizeRepeatedSlashes: () => d,
					stringifyError: () => v,
				});
			const t = ["CLS", "FCP", "FID", "INP", "LCP", "TTFB"];
			function o(e) {
				let r,
					t = !1;
				return () => {
					for (var o = arguments.length, n = Array(o), l = 0; l < o; l++)
						n[l] = arguments[l];
					return t || ((t = !0), (r = e(...n))), r;
				};
			}
			const n = /^[a-zA-Z][a-zA-Z\d+\-.]*?:/,
				l = (e) => n.test(e);
			function a() {
				const { protocol: e, hostname: r, port: t } = window.location;
				return e + "//" + r + (t ? ":" + t : "");
			}
			function i() {
				const { href: e } = window.location,
					r = a();
				return e.substring(r.length);
			}
			function s(e) {
				return "string" == typeof e ? e : e.displayName || e.name || "Unknown";
			}
			function c(e) {
				return e.finished || e.headersSent;
			}
			function d(e) {
				const r = e.split("?");
				return (
					r[0].replace(/\\/g, "/").replace(/\/\/+/g, "/") +
					(r[1] ? "?" + r.slice(1).join("?") : "")
				);
			}
			async function u(e, r) {
				const t = r.res || (r.ctx && r.ctx.res);
				if (!e.getInitialProps)
					return r.ctx && r.Component
						? { pageProps: await u(r.Component, r.ctx) }
						: {};
				const o = await e.getInitialProps(r);
				if (t && c(t)) return o;
				if (!o)
					throw Object.defineProperty(
						Error(
							'"' +
								s(e) +
								'.getInitialProps()" should resolve to an object. But found "' +
								o +
								'" instead.',
						),
						"__NEXT_ERROR_CODE",
						{ value: "E394", enumerable: !1, configurable: !0 },
					);
				return o;
			}
			const p = "undefined" != typeof performance,
				f =
					p &&
					["mark", "measure", "getEntriesByName"].every(
						(e) => "function" == typeof performance[e],
					);
			class b extends Error {}
			class m extends Error {}
			class g extends Error {
				constructor(e) {
					super(),
						(this.code = "ENOENT"),
						(this.name = "PageNotFoundError"),
						(this.message = "Cannot find module for page: " + e);
				}
			}
			class h extends Error {
				constructor(e, r) {
					super(),
						(this.message =
							"Failed to load static file for page: " + e + " " + r);
				}
			}
			class y extends Error {
				constructor() {
					super(),
						(this.code = "ENOENT"),
						(this.message = "Cannot find the middleware module");
				}
			}
			function v(e) {
				return JSON.stringify({ message: e.message, stack: e.stack });
			}
		},
		3484: (e, r, t) => {
			t.d(r, { F: () => a });
			var o = t(6522);
			const n = (e) => ("boolean" == typeof e ? `${e}` : 0 === e ? "0" : e),
				l = o.$,
				a = (e, r) => (t) => {
					var o;
					if ((null == r ? void 0 : r.variants) == null)
						return l(
							e,
							null == t ? void 0 : t.class,
							null == t ? void 0 : t.className,
						);
					const { variants: a, defaultVariants: i } = r,
						s = Object.keys(a).map((e) => {
							const r = null == t ? void 0 : t[e],
								o = null == i ? void 0 : i[e];
							if (null === r) return null;
							const l = n(r) || n(o);
							return a[e][l];
						}),
						c =
							t &&
							Object.entries(t).reduce((e, r) => {
								const [t, o] = r;
								return void 0 === o || (e[t] = o), e;
							}, {});
					return l(
						e,
						s,
						null == r
							? void 0
							: null === (o = r.compoundVariants) || void 0 === o
								? void 0
								: o.reduce((e, r) => {
										const { class: t, className: o, ...n } = r;
										return Object.entries(n).every((e) => {
											const [r, t] = e;
											return Array.isArray(t)
												? t.includes({ ...i, ...c }[r])
												: { ...i, ...c }[r] === t;
										})
											? [...e, t, o]
											: e;
									}, []),
						null == t ? void 0 : t.class,
						null == t ? void 0 : t.className,
					);
				};
		},
		3629: (e, r, t) => {
			t.d(r, { DX: () => a });
			var o = t(2149),
				n = t(8735),
				l = t(8081),
				a = o.forwardRef((e, r) => {
					const { children: t, ...n } = e,
						a = o.Children.toArray(t),
						s = a.find(c);
					if (s) {
						const e = s.props.children,
							t = a.map((r) =>
								r !== s
									? r
									: o.Children.count(e) > 1
										? o.Children.only(null)
										: o.isValidElement(e)
											? e.props.children
											: null,
							);
						return (0, l.jsx)(i, {
							...n,
							ref: r,
							children: o.isValidElement(e)
								? o.cloneElement(e, void 0, t)
								: null,
						});
					}
					return (0, l.jsx)(i, { ...n, ref: r, children: t });
				});
			a.displayName = "Slot";
			var i = o.forwardRef((e, r) => {
				const { children: t, ...l } = e;
				if (o.isValidElement(t)) {
					const e = ((e) => {
						let r = Object.getOwnPropertyDescriptor(e.props, "ref")?.get,
							t = r && "isReactWarning" in r && r.isReactWarning;
						return t
							? e.ref
							: (t =
										(r = Object.getOwnPropertyDescriptor(e, "ref")?.get) &&
										"isReactWarning" in r &&
										r.isReactWarning)
								? e.props.ref
								: e.props.ref || e.ref;
					})(t);
					return o.cloneElement(t, {
						...((e, r) => {
							const t = { ...r };
							for (const o in r) {
								const n = e[o],
									l = r[o];
								/^on[A-Z]/.test(o)
									? n && l
										? (t[o] = (...e) => {
												l(...e), n(...e);
											})
										: n && (t[o] = n)
									: "style" === o
										? (t[o] = { ...n, ...l })
										: "className" === o &&
											(t[o] = [n, l].filter(Boolean).join(" "));
							}
							return { ...e, ...t };
						})(l, t.props),
						ref: r ? (0, n.t)(r, e) : e,
					});
				}
				return o.Children.count(t) > 1 ? o.Children.only(null) : null;
			});
			i.displayName = "SlotClone";
			var s = ({ children: e }) => (0, l.jsx)(l.Fragment, { children: e });
			function c(e) {
				return o.isValidElement(e) && e.type === s;
			}
		},
		3778: (e, r, t) => {
			Object.defineProperty(r, "__esModule", { value: !0 }),
				Object.defineProperty(r, "useMergedRef", {
					enumerable: !0,
					get: () => n,
				});
			const o = t(2149);
			function n(e, r) {
				const t = (0, o.useRef)(null),
					n = (0, o.useRef)(null);
				return (0, o.useCallback)(
					(o) => {
						if (null === o) {
							const e = t.current;
							e && ((t.current = null), e());
							const r = n.current;
							r && ((n.current = null), r());
						} else e && (t.current = l(e, o)), r && (n.current = l(r, o));
					},
					[e, r],
				);
			}
			function l(e, r) {
				if ("function" != typeof e)
					return (
						(e.current = r),
						() => {
							e.current = null;
						}
					);
				{
					const t = e(r);
					return "function" == typeof t ? t : () => e(null);
				}
			}
			("function" == typeof r.default ||
				("object" == typeof r.default && null !== r.default)) &&
				void 0 === r.default.__esModule &&
				(Object.defineProperty(r.default, "__esModule", { value: !0 }),
				Object.assign(r.default, r),
				(e.exports = r.default));
		},
		4483: (e, r, t) => {
			t.d(r, { QP: () => Z });
			const o = (e) => {
					const r = i(e),
						{ conflictingClassGroups: t, conflictingClassGroupModifiers: o } =
							e;
					return {
						getClassGroupId: (e) => {
							const t = e.split("-");
							return (
								"" === t[0] && 1 !== t.length && t.shift(), n(t, r) || a(e)
							);
						},
						getConflictingClassGroupIds: (e, r) => {
							const n = t[e] || [];
							return r && o[e] ? [...n, ...o[e]] : n;
						},
					};
				},
				n = (e, r) => {
					if (0 === e.length) return r.classGroupId;
					const t = e[0],
						o = r.nextPart.get(t),
						l = o ? n(e.slice(1), o) : void 0;
					if (l) return l;
					if (0 === r.validators.length) return;
					const a = e.join("-");
					return r.validators.find(({ validator: e }) => e(a))?.classGroupId;
				},
				l = /^\[(.+)\]$/,
				a = (e) => {
					if (l.test(e)) {
						const r = l.exec(e)[1],
							t = r?.substring(0, r.indexOf(":"));
						if (t) return "arbitrary.." + t;
					}
				},
				i = (e) => {
					const { theme: r, prefix: t } = e,
						o = { nextPart: new Map(), validators: [] };
					return (
						u(Object.entries(e.classGroups), t).forEach(([e, t]) => {
							s(t, o, e, r);
						}),
						o
					);
				},
				s = (e, r, t, o) => {
					e.forEach((e) => {
						if ("string" == typeof e) {
							("" === e ? r : c(r, e)).classGroupId = t;
							return;
						}
						if ("function" == typeof e) {
							if (d(e)) {
								s(e(o), r, t, o);
								return;
							}
							r.validators.push({ validator: e, classGroupId: t });
							return;
						}
						Object.entries(e).forEach(([e, n]) => {
							s(n, c(r, e), t, o);
						});
					});
				},
				c = (e, r) => {
					let t = e;
					return (
						r.split("-").forEach((e) => {
							t.nextPart.has(e) ||
								t.nextPart.set(e, { nextPart: new Map(), validators: [] }),
								(t = t.nextPart.get(e));
						}),
						t
					);
				},
				d = (e) => e.isThemeGetter,
				u = (e, r) =>
					r
						? e.map(([e, t]) => [
								e,
								t.map((e) =>
									"string" == typeof e
										? r + e
										: "object" == typeof e
											? Object.fromEntries(
													Object.entries(e).map(([e, t]) => [r + e, t]),
												)
											: e,
								),
							])
						: e,
				p = (e) => {
					if (e < 1) return { get: () => void 0, set: () => {} };
					let r = 0,
						t = new Map(),
						o = new Map(),
						n = (n, l) => {
							t.set(n, l), ++r > e && ((r = 0), (o = t), (t = new Map()));
						};
					return {
						get(e) {
							let r = t.get(e);
							return void 0 !== r
								? r
								: void 0 !== (r = o.get(e))
									? (n(e, r), r)
									: void 0;
						},
						set(e, r) {
							t.has(e) ? t.set(e, r) : n(e, r);
						},
					};
				},
				f = (e) => {
					const { separator: r, experimentalParseClassName: t } = e,
						o = 1 === r.length,
						n = r[0],
						l = r.length,
						a = (e) => {
							let t;
							let a = [],
								i = 0,
								s = 0;
							for (let c = 0; c < e.length; c++) {
								const d = e[c];
								if (0 === i) {
									if (d === n && (o || e.slice(c, c + l) === r)) {
										a.push(e.slice(s, c)), (s = c + l);
										continue;
									}
									if ("/" === d) {
										t = c;
										continue;
									}
								}
								"[" === d ? i++ : "]" === d && i--;
							}
							const c = 0 === a.length ? e : e.substring(s),
								d = c.startsWith("!"),
								u = d ? c.substring(1) : c;
							return {
								modifiers: a,
								hasImportantModifier: d,
								baseClassName: u,
								maybePostfixModifierPosition: t && t > s ? t - s : void 0,
							};
						};
					return t ? (e) => t({ className: e, parseClassName: a }) : a;
				},
				b = (e) => {
					if (e.length <= 1) return e;
					let r = [],
						t = [];
					return (
						e.forEach((e) => {
							"[" === e[0] ? (r.push(...t.sort(), e), (t = [])) : t.push(e);
						}),
						r.push(...t.sort()),
						r
					);
				},
				m = (e) => ({ cache: p(e.cacheSize), parseClassName: f(e), ...o(e) }),
				g = /\s+/,
				h = (e, r) => {
					let {
							parseClassName: t,
							getClassGroupId: o,
							getConflictingClassGroupIds: n,
						} = r,
						l = [],
						a = e.trim().split(g),
						i = "";
					for (let e = a.length - 1; e >= 0; e -= 1) {
						let r = a[e],
							{
								modifiers: s,
								hasImportantModifier: c,
								baseClassName: d,
								maybePostfixModifierPosition: u,
							} = t(r),
							p = !!u,
							f = o(p ? d.substring(0, u) : d);
						if (!f) {
							if (!p || !(f = o(d))) {
								i = r + (i.length > 0 ? " " + i : i);
								continue;
							}
							p = !1;
						}
						const m = b(s).join(":"),
							g = c ? m + "!" : m,
							h = g + f;
						if (l.includes(h)) continue;
						l.push(h);
						const y = n(f, p);
						for (let e = 0; e < y.length; ++e) {
							const r = y[e];
							l.push(g + r);
						}
						i = r + (i.length > 0 ? " " + i : i);
					}
					return i;
				};
			function y() {
				let e,
					r,
					t = 0,
					o = "";
				while (t < arguments.length)
					(e = arguments[t++]) && (r = v(e)) && (o && (o += " "), (o += r));
				return o;
			}
			const v = (e) => {
					let r;
					if ("string" == typeof e) return e;
					let t = "";
					for (let o = 0; o < e.length; o++)
						e[o] && (r = v(e[o])) && (t && (t += " "), (t += r));
					return t;
				},
				x = (e) => {
					const r = (r) => r[e] || [];
					return (r.isThemeGetter = !0), r;
				},
				w = /^\[(?:([a-z-]+):)?(.+)\]$/i,
				k = /^\d+\/\d+$/,
				j = new Set(["px", "full", "screen"]),
				z = /^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,
				P =
					/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,
				E = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,
				C = /^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,
				O =
					/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,
				N = (e) => _(e) || j.has(e) || k.test(e),
				S = (e) => B(e, "length", K),
				_ = (e) => !!e && !Number.isNaN(Number(e)),
				A = (e) => B(e, "number", _),
				R = (e) => !!e && Number.isInteger(Number(e)),
				M = (e) => e.endsWith("%") && _(e.slice(0, -1)),
				T = (e) => w.test(e),
				I = (e) => z.test(e),
				L = new Set(["length", "size", "percentage"]),
				U = (e) => B(e, L, V),
				W = (e) => B(e, "position", V),
				G = new Set(["image", "url"]),
				$ = (e) => B(e, G, Q),
				F = (e) => B(e, "", q),
				D = () => !0,
				B = (e, r, t) => {
					const o = w.exec(e);
					return (
						!!o &&
						(o[1] ? ("string" == typeof r ? o[1] === r : r.has(o[1])) : t(o[2]))
					);
				},
				K = (e) => P.test(e) && !E.test(e),
				V = () => !1,
				q = (e) => C.test(e),
				Q = (e) => O.test(e);
			Symbol.toStringTag;
			const Z = ((e, ...r) => {
				let t, o, n;
				let l = (i) => (
					(o = (t = m(r.reduce((e, r) => r(e), e()))).cache.get),
					(n = t.cache.set),
					(l = a),
					a(i)
				);
				function a(e) {
					const r = o(e);
					if (r) return r;
					const l = h(e, t);
					return n(e, l), l;
				}
				return () => l(y.apply(null, arguments));
			})(() => {
				const e = x("colors"),
					r = x("spacing"),
					t = x("blur"),
					o = x("brightness"),
					n = x("borderColor"),
					l = x("borderRadius"),
					a = x("borderSpacing"),
					i = x("borderWidth"),
					s = x("contrast"),
					c = x("grayscale"),
					d = x("hueRotate"),
					u = x("invert"),
					p = x("gap"),
					f = x("gradientColorStops"),
					b = x("gradientColorStopPositions"),
					m = x("inset"),
					g = x("margin"),
					h = x("opacity"),
					y = x("padding"),
					v = x("saturate"),
					w = x("scale"),
					k = x("sepia"),
					j = x("skew"),
					z = x("space"),
					P = x("translate"),
					E = () => ["auto", "contain", "none"],
					C = () => ["auto", "hidden", "clip", "visible", "scroll"],
					O = () => ["auto", T, r],
					L = () => [T, r],
					G = () => ["", N, S],
					B = () => ["auto", _, T],
					K = () => [
						"bottom",
						"center",
						"left",
						"left-bottom",
						"left-top",
						"right",
						"right-bottom",
						"right-top",
						"top",
					],
					V = () => ["solid", "dashed", "dotted", "double", "none"],
					q = () => [
						"normal",
						"multiply",
						"screen",
						"overlay",
						"darken",
						"lighten",
						"color-dodge",
						"color-burn",
						"hard-light",
						"soft-light",
						"difference",
						"exclusion",
						"hue",
						"saturation",
						"color",
						"luminosity",
					],
					Q = () => [
						"start",
						"end",
						"center",
						"between",
						"around",
						"evenly",
						"stretch",
					],
					Z = () => ["", "0", T],
					X = () => [
						"auto",
						"avoid",
						"all",
						"avoid-page",
						"page",
						"left",
						"right",
						"column",
					],
					J = () => [_, T];
				return {
					cacheSize: 500,
					separator: ":",
					theme: {
						colors: [D],
						spacing: [N, S],
						blur: ["none", "", I, T],
						brightness: J(),
						borderColor: [e],
						borderRadius: ["none", "", "full", I, T],
						borderSpacing: L(),
						borderWidth: G(),
						contrast: J(),
						grayscale: Z(),
						hueRotate: J(),
						invert: Z(),
						gap: L(),
						gradientColorStops: [e],
						gradientColorStopPositions: [M, S],
						inset: O(),
						margin: O(),
						opacity: J(),
						padding: L(),
						saturate: J(),
						scale: J(),
						sepia: Z(),
						skew: J(),
						space: L(),
						translate: L(),
					},
					classGroups: {
						aspect: [{ aspect: ["auto", "square", "video", T] }],
						container: ["container"],
						columns: [{ columns: [I] }],
						"break-after": [{ "break-after": X() }],
						"break-before": [{ "break-before": X() }],
						"break-inside": [
							{
								"break-inside": ["auto", "avoid", "avoid-page", "avoid-column"],
							},
						],
						"box-decoration": [{ "box-decoration": ["slice", "clone"] }],
						box: [{ box: ["border", "content"] }],
						display: [
							"block",
							"inline-block",
							"inline",
							"flex",
							"inline-flex",
							"table",
							"inline-table",
							"table-caption",
							"table-cell",
							"table-column",
							"table-column-group",
							"table-footer-group",
							"table-header-group",
							"table-row-group",
							"table-row",
							"flow-root",
							"grid",
							"inline-grid",
							"contents",
							"list-item",
							"hidden",
						],
						float: [{ float: ["right", "left", "none", "start", "end"] }],
						clear: [
							{ clear: ["left", "right", "both", "none", "start", "end"] },
						],
						isolation: ["isolate", "isolation-auto"],
						"object-fit": [
							{ object: ["contain", "cover", "fill", "none", "scale-down"] },
						],
						"object-position": [{ object: [...K(), T] }],
						overflow: [{ overflow: C() }],
						"overflow-x": [{ "overflow-x": C() }],
						"overflow-y": [{ "overflow-y": C() }],
						overscroll: [{ overscroll: E() }],
						"overscroll-x": [{ "overscroll-x": E() }],
						"overscroll-y": [{ "overscroll-y": E() }],
						position: ["static", "fixed", "absolute", "relative", "sticky"],
						inset: [{ inset: [m] }],
						"inset-x": [{ "inset-x": [m] }],
						"inset-y": [{ "inset-y": [m] }],
						start: [{ start: [m] }],
						end: [{ end: [m] }],
						top: [{ top: [m] }],
						right: [{ right: [m] }],
						bottom: [{ bottom: [m] }],
						left: [{ left: [m] }],
						visibility: ["visible", "invisible", "collapse"],
						z: [{ z: ["auto", R, T] }],
						basis: [{ basis: O() }],
						"flex-direction": [
							{ flex: ["row", "row-reverse", "col", "col-reverse"] },
						],
						"flex-wrap": [{ flex: ["wrap", "wrap-reverse", "nowrap"] }],
						flex: [{ flex: ["1", "auto", "initial", "none", T] }],
						grow: [{ grow: Z() }],
						shrink: [{ shrink: Z() }],
						order: [{ order: ["first", "last", "none", R, T] }],
						"grid-cols": [{ "grid-cols": [D] }],
						"col-start-end": [{ col: ["auto", { span: ["full", R, T] }, T] }],
						"col-start": [{ "col-start": B() }],
						"col-end": [{ "col-end": B() }],
						"grid-rows": [{ "grid-rows": [D] }],
						"row-start-end": [{ row: ["auto", { span: [R, T] }, T] }],
						"row-start": [{ "row-start": B() }],
						"row-end": [{ "row-end": B() }],
						"grid-flow": [
							{
								"grid-flow": ["row", "col", "dense", "row-dense", "col-dense"],
							},
						],
						"auto-cols": [{ "auto-cols": ["auto", "min", "max", "fr", T] }],
						"auto-rows": [{ "auto-rows": ["auto", "min", "max", "fr", T] }],
						gap: [{ gap: [p] }],
						"gap-x": [{ "gap-x": [p] }],
						"gap-y": [{ "gap-y": [p] }],
						"justify-content": [{ justify: ["normal", ...Q()] }],
						"justify-items": [
							{ "justify-items": ["start", "end", "center", "stretch"] },
						],
						"justify-self": [
							{ "justify-self": ["auto", "start", "end", "center", "stretch"] },
						],
						"align-content": [{ content: ["normal", ...Q(), "baseline"] }],
						"align-items": [
							{ items: ["start", "end", "center", "baseline", "stretch"] },
						],
						"align-self": [
							{
								self: ["auto", "start", "end", "center", "stretch", "baseline"],
							},
						],
						"place-content": [{ "place-content": [...Q(), "baseline"] }],
						"place-items": [
							{
								"place-items": [
									"start",
									"end",
									"center",
									"baseline",
									"stretch",
								],
							},
						],
						"place-self": [
							{ "place-self": ["auto", "start", "end", "center", "stretch"] },
						],
						p: [{ p: [y] }],
						px: [{ px: [y] }],
						py: [{ py: [y] }],
						ps: [{ ps: [y] }],
						pe: [{ pe: [y] }],
						pt: [{ pt: [y] }],
						pr: [{ pr: [y] }],
						pb: [{ pb: [y] }],
						pl: [{ pl: [y] }],
						m: [{ m: [g] }],
						mx: [{ mx: [g] }],
						my: [{ my: [g] }],
						ms: [{ ms: [g] }],
						me: [{ me: [g] }],
						mt: [{ mt: [g] }],
						mr: [{ mr: [g] }],
						mb: [{ mb: [g] }],
						ml: [{ ml: [g] }],
						"space-x": [{ "space-x": [z] }],
						"space-x-reverse": ["space-x-reverse"],
						"space-y": [{ "space-y": [z] }],
						"space-y-reverse": ["space-y-reverse"],
						w: [
							{ w: ["auto", "min", "max", "fit", "svw", "lvw", "dvw", T, r] },
						],
						"min-w": [{ "min-w": [T, r, "min", "max", "fit"] }],
						"max-w": [
							{
								"max-w": [
									T,
									r,
									"none",
									"full",
									"min",
									"max",
									"fit",
									"prose",
									{ screen: [I] },
									I,
								],
							},
						],
						h: [
							{ h: [T, r, "auto", "min", "max", "fit", "svh", "lvh", "dvh"] },
						],
						"min-h": [
							{ "min-h": [T, r, "min", "max", "fit", "svh", "lvh", "dvh"] },
						],
						"max-h": [
							{ "max-h": [T, r, "min", "max", "fit", "svh", "lvh", "dvh"] },
						],
						size: [{ size: [T, r, "auto", "min", "max", "fit"] }],
						"font-size": [{ text: ["base", I, S] }],
						"font-smoothing": ["antialiased", "subpixel-antialiased"],
						"font-style": ["italic", "not-italic"],
						"font-weight": [
							{
								font: [
									"thin",
									"extralight",
									"light",
									"normal",
									"medium",
									"semibold",
									"bold",
									"extrabold",
									"black",
									A,
								],
							},
						],
						"font-family": [{ font: [D] }],
						"fvn-normal": ["normal-nums"],
						"fvn-ordinal": ["ordinal"],
						"fvn-slashed-zero": ["slashed-zero"],
						"fvn-figure": ["lining-nums", "oldstyle-nums"],
						"fvn-spacing": ["proportional-nums", "tabular-nums"],
						"fvn-fraction": ["diagonal-fractions", "stacked-fractions"],
						tracking: [
							{
								tracking: [
									"tighter",
									"tight",
									"normal",
									"wide",
									"wider",
									"widest",
									T,
								],
							},
						],
						"line-clamp": [{ "line-clamp": ["none", _, A] }],
						leading: [
							{
								leading: [
									"none",
									"tight",
									"snug",
									"normal",
									"relaxed",
									"loose",
									N,
									T,
								],
							},
						],
						"list-image": [{ "list-image": ["none", T] }],
						"list-style-type": [{ list: ["none", "disc", "decimal", T] }],
						"list-style-position": [{ list: ["inside", "outside"] }],
						"placeholder-color": [{ placeholder: [e] }],
						"placeholder-opacity": [{ "placeholder-opacity": [h] }],
						"text-alignment": [
							{ text: ["left", "center", "right", "justify", "start", "end"] },
						],
						"text-color": [{ text: [e] }],
						"text-opacity": [{ "text-opacity": [h] }],
						"text-decoration": [
							"underline",
							"overline",
							"line-through",
							"no-underline",
						],
						"text-decoration-style": [{ decoration: [...V(), "wavy"] }],
						"text-decoration-thickness": [
							{ decoration: ["auto", "from-font", N, S] },
						],
						"underline-offset": [{ "underline-offset": ["auto", N, T] }],
						"text-decoration-color": [{ decoration: [e] }],
						"text-transform": [
							"uppercase",
							"lowercase",
							"capitalize",
							"normal-case",
						],
						"text-overflow": ["truncate", "text-ellipsis", "text-clip"],
						"text-wrap": [{ text: ["wrap", "nowrap", "balance", "pretty"] }],
						indent: [{ indent: L() }],
						"vertical-align": [
							{
								align: [
									"baseline",
									"top",
									"middle",
									"bottom",
									"text-top",
									"text-bottom",
									"sub",
									"super",
									T,
								],
							},
						],
						whitespace: [
							{
								whitespace: [
									"normal",
									"nowrap",
									"pre",
									"pre-line",
									"pre-wrap",
									"break-spaces",
								],
							},
						],
						break: [{ break: ["normal", "words", "all", "keep"] }],
						hyphens: [{ hyphens: ["none", "manual", "auto"] }],
						content: [{ content: ["none", T] }],
						"bg-attachment": [{ bg: ["fixed", "local", "scroll"] }],
						"bg-clip": [
							{ "bg-clip": ["border", "padding", "content", "text"] },
						],
						"bg-opacity": [{ "bg-opacity": [h] }],
						"bg-origin": [{ "bg-origin": ["border", "padding", "content"] }],
						"bg-position": [{ bg: [...K(), W] }],
						"bg-repeat": [
							{
								bg: ["no-repeat", { repeat: ["", "x", "y", "round", "space"] }],
							},
						],
						"bg-size": [{ bg: ["auto", "cover", "contain", U] }],
						"bg-image": [
							{
								bg: [
									"none",
									{
										"gradient-to": ["t", "tr", "r", "br", "b", "bl", "l", "tl"],
									},
									$,
								],
							},
						],
						"bg-color": [{ bg: [e] }],
						"gradient-from-pos": [{ from: [b] }],
						"gradient-via-pos": [{ via: [b] }],
						"gradient-to-pos": [{ to: [b] }],
						"gradient-from": [{ from: [f] }],
						"gradient-via": [{ via: [f] }],
						"gradient-to": [{ to: [f] }],
						rounded: [{ rounded: [l] }],
						"rounded-s": [{ "rounded-s": [l] }],
						"rounded-e": [{ "rounded-e": [l] }],
						"rounded-t": [{ "rounded-t": [l] }],
						"rounded-r": [{ "rounded-r": [l] }],
						"rounded-b": [{ "rounded-b": [l] }],
						"rounded-l": [{ "rounded-l": [l] }],
						"rounded-ss": [{ "rounded-ss": [l] }],
						"rounded-se": [{ "rounded-se": [l] }],
						"rounded-ee": [{ "rounded-ee": [l] }],
						"rounded-es": [{ "rounded-es": [l] }],
						"rounded-tl": [{ "rounded-tl": [l] }],
						"rounded-tr": [{ "rounded-tr": [l] }],
						"rounded-br": [{ "rounded-br": [l] }],
						"rounded-bl": [{ "rounded-bl": [l] }],
						"border-w": [{ border: [i] }],
						"border-w-x": [{ "border-x": [i] }],
						"border-w-y": [{ "border-y": [i] }],
						"border-w-s": [{ "border-s": [i] }],
						"border-w-e": [{ "border-e": [i] }],
						"border-w-t": [{ "border-t": [i] }],
						"border-w-r": [{ "border-r": [i] }],
						"border-w-b": [{ "border-b": [i] }],
						"border-w-l": [{ "border-l": [i] }],
						"border-opacity": [{ "border-opacity": [h] }],
						"border-style": [{ border: [...V(), "hidden"] }],
						"divide-x": [{ "divide-x": [i] }],
						"divide-x-reverse": ["divide-x-reverse"],
						"divide-y": [{ "divide-y": [i] }],
						"divide-y-reverse": ["divide-y-reverse"],
						"divide-opacity": [{ "divide-opacity": [h] }],
						"divide-style": [{ divide: V() }],
						"border-color": [{ border: [n] }],
						"border-color-x": [{ "border-x": [n] }],
						"border-color-y": [{ "border-y": [n] }],
						"border-color-s": [{ "border-s": [n] }],
						"border-color-e": [{ "border-e": [n] }],
						"border-color-t": [{ "border-t": [n] }],
						"border-color-r": [{ "border-r": [n] }],
						"border-color-b": [{ "border-b": [n] }],
						"border-color-l": [{ "border-l": [n] }],
						"divide-color": [{ divide: [n] }],
						"outline-style": [{ outline: ["", ...V()] }],
						"outline-offset": [{ "outline-offset": [N, T] }],
						"outline-w": [{ outline: [N, S] }],
						"outline-color": [{ outline: [e] }],
						"ring-w": [{ ring: G() }],
						"ring-w-inset": ["ring-inset"],
						"ring-color": [{ ring: [e] }],
						"ring-opacity": [{ "ring-opacity": [h] }],
						"ring-offset-w": [{ "ring-offset": [N, S] }],
						"ring-offset-color": [{ "ring-offset": [e] }],
						shadow: [{ shadow: ["", "inner", "none", I, F] }],
						"shadow-color": [{ shadow: [D] }],
						opacity: [{ opacity: [h] }],
						"mix-blend": [
							{ "mix-blend": [...q(), "plus-lighter", "plus-darker"] },
						],
						"bg-blend": [{ "bg-blend": q() }],
						filter: [{ filter: ["", "none"] }],
						blur: [{ blur: [t] }],
						brightness: [{ brightness: [o] }],
						contrast: [{ contrast: [s] }],
						"drop-shadow": [{ "drop-shadow": ["", "none", I, T] }],
						grayscale: [{ grayscale: [c] }],
						"hue-rotate": [{ "hue-rotate": [d] }],
						invert: [{ invert: [u] }],
						saturate: [{ saturate: [v] }],
						sepia: [{ sepia: [k] }],
						"backdrop-filter": [{ "backdrop-filter": ["", "none"] }],
						"backdrop-blur": [{ "backdrop-blur": [t] }],
						"backdrop-brightness": [{ "backdrop-brightness": [o] }],
						"backdrop-contrast": [{ "backdrop-contrast": [s] }],
						"backdrop-grayscale": [{ "backdrop-grayscale": [c] }],
						"backdrop-hue-rotate": [{ "backdrop-hue-rotate": [d] }],
						"backdrop-invert": [{ "backdrop-invert": [u] }],
						"backdrop-opacity": [{ "backdrop-opacity": [h] }],
						"backdrop-saturate": [{ "backdrop-saturate": [v] }],
						"backdrop-sepia": [{ "backdrop-sepia": [k] }],
						"border-collapse": [{ border: ["collapse", "separate"] }],
						"border-spacing": [{ "border-spacing": [a] }],
						"border-spacing-x": [{ "border-spacing-x": [a] }],
						"border-spacing-y": [{ "border-spacing-y": [a] }],
						"table-layout": [{ table: ["auto", "fixed"] }],
						caption: [{ caption: ["top", "bottom"] }],
						transition: [
							{
								transition: [
									"none",
									"all",
									"",
									"colors",
									"opacity",
									"shadow",
									"transform",
									T,
								],
							},
						],
						duration: [{ duration: J() }],
						ease: [{ ease: ["linear", "in", "out", "in-out", T] }],
						delay: [{ delay: J() }],
						animate: [
							{ animate: ["none", "spin", "ping", "pulse", "bounce", T] },
						],
						transform: [{ transform: ["", "gpu", "none"] }],
						scale: [{ scale: [w] }],
						"scale-x": [{ "scale-x": [w] }],
						"scale-y": [{ "scale-y": [w] }],
						rotate: [{ rotate: [R, T] }],
						"translate-x": [{ "translate-x": [P] }],
						"translate-y": [{ "translate-y": [P] }],
						"skew-x": [{ "skew-x": [j] }],
						"skew-y": [{ "skew-y": [j] }],
						"transform-origin": [
							{
								origin: [
									"center",
									"top",
									"top-right",
									"right",
									"bottom-right",
									"bottom",
									"bottom-left",
									"left",
									"top-left",
									T,
								],
							},
						],
						accent: [{ accent: ["auto", e] }],
						appearance: [{ appearance: ["none", "auto"] }],
						cursor: [
							{
								cursor: [
									"auto",
									"default",
									"pointer",
									"wait",
									"text",
									"move",
									"help",
									"not-allowed",
									"none",
									"context-menu",
									"progress",
									"cell",
									"crosshair",
									"vertical-text",
									"alias",
									"copy",
									"no-drop",
									"grab",
									"grabbing",
									"all-scroll",
									"col-resize",
									"row-resize",
									"n-resize",
									"e-resize",
									"s-resize",
									"w-resize",
									"ne-resize",
									"nw-resize",
									"se-resize",
									"sw-resize",
									"ew-resize",
									"ns-resize",
									"nesw-resize",
									"nwse-resize",
									"zoom-in",
									"zoom-out",
									T,
								],
							},
						],
						"caret-color": [{ caret: [e] }],
						"pointer-events": [{ "pointer-events": ["none", "auto"] }],
						resize: [{ resize: ["none", "y", "x", ""] }],
						"scroll-behavior": [{ scroll: ["auto", "smooth"] }],
						"scroll-m": [{ "scroll-m": L() }],
						"scroll-mx": [{ "scroll-mx": L() }],
						"scroll-my": [{ "scroll-my": L() }],
						"scroll-ms": [{ "scroll-ms": L() }],
						"scroll-me": [{ "scroll-me": L() }],
						"scroll-mt": [{ "scroll-mt": L() }],
						"scroll-mr": [{ "scroll-mr": L() }],
						"scroll-mb": [{ "scroll-mb": L() }],
						"scroll-ml": [{ "scroll-ml": L() }],
						"scroll-p": [{ "scroll-p": L() }],
						"scroll-px": [{ "scroll-px": L() }],
						"scroll-py": [{ "scroll-py": L() }],
						"scroll-ps": [{ "scroll-ps": L() }],
						"scroll-pe": [{ "scroll-pe": L() }],
						"scroll-pt": [{ "scroll-pt": L() }],
						"scroll-pr": [{ "scroll-pr": L() }],
						"scroll-pb": [{ "scroll-pb": L() }],
						"scroll-pl": [{ "scroll-pl": L() }],
						"snap-align": [{ snap: ["start", "end", "center", "align-none"] }],
						"snap-stop": [{ snap: ["normal", "always"] }],
						"snap-type": [{ snap: ["none", "x", "y", "both"] }],
						"snap-strictness": [{ snap: ["mandatory", "proximity"] }],
						touch: [{ touch: ["auto", "none", "manipulation"] }],
						"touch-x": [{ "touch-pan": ["x", "left", "right"] }],
						"touch-y": [{ "touch-pan": ["y", "up", "down"] }],
						"touch-pz": ["touch-pinch-zoom"],
						select: [{ select: ["none", "text", "all", "auto"] }],
						"will-change": [
							{ "will-change": ["auto", "scroll", "contents", "transform", T] },
						],
						fill: [{ fill: [e, "none"] }],
						"stroke-w": [{ stroke: [N, S, A] }],
						stroke: [{ stroke: [e, "none"] }],
						sr: ["sr-only", "not-sr-only"],
						"forced-color-adjust": [
							{ "forced-color-adjust": ["auto", "none"] },
						],
					},
					conflictingClassGroups: {
						overflow: ["overflow-x", "overflow-y"],
						overscroll: ["overscroll-x", "overscroll-y"],
						inset: [
							"inset-x",
							"inset-y",
							"start",
							"end",
							"top",
							"right",
							"bottom",
							"left",
						],
						"inset-x": ["right", "left"],
						"inset-y": ["top", "bottom"],
						flex: ["basis", "grow", "shrink"],
						gap: ["gap-x", "gap-y"],
						p: ["px", "py", "ps", "pe", "pt", "pr", "pb", "pl"],
						px: ["pr", "pl"],
						py: ["pt", "pb"],
						m: ["mx", "my", "ms", "me", "mt", "mr", "mb", "ml"],
						mx: ["mr", "ml"],
						my: ["mt", "mb"],
						size: ["w", "h"],
						"font-size": ["leading"],
						"fvn-normal": [
							"fvn-ordinal",
							"fvn-slashed-zero",
							"fvn-figure",
							"fvn-spacing",
							"fvn-fraction",
						],
						"fvn-ordinal": ["fvn-normal"],
						"fvn-slashed-zero": ["fvn-normal"],
						"fvn-figure": ["fvn-normal"],
						"fvn-spacing": ["fvn-normal"],
						"fvn-fraction": ["fvn-normal"],
						"line-clamp": ["display", "overflow"],
						rounded: [
							"rounded-s",
							"rounded-e",
							"rounded-t",
							"rounded-r",
							"rounded-b",
							"rounded-l",
							"rounded-ss",
							"rounded-se",
							"rounded-ee",
							"rounded-es",
							"rounded-tl",
							"rounded-tr",
							"rounded-br",
							"rounded-bl",
						],
						"rounded-s": ["rounded-ss", "rounded-es"],
						"rounded-e": ["rounded-se", "rounded-ee"],
						"rounded-t": ["rounded-tl", "rounded-tr"],
						"rounded-r": ["rounded-tr", "rounded-br"],
						"rounded-b": ["rounded-br", "rounded-bl"],
						"rounded-l": ["rounded-tl", "rounded-bl"],
						"border-spacing": ["border-spacing-x", "border-spacing-y"],
						"border-w": [
							"border-w-s",
							"border-w-e",
							"border-w-t",
							"border-w-r",
							"border-w-b",
							"border-w-l",
						],
						"border-w-x": ["border-w-r", "border-w-l"],
						"border-w-y": ["border-w-t", "border-w-b"],
						"border-color": [
							"border-color-s",
							"border-color-e",
							"border-color-t",
							"border-color-r",
							"border-color-b",
							"border-color-l",
						],
						"border-color-x": ["border-color-r", "border-color-l"],
						"border-color-y": ["border-color-t", "border-color-b"],
						"scroll-m": [
							"scroll-mx",
							"scroll-my",
							"scroll-ms",
							"scroll-me",
							"scroll-mt",
							"scroll-mr",
							"scroll-mb",
							"scroll-ml",
						],
						"scroll-mx": ["scroll-mr", "scroll-ml"],
						"scroll-my": ["scroll-mt", "scroll-mb"],
						"scroll-p": [
							"scroll-px",
							"scroll-py",
							"scroll-ps",
							"scroll-pe",
							"scroll-pt",
							"scroll-pr",
							"scroll-pb",
							"scroll-pl",
						],
						"scroll-px": ["scroll-pr", "scroll-pl"],
						"scroll-py": ["scroll-pt", "scroll-pb"],
						touch: ["touch-x", "touch-y", "touch-pz"],
						"touch-x": ["touch"],
						"touch-y": ["touch"],
						"touch-pz": ["touch"],
					},
					conflictingClassGroupModifiers: { "font-size": ["leading"] },
				};
			});
		},
		4951: (e, r) => {
			function t(e) {
				const r = {};
				for (const [t, o] of e.entries()) {
					const e = r[t];
					void 0 === e
						? (r[t] = o)
						: Array.isArray(e)
							? e.push(o)
							: (r[t] = [e, o]);
				}
				return r;
			}
			function o(e) {
				return "string" == typeof e
					? e
					: ("number" != typeof e || isNaN(e)) && "boolean" != typeof e
						? ""
						: String(e);
			}
			function n(e) {
				const r = new URLSearchParams();
				for (const [t, n] of Object.entries(e))
					if (Array.isArray(n)) for (const e of n) r.append(t, o(e));
					else r.set(t, o(n));
				return r;
			}
			function l(e) {
				for (
					var r = arguments.length, t = Array(r > 1 ? r - 1 : 0), o = 1;
					o < r;
					o++
				)
					t[o - 1] = arguments[o];
				for (const r of t) {
					for (const t of r.keys()) e.delete(t);
					for (const [t, o] of r.entries()) e.append(t, o);
				}
				return e;
			}
			Object.defineProperty(r, "__esModule", { value: !0 }),
				!((e, r) => {
					for (var t in r)
						Object.defineProperty(e, t, { enumerable: !0, get: r[t] });
				})(r, {
					assign: () => l,
					searchParamsToUrlQuery: () => t,
					urlQueryToSearchParams: () => n,
				});
		},
		6522: (e, r, t) => {
			t.d(r, { $: () => o });
			function o() {
				for (var e, r, t = 0, o = "", n = arguments.length; t < n; t++)
					(e = arguments[t]) &&
						(r = (function e(r) {
							var t,
								o,
								n = "";
							if ("string" == typeof r || "number" == typeof r) n += r;
							else if ("object" == typeof r) {
								if (Array.isArray(r)) {
									var l = r.length;
									for (t = 0; t < l; t++)
										r[t] && (o = e(r[t])) && (n && (n += " "), (n += o));
								} else for (o in r) r[o] && (n && (n += " "), (n += o));
							}
							return n;
						})(e)) &&
						(o && (o += " "), (o += r));
				return o;
			}
		},
		7950: (e, r, t) => {
			Object.defineProperty(r, "__esModule", { value: !0 }),
				Object.defineProperty(r, "default", { enumerable: !0, get: () => b });
			const o = t(4879),
				n = t(8081),
				l = o._(t(2149)),
				a = t(473),
				i = t(9615),
				s = t(8902),
				c = t(3778),
				d = t(1747),
				u = t(3117);
			t(850);
			const p = t(8142);
			function f(e) {
				return "string" == typeof e ? e : (0, a.formatUrl)(e);
			}
			const b = l.default.forwardRef((e, r) => {
				let t, o;
				const {
					href: a,
					as: b,
					children: m,
					prefetch: g = null,
					passHref: h,
					replace: y,
					shallow: v,
					scroll: x,
					onClick: w,
					onMouseEnter: k,
					onTouchStart: j,
					legacyBehavior: z = !1,
					...P
				} = e;
				(t = m),
					z &&
						("string" == typeof t || "number" == typeof t) &&
						(t = (0, n.jsx)("a", { children: t }));
				const E = l.default.useContext(i.AppRouterContext),
					C = !1 !== g,
					O = null === g ? s.PrefetchKind.AUTO : s.PrefetchKind.FULL,
					{ href: N, as: S } = l.default.useMemo(() => {
						const e = f(a);
						return { href: e, as: b ? f(b) : e };
					}, [a, b]);
				z && (o = l.default.Children.only(t));
				const _ = z ? o && "object" == typeof o && o.ref : r,
					A = l.default.useCallback(
						(e) => (
							C && null !== E && (0, p.mountLinkInstance)(e, N, E, O),
							() => {
								(0, p.unmountLinkInstance)(e);
							}
						),
						[C, N, E, O],
					),
					R = {
						ref: (0, c.useMergedRef)(A, _),
						onClick(e) {
							z || "function" != typeof w || w(e),
								z &&
									o.props &&
									"function" == typeof o.props.onClick &&
									o.props.onClick(e),
								E &&
									!e.defaultPrevented &&
									!((e, r, t, o, n, a, i) => {
										const { nodeName: s } = e.currentTarget;
										!(
											"A" === s.toUpperCase() &&
											((e) => {
												const r = e.currentTarget.getAttribute("target");
												return (
													(r && "_self" !== r) ||
													e.metaKey ||
													e.ctrlKey ||
													e.shiftKey ||
													e.altKey ||
													(e.nativeEvent && 2 === e.nativeEvent.which)
												);
											})(e)
										) &&
											(e.preventDefault(),
											l.default.startTransition(() => {
												const e = null == i || i;
												"beforePopState" in r
													? r[n ? "replace" : "push"](t, o, {
															shallow: a,
															scroll: e,
														})
													: r[n ? "replace" : "push"](o || t, { scroll: e });
											}));
									})(e, E, N, S, y, v, x);
						},
						onMouseEnter(e) {
							z || "function" != typeof k || k(e),
								z &&
									o.props &&
									"function" == typeof o.props.onMouseEnter &&
									o.props.onMouseEnter(e),
								E && C && (0, p.onNavigationIntent)(e.currentTarget);
						},
						onTouchStart: (e) => {
							z || "function" != typeof j || j(e),
								z &&
									o.props &&
									"function" == typeof o.props.onTouchStart &&
									o.props.onTouchStart(e),
								E && C && (0, p.onNavigationIntent)(e.currentTarget);
						},
					};
				return (
					(0, d.isAbsoluteUrl)(S)
						? (R.href = S)
						: (z && !h && ("a" !== o.type || "href" in o.props)) ||
							(R.href = (0, u.addBasePath)(S)),
					z
						? l.default.cloneElement(o, R)
						: (0, n.jsx)("a", { ...P, ...R, children: t })
				);
			});
			("function" == typeof r.default ||
				("object" == typeof r.default && null !== r.default)) &&
				void 0 === r.default.__esModule &&
				(Object.defineProperty(r.default, "__esModule", { value: !0 }),
				Object.assign(r.default, r),
				(e.exports = r.default));
		},
		8735: (e, r, t) => {
			t.d(r, { s: () => a, t: () => l });
			var o = t(2149);
			function n(e, r) {
				if ("function" == typeof e) return e(r);
				null != e && (e.current = r);
			}
			function l(...e) {
				return (r) => {
					let t = !1,
						o = e.map((e) => {
							const o = n(e, r);
							return t || "function" != typeof o || (t = !0), o;
						});
					if (t)
						return () => {
							for (let r = 0; r < o.length; r++) {
								const t = o[r];
								"function" == typeof t ? t() : n(e[r], null);
							}
						};
				};
			}
			function a(...e) {
				return o.useCallback(l(...e), e);
			}
		},
	},
]);
