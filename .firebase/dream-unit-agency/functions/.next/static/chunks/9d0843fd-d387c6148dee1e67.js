(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[85],
	{
		8912: (e, t, r) => {
			r.d(t, { aa: () => ts, o: () => rI });
			var i = r(8339),
				n = r(854),
				s = r(1176),
				a = r(5334),
				o = r(8101);
			function l() {
				return {
					"dependent-sdk-initialized-before-auth":
						"Another Firebase SDK was initialized and is trying to use Auth before Auth is initialized. Please be sure to call `initializeAuth` or `getAuth` before starting any other Firebase SDK.",
				};
			}
			const c = new n.FA("auth", "Firebase", l()),
				u = new s.Vy("@firebase/auth");
			function h(e, ...t) {
				u.logLevel <= s.$b.ERROR && u.error(`Auth (${i.MF}): ${e}`, ...t);
			}
			function d(e, ...t) {
				throw v(e, ...t);
			}
			function p(e, ...t) {
				return v(e, ...t);
			}
			function f(e, t, r) {
				const i = Object.assign(Object.assign({}, l()), { [t]: r });
				return new n.FA("auth", "Firebase", i).create(t, { appName: e.name });
			}
			function m(e) {
				return f(
					e,
					"operation-not-supported-in-this-environment",
					"Operations that alter the current user are not supported in conjunction with FirebaseServerApp",
				);
			}
			function g(e, t, r) {
				if (!(t instanceof r))
					throw (
						(r.name !== t.constructor.name && d(e, "argument-error"),
						f(
							e,
							"argument-error",
							`Type of ${t.constructor.name} does not match expected instance.Did you pass a reference from a different Auth SDK?`,
						))
					);
			}
			function v(e, ...t) {
				if ("string" != typeof e) {
					const r = t[0],
						i = [...t.slice(1)];
					return (
						i[0] && (i[0].appName = e.name), e._errorFactory.create(r, ...i)
					);
				}
				return c.create(e, ...t);
			}
			function _(e, t, ...r) {
				if (!e) throw v(t, ...r);
			}
			function I(e) {
				const t = "INTERNAL ASSERTION FAILED: " + e;
				throw (h(t), Error(t));
			}
			function w(e, t) {
				e || I(t);
			}
			function y() {
				var e;
				return (
					("undefined" != typeof self &&
						(null === (e = self.location) || void 0 === e ? void 0 : e.href)) ||
					""
				);
			}
			function T() {
				var e;
				return (
					("undefined" != typeof self &&
						(null === (e = self.location) || void 0 === e
							? void 0
							: e.protocol)) ||
					null
				);
			}
			class E {
				constructor(e, t) {
					var r;
					(this.shortDelay = e),
						(this.longDelay = t),
						(r = "Short delay should be less than long delay!"),
						t > e || I(r),
						(this.isMobile = (0, n.jZ)() || (0, n.lV)());
				}
				get() {
					return !(
						"undefined" != typeof navigator &&
						navigator &&
						"onLine" in navigator &&
						"boolean" == typeof navigator.onLine &&
						("http:" === T() ||
							"https:" === T() ||
							(0, n.sr)() ||
							"connection" in navigator)
					) || navigator.onLine
						? this.isMobile
							? this.longDelay
							: this.shortDelay
						: Math.min(5e3, this.shortDelay);
				}
			}
			function k(e, t) {
				var r, i;
				(r = e.emulator), (i = "Emulator should always be set here"), r || I(i);
				const { url: n } = e.emulator;
				return t ? `${n}${t.startsWith("/") ? t.slice(1) : t}` : n;
			}
			class S {
				static initialize(e, t, r) {
					(this.fetchImpl = e),
						t && (this.headersImpl = t),
						r && (this.responseImpl = r);
				}
				static fetch() {
					return this.fetchImpl
						? this.fetchImpl
						: "undefined" != typeof self && "fetch" in self
							? self.fetch
							: "undefined" != typeof globalThis && globalThis.fetch
								? globalThis.fetch
								: "undefined" != typeof fetch
									? fetch
									: void I(
											"Could not find fetch implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill",
										);
				}
				static headers() {
					return this.headersImpl
						? this.headersImpl
						: "undefined" != typeof self && "Headers" in self
							? self.Headers
							: "undefined" != typeof globalThis && globalThis.Headers
								? globalThis.Headers
								: "undefined" != typeof Headers
									? Headers
									: void I(
											"Could not find Headers implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill",
										);
				}
				static response() {
					return this.responseImpl
						? this.responseImpl
						: "undefined" != typeof self && "Response" in self
							? self.Response
							: "undefined" != typeof globalThis && globalThis.Response
								? globalThis.Response
								: "undefined" != typeof Response
									? Response
									: void I(
											"Could not find Response implementation, make sure you call FetchProvider.initialize() with an appropriate polyfill",
										);
				}
			}
			const R = {
					CREDENTIAL_MISMATCH: "custom-token-mismatch",
					MISSING_CUSTOM_TOKEN: "internal-error",
					INVALID_IDENTIFIER: "invalid-email",
					MISSING_CONTINUE_URI: "internal-error",
					INVALID_PASSWORD: "wrong-password",
					MISSING_PASSWORD: "missing-password",
					INVALID_LOGIN_CREDENTIALS: "invalid-credential",
					EMAIL_EXISTS: "email-already-in-use",
					PASSWORD_LOGIN_DISABLED: "operation-not-allowed",
					INVALID_IDP_RESPONSE: "invalid-credential",
					INVALID_PENDING_TOKEN: "invalid-credential",
					FEDERATED_USER_ID_ALREADY_LINKED: "credential-already-in-use",
					MISSING_REQ_TYPE: "internal-error",
					EMAIL_NOT_FOUND: "user-not-found",
					RESET_PASSWORD_EXCEED_LIMIT: "too-many-requests",
					EXPIRED_OOB_CODE: "expired-action-code",
					INVALID_OOB_CODE: "invalid-action-code",
					MISSING_OOB_CODE: "internal-error",
					CREDENTIAL_TOO_OLD_LOGIN_AGAIN: "requires-recent-login",
					INVALID_ID_TOKEN: "invalid-user-token",
					TOKEN_EXPIRED: "user-token-expired",
					USER_NOT_FOUND: "user-token-expired",
					TOO_MANY_ATTEMPTS_TRY_LATER: "too-many-requests",
					PASSWORD_DOES_NOT_MEET_REQUIREMENTS:
						"password-does-not-meet-requirements",
					INVALID_CODE: "invalid-verification-code",
					INVALID_SESSION_INFO: "invalid-verification-id",
					INVALID_TEMPORARY_PROOF: "invalid-credential",
					MISSING_SESSION_INFO: "missing-verification-id",
					SESSION_EXPIRED: "code-expired",
					MISSING_ANDROID_PACKAGE_NAME: "missing-android-pkg-name",
					UNAUTHORIZED_DOMAIN: "unauthorized-continue-uri",
					INVALID_OAUTH_CLIENT_ID: "invalid-oauth-client-id",
					ADMIN_ONLY_OPERATION: "admin-restricted-operation",
					INVALID_MFA_PENDING_CREDENTIAL: "invalid-multi-factor-session",
					MFA_ENROLLMENT_NOT_FOUND: "multi-factor-info-not-found",
					MISSING_MFA_ENROLLMENT_ID: "missing-multi-factor-info",
					MISSING_MFA_PENDING_CREDENTIAL: "missing-multi-factor-session",
					SECOND_FACTOR_EXISTS: "second-factor-already-in-use",
					SECOND_FACTOR_LIMIT_EXCEEDED: "maximum-second-factor-count-exceeded",
					BLOCKING_FUNCTION_ERROR_RESPONSE: "internal-error",
					RECAPTCHA_NOT_ENABLED: "recaptcha-not-enabled",
					MISSING_RECAPTCHA_TOKEN: "missing-recaptcha-token",
					INVALID_RECAPTCHA_TOKEN: "invalid-recaptcha-token",
					INVALID_RECAPTCHA_ACTION: "invalid-recaptcha-action",
					MISSING_CLIENT_TYPE: "missing-client-type",
					MISSING_RECAPTCHA_VERSION: "missing-recaptcha-version",
					INVALID_RECAPTCHA_VERSION: "invalid-recaptcha-version",
					INVALID_REQ_TYPE: "invalid-req-type",
				},
				P = new E(3e4, 6e4);
			function C(e, t) {
				return e.tenantId && !t.tenantId
					? Object.assign(Object.assign({}, t), { tenantId: e.tenantId })
					: t;
			}
			async function O(e, t, r, i, s = {}) {
				return b(e, s, async () => {
					let s = {},
						a = {};
					i && ("GET" === t ? (a = i) : (s = { body: JSON.stringify(i) }));
					const o = (0, n.Am)(Object.assign({ key: e.config.apiKey }, a)).slice(
							1,
						),
						l = await e._getAdditionalHeaders();
					(l["Content-Type"] = "application/json"),
						e.languageCode && (l["X-Firebase-Locale"] = e.languageCode);
					const c = Object.assign({ method: t, headers: l }, s);
					return (
						(0, n.c1)() || (c.referrerPolicy = "no-referrer"),
						S.fetch()(N(e, e.config.apiHost, r, o), c)
					);
				});
			}
			async function b(e, t, r) {
				e._canInitEmulator = !1;
				const i = Object.assign(Object.assign({}, R), t);
				try {
					const t = new L(e),
						n = await Promise.race([r(), t.promise]);
					t.clearNetworkTimeout();
					const s = await n.json();
					if ("needConfirmation" in s)
						throw U(e, "account-exists-with-different-credential", s);
					if (n.ok && !("errorMessage" in s)) return s;
					{
						const [t, r] = (n.ok ? s.errorMessage : s.error.message).split(
							" : ",
						);
						if ("FEDERATED_USER_ID_ALREADY_LINKED" === t)
							throw U(e, "credential-already-in-use", s);
						if ("EMAIL_EXISTS" === t) throw U(e, "email-already-in-use", s);
						if ("USER_DISABLED" === t) throw U(e, "user-disabled", s);
						const a = i[t] || t.toLowerCase().replace(/[_\s]+/g, "-");
						if (r) throw f(e, a, r);
						d(e, a);
					}
				} catch (t) {
					if (t instanceof n.g) throw t;
					d(e, "network-request-failed", { message: String(t) });
				}
			}
			async function A(e, t, r, i, n = {}) {
				const s = await O(e, t, r, i, n);
				return (
					"mfaPendingCredential" in s &&
						d(e, "multi-factor-auth-required", { _serverResponse: s }),
					s
				);
			}
			function N(e, t, r, i) {
				const n = `${t}${r}?${i}`;
				return e.config.emulator
					? k(e.config, n)
					: `${e.config.apiScheme}://${n}`;
			}
			class L {
				constructor(e) {
					(this.auth = e),
						(this.timer = null),
						(this.promise = new Promise((e, t) => {
							this.timer = setTimeout(
								() => t(p(this.auth, "network-request-failed")),
								P.get(),
							);
						}));
				}
				clearNetworkTimeout() {
					clearTimeout(this.timer);
				}
			}
			function U(e, t, r) {
				const i = { appName: e.name };
				r.email && (i.email = r.email),
					r.phoneNumber && (i.phoneNumber = r.phoneNumber);
				const n = p(e, t, i);
				return (n.customData._tokenResponse = r), n;
			}
			function D(e) {
				return void 0 !== e && void 0 !== e.enterprise;
			}
			class M {
				constructor(e) {
					if (
						((this.siteKey = ""),
						(this.recaptchaEnforcementState = []),
						void 0 === e.recaptchaKey)
					)
						throw Error("recaptchaKey undefined");
					(this.siteKey = e.recaptchaKey.split("/")[3]),
						(this.recaptchaEnforcementState = e.recaptchaEnforcementState);
				}
				getProviderEnforcementState(e) {
					if (
						!this.recaptchaEnforcementState ||
						0 === this.recaptchaEnforcementState.length
					)
						return null;
					for (const t of this.recaptchaEnforcementState)
						if (t.provider && t.provider === e)
							return ((e) => {
								switch (e) {
									case "ENFORCE":
										return "ENFORCE";
									case "AUDIT":
										return "AUDIT";
									case "OFF":
										return "OFF";
									default:
										return "ENFORCEMENT_STATE_UNSPECIFIED";
								}
							})(t.enforcementState);
					return null;
				}
				isProviderEnabled(e) {
					return (
						"ENFORCE" === this.getProviderEnforcementState(e) ||
						"AUDIT" === this.getProviderEnforcementState(e)
					);
				}
			}
			async function F(e, t) {
				return O(e, "GET", "/v2/recaptchaConfig", C(e, t));
			}
			async function x(e, t) {
				return O(e, "POST", "/v1/accounts:delete", t);
			}
			async function V(e, t) {
				return O(e, "POST", "/v1/accounts:lookup", t);
			}
			function j(e) {
				if (e)
					try {
						const t = new Date(Number(e));
						if (!isNaN(t.getTime())) return t.toUTCString();
					} catch (e) {}
			}
			async function H(e, t = !1) {
				const r = (0, n.Ku)(e),
					i = await r.getIdToken(t),
					s = z(i);
				_(s && s.exp && s.auth_time && s.iat, r.auth, "internal-error");
				const a = "object" == typeof s.firebase ? s.firebase : void 0,
					o = null == a ? void 0 : a.sign_in_provider;
				return {
					claims: s,
					token: i,
					authTime: j(W(s.auth_time)),
					issuedAtTime: j(W(s.iat)),
					expirationTime: j(W(s.exp)),
					signInProvider: o || null,
					signInSecondFactor:
						(null == a ? void 0 : a.sign_in_second_factor) || null,
				};
			}
			function W(e) {
				return 1e3 * Number(e);
			}
			function z(e) {
				const [t, r, i] = e.split(".");
				if (void 0 === t || void 0 === r || void 0 === i)
					return h("JWT malformed, contained fewer than 3 sections"), null;
				try {
					const e = (0, n.u)(r);
					if (!e) return h("Failed to decode base64 JWT payload"), null;
					return JSON.parse(e);
				} catch (e) {
					return (
						h(
							"Caught error parsing JWT payload as JSON",
							null == e ? void 0 : e.toString(),
						),
						null
					);
				}
			}
			function K(e) {
				const t = z(e);
				return (
					_(t, "internal-error"),
					_(void 0 !== t.exp, "internal-error"),
					_(void 0 !== t.iat, "internal-error"),
					Number(t.exp) - Number(t.iat)
				);
			}
			async function $(e, t, r = !1) {
				if (r) return t;
				try {
					return await t;
				} catch (t) {
					throw (
						(t instanceof n.g &&
							(({ code: e }) =>
								"auth/user-disabled" === e || "auth/user-token-expired" === e)(
								t,
							) &&
							e.auth.currentUser === e &&
							(await e.auth.signOut()),
						t)
					);
				}
			}
			class q {
				constructor(e) {
					(this.user = e),
						(this.isRunning = !1),
						(this.timerId = null),
						(this.errorBackoff = 3e4);
				}
				_start() {
					!this.isRunning && ((this.isRunning = !0), this.schedule());
				}
				_stop() {
					this.isRunning &&
						((this.isRunning = !1),
						null !== this.timerId && clearTimeout(this.timerId));
				}
				getInterval(e) {
					var t;
					if (!e)
						return (
							(this.errorBackoff = 3e4),
							Math.max(
								0,
								(null !== (t = this.user.stsTokenManager.expirationTime) &&
								void 0 !== t
									? t
									: 0) -
									Date.now() -
									3e5,
							)
						);
					{
						const e = this.errorBackoff;
						return (
							(this.errorBackoff = Math.min(2 * this.errorBackoff, 96e4)), e
						);
					}
				}
				schedule(e = !1) {
					if (!this.isRunning) return;
					const t = this.getInterval(e);
					this.timerId = setTimeout(async () => {
						await this.iteration();
					}, t);
				}
				async iteration() {
					try {
						await this.user.getIdToken(!0);
					} catch (e) {
						(null == e ? void 0 : e.code) === "auth/network-request-failed" &&
							this.schedule(!0);
						return;
					}
					this.schedule();
				}
			}
			class G {
				constructor(e, t) {
					(this.createdAt = e), (this.lastLoginAt = t), this._initializeTime();
				}
				_initializeTime() {
					(this.lastSignInTime = j(this.lastLoginAt)),
						(this.creationTime = j(this.createdAt));
				}
				_copy(e) {
					(this.createdAt = e.createdAt),
						(this.lastLoginAt = e.lastLoginAt),
						this._initializeTime();
				}
				toJSON() {
					return { createdAt: this.createdAt, lastLoginAt: this.lastLoginAt };
				}
			}
			async function J(e) {
				var t, r, i;
				const n = e.auth,
					s = await e.getIdToken(),
					a = await $(e, V(n, { idToken: s }));
				_(null == a ? void 0 : a.users.length, n, "internal-error");
				const o = a.users[0];
				e._notifyReloadListener(o);
				const l = (
						null === (t = o.providerUserInfo) || void 0 === t
							? void 0
							: t.length
					)
						? Z(o.providerUserInfo)
						: [],
					c =
						((r = e.providerData),
						(i = l),
						[
							...r.filter((e) => !i.some((t) => t.providerId === e.providerId)),
							...i,
						]),
					u = e.isAnonymous,
					h = !(e.email && o.passwordHash) && !(null == c ? void 0 : c.length);
				Object.assign(e, {
					uid: o.localId,
					displayName: o.displayName || null,
					photoURL: o.photoUrl || null,
					email: o.email || null,
					emailVerified: o.emailVerified || !1,
					phoneNumber: o.phoneNumber || null,
					tenantId: o.tenantId || null,
					providerData: c,
					metadata: new G(o.createdAt, o.lastLoginAt),
					isAnonymous: !!u && h,
				});
			}
			async function B(e) {
				const t = (0, n.Ku)(e);
				await J(t),
					await t.auth._persistUserIfCurrent(t),
					t.auth._notifyListenersIfCurrent(t);
			}
			function Z(e) {
				return e.map((e) => {
					var { providerId: t } = e,
						r = (0, a.Tt)(e, ["providerId"]);
					return {
						providerId: t,
						uid: r.rawId || "",
						displayName: r.displayName || null,
						email: r.email || null,
						phoneNumber: r.phoneNumber || null,
						photoURL: r.photoUrl || null,
					};
				});
			}
			async function Q(e, t) {
				const r = await b(e, {}, async () => {
					const r = (0, n.Am)({
							grant_type: "refresh_token",
							refresh_token: t,
						}).slice(1),
						{ tokenApiHost: i, apiKey: s } = e.config,
						a = N(e, i, "/v1/token", `key=${s}`),
						o = await e._getAdditionalHeaders();
					return (
						(o["Content-Type"] = "application/x-www-form-urlencoded"),
						S.fetch()(a, { method: "POST", headers: o, body: r })
					);
				});
				return {
					accessToken: r.access_token,
					expiresIn: r.expires_in,
					refreshToken: r.refresh_token,
				};
			}
			async function X(e, t) {
				return O(e, "POST", "/v2/accounts:revokeToken", C(e, t));
			}
			class Y {
				constructor() {
					(this.refreshToken = null),
						(this.accessToken = null),
						(this.expirationTime = null);
				}
				get isExpired() {
					return !this.expirationTime || Date.now() > this.expirationTime - 3e4;
				}
				updateFromServerResponse(e) {
					_(e.idToken, "internal-error"),
						_(void 0 !== e.idToken, "internal-error"),
						_(void 0 !== e.refreshToken, "internal-error");
					const t =
						"expiresIn" in e && void 0 !== e.expiresIn
							? Number(e.expiresIn)
							: K(e.idToken);
					this.updateTokensAndExpiration(e.idToken, e.refreshToken, t);
				}
				updateFromIdToken(e) {
					_(0 !== e.length, "internal-error");
					const t = K(e);
					this.updateTokensAndExpiration(e, null, t);
				}
				async getToken(e, t = !1) {
					return t || !this.accessToken || this.isExpired
						? (_(this.refreshToken, e, "user-token-expired"), this.refreshToken)
							? (await this.refresh(e, this.refreshToken), this.accessToken)
							: null
						: this.accessToken;
				}
				clearRefreshToken() {
					this.refreshToken = null;
				}
				async refresh(e, t) {
					const {
						accessToken: r,
						refreshToken: i,
						expiresIn: n,
					} = await Q(e, t);
					this.updateTokensAndExpiration(r, i, Number(n));
				}
				updateTokensAndExpiration(e, t, r) {
					(this.refreshToken = t || null),
						(this.accessToken = e || null),
						(this.expirationTime = Date.now() + 1e3 * r);
				}
				static fromJSON(e, t) {
					const { refreshToken: r, accessToken: i, expirationTime: n } = t,
						s = new Y();
					return (
						r &&
							(_("string" == typeof r, "internal-error", { appName: e }),
							(s.refreshToken = r)),
						i &&
							(_("string" == typeof i, "internal-error", { appName: e }),
							(s.accessToken = i)),
						n &&
							(_("number" == typeof n, "internal-error", { appName: e }),
							(s.expirationTime = n)),
						s
					);
				}
				toJSON() {
					return {
						refreshToken: this.refreshToken,
						accessToken: this.accessToken,
						expirationTime: this.expirationTime,
					};
				}
				_assign(e) {
					(this.accessToken = e.accessToken),
						(this.refreshToken = e.refreshToken),
						(this.expirationTime = e.expirationTime);
				}
				_clone() {
					return Object.assign(new Y(), this.toJSON());
				}
				_performRefresh() {
					return I("not implemented");
				}
			}
			function ee(e, t) {
				_("string" == typeof e || void 0 === e, "internal-error", {
					appName: t,
				});
			}
			class et {
				constructor(e) {
					var { uid: t, auth: r, stsTokenManager: i } = e,
						n = (0, a.Tt)(e, ["uid", "auth", "stsTokenManager"]);
					(this.providerId = "firebase"),
						(this.proactiveRefresh = new q(this)),
						(this.reloadUserInfo = null),
						(this.reloadListener = null),
						(this.uid = t),
						(this.auth = r),
						(this.stsTokenManager = i),
						(this.accessToken = i.accessToken),
						(this.displayName = n.displayName || null),
						(this.email = n.email || null),
						(this.emailVerified = n.emailVerified || !1),
						(this.phoneNumber = n.phoneNumber || null),
						(this.photoURL = n.photoURL || null),
						(this.isAnonymous = n.isAnonymous || !1),
						(this.tenantId = n.tenantId || null),
						(this.providerData = n.providerData ? [...n.providerData] : []),
						(this.metadata = new G(
							n.createdAt || void 0,
							n.lastLoginAt || void 0,
						));
				}
				async getIdToken(e) {
					const t = await $(this, this.stsTokenManager.getToken(this.auth, e));
					return (
						_(t, this.auth, "internal-error"),
						this.accessToken !== t &&
							((this.accessToken = t),
							await this.auth._persistUserIfCurrent(this),
							this.auth._notifyListenersIfCurrent(this)),
						t
					);
				}
				getIdTokenResult(e) {
					return H(this, e);
				}
				reload() {
					return B(this);
				}
				_assign(e) {
					this !== e &&
						(_(this.uid === e.uid, this.auth, "internal-error"),
						(this.displayName = e.displayName),
						(this.photoURL = e.photoURL),
						(this.email = e.email),
						(this.emailVerified = e.emailVerified),
						(this.phoneNumber = e.phoneNumber),
						(this.isAnonymous = e.isAnonymous),
						(this.tenantId = e.tenantId),
						(this.providerData = e.providerData.map((e) =>
							Object.assign({}, e),
						)),
						this.metadata._copy(e.metadata),
						this.stsTokenManager._assign(e.stsTokenManager));
				}
				_clone(e) {
					const t = new et(
						Object.assign(Object.assign({}, this), {
							auth: e,
							stsTokenManager: this.stsTokenManager._clone(),
						}),
					);
					return t.metadata._copy(this.metadata), t;
				}
				_onReload(e) {
					_(!this.reloadListener, this.auth, "internal-error"),
						(this.reloadListener = e),
						this.reloadUserInfo &&
							(this._notifyReloadListener(this.reloadUserInfo),
							(this.reloadUserInfo = null));
				}
				_notifyReloadListener(e) {
					this.reloadListener
						? this.reloadListener(e)
						: (this.reloadUserInfo = e);
				}
				_startProactiveRefresh() {
					this.proactiveRefresh._start();
				}
				_stopProactiveRefresh() {
					this.proactiveRefresh._stop();
				}
				async _updateTokensIfNecessary(e, t = !1) {
					let r = !1;
					e.idToken &&
						e.idToken !== this.stsTokenManager.accessToken &&
						(this.stsTokenManager.updateFromServerResponse(e), (r = !0)),
						t && (await J(this)),
						await this.auth._persistUserIfCurrent(this),
						r && this.auth._notifyListenersIfCurrent(this);
				}
				async delete() {
					if ((0, i.xZ)(this.auth.app)) return Promise.reject(m(this.auth));
					const e = await this.getIdToken();
					return (
						await $(this, x(this.auth, { idToken: e })),
						this.stsTokenManager.clearRefreshToken(),
						this.auth.signOut()
					);
				}
				toJSON() {
					return Object.assign(
						Object.assign(
							{
								uid: this.uid,
								email: this.email || void 0,
								emailVerified: this.emailVerified,
								displayName: this.displayName || void 0,
								isAnonymous: this.isAnonymous,
								photoURL: this.photoURL || void 0,
								phoneNumber: this.phoneNumber || void 0,
								tenantId: this.tenantId || void 0,
								providerData: this.providerData.map((e) =>
									Object.assign({}, e),
								),
								stsTokenManager: this.stsTokenManager.toJSON(),
								_redirectEventId: this._redirectEventId,
							},
							this.metadata.toJSON(),
						),
						{ apiKey: this.auth.config.apiKey, appName: this.auth.name },
					);
				}
				get refreshToken() {
					return this.stsTokenManager.refreshToken || "";
				}
				static _fromJSON(e, t) {
					var r, i, n, s, a, o, l, c;
					const u = null !== (r = t.displayName) && void 0 !== r ? r : void 0,
						h = null !== (i = t.email) && void 0 !== i ? i : void 0,
						d = null !== (n = t.phoneNumber) && void 0 !== n ? n : void 0,
						p = null !== (s = t.photoURL) && void 0 !== s ? s : void 0,
						f = null !== (a = t.tenantId) && void 0 !== a ? a : void 0,
						m = null !== (o = t._redirectEventId) && void 0 !== o ? o : void 0,
						g = null !== (l = t.createdAt) && void 0 !== l ? l : void 0,
						v = null !== (c = t.lastLoginAt) && void 0 !== c ? c : void 0,
						{
							uid: I,
							emailVerified: w,
							isAnonymous: y,
							providerData: T,
							stsTokenManager: E,
						} = t;
					_(I && E, e, "internal-error");
					const k = Y.fromJSON(this.name, E);
					_("string" == typeof I, e, "internal-error"),
						ee(u, e.name),
						ee(h, e.name),
						_("boolean" == typeof w, e, "internal-error"),
						_("boolean" == typeof y, e, "internal-error"),
						ee(d, e.name),
						ee(p, e.name),
						ee(f, e.name),
						ee(m, e.name),
						ee(g, e.name),
						ee(v, e.name);
					const S = new et({
						uid: I,
						auth: e,
						email: h,
						emailVerified: w,
						displayName: u,
						isAnonymous: y,
						photoURL: p,
						phoneNumber: d,
						tenantId: f,
						stsTokenManager: k,
						createdAt: g,
						lastLoginAt: v,
					});
					return (
						T &&
							Array.isArray(T) &&
							(S.providerData = T.map((e) => Object.assign({}, e))),
						m && (S._redirectEventId = m),
						S
					);
				}
				static async _fromIdTokenResponse(e, t, r = !1) {
					const i = new Y();
					i.updateFromServerResponse(t);
					const n = new et({
						uid: t.localId,
						auth: e,
						stsTokenManager: i,
						isAnonymous: r,
					});
					return await J(n), n;
				}
				static async _fromGetAccountInfoResponse(e, t, r) {
					const i = t.users[0];
					_(void 0 !== i.localId, "internal-error");
					const n = void 0 !== i.providerUserInfo ? Z(i.providerUserInfo) : [],
						s =
							!(i.email && i.passwordHash) && !(null == n ? void 0 : n.length),
						a = new Y();
					a.updateFromIdToken(r);
					const o = new et({
						uid: i.localId,
						auth: e,
						stsTokenManager: a,
						isAnonymous: s,
					});
					return (
						Object.assign(o, {
							uid: i.localId,
							displayName: i.displayName || null,
							photoURL: i.photoUrl || null,
							email: i.email || null,
							emailVerified: i.emailVerified || !1,
							phoneNumber: i.phoneNumber || null,
							tenantId: i.tenantId || null,
							providerData: n,
							metadata: new G(i.createdAt, i.lastLoginAt),
							isAnonymous:
								!(i.email && i.passwordHash) &&
								!(null == n ? void 0 : n.length),
						}),
						o
					);
				}
			}
			const er = new Map();
			function ei(e) {
				var t, r;
				(t = "Expected a class definition"), e instanceof Function || I(t);
				let i = er.get(e);
				return (
					i
						? ((r = "Instance stored in cache mismatched with class"),
							i instanceof e || I(r))
						: ((i = new e()), er.set(e, i)),
					i
				);
			}
			class en {
				constructor() {
					(this.type = "NONE"), (this.storage = {});
				}
				async _isAvailable() {
					return !0;
				}
				async _set(e, t) {
					this.storage[e] = t;
				}
				async _get(e) {
					const t = this.storage[e];
					return void 0 === t ? null : t;
				}
				async _remove(e) {
					delete this.storage[e];
				}
				_addListener(e, t) {}
				_removeListener(e, t) {}
			}
			function es(e, t, r) {
				return `firebase:${e}:${t}:${r}`;
			}
			en.type = "NONE";
			class ea {
				constructor(e, t, r) {
					(this.persistence = e), (this.auth = t), (this.userKey = r);
					const { config: i, name: n } = this.auth;
					(this.fullUserKey = es(this.userKey, i.apiKey, n)),
						(this.fullPersistenceKey = es("persistence", i.apiKey, n)),
						(this.boundEventHandler = t._onStorageEvent.bind(t)),
						this.persistence._addListener(
							this.fullUserKey,
							this.boundEventHandler,
						);
				}
				setCurrentUser(e) {
					return this.persistence._set(this.fullUserKey, e.toJSON());
				}
				async getCurrentUser() {
					const e = await this.persistence._get(this.fullUserKey);
					return e ? et._fromJSON(this.auth, e) : null;
				}
				removeCurrentUser() {
					return this.persistence._remove(this.fullUserKey);
				}
				savePersistenceForRedirect() {
					return this.persistence._set(
						this.fullPersistenceKey,
						this.persistence.type,
					);
				}
				async setPersistence(e) {
					if (this.persistence === e) return;
					const t = await this.getCurrentUser();
					if ((await this.removeCurrentUser(), (this.persistence = e), t))
						return this.setCurrentUser(t);
				}
				delete() {
					this.persistence._removeListener(
						this.fullUserKey,
						this.boundEventHandler,
					);
				}
				static async create(e, t, r = "authUser") {
					if (!t.length) return new ea(ei(en), e, r);
					let i = (
							await Promise.all(
								t.map(async (e) => {
									if (await e._isAvailable()) return e;
								}),
							)
						).filter((e) => e),
						n = i[0] || ei(en),
						s = es(r, e.config.apiKey, e.name),
						a = null;
					for (const r of t)
						try {
							const t = await r._get(s);
							if (t) {
								const i = et._fromJSON(e, t);
								r !== n && (a = i), (n = r);
								break;
							}
						} catch (e) {}
					const o = i.filter((e) => e._shouldAllowMigration);
					return (
						n._shouldAllowMigration &&
							o.length &&
							((n = o[0]),
							a && (await n._set(s, a.toJSON())),
							await Promise.all(
								t.map(async (e) => {
									if (e !== n)
										try {
											await e._remove(s);
										} catch (e) {}
								}),
							)),
						new ea(n, e, r)
					);
				}
			}
			function eo(e) {
				const t = e.toLowerCase();
				if (t.includes("opera/") || t.includes("opr/") || t.includes("opios/"))
					return "Opera";
				if (eh(t)) return "IEMobile";
				if (t.includes("msie") || t.includes("trident/")) return "IE";
				{
					if (t.includes("edge/")) return "Edge";
					if (el(t)) return "Firefox";
					if (t.includes("silk/")) return "Silk";
					if (ep(t)) return "Blackberry";
					if (ef(t)) return "Webos";
					if (ec(t)) return "Safari";
					if ((t.includes("chrome/") || eu(t)) && !t.includes("edge/"))
						return "Chrome";
					if (ed(t)) return "Android";
					const r = e.match(/([a-zA-Z\d\.]+)\/[a-zA-Z\d\.]*$/);
					if ((null == r ? void 0 : r.length) === 2) return r[1];
				}
				return "Other";
			}
			function el(e = (0, n.ZQ)()) {
				return /firefox\//i.test(e);
			}
			function ec(e = (0, n.ZQ)()) {
				const t = e.toLowerCase();
				return (
					t.includes("safari/") &&
					!t.includes("chrome/") &&
					!t.includes("crios/") &&
					!t.includes("android")
				);
			}
			function eu(e = (0, n.ZQ)()) {
				return /crios\//i.test(e);
			}
			function eh(e = (0, n.ZQ)()) {
				return /iemobile/i.test(e);
			}
			function ed(e = (0, n.ZQ)()) {
				return /android/i.test(e);
			}
			function ep(e = (0, n.ZQ)()) {
				return /blackberry/i.test(e);
			}
			function ef(e = (0, n.ZQ)()) {
				return /webos/i.test(e);
			}
			function em(e = (0, n.ZQ)()) {
				return (
					/iphone|ipad|ipod/i.test(e) ||
					(/macintosh/i.test(e) && /mobile/i.test(e))
				);
			}
			function eg(e = (0, n.ZQ)()) {
				return (
					em(e) || ed(e) || ef(e) || ep(e) || /windows phone/i.test(e) || eh(e)
				);
			}
			function ev(e, t = []) {
				let r;
				switch (e) {
					case "Browser":
						r = eo((0, n.ZQ)());
						break;
					case "Worker":
						r = `${eo((0, n.ZQ)())}-${e}`;
						break;
					default:
						r = e;
				}
				const s = t.length ? t.join(",") : "FirebaseCore-web";
				return `${r}/JsCore/${i.MF}/${s}`;
			}
			class e_ {
				constructor(e) {
					(this.auth = e), (this.queue = []);
				}
				pushCallback(e, t) {
					const r = (t) =>
						new Promise((r, i) => {
							try {
								const i = e(t);
								r(i);
							} catch (e) {
								i(e);
							}
						});
					(r.onAbort = t), this.queue.push(r);
					const i = this.queue.length - 1;
					return () => {
						this.queue[i] = () => Promise.resolve();
					};
				}
				async runMiddleware(e) {
					if (this.auth.currentUser === e) return;
					const t = [];
					try {
						for (const r of this.queue)
							await r(e), r.onAbort && t.push(r.onAbort);
					} catch (e) {
						for (const e of (t.reverse(), t))
							try {
								e();
							} catch (e) {}
						throw this.auth._errorFactory.create("login-blocked", {
							originalMessage: null == e ? void 0 : e.message,
						});
					}
				}
			}
			async function eI(e, t = {}) {
				return O(e, "GET", "/v2/passwordPolicy", C(e, t));
			}
			class ew {
				constructor(e) {
					var t, r, i, n;
					const s = e.customStrengthOptions;
					(this.customStrengthOptions = {}),
						(this.customStrengthOptions.minPasswordLength =
							null !== (t = s.minPasswordLength) && void 0 !== t ? t : 6),
						s.maxPasswordLength &&
							(this.customStrengthOptions.maxPasswordLength =
								s.maxPasswordLength),
						void 0 !== s.containsLowercaseCharacter &&
							(this.customStrengthOptions.containsLowercaseLetter =
								s.containsLowercaseCharacter),
						void 0 !== s.containsUppercaseCharacter &&
							(this.customStrengthOptions.containsUppercaseLetter =
								s.containsUppercaseCharacter),
						void 0 !== s.containsNumericCharacter &&
							(this.customStrengthOptions.containsNumericCharacter =
								s.containsNumericCharacter),
						void 0 !== s.containsNonAlphanumericCharacter &&
							(this.customStrengthOptions.containsNonAlphanumericCharacter =
								s.containsNonAlphanumericCharacter),
						(this.enforcementState = e.enforcementState),
						"ENFORCEMENT_STATE_UNSPECIFIED" === this.enforcementState &&
							(this.enforcementState = "OFF"),
						(this.allowedNonAlphanumericCharacters =
							null !==
								(i =
									null === (r = e.allowedNonAlphanumericCharacters) ||
									void 0 === r
										? void 0
										: r.join("")) && void 0 !== i
								? i
								: ""),
						(this.forceUpgradeOnSignin =
							null !== (n = e.forceUpgradeOnSignin) && void 0 !== n && n),
						(this.schemaVersion = e.schemaVersion);
				}
				validatePassword(e) {
					var t, r, i, n, s, a;
					const o = { isValid: !0, passwordPolicy: this };
					return (
						this.validatePasswordLengthOptions(e, o),
						this.validatePasswordCharacterOptions(e, o),
						o.isValid &&
							(o.isValid =
								null === (t = o.meetsMinPasswordLength) || void 0 === t || t),
						o.isValid &&
							(o.isValid =
								null === (r = o.meetsMaxPasswordLength) || void 0 === r || r),
						o.isValid &&
							(o.isValid =
								null === (i = o.containsLowercaseLetter) || void 0 === i || i),
						o.isValid &&
							(o.isValid =
								null === (n = o.containsUppercaseLetter) || void 0 === n || n),
						o.isValid &&
							(o.isValid =
								null === (s = o.containsNumericCharacter) || void 0 === s || s),
						o.isValid &&
							(o.isValid =
								null === (a = o.containsNonAlphanumericCharacter) ||
								void 0 === a ||
								a),
						o
					);
				}
				validatePasswordLengthOptions(e, t) {
					const r = this.customStrengthOptions.minPasswordLength,
						i = this.customStrengthOptions.maxPasswordLength;
					r && (t.meetsMinPasswordLength = e.length >= r),
						i && (t.meetsMaxPasswordLength = e.length <= i);
				}
				validatePasswordCharacterOptions(e, t) {
					let r;
					this.updatePasswordCharacterOptionsStatuses(t, !1, !1, !1, !1);
					for (let i = 0; i < e.length; i++)
						(r = e.charAt(i)),
							this.updatePasswordCharacterOptionsStatuses(
								t,
								r >= "a" && r <= "z",
								r >= "A" && r <= "Z",
								r >= "0" && r <= "9",
								this.allowedNonAlphanumericCharacters.includes(r),
							);
				}
				updatePasswordCharacterOptionsStatuses(e, t, r, i, n) {
					this.customStrengthOptions.containsLowercaseLetter &&
						(e.containsLowercaseLetter || (e.containsLowercaseLetter = t)),
						this.customStrengthOptions.containsUppercaseLetter &&
							(e.containsUppercaseLetter || (e.containsUppercaseLetter = r)),
						this.customStrengthOptions.containsNumericCharacter &&
							(e.containsNumericCharacter || (e.containsNumericCharacter = i)),
						this.customStrengthOptions.containsNonAlphanumericCharacter &&
							(e.containsNonAlphanumericCharacter ||
								(e.containsNonAlphanumericCharacter = n));
				}
			}
			class ey {
				constructor(e, t, r, i) {
					(this.app = e),
						(this.heartbeatServiceProvider = t),
						(this.appCheckServiceProvider = r),
						(this.config = i),
						(this.currentUser = null),
						(this.emulatorConfig = null),
						(this.operations = Promise.resolve()),
						(this.authStateSubscription = new eE(this)),
						(this.idTokenSubscription = new eE(this)),
						(this.beforeStateQueue = new e_(this)),
						(this.redirectUser = null),
						(this.isProactiveRefreshEnabled = !1),
						(this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION = 1),
						(this._canInitEmulator = !0),
						(this._isInitialized = !1),
						(this._deleted = !1),
						(this._initializationPromise = null),
						(this._popupRedirectResolver = null),
						(this._errorFactory = c),
						(this._agentRecaptchaConfig = null),
						(this._tenantRecaptchaConfigs = {}),
						(this._projectPasswordPolicy = null),
						(this._tenantPasswordPolicies = {}),
						(this.lastNotifiedUid = void 0),
						(this.languageCode = null),
						(this.tenantId = null),
						(this.settings = { appVerificationDisabledForTesting: !1 }),
						(this.frameworks = []),
						(this.name = e.name),
						(this.clientVersion = i.sdkClientVersion);
				}
				_initializeWithPersistence(e, t) {
					return (
						t && (this._popupRedirectResolver = ei(t)),
						(this._initializationPromise = this.queue(async () => {
							var r, i;
							if (
								!this._deleted &&
								((this.persistenceManager = await ea.create(this, e)),
								!this._deleted)
							) {
								if (
									null === (r = this._popupRedirectResolver) || void 0 === r
										? void 0
										: r._shouldInitProactively
								)
									try {
										await this._popupRedirectResolver._initialize(this);
									} catch (e) {}
								await this.initializeCurrentUser(t),
									(this.lastNotifiedUid =
										(null === (i = this.currentUser) || void 0 === i
											? void 0
											: i.uid) || null),
									!this._deleted && (this._isInitialized = !0);
							}
						})),
						this._initializationPromise
					);
				}
				async _onStorageEvent() {
					if (this._deleted) return;
					const e = await this.assertedPersistence.getCurrentUser();
					if (this.currentUser || e) {
						if (this.currentUser && e && this.currentUser.uid === e.uid) {
							this._currentUser._assign(e), await this.currentUser.getIdToken();
							return;
						}
						await this._updateCurrentUser(e, !0);
					}
				}
				async initializeCurrentUserFromIdToken(e) {
					try {
						const t = await V(this, { idToken: e }),
							r = await et._fromGetAccountInfoResponse(this, t, e);
						await this.directlySetCurrentUser(r);
					} catch (e) {
						console.warn(
							"FirebaseServerApp could not login user with provided authIdToken: ",
							e,
						),
							await this.directlySetCurrentUser(null);
					}
				}
				async initializeCurrentUser(e) {
					var t;
					if ((0, i.xZ)(this.app)) {
						const e = this.app.settings.authIdToken;
						return e
							? new Promise((t) => {
									setTimeout(() =>
										this.initializeCurrentUserFromIdToken(e).then(t, t),
									);
								})
							: this.directlySetCurrentUser(null);
					}
					let r = await this.assertedPersistence.getCurrentUser(),
						n = r,
						s = !1;
					if (e && this.config.authDomain) {
						await this.getOrInitRedirectPersistenceManager();
						const r =
								null === (t = this.redirectUser) || void 0 === t
									? void 0
									: t._redirectEventId,
							i = null == n ? void 0 : n._redirectEventId,
							a = await this.tryRedirectSignIn(e);
						(!r || r === i) &&
							(null == a ? void 0 : a.user) &&
							((n = a.user), (s = !0));
					}
					if (!n) return this.directlySetCurrentUser(null);
					if (!n._redirectEventId) {
						if (s)
							try {
								await this.beforeStateQueue.runMiddleware(n);
							} catch (e) {
								(n = r),
									this._popupRedirectResolver._overrideRedirectResult(
										this,
										() => Promise.reject(e),
									);
							}
						return n
							? this.reloadAndSetCurrentUserOrClear(n)
							: this.directlySetCurrentUser(null);
					}
					return (_(this._popupRedirectResolver, this, "argument-error"),
					await this.getOrInitRedirectPersistenceManager(),
					this.redirectUser &&
						this.redirectUser._redirectEventId === n._redirectEventId)
						? this.directlySetCurrentUser(n)
						: this.reloadAndSetCurrentUserOrClear(n);
				}
				async tryRedirectSignIn(e) {
					let t = null;
					try {
						t = await this._popupRedirectResolver._completeRedirectFn(
							this,
							e,
							!0,
						);
					} catch (e) {
						await this._setRedirectUser(null);
					}
					return t;
				}
				async reloadAndSetCurrentUserOrClear(e) {
					try {
						await J(e);
					} catch (e) {
						if ((null == e ? void 0 : e.code) !== "auth/network-request-failed")
							return this.directlySetCurrentUser(null);
					}
					return this.directlySetCurrentUser(e);
				}
				useDeviceLanguage() {
					this.languageCode = (() => {
						if ("undefined" == typeof navigator) return null;
						const e = navigator;
						return (e.languages && e.languages[0]) || e.language || null;
					})();
				}
				async _delete() {
					this._deleted = !0;
				}
				async updateCurrentUser(e) {
					if ((0, i.xZ)(this.app)) return Promise.reject(m(this));
					const t = e ? (0, n.Ku)(e) : null;
					return (
						t &&
							_(
								t.auth.config.apiKey === this.config.apiKey,
								this,
								"invalid-user-token",
							),
						this._updateCurrentUser(t && t._clone(this))
					);
				}
				async _updateCurrentUser(e, t = !1) {
					if (!this._deleted)
						return (
							e && _(this.tenantId === e.tenantId, this, "tenant-id-mismatch"),
							t || (await this.beforeStateQueue.runMiddleware(e)),
							this.queue(async () => {
								await this.directlySetCurrentUser(e),
									this.notifyAuthListeners();
							})
						);
				}
				async signOut() {
					return (0, i.xZ)(this.app)
						? Promise.reject(m(this))
						: (await this.beforeStateQueue.runMiddleware(null),
							(this.redirectPersistenceManager ||
								this._popupRedirectResolver) &&
								(await this._setRedirectUser(null)),
							this._updateCurrentUser(null, !0));
				}
				setPersistence(e) {
					return (0, i.xZ)(this.app)
						? Promise.reject(m(this))
						: this.queue(async () => {
								await this.assertedPersistence.setPersistence(ei(e));
							});
				}
				_getRecaptchaConfig() {
					return null == this.tenantId
						? this._agentRecaptchaConfig
						: this._tenantRecaptchaConfigs[this.tenantId];
				}
				async validatePassword(e) {
					this._getPasswordPolicyInternal() ||
						(await this._updatePasswordPolicy());
					const t = this._getPasswordPolicyInternal();
					return t.schemaVersion !==
						this.EXPECTED_PASSWORD_POLICY_SCHEMA_VERSION
						? Promise.reject(
								this._errorFactory.create(
									"unsupported-password-policy-schema-version",
									{},
								),
							)
						: t.validatePassword(e);
				}
				_getPasswordPolicyInternal() {
					return null === this.tenantId
						? this._projectPasswordPolicy
						: this._tenantPasswordPolicies[this.tenantId];
				}
				async _updatePasswordPolicy() {
					const e = new ew(await eI(this));
					null === this.tenantId
						? (this._projectPasswordPolicy = e)
						: (this._tenantPasswordPolicies[this.tenantId] = e);
				}
				_getPersistence() {
					return this.assertedPersistence.persistence.type;
				}
				_updateErrorMap(e) {
					this._errorFactory = new n.FA("auth", "Firebase", e());
				}
				onAuthStateChanged(e, t, r) {
					return this.registerStateListener(
						this.authStateSubscription,
						e,
						t,
						r,
					);
				}
				beforeAuthStateChanged(e, t) {
					return this.beforeStateQueue.pushCallback(e, t);
				}
				onIdTokenChanged(e, t, r) {
					return this.registerStateListener(this.idTokenSubscription, e, t, r);
				}
				authStateReady() {
					return new Promise((e, t) => {
						if (this.currentUser) e();
						else {
							const r = this.onAuthStateChanged(() => {
								r(), e();
							}, t);
						}
					});
				}
				async revokeAccessToken(e) {
					if (this.currentUser) {
						const t = {
							providerId: "apple.com",
							tokenType: "ACCESS_TOKEN",
							token: e,
							idToken: await this.currentUser.getIdToken(),
						};
						null != this.tenantId && (t.tenantId = this.tenantId),
							await X(this, t);
					}
				}
				toJSON() {
					var e;
					return {
						apiKey: this.config.apiKey,
						authDomain: this.config.authDomain,
						appName: this.name,
						currentUser:
							null === (e = this._currentUser) || void 0 === e
								? void 0
								: e.toJSON(),
					};
				}
				async _setRedirectUser(e, t) {
					const r = await this.getOrInitRedirectPersistenceManager(t);
					return null === e ? r.removeCurrentUser() : r.setCurrentUser(e);
				}
				async getOrInitRedirectPersistenceManager(e) {
					if (!this.redirectPersistenceManager) {
						const t = (e && ei(e)) || this._popupRedirectResolver;
						_(t, this, "argument-error"),
							(this.redirectPersistenceManager = await ea.create(
								this,
								[ei(t._redirectPersistence)],
								"redirectUser",
							)),
							(this.redirectUser =
								await this.redirectPersistenceManager.getCurrentUser());
					}
					return this.redirectPersistenceManager;
				}
				async _redirectUserForId(e) {
					var t, r;
					return (this._isInitialized && (await this.queue(async () => {})),
					(null === (t = this._currentUser) || void 0 === t
						? void 0
						: t._redirectEventId) === e)
						? this._currentUser
						: (null === (r = this.redirectUser) || void 0 === r
									? void 0
									: r._redirectEventId) === e
							? this.redirectUser
							: null;
				}
				async _persistUserIfCurrent(e) {
					if (e === this.currentUser)
						return this.queue(async () => this.directlySetCurrentUser(e));
				}
				_notifyListenersIfCurrent(e) {
					e === this.currentUser && this.notifyAuthListeners();
				}
				_key() {
					return `${this.config.authDomain}:${this.config.apiKey}:${this.name}`;
				}
				_startProactiveRefresh() {
					(this.isProactiveRefreshEnabled = !0),
						this.currentUser && this._currentUser._startProactiveRefresh();
				}
				_stopProactiveRefresh() {
					(this.isProactiveRefreshEnabled = !1),
						this.currentUser && this._currentUser._stopProactiveRefresh();
				}
				get _currentUser() {
					return this.currentUser;
				}
				notifyAuthListeners() {
					var e, t;
					if (!this._isInitialized) return;
					this.idTokenSubscription.next(this.currentUser);
					const r =
						null !==
							(t =
								null === (e = this.currentUser) || void 0 === e
									? void 0
									: e.uid) && void 0 !== t
							? t
							: null;
					this.lastNotifiedUid !== r &&
						((this.lastNotifiedUid = r),
						this.authStateSubscription.next(this.currentUser));
				}
				registerStateListener(e, t, r, i) {
					if (this._deleted) return () => {};
					let n = "function" == typeof t ? t : t.next.bind(t),
						s = !1,
						a = this._isInitialized
							? Promise.resolve()
							: this._initializationPromise;
					if (
						(_(a, this, "internal-error"),
						a.then(() => {
							!s && n(this.currentUser);
						}),
						"function" == typeof t)
					) {
						const n = e.addObserver(t, r, i);
						return () => {
							(s = !0), n();
						};
					}
					{
						const r = e.addObserver(t);
						return () => {
							(s = !0), r();
						};
					}
				}
				async directlySetCurrentUser(e) {
					this.currentUser &&
						this.currentUser !== e &&
						this._currentUser._stopProactiveRefresh(),
						e && this.isProactiveRefreshEnabled && e._startProactiveRefresh(),
						(this.currentUser = e),
						e
							? await this.assertedPersistence.setCurrentUser(e)
							: await this.assertedPersistence.removeCurrentUser();
				}
				queue(e) {
					return (
						(this.operations = this.operations.then(e, e)), this.operations
					);
				}
				get assertedPersistence() {
					return (
						_(this.persistenceManager, this, "internal-error"),
						this.persistenceManager
					);
				}
				_logFramework(e) {
					!(!e || this.frameworks.includes(e)) &&
						(this.frameworks.push(e),
						this.frameworks.sort(),
						(this.clientVersion = ev(
							this.config.clientPlatform,
							this._getFrameworks(),
						)));
				}
				_getFrameworks() {
					return this.frameworks;
				}
				async _getAdditionalHeaders() {
					var e;
					const t = { "X-Client-Version": this.clientVersion };
					this.app.options.appId &&
						(t["X-Firebase-gmpid"] = this.app.options.appId);
					const r = await (null ===
						(e = this.heartbeatServiceProvider.getImmediate({
							optional: !0,
						})) || void 0 === e
						? void 0
						: e.getHeartbeatsHeader());
					r && (t["X-Firebase-Client"] = r);
					const i = await this._getAppCheckToken();
					return i && (t["X-Firebase-AppCheck"] = i), t;
				}
				async _getAppCheckToken() {
					var e;
					const t = await (null ===
						(e = this.appCheckServiceProvider.getImmediate({ optional: !0 })) ||
					void 0 === e
						? void 0
						: e.getToken());
					return (
						(null == t ? void 0 : t.error) &&
							((e, ...t) => {
								u.logLevel <= s.$b.WARN && u.warn(`Auth (${i.MF}): ${e}`, ...t);
							})(`Error while retrieving App Check token: ${t.error}`),
						null == t ? void 0 : t.token
					);
				}
			}
			function eT(e) {
				return (0, n.Ku)(e);
			}
			class eE {
				constructor(e) {
					(this.auth = e),
						(this.observer = null),
						(this.addObserver = (0, n.tD)((e) => (this.observer = e)));
				}
				get next() {
					return (
						_(this.observer, this.auth, "internal-error"),
						this.observer.next.bind(this.observer)
					);
				}
			}
			let ek = {
				async loadJS() {
					throw Error("Unable to load external scripts");
				},
				recaptchaV2Script: "",
				recaptchaEnterpriseScript: "",
				gapiScript: "",
			};
			function eS(e) {
				return `__${e}${Math.floor(1e6 * Math.random())}`;
			}
			class eR {
				constructor(e) {
					(this.type = "recaptcha-enterprise"), (this.auth = eT(e));
				}
				async verify(e = "verify", t = !1) {
					async function r(e) {
						if (!t) {
							if (null == e.tenantId && null != e._agentRecaptchaConfig)
								return e._agentRecaptchaConfig.siteKey;
							if (
								null != e.tenantId &&
								void 0 !== e._tenantRecaptchaConfigs[e.tenantId]
							)
								return e._tenantRecaptchaConfigs[e.tenantId].siteKey;
						}
						return new Promise(async (t, r) => {
							F(e, {
								clientType: "CLIENT_TYPE_WEB",
								version: "RECAPTCHA_ENTERPRISE",
							})
								.then((i) => {
									if (void 0 === i.recaptchaKey)
										r(Error("recaptcha Enterprise site key undefined"));
									else {
										const r = new M(i);
										return (
											null == e.tenantId
												? (e._agentRecaptchaConfig = r)
												: (e._tenantRecaptchaConfigs[e.tenantId] = r),
											t(r.siteKey)
										);
									}
								})
								.catch((e) => {
									r(e);
								});
						});
					}
					function i(t, r, i) {
						const n = window.grecaptcha;
						D(n)
							? n.enterprise.ready(() => {
									n.enterprise
										.execute(t, { action: e })
										.then((e) => {
											r(e);
										})
										.catch(() => {
											r("NO_RECAPTCHA");
										});
								})
							: i(Error("No reCAPTCHA enterprise script loaded."));
					}
					return new Promise((e, n) => {
						r(this.auth)
							.then((r) => {
								if (!t && D(window.grecaptcha)) i(r, e, n);
								else {
									var s;
									if ("undefined" == typeof window) {
										n(Error("RecaptchaVerifier is only supported in browser"));
										return;
									}
									let t = ek.recaptchaEnterpriseScript;
									0 !== t.length && (t += r),
										((s = t), ek.loadJS(s))
											.then(() => {
												i(r, e, n);
											})
											.catch((e) => {
												n(e);
											});
								}
							})
							.catch((e) => {
								n(e);
							});
					});
				}
			}
			async function eP(e, t, r, i = !1) {
				let n;
				const s = new eR(e);
				try {
					n = await s.verify(r);
				} catch (e) {
					n = await s.verify(r, !0);
				}
				const a = Object.assign({}, t);
				return (
					i
						? Object.assign(a, { captchaResp: n })
						: Object.assign(a, { captchaResponse: n }),
					Object.assign(a, { clientType: "CLIENT_TYPE_WEB" }),
					Object.assign(a, { recaptchaVersion: "RECAPTCHA_ENTERPRISE" }),
					a
				);
			}
			async function eC(e, t, r, i) {
				var n;
				if (
					null === (n = e._getRecaptchaConfig()) ||
					void 0 === n ||
					!n.isProviderEnabled("EMAIL_PASSWORD_PROVIDER")
				)
					return i(e, t).catch(async (n) => {
						if ("auth/missing-recaptcha-token" !== n.code)
							return Promise.reject(n);
						{
							console.log(
								`${r} is protected by reCAPTCHA Enterprise for this project. Automatically triggering the reCAPTCHA flow and restarting the flow.`,
							);
							const n = await eP(e, t, r, "getOobCode" === r);
							return i(e, n);
						}
					});
				{
					const n = await eP(e, t, r, "getOobCode" === r);
					return i(e, n);
				}
			}
			function eO(e) {
				const t = e.indexOf(":");
				return t < 0 ? "" : e.substr(0, t + 1);
			}
			function eb(e) {
				if (!e) return null;
				const t = Number(e);
				return isNaN(t) ? null : t;
			}
			class eA {
				constructor(e, t) {
					(this.providerId = e), (this.signInMethod = t);
				}
				toJSON() {
					return I("not implemented");
				}
				_getIdTokenResponse(e) {
					return I("not implemented");
				}
				_linkToIdToken(e, t) {
					return I("not implemented");
				}
				_getReauthenticationResolver(e) {
					return I("not implemented");
				}
			}
			async function eN(e, t) {
				return O(e, "POST", "/v1/accounts:resetPassword", C(e, t));
			}
			async function eL(e, t) {
				return O(e, "POST", "/v1/accounts:update", t);
			}
			async function eU(e, t) {
				return O(e, "POST", "/v1/accounts:signUp", t);
			}
			async function eD(e, t) {
				return A(e, "POST", "/v1/accounts:signInWithPassword", C(e, t));
			}
			async function eM(e, t) {
				return O(e, "POST", "/v1/accounts:sendOobCode", C(e, t));
			}
			async function eF(e, t) {
				return A(e, "POST", "/v1/accounts:signInWithEmailLink", C(e, t));
			}
			async function ex(e, t) {
				return A(e, "POST", "/v1/accounts:signInWithEmailLink", C(e, t));
			}
			class eV extends eA {
				constructor(e, t, r, i = null) {
					super("password", r),
						(this._email = e),
						(this._password = t),
						(this._tenantId = i);
				}
				static _fromEmailAndPassword(e, t) {
					return new eV(e, t, "password");
				}
				static _fromEmailAndCode(e, t, r = null) {
					return new eV(e, t, "emailLink", r);
				}
				toJSON() {
					return {
						email: this._email,
						password: this._password,
						signInMethod: this.signInMethod,
						tenantId: this._tenantId,
					};
				}
				static fromJSON(e) {
					const t = "string" == typeof e ? JSON.parse(e) : e;
					if (
						(null == t ? void 0 : t.email) &&
						(null == t ? void 0 : t.password)
					) {
						if ("password" === t.signInMethod)
							return this._fromEmailAndPassword(t.email, t.password);
						if ("emailLink" === t.signInMethod)
							return this._fromEmailAndCode(t.email, t.password, t.tenantId);
					}
					return null;
				}
				async _getIdTokenResponse(e) {
					switch (this.signInMethod) {
						case "password":
							return eC(
								e,
								{
									returnSecureToken: !0,
									email: this._email,
									password: this._password,
									clientType: "CLIENT_TYPE_WEB",
								},
								"signInWithPassword",
								eD,
							);
						case "emailLink":
							return eF(e, { email: this._email, oobCode: this._password });
						default:
							d(e, "internal-error");
					}
				}
				async _linkToIdToken(e, t) {
					switch (this.signInMethod) {
						case "password":
							return eC(
								e,
								{
									idToken: t,
									returnSecureToken: !0,
									email: this._email,
									password: this._password,
									clientType: "CLIENT_TYPE_WEB",
								},
								"signUpPassword",
								eU,
							);
						case "emailLink":
							return ex(e, {
								idToken: t,
								email: this._email,
								oobCode: this._password,
							});
						default:
							d(e, "internal-error");
					}
				}
				_getReauthenticationResolver(e) {
					return this._getIdTokenResponse(e);
				}
			}
			async function ej(e, t) {
				return A(e, "POST", "/v1/accounts:signInWithIdp", C(e, t));
			}
			class eH extends eA {
				constructor() {
					super(...arguments), (this.pendingToken = null);
				}
				static _fromParams(e) {
					const t = new eH(e.providerId, e.signInMethod);
					return (
						e.idToken || e.accessToken
							? (e.idToken && (t.idToken = e.idToken),
								e.accessToken && (t.accessToken = e.accessToken),
								e.nonce && !e.pendingToken && (t.nonce = e.nonce),
								e.pendingToken && (t.pendingToken = e.pendingToken))
							: e.oauthToken && e.oauthTokenSecret
								? ((t.accessToken = e.oauthToken),
									(t.secret = e.oauthTokenSecret))
								: d("argument-error"),
						t
					);
				}
				toJSON() {
					return {
						idToken: this.idToken,
						accessToken: this.accessToken,
						secret: this.secret,
						nonce: this.nonce,
						pendingToken: this.pendingToken,
						providerId: this.providerId,
						signInMethod: this.signInMethod,
					};
				}
				static fromJSON(e) {
					const t = "string" == typeof e ? JSON.parse(e) : e,
						{ providerId: r, signInMethod: i } = t,
						n = (0, a.Tt)(t, ["providerId", "signInMethod"]);
					if (!r || !i) return null;
					const s = new eH(r, i);
					return (
						(s.idToken = n.idToken || void 0),
						(s.accessToken = n.accessToken || void 0),
						(s.secret = n.secret),
						(s.nonce = n.nonce),
						(s.pendingToken = n.pendingToken || null),
						s
					);
				}
				_getIdTokenResponse(e) {
					return ej(e, this.buildRequest());
				}
				_linkToIdToken(e, t) {
					const r = this.buildRequest();
					return (r.idToken = t), ej(e, r);
				}
				_getReauthenticationResolver(e) {
					const t = this.buildRequest();
					return (t.autoCreate = !1), ej(e, t);
				}
				buildRequest() {
					const e = { requestUri: "http://localhost", returnSecureToken: !0 };
					if (this.pendingToken) e.pendingToken = this.pendingToken;
					else {
						const t = {};
						this.idToken && (t.id_token = this.idToken),
							this.accessToken && (t.access_token = this.accessToken),
							this.secret && (t.oauth_token_secret = this.secret),
							(t.providerId = this.providerId),
							this.nonce && !this.pendingToken && (t.nonce = this.nonce),
							(e.postBody = (0, n.Am)(t));
					}
					return e;
				}
			}
			async function eW(e, t) {
				return O(e, "POST", "/v1/accounts:sendVerificationCode", C(e, t));
			}
			async function ez(e, t) {
				return A(e, "POST", "/v1/accounts:signInWithPhoneNumber", C(e, t));
			}
			async function eK(e, t) {
				const r = await A(
					e,
					"POST",
					"/v1/accounts:signInWithPhoneNumber",
					C(e, t),
				);
				if (r.temporaryProof)
					throw U(e, "account-exists-with-different-credential", r);
				return r;
			}
			const e$ = { USER_NOT_FOUND: "user-not-found" };
			async function eq(e, t) {
				return A(
					e,
					"POST",
					"/v1/accounts:signInWithPhoneNumber",
					C(e, Object.assign(Object.assign({}, t), { operation: "REAUTH" })),
					e$,
				);
			}
			class eG extends eA {
				constructor(e) {
					super("phone", "phone"), (this.params = e);
				}
				static _fromVerification(e, t) {
					return new eG({ verificationId: e, verificationCode: t });
				}
				static _fromTokenResponse(e, t) {
					return new eG({ phoneNumber: e, temporaryProof: t });
				}
				_getIdTokenResponse(e) {
					return ez(e, this._makeVerificationRequest());
				}
				_linkToIdToken(e, t) {
					return eK(
						e,
						Object.assign({ idToken: t }, this._makeVerificationRequest()),
					);
				}
				_getReauthenticationResolver(e) {
					return eq(e, this._makeVerificationRequest());
				}
				_makeVerificationRequest() {
					const {
						temporaryProof: e,
						phoneNumber: t,
						verificationId: r,
						verificationCode: i,
					} = this.params;
					return e && t
						? { temporaryProof: e, phoneNumber: t }
						: { sessionInfo: r, code: i };
				}
				toJSON() {
					const e = { providerId: this.providerId };
					return (
						this.params.phoneNumber &&
							(e.phoneNumber = this.params.phoneNumber),
						this.params.temporaryProof &&
							(e.temporaryProof = this.params.temporaryProof),
						this.params.verificationCode &&
							(e.verificationCode = this.params.verificationCode),
						this.params.verificationId &&
							(e.verificationId = this.params.verificationId),
						e
					);
				}
				static fromJSON(e) {
					"string" == typeof e && (e = JSON.parse(e));
					const {
						verificationId: t,
						verificationCode: r,
						phoneNumber: i,
						temporaryProof: n,
					} = e;
					return r || t || i || n
						? new eG({
								verificationId: t,
								verificationCode: r,
								phoneNumber: i,
								temporaryProof: n,
							})
						: null;
				}
			}
			class eJ {
				constructor(e) {
					var t, r, i, s, a, o;
					const l = (0, n.I9)((0, n.hp)(e)),
						c = null !== (t = l.apiKey) && void 0 !== t ? t : null,
						u = null !== (r = l.oobCode) && void 0 !== r ? r : null,
						h = ((e) => {
							switch (e) {
								case "recoverEmail":
									return "RECOVER_EMAIL";
								case "resetPassword":
									return "PASSWORD_RESET";
								case "signIn":
									return "EMAIL_SIGNIN";
								case "verifyEmail":
									return "VERIFY_EMAIL";
								case "verifyAndChangeEmail":
									return "VERIFY_AND_CHANGE_EMAIL";
								case "revertSecondFactorAddition":
									return "REVERT_SECOND_FACTOR_ADDITION";
								default:
									return null;
							}
						})(null !== (i = l.mode) && void 0 !== i ? i : null);
					_(c && u && h, "argument-error"),
						(this.apiKey = c),
						(this.operation = h),
						(this.code = u),
						(this.continueUrl =
							null !== (s = l.continueUrl) && void 0 !== s ? s : null),
						(this.languageCode =
							null !== (a = l.languageCode) && void 0 !== a ? a : null),
						(this.tenantId =
							null !== (o = l.tenantId) && void 0 !== o ? o : null);
				}
				static parseLink(e) {
					const t = ((e) => {
						const t = (0, n.I9)((0, n.hp)(e)).link,
							r = t ? (0, n.I9)((0, n.hp)(t)).deep_link_id : null,
							i = (0, n.I9)((0, n.hp)(e)).deep_link_id;
						return (
							(i ? (0, n.I9)((0, n.hp)(i)).link : null) || i || r || t || e
						);
					})(e);
					try {
						return new eJ(t);
					} catch (e) {
						return null;
					}
				}
			}
			class eB {
				constructor() {
					this.providerId = eB.PROVIDER_ID;
				}
				static credential(e, t) {
					return eV._fromEmailAndPassword(e, t);
				}
				static credentialWithLink(e, t) {
					const r = eJ.parseLink(t);
					return (
						_(r, "argument-error"), eV._fromEmailAndCode(e, r.code, r.tenantId)
					);
				}
			}
			(eB.PROVIDER_ID = "password"),
				(eB.EMAIL_PASSWORD_SIGN_IN_METHOD = "password"),
				(eB.EMAIL_LINK_SIGN_IN_METHOD = "emailLink");
			class eZ {
				constructor(e) {
					(this.providerId = e),
						(this.defaultLanguageCode = null),
						(this.customParameters = {});
				}
				setDefaultLanguage(e) {
					this.defaultLanguageCode = e;
				}
				setCustomParameters(e) {
					return (this.customParameters = e), this;
				}
				getCustomParameters() {
					return this.customParameters;
				}
			}
			class eQ extends eZ {
				constructor() {
					super(...arguments), (this.scopes = []);
				}
				addScope(e) {
					return this.scopes.includes(e) || this.scopes.push(e), this;
				}
				getScopes() {
					return [...this.scopes];
				}
			}
			class eX extends eQ {
				constructor() {
					super("facebook.com");
				}
				static credential(e) {
					return eH._fromParams({
						providerId: eX.PROVIDER_ID,
						signInMethod: eX.FACEBOOK_SIGN_IN_METHOD,
						accessToken: e,
					});
				}
				static credentialFromResult(e) {
					return eX.credentialFromTaggedObject(e);
				}
				static credentialFromError(e) {
					return eX.credentialFromTaggedObject(e.customData || {});
				}
				static credentialFromTaggedObject({ _tokenResponse: e }) {
					if (!e || !("oauthAccessToken" in e) || !e.oauthAccessToken)
						return null;
					try {
						return eX.credential(e.oauthAccessToken);
					} catch (e) {
						return null;
					}
				}
			}
			(eX.FACEBOOK_SIGN_IN_METHOD = "facebook.com"),
				(eX.PROVIDER_ID = "facebook.com");
			class eY extends eQ {
				constructor() {
					super("google.com"), this.addScope("profile");
				}
				static credential(e, t) {
					return eH._fromParams({
						providerId: eY.PROVIDER_ID,
						signInMethod: eY.GOOGLE_SIGN_IN_METHOD,
						idToken: e,
						accessToken: t,
					});
				}
				static credentialFromResult(e) {
					return eY.credentialFromTaggedObject(e);
				}
				static credentialFromError(e) {
					return eY.credentialFromTaggedObject(e.customData || {});
				}
				static credentialFromTaggedObject({ _tokenResponse: e }) {
					if (!e) return null;
					const { oauthIdToken: t, oauthAccessToken: r } = e;
					if (!t && !r) return null;
					try {
						return eY.credential(t, r);
					} catch (e) {
						return null;
					}
				}
			}
			(eY.GOOGLE_SIGN_IN_METHOD = "google.com"),
				(eY.PROVIDER_ID = "google.com");
			class e0 extends eQ {
				constructor() {
					super("github.com");
				}
				static credential(e) {
					return eH._fromParams({
						providerId: e0.PROVIDER_ID,
						signInMethod: e0.GITHUB_SIGN_IN_METHOD,
						accessToken: e,
					});
				}
				static credentialFromResult(e) {
					return e0.credentialFromTaggedObject(e);
				}
				static credentialFromError(e) {
					return e0.credentialFromTaggedObject(e.customData || {});
				}
				static credentialFromTaggedObject({ _tokenResponse: e }) {
					if (!e || !("oauthAccessToken" in e) || !e.oauthAccessToken)
						return null;
					try {
						return e0.credential(e.oauthAccessToken);
					} catch (e) {
						return null;
					}
				}
			}
			(e0.GITHUB_SIGN_IN_METHOD = "github.com"),
				(e0.PROVIDER_ID = "github.com");
			class e1 extends eQ {
				constructor() {
					super("twitter.com");
				}
				static credential(e, t) {
					return eH._fromParams({
						providerId: e1.PROVIDER_ID,
						signInMethod: e1.TWITTER_SIGN_IN_METHOD,
						oauthToken: e,
						oauthTokenSecret: t,
					});
				}
				static credentialFromResult(e) {
					return e1.credentialFromTaggedObject(e);
				}
				static credentialFromError(e) {
					return e1.credentialFromTaggedObject(e.customData || {});
				}
				static credentialFromTaggedObject({ _tokenResponse: e }) {
					if (!e) return null;
					const { oauthAccessToken: t, oauthTokenSecret: r } = e;
					if (!t || !r) return null;
					try {
						return e1.credential(t, r);
					} catch (e) {
						return null;
					}
				}
			}
			async function e3(e, t) {
				return A(e, "POST", "/v1/accounts:signUp", C(e, t));
			}
			(e1.TWITTER_SIGN_IN_METHOD = "twitter.com"),
				(e1.PROVIDER_ID = "twitter.com");
			class e2 {
				constructor(e) {
					(this.user = e.user),
						(this.providerId = e.providerId),
						(this._tokenResponse = e._tokenResponse),
						(this.operationType = e.operationType);
				}
				static async _fromIdTokenResponse(e, t, r, i = !1) {
					return new e2({
						user: await et._fromIdTokenResponse(e, r, i),
						providerId: e4(r),
						_tokenResponse: r,
						operationType: t,
					});
				}
				static async _forOperation(e, t, r) {
					return (
						await e._updateTokensIfNecessary(r, !0),
						new e2({
							user: e,
							providerId: e4(r),
							_tokenResponse: r,
							operationType: t,
						})
					);
				}
			}
			function e4(e) {
				return e.providerId
					? e.providerId
					: "phoneNumber" in e
						? "phone"
						: null;
			}
			class e5 extends n.g {
				constructor(e, t, r, i) {
					var n;
					super(t.code, t.message),
						(this.operationType = r),
						(this.user = i),
						Object.setPrototypeOf(this, e5.prototype),
						(this.customData = {
							appName: e.name,
							tenantId: null !== (n = e.tenantId) && void 0 !== n ? n : void 0,
							_serverResponse: t.customData._serverResponse,
							operationType: r,
						});
				}
				static _fromErrorAndOperation(e, t, r, i) {
					return new e5(e, t, r, i);
				}
			}
			function e6(e, t, r, i) {
				return (
					"reauthenticate" === t
						? r._getReauthenticationResolver(e)
						: r._getIdTokenResponse(e)
				).catch((r) => {
					if ("auth/multi-factor-auth-required" === r.code)
						throw e5._fromErrorAndOperation(e, r, t, i);
					throw r;
				});
			}
			async function e8(e, t, r = !1) {
				const i = await $(e, t._linkToIdToken(e.auth, await e.getIdToken()), r);
				return e2._forOperation(e, "link", i);
			}
			async function e9(e, t, r) {
				await J(t),
					_(
						new Set(
							t.providerData.map(({ providerId: e }) => e).filter((e) => !!e),
						).has(r) === e,
						t.auth,
						!1 === e ? "provider-already-linked" : "no-such-provider",
					);
			}
			async function e7(e, t, r = !1) {
				const { auth: n } = e;
				if ((0, i.xZ)(n.app)) return Promise.reject(m(n));
				const s = "reauthenticate";
				try {
					const i = await $(e, e6(n, s, t, e), r);
					_(i.idToken, n, "internal-error");
					const a = z(i.idToken);
					_(a, n, "internal-error");
					const { sub: o } = a;
					return _(e.uid === o, n, "user-mismatch"), e2._forOperation(e, s, i);
				} catch (e) {
					throw (
						((null == e ? void 0 : e.code) === "auth/user-not-found" &&
							d(n, "user-mismatch"),
						e)
					);
				}
			}
			async function te(e, t, r = !1) {
				if ((0, i.xZ)(e.app)) return Promise.reject(m(e));
				const n = "signIn",
					s = await e6(e, n, t),
					a = await e2._fromIdTokenResponse(e, n, s);
				return r || (await e._updateCurrentUser(a.user)), a;
			}
			class tt {
				constructor(e, t) {
					(this.factorId = e),
						(this.uid = t.mfaEnrollmentId),
						(this.enrollmentTime = new Date(t.enrolledAt).toUTCString()),
						(this.displayName = t.displayName);
				}
				static _fromServerResponse(e, t) {
					return "phoneInfo" in t
						? tr._fromServerResponse(e, t)
						: "totpInfo" in t
							? ti._fromServerResponse(e, t)
							: d(e, "internal-error");
				}
			}
			class tr extends tt {
				constructor(e) {
					super("phone", e), (this.phoneNumber = e.phoneInfo);
				}
				static _fromServerResponse(e, t) {
					return new tr(t);
				}
			}
			class ti extends tt {
				constructor(e) {
					super("totp", e);
				}
				static _fromServerResponse(e, t) {
					return new ti(t);
				}
			}
			async function tn(e) {
				const t = eT(e);
				t._getPasswordPolicyInternal() && (await t._updatePasswordPolicy());
			}
			async function ts(e, t, r) {
				if ((0, i.xZ)(e.app)) return Promise.reject(m(e));
				const n = eT(e),
					s = eC(
						n,
						{
							returnSecureToken: !0,
							email: t,
							password: r,
							clientType: "CLIENT_TYPE_WEB",
						},
						"signUpPassword",
						e3,
					),
					a = await s.catch((t) => {
						throw (
							("auth/password-does-not-meet-requirements" === t.code && tn(e),
							t)
						);
					}),
					o = await e2._fromIdTokenResponse(n, "signIn", a);
				return await n._updateCurrentUser(o.user), o;
			}
			class ta {
				constructor(e, t, r = {}) {
					(this.isNewUser = e), (this.providerId = t), (this.profile = r);
				}
			}
			class to extends ta {
				constructor(e, t, r, i) {
					super(e, t, r), (this.username = i);
				}
			}
			class tl {
				constructor(e, t, r) {
					(this.type = e), (this.credential = t), (this.user = r);
				}
				static _fromIdtoken(e, t) {
					return new tl("enroll", e, t);
				}
				static _fromMfaPendingCredential(e) {
					return new tl("signin", e);
				}
				toJSON() {
					return {
						multiFactorSession: {
							["enroll" === this.type ? "idToken" : "pendingCredential"]:
								this.credential,
						},
					};
				}
				static fromJSON(e) {
					var t, r;
					if (null == e ? void 0 : e.multiFactorSession) {
						if (
							null === (t = e.multiFactorSession) || void 0 === t
								? void 0
								: t.pendingCredential
						)
							return tl._fromMfaPendingCredential(
								e.multiFactorSession.pendingCredential,
							);
						if (
							null === (r = e.multiFactorSession) || void 0 === r
								? void 0
								: r.idToken
						)
							return tl._fromIdtoken(e.multiFactorSession.idToken);
					}
					return null;
				}
			}
			class tc {
				constructor(e, t, r) {
					(this.session = e), (this.hints = t), (this.signInResolver = r);
				}
				static _fromError(e, t) {
					const r = eT(e),
						i = t.customData._serverResponse,
						n = (i.mfaInfo || []).map((e) => tt._fromServerResponse(r, e));
					_(i.mfaPendingCredential, r, "internal-error");
					const s = tl._fromMfaPendingCredential(i.mfaPendingCredential);
					return new tc(s, n, async (e) => {
						const n = await e._process(r, s);
						delete i.mfaInfo, delete i.mfaPendingCredential;
						const a = Object.assign(Object.assign({}, i), {
							idToken: n.idToken,
							refreshToken: n.refreshToken,
						});
						switch (t.operationType) {
							case "signIn":
								const o = await e2._fromIdTokenResponse(r, t.operationType, a);
								return await r._updateCurrentUser(o.user), o;
							case "reauthenticate":
								return (
									_(t.user, r, "internal-error"),
									e2._forOperation(t.user, t.operationType, a)
								);
							default:
								d(r, "internal-error");
						}
					});
				}
				async resolveSignIn(e) {
					return this.signInResolver(e);
				}
			}
			class tu {
				constructor(e) {
					(this.user = e),
						(this.enrolledFactors = []),
						e._onReload((t) => {
							t.mfaInfo &&
								(this.enrolledFactors = t.mfaInfo.map((t) =>
									tt._fromServerResponse(e.auth, t),
								));
						});
				}
				static _fromUser(e) {
					return new tu(e);
				}
				async getSession() {
					return tl._fromIdtoken(await this.user.getIdToken(), this.user);
				}
				async enroll(e, t) {
					const r = await this.getSession(),
						i = await $(this.user, e._process(this.user.auth, r, t));
					return (
						await this.user._updateTokensIfNecessary(i), this.user.reload()
					);
				}
				async unenroll(e) {
					const t = "string" == typeof e ? e : e.uid,
						r = await this.user.getIdToken();
					try {
						var i;
						const e = await $(
							this.user,
							((i = this.user.auth),
							O(
								i,
								"POST",
								"/v2/accounts/mfaEnrollment:withdraw",
								C(i, { idToken: r, mfaEnrollmentId: t }),
							)),
						);
						(this.enrolledFactors = this.enrolledFactors.filter(
							({ uid: e }) => e !== t,
						)),
							await this.user._updateTokensIfNecessary(e),
							await this.user.reload();
					} catch (e) {
						throw e;
					}
				}
			}
			new WeakMap();
			const th = "__sak";
			class td {
				constructor(e, t) {
					(this.storageRetriever = e), (this.type = t);
				}
				_isAvailable() {
					try {
						if (!this.storage) return Promise.resolve(!1);
						return (
							this.storage.setItem(th, "1"),
							this.storage.removeItem(th),
							Promise.resolve(!0)
						);
					} catch (e) {
						return Promise.resolve(!1);
					}
				}
				_set(e, t) {
					return this.storage.setItem(e, JSON.stringify(t)), Promise.resolve();
				}
				_get(e) {
					const t = this.storage.getItem(e);
					return Promise.resolve(t ? JSON.parse(t) : null);
				}
				_remove(e) {
					return this.storage.removeItem(e), Promise.resolve();
				}
				get storage() {
					return this.storageRetriever();
				}
			}
			class tp extends td {
				constructor() {
					super(() => window.localStorage, "LOCAL"),
						(this.boundEventHandler = (e, t) => this.onStorageEvent(e, t)),
						(this.listeners = {}),
						(this.localCache = {}),
						(this.pollTimer = null),
						(this.fallbackToPolling = eg()),
						(this._shouldAllowMigration = !0);
				}
				forAllChangedKeys(e) {
					for (const t of Object.keys(this.listeners)) {
						const r = this.storage.getItem(t),
							i = this.localCache[t];
						r !== i && e(t, i, r);
					}
				}
				onStorageEvent(e, t = !1) {
					if (!e.key) {
						this.forAllChangedKeys((e, t, r) => {
							this.notifyListeners(e, r);
						});
						return;
					}
					const r = e.key;
					t ? this.detachListener() : this.stopPolling();
					const i = () => {
							const e = this.storage.getItem(r);
							(t || this.localCache[r] !== e) && this.notifyListeners(r, e);
						},
						s = this.storage.getItem(r);
					(0, n.lT)() &&
					10 === document.documentMode &&
					s !== e.newValue &&
					e.newValue !== e.oldValue
						? setTimeout(i, 10)
						: i();
				}
				notifyListeners(e, t) {
					this.localCache[e] = t;
					const r = this.listeners[e];
					if (r) for (const e of Array.from(r)) e(t ? JSON.parse(t) : t);
				}
				startPolling() {
					this.stopPolling(),
						(this.pollTimer = setInterval(() => {
							this.forAllChangedKeys((e, t, r) => {
								this.onStorageEvent(
									new StorageEvent("storage", {
										key: e,
										oldValue: t,
										newValue: r,
									}),
									!0,
								);
							});
						}, 1e3));
				}
				stopPolling() {
					this.pollTimer &&
						(clearInterval(this.pollTimer), (this.pollTimer = null));
				}
				attachListener() {
					window.addEventListener("storage", this.boundEventHandler);
				}
				detachListener() {
					window.removeEventListener("storage", this.boundEventHandler);
				}
				_addListener(e, t) {
					0 === Object.keys(this.listeners).length &&
						(this.fallbackToPolling
							? this.startPolling()
							: this.attachListener()),
						this.listeners[e] ||
							((this.listeners[e] = new Set()),
							(this.localCache[e] = this.storage.getItem(e))),
						this.listeners[e].add(t);
				}
				_removeListener(e, t) {
					this.listeners[e] &&
						(this.listeners[e].delete(t),
						0 === this.listeners[e].size && delete this.listeners[e]),
						0 === Object.keys(this.listeners).length &&
							(this.detachListener(), this.stopPolling());
				}
				async _set(e, t) {
					await super._set(e, t), (this.localCache[e] = JSON.stringify(t));
				}
				async _get(e) {
					const t = await super._get(e);
					return (this.localCache[e] = JSON.stringify(t)), t;
				}
				async _remove(e) {
					await super._remove(e), delete this.localCache[e];
				}
			}
			tp.type = "LOCAL";
			class tf extends td {
				constructor() {
					super(() => window.sessionStorage, "SESSION");
				}
				_addListener(e, t) {}
				_removeListener(e, t) {}
			}
			tf.type = "SESSION";
			class tm {
				constructor(e) {
					(this.eventTarget = e),
						(this.handlersMap = {}),
						(this.boundEventHandler = this.handleEvent.bind(this));
				}
				static _getInstance(e) {
					const t = this.receivers.find((t) => t.isListeningto(e));
					if (t) return t;
					const r = new tm(e);
					return this.receivers.push(r), r;
				}
				isListeningto(e) {
					return this.eventTarget === e;
				}
				async handleEvent(e) {
					const { eventId: t, eventType: r, data: i } = e.data,
						n = this.handlersMap[r];
					if (!(null == n ? void 0 : n.size)) return;
					e.ports[0].postMessage({ status: "ack", eventId: t, eventType: r });
					const s = Array.from(n).map(async (t) => t(e.origin, i)),
						a = await Promise.all(
							s.map(async (e) => {
								try {
									const t = await e;
									return { fulfilled: !0, value: t };
								} catch (e) {
									return { fulfilled: !1, reason: e };
								}
							}),
						);
					e.ports[0].postMessage({
						status: "done",
						eventId: t,
						eventType: r,
						response: a,
					});
				}
				_subscribe(e, t) {
					0 === Object.keys(this.handlersMap).length &&
						this.eventTarget.addEventListener(
							"message",
							this.boundEventHandler,
						),
						this.handlersMap[e] || (this.handlersMap[e] = new Set()),
						this.handlersMap[e].add(t);
				}
				_unsubscribe(e, t) {
					this.handlersMap[e] && t && this.handlersMap[e].delete(t),
						(t && 0 !== this.handlersMap[e].size) || delete this.handlersMap[e],
						0 === Object.keys(this.handlersMap).length &&
							this.eventTarget.removeEventListener(
								"message",
								this.boundEventHandler,
							);
				}
			}
			function tg(e = "", t = 10) {
				let r = "";
				for (let e = 0; e < t; e++) r += Math.floor(10 * Math.random());
				return e + r;
			}
			tm.receivers = [];
			class tv {
				constructor(e) {
					(this.target = e), (this.handlers = new Set());
				}
				removeMessageHandler(e) {
					e.messageChannel &&
						(e.messageChannel.port1.removeEventListener("message", e.onMessage),
						e.messageChannel.port1.close()),
						this.handlers.delete(e);
				}
				async _send(e, t, r = 50) {
					let i, n;
					const s =
						"undefined" != typeof MessageChannel ? new MessageChannel() : null;
					if (!s) throw Error("connection_unavailable");
					return new Promise((a, o) => {
						const l = tg("", 20);
						s.port1.start();
						const c = setTimeout(() => {
							o(Error("unsupported_event"));
						}, r);
						(n = {
							messageChannel: s,
							onMessage(e) {
								if (e.data.eventId === l)
									switch (e.data.status) {
										case "ack":
											clearTimeout(c),
												(i = setTimeout(() => {
													o(Error("timeout"));
												}, 3e3));
											break;
										case "done":
											clearTimeout(i), a(e.data.response);
											break;
										default:
											clearTimeout(c),
												clearTimeout(i),
												o(Error("invalid_response"));
									}
							},
						}),
							this.handlers.add(n),
							s.port1.addEventListener("message", n.onMessage),
							this.target.postMessage({ eventType: e, eventId: l, data: t }, [
								s.port2,
							]);
					}).finally(() => {
						n && this.removeMessageHandler(n);
					});
				}
			}
			function t_() {
				return window;
			}
			function tI() {
				return (
					void 0 !== t_().WorkerGlobalScope &&
					"function" == typeof t_().importScripts
				);
			}
			async function tw() {
				if (!(null == navigator ? void 0 : navigator.serviceWorker))
					return null;
				try {
					return (await navigator.serviceWorker.ready).active;
				} catch (e) {
					return null;
				}
			}
			const ty = "firebaseLocalStorageDb",
				tT = "firebaseLocalStorage",
				tE = "fbase_key";
			class tk {
				constructor(e) {
					this.request = e;
				}
				toPromise() {
					return new Promise((e, t) => {
						this.request.addEventListener("success", () => {
							e(this.request.result);
						}),
							this.request.addEventListener("error", () => {
								t(this.request.error);
							});
					});
				}
			}
			function tS(e, t) {
				return e
					.transaction([tT], t ? "readwrite" : "readonly")
					.objectStore(tT);
			}
			function tR() {
				const e = indexedDB.open(ty, 1);
				return new Promise((t, r) => {
					e.addEventListener("error", () => {
						r(e.error);
					}),
						e.addEventListener("upgradeneeded", () => {
							const t = e.result;
							try {
								t.createObjectStore(tT, { keyPath: tE });
							} catch (e) {
								r(e);
							}
						}),
						e.addEventListener("success", async () => {
							const r = e.result;
							r.objectStoreNames.contains(tT)
								? t(r)
								: (r.close(),
									await new tk(indexedDB.deleteDatabase(ty)).toPromise(),
									t(await tR()));
						});
				});
			}
			async function tP(e, t, r) {
				return new tk(tS(e, !0).put({ [tE]: t, value: r })).toPromise();
			}
			async function tC(e, t) {
				const r = tS(e, !1).get(t),
					i = await new tk(r).toPromise();
				return void 0 === i ? null : i.value;
			}
			function tO(e, t) {
				return new tk(tS(e, !0).delete(t)).toPromise();
			}
			class tb {
				constructor() {
					(this.type = "LOCAL"),
						(this._shouldAllowMigration = !0),
						(this.listeners = {}),
						(this.localCache = {}),
						(this.pollTimer = null),
						(this.pendingWrites = 0),
						(this.receiver = null),
						(this.sender = null),
						(this.serviceWorkerReceiverAvailable = !1),
						(this.activeServiceWorker = null),
						(this._workerInitializationPromise =
							this.initializeServiceWorkerMessaging().then(
								() => {},
								() => {},
							));
				}
				async _openDb() {
					return this.db || (this.db = await tR()), this.db;
				}
				async _withRetries(e) {
					let t = 0;
					for (;;)
						try {
							const t = await this._openDb();
							return await e(t);
						} catch (e) {
							if (t++ > 3) throw e;
							this.db && (this.db.close(), (this.db = void 0));
						}
				}
				async initializeServiceWorkerMessaging() {
					return tI() ? this.initializeReceiver() : this.initializeSender();
				}
				async initializeReceiver() {
					(this.receiver = tm._getInstance(tI() ? self : null)),
						this.receiver._subscribe("keyChanged", async (e, t) => ({
							keyProcessed: (await this._poll()).includes(t.key),
						})),
						this.receiver._subscribe("ping", async (e, t) => ["keyChanged"]);
				}
				async initializeSender() {
					var e, t;
					if (
						((this.activeServiceWorker = await tw()), !this.activeServiceWorker)
					)
						return;
					this.sender = new tv(this.activeServiceWorker);
					const r = await this.sender._send("ping", {}, 800);
					r &&
						(null === (e = r[0]) || void 0 === e ? void 0 : e.fulfilled) &&
						(null === (t = r[0]) || void 0 === t
							? void 0
							: t.value.includes("keyChanged")) &&
						(this.serviceWorkerReceiverAvailable = !0);
				}
				async notifyServiceWorker(e) {
					var t;
					if (
						this.sender &&
						this.activeServiceWorker &&
						((null ===
							(t = null == navigator ? void 0 : navigator.serviceWorker) ||
						void 0 === t
							? void 0
							: t.controller) || null) === this.activeServiceWorker
					)
						try {
							await this.sender._send(
								"keyChanged",
								{ key: e },
								this.serviceWorkerReceiverAvailable ? 800 : 50,
							);
						} catch (e) {}
				}
				async _isAvailable() {
					try {
						if (!indexedDB) return !1;
						const e = await tR();
						return await tP(e, th, "1"), await tO(e, th), !0;
					} catch (e) {}
					return !1;
				}
				async _withPendingWrite(e) {
					this.pendingWrites++;
					try {
						await e();
					} finally {
						this.pendingWrites--;
					}
				}
				async _set(e, t) {
					return this._withPendingWrite(
						async () => (
							await this._withRetries((r) => tP(r, e, t)),
							(this.localCache[e] = t),
							this.notifyServiceWorker(e)
						),
					);
				}
				async _get(e) {
					const t = await this._withRetries((t) => tC(t, e));
					return (this.localCache[e] = t), t;
				}
				async _remove(e) {
					return this._withPendingWrite(
						async () => (
							await this._withRetries((t) => tO(t, e)),
							delete this.localCache[e],
							this.notifyServiceWorker(e)
						),
					);
				}
				async _poll() {
					const e = await this._withRetries((e) =>
						new tk(tS(e, !1).getAll()).toPromise(),
					);
					if (!e || 0 !== this.pendingWrites) return [];
					const t = [],
						r = new Set();
					if (0 !== e.length)
						for (const { fbase_key: i, value: n } of e)
							r.add(i),
								JSON.stringify(this.localCache[i]) !== JSON.stringify(n) &&
									(this.notifyListeners(i, n), t.push(i));
					for (const e of Object.keys(this.localCache))
						this.localCache[e] &&
							!r.has(e) &&
							(this.notifyListeners(e, null), t.push(e));
					return t;
				}
				notifyListeners(e, t) {
					this.localCache[e] = t;
					const r = this.listeners[e];
					if (r) for (const e of Array.from(r)) e(t);
				}
				startPolling() {
					this.stopPolling(),
						(this.pollTimer = setInterval(async () => this._poll(), 800));
				}
				stopPolling() {
					this.pollTimer &&
						(clearInterval(this.pollTimer), (this.pollTimer = null));
				}
				_addListener(e, t) {
					0 === Object.keys(this.listeners).length && this.startPolling(),
						this.listeners[e] ||
							((this.listeners[e] = new Set()), this._get(e)),
						this.listeners[e].add(t);
				}
				_removeListener(e, t) {
					this.listeners[e] &&
						(this.listeners[e].delete(t),
						0 === this.listeners[e].size && delete this.listeners[e]),
						0 === Object.keys(this.listeners).length && this.stopPolling();
				}
			}
			(tb.type = "LOCAL"), eS("rcb"), new E(3e4, 6e4);
			async function tA(e, t, r) {
				var i, n, s;
				const a = await r.verify();
				try {
					let o;
					if (
						(_("string" == typeof a, e, "argument-error"),
						_("recaptcha" === r.type, e, "argument-error"),
						(o = "string" == typeof t ? { phoneNumber: t } : t),
						"session" in o)
					) {
						const t = o.session;
						if ("phoneNumber" in o)
							return (
								_("enroll" === t.type, e, "internal-error"),
								(
									await ((n = {
										idToken: t.credential,
										phoneEnrollmentInfo: {
											phoneNumber: o.phoneNumber,
											recaptchaToken: a,
										},
									}),
									O(e, "POST", "/v2/accounts/mfaEnrollment:start", C(e, n)))
								).phoneSessionInfo.sessionInfo
							);
						{
							_("signin" === t.type, e, "internal-error");
							const r =
								(null === (i = o.multiFactorHint) || void 0 === i
									? void 0
									: i.uid) || o.multiFactorUid;
							return (
								_(r, e, "missing-multi-factor-info"),
								(
									await ((s = {
										mfaPendingCredential: t.credential,
										mfaEnrollmentId: r,
										phoneSignInInfo: { recaptchaToken: a },
									}),
									O(e, "POST", "/v2/accounts/mfaSignIn:start", C(e, s)))
								).phoneResponseInfo.sessionInfo
							);
						}
					}
					{
						const { sessionInfo: t } = await eW(e, {
							phoneNumber: o.phoneNumber,
							recaptchaToken: a,
						});
						return t;
					}
				} finally {
					r._reset();
				}
			}
			class tN {
				constructor(e) {
					(this.providerId = tN.PROVIDER_ID), (this.auth = eT(e));
				}
				verifyPhoneNumber(e, t) {
					return tA(this.auth, e, (0, n.Ku)(t));
				}
				static credential(e, t) {
					return eG._fromVerification(e, t);
				}
				static credentialFromResult(e) {
					return tN.credentialFromTaggedObject(e);
				}
				static credentialFromError(e) {
					return tN.credentialFromTaggedObject(e.customData || {});
				}
				static credentialFromTaggedObject({ _tokenResponse: e }) {
					if (!e) return null;
					const { phoneNumber: t, temporaryProof: r } = e;
					return t && r ? eG._fromTokenResponse(t, r) : null;
				}
			}
			function tL(e, t) {
				return t
					? ei(t)
					: (_(e._popupRedirectResolver, e, "argument-error"),
						e._popupRedirectResolver);
			}
			(tN.PROVIDER_ID = "phone"), (tN.PHONE_SIGN_IN_METHOD = "phone");
			class tU extends eA {
				constructor(e) {
					super("custom", "custom"), (this.params = e);
				}
				_getIdTokenResponse(e) {
					return ej(e, this._buildIdpRequest());
				}
				_linkToIdToken(e, t) {
					return ej(e, this._buildIdpRequest(t));
				}
				_getReauthenticationResolver(e) {
					return ej(e, this._buildIdpRequest());
				}
				_buildIdpRequest(e) {
					const t = {
						requestUri: this.params.requestUri,
						sessionId: this.params.sessionId,
						postBody: this.params.postBody,
						tenantId: this.params.tenantId,
						pendingToken: this.params.pendingToken,
						returnSecureToken: !0,
						returnIdpCredential: !0,
					};
					return e && (t.idToken = e), t;
				}
			}
			function tD(e) {
				return te(e.auth, new tU(e), e.bypassAuthState);
			}
			function tM(e) {
				const { auth: t, user: r } = e;
				return _(r, t, "internal-error"), e7(r, new tU(e), e.bypassAuthState);
			}
			async function tF(e) {
				const { auth: t, user: r } = e;
				return _(r, t, "internal-error"), e8(r, new tU(e), e.bypassAuthState);
			}
			class tx {
				constructor(e, t, r, i, n = !1) {
					(this.auth = e),
						(this.resolver = r),
						(this.user = i),
						(this.bypassAuthState = n),
						(this.pendingPromise = null),
						(this.eventManager = null),
						(this.filter = Array.isArray(t) ? t : [t]);
				}
				execute() {
					return new Promise(async (e, t) => {
						this.pendingPromise = { resolve: e, reject: t };
						try {
							(this.eventManager = await this.resolver._initialize(this.auth)),
								await this.onExecution(),
								this.eventManager.registerConsumer(this);
						} catch (e) {
							this.reject(e);
						}
					});
				}
				async onAuthEvent(e) {
					const {
						urlResponse: t,
						sessionId: r,
						postBody: i,
						tenantId: n,
						error: s,
						type: a,
					} = e;
					if (s) {
						this.reject(s);
						return;
					}
					const o = {
						auth: this.auth,
						requestUri: t,
						sessionId: r,
						tenantId: n || void 0,
						postBody: i || void 0,
						user: this.user,
						bypassAuthState: this.bypassAuthState,
					};
					try {
						this.resolve(await this.getIdpTask(a)(o));
					} catch (e) {
						this.reject(e);
					}
				}
				onError(e) {
					this.reject(e);
				}
				getIdpTask(e) {
					switch (e) {
						case "signInViaPopup":
						case "signInViaRedirect":
							return tD;
						case "linkViaPopup":
						case "linkViaRedirect":
							return tF;
						case "reauthViaPopup":
						case "reauthViaRedirect":
							return tM;
						default:
							d(this.auth, "internal-error");
					}
				}
				resolve(e) {
					var t, r;
					(t = this.pendingPromise),
						(r = "Pending promise was never set"),
						t || I(r),
						this.pendingPromise.resolve(e),
						this.unregisterAndCleanUp();
				}
				reject(e) {
					var t, r;
					(t = this.pendingPromise),
						(r = "Pending promise was never set"),
						t || I(r),
						this.pendingPromise.reject(e),
						this.unregisterAndCleanUp();
				}
				unregisterAndCleanUp() {
					this.eventManager && this.eventManager.unregisterConsumer(this),
						(this.pendingPromise = null),
						this.cleanUp();
				}
			}
			const tV = new E(2e3, 1e4);
			class tj extends tx {
				constructor(e, t, r, i, n) {
					super(e, t, i, n),
						(this.provider = r),
						(this.authWindow = null),
						(this.pollId = null),
						tj.currentPopupAction && tj.currentPopupAction.cancel(),
						(tj.currentPopupAction = this);
				}
				async executeNotNull() {
					const e = await this.execute();
					return _(e, this.auth, "internal-error"), e;
				}
				async onExecution() {
					var e, t;
					(e = 1 === this.filter.length),
						(t = "Popup operations only handle one event"),
						e || I(t);
					const r = tg();
					(this.authWindow = await this.resolver._openPopup(
						this.auth,
						this.provider,
						this.filter[0],
						r,
					)),
						(this.authWindow.associatedEvent = r),
						this.resolver._originValidation(this.auth).catch((e) => {
							this.reject(e);
						}),
						this.resolver._isIframeWebStorageSupported(this.auth, (e) => {
							e || this.reject(p(this.auth, "web-storage-unsupported"));
						}),
						this.pollUserCancellation();
				}
				get eventId() {
					var e;
					return (
						(null === (e = this.authWindow) || void 0 === e
							? void 0
							: e.associatedEvent) || null
					);
				}
				cancel() {
					this.reject(p(this.auth, "cancelled-popup-request"));
				}
				cleanUp() {
					this.authWindow && this.authWindow.close(),
						this.pollId && window.clearTimeout(this.pollId),
						(this.authWindow = null),
						(this.pollId = null),
						(tj.currentPopupAction = null);
				}
				pollUserCancellation() {
					const e = () => {
						var t, r;
						if (
							null ===
								(r =
									null === (t = this.authWindow) || void 0 === t
										? void 0
										: t.window) || void 0 === r
								? void 0
								: r.closed
						) {
							this.pollId = window.setTimeout(() => {
								(this.pollId = null),
									this.reject(p(this.auth, "popup-closed-by-user"));
							}, 8e3);
							return;
						}
						this.pollId = window.setTimeout(e, tV.get());
					};
					e();
				}
			}
			tj.currentPopupAction = null;
			const tH = new Map();
			class tW extends tx {
				constructor(e, t, r = !1) {
					super(
						e,
						[
							"signInViaRedirect",
							"linkViaRedirect",
							"reauthViaRedirect",
							"unknown",
						],
						t,
						void 0,
						r,
					),
						(this.eventId = null);
				}
				async execute() {
					let e = tH.get(this.auth._key());
					if (!e) {
						try {
							const t = (await tz(this.resolver, this.auth))
								? await super.execute()
								: null;
							e = () => Promise.resolve(t);
						} catch (t) {
							e = () => Promise.reject(t);
						}
						tH.set(this.auth._key(), e);
					}
					return (
						this.bypassAuthState ||
							tH.set(this.auth._key(), () => Promise.resolve(null)),
						e()
					);
				}
				async onAuthEvent(e) {
					if ("signInViaRedirect" === e.type) return super.onAuthEvent(e);
					if ("unknown" === e.type) {
						this.resolve(null);
						return;
					}
					if (e.eventId) {
						const t = await this.auth._redirectUserForId(e.eventId);
						if (t) return (this.user = t), super.onAuthEvent(e);
						this.resolve(null);
					}
				}
				async onExecution() {}
				cleanUp() {}
			}
			async function tz(e, t) {
				const r = tG(t),
					i = tq(e);
				if (!(await i._isAvailable())) return !1;
				const n = (await i._get(r)) === "true";
				return await i._remove(r), n;
			}
			async function tK(e, t) {
				return tq(e)._set(tG(t), "true");
			}
			function t$(e, t) {
				tH.set(e._key(), t);
			}
			function tq(e) {
				return ei(e._redirectPersistence);
			}
			function tG(e) {
				return es("pendingRedirect", e.config.apiKey, e.name);
			}
			async function tJ(e, t, r = !1) {
				if ((0, i.xZ)(e.app)) return Promise.reject(m(e));
				const n = eT(e),
					s = tL(n, t),
					a = new tW(n, s, r),
					o = await a.execute();
				return (
					o &&
						!r &&
						(delete o.user._redirectEventId,
						await n._persistUserIfCurrent(o.user),
						await n._setRedirectUser(null, t)),
					o
				);
			}
			async function tB(e) {
				const t = tg(`${e.uid}:::`);
				return (
					(e._redirectEventId = t),
					await e.auth._setRedirectUser(e),
					await e.auth._persistUserIfCurrent(e),
					t
				);
			}
			class tZ {
				constructor(e) {
					(this.auth = e),
						(this.cachedEventUids = new Set()),
						(this.consumers = new Set()),
						(this.queuedRedirectEvent = null),
						(this.hasHandledPotentialRedirect = !1),
						(this.lastProcessedEventTime = Date.now());
				}
				registerConsumer(e) {
					this.consumers.add(e),
						this.queuedRedirectEvent &&
							this.isEventForConsumer(this.queuedRedirectEvent, e) &&
							(this.sendToConsumer(this.queuedRedirectEvent, e),
							this.saveEventToCache(this.queuedRedirectEvent),
							(this.queuedRedirectEvent = null));
				}
				unregisterConsumer(e) {
					this.consumers.delete(e);
				}
				onEvent(e) {
					if (this.hasEventBeenHandled(e)) return !1;
					let t = !1;
					return (
						this.consumers.forEach((r) => {
							this.isEventForConsumer(e, r) &&
								((t = !0), this.sendToConsumer(e, r), this.saveEventToCache(e));
						}),
						this.hasHandledPotentialRedirect ||
							!((e) => {
								switch (e.type) {
									case "signInViaRedirect":
									case "linkViaRedirect":
									case "reauthViaRedirect":
										return !0;
									case "unknown":
										return tX(e);
									default:
										return !1;
								}
							})(e) ||
							((this.hasHandledPotentialRedirect = !0),
							t || ((this.queuedRedirectEvent = e), (t = !0))),
						t
					);
				}
				sendToConsumer(e, t) {
					var r;
					if (e.error && !tX(e)) {
						const i =
							(null === (r = e.error.code) || void 0 === r
								? void 0
								: r.split("auth/")[1]) || "internal-error";
						t.onError(p(this.auth, i));
					} else t.onAuthEvent(e);
				}
				isEventForConsumer(e, t) {
					const r =
						null === t.eventId || (!!e.eventId && e.eventId === t.eventId);
					return t.filter.includes(e.type) && r;
				}
				hasEventBeenHandled(e) {
					return (
						Date.now() - this.lastProcessedEventTime >= 6e5 &&
							this.cachedEventUids.clear(),
						this.cachedEventUids.has(tQ(e))
					);
				}
				saveEventToCache(e) {
					this.cachedEventUids.add(tQ(e)),
						(this.lastProcessedEventTime = Date.now());
				}
			}
			function tQ(e) {
				return [e.type, e.eventId, e.sessionId, e.tenantId]
					.filter((e) => e)
					.join("-");
			}
			function tX({ type: e, error: t }) {
				return (
					"unknown" === e &&
					(null == t ? void 0 : t.code) === "auth/no-auth-event"
				);
			}
			async function tY(e, t = {}) {
				return O(e, "GET", "/v1/projects", t);
			}
			const t0 = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,
				t1 = /^https?/;
			async function t3(e) {
				if (e.config.emulator) return;
				const { authorizedDomains: t } = await tY(e);
				for (const e of t)
					try {
						if (
							((e) => {
								const t = y(),
									{ protocol: r, hostname: i } = new URL(t);
								if (e.startsWith("chrome-extension://")) {
									const n = new URL(e);
									return "" === n.hostname && "" === i
										? "chrome-extension:" === r &&
												e.replace("chrome-extension://", "") ===
													t.replace("chrome-extension://", "")
										: "chrome-extension:" === r && n.hostname === i;
								}
								if (!t1.test(r)) return !1;
								if (t0.test(e)) return i === e;
								const n = e.replace(/\./g, "\\.");
								return RegExp("^(.+\\." + n + "|" + n + ")$", "i").test(i);
							})(e)
						)
							return;
					} catch (e) {}
				d(e, "unauthorized-domain");
			}
			const t2 = new E(3e4, 6e4);
			function t4() {
				const e = t_().___jsl;
				if (null == e ? void 0 : e.H) {
					for (const t of Object.keys(e.H))
						if (
							((e.H[t].r = e.H[t].r || []),
							(e.H[t].L = e.H[t].L || []),
							(e.H[t].r = [...e.H[t].L]),
							e.CP)
						)
							for (let t = 0; t < e.CP.length; t++) e.CP[t] = null;
				}
			}
			let t5 = null,
				t6 = new E(5e3, 15e3),
				t8 = {
					style: {
						position: "absolute",
						top: "-100px",
						width: "1px",
						height: "1px",
					},
					"aria-hidden": "true",
					tabindex: "-1",
				},
				t9 = new Map([
					["identitytoolkit.googleapis.com", "p"],
					["staging-identitytoolkit.sandbox.googleapis.com", "s"],
					["test-identitytoolkit.sandbox.googleapis.com", "t"],
				]);
			async function t7(e) {
				const t = await (t5 =
						t5 ||
						new Promise((t, r) => {
							var i, n, s, a;
							function o() {
								t4(),
									gapi.load("gapi.iframes", {
										callback: () => {
											t(gapi.iframes.getContext());
										},
										ontimeout: () => {
											t4(), r(p(e, "network-request-failed"));
										},
										timeout: t2.get(),
									});
							}
							if (
								null ===
									(n =
										null === (i = t_().gapi) || void 0 === i
											? void 0
											: i.iframes) || void 0 === n
									? void 0
									: n.Iframe
							)
								t(gapi.iframes.getContext());
							else if (
								null === (s = t_().gapi) || void 0 === s ? void 0 : s.load
							)
								o();
							else {
								const t = eS("iframefcb");
								return (
									(t_()[t] = () => {
										gapi.load ? o() : r(p(e, "network-request-failed"));
									}),
									((a = `${ek.gapiScript}?onload=${t}`), ek.loadJS(a)).catch(
										(e) => r(e),
									)
								);
							}
						}).catch((e) => {
							throw ((t5 = null), e);
						})),
					r = t_().gapi;
				return (
					_(r, e, "internal-error"),
					t.open(
						{
							where: document.body,
							url: ((e) => {
								const t = e.config;
								_(t.authDomain, e, "auth-domain-config-required");
								const r = t.emulator
										? k(t, "emulator/auth/iframe")
										: `https://${e.config.authDomain}/__/auth/iframe`,
									s = { apiKey: t.apiKey, appName: e.name, v: i.MF },
									a = t9.get(e.config.apiHost);
								a && (s.eid = a);
								const o = e._getFrameworks();
								return (
									o.length && (s.fw = o.join(",")),
									`${r}?${(0, n.Am)(s).slice(1)}`
								);
							})(e),
							messageHandlersFilter: r.iframes.CROSS_ORIGIN_IFRAMES_FILTER,
							attributes: t8,
							dontclear: !0,
						},
						(t) =>
							new Promise(async (r, i) => {
								await t.restyle({ setHideOnLeave: !1 });
								const n = p(e, "network-request-failed"),
									s = t_().setTimeout(() => {
										i(n);
									}, t6.get());
								function a() {
									t_().clearTimeout(s), r(t);
								}
								t.ping(a).then(a, () => {
									i(n);
								});
							}),
					)
				);
			}
			const re = {
				location: "yes",
				resizable: "yes",
				statusbar: "yes",
				toolbar: "no",
			};
			class rt {
				constructor(e) {
					(this.window = e), (this.associatedEvent = null);
				}
				close() {
					if (this.window)
						try {
							this.window.close();
						} catch (e) {}
				}
			}
			const rr = encodeURIComponent("fac");
			async function ri(e, t, r, s, a, o) {
				_(e.config.authDomain, e, "auth-domain-config-required"),
					_(e.config.apiKey, e, "invalid-api-key");
				const l = {
					apiKey: e.config.apiKey,
					appName: e.name,
					authType: r,
					redirectUrl: s,
					v: i.MF,
					eventId: a,
				};
				if (t instanceof eZ)
					for (const [r, i] of (t.setDefaultLanguage(e.languageCode),
					(l.providerId = t.providerId || ""),
					(0, n.Im)(t.getCustomParameters()) ||
						(l.customParameters = JSON.stringify(t.getCustomParameters())),
					Object.entries(o || {})))
						l[r] = i;
				if (t instanceof eQ) {
					const e = t.getScopes().filter((e) => "" !== e);
					e.length > 0 && (l.scopes = e.join(","));
				}
				for (const t of (e.tenantId && (l.tid = e.tenantId), Object.keys(l)))
					void 0 === l[t] && delete l[t];
				const c = await e._getAppCheckToken(),
					u = c ? `#${rr}=${encodeURIComponent(c)}` : "";
				return `${(({ config: e }) => (e.emulator ? k(e, "emulator/auth/handler") : `https://${e.authDomain}/__/auth/handler`))(e)}?${(0, n.Am)(l).slice(1)}${u}`;
			}
			const rn = "webStorageSupport";
			class rs {
				constructor() {
					(this.eventManagers = {}),
						(this.iframes = {}),
						(this.originValidationPromises = {}),
						(this._redirectPersistence = tf),
						(this._completeRedirectFn = tJ),
						(this._overrideRedirectResult = t$);
				}
				async _openPopup(e, t, r, i) {
					var s, a, o;
					(a =
						null === (s = this.eventManagers[e._key()]) || void 0 === s
							? void 0
							: s.manager),
						(o = "_initialize() not called before _openPopup()"),
						a || I(o);
					const l = await ri(e, t, r, y(), i);
					return ((e, t, r, i = 500, s = 600) => {
						let a = Math.max((window.screen.availHeight - s) / 2, 0).toString(),
							o = Math.max((window.screen.availWidth - i) / 2, 0).toString(),
							l = "",
							c = Object.assign(Object.assign({}, re), {
								width: i.toString(),
								height: s.toString(),
								top: a,
								left: o,
							}),
							u = (0, n.ZQ)().toLowerCase();
						r && (l = eu(u) ? "_blank" : r),
							el(u) && ((t = t || "http://localhost"), (c.scrollbars = "yes"));
						const h = Object.entries(c).reduce(
							(e, [t, r]) => `${e}${t}=${r},`,
							"",
						);
						if (
							((e = (0, n.ZQ)()) => {
								var t;
								return (
									em(e) &&
									!!(null === (t = window.navigator) || void 0 === t
										? void 0
										: t.standalone)
								);
							})(u) &&
							"_self" !== l
						)
							return (
								((e, t) => {
									const r = document.createElement("a");
									(r.href = e), (r.target = t);
									const i = document.createEvent("MouseEvent");
									i.initMouseEvent(
										"click",
										!0,
										!0,
										window,
										1,
										0,
										0,
										0,
										0,
										!1,
										!1,
										!1,
										!1,
										1,
										null,
									),
										r.dispatchEvent(i);
								})(t || "", l),
								new rt(null)
							);
						const d = window.open(t || "", l, h);
						_(d, e, "popup-blocked");
						try {
							d.focus();
						} catch (e) {}
						return new rt(d);
					})(e, l, tg());
				}
				async _openRedirect(e, t, r, i) {
					var n;
					return (
						await this._originValidation(e),
						(n = await ri(e, t, r, y(), i)),
						(t_().location.href = n),
						new Promise(() => {})
					);
				}
				_initialize(e) {
					const t = e._key();
					if (this.eventManagers[t]) {
						var r;
						const { manager: e, promise: i } = this.eventManagers[t];
						return e
							? Promise.resolve(e)
							: ((r = "If manager is not set, promise should be"),
								i || I(r),
								i);
					}
					const i = this.initAndGetManager(e);
					return (
						(this.eventManagers[t] = { promise: i }),
						i.catch(() => {
							delete this.eventManagers[t];
						}),
						i
					);
				}
				async initAndGetManager(e) {
					const t = await t7(e),
						r = new tZ(e);
					return (
						t.register(
							"authEvent",
							(t) => (
								_(null == t ? void 0 : t.authEvent, e, "invalid-auth-event"),
								{ status: r.onEvent(t.authEvent) ? "ACK" : "ERROR" }
							),
							gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,
						),
						(this.eventManagers[e._key()] = { manager: r }),
						(this.iframes[e._key()] = t),
						r
					);
				}
				_isIframeWebStorageSupported(e, t) {
					this.iframes[e._key()].send(
						rn,
						{ type: rn },
						(r) => {
							var i;
							const n =
								null === (i = null == r ? void 0 : r[0]) || void 0 === i
									? void 0
									: i[rn];
							void 0 !== n && t(!!n), d(e, "internal-error");
						},
						gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,
					);
				}
				_originValidation(e) {
					const t = e._key();
					return (
						this.originValidationPromises[t] ||
							(this.originValidationPromises[t] = t3(e)),
						this.originValidationPromises[t]
					);
				}
				get _shouldInitProactively() {
					return eg() || ec() || em();
				}
			}
			class ra {
				constructor(e) {
					this.factorId = e;
				}
				_process(e, t, r) {
					switch (t.type) {
						case "enroll":
							return this._finalizeEnroll(e, t.credential, r);
						case "signin":
							return this._finalizeSignIn(e, t.credential);
						default:
							return I("unexpected MultiFactorSessionType");
					}
				}
			}
			class ro extends ra {
				constructor(e) {
					super("phone"), (this.credential = e);
				}
				static _fromCredential(e) {
					return new ro(e);
				}
				_finalizeEnroll(e, t, r) {
					return O(
						e,
						"POST",
						"/v2/accounts/mfaEnrollment:finalize",
						C(e, {
							idToken: t,
							displayName: r,
							phoneVerificationInfo: this.credential._makeVerificationRequest(),
						}),
					);
				}
				_finalizeSignIn(e, t) {
					return O(
						e,
						"POST",
						"/v2/accounts/mfaSignIn:finalize",
						C(e, {
							mfaPendingCredential: t,
							phoneVerificationInfo: this.credential._makeVerificationRequest(),
						}),
					);
				}
			}
			class rl {
				constructor() {}
				static assertion(e) {
					return ro._fromCredential(e);
				}
			}
			rl.FACTOR_ID = "phone";
			class rc {
				static assertionForEnrollment(e, t) {
					return ru._fromSecret(e, t);
				}
				static assertionForSignIn(e, t) {
					return ru._fromEnrollmentId(e, t);
				}
				static async generateSecret(e) {
					var t, r;
					_(
						void 0 !==
							(null === (t = e.user) || void 0 === t ? void 0 : t.auth),
						"internal-error",
					);
					const i = await O(
						(r = e.user.auth),
						"POST",
						"/v2/accounts/mfaEnrollment:start",
						C(r, { idToken: e.credential, totpEnrollmentInfo: {} }),
					);
					return rh._fromStartTotpMfaEnrollmentResponse(i, e.user.auth);
				}
			}
			rc.FACTOR_ID = "totp";
			class ru extends ra {
				constructor(e, t, r) {
					super("totp"),
						(this.otp = e),
						(this.enrollmentId = t),
						(this.secret = r);
				}
				static _fromSecret(e, t) {
					return new ru(t, void 0, e);
				}
				static _fromEnrollmentId(e, t) {
					return new ru(t, e);
				}
				async _finalizeEnroll(e, t, r) {
					return (
						_(void 0 !== this.secret, e, "argument-error"),
						O(
							e,
							"POST",
							"/v2/accounts/mfaEnrollment:finalize",
							C(e, {
								idToken: t,
								displayName: r,
								totpVerificationInfo: this.secret._makeTotpVerificationInfo(
									this.otp,
								),
							}),
						)
					);
				}
				async _finalizeSignIn(e, t) {
					_(
						void 0 !== this.enrollmentId && void 0 !== this.otp,
						e,
						"argument-error",
					);
					const r = { verificationCode: this.otp };
					return O(
						e,
						"POST",
						"/v2/accounts/mfaSignIn:finalize",
						C(e, {
							mfaPendingCredential: t,
							mfaEnrollmentId: this.enrollmentId,
							totpVerificationInfo: r,
						}),
					);
				}
			}
			class rh {
				constructor(e, t, r, i, n, s, a) {
					(this.sessionInfo = s),
						(this.auth = a),
						(this.secretKey = e),
						(this.hashingAlgorithm = t),
						(this.codeLength = r),
						(this.codeIntervalSeconds = i),
						(this.enrollmentCompletionDeadline = n);
				}
				static _fromStartTotpMfaEnrollmentResponse(e, t) {
					return new rh(
						e.totpSessionInfo.sharedSecretKey,
						e.totpSessionInfo.hashingAlgorithm,
						e.totpSessionInfo.verificationCodeLength,
						e.totpSessionInfo.periodSec,
						new Date(e.totpSessionInfo.finalizeEnrollmentTime).toUTCString(),
						e.totpSessionInfo.sessionInfo,
						t,
					);
				}
				_makeTotpVerificationInfo(e) {
					return { sessionInfo: this.sessionInfo, verificationCode: e };
				}
				generateQrCodeUrl(e, t) {
					var r;
					let i = !1;
					return (
						(rd(e) || rd(t)) && (i = !0),
						i &&
							(rd(e) &&
								(e =
									(null === (r = this.auth.currentUser) || void 0 === r
										? void 0
										: r.email) || "unknownuser"),
							rd(t) && (t = this.auth.name)),
						`otpauth://totp/${t}:${e}?secret=${this.secretKey}&issuer=${t}&algorithm=${this.hashingAlgorithm}&digits=${this.codeLength}`
					);
				}
			}
			function rd(e) {
				return void 0 === e || (null == e ? void 0 : e.length) === 0;
			}
			var rp = "@firebase/auth",
				rf = "1.7.9";
			class rm {
				constructor(e) {
					(this.auth = e), (this.internalListeners = new Map());
				}
				getUid() {
					var e;
					return (
						this.assertAuthConfigured(),
						(null === (e = this.auth.currentUser) || void 0 === e
							? void 0
							: e.uid) || null
					);
				}
				async getToken(e) {
					return (this.assertAuthConfigured(),
					await this.auth._initializationPromise,
					this.auth.currentUser)
						? { accessToken: await this.auth.currentUser.getIdToken(e) }
						: null;
				}
				addAuthTokenListener(e) {
					if ((this.assertAuthConfigured(), this.internalListeners.has(e)))
						return;
					const t = this.auth.onIdTokenChanged((t) => {
						e((null == t ? void 0 : t.stsTokenManager.accessToken) || null);
					});
					this.internalListeners.set(e, t), this.updateProactiveRefresh();
				}
				removeAuthTokenListener(e) {
					this.assertAuthConfigured();
					const t = this.internalListeners.get(e);
					t &&
						(this.internalListeners.delete(e),
						t(),
						this.updateProactiveRefresh());
				}
				assertAuthConfigured() {
					_(
						this.auth._initializationPromise,
						"dependent-sdk-initialized-before-auth",
					);
				}
				updateProactiveRefresh() {
					this.internalListeners.size > 0
						? this.auth._startProactiveRefresh()
						: this.auth._stopProactiveRefresh();
				}
			}
			let rg = (0, n.XA)("authIdTokenMaxAge") || 300,
				rv = null,
				r_ = (e) => async (t) => {
					const r = t && (await t.getIdTokenResult()),
						i = r && (new Date().getTime() - Date.parse(r.issuedAtTime)) / 1e3;
					if (i && i > rg) return;
					const n = null == r ? void 0 : r.token;
					rv !== n &&
						((rv = n),
						await fetch(e, {
							method: n ? "POST" : "DELETE",
							headers: n ? { Authorization: `Bearer ${n}` } : {},
						}));
				};
			function rI(e = (0, i.Sx)()) {
				const t = (0, i.j6)(e, "auth");
				if (t.isInitialized()) return t.getImmediate();
				const r = ((e, t) => {
						const r = (0, i.j6)(e, "auth");
						if (r.isInitialized()) {
							const e = r.getImmediate(),
								i = r.getOptions();
							if ((0, n.bD)(i, null != t ? t : {})) return e;
							d(e, "already-initialized");
						}
						return r.initialize({ options: t });
					})(e, { popupRedirectResolver: rs, persistence: [tb, tp, tf] }),
					s = (0, n.XA)("authTokenSyncURL");
				if (s && "boolean" == typeof isSecureContext && isSecureContext) {
					const e = new URL(s, location.origin);
					if (location.origin === e.origin) {
						const t = r_(e.toString());
						(0, n.Ku)(r).beforeAuthStateChanged(t, () => t(r.currentUser)),
							(0, n.Ku)(r).onIdTokenChanged((e) => t(e), void 0, void 0);
					}
				}
				const a = (0, n.Tj)("auth");
				return (
					a &&
						((e, t, r) => {
							const i = eT(e);
							_(i._canInitEmulator, i, "emulator-config-failed"),
								_(/^https?:\/\//.test(t), i, "invalid-emulator-scheme");
							const n = eO(t),
								{ host: s, port: a } = ((e) => {
									const t = eO(e),
										r = /(\/\/)?([^?#/]+)/.exec(e.substr(t.length));
									if (!r) return { host: "", port: null };
									const i = r[2].split("@").pop() || "",
										n = /^(\[[^\]]+\])(:|$)/.exec(i);
									if (n) {
										const e = n[1];
										return { host: e, port: eb(i.substr(e.length + 1)) };
									}
									{
										const [e, t] = i.split(":");
										return { host: e, port: eb(t) };
									}
								})(t),
								o = null === a ? "" : `:${a}`;
							(i.config.emulator = { url: `${n}//${s}${o}/` }),
								(i.settings.appVerificationDisabledForTesting = !0),
								(i.emulatorConfig = Object.freeze({
									host: s,
									port: a,
									protocol: n.replace(":", ""),
									options: Object.freeze({ disableWarnings: !1 }),
								})),
								(() => {
									function e() {
										const e = document.createElement("p"),
											t = e.style;
										(e.innerText =
											"Running in emulator mode. Do not use with production credentials."),
											(t.position = "fixed"),
											(t.width = "100%"),
											(t.backgroundColor = "#ffffff"),
											(t.border = ".1em solid #000000"),
											(t.color = "#b50000"),
											(t.bottom = "0px"),
											(t.left = "0px"),
											(t.margin = "0px"),
											(t.zIndex = "10000"),
											(t.textAlign = "center"),
											e.classList.add("firebase-emulator-warning"),
											document.body.appendChild(e);
									}
									"undefined" != typeof console &&
										"function" == typeof console.info &&
										console.info(
											"WARNING: You are using the Auth Emulator, which is intended for local testing only.  Do not use with production credentials.",
										),
										"undefined" != typeof window &&
											"undefined" != typeof document &&
											("loading" === document.readyState
												? window.addEventListener("DOMContentLoaded", e)
												: e());
								})();
						})(r, `http://${a}`),
					r
				);
			}
			(ek = {
				loadJS: (e) =>
					new Promise((t, r) => {
						const i = document.createElement("script");
						i.setAttribute("src", e),
							(i.onload = t),
							(i.onerror = (e) => {
								const t = p("internal-error");
								(t.customData = e), r(t);
							}),
							(i.type = "text/javascript"),
							(i.charset = "UTF-8"),
							(() => {
								var e, t;
								return null !==
									(t =
										null === (e = document.getElementsByTagName("head")) ||
										void 0 === e
											? void 0
											: e[0]) && void 0 !== t
									? t
									: document;
							})().appendChild(i);
					}),
				gapiScript: "https://apis.google.com/js/api.js",
				recaptchaV2Script: "https://www.google.com/recaptcha/api.js",
				recaptchaEnterpriseScript:
					"https://www.google.com/recaptcha/enterprise.js?render=",
			}),
				((e) => {
					(0, i.om)(
						new o.uA(
							"auth",
							(t, { options: r }) => {
								const i = t.getProvider("app").getImmediate(),
									n = t.getProvider("heartbeat"),
									s = t.getProvider("app-check-internal"),
									{ apiKey: a, authDomain: o } = i.options;
								_(a && !a.includes(":"), "invalid-api-key", {
									appName: i.name,
								});
								const l = new ey(i, n, s, {
									apiKey: a,
									authDomain: o,
									clientPlatform: e,
									apiHost: "identitytoolkit.googleapis.com",
									tokenApiHost: "securetoken.googleapis.com",
									apiScheme: "https",
									sdkClientVersion: ev(e),
								});
								return (
									((e, t) => {
										const r = (null == t ? void 0 : t.persistence) || [],
											i = (Array.isArray(r) ? r : [r]).map(ei);
										(null == t ? void 0 : t.errorMap) &&
											e._updateErrorMap(t.errorMap),
											e._initializeWithPersistence(
												i,
												null == t ? void 0 : t.popupRedirectResolver,
											);
									})(l, r),
									l
								);
							},
							"PUBLIC",
						)
							.setInstantiationMode("EXPLICIT")
							.setInstanceCreatedCallback((e, t, r) => {
								e.getProvider("auth-internal").initialize();
							}),
					),
						(0, i.om)(
							new o.uA(
								"auth-internal",
								(e) => new rm(eT(e.getProvider("auth").getImmediate())),
								"PRIVATE",
							).setInstantiationMode("EXPLICIT"),
						),
						(0, i.KO)(
							rp,
							rf,
							((e) => {
								switch (e) {
									case "Node":
										return "node";
									case "ReactNative":
										return "rn";
									case "Worker":
										return "webworker";
									case "Cordova":
										return "cordova";
									case "WebExtension":
										return "web-extension";
									default:
										return;
								}
							})(e),
						),
						(0, i.KO)(rp, rf, "esm2017");
				})("Browser");
		},
	},
]);
