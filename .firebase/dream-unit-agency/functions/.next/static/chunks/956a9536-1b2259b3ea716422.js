(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[315],
	{
		8693: (e, t, n) => {
			let i, r, s, o, l, a, h, u, c, d;
			n.d(t, {
				M4: () => iz,
				U0: () => iB,
				X1: () => iV,
				Zy: () => ij,
				_O: () => iY,
			});
			var _,
				p,
				f = n(8339),
				g = n(8101),
				m = n(854),
				y = n(1176),
				v = n(5036);
			let C = "@firebase/database",
				w = "1.0.8",
				T = "";
			class I {
				constructor(e) {
					(this.domStorage_ = e), (this.prefix_ = "firebase:");
				}
				set(e, t) {
					null == t
						? this.domStorage_.removeItem(this.prefixedName_(e))
						: this.domStorage_.setItem(this.prefixedName_(e), (0, m.As)(t));
				}
				get(e) {
					const t = this.domStorage_.getItem(this.prefixedName_(e));
					return null == t ? null : (0, m.$L)(t);
				}
				remove(e) {
					this.domStorage_.removeItem(this.prefixedName_(e));
				}
				prefixedName_(e) {
					return this.prefix_ + e;
				}
				toString() {
					return this.domStorage_.toString();
				}
			}
			class b {
				constructor() {
					(this.cache_ = {}), (this.isInMemoryStorage = !0);
				}
				set(e, t) {
					null == t ? delete this.cache_[e] : (this.cache_[e] = t);
				}
				get(e) {
					return (0, m.gR)(this.cache_, e) ? this.cache_[e] : null;
				}
				remove(e) {
					delete this.cache_[e];
				}
			}
			let E = (e) => {
					try {
						if ("undefined" != typeof window && void 0 !== window[e]) {
							const t = window[e];
							return (
								t.setItem("firebase:sentinel", "cache"),
								t.removeItem("firebase:sentinel"),
								new I(t)
							);
						}
					} catch (e) {}
					return new b();
				},
				k = E("localStorage"),
				S = E("sessionStorage"),
				N = new y.Vy("@firebase/database"),
				x = (() => {
					let e = 1;
					return () => e++;
				})(),
				P = (e) => {
					const t = (0, m.kj)(e),
						n = new m.gz();
					n.update(t);
					const i = n.digest();
					return m.K3.encodeByteArray(i);
				},
				R = (...e) => {
					let t = "";
					for (let n = 0; n < e.length; n++) {
						const i = e[n];
						Array.isArray(i) ||
						(i && "object" == typeof i && "number" == typeof i.length)
							? (t += R.apply(null, i))
							: "object" == typeof i
								? (t += (0, m.As)(i))
								: (t += i),
							(t += " ");
					}
					return t;
				},
				A = null,
				D = !0,
				F = (e, t) => {
					(0, m.vA)(
						!t || !0 === e || !1 === e,
						"Can't turn on custom loggers persistently.",
					),
						!0 === e
							? ((N.logLevel = y.$b.VERBOSE),
								(A = N.log.bind(N)),
								t && S.set("logging_enabled", !0))
							: "function" == typeof e
								? (A = e)
								: ((A = null), S.remove("logging_enabled"));
				},
				M = (...e) => {
					if (
						(!0 === D &&
							((D = !1),
							null === A && !0 === S.get("logging_enabled") && F(!0)),
						A)
					) {
						const t = R.apply(null, e);
						A(t);
					}
				},
				q =
					(e) =>
					(...t) => {
						M(e, ...t);
					},
				L = (...e) => {
					const t = "FIREBASE INTERNAL ERROR: " + R(...e);
					N.error(t);
				},
				O = (...e) => {
					const t = `FIREBASE FATAL ERROR: ${R(...e)}`;
					throw (N.error(t), Error(t));
				},
				W = (...e) => {
					const t = "FIREBASE WARNING: " + R(...e);
					N.warn(t);
				},
				U = () => {
					"undefined" != typeof window &&
						window.location &&
						window.location.protocol &&
						-1 !== window.location.protocol.indexOf("https:") &&
						W(
							"Insecure Firebase access from a secure page. Please use https in calls to new Firebase().",
						);
				},
				H = (e) =>
					"number" == typeof e &&
					(e != e ||
						e === Number.POSITIVE_INFINITY ||
						e === Number.NEGATIVE_INFINITY),
				j = (e) => {
					if ((0, m.$g)() || "complete" === document.readyState) e();
					else {
						let t = !1,
							n = () => {
								if (!document.body) {
									setTimeout(n, Math.floor(10));
									return;
								}
								t || ((t = !0), e());
							};
						document.addEventListener
							? (document.addEventListener("DOMContentLoaded", n, !1),
								window.addEventListener("load", n, !1))
							: document.attachEvent &&
								(document.attachEvent("onreadystatechange", () => {
									"complete" === document.readyState && n();
								}),
								window.attachEvent("onload", n));
					}
				},
				Y = "[MIN_NAME]",
				z = "[MAX_NAME]",
				V = (e, t) => {
					if (e === t) return 0;
					if (e === Y || t === z) return -1;
					if (t === Y || e === z) return 1;
					{
						const n = Z(e),
							i = Z(t);
						return null !== n
							? null !== i
								? n - i == 0
									? e.length - t.length
									: n - i
								: -1
							: null !== i
								? 1
								: e < t
									? -1
									: 1;
					}
				},
				B = (e, t) => (e === t ? 0 : e < t ? -1 : 1),
				K = (e, t) => {
					if (t && e in t) return t[e];
					throw Error(
						"Missing required key (" + e + ") in object: " + (0, m.As)(t),
					);
				},
				$ = (e) => {
					if ("object" != typeof e || null === e) return (0, m.As)(e);
					const t = [];
					for (const n in e) t.push(n);
					t.sort();
					let n = "{";
					for (let i = 0; i < t.length; i++)
						0 !== i && (n += ","),
							(n += (0, m.As)(t[i])),
							(n += ":"),
							(n += $(e[t[i]]));
					return n + "}";
				},
				G = (e, t) => {
					const n = e.length;
					if (n <= t) return [e];
					const i = [];
					for (let r = 0; r < n; r += t)
						r + t > n
							? i.push(e.substring(r, n))
							: i.push(e.substring(r, r + t));
					return i;
				};
			function Q(e, t) {
				for (const n in e) e.hasOwnProperty(n) && t(n, e[n]);
			}
			const X = (e) => {
					let t, n, i, r, s;
					(0, m.vA)(!H(e), "Invalid JSON number");
					0 === e
						? ((n = 0), (i = 0), (t = +(1 / e == -1 / 0)))
						: ((t = e < 0),
							(e = Math.abs(e)) >= 22250738585072014e-324
								? ((n =
										(r = Math.min(Math.floor(Math.log(e) / Math.LN2), 1023)) +
										1023),
									(i = Math.round(e * Math.pow(2, 52 - r) - 0x10000000000000)))
								: ((n = 0), (i = Math.round(e / 5e-324))));
					const o = [];
					for (s = 52; s; s -= 1)
						o.push(i % 2 ? 1 : 0), (i = Math.floor(i / 2));
					for (s = 11; s; s -= 1)
						o.push(n % 2 ? 1 : 0), (n = Math.floor(n / 2));
					o.push(+!!t), o.reverse();
					let l = o.join(""),
						a = "";
					for (s = 0; s < 64; s += 8) {
						let e = Number.parseInt(l.substr(s, 8), 2).toString(16);
						1 === e.length && (e = "0" + e), (a += e);
					}
					return a.toLowerCase();
				},
				J = /^-?(0*)\d{1,10}$/,
				Z = (e) => {
					if (J.test(e)) {
						const t = Number(e);
						if (t >= -0x80000000 && t <= 0x7fffffff) return t;
					}
					return null;
				},
				ee = (e) => {
					try {
						e();
					} catch (e) {
						setTimeout(() => {
							throw (
								(W("Exception was thrown by user callback.", e.stack || ""), e)
							);
						}, Math.floor(0));
					}
				},
				et = (e, t) => {
					const n = setTimeout(e, t);
					return (
						"number" == typeof n &&
						"undefined" != typeof Deno &&
						Deno.unrefTimer
							? Deno.unrefTimer(n)
							: "object" == typeof n && n.unref && n.unref(),
						n
					);
				};
			class en {
				constructor(e, t) {
					(this.appName_ = e),
						(this.appCheckProvider = t),
						(this.appCheck =
							null == t ? void 0 : t.getImmediate({ optional: !0 })),
						this.appCheck ||
							null == t ||
							t.get().then((e) => (this.appCheck = e));
				}
				getToken(e) {
					return this.appCheck
						? this.appCheck.getToken(e)
						: new Promise((t, n) => {
								setTimeout(() => {
									this.appCheck ? this.getToken(e).then(t, n) : t(null);
								}, 0);
							});
				}
				addTokenChangeListener(e) {
					var t;
					null === (t = this.appCheckProvider) ||
						void 0 === t ||
						t.get().then((t) => t.addTokenListener(e));
				}
				notifyForInvalidToken() {
					W(
						`Provided AppCheck credentials for the app named "${this.appName_}" are invalid. This usually indicates your app was not initialized correctly.`,
					);
				}
			}
			class ei {
				constructor(e, t, n) {
					(this.appName_ = e),
						(this.firebaseOptions_ = t),
						(this.authProvider_ = n),
						(this.auth_ = null),
						(this.auth_ = n.getImmediate({ optional: !0 })),
						this.auth_ || n.onInit((e) => (this.auth_ = e));
				}
				getToken(e) {
					return this.auth_
						? this.auth_
								.getToken(e)
								.catch((e) =>
									e && "auth/token-not-initialized" === e.code
										? (M(
												"Got auth/token-not-initialized error.  Treating as null token.",
											),
											null)
										: Promise.reject(e),
								)
						: new Promise((t, n) => {
								setTimeout(() => {
									this.auth_ ? this.getToken(e).then(t, n) : t(null);
								}, 0);
							});
				}
				addTokenChangeListener(e) {
					this.auth_
						? this.auth_.addAuthTokenListener(e)
						: this.authProvider_.get().then((t) => t.addAuthTokenListener(e));
				}
				removeTokenChangeListener(e) {
					this.authProvider_.get().then((t) => t.removeAuthTokenListener(e));
				}
				notifyForInvalidToken() {
					let e =
						'Provided authentication credentials for the app named "' +
						this.appName_ +
						'" are invalid. This usually indicates your app was not initialized correctly. ';
					"credential" in this.firebaseOptions_
						? (e +=
								'Make sure the "credential" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.')
						: "serviceAccount" in this.firebaseOptions_
							? (e +=
									'Make sure the "serviceAccount" property provided to initializeApp() is authorized to access the specified "databaseURL" and is from the correct project.')
							: (e +=
									'Make sure the "apiKey" and "databaseURL" properties provided to initializeApp() match the values provided for your app at https://console.firebase.google.com/.'),
						W(e);
				}
			}
			class er {
				constructor(e) {
					this.accessToken = e;
				}
				getToken(e) {
					return Promise.resolve({ accessToken: this.accessToken });
				}
				addTokenChangeListener(e) {
					e(this.accessToken);
				}
				removeTokenChangeListener(e) {}
				notifyForInvalidToken() {}
			}
			er.OWNER = "owner";
			const es =
					/(console\.firebase|firebase-console-\w+\.corp|firebase\.corp)\.google\.com/,
				eo = "websocket",
				el = "long_polling";
			class ea {
				constructor(e, t, n, i, r = !1, s = "", o = !1, l = !1) {
					(this.secure = t),
						(this.namespace = n),
						(this.webSocketOnly = i),
						(this.nodeAdmin = r),
						(this.persistenceKey = s),
						(this.includeNamespaceInQueryParams = o),
						(this.isUsingEmulator = l),
						(this._host = e.toLowerCase()),
						(this._domain = this._host.substr(this._host.indexOf(".") + 1)),
						(this.internalHost = k.get("host:" + e) || this._host);
				}
				isCacheableHost() {
					return "s-" === this.internalHost.substr(0, 2);
				}
				isCustomHost() {
					return (
						"firebaseio.com" !== this._domain &&
						"firebaseio-demo.com" !== this._domain
					);
				}
				get host() {
					return this._host;
				}
				set host(e) {
					e !== this.internalHost &&
						((this.internalHost = e),
						this.isCacheableHost() &&
							k.set("host:" + this._host, this.internalHost));
				}
				toString() {
					let e = this.toURLString();
					return (
						this.persistenceKey && (e += "<" + this.persistenceKey + ">"), e
					);
				}
				toURLString() {
					const e = this.secure ? "https://" : "http://",
						t = this.includeNamespaceInQueryParams
							? `?ns=${this.namespace}`
							: "";
					return `${e}${this.host}/${t}`;
				}
			}
			function eh(e, t, n) {
				let i;
				if (
					((0, m.vA)("string" == typeof t, "typeof type must == string"),
					(0, m.vA)("object" == typeof n, "typeof params must == object"),
					t === eo)
				)
					i = (e.secure ? "wss://" : "ws://") + e.internalHost + "/.ws?";
				else if (t === el)
					i = (e.secure ? "https://" : "http://") + e.internalHost + "/.lp?";
				else throw Error("Unknown connection type: " + t);
				(e.host !== e.internalHost ||
					e.isCustomHost() ||
					e.includeNamespaceInQueryParams) &&
					(n.ns = e.namespace);
				const r = [];
				return (
					Q(n, (e, t) => {
						r.push(e + "=" + t);
					}),
					i + r.join("&")
				);
			}
			class eu {
				constructor() {
					this.counters_ = {};
				}
				incrementCounter(e, t = 1) {
					(0, m.gR)(this.counters_, e) || (this.counters_[e] = 0),
						(this.counters_[e] += t);
				}
				get() {
					return (0, m.A4)(this.counters_);
				}
			}
			const ec = {},
				ed = {};
			function e_(e) {
				const t = e.toString();
				return ec[t] || (ec[t] = new eu()), ec[t];
			}
			class ep {
				constructor(e) {
					(this.onMessage_ = e),
						(this.pendingResponses = []),
						(this.currentResponseNum = 0),
						(this.closeAfterResponse = -1),
						(this.onClose = null);
				}
				closeAfter(e, t) {
					(this.closeAfterResponse = e),
						(this.onClose = t),
						this.closeAfterResponse < this.currentResponseNum &&
							(this.onClose(), (this.onClose = null));
				}
				handleResponse(e, t) {
					for (
						this.pendingResponses[e] = t;
						this.pendingResponses[this.currentResponseNum];
					) {
						const e = this.pendingResponses[this.currentResponseNum];
						delete this.pendingResponses[this.currentResponseNum];
						for (let t = 0; t < e.length; ++t)
							e[t] &&
								ee(() => {
									this.onMessage_(e[t]);
								});
						if (this.currentResponseNum === this.closeAfterResponse) {
							this.onClose && (this.onClose(), (this.onClose = null));
							break;
						}
						this.currentResponseNum++;
					}
				}
			}
			const ef = "start";
			class eg {
				constructor(e, t, n, i, r, s, o) {
					(this.connId = e),
						(this.repoInfo = t),
						(this.applicationId = n),
						(this.appCheckToken = i),
						(this.authToken = r),
						(this.transportSessionId = s),
						(this.lastSessionId = o),
						(this.bytesSent = 0),
						(this.bytesReceived = 0),
						(this.everConnected_ = !1),
						(this.log_ = q(e)),
						(this.stats_ = e_(t)),
						(this.urlFn = (e) => (
							this.appCheckToken && (e.ac = this.appCheckToken), eh(t, el, e)
						));
				}
				open(e, t) {
					(this.curSegmentNum = 0),
						(this.onDisconnect_ = t),
						(this.myPacketOrderer = new ep(e)),
						(this.isClosed_ = !1),
						(this.connectTimeoutTimer_ = setTimeout(() => {
							this.log_("Timed out trying to connect."),
								this.onClosed_(),
								(this.connectTimeoutTimer_ = null);
						}, Math.floor(3e4))),
						j(() => {
							if (this.isClosed_) return;
							this.scriptTagHolder = new em(
								(...e) => {
									const [t, n, i, r, s] = e;
									if ((this.incrementIncomingBytes_(e), this.scriptTagHolder)) {
										if (
											(this.connectTimeoutTimer_ &&
												(clearTimeout(this.connectTimeoutTimer_),
												(this.connectTimeoutTimer_ = null)),
											(this.everConnected_ = !0),
											t === ef)
										)
											(this.id = n), (this.password = i);
										else if ("close" === t)
											n
												? ((this.scriptTagHolder.sendNewPolls = !1),
													this.myPacketOrderer.closeAfter(n, () => {
														this.onClosed_();
													}))
												: this.onClosed_();
										else throw Error("Unrecognized command received: " + t);
									}
								},
								(...e) => {
									const [t, n] = e;
									this.incrementIncomingBytes_(e),
										this.myPacketOrderer.handleResponse(t, n);
								},
								() => {
									this.onClosed_();
								},
								this.urlFn,
							);
							const e = {};
							(e[ef] = "t"),
								(e.ser = Math.floor(1e8 * Math.random())),
								this.scriptTagHolder.uniqueCallbackIdentifier &&
									(e.cb = this.scriptTagHolder.uniqueCallbackIdentifier),
								(e.v = "5"),
								this.transportSessionId && (e.s = this.transportSessionId),
								this.lastSessionId && (e.ls = this.lastSessionId),
								this.applicationId && (e.p = this.applicationId),
								this.appCheckToken && (e.ac = this.appCheckToken),
								"undefined" != typeof location &&
									location.hostname &&
									es.test(location.hostname) &&
									(e.r = "f");
							const t = this.urlFn(e);
							this.log_("Connecting via long-poll to " + t),
								this.scriptTagHolder.addTag(t, () => {});
						});
				}
				start() {
					this.scriptTagHolder.startLongPoll(this.id, this.password),
						this.addDisconnectPingFrame(this.id, this.password);
				}
				static forceAllow() {
					eg.forceAllow_ = !0;
				}
				static forceDisallow() {
					eg.forceDisallow_ = !0;
				}
				static isAvailable() {
					return (
						!(0, m.$g)() &&
						(!!eg.forceAllow_ ||
							(!eg.forceDisallow_ &&
								"undefined" != typeof document &&
								null != document.createElement &&
								!(
									"object" == typeof window &&
									window.chrome &&
									window.chrome.extension &&
									!/^chrome/.test(window.location.href)
								) &&
								("object" != typeof Windows || "object" != typeof Windows.UI)))
					);
				}
				markConnectionHealthy() {}
				shutdown_() {
					(this.isClosed_ = !0),
						this.scriptTagHolder &&
							(this.scriptTagHolder.close(), (this.scriptTagHolder = null)),
						this.myDisconnFrame &&
							(document.body.removeChild(this.myDisconnFrame),
							(this.myDisconnFrame = null)),
						this.connectTimeoutTimer_ &&
							(clearTimeout(this.connectTimeoutTimer_),
							(this.connectTimeoutTimer_ = null));
				}
				onClosed_() {
					!this.isClosed_ &&
						(this.log_("Longpoll is closing itself"),
						this.shutdown_(),
						this.onDisconnect_ &&
							(this.onDisconnect_(this.everConnected_),
							(this.onDisconnect_ = null)));
				}
				close() {
					this.isClosed_ ||
						(this.log_("Longpoll is being closed."), this.shutdown_());
				}
				send(e) {
					const t = (0, m.As)(e);
					(this.bytesSent += t.length),
						this.stats_.incrementCounter("bytes_sent", t.length);
					const n = G((0, m.KA)(t), 1840);
					for (let e = 0; e < n.length; e++)
						this.scriptTagHolder.enqueueSegment(
							this.curSegmentNum,
							n.length,
							n[e],
						),
							this.curSegmentNum++;
				}
				addDisconnectPingFrame(e, t) {
					if ((0, m.$g)()) return;
					this.myDisconnFrame = document.createElement("iframe");
					const n = {};
					(n.dframe = "t"),
						(n.id = e),
						(n.pw = t),
						(this.myDisconnFrame.src = this.urlFn(n)),
						(this.myDisconnFrame.style.display = "none"),
						document.body.appendChild(this.myDisconnFrame);
				}
				incrementIncomingBytes_(e) {
					const t = (0, m.As)(e).length;
					(this.bytesReceived += t),
						this.stats_.incrementCounter("bytes_received", t);
				}
			}
			class em {
				constructor(e, t, n, i) {
					if (
						((this.onDisconnect = n),
						(this.urlFn = i),
						(this.outstandingRequests = new Set()),
						(this.pendingSegs = []),
						(this.currentSerial = Math.floor(1e8 * Math.random())),
						(this.sendNewPolls = !0),
						(0, m.$g)())
					)
						(this.commandCB = e), (this.onMessageCB = t);
					else {
						(this.uniqueCallbackIdentifier = x()),
							(window["pLPCommand" + this.uniqueCallbackIdentifier] = e),
							(window["pRTLPCB" + this.uniqueCallbackIdentifier] = t),
							(this.myIFrame = em.createIFrame_());
						let n = "";
						this.myIFrame.src &&
							"javascript:" === this.myIFrame.src.substr(0, 11) &&
							(n =
								'<script>document.domain="' + document.domain + '";</script>');
						const i = "<html><body>" + n + "</body></html>";
						try {
							this.myIFrame.doc.open(),
								this.myIFrame.doc.write(i),
								this.myIFrame.doc.close();
						} catch (e) {
							M("frame writing exception"), e.stack && M(e.stack), M(e);
						}
					}
				}
				static createIFrame_() {
					const e = document.createElement("iframe");
					if (((e.style.display = "none"), document.body)) {
						document.body.appendChild(e);
						try {
							e.contentWindow.document || M("No IE domain setting required");
						} catch (t) {
							e.src =
								"javascript:void((function(){document.open();document.domain='" +
								document.domain +
								"';document.close();})())";
						}
					} else
						throw "Document body has not initialized. Wait to initialize Firebase until after the document is ready.";
					return (
						e.contentDocument
							? (e.doc = e.contentDocument)
							: e.contentWindow
								? (e.doc = e.contentWindow.document)
								: e.document && (e.doc = e.document),
						e
					);
				}
				close() {
					(this.alive = !1),
						this.myIFrame &&
							((this.myIFrame.doc.body.textContent = ""),
							setTimeout(() => {
								null !== this.myIFrame &&
									(document.body.removeChild(this.myIFrame),
									(this.myIFrame = null));
							}, Math.floor(0)));
					const e = this.onDisconnect;
					e && ((this.onDisconnect = null), e());
				}
				startLongPoll(e, t) {
					for (
						this.myID = e, this.myPW = t, this.alive = !0;
						this.newRequest_();
					);
				}
				newRequest_() {
					if (
						!this.alive ||
						!this.sendNewPolls ||
						!(
							this.outstandingRequests.size <
							(this.pendingSegs.length > 0 ? 2 : 1)
						)
					)
						return !1;
					{
						this.currentSerial++;
						const e = {};
						(e.id = this.myID),
							(e.pw = this.myPW),
							(e.ser = this.currentSerial);
						let t = this.urlFn(e),
							n = "",
							i = 0;
						while (this.pendingSegs.length > 0)
							if (this.pendingSegs[0].d.length + 30 + n.length <= 1870) {
								const e = this.pendingSegs.shift();
								(n =
									n +
									"&seg" +
									i +
									"=" +
									e.seg +
									"&ts" +
									i +
									"=" +
									e.ts +
									"&d" +
									i +
									"=" +
									e.d),
									i++;
							} else break;
						return (t += n), this.addLongPollTag_(t, this.currentSerial), !0;
					}
				}
				enqueueSegment(e, t, n) {
					this.pendingSegs.push({ seg: e, ts: t, d: n }),
						this.alive && this.newRequest_();
				}
				addLongPollTag_(e, t) {
					this.outstandingRequests.add(t);
					const n = () => {
							this.outstandingRequests.delete(t), this.newRequest_();
						},
						i = setTimeout(n, Math.floor(25e3));
					this.addTag(e, () => {
						clearTimeout(i), n();
					});
				}
				addTag(e, t) {
					(0, m.$g)()
						? this.doNodeLongPoll(e, t)
						: setTimeout(() => {
								try {
									if (!this.sendNewPolls) return;
									const n = this.myIFrame.doc.createElement("script");
									(n.type = "text/javascript"),
										(n.async = !0),
										(n.src = e),
										(n.onload = n.onreadystatechange =
											() => {
												const e = n.readyState;
												(e && "loaded" !== e && "complete" !== e) ||
													((n.onload = n.onreadystatechange = null),
													n.parentNode && n.parentNode.removeChild(n),
													t());
											}),
										(n.onerror = () => {
											M("Long-poll script failed to load: " + e),
												(this.sendNewPolls = !1),
												this.close();
										}),
										this.myIFrame.doc.body.appendChild(n);
								} catch (e) {}
							}, Math.floor(1));
				}
			}
			let ey = null;
			"undefined" != typeof MozWebSocket
				? (ey = MozWebSocket)
				: "undefined" != typeof WebSocket && (ey = WebSocket);
			class ev {
				constructor(e, t, n, i, r, s, o) {
					(this.connId = e),
						(this.applicationId = n),
						(this.appCheckToken = i),
						(this.authToken = r),
						(this.keepaliveTimer = null),
						(this.frames = null),
						(this.totalFrames = 0),
						(this.bytesSent = 0),
						(this.bytesReceived = 0),
						(this.log_ = q(this.connId)),
						(this.stats_ = e_(t)),
						(this.connURL = ev.connectionURL_(t, s, o, i, n)),
						(this.nodeAdmin = t.nodeAdmin);
				}
				static connectionURL_(e, t, n, i, r) {
					const s = {};
					return (
						(s.v = "5"),
						!(0, m.$g)() &&
							"undefined" != typeof location &&
							location.hostname &&
							es.test(location.hostname) &&
							(s.r = "f"),
						t && (s.s = t),
						n && (s.ls = n),
						i && (s.ac = i),
						r && (s.p = r),
						eh(e, eo, s)
					);
				}
				open(e, t) {
					(this.onDisconnect = t),
						(this.onMessage = e),
						this.log_("Websocket connecting to " + this.connURL),
						(this.everConnected_ = !1),
						k.set("previous_websocket_failure", !0);
					try {
						let e;
						if ((0, m.$g)()) {
							const t = this.nodeAdmin ? "AdminNode" : "Node";
							(e = {
								headers: {
									"User-Agent": `Firebase/5/${T}/${v.platform}/${t}`,
									"X-Firebase-GMPID": this.applicationId || "",
								},
							}),
								this.authToken &&
									(e.headers.Authorization = `Bearer ${this.authToken}`),
								this.appCheckToken &&
									(e.headers["X-Firebase-AppCheck"] = this.appCheckToken);
							const n = v.env,
								i =
									0 === this.connURL.indexOf("wss://")
										? n.HTTPS_PROXY || n.https_proxy
										: n.HTTP_PROXY || n.http_proxy;
							i && (e.proxy = { origin: i });
						}
						this.mySock = new ey(this.connURL, [], e);
					} catch (t) {
						this.log_("Error instantiating WebSocket.");
						const e = t.message || t.data;
						e && this.log_(e), this.onClosed_();
						return;
					}
					(this.mySock.onopen = () => {
						this.log_("Websocket connected."), (this.everConnected_ = !0);
					}),
						(this.mySock.onclose = () => {
							this.log_("Websocket connection was disconnected."),
								(this.mySock = null),
								this.onClosed_();
						}),
						(this.mySock.onmessage = (e) => {
							this.handleIncomingFrame(e);
						}),
						(this.mySock.onerror = (e) => {
							this.log_("WebSocket error.  Closing connection.");
							const t = e.message || e.data;
							t && this.log_(t), this.onClosed_();
						});
				}
				start() {}
				static forceDisallow() {
					ev.forceDisallow_ = !0;
				}
				static isAvailable() {
					let e = !1;
					if ("undefined" != typeof navigator && navigator.userAgent) {
						const t = navigator.userAgent.match(
							/Android ([0-9]{0,}\.[0-9]{0,})/,
						);
						t && t.length > 1 && 4.4 > Number.parseFloat(t[1]) && (e = !0);
					}
					return !e && null !== ey && !ev.forceDisallow_;
				}
				static previouslyFailed() {
					return (
						k.isInMemoryStorage || !0 === k.get("previous_websocket_failure")
					);
				}
				markConnectionHealthy() {
					k.remove("previous_websocket_failure");
				}
				appendFrame_(e) {
					if ((this.frames.push(e), this.frames.length === this.totalFrames)) {
						const e = this.frames.join("");
						this.frames = null;
						const t = (0, m.$L)(e);
						this.onMessage(t);
					}
				}
				handleNewFrameCount_(e) {
					(this.totalFrames = e), (this.frames = []);
				}
				extractFrameCount_(e) {
					if (
						((0, m.vA)(null === this.frames, "We already have a frame buffer"),
						e.length <= 6)
					) {
						const t = Number(e);
						if (!isNaN(t)) return this.handleNewFrameCount_(t), null;
					}
					return this.handleNewFrameCount_(1), e;
				}
				handleIncomingFrame(e) {
					if (null === this.mySock) return;
					const t = e.data;
					if (
						((this.bytesReceived += t.length),
						this.stats_.incrementCounter("bytes_received", t.length),
						this.resetKeepAlive(),
						null !== this.frames)
					)
						this.appendFrame_(t);
					else {
						const e = this.extractFrameCount_(t);
						null !== e && this.appendFrame_(e);
					}
				}
				send(e) {
					this.resetKeepAlive();
					const t = (0, m.As)(e);
					(this.bytesSent += t.length),
						this.stats_.incrementCounter("bytes_sent", t.length);
					const n = G(t, 16384);
					n.length > 1 && this.sendString_(String(n.length));
					for (let e = 0; e < n.length; e++) this.sendString_(n[e]);
				}
				shutdown_() {
					(this.isClosed_ = !0),
						this.keepaliveTimer &&
							(clearInterval(this.keepaliveTimer),
							(this.keepaliveTimer = null)),
						this.mySock && (this.mySock.close(), (this.mySock = null));
				}
				onClosed_() {
					!this.isClosed_ &&
						(this.log_("WebSocket is closing itself"),
						this.shutdown_(),
						this.onDisconnect &&
							(this.onDisconnect(this.everConnected_),
							(this.onDisconnect = null)));
				}
				close() {
					this.isClosed_ ||
						(this.log_("WebSocket is being closed"), this.shutdown_());
				}
				resetKeepAlive() {
					clearInterval(this.keepaliveTimer),
						(this.keepaliveTimer = setInterval(() => {
							this.mySock && this.sendString_("0"), this.resetKeepAlive();
						}, Math.floor(45e3)));
				}
				sendString_(e) {
					try {
						this.mySock.send(e);
					} catch (e) {
						this.log_(
							"Exception thrown from WebSocket.send():",
							e.message || e.data,
							"Closing connection.",
						),
							setTimeout(this.onClosed_.bind(this), 0);
					}
				}
			}
			(ev.responsesRequiredToBeHealthy = 2), (ev.healthyTimeout = 3e4);
			class eC {
				constructor(e) {
					this.initTransports_(e);
				}
				static get ALL_TRANSPORTS() {
					return [eg, ev];
				}
				static get IS_TRANSPORT_INITIALIZED() {
					return this.globalTransportInitialized_;
				}
				initTransports_(e) {
					let t = ev && ev.isAvailable(),
						n = t && !ev.previouslyFailed();
					if (
						(e.webSocketOnly &&
							(t ||
								W(
									"wss:// URL used, but browser isn't known to support websockets.  Trying anyway.",
								),
							(n = !0)),
						n)
					)
						this.transports_ = [ev];
					else {
						const e = (this.transports_ = []);
						for (const t of eC.ALL_TRANSPORTS)
							t && t.isAvailable() && e.push(t);
						eC.globalTransportInitialized_ = !0;
					}
				}
				initialTransport() {
					if (this.transports_.length > 0) return this.transports_[0];
					throw Error("No transports available");
				}
				upgradeTransport() {
					return this.transports_.length > 1 ? this.transports_[1] : null;
				}
			}
			eC.globalTransportInitialized_ = !1;
			class ew {
				constructor(e, t, n, i, r, s, o, l, a, h) {
					(this.id = e),
						(this.repoInfo_ = t),
						(this.applicationId_ = n),
						(this.appCheckToken_ = i),
						(this.authToken_ = r),
						(this.onMessage_ = s),
						(this.onReady_ = o),
						(this.onDisconnect_ = l),
						(this.onKill_ = a),
						(this.lastSessionId = h),
						(this.connectionCount = 0),
						(this.pendingDataMessages = []),
						(this.state_ = 0),
						(this.log_ = q("c:" + this.id + ":")),
						(this.transportManager_ = new eC(t)),
						this.log_("Connection created"),
						this.start_();
				}
				start_() {
					const e = this.transportManager_.initialTransport();
					(this.conn_ = new e(
						this.nextTransportId_(),
						this.repoInfo_,
						this.applicationId_,
						this.appCheckToken_,
						this.authToken_,
						null,
						this.lastSessionId,
					)),
						(this.primaryResponsesRequired_ =
							e.responsesRequiredToBeHealthy || 0);
					const t = this.connReceiver_(this.conn_),
						n = this.disconnReceiver_(this.conn_);
					(this.tx_ = this.conn_),
						(this.rx_ = this.conn_),
						(this.secondaryConn_ = null),
						(this.isHealthy_ = !1),
						setTimeout(() => {
							this.conn_ && this.conn_.open(t, n);
						}, Math.floor(0));
					const i = e.healthyTimeout || 0;
					i > 0 &&
						(this.healthyTimeout_ = et(() => {
							(this.healthyTimeout_ = null),
								this.isHealthy_ ||
									(this.conn_ && this.conn_.bytesReceived > 102400
										? (this.log_(
												"Connection exceeded healthy timeout but has received " +
													this.conn_.bytesReceived +
													" bytes.  Marking connection healthy.",
											),
											(this.isHealthy_ = !0),
											this.conn_.markConnectionHealthy())
										: this.conn_ && this.conn_.bytesSent > 10240
											? this.log_(
													"Connection exceeded healthy timeout but has sent " +
														this.conn_.bytesSent +
														" bytes.  Leaving connection alive.",
												)
											: (this.log_(
													"Closing unhealthy connection after timeout.",
												),
												this.close()));
						}, Math.floor(i)));
				}
				nextTransportId_() {
					return "c:" + this.id + ":" + this.connectionCount++;
				}
				disconnReceiver_(e) {
					return (t) => {
						e === this.conn_
							? this.onConnectionLost_(t)
							: e === this.secondaryConn_
								? (this.log_("Secondary connection lost."),
									this.onSecondaryConnectionLost_())
								: this.log_("closing an old connection");
					};
				}
				connReceiver_(e) {
					return (t) => {
						2 !== this.state_ &&
							(e === this.rx_
								? this.onPrimaryMessageReceived_(t)
								: e === this.secondaryConn_
									? this.onSecondaryMessageReceived_(t)
									: this.log_("message on old connection"));
					};
				}
				sendRequest(e) {
					this.sendData_({ t: "d", d: e });
				}
				tryCleanupConnection() {
					this.tx_ === this.secondaryConn_ &&
						this.rx_ === this.secondaryConn_ &&
						(this.log_(
							"cleaning up and promoting a connection: " +
								this.secondaryConn_.connId,
						),
						(this.conn_ = this.secondaryConn_),
						(this.secondaryConn_ = null));
				}
				onSecondaryControl_(e) {
					if ("t" in e) {
						const t = e.t;
						"a" === t
							? this.upgradeIfSecondaryHealthy_()
							: "r" === t
								? (this.log_("Got a reset on secondary, closing it"),
									this.secondaryConn_.close(),
									(this.tx_ === this.secondaryConn_ ||
										this.rx_ === this.secondaryConn_) &&
										this.close())
								: "o" === t &&
									(this.log_("got pong on secondary."),
									this.secondaryResponsesRequired_--,
									this.upgradeIfSecondaryHealthy_());
					}
				}
				onSecondaryMessageReceived_(e) {
					const t = K("t", e),
						n = K("d", e);
					if ("c" === t) this.onSecondaryControl_(n);
					else if ("d" === t) this.pendingDataMessages.push(n);
					else throw Error("Unknown protocol layer: " + t);
				}
				upgradeIfSecondaryHealthy_() {
					this.secondaryResponsesRequired_ <= 0
						? (this.log_("Secondary connection is healthy."),
							(this.isHealthy_ = !0),
							this.secondaryConn_.markConnectionHealthy(),
							this.proceedWithUpgrade_())
						: (this.log_("sending ping on secondary."),
							this.secondaryConn_.send({ t: "c", d: { t: "p", d: {} } }));
				}
				proceedWithUpgrade_() {
					this.secondaryConn_.start(),
						this.log_("sending client ack on secondary"),
						this.secondaryConn_.send({ t: "c", d: { t: "a", d: {} } }),
						this.log_("Ending transmission on primary"),
						this.conn_.send({ t: "c", d: { t: "n", d: {} } }),
						(this.tx_ = this.secondaryConn_),
						this.tryCleanupConnection();
				}
				onPrimaryMessageReceived_(e) {
					const t = K("t", e),
						n = K("d", e);
					"c" === t ? this.onControl_(n) : "d" === t && this.onDataMessage_(n);
				}
				onDataMessage_(e) {
					this.onPrimaryResponse_(), this.onMessage_(e);
				}
				onPrimaryResponse_() {
					!this.isHealthy_ &&
						(this.primaryResponsesRequired_--,
						this.primaryResponsesRequired_ <= 0 &&
							(this.log_("Primary connection is healthy."),
							(this.isHealthy_ = !0),
							this.conn_.markConnectionHealthy()));
				}
				onControl_(e) {
					const t = K("t", e);
					if ("d" in e) {
						const n = e.d;
						if ("h" === t) {
							const e = Object.assign({}, n);
							this.repoInfo_.isUsingEmulator && (e.h = this.repoInfo_.host),
								this.onHandshake_(e);
						} else if ("n" === t) {
							this.log_("recvd end transmission on primary"),
								(this.rx_ = this.secondaryConn_);
							for (let e = 0; e < this.pendingDataMessages.length; ++e)
								this.onDataMessage_(this.pendingDataMessages[e]);
							(this.pendingDataMessages = []), this.tryCleanupConnection();
						} else
							"s" === t
								? this.onConnectionShutdown_(n)
								: "r" === t
									? this.onReset_(n)
									: "e" === t
										? L("Server Error: " + n)
										: "o" === t
											? (this.log_("got pong on primary."),
												this.onPrimaryResponse_(),
												this.sendPingOnPrimaryIfNecessary_())
											: L("Unknown control packet command: " + t);
					}
				}
				onHandshake_(e) {
					const t = e.ts,
						n = e.v,
						i = e.h;
					(this.sessionId = e.s),
						(this.repoInfo_.host = i),
						0 === this.state_ &&
							(this.conn_.start(),
							this.onConnectionEstablished_(this.conn_, t),
							"5" !== n && W("Protocol version mismatch detected"),
							this.tryStartUpgrade_());
				}
				tryStartUpgrade_() {
					const e = this.transportManager_.upgradeTransport();
					e && this.startUpgrade_(e);
				}
				startUpgrade_(e) {
					(this.secondaryConn_ = new e(
						this.nextTransportId_(),
						this.repoInfo_,
						this.applicationId_,
						this.appCheckToken_,
						this.authToken_,
						this.sessionId,
					)),
						(this.secondaryResponsesRequired_ =
							e.responsesRequiredToBeHealthy || 0);
					const t = this.connReceiver_(this.secondaryConn_),
						n = this.disconnReceiver_(this.secondaryConn_);
					this.secondaryConn_.open(t, n),
						et(() => {
							this.secondaryConn_ &&
								(this.log_("Timed out trying to upgrade."),
								this.secondaryConn_.close());
						}, Math.floor(6e4));
				}
				onReset_(e) {
					this.log_("Reset packet received.  New host: " + e),
						(this.repoInfo_.host = e),
						1 === this.state_
							? this.close()
							: (this.closeConnections_(), this.start_());
				}
				onConnectionEstablished_(e, t) {
					this.log_("Realtime connection established."),
						(this.conn_ = e),
						(this.state_ = 1),
						this.onReady_ &&
							(this.onReady_(t, this.sessionId), (this.onReady_ = null)),
						0 === this.primaryResponsesRequired_
							? (this.log_("Primary connection is healthy."),
								(this.isHealthy_ = !0))
							: et(() => {
									this.sendPingOnPrimaryIfNecessary_();
								}, Math.floor(5e3));
				}
				sendPingOnPrimaryIfNecessary_() {
					this.isHealthy_ ||
						1 !== this.state_ ||
						(this.log_("sending ping on primary."),
						this.sendData_({ t: "c", d: { t: "p", d: {} } }));
				}
				onSecondaryConnectionLost_() {
					const e = this.secondaryConn_;
					(this.secondaryConn_ = null),
						(this.tx_ === e || this.rx_ === e) && this.close();
				}
				onConnectionLost_(e) {
					(this.conn_ = null),
						e || 0 !== this.state_
							? 1 === this.state_ && this.log_("Realtime connection lost.")
							: (this.log_("Realtime connection failed."),
								this.repoInfo_.isCacheableHost() &&
									(k.remove("host:" + this.repoInfo_.host),
									(this.repoInfo_.internalHost = this.repoInfo_.host))),
						this.close();
				}
				onConnectionShutdown_(e) {
					this.log_("Connection shutdown command received. Shutting down..."),
						this.onKill_ && (this.onKill_(e), (this.onKill_ = null)),
						(this.onDisconnect_ = null),
						this.close();
				}
				sendData_(e) {
					if (1 !== this.state_) throw "Connection is not connected";
					this.tx_.send(e);
				}
				close() {
					2 !== this.state_ &&
						(this.log_("Closing realtime connection."),
						(this.state_ = 2),
						this.closeConnections_(),
						this.onDisconnect_ &&
							(this.onDisconnect_(), (this.onDisconnect_ = null)));
				}
				closeConnections_() {
					this.log_("Shutting down all connections"),
						this.conn_ && (this.conn_.close(), (this.conn_ = null)),
						this.secondaryConn_ &&
							(this.secondaryConn_.close(), (this.secondaryConn_ = null)),
						this.healthyTimeout_ &&
							(clearTimeout(this.healthyTimeout_),
							(this.healthyTimeout_ = null));
				}
			}
			class eT {
				put(e, t, n, i) {}
				merge(e, t, n, i) {}
				refreshAuthToken(e) {}
				refreshAppCheckToken(e) {}
				onDisconnectPut(e, t, n) {}
				onDisconnectMerge(e, t, n) {}
				onDisconnectCancel(e, t) {}
				reportStats(e) {}
			}
			class eI {
				constructor(e) {
					(this.allowedEvents_ = e),
						(this.listeners_ = {}),
						(0, m.vA)(
							Array.isArray(e) && e.length > 0,
							"Requires a non-empty array",
						);
				}
				trigger(e, ...t) {
					if (Array.isArray(this.listeners_[e])) {
						const n = [...this.listeners_[e]];
						for (let e = 0; e < n.length; e++)
							n[e].callback.apply(n[e].context, t);
					}
				}
				on(e, t, n) {
					this.validateEventType_(e),
						(this.listeners_[e] = this.listeners_[e] || []),
						this.listeners_[e].push({ callback: t, context: n });
					const i = this.getInitialEvent(e);
					i && t.apply(n, i);
				}
				off(e, t, n) {
					this.validateEventType_(e);
					const i = this.listeners_[e] || [];
					for (let e = 0; e < i.length; e++)
						if (i[e].callback === t && (!n || n === i[e].context)) {
							i.splice(e, 1);
							return;
						}
				}
				validateEventType_(e) {
					(0, m.vA)(
						this.allowedEvents_.find((t) => t === e),
						"Unknown event: " + e,
					);
				}
			}
			class eb extends eI {
				constructor() {
					super(["online"]),
						(this.online_ = !0),
						"undefined" == typeof window ||
							void 0 === window.addEventListener ||
							(0, m.jZ)() ||
							(window.addEventListener(
								"online",
								() => {
									this.online_ ||
										((this.online_ = !0), this.trigger("online", !0));
								},
								!1,
							),
							window.addEventListener(
								"offline",
								() => {
									this.online_ &&
										((this.online_ = !1), this.trigger("online", !1));
								},
								!1,
							));
				}
				static getInstance() {
					return new eb();
				}
				getInitialEvent(e) {
					return (
						(0, m.vA)("online" === e, "Unknown event type: " + e),
						[this.online_]
					);
				}
				currentlyOnline() {
					return this.online_;
				}
			}
			class eE {
				constructor(e, t) {
					if (void 0 === t) {
						this.pieces_ = e.split("/");
						let t = 0;
						for (let e = 0; e < this.pieces_.length; e++)
							this.pieces_[e].length > 0 &&
								((this.pieces_[t] = this.pieces_[e]), t++);
						(this.pieces_.length = t), (this.pieceNum_ = 0);
					} else (this.pieces_ = e), (this.pieceNum_ = t);
				}
				toString() {
					let e = "";
					for (let t = this.pieceNum_; t < this.pieces_.length; t++)
						"" !== this.pieces_[t] && (e += "/" + this.pieces_[t]);
					return e || "/";
				}
			}
			function ek() {
				return new eE("");
			}
			function eS(e) {
				return e.pieceNum_ >= e.pieces_.length ? null : e.pieces_[e.pieceNum_];
			}
			function eN(e) {
				return e.pieces_.length - e.pieceNum_;
			}
			function ex(e) {
				let t = e.pieceNum_;
				return t < e.pieces_.length && t++, new eE(e.pieces_, t);
			}
			function eP(e) {
				return e.pieceNum_ < e.pieces_.length
					? e.pieces_[e.pieces_.length - 1]
					: null;
			}
			function eR(e, t = 0) {
				return e.pieces_.slice(e.pieceNum_ + t);
			}
			function eA(e) {
				if (e.pieceNum_ >= e.pieces_.length) return null;
				const t = [];
				for (let n = e.pieceNum_; n < e.pieces_.length - 1; n++)
					t.push(e.pieces_[n]);
				return new eE(t, 0);
			}
			function eD(e, t) {
				const n = [];
				for (let t = e.pieceNum_; t < e.pieces_.length; t++)
					n.push(e.pieces_[t]);
				if (t instanceof eE)
					for (let e = t.pieceNum_; e < t.pieces_.length; e++)
						n.push(t.pieces_[e]);
				else {
					const e = t.split("/");
					for (let t = 0; t < e.length; t++) e[t].length > 0 && n.push(e[t]);
				}
				return new eE(n, 0);
			}
			function eF(e) {
				return e.pieceNum_ >= e.pieces_.length;
			}
			function eM(e, t) {
				const n = eS(e),
					i = eS(t);
				if (null === n) return t;
				if (n === i) return eM(ex(e), ex(t));
				throw Error(
					"INTERNAL ERROR: innerPath (" +
						t +
						") is not within outerPath (" +
						e +
						")",
				);
			}
			function eq(e, t) {
				const n = eR(e, 0),
					i = eR(t, 0);
				for (let e = 0; e < n.length && e < i.length; e++) {
					const t = V(n[e], i[e]);
					if (0 !== t) return t;
				}
				return n.length === i.length ? 0 : n.length < i.length ? -1 : 1;
			}
			function eL(e, t) {
				if (eN(e) !== eN(t)) return !1;
				for (
					let n = e.pieceNum_, i = t.pieceNum_;
					n <= e.pieces_.length;
					n++, i++
				)
					if (e.pieces_[n] !== t.pieces_[i]) return !1;
				return !0;
			}
			function eO(e, t) {
				let n = e.pieceNum_,
					i = t.pieceNum_;
				if (eN(e) > eN(t)) return !1;
				while (n < e.pieces_.length) {
					if (e.pieces_[n] !== t.pieces_[i]) return !1;
					++n, ++i;
				}
				return !0;
			}
			class eW {
				constructor(e, t) {
					(this.errorPrefix_ = t),
						(this.parts_ = eR(e, 0)),
						(this.byteLength_ = Math.max(1, this.parts_.length));
					for (let e = 0; e < this.parts_.length; e++)
						this.byteLength_ += (0, m.OE)(this.parts_[e]);
					eU(this);
				}
			}
			function eU(e) {
				if (e.byteLength_ > 768)
					throw Error(
						e.errorPrefix_ +
							"has a key path longer than 768 bytes (" +
							e.byteLength_ +
							").",
					);
				if (e.parts_.length > 32)
					throw Error(
						e.errorPrefix_ +
							"path specified exceeds the maximum depth that can be written (32) or object contains a cycle " +
							eH(e),
					);
			}
			function eH(e) {
				return 0 === e.parts_.length
					? ""
					: "in property '" + e.parts_.join(".") + "'";
			}
			class ej extends eI {
				constructor() {
					let e, t;
					super(["visible"]),
						"undefined" != typeof document &&
							void 0 !== document.addEventListener &&
							(void 0 !== document.hidden
								? ((t = "visibilitychange"), (e = "hidden"))
								: void 0 !== document.mozHidden
									? ((t = "mozvisibilitychange"), (e = "mozHidden"))
									: void 0 !== document.msHidden
										? ((t = "msvisibilitychange"), (e = "msHidden"))
										: void 0 !== document.webkitHidden &&
											((t = "webkitvisibilitychange"), (e = "webkitHidden"))),
						(this.visible_ = !0),
						t &&
							document.addEventListener(
								t,
								() => {
									const t = !document[e];
									t !== this.visible_ &&
										((this.visible_ = t), this.trigger("visible", t));
								},
								!1,
							);
				}
				static getInstance() {
					return new ej();
				}
				getInitialEvent(e) {
					return (
						(0, m.vA)("visible" === e, "Unknown event type: " + e),
						[this.visible_]
					);
				}
			}
			class eY extends eT {
				constructor(e, t, n, i, r, s, o, l) {
					if (
						(super(),
						(this.repoInfo_ = e),
						(this.applicationId_ = t),
						(this.onDataUpdate_ = n),
						(this.onConnectStatus_ = i),
						(this.onServerInfoUpdate_ = r),
						(this.authTokenProvider_ = s),
						(this.appCheckTokenProvider_ = o),
						(this.authOverride_ = l),
						(this.id = eY.nextPersistentConnectionId_++),
						(this.log_ = q("p:" + this.id + ":")),
						(this.interruptReasons_ = {}),
						(this.listens = new Map()),
						(this.outstandingPuts_ = []),
						(this.outstandingGets_ = []),
						(this.outstandingPutCount_ = 0),
						(this.outstandingGetCount_ = 0),
						(this.onDisconnectRequestQueue_ = []),
						(this.connected_ = !1),
						(this.reconnectDelay_ = 1e3),
						(this.maxReconnectDelay_ = 3e5),
						(this.securityDebugCallback_ = null),
						(this.lastSessionId = null),
						(this.establishConnectionTimer_ = null),
						(this.visible_ = !1),
						(this.requestCBHash_ = {}),
						(this.requestNumber_ = 0),
						(this.realtime_ = null),
						(this.authToken_ = null),
						(this.appCheckToken_ = null),
						(this.forceTokenRefresh_ = !1),
						(this.invalidAuthTokenCount_ = 0),
						(this.invalidAppCheckTokenCount_ = 0),
						(this.firstConnection_ = !0),
						(this.lastConnectionAttemptTime_ = null),
						(this.lastConnectionEstablishedTime_ = null),
						l && !(0, m.$g)())
					)
						throw Error(
							"Auth override specified in options, but not supported on non Node.js platforms",
						);
					ej.getInstance().on("visible", this.onVisible_, this),
						-1 === e.host.indexOf("fblocal") &&
							eb.getInstance().on("online", this.onOnline_, this);
				}
				sendRequest(e, t, n) {
					const i = ++this.requestNumber_,
						r = { r: i, a: e, b: t };
					this.log_((0, m.As)(r)),
						(0, m.vA)(
							this.connected_,
							"sendRequest call when we're not connected not allowed.",
						),
						this.realtime_.sendRequest(r),
						n && (this.requestCBHash_[i] = n);
				}
				get(e) {
					this.initConnection_();
					const t = new m.cY(),
						n = { p: e._path.toString(), q: e._queryObject };
					this.outstandingGets_.push({
						action: "g",
						request: n,
						onComplete: (e) => {
							const n = e.d;
							"ok" === e.s ? t.resolve(n) : t.reject(n);
						},
					}),
						this.outstandingGetCount_++;
					const i = this.outstandingGets_.length - 1;
					return this.connected_ && this.sendGet_(i), t.promise;
				}
				listen(e, t, n, i) {
					this.initConnection_();
					const r = e._queryIdentifier,
						s = e._path.toString();
					this.log_("Listen called for " + s + " " + r),
						this.listens.has(s) || this.listens.set(s, new Map()),
						(0, m.vA)(
							e._queryParams.isDefault() || !e._queryParams.loadsAllData(),
							"listen() called for non-default but complete query",
						),
						(0, m.vA)(
							!this.listens.get(s).has(r),
							"listen() called twice for same path/queryId.",
						);
					const o = { onComplete: i, hashFn: t, query: e, tag: n };
					this.listens.get(s).set(r, o), this.connected_ && this.sendListen_(o);
				}
				sendGet_(e) {
					const t = this.outstandingGets_[e];
					this.sendRequest("g", t.request, (n) => {
						delete this.outstandingGets_[e],
							this.outstandingGetCount_--,
							0 === this.outstandingGetCount_ && (this.outstandingGets_ = []),
							t.onComplete && t.onComplete(n);
					});
				}
				sendListen_(e) {
					const t = e.query,
						n = t._path.toString(),
						i = t._queryIdentifier;
					this.log_("Listen on " + n + " for " + i);
					const r = { p: n };
					e.tag && ((r.q = t._queryObject), (r.t = e.tag)),
						(r.h = e.hashFn()),
						this.sendRequest("q", r, (r) => {
							const s = r.d,
								o = r.s;
							eY.warnOnListenWarnings_(s, t),
								(this.listens.get(n) && this.listens.get(n).get(i)) === e &&
									(this.log_("listen response", r),
									"ok" !== o && this.removeListen_(n, i),
									e.onComplete && e.onComplete(o, s));
						});
				}
				static warnOnListenWarnings_(e, t) {
					if (e && "object" == typeof e && (0, m.gR)(e, "w")) {
						const n = (0, m.yw)(e, "w");
						if (Array.isArray(n) && ~n.indexOf("no_index")) {
							const e =
									'".indexOn": "' + t._queryParams.getIndex().toString() + '"',
								n = t._path.toString();
							W(
								`Using an unspecified index. Your data will be downloaded and filtered on the client. Consider adding ${e} at ${n} to your security rules for better performance.`,
							);
						}
					}
				}
				refreshAuthToken(e) {
					(this.authToken_ = e),
						this.log_("Auth token refreshed"),
						this.authToken_
							? this.tryAuth()
							: this.connected_ && this.sendRequest("unauth", {}, () => {}),
						this.reduceReconnectDelayIfAdminCredential_(e);
				}
				reduceReconnectDelayIfAdminCredential_(e) {
					((e && 40 === e.length) || (0, m.qc)(e)) &&
						(this.log_(
							"Admin auth credential detected.  Reducing max reconnect time.",
						),
						(this.maxReconnectDelay_ = 3e4));
				}
				refreshAppCheckToken(e) {
					(this.appCheckToken_ = e),
						this.log_("App check token refreshed"),
						this.appCheckToken_
							? this.tryAppCheck()
							: this.connected_ && this.sendRequest("unappeck", {}, () => {});
				}
				tryAuth() {
					if (this.connected_ && this.authToken_) {
						const e = this.authToken_,
							t = (0, m.Cv)(e) ? "auth" : "gauth",
							n = { cred: e };
						null === this.authOverride_
							? (n.noauth = !0)
							: "object" == typeof this.authOverride_ &&
								(n.authvar = this.authOverride_),
							this.sendRequest(t, n, (t) => {
								const n = t.s,
									i = t.d || "error";
								this.authToken_ === e &&
									("ok" === n
										? (this.invalidAuthTokenCount_ = 0)
										: this.onAuthRevoked_(n, i));
							});
					}
				}
				tryAppCheck() {
					this.connected_ &&
						this.appCheckToken_ &&
						this.sendRequest(
							"appcheck",
							{ token: this.appCheckToken_ },
							(e) => {
								const t = e.s,
									n = e.d || "error";
								"ok" === t
									? (this.invalidAppCheckTokenCount_ = 0)
									: this.onAppCheckRevoked_(t, n);
							},
						);
				}
				unlisten(e, t) {
					const n = e._path.toString(),
						i = e._queryIdentifier;
					this.log_("Unlisten called for " + n + " " + i),
						(0, m.vA)(
							e._queryParams.isDefault() || !e._queryParams.loadsAllData(),
							"unlisten() called for non-default but complete query",
						),
						this.removeListen_(n, i) &&
							this.connected_ &&
							this.sendUnlisten_(n, i, e._queryObject, t);
				}
				sendUnlisten_(e, t, n, i) {
					this.log_("Unlisten on " + e + " for " + t);
					const r = { p: e };
					i && ((r.q = n), (r.t = i)), this.sendRequest("n", r);
				}
				onDisconnectPut(e, t, n) {
					this.initConnection_(),
						this.connected_
							? this.sendOnDisconnect_("o", e, t, n)
							: this.onDisconnectRequestQueue_.push({
									pathString: e,
									action: "o",
									data: t,
									onComplete: n,
								});
				}
				onDisconnectMerge(e, t, n) {
					this.initConnection_(),
						this.connected_
							? this.sendOnDisconnect_("om", e, t, n)
							: this.onDisconnectRequestQueue_.push({
									pathString: e,
									action: "om",
									data: t,
									onComplete: n,
								});
				}
				onDisconnectCancel(e, t) {
					this.initConnection_(),
						this.connected_
							? this.sendOnDisconnect_("oc", e, null, t)
							: this.onDisconnectRequestQueue_.push({
									pathString: e,
									action: "oc",
									data: null,
									onComplete: t,
								});
				}
				sendOnDisconnect_(e, t, n, i) {
					const r = { p: t, d: n };
					this.log_("onDisconnect " + e, r),
						this.sendRequest(e, r, (e) => {
							i &&
								setTimeout(() => {
									i(e.s, e.d);
								}, Math.floor(0));
						});
				}
				put(e, t, n, i) {
					this.putInternal("p", e, t, n, i);
				}
				merge(e, t, n, i) {
					this.putInternal("m", e, t, n, i);
				}
				putInternal(e, t, n, i, r) {
					this.initConnection_();
					const s = { p: t, d: n };
					void 0 !== r && (s.h = r),
						this.outstandingPuts_.push({
							action: e,
							request: s,
							onComplete: i,
						}),
						this.outstandingPutCount_++;
					const o = this.outstandingPuts_.length - 1;
					this.connected_ ? this.sendPut_(o) : this.log_("Buffering put: " + t);
				}
				sendPut_(e) {
					const t = this.outstandingPuts_[e].action,
						n = this.outstandingPuts_[e].request,
						i = this.outstandingPuts_[e].onComplete;
					(this.outstandingPuts_[e].queued = this.connected_),
						this.sendRequest(t, n, (n) => {
							this.log_(t + " response", n),
								delete this.outstandingPuts_[e],
								this.outstandingPutCount_--,
								0 === this.outstandingPutCount_ && (this.outstandingPuts_ = []),
								i && i(n.s, n.d);
						});
				}
				reportStats(e) {
					if (this.connected_) {
						const t = { c: e };
						this.log_("reportStats", t),
							this.sendRequest("s", t, (e) => {
								if ("ok" !== e.s) {
									const t = e.d;
									this.log_("reportStats", "Error sending stats: " + t);
								}
							});
					}
				}
				onDataMessage_(e) {
					if ("r" in e) {
						this.log_("from server: " + (0, m.As)(e));
						const t = e.r,
							n = this.requestCBHash_[t];
						n && (delete this.requestCBHash_[t], n(e.b));
					} else if ("error" in e)
						throw "A server-side error has occurred: " + e.error;
					else "a" in e && this.onDataPush_(e.a, e.b);
				}
				onDataPush_(e, t) {
					this.log_("handleServerMessage", e, t),
						"d" === e
							? this.onDataUpdate_(t.p, t.d, !1, t.t)
							: "m" === e
								? this.onDataUpdate_(t.p, t.d, !0, t.t)
								: "c" === e
									? this.onListenRevoked_(t.p, t.q)
									: "ac" === e
										? this.onAuthRevoked_(t.s, t.d)
										: "apc" === e
											? this.onAppCheckRevoked_(t.s, t.d)
											: "sd" === e
												? this.onSecurityDebugPacket_(t)
												: L(
														"Unrecognized action received from server: " +
															(0, m.As)(e) +
															"\nAre you using the latest client?",
													);
				}
				onReady_(e, t) {
					this.log_("connection ready"),
						(this.connected_ = !0),
						(this.lastConnectionEstablishedTime_ = new Date().getTime()),
						this.handleTimestamp_(e),
						(this.lastSessionId = t),
						this.firstConnection_ && this.sendConnectStats_(),
						this.restoreState_(),
						(this.firstConnection_ = !1),
						this.onConnectStatus_(!0);
				}
				scheduleConnect_(e) {
					(0, m.vA)(
						!this.realtime_,
						"Scheduling a connect when we're already connected/ing?",
					),
						this.establishConnectionTimer_ &&
							clearTimeout(this.establishConnectionTimer_),
						(this.establishConnectionTimer_ = setTimeout(() => {
							(this.establishConnectionTimer_ = null),
								this.establishConnection_();
						}, Math.floor(e)));
				}
				initConnection_() {
					!this.realtime_ && this.firstConnection_ && this.scheduleConnect_(0);
				}
				onVisible_(e) {
					!e ||
						this.visible_ ||
						this.reconnectDelay_ !== this.maxReconnectDelay_ ||
						(this.log_("Window became visible.  Reducing delay."),
						(this.reconnectDelay_ = 1e3),
						this.realtime_ || this.scheduleConnect_(0)),
						(this.visible_ = e);
				}
				onOnline_(e) {
					e
						? (this.log_("Browser went online."),
							(this.reconnectDelay_ = 1e3),
							this.realtime_ || this.scheduleConnect_(0))
						: (this.log_("Browser went offline.  Killing connection."),
							this.realtime_ && this.realtime_.close());
				}
				onRealtimeDisconnect_() {
					if (
						(this.log_("data client disconnected"),
						(this.connected_ = !1),
						(this.realtime_ = null),
						this.cancelSentTransactions_(),
						(this.requestCBHash_ = {}),
						this.shouldReconnect_())
					) {
						this.visible_
							? this.lastConnectionEstablishedTime_ &&
								(new Date().getTime() - this.lastConnectionEstablishedTime_ >
									3e4 && (this.reconnectDelay_ = 1e3),
								(this.lastConnectionEstablishedTime_ = null))
							: (this.log_("Window isn't visible.  Delaying reconnect."),
								(this.reconnectDelay_ = this.maxReconnectDelay_),
								(this.lastConnectionAttemptTime_ = new Date().getTime()));
						let e = new Date().getTime() - this.lastConnectionAttemptTime_,
							t = Math.max(0, this.reconnectDelay_ - e);
						(t = Math.random() * t),
							this.log_("Trying to reconnect in " + t + "ms"),
							this.scheduleConnect_(t),
							(this.reconnectDelay_ = Math.min(
								this.maxReconnectDelay_,
								1.3 * this.reconnectDelay_,
							));
					}
					this.onConnectStatus_(!1);
				}
				async establishConnection_() {
					if (this.shouldReconnect_()) {
						this.log_("Making a connection attempt"),
							(this.lastConnectionAttemptTime_ = new Date().getTime()),
							(this.lastConnectionEstablishedTime_ = null);
						let e = this.onDataMessage_.bind(this),
							t = this.onReady_.bind(this),
							n = this.onRealtimeDisconnect_.bind(this),
							i = this.id + ":" + eY.nextConnectionId_++,
							r = this.lastSessionId,
							s = !1,
							o = null,
							l = () => {
								o ? o.close() : ((s = !0), n());
							};
						this.realtime_ = {
							close: l,
							sendRequest: (e) => {
								(0, m.vA)(
									o,
									"sendRequest call when we're not connected not allowed.",
								),
									o.sendRequest(e);
							},
						};
						const a = this.forceTokenRefresh_;
						this.forceTokenRefresh_ = !1;
						try {
							const [l, h] = await Promise.all([
								this.authTokenProvider_.getToken(a),
								this.appCheckTokenProvider_.getToken(a),
							]);
							s
								? M("getToken() completed but was canceled")
								: (M("getToken() completed. Creating connection."),
									(this.authToken_ = l && l.accessToken),
									(this.appCheckToken_ = h && h.token),
									(o = new ew(
										i,
										this.repoInfo_,
										this.applicationId_,
										this.appCheckToken_,
										this.authToken_,
										e,
										t,
										n,
										(e) => {
											W(e + " (" + this.repoInfo_.toString() + ")"),
												this.interrupt("server_kill");
										},
										r,
									)));
						} catch (e) {
							this.log_("Failed to get token: " + e),
								s || (this.repoInfo_.nodeAdmin && W(e), l());
						}
					}
				}
				interrupt(e) {
					M("Interrupting connection for reason: " + e),
						(this.interruptReasons_[e] = !0),
						this.realtime_
							? this.realtime_.close()
							: (this.establishConnectionTimer_ &&
									(clearTimeout(this.establishConnectionTimer_),
									(this.establishConnectionTimer_ = null)),
								this.connected_ && this.onRealtimeDisconnect_());
				}
				resume(e) {
					M("Resuming connection for reason: " + e),
						delete this.interruptReasons_[e],
						(0, m.Im)(this.interruptReasons_) &&
							((this.reconnectDelay_ = 1e3),
							this.realtime_ || this.scheduleConnect_(0));
				}
				handleTimestamp_(e) {
					const t = e - new Date().getTime();
					this.onServerInfoUpdate_({ serverTimeOffset: t });
				}
				cancelSentTransactions_() {
					for (let e = 0; e < this.outstandingPuts_.length; e++) {
						const t = this.outstandingPuts_[e];
						t &&
							"h" in t.request &&
							t.queued &&
							(t.onComplete && t.onComplete("disconnect"),
							delete this.outstandingPuts_[e],
							this.outstandingPutCount_--);
					}
					0 === this.outstandingPutCount_ && (this.outstandingPuts_ = []);
				}
				onListenRevoked_(e, t) {
					let n;
					n = t ? t.map((e) => $(e)).join("$") : "default";
					const i = this.removeListen_(e, n);
					i && i.onComplete && i.onComplete("permission_denied");
				}
				removeListen_(e, t) {
					let n;
					const i = new eE(e).toString();
					if (this.listens.has(i)) {
						const e = this.listens.get(i);
						(n = e.get(t)), e.delete(t), 0 === e.size && this.listens.delete(i);
					} else n = void 0;
					return n;
				}
				onAuthRevoked_(e, t) {
					M("Auth token revoked: " + e + "/" + t),
						(this.authToken_ = null),
						(this.forceTokenRefresh_ = !0),
						this.realtime_.close(),
						("invalid_token" === e || "permission_denied" === e) &&
							(this.invalidAuthTokenCount_++,
							this.invalidAuthTokenCount_ >= 3 &&
								((this.reconnectDelay_ = 3e4),
								this.authTokenProvider_.notifyForInvalidToken()));
				}
				onAppCheckRevoked_(e, t) {
					M("App check token revoked: " + e + "/" + t),
						(this.appCheckToken_ = null),
						(this.forceTokenRefresh_ = !0),
						("invalid_token" === e || "permission_denied" === e) &&
							(this.invalidAppCheckTokenCount_++,
							this.invalidAppCheckTokenCount_ >= 3 &&
								this.appCheckTokenProvider_.notifyForInvalidToken());
				}
				onSecurityDebugPacket_(e) {
					this.securityDebugCallback_
						? this.securityDebugCallback_(e)
						: "msg" in e &&
							console.log("FIREBASE: " + e.msg.replace("\n", "\nFIREBASE: "));
				}
				restoreState_() {
					for (const e of (this.tryAuth(),
					this.tryAppCheck(),
					this.listens.values()))
						for (const t of e.values()) this.sendListen_(t);
					for (let e = 0; e < this.outstandingPuts_.length; e++)
						this.outstandingPuts_[e] && this.sendPut_(e);
					while (this.onDisconnectRequestQueue_.length) {
						const e = this.onDisconnectRequestQueue_.shift();
						this.sendOnDisconnect_(
							e.action,
							e.pathString,
							e.data,
							e.onComplete,
						);
					}
					for (let e = 0; e < this.outstandingGets_.length; e++)
						this.outstandingGets_[e] && this.sendGet_(e);
				}
				sendConnectStats_() {
					let e = {},
						t = "js";
					(0, m.$g)() && (t = this.repoInfo_.nodeAdmin ? "admin_node" : "node"),
						(e["sdk." + t + "." + T.replace(/\./g, "-")] = 1),
						(0, m.jZ)()
							? (e["framework.cordova"] = 1)
							: (0, m.lV)() && (e["framework.reactnative"] = 1),
						this.reportStats(e);
				}
				shouldReconnect_() {
					const e = eb.getInstance().currentlyOnline();
					return (0, m.Im)(this.interruptReasons_) && e;
				}
			}
			(eY.nextPersistentConnectionId_ = 0), (eY.nextConnectionId_ = 0);
			class ez {
				constructor(e, t) {
					(this.name = e), (this.node = t);
				}
				static Wrap(e, t) {
					return new ez(e, t);
				}
			}
			class eV {
				getCompare() {
					return this.compare.bind(this);
				}
				indexedValueChanged(e, t) {
					const n = new ez(Y, e),
						i = new ez(Y, t);
					return 0 !== this.compare(n, i);
				}
				minPost() {
					return ez.MIN;
				}
			}
			class eB extends eV {
				static get __EMPTY_NODE() {
					return i;
				}
				static set __EMPTY_NODE(e) {
					i = e;
				}
				compare(e, t) {
					return V(e.name, t.name);
				}
				isDefinedOn(e) {
					throw (0, m.Hk)("KeyIndex.isDefinedOn not expected to be called.");
				}
				indexedValueChanged(e, t) {
					return !1;
				}
				minPost() {
					return ez.MIN;
				}
				maxPost() {
					return new ez(z, i);
				}
				makePost(e, t) {
					return (
						(0, m.vA)(
							"string" == typeof e,
							"KeyIndex indexValue must always be a string.",
						),
						new ez(e, i)
					);
				}
				toString() {
					return ".key";
				}
			}
			const eK = new eB();
			class e$ {
				constructor(e, t, n, i, r = null) {
					(this.isReverse_ = i),
						(this.resultGenerator_ = r),
						(this.nodeStack_ = []);
					let s = 1;
					while (!e.isEmpty())
						if (((s = t ? n(e.key, t) : 1), i && (s *= -1), s < 0))
							e = this.isReverse_ ? e.left : e.right;
						else if (0 === s) {
							this.nodeStack_.push(e);
							break;
						} else
							this.nodeStack_.push(e), (e = this.isReverse_ ? e.right : e.left);
				}
				getNext() {
					let e;
					if (0 === this.nodeStack_.length) return null;
					let t = this.nodeStack_.pop();
					if (
						((e = this.resultGenerator_
							? this.resultGenerator_(t.key, t.value)
							: { key: t.key, value: t.value }),
						this.isReverse_)
					)
						for (t = t.left; !t.isEmpty(); )
							this.nodeStack_.push(t), (t = t.right);
					else
						for (t = t.right; !t.isEmpty(); )
							this.nodeStack_.push(t), (t = t.left);
					return e;
				}
				hasNext() {
					return this.nodeStack_.length > 0;
				}
				peek() {
					if (0 === this.nodeStack_.length) return null;
					const e = this.nodeStack_[this.nodeStack_.length - 1];
					return this.resultGenerator_
						? this.resultGenerator_(e.key, e.value)
						: { key: e.key, value: e.value };
				}
			}
			class eG {
				constructor(e, t, n, i, r) {
					(this.key = e),
						(this.value = t),
						(this.color = null != n ? n : eG.RED),
						(this.left = null != i ? i : eX.EMPTY_NODE),
						(this.right = null != r ? r : eX.EMPTY_NODE);
				}
				copy(e, t, n, i, r) {
					return new eG(
						null != e ? e : this.key,
						null != t ? t : this.value,
						null != n ? n : this.color,
						null != i ? i : this.left,
						null != r ? r : this.right,
					);
				}
				count() {
					return this.left.count() + 1 + this.right.count();
				}
				isEmpty() {
					return !1;
				}
				inorderTraversal(e) {
					return (
						this.left.inorderTraversal(e) ||
						!!e(this.key, this.value) ||
						this.right.inorderTraversal(e)
					);
				}
				reverseTraversal(e) {
					return (
						this.right.reverseTraversal(e) ||
						e(this.key, this.value) ||
						this.left.reverseTraversal(e)
					);
				}
				min_() {
					return this.left.isEmpty() ? this : this.left.min_();
				}
				minKey() {
					return this.min_().key;
				}
				maxKey() {
					return this.right.isEmpty() ? this.key : this.right.maxKey();
				}
				insert(e, t, n) {
					let i = this,
						r = n(e, i.key);
					return (i =
						r < 0
							? i.copy(null, null, null, i.left.insert(e, t, n), null)
							: 0 === r
								? i.copy(null, t, null, null, null)
								: i.copy(
										null,
										null,
										null,
										null,
										i.right.insert(e, t, n),
									)).fixUp_();
				}
				removeMin_() {
					if (this.left.isEmpty()) return eX.EMPTY_NODE;
					let e = this;
					return (
						e.left.isRed_() || e.left.left.isRed_() || (e = e.moveRedLeft_()),
						(e = e.copy(null, null, null, e.left.removeMin_(), null)).fixUp_()
					);
				}
				remove(e, t) {
					let n, i;
					if (((n = this), 0 > t(e, n.key)))
						n.left.isEmpty() ||
							n.left.isRed_() ||
							n.left.left.isRed_() ||
							(n = n.moveRedLeft_()),
							(n = n.copy(null, null, null, n.left.remove(e, t), null));
					else {
						if (
							(n.left.isRed_() && (n = n.rotateRight_()),
							n.right.isEmpty() ||
								n.right.isRed_() ||
								n.right.left.isRed_() ||
								(n = n.moveRedRight_()),
							0 === t(e, n.key))
						) {
							if (n.right.isEmpty()) return eX.EMPTY_NODE;
							(i = n.right.min_()),
								(n = n.copy(i.key, i.value, null, null, n.right.removeMin_()));
						}
						n = n.copy(null, null, null, null, n.right.remove(e, t));
					}
					return n.fixUp_();
				}
				isRed_() {
					return this.color;
				}
				fixUp_() {
					let e = this;
					return (
						e.right.isRed_() && !e.left.isRed_() && (e = e.rotateLeft_()),
						e.left.isRed_() && e.left.left.isRed_() && (e = e.rotateRight_()),
						e.left.isRed_() && e.right.isRed_() && (e = e.colorFlip_()),
						e
					);
				}
				moveRedLeft_() {
					let e = this.colorFlip_();
					return (
						e.right.left.isRed_() &&
							(e = (e = (e = e.copy(
								null,
								null,
								null,
								null,
								e.right.rotateRight_(),
							)).rotateLeft_()).colorFlip_()),
						e
					);
				}
				moveRedRight_() {
					let e = this.colorFlip_();
					return (
						e.left.left.isRed_() && (e = (e = e.rotateRight_()).colorFlip_()), e
					);
				}
				rotateLeft_() {
					const e = this.copy(null, null, eG.RED, null, this.right.left);
					return this.right.copy(null, null, this.color, e, null);
				}
				rotateRight_() {
					const e = this.copy(null, null, eG.RED, this.left.right, null);
					return this.left.copy(null, null, this.color, null, e);
				}
				colorFlip_() {
					const e = this.left.copy(null, null, !this.left.color, null, null),
						t = this.right.copy(null, null, !this.right.color, null, null);
					return this.copy(null, null, !this.color, e, t);
				}
				checkMaxDepth_() {
					return Math.pow(2, this.check_()) <= this.count() + 1;
				}
				check_() {
					if (this.isRed_() && this.left.isRed_())
						throw Error(
							"Red node has red child(" + this.key + "," + this.value + ")",
						);
					if (this.right.isRed_())
						throw Error(
							"Right child of (" + this.key + "," + this.value + ") is red",
						);
					const e = this.left.check_();
					if (e === this.right.check_()) return e + +!this.isRed_();
					throw Error("Black depths differ");
				}
			}
			(eG.RED = !0), (eG.BLACK = !1);
			class eQ {
				copy(e, t, n, i, r) {
					return this;
				}
				insert(e, t, n) {
					return new eG(e, t, null);
				}
				remove(e, t) {
					return this;
				}
				count() {
					return 0;
				}
				isEmpty() {
					return !0;
				}
				inorderTraversal(e) {
					return !1;
				}
				reverseTraversal(e) {
					return !1;
				}
				minKey() {
					return null;
				}
				maxKey() {
					return null;
				}
				check_() {
					return 0;
				}
				isRed_() {
					return !1;
				}
			}
			class eX {
				constructor(e, t = eX.EMPTY_NODE) {
					(this.comparator_ = e), (this.root_ = t);
				}
				insert(e, t) {
					return new eX(
						this.comparator_,
						this.root_
							.insert(e, t, this.comparator_)
							.copy(null, null, eG.BLACK, null, null),
					);
				}
				remove(e) {
					return new eX(
						this.comparator_,
						this.root_
							.remove(e, this.comparator_)
							.copy(null, null, eG.BLACK, null, null),
					);
				}
				get(e) {
					let t;
					let n = this.root_;
					while (!n.isEmpty()) {
						if (0 === (t = this.comparator_(e, n.key))) return n.value;
						t < 0 ? (n = n.left) : t > 0 && (n = n.right);
					}
					return null;
				}
				getPredecessorKey(e) {
					let t,
						n = this.root_,
						i = null;
					while (!n.isEmpty()) {
						if (0 === (t = this.comparator_(e, n.key))) {
							if (n.left.isEmpty()) {
								if (i) return i.key;
								return null;
							}
							for (n = n.left; !n.right.isEmpty(); ) n = n.right;
							return n.key;
						}
						t < 0 ? (n = n.left) : t > 0 && ((i = n), (n = n.right));
					}
					throw Error(
						"Attempted to find predecessor key for a nonexistent key.  What gives?",
					);
				}
				isEmpty() {
					return this.root_.isEmpty();
				}
				count() {
					return this.root_.count();
				}
				minKey() {
					return this.root_.minKey();
				}
				maxKey() {
					return this.root_.maxKey();
				}
				inorderTraversal(e) {
					return this.root_.inorderTraversal(e);
				}
				reverseTraversal(e) {
					return this.root_.reverseTraversal(e);
				}
				getIterator(e) {
					return new e$(this.root_, null, this.comparator_, !1, e);
				}
				getIteratorFrom(e, t) {
					return new e$(this.root_, e, this.comparator_, !1, t);
				}
				getReverseIteratorFrom(e, t) {
					return new e$(this.root_, e, this.comparator_, !0, t);
				}
				getReverseIterator(e) {
					return new e$(this.root_, null, this.comparator_, !0, e);
				}
			}
			function eJ(e, t) {
				return V(e.name, t.name);
			}
			function eZ(e, t) {
				return V(e, t);
			}
			eX.EMPTY_NODE = new eQ();
			const e0 = (e) =>
					"number" == typeof e ? "number:" + X(e) : "string:" + e,
				e1 = (e) => {
					if (e.isLeafNode()) {
						const t = e.val();
						(0, m.vA)(
							"string" == typeof t ||
								"number" == typeof t ||
								("object" == typeof t && (0, m.gR)(t, ".sv")),
							"Priority must be a string or number.",
						);
					} else
						(0, m.vA)(e === r || e.isEmpty(), "priority of unexpected type.");
					(0, m.vA)(
						e === r || e.getPriority().isEmpty(),
						"Priority nodes can't have a priority of their own.",
					);
				};
			class e2 {
				constructor(e, t = e2.__childrenNodeConstructor.EMPTY_NODE) {
					(this.value_ = e),
						(this.priorityNode_ = t),
						(this.lazyHash_ = null),
						(0, m.vA)(
							void 0 !== this.value_ && null !== this.value_,
							"LeafNode shouldn't be created with null/undefined value.",
						),
						e1(this.priorityNode_);
				}
				static set __childrenNodeConstructor(e) {
					s = e;
				}
				static get __childrenNodeConstructor() {
					return s;
				}
				isLeafNode() {
					return !0;
				}
				getPriority() {
					return this.priorityNode_;
				}
				updatePriority(e) {
					return new e2(this.value_, e);
				}
				getImmediateChild(e) {
					return ".priority" === e
						? this.priorityNode_
						: e2.__childrenNodeConstructor.EMPTY_NODE;
				}
				getChild(e) {
					return eF(e)
						? this
						: ".priority" === eS(e)
							? this.priorityNode_
							: e2.__childrenNodeConstructor.EMPTY_NODE;
				}
				hasChild() {
					return !1;
				}
				getPredecessorChildName(e, t) {
					return null;
				}
				updateImmediateChild(e, t) {
					return ".priority" === e
						? this.updatePriority(t)
						: t.isEmpty() && ".priority" !== e
							? this
							: e2.__childrenNodeConstructor.EMPTY_NODE.updateImmediateChild(
									e,
									t,
								).updatePriority(this.priorityNode_);
				}
				updateChild(e, t) {
					const n = eS(e);
					return null === n
						? t
						: t.isEmpty() && ".priority" !== n
							? this
							: ((0, m.vA)(
									".priority" !== n || 1 === eN(e),
									".priority must be the last token in a path",
								),
								this.updateImmediateChild(
									n,
									e2.__childrenNodeConstructor.EMPTY_NODE.updateChild(ex(e), t),
								));
				}
				isEmpty() {
					return !1;
				}
				numChildren() {
					return 0;
				}
				forEachChild(e, t) {
					return !1;
				}
				val(e) {
					return e && !this.getPriority().isEmpty()
						? {
								".value": this.getValue(),
								".priority": this.getPriority().val(),
							}
						: this.getValue();
				}
				hash() {
					if (null === this.lazyHash_) {
						let e = "";
						this.priorityNode_.isEmpty() ||
							(e += "priority:" + e0(this.priorityNode_.val()) + ":");
						const t = typeof this.value_;
						(e += t + ":"),
							"number" === t ? (e += X(this.value_)) : (e += this.value_),
							(this.lazyHash_ = P(e));
					}
					return this.lazyHash_;
				}
				getValue() {
					return this.value_;
				}
				compareTo(e) {
					return e === e2.__childrenNodeConstructor.EMPTY_NODE
						? 1
						: e instanceof e2.__childrenNodeConstructor
							? -1
							: ((0, m.vA)(e.isLeafNode(), "Unknown node type"),
								this.compareToLeafNode_(e));
				}
				compareToLeafNode_(e) {
					const t = typeof e.value_,
						n = typeof this.value_,
						i = e2.VALUE_TYPE_ORDER.indexOf(t),
						r = e2.VALUE_TYPE_ORDER.indexOf(n);
					return ((0, m.vA)(i >= 0, "Unknown leaf type: " + t),
					(0, m.vA)(r >= 0, "Unknown leaf type: " + n),
					i !== r)
						? r - i
						: "object" === n
							? 0
							: this.value_ < e.value_
								? -1
								: +(this.value_ !== e.value_);
				}
				withIndex() {
					return this;
				}
				isIndexed() {
					return !0;
				}
				equals(e) {
					return (
						e === this ||
						(!!e.isLeafNode() &&
							this.value_ === e.value_ &&
							this.priorityNode_.equals(e.priorityNode_))
					);
				}
			}
			e2.VALUE_TYPE_ORDER = ["object", "boolean", "number", "string"];
			class e3 extends eV {
				compare(e, t) {
					const n = e.node.getPriority(),
						i = t.node.getPriority(),
						r = n.compareTo(i);
					return 0 === r ? V(e.name, t.name) : r;
				}
				isDefinedOn(e) {
					return !e.getPriority().isEmpty();
				}
				indexedValueChanged(e, t) {
					return !e.getPriority().equals(t.getPriority());
				}
				minPost() {
					return ez.MIN;
				}
				maxPost() {
					return new ez(z, new e2("[PRIORITY-POST]", l));
				}
				makePost(e, t) {
					return new ez(t, new e2("[PRIORITY-POST]", o(e)));
				}
				toString() {
					return ".priority";
				}
			}
			const e4 = new e3(),
				e5 = Math.log(2);
			class e8 {
				constructor(e) {
					(this.count = Number.parseInt(Math.log(e + 1) / e5, 10)),
						(this.current_ = this.count - 1);
					const t = Number.parseInt(Array(this.count + 1).join("1"), 2);
					this.bits_ = (e + 1) & t;
				}
				nextBitIsOne() {
					const e = !(this.bits_ & (1 << this.current_));
					return this.current_--, e;
				}
			}
			const e6 = (e, t, n, i) => {
					e.sort(t);
					const r = (t, i) => {
						let s, o;
						const l = i - t;
						if (0 === l) return null;
						if (1 === l)
							return (
								(s = e[t]), new eG(n ? n(s) : s, s.node, eG.BLACK, null, null)
							);
						{
							const o = Number.parseInt(l / 2, 10) + t,
								a = r(t, o),
								h = r(o + 1, i);
							return (s = e[o]), new eG(n ? n(s) : s, s.node, eG.BLACK, a, h);
						}
					};
					return new eX(
						i || t,
						((t) => {
							let i = null,
								s = null,
								o = e.length,
								l = (t, i) => {
									const s = o - t,
										l = o;
									o -= t;
									const h = r(s + 1, l),
										u = e[s];
									a(new eG(n ? n(u) : u, u.node, i, null, h));
								},
								a = (e) => {
									i ? (i.left = e) : (s = e), (i = e);
								};
							for (let e = 0; e < t.count; ++e) {
								const n = t.nextBitIsOne(),
									i = Math.pow(2, t.count - (e + 1));
								n ? l(i, eG.BLACK) : (l(i, eG.BLACK), l(i, eG.RED));
							}
							return s;
						})(new e8(e.length)),
					);
				},
				e7 = {};
			class e9 {
				constructor(e, t) {
					(this.indexes_ = e), (this.indexSet_ = t);
				}
				static get Default() {
					return (
						(0, m.vA)(e7 && e4, "ChildrenNode.ts has not been loaded"),
						(a = a || new e9({ ".priority": e7 }, { ".priority": e4 }))
					);
				}
				get(e) {
					const t = (0, m.yw)(this.indexes_, e);
					if (!t) throw Error("No index defined for " + e);
					return t instanceof eX ? t : null;
				}
				hasIndex(e) {
					return (0, m.gR)(this.indexSet_, e.toString());
				}
				addIndex(e, t) {
					let n;
					(0, m.vA)(
						e !== eK,
						"KeyIndex always exists and isn't meant to be added to the IndexMap.",
					);
					let i = [],
						r = !1,
						s = t.getIterator(ez.Wrap),
						o = s.getNext();
					while (o)
						(r = r || e.isDefinedOn(o.node)), i.push(o), (o = s.getNext());
					n = r ? e6(i, e.getCompare()) : e7;
					const l = e.toString(),
						a = Object.assign({}, this.indexSet_);
					a[l] = e;
					const h = Object.assign({}, this.indexes_);
					return (h[l] = n), new e9(h, a);
				}
				addToIndexes(e, t) {
					return new e9(
						(0, m.kH)(this.indexes_, (n, i) => {
							const r = (0, m.yw)(this.indexSet_, i);
							if (
								((0, m.vA)(r, "Missing index implementation for " + i),
								n === e7)
							) {
								if (!r.isDefinedOn(e.node)) return e7;
								{
									let n = [],
										i = t.getIterator(ez.Wrap),
										s = i.getNext();
									while (s) s.name !== e.name && n.push(s), (s = i.getNext());
									return n.push(e), e6(n, r.getCompare());
								}
							}
							{
								let i = t.get(e.name),
									r = n;
								return (
									i && (r = r.remove(new ez(e.name, i))), r.insert(e, e.node)
								);
							}
						}),
						this.indexSet_,
					);
				}
				removeFromIndexes(e, t) {
					return new e9(
						(0, m.kH)(this.indexes_, (n) => {
							if (n === e7) return n;
							{
								const i = t.get(e.name);
								return i ? n.remove(new ez(e.name, i)) : n;
							}
						}),
						this.indexSet_,
					);
				}
			}
			class te {
				constructor(e, t, n) {
					(this.children_ = e),
						(this.priorityNode_ = t),
						(this.indexMap_ = n),
						(this.lazyHash_ = null),
						this.priorityNode_ && e1(this.priorityNode_),
						this.children_.isEmpty() &&
							(0, m.vA)(
								!this.priorityNode_ || this.priorityNode_.isEmpty(),
								"An empty node cannot have a priority",
							);
				}
				static get EMPTY_NODE() {
					return h || (h = new te(new eX(eZ), null, e9.Default));
				}
				isLeafNode() {
					return !1;
				}
				getPriority() {
					return this.priorityNode_ || h;
				}
				updatePriority(e) {
					return this.children_.isEmpty()
						? this
						: new te(this.children_, e, this.indexMap_);
				}
				getImmediateChild(e) {
					if (".priority" === e) return this.getPriority();
					{
						const t = this.children_.get(e);
						return null === t ? h : t;
					}
				}
				getChild(e) {
					const t = eS(e);
					return null === t ? this : this.getImmediateChild(t).getChild(ex(e));
				}
				hasChild(e) {
					return null !== this.children_.get(e);
				}
				updateImmediateChild(e, t) {
					if (
						((0, m.vA)(t, "We should always be passing snapshot nodes"),
						".priority" === e)
					)
						return this.updatePriority(t);
					{
						let n, i;
						const r = new ez(e, t);
						t.isEmpty()
							? ((n = this.children_.remove(e)),
								(i = this.indexMap_.removeFromIndexes(r, this.children_)))
							: ((n = this.children_.insert(e, t)),
								(i = this.indexMap_.addToIndexes(r, this.children_)));
						const s = n.isEmpty() ? h : this.priorityNode_;
						return new te(n, s, i);
					}
				}
				updateChild(e, t) {
					const n = eS(e);
					if (null === n) return t;
					{
						(0, m.vA)(
							".priority" !== eS(e) || 1 === eN(e),
							".priority must be the last token in a path",
						);
						const i = this.getImmediateChild(n).updateChild(ex(e), t);
						return this.updateImmediateChild(n, i);
					}
				}
				isEmpty() {
					return this.children_.isEmpty();
				}
				numChildren() {
					return this.children_.count();
				}
				val(e) {
					if (this.isEmpty()) return null;
					let t = {},
						n = 0,
						i = 0,
						r = !0;
					if (
						(this.forEachChild(e4, (s, o) => {
							(t[s] = o.val(e)),
								n++,
								r && te.INTEGER_REGEXP_.test(s)
									? (i = Math.max(i, Number(s)))
									: (r = !1);
						}),
						e || !r || !(i < 2 * n))
					)
						return (
							e &&
								!this.getPriority().isEmpty() &&
								(t[".priority"] = this.getPriority().val()),
							t
						);
					{
						const e = [];
						for (const n in t) e[n] = t[n];
						return e;
					}
				}
				hash() {
					if (null === this.lazyHash_) {
						let e = "";
						this.getPriority().isEmpty() ||
							(e += "priority:" + e0(this.getPriority().val()) + ":"),
							this.forEachChild(e4, (t, n) => {
								const i = n.hash();
								"" !== i && (e += ":" + t + ":" + i);
							}),
							(this.lazyHash_ = "" === e ? "" : P(e));
					}
					return this.lazyHash_;
				}
				getPredecessorChildName(e, t, n) {
					const i = this.resolveIndex_(n);
					if (!i) return this.children_.getPredecessorKey(e);
					{
						const n = i.getPredecessorKey(new ez(e, t));
						return n ? n.name : null;
					}
				}
				getFirstChildName(e) {
					const t = this.resolveIndex_(e);
					if (!t) return this.children_.minKey();
					{
						const e = t.minKey();
						return e && e.name;
					}
				}
				getFirstChild(e) {
					const t = this.getFirstChildName(e);
					return t ? new ez(t, this.children_.get(t)) : null;
				}
				getLastChildName(e) {
					const t = this.resolveIndex_(e);
					if (!t) return this.children_.maxKey();
					{
						const e = t.maxKey();
						return e && e.name;
					}
				}
				getLastChild(e) {
					const t = this.getLastChildName(e);
					return t ? new ez(t, this.children_.get(t)) : null;
				}
				forEachChild(e, t) {
					const n = this.resolveIndex_(e);
					return n
						? n.inorderTraversal((e) => t(e.name, e.node))
						: this.children_.inorderTraversal(t);
				}
				getIterator(e) {
					return this.getIteratorFrom(e.minPost(), e);
				}
				getIteratorFrom(e, t) {
					const n = this.resolveIndex_(t);
					if (n) return n.getIteratorFrom(e, (e) => e);
					{
						let n = this.children_.getIteratorFrom(e.name, ez.Wrap),
							i = n.peek();
						while (null != i && 0 > t.compare(i, e))
							n.getNext(), (i = n.peek());
						return n;
					}
				}
				getReverseIterator(e) {
					return this.getReverseIteratorFrom(e.maxPost(), e);
				}
				getReverseIteratorFrom(e, t) {
					const n = this.resolveIndex_(t);
					if (n) return n.getReverseIteratorFrom(e, (e) => e);
					{
						let n = this.children_.getReverseIteratorFrom(e.name, ez.Wrap),
							i = n.peek();
						while (null != i && t.compare(i, e) > 0)
							n.getNext(), (i = n.peek());
						return n;
					}
				}
				compareTo(e) {
					return this.isEmpty()
						? e.isEmpty()
							? 0
							: -1
						: e.isLeafNode() || e.isEmpty()
							? 1
							: e === tn
								? -1
								: 0;
				}
				withIndex(e) {
					if (e === eK || this.indexMap_.hasIndex(e)) return this;
					{
						const t = this.indexMap_.addIndex(e, this.children_);
						return new te(this.children_, this.priorityNode_, t);
					}
				}
				isIndexed(e) {
					return e === eK || this.indexMap_.hasIndex(e);
				}
				equals(e) {
					if (e === this) return !0;
					if (e.isLeafNode() || !this.getPriority().equals(e.getPriority()))
						return !1;
					if (this.children_.count() !== e.children_.count()) return !1;
					{
						let t = this.getIterator(e4),
							n = e.getIterator(e4),
							i = t.getNext(),
							r = n.getNext();
						while (i && r) {
							if (i.name !== r.name || !i.node.equals(r.node)) return !1;
							(i = t.getNext()), (r = n.getNext());
						}
						return null === i && null === r;
					}
				}
				resolveIndex_(e) {
					return e === eK ? null : this.indexMap_.get(e.toString());
				}
			}
			te.INTEGER_REGEXP_ = /^(0|[1-9]\d*)$/;
			class tt extends te {
				constructor() {
					super(new eX(eZ), te.EMPTY_NODE, e9.Default);
				}
				compareTo(e) {
					return +(e !== this);
				}
				equals(e) {
					return e === this;
				}
				getPriority() {
					return this;
				}
				getImmediateChild(e) {
					return te.EMPTY_NODE;
				}
				isEmpty() {
					return !1;
				}
			}
			const tn = new tt();
			function ti(e, t = null) {
				if (null === e) return te.EMPTY_NODE;
				if (
					("object" == typeof e && ".priority" in e && (t = e[".priority"]),
					(0, m.vA)(
						null === t ||
							"string" == typeof t ||
							"number" == typeof t ||
							("object" == typeof t && ".sv" in t),
						"Invalid priority type found: " + typeof t,
					),
					"object" == typeof e &&
						".value" in e &&
						null !== e[".value"] &&
						(e = e[".value"]),
					"object" != typeof e || ".sv" in e)
				)
					return new e2(e, ti(t));
				if (e instanceof Array) {
					let n = te.EMPTY_NODE;
					return (
						Q(e, (t, i) => {
							if ((0, m.gR)(e, t) && "." !== t.substring(0, 1)) {
								const e = ti(i);
								(e.isLeafNode() || !e.isEmpty()) &&
									(n = n.updateImmediateChild(t, e));
							}
						}),
						n.updatePriority(ti(t))
					);
				}
				{
					let n = [],
						i = !1;
					if (
						(Q(e, (e, t) => {
							if ("." !== e.substring(0, 1)) {
								const r = ti(t);
								r.isEmpty() ||
									((i = i || !r.getPriority().isEmpty()), n.push(new ez(e, r)));
							}
						}),
						0 === n.length)
					)
						return te.EMPTY_NODE;
					const r = e6(n, eJ, (e) => e.name, eZ);
					if (!i) return new te(r, ti(t), e9.Default);
					{
						const e = e6(n, e4.getCompare());
						return new te(
							r,
							ti(t),
							new e9({ ".priority": e }, { ".priority": e4 }),
						);
					}
				}
			}
			Object.defineProperties(ez, {
				MIN: { value: new ez(Y, te.EMPTY_NODE) },
				MAX: { value: new ez(z, tn) },
			}),
				(eB.__EMPTY_NODE = te.EMPTY_NODE),
				(e2.__childrenNodeConstructor = te),
				(r = tn),
				(l = tn),
				(o = ti);
			class tr extends eV {
				constructor(e) {
					super(),
						(this.indexPath_ = e),
						(0, m.vA)(
							!eF(e) && ".priority" !== eS(e),
							"Can't create PathIndex with empty path or .priority key",
						);
				}
				extractChild(e) {
					return e.getChild(this.indexPath_);
				}
				isDefinedOn(e) {
					return !e.getChild(this.indexPath_).isEmpty();
				}
				compare(e, t) {
					const n = this.extractChild(e.node),
						i = this.extractChild(t.node),
						r = n.compareTo(i);
					return 0 === r ? V(e.name, t.name) : r;
				}
				makePost(e, t) {
					const n = ti(e);
					return new ez(t, te.EMPTY_NODE.updateChild(this.indexPath_, n));
				}
				maxPost() {
					return new ez(z, te.EMPTY_NODE.updateChild(this.indexPath_, tn));
				}
				toString() {
					return eR(this.indexPath_, 0).join("/");
				}
			}
			class ts extends eV {
				compare(e, t) {
					const n = e.node.compareTo(t.node);
					return 0 === n ? V(e.name, t.name) : n;
				}
				isDefinedOn(e) {
					return !0;
				}
				indexedValueChanged(e, t) {
					return !e.equals(t);
				}
				minPost() {
					return ez.MIN;
				}
				maxPost() {
					return ez.MAX;
				}
				makePost(e, t) {
					return new ez(t, ti(e));
				}
				toString() {
					return ".value";
				}
			}
			const to = new ts();
			function tl(e) {
				return { type: "value", snapshotNode: e };
			}
			function ta(e, t) {
				return { type: "child_added", snapshotNode: t, childName: e };
			}
			function th(e, t) {
				return { type: "child_removed", snapshotNode: t, childName: e };
			}
			function tu(e, t, n) {
				return {
					type: "child_changed",
					snapshotNode: t,
					childName: e,
					oldSnap: n,
				};
			}
			class tc {
				constructor(e) {
					this.index_ = e;
				}
				updateChild(e, t, n, i, r, s) {
					(0, m.vA)(
						e.isIndexed(this.index_),
						"A node must be indexed if only a child is updated",
					);
					const o = e.getImmediateChild(t);
					return o.getChild(i).equals(n.getChild(i)) &&
						o.isEmpty() === n.isEmpty()
						? e
						: (null != s &&
									(n.isEmpty()
										? e.hasChild(t)
											? s.trackChildChange(th(t, o))
											: (0, m.vA)(
													e.isLeafNode(),
													"A child remove without an old child only makes sense on a leaf node",
												)
										: o.isEmpty()
											? s.trackChildChange(ta(t, n))
											: s.trackChildChange(tu(t, n, o))),
								e.isLeafNode() && n.isEmpty())
							? e
							: e.updateImmediateChild(t, n).withIndex(this.index_);
				}
				updateFullNode(e, t, n) {
					return (
						null == n ||
							(e.isLeafNode() ||
								e.forEachChild(e4, (e, i) => {
									t.hasChild(e) || n.trackChildChange(th(e, i));
								}),
							t.isLeafNode() ||
								t.forEachChild(e4, (t, i) => {
									if (e.hasChild(t)) {
										const r = e.getImmediateChild(t);
										r.equals(i) || n.trackChildChange(tu(t, i, r));
									} else n.trackChildChange(ta(t, i));
								})),
						t.withIndex(this.index_)
					);
				}
				updatePriority(e, t) {
					return e.isEmpty() ? te.EMPTY_NODE : e.updatePriority(t);
				}
				filtersNodes() {
					return !1;
				}
				getIndexedFilter() {
					return this;
				}
				getIndex() {
					return this.index_;
				}
			}
			class td {
				constructor(e) {
					(this.indexedFilter_ = new tc(e.getIndex())),
						(this.index_ = e.getIndex()),
						(this.startPost_ = td.getStartPost_(e)),
						(this.endPost_ = td.getEndPost_(e)),
						(this.startIsInclusive_ = !e.startAfterSet_),
						(this.endIsInclusive_ = !e.endBeforeSet_);
				}
				getStartPost() {
					return this.startPost_;
				}
				getEndPost() {
					return this.endPost_;
				}
				matches(e) {
					const t = this.startIsInclusive_
							? 0 >= this.index_.compare(this.getStartPost(), e)
							: 0 > this.index_.compare(this.getStartPost(), e),
						n = this.endIsInclusive_
							? 0 >= this.index_.compare(e, this.getEndPost())
							: 0 > this.index_.compare(e, this.getEndPost());
					return t && n;
				}
				updateChild(e, t, n, i, r, s) {
					return (
						this.matches(new ez(t, n)) || (n = te.EMPTY_NODE),
						this.indexedFilter_.updateChild(e, t, n, i, r, s)
					);
				}
				updateFullNode(e, t, n) {
					t.isLeafNode() && (t = te.EMPTY_NODE);
					let i = t.withIndex(this.index_);
					i = i.updatePriority(te.EMPTY_NODE);
					return (
						t.forEachChild(e4, (e, t) => {
							this.matches(new ez(e, t)) ||
								(i = i.updateImmediateChild(e, te.EMPTY_NODE));
						}),
						this.indexedFilter_.updateFullNode(e, i, n)
					);
				}
				updatePriority(e, t) {
					return e;
				}
				filtersNodes() {
					return !0;
				}
				getIndexedFilter() {
					return this.indexedFilter_;
				}
				getIndex() {
					return this.index_;
				}
				static getStartPost_(e) {
					if (!e.hasStart()) return e.getIndex().minPost();
					{
						const t = e.getIndexStartName();
						return e.getIndex().makePost(e.getIndexStartValue(), t);
					}
				}
				static getEndPost_(e) {
					if (!e.hasEnd()) return e.getIndex().maxPost();
					{
						const t = e.getIndexEndName();
						return e.getIndex().makePost(e.getIndexEndValue(), t);
					}
				}
			}
			class t_ {
				constructor(e) {
					(this.withinDirectionalStart = (e) =>
						this.reverse_ ? this.withinEndPost(e) : this.withinStartPost(e)),
						(this.withinDirectionalEnd = (e) =>
							this.reverse_ ? this.withinStartPost(e) : this.withinEndPost(e)),
						(this.withinStartPost = (e) => {
							const t = this.index_.compare(
								this.rangedFilter_.getStartPost(),
								e,
							);
							return this.startIsInclusive_ ? t <= 0 : t < 0;
						}),
						(this.withinEndPost = (e) => {
							const t = this.index_.compare(e, this.rangedFilter_.getEndPost());
							return this.endIsInclusive_ ? t <= 0 : t < 0;
						}),
						(this.rangedFilter_ = new td(e)),
						(this.index_ = e.getIndex()),
						(this.limit_ = e.getLimit()),
						(this.reverse_ = !e.isViewFromLeft()),
						(this.startIsInclusive_ = !e.startAfterSet_),
						(this.endIsInclusive_ = !e.endBeforeSet_);
				}
				updateChild(e, t, n, i, r, s) {
					return (this.rangedFilter_.matches(new ez(t, n)) ||
						(n = te.EMPTY_NODE),
					e.getImmediateChild(t).equals(n))
						? e
						: e.numChildren() < this.limit_
							? this.rangedFilter_
									.getIndexedFilter()
									.updateChild(e, t, n, i, r, s)
							: this.fullLimitUpdateChild_(e, t, n, r, s);
				}
				updateFullNode(e, t, n) {
					let i;
					if (t.isLeafNode() || t.isEmpty())
						i = te.EMPTY_NODE.withIndex(this.index_);
					else if (
						2 * this.limit_ < t.numChildren() &&
						t.isIndexed(this.index_)
					) {
						let e;
						(i = te.EMPTY_NODE.withIndex(this.index_)),
							(e = this.reverse_
								? t.getReverseIteratorFrom(
										this.rangedFilter_.getEndPost(),
										this.index_,
									)
								: t.getIteratorFrom(
										this.rangedFilter_.getStartPost(),
										this.index_,
									));
						let n = 0;
						while (e.hasNext() && n < this.limit_) {
							const t = e.getNext();
							if (this.withinDirectionalStart(t)) {
								if (this.withinDirectionalEnd(t))
									(i = i.updateImmediateChild(t.name, t.node)), n++;
								else break;
							}
						}
					} else {
						let e;
						(i = (i = t.withIndex(this.index_)).updatePriority(te.EMPTY_NODE)),
							(e = this.reverse_
								? i.getReverseIterator(this.index_)
								: i.getIterator(this.index_));
						let n = 0;
						while (e.hasNext()) {
							const t = e.getNext();
							n < this.limit_ &&
							this.withinDirectionalStart(t) &&
							this.withinDirectionalEnd(t)
								? n++
								: (i = i.updateImmediateChild(t.name, te.EMPTY_NODE));
						}
					}
					return this.rangedFilter_.getIndexedFilter().updateFullNode(e, i, n);
				}
				updatePriority(e, t) {
					return e;
				}
				filtersNodes() {
					return !0;
				}
				getIndexedFilter() {
					return this.rangedFilter_.getIndexedFilter();
				}
				getIndex() {
					return this.index_;
				}
				fullLimitUpdateChild_(e, t, n, i, r) {
					let s;
					if (this.reverse_) {
						const e = this.index_.getCompare();
						s = (t, n) => e(n, t);
					} else s = this.index_.getCompare();
					(0, m.vA)(e.numChildren() === this.limit_, "");
					const o = new ez(t, n),
						l = this.reverse_
							? e.getFirstChild(this.index_)
							: e.getLastChild(this.index_),
						a = this.rangedFilter_.matches(o);
					if (e.hasChild(t)) {
						let h = e.getImmediateChild(t),
							u = i.getChildAfterChild(this.index_, l, this.reverse_);
						while (null != u && (u.name === t || e.hasChild(u.name)))
							u = i.getChildAfterChild(this.index_, u, this.reverse_);
						const c = null == u ? 1 : s(u, o);
						if (a && !n.isEmpty() && c >= 0)
							return (
								null != r && r.trackChildChange(tu(t, n, h)),
								e.updateImmediateChild(t, n)
							);
						{
							null != r && r.trackChildChange(th(t, h));
							const n = e.updateImmediateChild(t, te.EMPTY_NODE);
							return null != u && this.rangedFilter_.matches(u)
								? (null != r && r.trackChildChange(ta(u.name, u.node)),
									n.updateImmediateChild(u.name, u.node))
								: n;
						}
					}
					return n.isEmpty()
						? e
						: a
							? s(l, o) >= 0
								? (null != r &&
										(r.trackChildChange(th(l.name, l.node)),
										r.trackChildChange(ta(t, n))),
									e
										.updateImmediateChild(t, n)
										.updateImmediateChild(l.name, te.EMPTY_NODE))
								: e
							: e;
				}
			}
			class tp {
				constructor() {
					(this.limitSet_ = !1),
						(this.startSet_ = !1),
						(this.startNameSet_ = !1),
						(this.startAfterSet_ = !1),
						(this.endSet_ = !1),
						(this.endNameSet_ = !1),
						(this.endBeforeSet_ = !1),
						(this.limit_ = 0),
						(this.viewFrom_ = ""),
						(this.indexStartValue_ = null),
						(this.indexStartName_ = ""),
						(this.indexEndValue_ = null),
						(this.indexEndName_ = ""),
						(this.index_ = e4);
				}
				hasStart() {
					return this.startSet_;
				}
				isViewFromLeft() {
					return "" === this.viewFrom_
						? this.startSet_
						: "l" === this.viewFrom_;
				}
				getIndexStartValue() {
					return (
						(0, m.vA)(this.startSet_, "Only valid if start has been set"),
						this.indexStartValue_
					);
				}
				getIndexStartName() {
					return ((0, m.vA)(this.startSet_, "Only valid if start has been set"),
					this.startNameSet_)
						? this.indexStartName_
						: Y;
				}
				hasEnd() {
					return this.endSet_;
				}
				getIndexEndValue() {
					return (
						(0, m.vA)(this.endSet_, "Only valid if end has been set"),
						this.indexEndValue_
					);
				}
				getIndexEndName() {
					return ((0, m.vA)(this.endSet_, "Only valid if end has been set"),
					this.endNameSet_)
						? this.indexEndName_
						: z;
				}
				hasLimit() {
					return this.limitSet_;
				}
				hasAnchoredLimit() {
					return this.limitSet_ && "" !== this.viewFrom_;
				}
				getLimit() {
					return (
						(0, m.vA)(this.limitSet_, "Only valid if limit has been set"),
						this.limit_
					);
				}
				getIndex() {
					return this.index_;
				}
				loadsAllData() {
					return !(this.startSet_ || this.endSet_ || this.limitSet_);
				}
				isDefault() {
					return this.loadsAllData() && this.index_ === e4;
				}
				copy() {
					const e = new tp();
					return (
						(e.limitSet_ = this.limitSet_),
						(e.limit_ = this.limit_),
						(e.startSet_ = this.startSet_),
						(e.startAfterSet_ = this.startAfterSet_),
						(e.indexStartValue_ = this.indexStartValue_),
						(e.startNameSet_ = this.startNameSet_),
						(e.indexStartName_ = this.indexStartName_),
						(e.endSet_ = this.endSet_),
						(e.endBeforeSet_ = this.endBeforeSet_),
						(e.indexEndValue_ = this.indexEndValue_),
						(e.endNameSet_ = this.endNameSet_),
						(e.indexEndName_ = this.indexEndName_),
						(e.index_ = this.index_),
						(e.viewFrom_ = this.viewFrom_),
						e
					);
				}
			}
			function tf(e, t, n) {
				const i = e.copy();
				return (
					(i.startSet_ = !0),
					void 0 === t && (t = null),
					(i.indexStartValue_ = t),
					null != n
						? ((i.startNameSet_ = !0), (i.indexStartName_ = n))
						: ((i.startNameSet_ = !1), (i.indexStartName_ = "")),
					i
				);
			}
			function tg(e, t, n) {
				const i = e.copy();
				return (
					(i.endSet_ = !0),
					void 0 === t && (t = null),
					(i.indexEndValue_ = t),
					void 0 !== n
						? ((i.endNameSet_ = !0), (i.indexEndName_ = n))
						: ((i.endNameSet_ = !1), (i.indexEndName_ = "")),
					i
				);
			}
			function tm(e, t) {
				const n = e.copy();
				return (n.index_ = t), n;
			}
			function ty(e) {
				let t;
				const n = {};
				if (e.isDefault()) return n;
				if (
					(e.index_ === e4
						? (t = "$priority")
						: e.index_ === to
							? (t = "$value")
							: e.index_ === eK
								? (t = "$key")
								: ((0, m.vA)(
										e.index_ instanceof tr,
										"Unrecognized index type!",
									),
									(t = e.index_.toString())),
					(n.orderBy = (0, m.As)(t)),
					e.startSet_)
				) {
					const t = e.startAfterSet_ ? "startAfter" : "startAt";
					(n[t] = (0, m.As)(e.indexStartValue_)),
						e.startNameSet_ && (n[t] += "," + (0, m.As)(e.indexStartName_));
				}
				if (e.endSet_) {
					const t = e.endBeforeSet_ ? "endBefore" : "endAt";
					(n[t] = (0, m.As)(e.indexEndValue_)),
						e.endNameSet_ && (n[t] += "," + (0, m.As)(e.indexEndName_));
				}
				return (
					e.limitSet_ &&
						(e.isViewFromLeft()
							? (n.limitToFirst = e.limit_)
							: (n.limitToLast = e.limit_)),
					n
				);
			}
			function tv(e) {
				const t = {};
				if (
					(e.startSet_ &&
						((t.sp = e.indexStartValue_),
						e.startNameSet_ && (t.sn = e.indexStartName_),
						(t.sin = !e.startAfterSet_)),
					e.endSet_ &&
						((t.ep = e.indexEndValue_),
						e.endNameSet_ && (t.en = e.indexEndName_),
						(t.ein = !e.endBeforeSet_)),
					e.limitSet_)
				) {
					t.l = e.limit_;
					let n = e.viewFrom_;
					"" === n && (n = e.isViewFromLeft() ? "l" : "r"), (t.vf = n);
				}
				return e.index_ !== e4 && (t.i = e.index_.toString()), t;
			}
			class tC extends eT {
				constructor(e, t, n, i) {
					super(),
						(this.repoInfo_ = e),
						(this.onDataUpdate_ = t),
						(this.authTokenProvider_ = n),
						(this.appCheckTokenProvider_ = i),
						(this.log_ = q("p:rest:")),
						(this.listens_ = {});
				}
				reportStats(e) {
					throw Error("Method not implemented.");
				}
				static getListenId_(e, t) {
					return void 0 !== t
						? "tag$" + t
						: ((0, m.vA)(
								e._queryParams.isDefault(),
								"should have a tag if it's not a default query.",
							),
							e._path.toString());
				}
				listen(e, t, n, i) {
					const r = e._path.toString();
					this.log_("Listen called for " + r + " " + e._queryIdentifier);
					const s = tC.getListenId_(e, n),
						o = {};
					this.listens_[s] = o;
					const l = ty(e._queryParams);
					this.restRequest_(r + ".json", l, (e, t) => {
						let l = t;
						if (
							(404 === e && ((l = null), (e = null)),
							null === e && this.onDataUpdate_(r, l, !1, n),
							(0, m.yw)(this.listens_, s) === o)
						) {
							let t;
							i(
								e
									? 401 === e
										? "permission_denied"
										: "rest_error:" + e
									: "ok",
								null,
							);
						}
					});
				}
				unlisten(e, t) {
					const n = tC.getListenId_(e, t);
					delete this.listens_[n];
				}
				get(e) {
					const t = ty(e._queryParams),
						n = e._path.toString(),
						i = new m.cY();
					return (
						this.restRequest_(n + ".json", t, (e, t) => {
							let r = t;
							404 === e && ((r = null), (e = null)),
								null === e
									? (this.onDataUpdate_(n, r, !1, null), i.resolve(r))
									: i.reject(Error(r));
						}),
						i.promise
					);
				}
				refreshAuthToken(e) {}
				restRequest_(e, t = {}, n) {
					return (
						(t.format = "export"),
						Promise.all([
							this.authTokenProvider_.getToken(!1),
							this.appCheckTokenProvider_.getToken(!1),
						]).then(([i, r]) => {
							i && i.accessToken && (t.auth = i.accessToken),
								r && r.token && (t.ac = r.token);
							const s =
								(this.repoInfo_.secure ? "https://" : "http://") +
								this.repoInfo_.host +
								e +
								"?ns=" +
								this.repoInfo_.namespace +
								(0, m.Am)(t);
							this.log_("Sending REST request for " + s);
							const o = new XMLHttpRequest();
							(o.onreadystatechange = () => {
								if (n && 4 === o.readyState) {
									this.log_(
										"REST Response for " + s + " received. status:",
										o.status,
										"response:",
										o.responseText,
									);
									let e = null;
									if (o.status >= 200 && o.status < 300) {
										try {
											e = (0, m.$L)(o.responseText);
										} catch (e) {
											W(
												"Failed to parse JSON response for " +
													s +
													": " +
													o.responseText,
											);
										}
										n(null, e);
									} else
										401 !== o.status &&
											404 !== o.status &&
											W(
												"Got unsuccessful REST response for " +
													s +
													" Status: " +
													o.status,
											),
											n(o.status);
									n = null;
								}
							}),
								o.open("GET", s, !0),
								o.send();
						})
					);
				}
			}
			class tw {
				constructor() {
					this.rootNode_ = te.EMPTY_NODE;
				}
				getNode(e) {
					return this.rootNode_.getChild(e);
				}
				updateSnapshot(e, t) {
					this.rootNode_ = this.rootNode_.updateChild(e, t);
				}
			}
			function tT() {
				return { value: null, children: new Map() };
			}
			function tI(e, t, n) {
				if (eF(t)) (e.value = n), e.children.clear();
				else if (null !== e.value) e.value = e.value.updateChild(t, n);
				else {
					const i = eS(t);
					e.children.has(i) || e.children.set(i, tT()),
						tI(e.children.get(i), (t = ex(t)), n);
				}
			}
			function tb(e, t, n) {
				null !== e.value
					? n(t, e.value)
					: ((e, t) => {
							e.children.forEach((e, n) => {
								t(n, e);
							});
						})(e, (e, i) => {
							tb(i, new eE(t.toString() + "/" + e), n);
						});
			}
			class tE {
				constructor(e) {
					(this.collection_ = e), (this.last_ = null);
				}
				get() {
					const e = this.collection_.get(),
						t = Object.assign({}, e);
					return (
						this.last_ &&
							Q(this.last_, (e, n) => {
								t[e] = t[e] - n;
							}),
						(this.last_ = e),
						t
					);
				}
			}
			class tk {
				constructor(e, t) {
					(this.server_ = t),
						(this.statsToReport_ = {}),
						(this.statsListener_ = new tE(e));
					const n = 1e4 + 2e4 * Math.random();
					et(this.reportStats_.bind(this), Math.floor(n));
				}
				reportStats_() {
					let e = this.statsListener_.get(),
						t = {},
						n = !1;
					Q(e, (e, i) => {
						i > 0 &&
							(0, m.gR)(this.statsToReport_, e) &&
							((t[e] = i), (n = !0));
					}),
						n && this.server_.reportStats(t),
						et(
							this.reportStats_.bind(this),
							Math.floor(2 * Math.random() * 3e5),
						);
				}
			}
			function tS() {
				return { fromUser: !0, fromServer: !1, queryId: null, tagged: !1 };
			}
			function tN() {
				return { fromUser: !1, fromServer: !0, queryId: null, tagged: !1 };
			}
			function tx(e) {
				return { fromUser: !1, fromServer: !0, queryId: e, tagged: !0 };
			}
			!((e) => {
				(e[(e.OVERWRITE = 0)] = "OVERWRITE"),
					(e[(e.MERGE = 1)] = "MERGE"),
					(e[(e.ACK_USER_WRITE = 2)] = "ACK_USER_WRITE"),
					(e[(e.LISTEN_COMPLETE = 3)] = "LISTEN_COMPLETE");
			})(p || (p = {}));
			class tP {
				constructor(e, t, n) {
					(this.path = e),
						(this.affectedTree = t),
						(this.revert = n),
						(this.type = p.ACK_USER_WRITE),
						(this.source = tS());
				}
				operationForChild(e) {
					if (!eF(this.path))
						return (
							(0, m.vA)(
								eS(this.path) === e,
								"operationForChild called for unrelated child.",
							),
							new tP(ex(this.path), this.affectedTree, this.revert)
						);
					if (null != this.affectedTree.value)
						return (
							(0, m.vA)(
								this.affectedTree.children.isEmpty(),
								"affectedTree should not have overlapping affected paths.",
							),
							this
						);
					{
						const t = this.affectedTree.subtree(new eE(e));
						return new tP(ek(), t, this.revert);
					}
				}
			}
			class tR {
				constructor(e, t) {
					(this.source = e), (this.path = t), (this.type = p.LISTEN_COMPLETE);
				}
				operationForChild(e) {
					return eF(this.path)
						? new tR(this.source, ek())
						: new tR(this.source, ex(this.path));
				}
			}
			class tA {
				constructor(e, t, n) {
					(this.source = e),
						(this.path = t),
						(this.snap = n),
						(this.type = p.OVERWRITE);
				}
				operationForChild(e) {
					return eF(this.path)
						? new tA(this.source, ek(), this.snap.getImmediateChild(e))
						: new tA(this.source, ex(this.path), this.snap);
				}
			}
			class tD {
				constructor(e, t, n) {
					(this.source = e),
						(this.path = t),
						(this.children = n),
						(this.type = p.MERGE);
				}
				operationForChild(e) {
					if (!eF(this.path))
						return (
							(0, m.vA)(
								eS(this.path) === e,
								"Can't get a merge for a child not on the path of the operation",
							),
							new tD(this.source, ex(this.path), this.children)
						);
					{
						const t = this.children.subtree(new eE(e));
						return t.isEmpty()
							? null
							: t.value
								? new tA(this.source, ek(), t.value)
								: new tD(this.source, ek(), t);
					}
				}
				toString() {
					return (
						"Operation(" +
						this.path +
						": " +
						this.source.toString() +
						" merge: " +
						this.children.toString() +
						")"
					);
				}
			}
			class tF {
				constructor(e, t, n) {
					(this.node_ = e), (this.fullyInitialized_ = t), (this.filtered_ = n);
				}
				isFullyInitialized() {
					return this.fullyInitialized_;
				}
				isFiltered() {
					return this.filtered_;
				}
				isCompleteForPath(e) {
					if (eF(e)) return this.isFullyInitialized() && !this.filtered_;
					const t = eS(e);
					return this.isCompleteForChild(t);
				}
				isCompleteForChild(e) {
					return (
						(this.isFullyInitialized() && !this.filtered_) ||
						this.node_.hasChild(e)
					);
				}
				getNode() {
					return this.node_;
				}
			}
			class tM {
				constructor(e) {
					(this.query_ = e),
						(this.index_ = this.query_._queryParams.getIndex());
				}
			}
			function tq(e, t, n, i, r, s) {
				const o = i.filter((e) => e.type === n);
				o.sort((t, n) =>
					((e, t, n) => {
						if (null == t.childName || null == n.childName)
							throw (0, m.Hk)("Should only compare child_ events.");
						const i = new ez(t.childName, t.snapshotNode),
							r = new ez(n.childName, n.snapshotNode);
						return e.index_.compare(i, r);
					})(e, t, n),
				),
					o.forEach((n) => {
						var i, o, l;
						const a =
							((i = e),
							(o = n),
							(l = s),
							"value" === o.type ||
								"child_removed" === o.type ||
								(o.prevName = l.getPredecessorChildName(
									o.childName,
									o.snapshotNode,
									i.index_,
								)),
							o);
						r.forEach((i) => {
							i.respondsTo(n.type) && t.push(i.createEvent(a, e.query_));
						});
					});
			}
			function tL(e, t) {
				return { eventCache: e, serverCache: t };
			}
			function tO(e, t, n, i) {
				return tL(new tF(t, n, i), e.serverCache);
			}
			function tW(e, t, n, i) {
				return tL(e.eventCache, new tF(t, n, i));
			}
			function tU(e) {
				return e.eventCache.isFullyInitialized()
					? e.eventCache.getNode()
					: null;
			}
			function tH(e) {
				return e.serverCache.isFullyInitialized()
					? e.serverCache.getNode()
					: null;
			}
			const tj = () => (u || (u = new eX(B)), u);
			class tY {
				constructor(e, t = tj()) {
					(this.value = e), (this.children = t);
				}
				static fromObject(e) {
					let t = new tY(null);
					return (
						Q(e, (e, n) => {
							t = t.set(new eE(e), n);
						}),
						t
					);
				}
				isEmpty() {
					return null === this.value && this.children.isEmpty();
				}
				findRootMostMatchingPathAndValue(e, t) {
					if (null != this.value && t(this.value))
						return { path: ek(), value: this.value };
					if (eF(e)) return null;
					{
						const n = eS(e),
							i = this.children.get(n);
						if (null === i) return null;
						{
							const r = i.findRootMostMatchingPathAndValue(ex(e), t);
							return null != r
								? { path: eD(new eE(n), r.path), value: r.value }
								: null;
						}
					}
				}
				findRootMostValueAndPath(e) {
					return this.findRootMostMatchingPathAndValue(e, () => !0);
				}
				subtree(e) {
					if (eF(e)) return this;
					{
						const t = eS(e),
							n = this.children.get(t);
						return null !== n ? n.subtree(ex(e)) : new tY(null);
					}
				}
				set(e, t) {
					if (eF(e)) return new tY(t, this.children);
					{
						const n = eS(e),
							i = (this.children.get(n) || new tY(null)).set(ex(e), t),
							r = this.children.insert(n, i);
						return new tY(this.value, r);
					}
				}
				remove(e) {
					if (eF(e))
						return this.children.isEmpty()
							? new tY(null)
							: new tY(null, this.children);
					{
						const t = eS(e),
							n = this.children.get(t);
						if (!n) return this;
						{
							let i;
							const r = n.remove(ex(e));
							return ((i = r.isEmpty()
								? this.children.remove(t)
								: this.children.insert(t, r)),
							null === this.value && i.isEmpty())
								? new tY(null)
								: new tY(this.value, i);
						}
					}
				}
				get(e) {
					if (eF(e)) return this.value;
					{
						const t = eS(e),
							n = this.children.get(t);
						return n ? n.get(ex(e)) : null;
					}
				}
				setTree(e, t) {
					if (eF(e)) return t;
					{
						let n;
						const i = eS(e),
							r = (this.children.get(i) || new tY(null)).setTree(ex(e), t);
						return (
							(n = r.isEmpty()
								? this.children.remove(i)
								: this.children.insert(i, r)),
							new tY(this.value, n)
						);
					}
				}
				fold(e) {
					return this.fold_(ek(), e);
				}
				fold_(e, t) {
					const n = {};
					return (
						this.children.inorderTraversal((i, r) => {
							n[i] = r.fold_(eD(e, i), t);
						}),
						t(e, this.value, n)
					);
				}
				findOnPath(e, t) {
					return this.findOnPath_(e, ek(), t);
				}
				findOnPath_(e, t, n) {
					const i = !!this.value && n(t, this.value);
					if (i) return i;
					if (eF(e)) return null;
					{
						const i = eS(e),
							r = this.children.get(i);
						return r ? r.findOnPath_(ex(e), eD(t, i), n) : null;
					}
				}
				foreachOnPath(e, t) {
					return this.foreachOnPath_(e, ek(), t);
				}
				foreachOnPath_(e, t, n) {
					if (eF(e)) return this;
					{
						this.value && n(t, this.value);
						const i = eS(e),
							r = this.children.get(i);
						return r ? r.foreachOnPath_(ex(e), eD(t, i), n) : new tY(null);
					}
				}
				foreach(e) {
					this.foreach_(ek(), e);
				}
				foreach_(e, t) {
					this.children.inorderTraversal((n, i) => {
						i.foreach_(eD(e, n), t);
					}),
						this.value && t(e, this.value);
				}
				foreachChild(e) {
					this.children.inorderTraversal((t, n) => {
						n.value && e(t, n.value);
					});
				}
			}
			class tz {
				constructor(e) {
					this.writeTree_ = e;
				}
				static empty() {
					return new tz(new tY(null));
				}
			}
			function tV(e, t, n) {
				if (eF(t)) return new tz(new tY(n));
				{
					const i = e.writeTree_.findRootMostValueAndPath(t);
					if (null != i) {
						let r = i.path,
							s = i.value,
							o = eM(r, t);
						return (s = s.updateChild(o, n)), new tz(e.writeTree_.set(r, s));
					}
					{
						const i = new tY(n);
						return new tz(e.writeTree_.setTree(t, i));
					}
				}
			}
			function tB(e, t, n) {
				let i = e;
				return (
					Q(n, (e, n) => {
						i = tV(i, eD(t, e), n);
					}),
					i
				);
			}
			function tK(e, t) {
				return eF(t)
					? tz.empty()
					: new tz(e.writeTree_.setTree(t, new tY(null)));
			}
			function t$(e, t) {
				return null != tG(e, t);
			}
			function tG(e, t) {
				const n = e.writeTree_.findRootMostValueAndPath(t);
				return null != n
					? e.writeTree_.get(n.path).getChild(eM(n.path, t))
					: null;
			}
			function tQ(e) {
				const t = [],
					n = e.writeTree_.value;
				return (
					null != n
						? n.isLeafNode() ||
							n.forEachChild(e4, (e, n) => {
								t.push(new ez(e, n));
							})
						: e.writeTree_.children.inorderTraversal((e, n) => {
								null != n.value && t.push(new ez(e, n.value));
							}),
					t
				);
			}
			function tX(e, t) {
				if (eF(t)) return e;
				{
					const n = tG(e, t);
					return new tz(null != n ? new tY(n) : e.writeTree_.subtree(t));
				}
			}
			function tJ(e) {
				return e.writeTree_.isEmpty();
			}
			function tZ(e, t) {
				return (function e(t, n, i) {
					if (null != n.value) return i.updateChild(t, n.value);
					{
						let r = null;
						return (
							n.children.inorderTraversal((n, s) => {
								".priority" === n
									? ((0, m.vA)(
											null !== s.value,
											"Priority writes must always be leaf nodes",
										),
										(r = s.value))
									: (i = e(eD(t, n), s, i));
							}),
							i.getChild(t).isEmpty() ||
								null === r ||
								(i = i.updateChild(eD(t, ".priority"), r)),
							i
						);
					}
				})(ek(), e.writeTree_, t);
			}
			function t0(e) {
				return e.visible;
			}
			function t1(e, t, n) {
				let i = tz.empty();
				for (let r = 0; r < e.length; ++r) {
					const s = e[r];
					if (t(s)) {
						let e;
						const t = s.path;
						if (s.snap)
							eO(n, t)
								? (i = tV(i, (e = eM(n, t)), s.snap))
								: eO(t, n) &&
									((e = eM(t, n)), (i = tV(i, ek(), s.snap.getChild(e))));
						else if (s.children) {
							if (eO(n, t)) i = tB(i, (e = eM(n, t)), s.children);
							else if (eO(t, n)) {
								if (eF((e = eM(t, n)))) i = tB(i, ek(), s.children);
								else {
									const t = (0, m.yw)(s.children, eS(e));
									if (t) {
										const n = t.getChild(ex(e));
										i = tV(i, ek(), n);
									}
								}
							}
						} else
							throw (0, m.Hk)("WriteRecord should have .snap or .children");
					}
				}
				return i;
			}
			function t2(e, t, n, i, r) {
				if (i || r) {
					const s = tX(e.visibleWrites, t);
					return !r && tJ(s)
						? n
						: r || null != n || t$(s, ek())
							? tZ(
									t1(
										e.allWrites,
										(e) =>
											(e.visible || r) &&
											(!i || !~i.indexOf(e.writeId)) &&
											(eO(e.path, t) || eO(t, e.path)),
										t,
									),
									n || te.EMPTY_NODE,
								)
							: null;
				}
				{
					const i = tG(e.visibleWrites, t);
					if (null != i) return i;
					{
						const i = tX(e.visibleWrites, t);
						return tJ(i)
							? n
							: null != n || t$(i, ek())
								? tZ(i, n || te.EMPTY_NODE)
								: null;
					}
				}
			}
			function t3(e, t, n, i) {
				return t2(e.writeTree, e.treePath, t, n, i);
			}
			function t4(e, t) {
				return ((e, t, n) => {
					let i = te.EMPTY_NODE,
						r = tG(e.visibleWrites, t);
					if (r)
						return (
							r.isLeafNode() ||
								r.forEachChild(e4, (e, t) => {
									i = i.updateImmediateChild(e, t);
								}),
							i
						);
					if (!n)
						return (
							tQ(tX(e.visibleWrites, t)).forEach((e) => {
								i = i.updateImmediateChild(e.name, e.node);
							}),
							i
						);
					{
						const r = tX(e.visibleWrites, t);
						return (
							n.forEachChild(e4, (e, t) => {
								const n = tZ(tX(r, new eE(e)), t);
								i = i.updateImmediateChild(e, n);
							}),
							tQ(r).forEach((e) => {
								i = i.updateImmediateChild(e.name, e.node);
							}),
							i
						);
					}
				})(e.writeTree, e.treePath, t);
			}
			function t5(e, t, n, i) {
				return ((e, t, n, i, r) => {
					(0, m.vA)(
						i || r,
						"Either existingEventSnap or existingServerSnap must exist",
					);
					const s = eD(t, n);
					if (t$(e.visibleWrites, s)) return null;
					{
						const t = tX(e.visibleWrites, s);
						return tJ(t) ? r.getChild(n) : tZ(t, r.getChild(n));
					}
				})(e.writeTree, e.treePath, t, n, i);
			}
			function t8(e, t) {
				var n, i;
				return (
					(n = e.writeTree), (i = eD(e.treePath, t)), tG(n.visibleWrites, i)
				);
			}
			function t6(e, t, n) {
				return ((e, t, n, i) => {
					const r = eD(t, n),
						s = tG(e.visibleWrites, r);
					return null != s
						? s
						: i.isCompleteForChild(n)
							? tZ(tX(e.visibleWrites, r), i.getNode().getImmediateChild(n))
							: null;
				})(e.writeTree, e.treePath, t, n);
			}
			function t7(e, t) {
				return t9(eD(e.treePath, t), e.writeTree);
			}
			function t9(e, t) {
				return { treePath: e, writeTree: t };
			}
			class ne {
				constructor() {
					this.changeMap = new Map();
				}
				trackChildChange(e) {
					const t = e.type,
						n = e.childName;
					(0, m.vA)(
						"child_added" === t ||
							"child_changed" === t ||
							"child_removed" === t,
						"Only child changes supported for tracking",
					),
						(0, m.vA)(
							".priority" !== n,
							"Only non-priority child changes can be tracked.",
						);
					const i = this.changeMap.get(n);
					if (i) {
						const r = i.type;
						if ("child_added" === t && "child_removed" === r)
							this.changeMap.set(n, tu(n, e.snapshotNode, i.snapshotNode));
						else if ("child_removed" === t && "child_added" === r)
							this.changeMap.delete(n);
						else if ("child_removed" === t && "child_changed" === r)
							this.changeMap.set(n, th(n, i.oldSnap));
						else if ("child_changed" === t && "child_added" === r)
							this.changeMap.set(n, ta(n, e.snapshotNode));
						else if ("child_changed" === t && "child_changed" === r)
							this.changeMap.set(n, tu(n, e.snapshotNode, i.oldSnap));
						else
							throw (0, m.Hk)(
								"Illegal combination of changes: " + e + " occurred after " + i,
							);
					} else this.changeMap.set(n, e);
				}
				getChanges() {
					return Array.from(this.changeMap.values());
				}
			}
			class nt {
				getCompleteChild(e) {
					return null;
				}
				getChildAfterChild(e, t, n) {
					return null;
				}
			}
			const nn = new nt();
			class ni {
				constructor(e, t, n = null) {
					(this.writes_ = e),
						(this.viewCache_ = t),
						(this.optCompleteServerCache_ = n);
				}
				getCompleteChild(e) {
					const t = this.viewCache_.eventCache;
					if (t.isCompleteForChild(e)) return t.getNode().getImmediateChild(e);
					{
						const t =
							null != this.optCompleteServerCache_
								? new tF(this.optCompleteServerCache_, !0, !1)
								: this.viewCache_.serverCache;
						return t6(this.writes_, e, t);
					}
				}
				getChildAfterChild(e, t, n) {
					var i;
					const r =
							null != this.optCompleteServerCache_
								? this.optCompleteServerCache_
								: tH(this.viewCache_),
						s = ((e, t, n, i, r, s, o) => {
							let l;
							const a = tX(e.visibleWrites, t),
								h = tG(a, ek());
							if (null != h) l = h;
							else {
								if (null == n) return [];
								l = tZ(a, n);
							}
							if ((l = l.withIndex(o)).isEmpty() || l.isLeafNode()) return [];
							{
								let e = [],
									t = o.getCompare(),
									n = s
										? l.getReverseIteratorFrom(i, o)
										: l.getIteratorFrom(i, o),
									r = n.getNext();
								while (r && e.length < 1)
									0 !== t(r, i) && e.push(r), (r = n.getNext());
								return e;
							}
						})((i = this.writes_).writeTree, i.treePath, r, t, 1, n, e);
					return 0 === s.length ? null : s[0];
				}
			}
			function nr(e, t, n, i, r, s) {
				const o = t.eventCache;
				if (null != t8(i, n)) return t;
				{
					let l, a;
					if (eF(n)) {
						if (
							((0, m.vA)(
								t.serverCache.isFullyInitialized(),
								"If change path is empty, we must have complete server data",
							),
							t.serverCache.isFiltered())
						) {
							const n = tH(t),
								r = t4(i, n instanceof te ? n : te.EMPTY_NODE);
							l = e.filter.updateFullNode(t.eventCache.getNode(), r, s);
						} else {
							const n = t3(i, tH(t));
							l = e.filter.updateFullNode(t.eventCache.getNode(), n, s);
						}
					} else {
						const h = eS(n);
						if (".priority" === h) {
							(0, m.vA)(
								1 === eN(n),
								"Can't have a priority with additional path components",
							);
							const r = o.getNode(),
								s = t5(i, n, r, (a = t.serverCache.getNode()));
							l = null != s ? e.filter.updatePriority(r, s) : o.getNode();
						} else {
							let u;
							const c = ex(n);
							if (o.isCompleteForChild(h)) {
								a = t.serverCache.getNode();
								const e = t5(i, n, o.getNode(), a);
								u =
									null != e
										? o.getNode().getImmediateChild(h).updateChild(c, e)
										: o.getNode().getImmediateChild(h);
							} else u = t6(i, h, t.serverCache);
							l =
								null != u
									? e.filter.updateChild(o.getNode(), h, u, c, r, s)
									: o.getNode();
						}
					}
					return tO(
						t,
						l,
						o.isFullyInitialized() || eF(n),
						e.filter.filtersNodes(),
					);
				}
			}
			function ns(e, t, n, i, r, s, o, l) {
				let a;
				const h = t.serverCache,
					u = o ? e.filter : e.filter.getIndexedFilter();
				if (eF(n)) a = u.updateFullNode(h.getNode(), i, null);
				else if (u.filtersNodes() && !h.isFiltered()) {
					const e = h.getNode().updateChild(n, i);
					a = u.updateFullNode(h.getNode(), e, null);
				} else {
					const e = eS(n);
					if (!h.isCompleteForPath(n) && eN(n) > 1) return t;
					const r = ex(n),
						s = h.getNode().getImmediateChild(e).updateChild(r, i);
					a =
						".priority" === e
							? u.updatePriority(h.getNode(), s)
							: u.updateChild(h.getNode(), e, s, r, nn, null);
				}
				const c = tW(t, a, h.isFullyInitialized() || eF(n), u.filtersNodes()),
					d = new ni(r, c, s);
				return nr(e, c, n, r, d, l);
			}
			function no(e, t, n, i, r, s, o) {
				let l, a;
				const h = t.eventCache,
					u = new ni(r, t, s);
				if (eF(n))
					(a = e.filter.updateFullNode(t.eventCache.getNode(), i, o)),
						(l = tO(t, a, !0, e.filter.filtersNodes()));
				else {
					const r = eS(n);
					if (".priority" === r)
						(a = e.filter.updatePriority(t.eventCache.getNode(), i)),
							(l = tO(t, a, h.isFullyInitialized(), h.isFiltered()));
					else {
						let s;
						const a = ex(n),
							c = h.getNode().getImmediateChild(r);
						if (eF(a)) s = i;
						else {
							const e = u.getCompleteChild(r);
							s =
								null != e
									? ".priority" === eP(a) && e.getChild(eA(a)).isEmpty()
										? e
										: e.updateChild(a, i)
									: te.EMPTY_NODE;
						}
						l = c.equals(s)
							? t
							: tO(
									t,
									e.filter.updateChild(h.getNode(), r, s, a, u, o),
									h.isFullyInitialized(),
									e.filter.filtersNodes(),
								);
					}
				}
				return l;
			}
			function nl(e, t) {
				return e.eventCache.isCompleteForChild(t);
			}
			function na(e, t, n) {
				return (
					n.foreach((e, n) => {
						t = t.updateChild(e, n);
					}),
					t
				);
			}
			function nh(e, t, n, i, r, s, o, l) {
				let a;
				if (
					t.serverCache.getNode().isEmpty() &&
					!t.serverCache.isFullyInitialized()
				)
					return t;
				let h = t;
				a = eF(n) ? i : new tY(null).setTree(n, i);
				const u = t.serverCache.getNode();
				return (
					a.children.inorderTraversal((n, i) => {
						if (u.hasChild(n)) {
							const a = na(e, t.serverCache.getNode().getImmediateChild(n), i);
							h = ns(e, h, new eE(n), a, r, s, o, l);
						}
					}),
					a.children.inorderTraversal((n, i) => {
						const a = !t.serverCache.isCompleteForChild(n) && null === i.value;
						if (!u.hasChild(n) && !a) {
							const a = na(e, t.serverCache.getNode().getImmediateChild(n), i);
							h = ns(e, h, new eE(n), a, r, s, o, l);
						}
					}),
					h
				);
			}
			class nu {
				constructor(e, t) {
					(this.query_ = e), (this.eventRegistrations_ = []);
					const n = this.query_._queryParams,
						i = new tc(n.getIndex()),
						r = ((e) =>
							e.loadsAllData()
								? new tc(e.getIndex())
								: e.hasLimit()
									? new t_(e)
									: new td(e))(n);
					this.processor_ = { filter: r };
					const s = t.serverCache,
						o = t.eventCache,
						l = i.updateFullNode(te.EMPTY_NODE, s.getNode(), null),
						a = r.updateFullNode(te.EMPTY_NODE, o.getNode(), null),
						h = new tF(l, s.isFullyInitialized(), i.filtersNodes()),
						u = new tF(a, o.isFullyInitialized(), r.filtersNodes());
					(this.viewCache_ = tL(u, h)),
						(this.eventGenerator_ = new tM(this.query_));
				}
				get query() {
					return this.query_;
				}
			}
			function nc(e) {
				return 0 === e.eventRegistrations_.length;
			}
			function nd(e, t, n) {
				const i = [];
				if (n) {
					(0, m.vA)(
						null == t,
						"A cancel should cancel all event registrations.",
					);
					const r = e.query._path;
					e.eventRegistrations_.forEach((e) => {
						const t = e.createCancelEvent(n, r);
						t && i.push(t);
					});
				}
				if (t) {
					let n = [];
					for (let i = 0; i < e.eventRegistrations_.length; ++i) {
						const r = e.eventRegistrations_[i];
						if (r.matches(t)) {
							if (t.hasAnyCallback()) {
								n = n.concat(e.eventRegistrations_.slice(i + 1));
								break;
							}
						} else n.push(r);
					}
					e.eventRegistrations_ = n;
				} else e.eventRegistrations_ = [];
				return i;
			}
			function n_(e, t, n, i) {
				var r, s;
				t.type === p.MERGE &&
					null !== t.source.queryId &&
					((0, m.vA)(
						tH(e.viewCache_),
						"We should always have a full cache before handling merges",
					),
					(0, m.vA)(
						tU(e.viewCache_),
						"Missing event cache, even though we have a server cache",
					));
				const o = e.viewCache_,
					l = ((e, t, n, i, r) => {
						let s, o;
						const l = new ne();
						if (n.type === p.OVERWRITE)
							n.source.fromUser
								? (s = no(e, t, n.path, n.snap, i, r, l))
								: ((0, m.vA)(n.source.fromServer, "Unknown source."),
									(o =
										n.source.tagged ||
										(t.serverCache.isFiltered() && !eF(n.path))),
									(s = ns(e, t, n.path, n.snap, i, r, o, l)));
						else if (n.type === p.MERGE) {
							var a, h, u, c, d, _, f;
							let p;
							n.source.fromUser
								? ((a = e),
									(h = t),
									(u = n.path),
									(c = n.children),
									(d = i),
									(_ = r),
									(f = l),
									(p = h),
									c.foreach((e, t) => {
										const n = eD(u, e);
										nl(h, eS(n)) && (p = no(a, p, n, t, d, _, f));
									}),
									c.foreach((e, t) => {
										const n = eD(u, e);
										nl(h, eS(n)) || (p = no(a, p, n, t, d, _, f));
									}),
									(s = p))
								: ((0, m.vA)(n.source.fromServer, "Unknown source."),
									(o = n.source.tagged || t.serverCache.isFiltered()),
									(s = nh(e, t, n.path, n.children, i, r, o, l)));
						} else if (n.type === p.ACK_USER_WRITE)
							s = n.revert
								? ((e, t, n, i, r, s) => {
										let o;
										if (null != t8(i, n)) return t;
										{
											let l;
											const a = new ni(i, t, r),
												h = t.eventCache.getNode();
											if (eF(n) || ".priority" === eS(n)) {
												let n;
												if (t.serverCache.isFullyInitialized())
													n = t3(i, tH(t));
												else {
													const e = t.serverCache.getNode();
													(0, m.vA)(
														e instanceof te,
														"serverChildren would be complete if leaf node",
													),
														(n = t4(i, e));
												}
												l = e.filter.updateFullNode(h, n, s);
											} else {
												let r = eS(n),
													u = t6(i, r, t.serverCache);
												null == u &&
													t.serverCache.isCompleteForChild(r) &&
													(u = h.getImmediateChild(r)),
													(l =
														null != u
															? e.filter.updateChild(h, r, u, ex(n), a, s)
															: t.eventCache.getNode().hasChild(r)
																? e.filter.updateChild(
																		h,
																		r,
																		te.EMPTY_NODE,
																		ex(n),
																		a,
																		s,
																	)
																: h).isEmpty() &&
														t.serverCache.isFullyInitialized() &&
														(o = t3(i, tH(t))).isLeafNode() &&
														(l = e.filter.updateFullNode(l, o, s));
											}
											return (
												(o =
													t.serverCache.isFullyInitialized() ||
													null != t8(i, ek())),
												tO(t, l, o, e.filter.filtersNodes())
											);
										}
									})(e, t, n.path, i, r, l)
								: ((e, t, n, i, r, s, o) => {
										if (null != t8(r, n)) return t;
										const l = t.serverCache.isFiltered(),
											a = t.serverCache;
										if (null != i.value) {
											if (
												(eF(n) && a.isFullyInitialized()) ||
												a.isCompleteForPath(n)
											)
												return ns(e, t, n, a.getNode().getChild(n), r, s, l, o);
											if (!eF(n)) return t;
											{
												let i = new tY(null);
												return (
													a.getNode().forEachChild(eK, (e, t) => {
														i = i.set(new eE(e), t);
													}),
													nh(e, t, n, i, r, s, l, o)
												);
											}
										}
										{
											let h = new tY(null);
											return (
												i.foreach((e, t) => {
													const i = eD(n, e);
													a.isCompleteForPath(i) &&
														(h = h.set(e, a.getNode().getChild(i)));
												}),
												nh(e, t, n, h, r, s, l, o)
											);
										}
									})(e, t, n.path, n.affectedTree, i, r, l);
						else if (n.type === p.LISTEN_COMPLETE)
							s = ((e, t, n, i, r) => {
								const s = t.serverCache;
								return nr(
									e,
									tW(
										t,
										s.getNode(),
										s.isFullyInitialized() || eF(n),
										s.isFiltered(),
									),
									n,
									i,
									nn,
									r,
								);
							})(e, t, n.path, i, l);
						else throw (0, m.Hk)("Unknown operation type: " + n.type);
						const g = l.getChanges();
						return (
							((e, t, n) => {
								const i = t.eventCache;
								if (i.isFullyInitialized()) {
									const r = i.getNode().isLeafNode() || i.getNode().isEmpty(),
										s = tU(e);
									(!(n.length > 0) &&
										e.eventCache.isFullyInitialized() &&
										(!r || i.getNode().equals(s)) &&
										i.getNode().getPriority().equals(s.getPriority())) ||
										n.push(tl(tU(t)));
								}
							})(t, s, g),
							{ viewCache: s, changes: g }
						);
					})(e.processor_, o, t, n, i);
				return (
					(r = e.processor_),
					(s = l.viewCache),
					(0, m.vA)(
						s.eventCache.getNode().isIndexed(r.filter.getIndex()),
						"Event snap not indexed",
					),
					(0, m.vA)(
						s.serverCache.getNode().isIndexed(r.filter.getIndex()),
						"Server snap not indexed",
					),
					(0, m.vA)(
						l.viewCache.serverCache.isFullyInitialized() ||
							!o.serverCache.isFullyInitialized(),
						"Once a server snap is complete, it should never go back",
					),
					(e.viewCache_ = l.viewCache),
					np(e, l.changes, l.viewCache.eventCache.getNode(), null)
				);
			}
			function np(e, t, n, i) {
				const r = i ? [i] : e.eventRegistrations_;
				return ((e, t, n, i) => {
					const r = [],
						s = [];
					return (
						t.forEach((t) => {
							if (
								"child_changed" === t.type &&
								e.index_.indexedValueChanged(t.oldSnap, t.snapshotNode)
							) {
								var n;
								s.push(
									((n = t.childName),
									{
										type: "child_moved",
										snapshotNode: t.snapshotNode,
										childName: n,
									}),
								);
							}
						}),
						tq(e, r, "child_removed", t, i, n),
						tq(e, r, "child_added", t, i, n),
						tq(e, r, "child_moved", s, i, n),
						tq(e, r, "child_changed", t, i, n),
						tq(e, r, "value", t, i, n),
						r
					);
				})(e.eventGenerator_, t, n, r);
			}
			class nf {
				constructor() {
					this.views = new Map();
				}
			}
			function ng(e, t, n, i) {
				const r = t.source.queryId;
				if (null !== r) {
					const s = e.views.get(r);
					return (
						(0, m.vA)(
							null != s,
							"SyncTree gave us an op for an invalid query.",
						),
						n_(s, t, n, i)
					);
				}
				{
					let r = [];
					for (const s of e.views.values()) r = r.concat(n_(s, t, n, i));
					return r;
				}
			}
			function nm(e) {
				const t = [];
				for (const n of e.views.values())
					n.query._queryParams.loadsAllData() || t.push(n);
				return t;
			}
			function ny(e, t) {
				let n = null;
				for (const i of e.views.values())
					n =
						n ||
						((e, t) => {
							const n = tH(e.viewCache_);
							return n &&
								(e.query._queryParams.loadsAllData() ||
									(!eF(t) && !n.getImmediateChild(eS(t)).isEmpty()))
								? n.getChild(t)
								: null;
						})(i, t);
				return n;
			}
			function nv(e, t) {
				if (t._queryParams.loadsAllData()) return nw(e);
				{
					const n = t._queryIdentifier;
					return e.views.get(n);
				}
			}
			function nC(e) {
				return null != nw(e);
			}
			function nw(e) {
				for (const t of e.views.values())
					if (t.query._queryParams.loadsAllData()) return t;
				return null;
			}
			let nT = 1;
			class nI {
				constructor(e) {
					(this.listenProvider_ = e),
						(this.syncPointTree_ = new tY(null)),
						(this.pendingWriteTree_ = {
							visibleWrites: tz.empty(),
							allWrites: [],
							lastWriteId: -1,
						}),
						(this.tagToQueryMap = new Map()),
						(this.queryToTagMap = new Map());
				}
			}
			function nb(e, t, n, i, r) {
				var s, o;
				return ((s = e.pendingWriteTree_),
				(o = r),
				(0, m.vA)(
					i > s.lastWriteId,
					"Stacking an older write on top of newer ones",
				),
				void 0 === o && (o = !0),
				s.allWrites.push({ path: t, snap: n, writeId: i, visible: o }),
				o && (s.visibleWrites = tV(s.visibleWrites, t, n)),
				(s.lastWriteId = i),
				r)
					? nP(e, new tA(tS(), t, n))
					: [];
			}
			function nE(e, t, n = !1) {
				const i = ((e, t) => {
					for (let n = 0; n < e.allWrites.length; n++) {
						const i = e.allWrites[n];
						if (i.writeId === t) return i;
					}
					return null;
				})(e.pendingWriteTree_, t);
				if (
					!((e, t) => {
						var n;
						const i = e.allWrites.findIndex((e) => e.writeId === t);
						(0, m.vA)(i >= 0, "removeWrite called with nonexistent writeId.");
						const r = e.allWrites[i];
						e.allWrites.splice(i, 1);
						let s = r.visible,
							o = !1,
							l = e.allWrites.length - 1;
						while (s && l >= 0) {
							const t = e.allWrites[l];
							t.visible &&
								(l >= i &&
								((e, t) => {
									if (e.snap) return eO(e.path, t);
									for (const n in e.children)
										if (e.children.hasOwnProperty(n) && eO(eD(e.path, n), t))
											return !0;
									return !1;
								})(t, r.path)
									? (s = !1)
									: eO(r.path, t.path) && (o = !0)),
								l--;
						}
						return (
							!!s &&
							(o
								? (((n = e).visibleWrites = t1(n.allWrites, t0, ek())),
									n.allWrites.length > 0
										? (n.lastWriteId =
												n.allWrites[n.allWrites.length - 1].writeId)
										: (n.lastWriteId = -1))
								: r.snap
									? (e.visibleWrites = tK(e.visibleWrites, r.path))
									: Q(r.children, (t) => {
											e.visibleWrites = tK(e.visibleWrites, eD(r.path, t));
										}),
							!0)
						);
					})(e.pendingWriteTree_, t)
				)
					return [];
				{
					let t = new tY(null);
					return (
						null != i.snap
							? (t = t.set(ek(), !0))
							: Q(i.children, (e) => {
									t = t.set(new eE(e), !0);
								}),
						nP(e, new tP(i.path, t, n))
					);
				}
			}
			function nk(e, t, n) {
				return nP(e, new tA(tN(), t, n));
			}
			function nS(e, t, n, i, r = !1) {
				let s = t._path,
					o = e.syncPointTree_.get(s),
					l = [];
				if (o && ("default" === t._queryIdentifier || null != nv(o, t))) {
					const a = ((e, t, n, i) => {
						let r = t._queryIdentifier,
							s = [],
							o = [],
							l = nC(e);
						if ("default" === r)
							for (const [t, r] of e.views.entries())
								(o = o.concat(nd(r, n, i))),
									nc(r) &&
										(e.views.delete(t),
										r.query._queryParams.loadsAllData() || s.push(r.query));
						else {
							const t = e.views.get(r);
							t &&
								((o = o.concat(nd(t, n, i))),
								nc(t) &&
									(e.views.delete(r),
									t.query._queryParams.loadsAllData() || s.push(t.query)));
						}
						return (
							l &&
								!nC(e) &&
								s.push(
									new ((0, m.vA)(c, "Reference.ts has not been loaded"), c)(
										t._repo,
										t._path,
									),
								),
							{ removed: s, events: o }
						);
					})(o, t, n, i);
					0 === o.views.size && (e.syncPointTree_ = e.syncPointTree_.remove(s));
					const h = a.removed;
					if (((l = a.events), !r)) {
						const n = -1 !== h.findIndex((e) => e._queryParams.loadsAllData()),
							r = e.syncPointTree_.findOnPath(s, (e, t) => nC(t));
						if (n && !r) {
							const t = e.syncPointTree_.subtree(s);
							if (!t.isEmpty()) {
								const n = t.fold((e, t, n) => {
									if (t && nC(t)) return [nw(t)];
									{
										let e = [];
										return (
											t && (e = nm(t)),
											Q(n, (t, n) => {
												e = e.concat(n);
											}),
											e
										);
									}
								});
								for (let t = 0; t < n.length; ++t) {
									const i = n[t],
										r = i.query,
										s = nR(e, i);
									e.listenProvider_.startListening(
										nL(r),
										nA(e, r),
										s.hashFn,
										s.onComplete,
									);
								}
							}
						}
						r ||
							!(h.length > 0) ||
							i ||
							(n
								? e.listenProvider_.stopListening(nL(t), null)
								: h.forEach((t) => {
										const n = e.queryToTagMap.get(nD(t));
										e.listenProvider_.stopListening(nL(t), n);
									}));
					}
					!((e, t) => {
						for (let n = 0; n < t.length; ++n) {
							const i = t[n];
							if (!i._queryParams.loadsAllData()) {
								const t = nD(i),
									n = e.queryToTagMap.get(t);
								e.queryToTagMap.delete(t), e.tagToQueryMap.delete(n);
							}
						}
					})(e, h);
				}
				return l;
			}
			function nN(e, t, n, i = !1) {
				let r;
				let s = t._path,
					o = null,
					l = !1;
				e.syncPointTree_.foreachOnPath(s, (e, t) => {
					const n = eM(e, s);
					(o = o || ny(t, n)), (l = l || nC(t));
				});
				let a = e.syncPointTree_.get(s);
				a
					? ((l = l || nC(a)), (o = o || ny(a, ek())))
					: ((a = new nf()), (e.syncPointTree_ = e.syncPointTree_.set(s, a))),
					null != o
						? (r = !0)
						: ((r = !1),
							(o = te.EMPTY_NODE),
							e.syncPointTree_.subtree(s).foreachChild((e, t) => {
								const n = ny(t, ek());
								n && (o = o.updateImmediateChild(e, n));
							}));
				const h = null != nv(a, t);
				if (!h && !t._queryParams.loadsAllData()) {
					const n = nD(t);
					(0, m.vA)(
						!e.queryToTagMap.has(n),
						"View does not exist, but we have a tag",
					);
					const i = nT++;
					e.queryToTagMap.set(n, i), e.tagToQueryMap.set(i, n);
				}
				let u = ((e, t, n, i, r, s) => {
					const o = ((e, t, n, i, r) => {
						const s = t._queryIdentifier,
							o = e.views.get(s);
						if (!o) {
							let e = t3(n, r ? i : null),
								s = !1;
							return (
								e
									? (s = !0)
									: ((e = i instanceof te ? t4(n, i) : te.EMPTY_NODE),
										(s = !1)),
								new nu(t, tL(new tF(e, s, !1), new tF(i, r, !1)))
							);
						}
						return o;
					})(e, t, i, r, s);
					return (
						e.views.has(t._queryIdentifier) ||
							e.views.set(t._queryIdentifier, o),
						!((e, t) => {
							e.eventRegistrations_.push(t);
						})(o, n),
						((e, t) => {
							const n = e.viewCache_.eventCache,
								i = [];
							return (
								n.getNode().isLeafNode() ||
									n.getNode().forEachChild(e4, (e, t) => {
										i.push(ta(e, t));
									}),
								n.isFullyInitialized() && i.push(tl(n.getNode())),
								np(e, i, n.getNode(), t)
							);
						})(o, n)
					);
				})(a, t, n, t9(s, e.pendingWriteTree_), o, r);
				if (!h && !l && !i) {
					const n = nv(a, t);
					u = u.concat(
						((e, t, n) => {
							const i = t._path,
								r = nA(e, t),
								s = nR(e, n),
								o = e.listenProvider_.startListening(
									nL(t),
									r,
									s.hashFn,
									s.onComplete,
								),
								l = e.syncPointTree_.subtree(i);
							if (r)
								(0, m.vA)(
									!nC(l.value),
									"If we're adding a query, it shouldn't be shadowed",
								);
							else {
								const t = l.fold((e, t, n) => {
									if (!eF(e) && t && nC(t)) return [nw(t).query];
									{
										let e = [];
										return (
											t && (e = e.concat(nm(t).map((e) => e.query))),
											Q(n, (t, n) => {
												e = e.concat(n);
											}),
											e
										);
									}
								});
								for (let n = 0; n < t.length; ++n) {
									const i = t[n];
									e.listenProvider_.stopListening(nL(i), nA(e, i));
								}
							}
							return o;
						})(e, t, n),
					);
				}
				return u;
			}
			function nx(e, t, n) {
				const i = e.pendingWriteTree_,
					r = e.syncPointTree_.findOnPath(t, (e, n) => {
						const i = ny(n, eM(e, t));
						if (i) return i;
					});
				return t2(i, t, r, n, !0);
			}
			function nP(e, t) {
				var n;
				return (function e(t, n, i, r) {
					if (eF(t.path))
						return (function e(t, n, i, r) {
							const s = n.get(ek());
							null == i && null != s && (i = ny(s, ek()));
							let o = [];
							return (
								n.children.inorderTraversal((n, s) => {
									const l = i ? i.getImmediateChild(n) : null,
										a = t7(r, n),
										h = t.operationForChild(n);
									h && (o = o.concat(e(h, s, l, a)));
								}),
								s && (o = o.concat(ng(s, t, r, i))),
								o
							);
						})(t, n, i, r);
					{
						const s = n.get(ek());
						null == i && null != s && (i = ny(s, ek()));
						let o = [],
							l = eS(t.path),
							a = t.operationForChild(l),
							h = n.children.get(l);
						if (h && a) {
							const t = i ? i.getImmediateChild(l) : null,
								n = t7(r, l);
							o = o.concat(e(a, h, t, n));
						}
						return s && (o = o.concat(ng(s, t, r, i))), o;
					}
				})(t, e.syncPointTree_, null, ((n = e.pendingWriteTree_), t9(ek(), n)));
			}
			function nR(e, t) {
				const n = t.query,
					i = nA(e, n);
				return {
					hashFn: () =>
						(t.viewCache_.serverCache.getNode() || te.EMPTY_NODE).hash(),
					onComplete: (t) => {
						if ("ok" === t) {
							var r;
							return i
								? ((e, t, n) => {
										const i = nF(e, n);
										if (!i) return [];
										{
											const n = nM(i),
												r = n.path,
												s = n.queryId,
												o = eM(r, t);
											return nq(e, r, new tR(tx(s), o));
										}
									})(e, n._path, i)
								: ((r = n._path), nP(e, new tR(tN(), r)));
						}
						{
							const i = ((e, t) => {
								let n = "Unknown Error";
								"too_big" === e
									? (n =
											"The data requested exceeds the maximum size that can be accessed with a single request.")
									: "permission_denied" === e
										? (n =
												"Client doesn't have permission to access the desired data.")
										: "unavailable" === e && (n = "The service is unavailable");
								const i = Error(e + " at " + t._path.toString() + ": " + n);
								return (i.code = e.toUpperCase()), i;
							})(t, n);
							return nS(e, n, null, i);
						}
					},
				};
			}
			function nA(e, t) {
				const n = nD(t);
				return e.queryToTagMap.get(n);
			}
			function nD(e) {
				return e._path.toString() + "$" + e._queryIdentifier;
			}
			function nF(e, t) {
				return e.tagToQueryMap.get(t);
			}
			function nM(e) {
				const t = e.indexOf("$");
				return (
					(0, m.vA)(-1 !== t && t < e.length - 1, "Bad queryKey."),
					{ queryId: e.substr(t + 1), path: new eE(e.substr(0, t)) }
				);
			}
			function nq(e, t, n) {
				const i = e.syncPointTree_.get(t);
				return (
					(0, m.vA)(i, "Missing sync point for query tag that we're tracking"),
					ng(i, n, t9(t, e.pendingWriteTree_), null)
				);
			}
			function nL(e) {
				return e._queryParams.loadsAllData() && !e._queryParams.isDefault()
					? new ((0, m.vA)(d, "Reference.ts has not been loaded"), d)(
							e._repo,
							e._path,
						)
					: e;
			}
			class nO {
				constructor(e) {
					this.node_ = e;
				}
				getImmediateChild(e) {
					return new nO(this.node_.getImmediateChild(e));
				}
				node() {
					return this.node_;
				}
			}
			class nW {
				constructor(e, t) {
					(this.syncTree_ = e), (this.path_ = t);
				}
				getImmediateChild(e) {
					const t = eD(this.path_, e);
					return new nW(this.syncTree_, t);
				}
				node() {
					return nx(this.syncTree_, this.path_);
				}
			}
			const nU = (e, t, n) =>
					e && "object" == typeof e
						? ((0, m.vA)(
								".sv" in e,
								"Unexpected leaf node or priority contents",
							),
							"string" == typeof e[".sv"])
							? nH(e[".sv"], t, n)
							: "object" == typeof e[".sv"]
								? nj(e[".sv"], t)
								: void (0, m.vA)(
										!1,
										"Unexpected server value: " + JSON.stringify(e, null, 2),
									)
						: e,
				nH = (e, t, n) => {
					if ("timestamp" === e) return n.timestamp;
					(0, m.vA)(!1, "Unexpected server value: " + e);
				},
				nj = (e, t, n) => {
					e.hasOwnProperty("increment") ||
						(0, m.vA)(
							!1,
							"Unexpected server value: " + JSON.stringify(e, null, 2),
						);
					const i = e.increment;
					"number" != typeof i &&
						(0, m.vA)(!1, "Unexpected increment value: " + i);
					const r = t.node();
					if (
						((0, m.vA)(null != r, "Expected ChildrenNode.EMPTY_NODE for nulls"),
						!r.isLeafNode())
					)
						return i;
					const s = r.getValue();
					return "number" != typeof s ? i : s + i;
				},
				nY = (e, t, n) => nz(e, new nO(t), n);
			function nz(e, t, n) {
				let i;
				const r = nU(
					e.getPriority().val(),
					t.getImmediateChild(".priority"),
					n,
				);
				if (!e.isLeafNode())
					return (
						(i = e),
						r !== e.getPriority().val() && (i = i.updatePriority(new e2(r))),
						e.forEachChild(e4, (e, r) => {
							const s = nz(r, t.getImmediateChild(e), n);
							s !== r && (i = i.updateImmediateChild(e, s));
						}),
						i
					);
				{
					const i = nU(e.getValue(), t, n);
					return i !== e.getValue() || r !== e.getPriority().val()
						? new e2(i, ti(r))
						: e;
				}
			}
			class nV {
				constructor(e = "", t = null, n = { children: {}, childCount: 0 }) {
					(this.name = e), (this.parent = t), (this.node = n);
				}
			}
			function nB(e, t) {
				let n = t instanceof eE ? t : new eE(t),
					i = e,
					r = eS(n);
				while (null !== r) {
					const e = (0, m.yw)(i.node.children, r) || {
						children: {},
						childCount: 0,
					};
					(i = new nV(r, i, e)), (r = eS((n = ex(n))));
				}
				return i;
			}
			function nK(e) {
				return e.node.value;
			}
			function n$(e, t) {
				(e.node.value = t),
					(function e(t) {
						null !== t.parent &&
							((t, n, i) => {
								const r = void 0 === nK(i) && !nG(i),
									s = (0, m.gR)(t.node.children, n);
								r && s
									? (delete t.node.children[n], t.node.childCount--, e(t))
									: r ||
										s ||
										((t.node.children[n] = i.node), t.node.childCount++, e(t));
							})(t.parent, t.name, t);
					})(e);
			}
			function nG(e) {
				return e.node.childCount > 0;
			}
			function nQ(e, t) {
				Q(e.node.children, (n, i) => {
					t(new nV(n, e, i));
				});
			}
			function nX(e) {
				return new eE(null === e.parent ? e.name : nX(e.parent) + "/" + e.name);
			}
			const nJ = /[\[\].#$\/\u0000-\u001F\u007F]/,
				nZ = /[\[\].#$\u0000-\u001F\u007F]/,
				n0 = (e) => "string" == typeof e && 0 !== e.length && !nJ.test(e),
				n1 = (e) => "string" == typeof e && 0 !== e.length && !nZ.test(e),
				n2 = (e) =>
					null === e ||
					"string" == typeof e ||
					("number" == typeof e && !H(e)) ||
					(e && "object" == typeof e && (0, m.gR)(e, ".sv")),
				n3 = (e, t, n, i) => {
					(!i || void 0 !== t) && n4((0, m.dI)(e, "value"), t, n);
				},
				n4 = (e, t, n) => {
					const i = n instanceof eE ? new eW(n, e) : n;
					if (void 0 === t) throw Error(e + "contains undefined " + eH(i));
					if ("function" == typeof t)
						throw Error(
							e +
								"contains a function " +
								eH(i) +
								" with contents = " +
								t.toString(),
						);
					if (H(t)) throw Error(e + "contains " + t.toString() + " " + eH(i));
					if (
						"string" == typeof t &&
						t.length > 3495253.3333333335 &&
						(0, m.OE)(t) > 0xa00000
					)
						throw Error(
							e +
								"contains a string greater than 10485760 utf8 bytes " +
								eH(i) +
								" ('" +
								t.substring(0, 50) +
								"...')",
						);
					if (t && "object" == typeof t) {
						let n = !1,
							r = !1;
						if (
							(Q(t, (t, s) => {
								if (".value" === t) n = !0;
								else if (".priority" !== t && ".sv" !== t && ((r = !0), !n0(t)))
									throw Error(
										e +
											" contains an invalid key (" +
											t +
											") " +
											eH(i) +
											'.  Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"',
									);
								i.parts_.length > 0 && (i.byteLength_ += 1),
									i.parts_.push(t),
									(i.byteLength_ += (0, m.OE)(t)),
									eU(i),
									n4(e, s, i),
									((e) => {
										const t = e.parts_.pop();
										(e.byteLength_ -= (0, m.OE)(t)),
											e.parts_.length > 0 && (e.byteLength_ -= 1);
									})(i);
							}),
							n && r)
						)
							throw Error(
								e +
									' contains ".value" child ' +
									eH(i) +
									" in addition to actual children.",
							);
					}
				},
				n5 = (e, t) => {
					let n, i;
					for (n = 0; n < t.length; n++) {
						const r = eR((i = t[n]));
						for (let t = 0; t < r.length; t++)
							if (".priority" === r[t] && t === r.length - 1);
							else if (!n0(r[t]))
								throw Error(
									e +
										"contains an invalid key (" +
										r[t] +
										") in path " +
										i.toString() +
										'. Keys must be non-empty strings and can\'t contain ".", "#", "$", "/", "[", or "]"',
								);
					}
					t.sort(eq);
					let r = null;
					for (n = 0; n < t.length; n++) {
						if (((i = t[n]), null !== r && eO(r, i)))
							throw Error(
								e +
									"contains a path " +
									r.toString() +
									" that is ancestor of another path " +
									i.toString(),
							);
						r = i;
					}
				},
				n8 = (e, t, n, i) => {
					if (i && void 0 === t) return;
					const r = errorPrefix(e, "values");
					if (!(t && "object" == typeof t) || Array.isArray(t))
						throw Error(
							r + " must be an object containing the children to replace.",
						);
					const s = [];
					Q(t, (e, t) => {
						const i = new eE(e);
						if ((n4(r, t, eD(n, i)), ".priority" === eP(i) && !n2(t)))
							throw Error(
								r +
									"contains an invalid value for '" +
									i.toString() +
									"', which must be a valid Firebase priority (a string, finite number, server value, or null).",
							);
						s.push(i);
					}),
						n5(r, s);
				},
				n6 = (e, t, n) => {
					if (!n || void 0 !== t) {
						if (H(t))
							throw Error(
								errorPrefix(e, "priority") +
									"is " +
									t.toString() +
									", but must be a valid Firebase priority (a string, finite number, server value, or null).",
							);
						if (!n2(t))
							throw Error(
								errorPrefix(e, "priority") +
									"must be a valid Firebase priority (a string, finite number, server value, or null).",
							);
					}
				},
				n7 = (e, t, n, i) => {
					if ((!i || void 0 !== n) && !n1(n))
						throw Error(
							(0, m.dI)(e, t) +
								'was an invalid path = "' +
								n +
								'". Paths must be non-empty strings and can\'t contain ".", "#", "$", "[", or "]"',
						);
				},
				n9 = (e, t, n, i) => {
					n && (n = n.replace(/^\/*\.info(\/|$)/, "/")), n7(e, t, n, i);
				},
				ie = (e, t) => {
					if (".info" === eS(t))
						throw Error(e + " failed = Can't modify data under /.info/");
				},
				it = (e, t) => {
					var n;
					const i = t.path.toString();
					if (
						"string" != typeof t.repoInfo.host ||
						0 === t.repoInfo.host.length ||
						(!n0(t.repoInfo.namespace) &&
							"localhost" !== t.repoInfo.host.split(":")[0]) ||
						(0 !== i.length &&
							((n = i) && (n = n.replace(/^\/*\.info(\/|$)/, "/")), !n1(n)))
					)
						throw Error(
							(0, m.dI)(e, "url") +
								'must be a valid firebase URL and the path can\'t contain ".", "#", "$", "[", or "]".',
						);
				};
			class ii {
				constructor() {
					(this.eventLists_ = []), (this.recursionDepth_ = 0);
				}
			}
			function ir(e, t) {
				let n = null;
				for (let i = 0; i < t.length; i++) {
					const r = t[i],
						s = r.getPath();
					null === n || eL(s, n.path) || (e.eventLists_.push(n), (n = null)),
						null === n && (n = { events: [], path: s }),
						n.events.push(r);
				}
				n && e.eventLists_.push(n);
			}
			function is(e, t, n) {
				ir(e, n), il(e, (e) => eL(e, t));
			}
			function io(e, t, n) {
				ir(e, n), il(e, (e) => eO(e, t) || eO(t, e));
			}
			function il(e, t) {
				e.recursionDepth_++;
				let n = !0;
				for (let i = 0; i < e.eventLists_.length; i++) {
					const r = e.eventLists_[i];
					r &&
						(t(r.path)
							? (((e) => {
									for (let t = 0; t < e.events.length; t++) {
										const n = e.events[t];
										if (null !== n) {
											e.events[t] = null;
											const i = n.getEventRunner();
											A && M("event: " + n.toString()), ee(i);
										}
									}
								})(e.eventLists_[i]),
								(e.eventLists_[i] = null))
							: (n = !1));
				}
				n && (e.eventLists_ = []), e.recursionDepth_--;
			}
			class ia {
				constructor(e, t, n, i) {
					(this.repoInfo_ = e),
						(this.forceRestClient_ = t),
						(this.authTokenProvider_ = n),
						(this.appCheckProvider_ = i),
						(this.dataUpdateCount = 0),
						(this.statsListener_ = null),
						(this.eventQueue_ = new ii()),
						(this.nextWriteId_ = 1),
						(this.interceptServerDataCallback_ = null),
						(this.onDisconnect_ = tT()),
						(this.transactionQueueTree_ = new nV()),
						(this.persistentConnection_ = null),
						(this.key = this.repoInfo_.toURLString());
				}
				toString() {
					return (
						(this.repoInfo_.secure ? "https://" : "http://") +
						this.repoInfo_.host
					);
				}
			}
			function ih(e) {
				var t;
				return (
					((t = t =
						{
							timestamp: ((e) => {
								const t =
									e.infoData_.getNode(new eE(".info/serverTimeOffset")).val() ||
									0;
								return new Date().getTime() + t;
							})(e),
						}).timestamp = t.timestamp || new Date().getTime()),
					t
				);
			}
			function iu(e, t, n, i, r) {
				e.dataUpdateCount++;
				const s = new eE(t);
				n = e.interceptServerDataCallback_
					? e.interceptServerDataCallback_(t, n)
					: n;
				let o = [];
				if (r) {
					if (i) {
						const t = (0, m.kH)(n, (e) => ti(e));
						o = ((e, t, n, i) => {
							const r = nF(e, i);
							if (!r) return [];
							{
								const i = nM(r),
									s = i.path,
									o = i.queryId,
									l = eM(s, t),
									a = tY.fromObject(n);
								return nq(e, s, new tD(tx(o), l, a));
							}
						})(e.serverSyncTree_, s, t, r);
					} else {
						const t = ti(n);
						o = ((e, t, n, i) => {
							const r = nF(e, i);
							if (null == r) return [];
							{
								const i = nM(r),
									s = i.path,
									o = i.queryId,
									l = eM(s, t);
								return nq(e, s, new tA(tx(o), l, n));
							}
						})(e.serverSyncTree_, s, t, r);
					}
				} else if (i) {
					const t = (0, m.kH)(n, (e) => ti(e));
					o = ((e, t, n) => {
						const i = tY.fromObject(n);
						return nP(e, new tD(tN(), t, i));
					})(e.serverSyncTree_, s, t);
				} else {
					const t = ti(n);
					o = nk(e.serverSyncTree_, s, t);
				}
				let l = s;
				o.length > 0 && (l = iC(e, s)), io(e.eventQueue_, l, o);
			}
			function ic(e, t) {
				id(e, "connected", t),
					!1 === t &&
						((e) => {
							im(e, "onDisconnectEvents");
							const t = ih(e),
								n = tT();
							tb(e.onDisconnect_, ek(), (i, r) => {
								var s, o, l, a;
								const h =
									((s = i),
									(o = r),
									(l = e.serverSyncTree_),
									(a = t),
									nz(o, new nW(l, s), a));
								tI(n, i, h);
							});
							let i = [];
							tb(n, ek(), (t, n) => {
								i = i.concat(nk(e.serverSyncTree_, t, n));
								const r = ib(e, t);
								iC(e, r);
							}),
								(e.onDisconnect_ = tT()),
								io(e.eventQueue_, ek(), i);
						})(e);
			}
			function id(e, t, n) {
				const i = new eE("/.info/" + t),
					r = ti(n);
				e.infoData_.updateSnapshot(i, r);
				const s = nk(e.infoSyncTree_, i, r);
				io(e.eventQueue_, i, s);
			}
			function i_(e) {
				return e.nextWriteId_++;
			}
			function ip(e, t, n, i) {
				const r = ti(n);
				e.server_.onDisconnectPut(t.toString(), r.val(!0), (n, s) => {
					"ok" === n && tI(e.onDisconnect_, t, r), iy(e, i, n, s);
				});
			}
			function ig(e, t, n) {
				let i;
				(i =
					".info" === eS(t._path)
						? nS(e.infoSyncTree_, t, n)
						: nS(e.serverSyncTree_, t, n)),
					is(e.eventQueue_, t._path, i);
			}
			function im(e, ...t) {
				let n = "";
				e.persistentConnection_ && (n = e.persistentConnection_.id + ":"),
					M(n, ...t);
			}
			function iy(e, t, n, i) {
				t &&
					ee(() => {
						if ("ok" === n) t(null);
						else {
							let e = (n || "error").toUpperCase(),
								r = e;
							i && (r += ": " + i);
							const s = Error(r);
							(s.code = e), t(s);
						}
					});
			}
			function iv(e, t, n) {
				return nx(e.serverSyncTree_, t, n) || te.EMPTY_NODE;
			}
			function iC(e, t) {
				const n = iw(e, t),
					i = nX(n),
					r = iT(e, n);
				return (
					((e, t, n) => {
						if (0 === t.length) return;
						let i = [],
							r = [],
							s = t.filter((e) => 0 === e.status).map((e) => e.currentWriteId);
						for (let o = 0; o < t.length; o++) {
							let l = t[o],
								a = eM(n, l.path),
								h = !1,
								u;
							if (
								((0, m.vA)(
									null !== a,
									"rerunTransactionsUnderNode_: relativePath should not be null.",
								),
								4 === l.status)
							)
								(h = !0),
									(u = l.abortReason),
									(r = r.concat(nE(e.serverSyncTree_, l.currentWriteId, !0)));
							else if (0 === l.status) {
								if (l.retryCount >= 25)
									(h = !0),
										(u = "maxretry"),
										(r = r.concat(nE(e.serverSyncTree_, l.currentWriteId, !0)));
								else {
									const n = iv(e, l.path, s);
									l.currentInputSnapshot = n;
									const i = t[o].update(n.val());
									if (void 0 !== i) {
										n4("transaction failed: Data returned ", i, l.path);
										let t = ti(i);
										("object" == typeof i &&
											null != i &&
											(0, m.gR)(i, ".priority")) ||
											(t = t.updatePriority(n.getPriority()));
										const o = l.currentWriteId,
											a = nY(t, n, ih(e));
										(l.currentOutputSnapshotRaw = t),
											(l.currentOutputSnapshotResolved = a),
											(l.currentWriteId = i_(e)),
											s.splice(s.indexOf(o), 1),
											(r = (r = r.concat(
												nb(
													e.serverSyncTree_,
													l.path,
													a,
													l.currentWriteId,
													l.applyLocally,
												),
											)).concat(nE(e.serverSyncTree_, o, !0)));
									} else
										(h = !0),
											(u = "nodata"),
											(r = r.concat(
												nE(e.serverSyncTree_, l.currentWriteId, !0),
											));
								}
							}
							io(e.eventQueue_, n, r),
								(r = []),
								h &&
									((t[o].status = 2),
									setTimeout(t[o].unwatcher, Math.floor(0)),
									t[o].onComplete &&
										("nodata" === u
											? i.push(() =>
													t[o].onComplete(null, !1, t[o].currentInputSnapshot),
												)
											: i.push(() => t[o].onComplete(Error(u), !1, null))));
						}
						iI(e, e.transactionQueueTree_);
						for (let e = 0; e < i.length; e++) ee(i[e]);
						(function e(t, n = t.transactionQueueTree_) {
							if ((n || iI(t, n), nK(n))) {
								const i = iT(t, n);
								(0, m.vA)(
									i.length > 0,
									"Sending zero length transaction queue",
								),
									i.every((e) => 0 === e.status) &&
										((t, n, i) => {
											let r = iv(
													t,
													n,
													i.map((e) => e.currentWriteId),
												),
												s = r,
												o = r.hash();
											for (let e = 0; e < i.length; e++) {
												const t = i[e];
												(0, m.vA)(
													0 === t.status,
													"tryToSendTransactionQueue_: items in queue should all be run.",
												),
													(t.status = 1),
													t.retryCount++;
												const r = eM(n, t.path);
												s = s.updateChild(r, t.currentOutputSnapshotRaw);
											}
											const l = s.val(!0);
											t.server_.put(
												n.toString(),
												l,
												(r) => {
													im(t, "transaction put response", {
														path: n.toString(),
														status: r,
													});
													let s = [];
													if ("ok" === r) {
														const r = [];
														for (let e = 0; e < i.length; e++)
															(i[e].status = 2),
																(s = s.concat(
																	nE(t.serverSyncTree_, i[e].currentWriteId),
																)),
																i[e].onComplete &&
																	r.push(() =>
																		i[e].onComplete(
																			null,
																			!0,
																			i[e].currentOutputSnapshotResolved,
																		),
																	),
																i[e].unwatcher();
														iI(t, nB(t.transactionQueueTree_, n)),
															e(t, t.transactionQueueTree_),
															io(t.eventQueue_, n, s);
														for (let e = 0; e < r.length; e++) ee(r[e]);
													} else {
														if ("datastale" === r)
															for (let e = 0; e < i.length; e++)
																3 === i[e].status
																	? (i[e].status = 4)
																	: (i[e].status = 0);
														else {
															W(
																"transaction at " +
																	n.toString() +
																	" failed: " +
																	r,
															);
															for (let e = 0; e < i.length; e++)
																(i[e].status = 4), (i[e].abortReason = r);
														}
														iC(t, n);
													}
												},
												o,
											);
										})(t, nX(n), i);
							} else
								nG(n) &&
									nQ(n, (n) => {
										e(t, n);
									});
						})(e, e.transactionQueueTree_);
					})(e, r, i),
					i
				);
			}
			function iw(e, t) {
				let n;
				let i = e.transactionQueueTree_;
				for (n = eS(t); null !== n && void 0 === nK(i); )
					(i = nB(i, n)), (n = eS((t = ex(t))));
				return i;
			}
			function iT(e, t) {
				const n = [];
				return (
					(function e(t, n, i) {
						const r = nK(n);
						if (r) for (let e = 0; e < r.length; e++) i.push(r[e]);
						nQ(n, (n) => {
							e(t, n, i);
						});
					})(e, t, n),
					n.sort((e, t) => e.order - t.order),
					n
				);
			}
			function iI(e, t) {
				const n = nK(t);
				if (n) {
					let e = 0;
					for (let t = 0; t < n.length; t++)
						2 !== n[t].status && ((n[e] = n[t]), e++);
					(n.length = e), n$(t, n.length > 0 ? n : void 0);
				}
				nQ(t, (t) => {
					iI(e, t);
				});
			}
			function ib(e, t) {
				const n = nX(iw(e, t)),
					i = nB(e.transactionQueueTree_, t);
				return (
					!((e, t, n) => {
						let i = e.parent;
						while (null !== i) {
							if (t(i)) return !0;
							i = i.parent;
						}
					})(i, (t) => {
						iE(e, t);
					}),
					iE(e, i),
					!(function e(t, n, i, r) {
						i && !r && n(t),
							nQ(t, (t) => {
								e(t, n, !0, r);
							}),
							i && r && n(t);
					})(i, (t) => {
						iE(e, t);
					}),
					n
				);
			}
			function iE(e, t) {
				const n = nK(t);
				if (n) {
					let i = [],
						r = [],
						s = -1;
					for (let t = 0; t < n.length; t++)
						3 === n[t].status ||
							(1 === n[t].status
								? ((0, m.vA)(
										s === t - 1,
										"All SENT items should be at beginning of queue.",
									),
									(s = t),
									(n[t].status = 3),
									(n[t].abortReason = "set"))
								: ((0, m.vA)(
										0 === n[t].status,
										"Unexpected transaction status in abort",
									),
									n[t].unwatcher(),
									(r = r.concat(
										nE(e.serverSyncTree_, n[t].currentWriteId, !0),
									)),
									n[t].onComplete &&
										i.push(
											n[t].onComplete.bind(null, Error("set"), !1, null),
										)));
					-1 === s ? n$(t, void 0) : (n.length = s + 1),
						io(e.eventQueue_, nX(t), r);
					for (let e = 0; e < i.length; e++) ee(i[e]);
				}
			}
			const ik = (e, t) => {
					const n = iS(e),
						i = n.namespace;
					"firebase.com" === n.domain &&
						O(
							n.host +
								" is no longer supported. Please use <YOUR FIREBASE>.firebaseio.com instead",
						),
						(i && "undefined" !== i) ||
							"localhost" === n.domain ||
							O(
								"Cannot parse Firebase url. Please use https://<YOUR FIREBASE>.firebaseio.com",
							),
						n.secure || U();
					const r = "ws" === n.scheme || "wss" === n.scheme;
					return {
						repoInfo: new ea(n.host, n.secure, i, r, t, "", i !== n.subdomain),
						path: new eE(n.pathString),
					};
				},
				iS = (e) => {
					let t = "",
						n = "",
						i = "",
						r = "",
						s = "",
						o = !0,
						l = "https",
						a = 443;
					if ("string" == typeof e) {
						let h = e.indexOf("//");
						h >= 0 && ((l = e.substring(0, h - 1)), (e = e.substring(h + 2)));
						let u = e.indexOf("/");
						-1 === u && (u = e.length);
						let c = e.indexOf("?");
						-1 === c && (c = e.length),
							(t = e.substring(0, Math.min(u, c))),
							u < c &&
								(r = ((e) => {
									let t = "",
										n = e.split("/");
									for (let e = 0; e < n.length; e++)
										if (n[e].length > 0) {
											let i = n[e];
											try {
												i = decodeURIComponent(i.replace(/\+/g, " "));
											} catch (e) {}
											t += "/" + i;
										}
									return t;
								})(e.substring(u, c)));
						const d = ((e) => {
							const t = {};
							for (const n of ("?" === e.charAt(0) && (e = e.substring(1)),
							e.split("&"))) {
								if (0 === n.length) continue;
								const i = n.split("=");
								2 === i.length
									? (t[decodeURIComponent(i[0])] = decodeURIComponent(i[1]))
									: W(`Invalid query segment '${n}' in query '${e}'`);
							}
							return t;
						})(e.substring(Math.min(e.length, c)));
						(h = t.indexOf(":")) >= 0
							? ((o = "https" === l || "wss" === l),
								(a = Number.parseInt(t.substring(h + 1), 10)))
							: (h = t.length);
						const _ = t.slice(0, h);
						if ("localhost" === _.toLowerCase()) n = "localhost";
						else if (_.split(".").length <= 2) n = _;
						else {
							const e = t.indexOf(".");
							(i = t.substring(0, e).toLowerCase()),
								(n = t.substring(e + 1)),
								(s = i);
						}
						"ns" in d && (s = d.ns);
					}
					return {
						host: t,
						port: a,
						domain: n,
						subdomain: i,
						secure: o,
						scheme: l,
						pathString: r,
						namespace: s,
					};
				},
				iN = "-0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ_abcdefghijklmnopqrstuvwxyz";
			(() => {
				const e = 0,
					t = [];
			})();
			class ix {
				constructor(e, t, n, i) {
					(this.eventType = e),
						(this.eventRegistration = t),
						(this.snapshot = n),
						(this.prevName = i);
				}
				getPath() {
					const e = this.snapshot.ref;
					return "value" === this.eventType ? e._path : e.parent._path;
				}
				getEventType() {
					return this.eventType;
				}
				getEventRunner() {
					return this.eventRegistration.getEventRunner(this);
				}
				toString() {
					return (
						this.getPath().toString() +
						":" +
						this.eventType +
						":" +
						(0, m.As)(this.snapshot.exportVal())
					);
				}
			}
			class iP {
				constructor(e, t, n) {
					(this.eventRegistration = e), (this.error = t), (this.path = n);
				}
				getPath() {
					return this.path;
				}
				getEventType() {
					return "cancel";
				}
				getEventRunner() {
					return this.eventRegistration.getEventRunner(this);
				}
				toString() {
					return this.path.toString() + ":cancel";
				}
			}
			class iR {
				constructor(e, t) {
					(this.snapshotCallback = e), (this.cancelCallback = t);
				}
				onValue(e, t) {
					this.snapshotCallback.call(null, e, t);
				}
				onCancel(e) {
					return (
						(0, m.vA)(
							this.hasCancelCallback,
							"Raising a cancel event on a listener with no cancel callback",
						),
						this.cancelCallback.call(null, e)
					);
				}
				get hasCancelCallback() {
					return !!this.cancelCallback;
				}
				matches(e) {
					return (
						this.snapshotCallback === e.snapshotCallback ||
						(void 0 !== this.snapshotCallback.userCallback &&
							this.snapshotCallback.userCallback ===
								e.snapshotCallback.userCallback &&
							this.snapshotCallback.context === e.snapshotCallback.context)
					);
				}
			}
			class iA {
				constructor(e, t, n, i) {
					(this._repo = e),
						(this._path = t),
						(this._queryParams = n),
						(this._orderByCalled = i);
				}
				get key() {
					return eF(this._path) ? null : eP(this._path);
				}
				get ref() {
					return new iq(this._repo, this._path);
				}
				get _queryIdentifier() {
					const e = $(tv(this._queryParams));
					return "{}" === e ? "default" : e;
				}
				get _queryObject() {
					return tv(this._queryParams);
				}
				isEqual(e) {
					if (!((e = (0, m.Ku)(e)) instanceof iA)) return !1;
					const t = this._repo === e._repo,
						n = eL(this._path, e._path),
						i = this._queryIdentifier === e._queryIdentifier;
					return t && n && i;
				}
				toJSON() {
					return this.toString();
				}
				toString() {
					return (
						this._repo.toString() +
						((e) => {
							let t = "";
							for (let n = e.pieceNum_; n < e.pieces_.length; n++)
								"" !== e.pieces_[n] &&
									(t += "/" + encodeURIComponent(String(e.pieces_[n])));
							return t || "/";
						})(this._path)
					);
				}
			}
			function iD(e, t) {
				if (!0 === e._orderByCalled)
					throw Error(t + ": You can't combine multiple orderBy calls.");
			}
			function iF(e) {
				let t = null,
					n = null;
				if (
					(e.hasStart() && (t = e.getIndexStartValue()),
					e.hasEnd() && (n = e.getIndexEndValue()),
					e.getIndex() === eK)
				) {
					const i =
							"Query: When ordering by key, you may only pass one argument to startAt(), endAt(), or equalTo().",
						r =
							"Query: When ordering by key, the argument passed to startAt(), startAfter(), endAt(), endBefore(), or equalTo() must be a string.";
					if (e.hasStart()) {
						if (e.getIndexStartName() !== Y) throw Error(i);
						if ("string" != typeof t) throw Error(r);
					}
					if (e.hasEnd()) {
						if (e.getIndexEndName() !== z) throw Error(i);
						if ("string" != typeof n) throw Error(r);
					}
				} else if (e.getIndex() === e4) {
					if ((null != t && !n2(t)) || (null != n && !n2(n)))
						throw Error(
							"Query: When ordering by priority, the first argument passed to startAt(), startAfter() endAt(), endBefore(), or equalTo() must be a valid priority value (null, a number, or a string).",
						);
				} else if (
					((0, m.vA)(
						e.getIndex() instanceof tr || e.getIndex() === to,
						"unknown index type.",
					),
					(null != t && "object" == typeof t) ||
						(null != n && "object" == typeof n))
				)
					throw Error(
						"Query: First argument passed to startAt(), startAfter(), endAt(), endBefore(), or equalTo() cannot be an object.",
					);
			}
			function iM(e) {
				if (e.hasStart() && e.hasEnd() && e.hasLimit() && !e.hasAnchoredLimit())
					throw Error(
						"Query: Can't combine startAt(), startAfter(), endAt(), endBefore(), and limit(). Use limitToFirst() or limitToLast() instead.",
					);
			}
			class iq extends iA {
				constructor(e, t) {
					super(e, t, new tp(), !1);
				}
				get parent() {
					const e = eA(this._path);
					return null === e ? null : new iq(this._repo, e);
				}
				get root() {
					let e = this;
					while (null !== e.parent) e = e.parent;
					return e;
				}
			}
			class iL {
				constructor(e, t, n) {
					(this._node = e), (this.ref = t), (this._index = n);
				}
				get priority() {
					return this._node.getPriority().val();
				}
				get key() {
					return this.ref.key;
				}
				get size() {
					return this._node.numChildren();
				}
				child(e) {
					const t = new eE(e),
						n = iO(this.ref, e);
					return new iL(this._node.getChild(t), n, e4);
				}
				exists() {
					return !this._node.isEmpty();
				}
				exportVal() {
					return this._node.val(!0);
				}
				forEach(e) {
					return (
						!this._node.isLeafNode() &&
						!!this._node.forEachChild(this._index, (t, n) =>
							e(new iL(n, iO(this.ref, t), e4)),
						)
					);
				}
				hasChild(e) {
					const t = new eE(e);
					return !this._node.getChild(t).isEmpty();
				}
				hasChildren() {
					return !this._node.isLeafNode() && !this._node.isEmpty();
				}
				toJSON() {
					return this.exportVal();
				}
				val() {
					return this._node.val();
				}
			}
			function iO(e, t) {
				return (
					null === eS((e = (0, m.Ku)(e))._path)
						? n9("child", "path", t, !1)
						: n7("child", "path", t, !1),
					new iq(e._repo, eD(e._path, t))
				);
			}
			class iW {
				constructor(e) {
					this.callbackContext = e;
				}
				respondsTo(e) {
					return "value" === e;
				}
				createEvent(e, t) {
					const n = t._queryParams.getIndex();
					return new ix(
						"value",
						this,
						new iL(e.snapshotNode, new iq(t._repo, t._path), n),
					);
				}
				getEventRunner(e) {
					return "cancel" === e.getEventType()
						? () => this.callbackContext.onCancel(e.error)
						: () => this.callbackContext.onValue(e.snapshot, null);
				}
				createCancelEvent(e, t) {
					return this.callbackContext.hasCancelCallback
						? new iP(this, e, t)
						: null;
				}
				matches(e) {
					return (
						e instanceof iW &&
						(!e.callbackContext ||
							!this.callbackContext ||
							e.callbackContext.matches(this.callbackContext))
					);
				}
				hasAnyCallback() {
					return null !== this.callbackContext;
				}
			}
			class iU {
				constructor(e, t) {
					(this.eventType = e), (this.callbackContext = t);
				}
				respondsTo(e) {
					let t = "children_added" === e ? "child_added" : e;
					return (
						(t = "children_removed" === t ? "child_removed" : t),
						this.eventType === t
					);
				}
				createCancelEvent(e, t) {
					return this.callbackContext.hasCancelCallback
						? new iP(this, e, t)
						: null;
				}
				createEvent(e, t) {
					(0, m.vA)(
						null != e.childName,
						"Child events should have a childName.",
					);
					const n = iO(new iq(t._repo, t._path), e.childName),
						i = t._queryParams.getIndex();
					return new ix(e.type, this, new iL(e.snapshotNode, n, i), e.prevName);
				}
				getEventRunner(e) {
					return "cancel" === e.getEventType()
						? () => this.callbackContext.onCancel(e.error)
						: () => this.callbackContext.onValue(e.snapshot, e.prevName);
				}
				matches(e) {
					return (
						e instanceof iU &&
						this.eventType === e.eventType &&
						(!this.callbackContext ||
							!e.callbackContext ||
							this.callbackContext.matches(e.callbackContext))
					);
				}
				hasAnyCallback() {
					return !!this.callbackContext;
				}
			}
			function iH(e, t, n, i, r) {
				var s;
				let o, l;
				if (
					("object" == typeof i && ((o = void 0), (r = i)),
					"function" == typeof i && (o = i),
					r && r.onlyOnce)
				) {
					const t = n,
						i = (n, i) => {
							ig(e._repo, e, h), t(n, i);
						};
					(i.userCallback = n.userCallback), (i.context = n.context), (n = i);
				}
				const a = new iR(n, o || void 0),
					h = "value" === t ? new iW(a) : new iU(t, a);
				return (
					(s = e._repo),
					(l =
						".info" === eS(e._path)
							? nN(s.infoSyncTree_, e, h)
							: nN(s.serverSyncTree_, e, h)),
					is(s.eventQueue_, e._path, l),
					() => ig(e._repo, e, h)
				);
			}
			function ij(e, t, n, i) {
				return iH(e, "value", t, n, i);
			}
			function iY(e, t, n, i) {
				return iH(e, "child_added", t, n, i);
			}
			function iz(e, t, n, i) {
				return iH(e, "child_changed", t, n, i);
			}
			function iV(e, t, n, i) {
				return iH(e, "child_moved", t, n, i);
			}
			function iB(e, t, n, i) {
				return iH(e, "child_removed", t, n, i);
			}
			class iK {}
			class i$ extends iK {
				constructor(e, t) {
					super(), (this._value = e), (this._key = t), (this.type = "endAt");
				}
				_apply(e) {
					n3("endAt", this._value, e._path, !0);
					const t = tg(e._queryParams, this._value, this._key);
					if ((iM(t), iF(t), e._queryParams.hasEnd()))
						throw Error(
							"endAt: Starting point was already set (by another call to endAt, endBefore or equalTo).",
						);
					return new iA(e._repo, e._path, t, e._orderByCalled);
				}
			}
			class iG extends iK {
				constructor(e, t) {
					super(), (this._value = e), (this._key = t), (this.type = "startAt");
				}
				_apply(e) {
					n3("startAt", this._value, e._path, !0);
					const t = tf(e._queryParams, this._value, this._key);
					if ((iM(t), iF(t), e._queryParams.hasStart()))
						throw Error(
							"startAt: Starting point was already set (by another call to startAt, startBefore or equalTo).",
						);
					return new iA(e._repo, e._path, t, e._orderByCalled);
				}
			}
			(0, m.vA)(!c, "__referenceConstructor has already been defined"),
				(c = iq),
				(0, m.vA)(!d, "__referenceConstructor has already been defined"),
				(d = iq);
			const iQ = {};
			class iX {
				constructor(e, t) {
					(this._repoInternal = e),
						(this.app = t),
						(this.type = "database"),
						(this._instanceStarted = !1);
				}
				get _repo() {
					return (
						this._instanceStarted ||
							(((e, t, n) => {
								if (
									((e.stats_ = e_(e.repoInfo_)),
									e.forceRestClient_ ||
										(
											("object" == typeof window &&
												window.navigator &&
												window.navigator.userAgent) ||
											""
										).search(
											/googlebot|google webmaster tools|bingbot|yahoo! slurp|baiduspider|yandexbot|duckduckbot/i,
										) >= 0)
								)
									(e.server_ = new tC(
										e.repoInfo_,
										(t, n, i, r) => {
											iu(e, t, n, i, r);
										},
										e.authTokenProvider_,
										e.appCheckProvider_,
									)),
										setTimeout(() => ic(e, !0), 0);
								else {
									if (null != n) {
										if ("object" != typeof n)
											throw Error(
												"Only objects are supported for option databaseAuthVariableOverride",
											);
										try {
											(0, m.As)(n);
										} catch (e) {
											throw Error("Invalid authOverride provided: " + e);
										}
									}
									(e.persistentConnection_ = new eY(
										e.repoInfo_,
										t,
										(t, n, i, r) => {
											iu(e, t, n, i, r);
										},
										(t) => {
											ic(e, t);
										},
										(t) => {
											var n;
											(n = e),
												Q(t, (e, t) => {
													id(n, e, t);
												});
										},
										e.authTokenProvider_,
										e.appCheckProvider_,
										n,
									)),
										(e.server_ = e.persistentConnection_);
								}
								e.authTokenProvider_.addTokenChangeListener((t) => {
									e.server_.refreshAuthToken(t);
								}),
									e.appCheckProvider_.addTokenChangeListener((t) => {
										e.server_.refreshAppCheckToken(t.token);
									}),
									(e.statsReporter_ = ((e, t) => {
										const n = e.toString();
										return ed[n] || (ed[n] = t()), ed[n];
									})(e.repoInfo_, () => new tk(e.stats_, e.server_))),
									(e.infoData_ = new tw()),
									(e.infoSyncTree_ = new nI({
										startListening: (t, n, i, r) => {
											let s = [],
												o = e.infoData_.getNode(t._path);
											return (
												o.isEmpty() ||
													((s = nk(e.infoSyncTree_, t._path, o)),
													setTimeout(() => {
														r("ok");
													}, 0)),
												s
											);
										},
										stopListening: () => {},
									})),
									id(e, "connected", !1),
									(e.serverSyncTree_ = new nI({
										startListening: (t, n, i, r) => (
											e.server_.listen(t, i, n, (n, i) => {
												const s = r(n, i);
												io(e.eventQueue_, t._path, s);
											}),
											[]
										),
										stopListening: (t, n) => {
											e.server_.unlisten(t, n);
										},
									}));
							})(
								this._repoInternal,
								this.app.options.appId,
								this.app.options.databaseAuthVariableOverride,
							),
							(this._instanceStarted = !0)),
						this._repoInternal
					);
				}
				get _root() {
					return (
						this._rootInternal ||
							(this._rootInternal = new iq(this._repo, ek())),
						this._rootInternal
					);
				}
				_delete() {
					return (
						null !== this._rootInternal &&
							(((e, t) => {
								var n;
								const i = iQ[t];
								(i && i[e.key] === e) ||
									O(`Database ${t}(${e.repoInfo_}) has already been deleted.`),
									(n = e).persistentConnection_ &&
										n.persistentConnection_.interrupt("repo_interrupt"),
									delete i[e.key];
							})(this._repo, this.app.name),
							(this._repoInternal = null),
							(this._rootInternal = null)),
						Promise.resolve()
					);
				}
				_checkNotDeleted(e) {
					null === this._rootInternal &&
						O("Cannot call " + e + " on a deleted database.");
				}
			}
			(eY.prototype.simpleListen = function (e, t) {
				this.sendRequest("q", { p: e }, t);
			}),
				(eY.prototype.echo = function (e, t) {
					this.sendRequest("echo", { d: e }, t);
				}),
				(T = f.MF),
				(0, f.om)(
					new g.uA(
						"database",
						(e, { instanceIdentifier: t }) => {
							const n = e.getProvider("app").getImmediate();
							return ((e, t, n, i, r) => {
								var s, o, l, a;
								let h,
									u,
									c,
									d,
									_ = i || e.options.databaseURL;
								void 0 === _ &&
									(e.options.projectId ||
										O(
											"Can't determine Firebase Database URL. Be sure to include  a Project ID when calling firebase.initializeApp().",
										),
									M("Using default host for project ", e.options.projectId),
									(_ = `${e.options.projectId}-default-rtdb.firebaseio.com`));
								let p = ik(_, void 0),
									f = p.repoInfo;
								void 0 !== v &&
									v.env &&
									(c = v.env.FIREBASE_DATABASE_EMULATOR_HOST),
									c
										? ((d = !0),
											(f = (p = ik((_ = `http://${c}?ns=${f.namespace}`), r))
												.repoInfo))
										: (d = !p.repoInfo.secure);
								const g =
									r && d ? new er(er.OWNER) : new ei(e.name, e.options, t);
								return (
									it("Invalid Firebase Database URL", p),
									eF(p.path) ||
										O(
											"Database URL must point to the root of a Firebase Database (not including a child path).",
										),
									new iX(
										((s = f),
										(o = e),
										(l = g),
										(a = new en(e.name, n)),
										(h = iQ[o.name]) || ((h = {}), (iQ[o.name] = h)),
										(u = h[s.toURLString()]) &&
											O(
												"Database initialized multiple times. Please make sure the format of the database URL matches with each database() call.",
											),
										(u = new ia(s, !1, l, a)),
										(h[s.toURLString()] = u),
										u),
										e,
									)
								);
							})(
								n,
								e.getProvider("auth-internal"),
								e.getProvider("app-check-internal"),
								t,
							);
						},
						"PUBLIC",
					).setMultipleInstances(!0),
				),
				(0, f.KO)(C, w, void 0),
				(0, f.KO)(C, w, "esm2017");
		},
	},
]);
