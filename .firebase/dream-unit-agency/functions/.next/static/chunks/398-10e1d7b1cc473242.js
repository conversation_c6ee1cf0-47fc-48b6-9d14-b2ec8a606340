(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[398],{392:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(1018).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},4290:(e,t,n)=>{n.d(t,{UC:()=>eD,YJ:()=>eH,In:()=>eL,q7:()=>eI,VF:()=>eF,p4:()=>eB,JU:()=>eO,ZL:()=>eN,bL:()=>eP,wn:()=>eW,PP:()=>eV,wv:()=>e_,l9:()=>eE,WT:()=>ej,LM:()=>eM});var r=n(2149),o=n(4632);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(493),a=n(9491),s=n(8735),u=n(4507),c=n(1484),d=n(8548),f=n(1391),p=n(7654),h=n(1470),m=n(4485),v=n(2619),g=n(6330),w=n(3629),y=n(2566),x=n(7522),b=n(5544),S=n(8081),C=r.forwardRef((e,t)=>(0,S.jsx)(g.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));C.displayName="VisuallyHidden";var R=n(5703),A=n(8426),T=[" ","Enter","ArrowUp","ArrowDown"],k=[" ","Enter"],P="Select",[E,j,L]=(0,a.N)(P),[N,D]=(0,u.A)(P,[L,m.Bk]),M=(0,m.Bk)(),[H,O]=N(P),[I,B]=N(P),F=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:v,required:g,form:w}=e,y=M(t),[b,C]=r.useState(null),[R,A]=r.useState(null),[T,k]=r.useState(!1),P=(0,c.jH)(d),[j=!1,L]=(0,x.i)({prop:o,defaultProp:i,onChange:l}),[N,D]=(0,x.i)({prop:a,defaultProp:s,onChange:u}),O=r.useRef(null),B=!b||w||!!b.closest("form"),[F,V]=r.useState(new Set),W=Array.from(F).map(e=>e.props.value).join(";");return(0,S.jsx)(m.bL,{...y,children:(0,S.jsxs)(H,{required:g,scope:t,trigger:b,onTriggerChange:C,valueNode:R,onValueNodeChange:A,valueNodeHasChildren:T,onValueNodeHasChildrenChange:k,contentId:(0,h.B)(),value:N,onValueChange:D,open:j,onOpenChange:L,dir:P,triggerPointerDownPosRef:O,disabled:v,children:[(0,S.jsx)(E.Provider,{scope:t,children:(0,S.jsx)(I,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{V(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{V(t=>{const n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,S.jsxs)(eA,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:p,value:N,onChange:e=>D(e.target.value),disabled:v,form:w,children:[void 0===N?(0,S.jsx)("option",{value:""}):null,Array.from(F)]},W):null]})})};F.displayName=P;var V="SelectTrigger",W=r.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...i}=e,a=M(n),u=O(V,n),c=u.disabled||o,d=(0,s.s)(t,u.onTriggerChange),f=j(n),p=r.useRef("touch"),[h,v,w]=eT(e=>{const t=f().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=ek(t,e,n);void 0!==r&&u.onValueChange(r.value)}),y=e=>{c||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(m.Mz,{asChild:!0,...a,children:(0,S.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(u.value)?"":void 0,...i,ref:d,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{const t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});W.displayName=V;var _="SelectValue",z=r.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=O(_,n),{onValueNodeHasChildrenChange:c}=u,d=void 0!==i,f=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(d)},[c,d]),(0,S.jsx)(g.sG.span,{...a,ref:f,style:{pointerEvents:"none"},children:eR(u.value)?(0,S.jsx)(S.Fragment,{children:l}):i})});z.displayName=_;var G=r.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});G.displayName="SelectIcon";var K=e=>(0,S.jsx)(v.Z,{asChild:!0,...e});K.displayName="SelectPortal";var U="SelectContent",q=r.forwardRef((e,t)=>{const n=O(U,e.__scopeSelect),[i,l]=r.useState();return((0,b.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,S.jsx)(Z,{...e,ref:t}):i?o.createPortal((0,S.jsx)(Y,{scope:e.__scopeSelect,children:(0,S.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});q.displayName=U;var[Y,X]=N(U),Z=r.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:C,avoidCollisions:T,...k}=e,P=O(U,n),[E,L]=r.useState(null),[N,D]=r.useState(null),M=(0,s.s)(t,e=>L(e)),[H,I]=r.useState(null),[B,F]=r.useState(null),V=j(n),[W,_]=r.useState(!1),z=r.useRef(!1);r.useEffect(()=>{if(E)return(0,R.Eq)(E)},[E]),(0,f.Oh)();const G=r.useCallback(e=>{const[t,...n]=V().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(const n of e)if(n===o||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),null==n||n.focus(),document.activeElement!==o))return},[V,N]),K=r.useCallback(()=>G([H,E]),[G,H,E]);r.useEffect(()=>{W&&K()},[W,K]);const{onOpenChange:q,triggerPointerDownPosRef:X}=P;r.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{var n,r,o,i;e={x:Math.abs(Math.round(t.pageX)-(null!==(o=null===(n=X.current)||void 0===n?void 0:n.x)&&void 0!==o?o:0)),y:Math.abs(Math.round(t.pageY)-(null!==(i=null===(r=X.current)||void 0===r?void 0:r.y)&&void 0!==i?i:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():E.contains(n.target)||q(!1),document.removeEventListener("pointermove",t),X.current=null};return null!==X.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[E,q,X]),r.useEffect(()=>{const e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);const[Z,Q]=eT(e=>{const t=V().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=ek(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{const r=!z.current&&!n;(void 0!==P.value&&P.value===t||r)&&(I(e),r&&(z.current=!0))},[P.value]),et=r.useCallback(()=>null==E?void 0:E.focus(),[E]),en=r.useCallback((e,t,n)=>{const r=!z.current&&!n;(void 0!==P.value&&P.value===t||r)&&F(e)},[P.value]),er="popper"===o?J:$,eo=er===J?{side:c,sideOffset:h,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:C,avoidCollisions:T}:{};return(0,S.jsx)(Y,{scope:n,content:E,viewport:N,onViewportChange:D,itemRefCallback:ee,selectedItem:H,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:K,selectedItemText:B,position:o,isPositioned:W,searchRef:Z,children:(0,S.jsx)(A.A,{as:w.DX,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:P.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{var t;null===(t=P.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>P.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:P.contentId,"data-state":P.open?"open":"closed",dir:P.dir,onContextMenu:e=>e.preventDefault(),...k,...eo,onPlaced:()=>_(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,l.m)(k.onKeyDown,e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});Z.displayName="SelectContentImpl";var $=r.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...l}=e,a=O(U,n),u=X(U,n),[c,d]=r.useState(null),[f,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=j(n),v=r.useRef(!1),w=r.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:C,focusSelectedItem:R}=u,A=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&f&&y&&x&&C){const e=a.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==a.dir){const o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{const o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),d=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=d+"px"}const l=m(),s=window.innerHeight-20,u=y.scrollHeight,d=window.getComputedStyle(f),p=Number.parseInt(d.borderTopWidth,10),h=Number.parseInt(d.paddingTop,10),g=Number.parseInt(d.borderBottomWidth,10),w=p+h+u+Number.parseInt(d.paddingBottom,10)+g,b=Math.min(5*x.offsetHeight,w),S=window.getComputedStyle(y),R=Number.parseInt(S.paddingTop,10),A=Number.parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,k=x.offsetHeight/2,P=p+h+(x.offsetTop+k);if(P<=T){const e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";const t=Math.max(s-T,k+(e?A:0)+(f.clientHeight-y.offsetTop-y.offsetHeight)+g);c.style.height=P+t+"px"}else{const e=l.length>0&&x===l[0].ref.current;c.style.top="0px";const t=Math.max(T,p+y.offsetTop+(e?R:0)+k);c.style.height=t+(w-P)+"px",y.scrollTop=P-T+y.offsetTop}c.style.margin="".concat(10,"px 0"),c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame(()=>v.current=!0)}},[m,a.trigger,a.valueNode,c,f,y,x,C,a.dir,o]);(0,b.N)(()=>A(),[A]);const[T,k]=r.useState();(0,b.N)(()=>{f&&k(window.getComputedStyle(f).zIndex)},[f]);const P=r.useCallback(e=>{e&&!0===w.current&&(A(),null==R||R(),w.current=!1)},[A,R]);return(0,S.jsx)(Q,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:v,onScrollButtonChange:P,children:(0,S.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,S.jsx)(g.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});$.displayName="SelectItemAlignedPosition";var J=r.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=M(n);return(0,S.jsx)(m.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[Q,ee]=N(U,{}),et="SelectViewport",en=r.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...i}=e,a=X(et,n),u=ee(et,n),c=(0,s.s)(t,a.onViewportChange),d=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(E.Slot,{scope:n,children:(0,S.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){const e=Math.abs(d.current-t.scrollTop);if(e>0){const r=window.innerHeight-20,o=Math.max(Number.parseFloat(n.style.minHeight),Number.parseFloat(n.style.height));if(o<r){const i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}d.current=t.scrollTop})})})]})});en.displayName=et;var er="SelectGroup",[eo,ei]=N(er),el=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,S.jsx)(eo,{scope:n,id:o,children:(0,S.jsx)(g.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});el.displayName=er;var ea="SelectLabel",es=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=ei(ea,n);return(0,S.jsx)(g.sG.div,{id:o.id,...r,ref:t})});es.displayName=ea;var eu="SelectItem",[ec,ed]=N(eu),ef=r.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,c=O(eu,n),d=X(eu,n),f=c.value===o,[p,m]=r.useState(null!=a?a:""),[v,w]=r.useState(!1),y=(0,s.s)(t,e=>{var t;return null===(t=d.itemRefCallback)||void 0===t?void 0:t.call(d,e,o,i)}),x=(0,h.B)(),b=r.useRef("touch"),C=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ec,{scope:n,value:o,disabled:i,textId:x,isSelected:f,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,S.jsx)(E.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(g.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,l.m)(u.onFocus,()=>w(!0)),onBlur:(0,l.m)(u.onBlur,()=>w(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{if(b.current=e.pointerType,i){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}else"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=d.onItemLeave)||void 0===t||t.call(d)}}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{var t;((null===(t=d.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(k.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{const{__scopeSelect:n,className:i,style:l,...a}=e,u=O(ep,n),c=X(ep,n),d=ed(ep,n),f=B(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),d.onItemTextChange,e=>{var t;return null===(t=c.itemTextRefCallback)||void 0===t?void 0:t.call(c,e,d.value,d.disabled)}),v=null==p?void 0:p.textContent,w=r.useMemo(()=>(0,S.jsx)("option",{value:d.value,disabled:d.disabled,children:v},d.value),[d.disabled,d.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=f;return(0,b.N)(()=>(y(w),()=>x(w)),[y,x,w]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.sG.span,{id:d.textId,...a,ref:m}),d.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",ev=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return ed(em,n).isSelected?(0,S.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ev.displayName=em;var eg="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{const n=X(eg,e.__scopeSelect),o=ee(eg,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){const e=()=> {l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var ey="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{const n=X(ey,e.__scopeSelect),o=ee(ey,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){const e=()=> {const e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...i}=e,a=X("SelectScrollButton",n),s=r.useRef(null),u=j(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{var e;const t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})}),eS=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return(0,S.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})});eS.displayName="SelectSeparator";var eC="SelectArrow";function eR(e){return""===e||void 0===e}r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=M(n),i=O(eC,n),l=X(eC,n);return i.open&&"popper"===l.position?(0,S.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eC;var eA=r.forwardRef((e,t)=>{const{value:n,...o}=e,i=r.useRef(null),l=(0,s.s)(t,i),a=((e)=> {const t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])})(n);return r.useEffect(()=>{const e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){const r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,S.jsx)(C,{asChild:!0,children:(0,S.jsx)("select",{...o,ref:l,defaultValue:n})})});function eT(e){const t=(0,y.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{const r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function ek(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));const s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}eA.displayName="BubbleSelect";var eP=F,eE=W,ej=z,eL=G,eN=K,eD=q,eM=en,eH=el,eO=es,eI=ef,eB=eh,eF=ev,eV=ew,eW=ex,e_=eS},4485:(e,t,n)=>{n.d(t,{Mz:()=>eX,i3:()=>e$,UC:()=>eZ,bL:()=>eY,Bk:()=>eN});var r=n(2149);const o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:i}=e,l=g(t),a=m(g(t)),s=v(a),u=p(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,w=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}const C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=S(u,r,s),f=r,p={},h=0;for(let n=0;n<a.length;n++){const{name:i,fn:m}=a[n],{x:v,y:g,data:w,reset:y}=await m({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:d}=S(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=f(t,e),m=x(h),v=a[p?"floating"===d?"reference":"floating":d],g=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),w="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),S=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:y,strategy:s}):w);return{top:(g.top-C.top+m.top)/S.y,bottom:(C.bottom-g.bottom+m.bottom)/S.y,left:(g.left-C.left+m.left)/S.x,right:(C.right-g.right+m.right)/S.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function k(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),s="y"===g(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=f(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:w}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof w&&(v="end"===a?-1*w:w),s?{x:v*c,y:m*u}:{x:m*u,y:v*c}}function P(){return"undefined"!=typeof window}function E(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!P()&&(e instanceof Node||e instanceof j(e).Node)}function D(e){return!!P()&&(e instanceof Element||e instanceof j(e).Element)}function M(e){return!!P()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function H(e){return!!P()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}function O(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){const t=F(),n=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(E(e))}function W(e){return j(e).getComputedStyle(e)}function _(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function z(e){if("html"===E(e))return e;const t=e.assignedSlot||e.parentNode||H(e)&&e.host||L(e);return H(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=function e(t){const n=z(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&O(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=j(o);if(i){const e=K(l);return t.concat(l,l.visualViewport||[],O(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function K(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function U(e){let t=W(e),n=Number.parseFloat(t.width)||0,r=Number.parseFloat(t.height)||0,o=M(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=a(n)!==i||a(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function q(e){return D(e)?e:e.contextElement}function Y(e){const t=q(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=U(t),l=(i?a(n.width):n.width)/r,s=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}const X=u(0);function Z(e){const t=j(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function $(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=q(e),a=u(1);t&&(r?D(r)&&(a=Y(r)):a=Y(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===j(l))&&o)?Z(l):u(0),c=(i.left+s.x)/a.x,d=(i.top+s.y)/a.y,f=i.width/a.x,p=i.height/a.y;if(l){let e=j(l),t=r&&D(r)?j(r):r,n=e,o=K(n);while(o&&r&&t!==n){const e=Y(o),t=o.getBoundingClientRect(),r=W(o),i=t.left+(o.clientLeft+Number.parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+Number.parseFloat(r.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=i,d+=l,o=K(n=j(o))}}return b({width:f,height:p,x:c,y:d})}function J(e,t){const n=_(e).scrollLeft;return t?t.left+n:$(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=((e,t)=> {let n=j(e),r=L(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;const e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}})(e,n);else if("document"===t)r=((e)=> {let t=L(e),n=_(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),s=-n.scrollTop;return"rtl"===W(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}})(L(e));else if(D(t))r=((e,t)=> {const n=$(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=M(e)?Y(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}})(t,n);else{const n=Z(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===W(e).position}function en(e,t){if(!M(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){const n=j(e);if(I(e))return n;if(!M(e)){let t=z(e);while(t&&!V(t)){if(D(t)&&!et(t))return t;t=z(t)}return n}let r=en(e,t);while(r&&["table","td","th"].includes(E(r))&&et(r))r=en(r,t);return r&&V(r)&&et(r)&&!B(r)?n:r||((e)=> {let t=z(e);while(M(t)&&!V(t)){if(B(t))return t;if(I(t))break;t=z(t)}return null})(e)||n}const eo=async function(e){const t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:((e,t,n)=> {let r=M(t),o=L(t),i="fixed"===n,l=$(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i){if(("body"!==E(t)||O(o))&&(a=_(t)),r){const e=$(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o))}i&&!r&&o&&(s.x=J(o));const c=!o||r||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}})(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:(e)=> {const{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=L(r),a=!!t&&I(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),d=u(0),f=M(r);if((f||!f&&!i)&&(("body"!==E(r)||O(l))&&(s=_(r)),M(r))){const e=$(r);c=Y(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}const p=!l||f||i?u(0):Q(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-s.scrollTop*c.y+d.y+p.y}},getDocumentElement:L,getClippingRect:function(e){const{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?I(t)?[]:((e,t)=> {const n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>D(e)&&"body"!==E(e)),o=null,i="fixed"===W(e).position,l=i?z(e):e;while(D(l)&&!V(l)){const t=W(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(l)&&!n&&function e(t,n){const r=z(t);return!(r===n||!D(r)||V(r))&&("fixed"===W(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=z(l)}return t.set(e,r),r})(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{const r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:(e)=> Array.from(e.getClientRects()),getDimensions:(e)=> {const{width:t,height:n}=U(e);return{width:t,height:n}},getScale:Y,isElement:D,isRTL:(e)=> "rtl"===W(e).direction};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}const ea=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let w=x(p),y={x:n,y:r},b=m(g(o)),S=v(b),C=await s.getDimensions(d),R="y"===b,A=R?"clientHeight":"clientWidth",T=a.reference[S]+a.reference[b]-y[b]-a.floating[S],k=y[b]-a.reference[b],P=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),E=P?P[A]:0;E&&await (null==s.isElement?void 0:s.isElement(P))||(E=u.floating[A]||a.floating[S]);const j=E/2-C[S]/2-1,L=i(w[R?"top":"left"],j),N=i(w[R?"bottom":"right"],j),D=E-C[S]-N,M=E/2-C[S]/2+(T/2-k/2),H=l(L,i(M,D)),O=!c.arrow&&null!=h(o)&&M!==H&&a.reference[S]/2-(M<L?L:N)-C[S]/2<0,I=O?M<L?M-L:M-D:0;return{[b]:y[b]+I,data:{[b]:H,centerOffset:M-H-I,...O&&{alignmentOffset:I}},reset:O}}}),es=(e,t,n)=>{const r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var eu=n(4632),ec="undefined"!=typeof document?r.useLayoutEffect:(()=> {});function ed(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ed(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ed(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){const n=ef(e);return Math.round(t*n)/n}function eh(e){const t=r.useRef(e);return ec(()=>{t.current=e}),t}const em=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),ev=(e,t)=>({...((e)=> (void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:a}=t,s=await k(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}))(e),options:[e,t]}),eg=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{const{x:t,y:n}=e;return{x:t,y:n}}},...c}=f(e,t),d={x:n,y:r},h=await R(t,c),v=g(p(o)),w=m(v),y=d[w],x=d[v];if(a){const e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=y+h[e],r=y-h[t];y=l(n,i(y,r))}if(s){const e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}const b=u.fn({...t,[w]:y,[v]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[v]:s}}}}}))(e),options:[e,t]}),ew=(e,t)=>({...((e)=> (void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=f(e,t),c={x:n,y:r},d=g(o),h=m(d),v=c[h],w=c[d],y=f(a,t),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){const e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;v<t?v=t:v>n&&(v=n)}if(u){var b,S;const e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[d])||0)+(t?0:x.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(S=l.offset)?void 0:S[d])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:v,[d]:w}}}))(e),options:[e,t]}),ey=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;const{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:x}=t,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:k=!0,...P}=f(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const E=p(a),j=g(c),L=p(c)===c,N=await (null==d.isRTL?void 0:d.isRTL(x.floating)),D=C||(L||!k?[y(c)]:((e)=> {const t=y(e);return[w(e),t,w(t)]})(c)),M="none"!==T;!C&&M&&D.push(...((e,t,n,r)=> {let o=h(e),i=((e,t,n)=> {const r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}})(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i})(c,k,T,N));let H=[c,...D],O=await R(t,P),I=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&I.push(O[E]),S){const e=((e,t,n)=> {void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=y(l)),[l,y(l)]})(a,u,N);I.push(O[e[0]],O[e[1]])}if(B=[...B,{placement:a,overflows:I}],!I.every(e=>e<=0)){const e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=H[e];if(t&&("alignment"!==S||j===g(t)||B.every(e=>e.overflows[0]>0&&g(e.placement)===j)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{const e=null==(l=B.filter(e=>{if(M){const t=g(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}))(e),options:[e,t]}),ex=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a;const{placement:s,rects:u,platform:c,elements:d}=t,{apply:m=()=>{},...v}=f(e,t),w=await R(t,v),y=p(s),x=h(s),b="y"===g(s),{width:S,height:C}=u.floating;"top"===y||"bottom"===y?(o=y,a=x===(await (null==c.isRTL?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(a=y,o="end"===x?"top":"bottom");let A=C-w.top-w.bottom,T=S-w.left-w.right,k=i(C-w[o],A),P=i(S-w[a],T),E=!t.middlewareData.shift,j=k,L=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(j=A),E&&!x){const e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?L=S-2*(0!==e||0!==t?e+t:l(w.left,w.right)):j=C-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:L,availableHeight:j});const N=await c.getDimensions(d.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}))(e),options:[e,t]}),eb=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{const e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{const e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}))(e),options:[e,t]}),eS=(e,t)=>({...em(e),options:[e,t]});var eC=n(6330),eR=n(8081),eA=r.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eT=n(8735),ek=n(4507),eP=n(2566),eE=n(5544),ej="Popper",[eL,eN]=(0,ek.A)(ej),[eD,eM]=eL(ej),eH=e=>{const{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eH.displayName=ej;var eO="PopperAnchor",eI=r.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...i}=e,l=eM(eO,n),a=r.useRef(null),s=(0,eT.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==o?void 0:o.current)||a.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:s})});eI.displayName=eO;var eB="PopperContent",[eF,eV]=eL(eB),eW=r.forwardRef((e,t)=>{var n,o,a,u,c,d,f,p;const{__scopePopper:h,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:w=0,arrowPadding:y=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:S=0,sticky:C="partial",hideWhenDetached:R=!1,updatePositionStrategy:A="optimized",onPlaced:T,...k}=e,P=eM(eB,h),[E,j]=r.useState(null),N=(0,eT.s)(t,e=>j(e)),[D,M]=r.useState(null),H=((e)=> {const[t,n]=r.useState(void 0);return(0,eE.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;const i=t[0];if("borderBoxSize"in i){const e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t})(D),O=null!==(f=null==H?void 0:H.width)&&void 0!==f?f:0,I=null!==(p=null==H?void 0:H.height)&&void 0!==p?p:0,B="number"==typeof S?S:{top:0,right:0,bottom:0,left:0,...S},F=Array.isArray(b)?b:[b],V=F.length>0,W={padding:B,boundary:F.filter(eK),altBoundary:V},{refs:_,floatingStyles:z,placement:K,isPositioned:U,middlewareData:Y}=((e)=> {void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ed(p,o)||h(o);const[m,v]=r.useState(null),[g,w]=r.useState(null),y=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),b=l||m,S=a||g,C=r.useRef(null),R=r.useRef(null),A=r.useRef(d),T=null!=u,k=eh(u),P=eh(i),E=eh(c),j=r.useCallback(()=>{if(!C.current||!R.current)return;const e={placement:t,strategy:n,middleware:p};P.current&&(e.platform=P.current),es(C.current,R.current,e).then(e=>{const t={...e,isPositioned:!1!==E.current};L.current&&!ed(A.current,t)&&(A.current=t,eu.flushSync(()=>{f(t)}))})},[p,t,n,P,E]);ec(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);const L=r.useRef(!1);ec(()=>(L.current=!0,()=>{L.current=!1}),[]),ec(()=>{if(b&&(C.current=b),S&&(R.current=S),b&&S){if(k.current)return k.current(b,S,j);j()}},[b,S,j,k,T]);const N=r.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:x}),[y,x]),D=r.useMemo(()=>({reference:b,floating:S}),[b,S]),M=r.useMemo(()=>{const e={position:n,left:0,top:0};if(!D.floating)return e;const t=ep(D.floating,d.x),r=ep(D.floating,d.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:j,refs:N,elements:D,floatingStyles:M}),[d,j,N,D,M])})({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:()=> {for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return ((e,t,n,r)=> {let o;void 0===r&&(r={});const{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=q(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&d?((e,t)=> {let n,r=null,o=L(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,d){void 0===c&&(c=!1),void 0===d&&(d=1),a();const f=e.getBoundingClientRect(),{left:p,top:h,width:m,height:v}=f;if(c||t(),!m||!v)return;let g=s(h),w=s(o.clientWidth-(p+m)),y={rootMargin:-g+"px "+-w+"px "+-s(o.clientHeight-(h+v))+"px "+-s(p)+"px",threshold:l(0,i(1,d))||1},x=!0;function b(t){const r=t[0].intersectionRatio;if(r!==d){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(f,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),a})(p,n):null,v=-1,g=null;c&&(g=new ResizeObserver(e=>{const[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let w=f?$(e):null;return f&&function t(){const r=$(e);w&&!el(w,r)&&n(),w=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}})(...t,{animationFrame:"always"===A})},elements:{reference:P.anchor},middleware:[ev({mainAxis:v+I,alignmentAxis:w}),x&&eg({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?ew():void 0,...W}),x&&ey({...W}),ex({...W,apply:e=>{const{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&eS({element:D,padding:y}),eU({arrowWidth:O,arrowHeight:I}),R&&eb({strategy:"referenceHidden",...W})]}),[X,Z]=eq(K),J=(0,eP.c)(T);(0,eE.N)(()=>{U&&(null==J||J())},[U,J]);const Q=null===(n=Y.arrow)||void 0===n?void 0:n.x,ee=null===(o=Y.arrow)||void 0===o?void 0:o.y,et=(null===(a=Y.arrow)||void 0===a?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eE.N)(()=>{E&&er(window.getComputedStyle(E).zIndex)},[E]),(0,eR.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:U?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(u=Y.transformOrigin)||void 0===u?void 0:u.x,null===(c=Y.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(d=Y.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eF,{scope:h,placedSide:X,onArrowChange:M,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,eR.jsx)(eC.sG.div,{"data-side":X,"data-align":Z,...k,ref:N,style:{...k.style,animation:U?void 0:"none"}})})})});eW.displayName=eB;var e_="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef((e,t)=> {const{__scopePopper:n,...r}=e,o=eV(e_,n),i=ez[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function eK(e){return null!==e}eG.displayName=e_;var eU=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[p,h]=eq(a),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!==(i=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+d/2,g=(null!==(l=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,w="",y="";return"bottom"===p?(w=c?m:"".concat(v,"px"),y="".concat(-f,"px")):"top"===p?(w=c?m:"".concat(v,"px"),y="".concat(s.floating.height+f,"px")):"right"===p?(w="".concat(-f,"px"),y=c?m:"".concat(g,"px")):"left"===p&&(w="".concat(s.floating.width+f,"px"),y=c?m:"".concat(g,"px")),{data:{x:w,y}}}});function eq(e){const[t,n="center"]=e.split("-");return[t,n]}var eY=eH,eX=eI,eZ=eW,e$=eG},6722:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(1018).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},6801:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(1018).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);