(self.webpackChunk_N_E = self.webpackChunk_N_E || []).push([
	[737],
	{
		1737: (n, r, e) => {
			e.d(r, { $n: () => w, D7: () => Q, NP: () => Z });
			var t = e(8099),
				o = e(8081),
				l = e(2149),
				s = e(3169),
				i = e(3629);
			function u() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover:not(:disabled) {\n      background-color: ",
					"dd;\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (u = () => n), n;
			}
			function d() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover:not(:disabled) {\n      background-color: ",
					"dd;\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (d = () => n), n;
			}
			function a() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid ",
					";\n    \n    &:hover:not(:disabled) {\n      background-color: ",
					";\n      color: ",
					";\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (a = () => n), n;
			}
			function c() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover:not(:disabled) {\n      background-color: ",
					"cc;\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (c = () => n), n;
			}
			function f() {
				const n = (0, t._)([
					"\n    background-color: transparent;\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover:not(:disabled) {\n      background-color: ",
					";\n      color: ",
					";\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (f = () => n), n;
			}
			function p() {
				const n = (0, t._)([
					"\n    background-color: transparent;\n    color: ",
					";\n    border: 1px solid transparent;\n    text-decoration: underline;\n    text-underline-offset: 4px;\n    \n    &:hover:not(:disabled) {\n      text-decoration: none;\n    }\n    \n    &:focus-visible {\n      outline: none;\n      box-shadow: 0 0 0 2px ",
					";\n    }\n  ",
				]);
				return (p = () => n), n;
			}
			function g() {
				const n = (0, t._)([
					"\n    height: 2.5rem;\n    padding: 0.5rem 1rem;\n    font-size: ",
					";\n  ",
				]);
				return (g = () => n), n;
			}
			function m() {
				const n = (0, t._)([
					"\n    height: 2.25rem;\n    padding: 0.5rem 0.75rem;\n    font-size: ",
					";\n    border-radius: calc(",
					" - 2px);\n  ",
				]);
				return (m = () => n), n;
			}
			function h() {
				const n = (0, t._)([
					"\n    height: 2.75rem;\n    padding: 0.5rem 2rem;\n    font-size: ",
					";\n    border-radius: calc(",
					" - 2px);\n  ",
				]);
				return (h = () => n), n;
			}
			function x() {
				const n = (0, t._)([
					"\n    height: 2.5rem;\n    width: 2.5rem;\n    padding: 0;\n  ",
				]);
				return (x = () => n), n;
			}
			function b() {
				const n = (0, t._)([
					"\n  /* Base styles */\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  white-space: nowrap;\n  border-radius: ",
					";\n  font-weight: ",
					";\n  transition: ",
					";\n  cursor: pointer;\n  user-select: none;\n  \n  /* SVG styles */\n  & svg {\n    pointer-events: none;\n    width: 1rem;\n    height: 1rem;\n    flex-shrink: 0;\n  }\n  \n  /* Disabled state */\n  &:disabled {\n    pointer-events: none;\n    opacity: 0.5;\n    cursor: not-allowed;\n  }\n  \n  /* Variant styles */\n  ",
					"\n  \n  /* Size styles */\n  ",
					"\n",
				]);
				return (b = () => n), n;
			}
			const y = {
					default: (0, s.AH)(
						u(),
						(n) => {
							const { theme: r } = n;
							return r.colors.primary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.primaryForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.primary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
					destructive: (0, s.AH)(
						d(),
						(n) => {
							const { theme: r } = n;
							return r.colors.destructive;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.destructiveForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.destructive;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
					outline: (0, s.AH)(
						a(),
						(n) => {
							const { theme: r } = n;
							return r.colors.background;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.foreground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.border;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.accent;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.accentForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
					secondary: (0, s.AH)(
						c(),
						(n) => {
							const { theme: r } = n;
							return r.colors.secondary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.secondaryForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.secondary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
					ghost: (0, s.AH)(
						f(),
						(n) => {
							const { theme: r } = n;
							return r.colors.foreground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.accent;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.accentForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
					link: (0, s.AH)(
						p(),
						(n) => {
							const { theme: r } = n;
							return r.colors.primary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.ring;
						},
					),
				},
				v = {
					default: (0, s.AH)(g(), (n) => {
						const { theme: r } = n;
						return r.sizes.fonts.sm;
					}),
					sm: (0, s.AH)(
						m(),
						(n) => {
							const { theme: r } = n;
							return r.sizes.fonts.sm;
						},
						(n) => {
							const { theme: r } = n;
							return r.sizes.borderRadius;
						},
					),
					lg: (0, s.AH)(
						h(),
						(n) => {
							const { theme: r } = n;
							return r.sizes.fonts.md;
						},
						(n) => {
							const { theme: r } = n;
							return r.sizes.borderRadius;
						},
					),
					icon: (0, s.AH)(x()),
				},
				k = s.Ay.button(
					b(),
					(n) => {
						const { theme: r } = n;
						return r.sizes.borderRadius;
					},
					(n) => {
						const { theme: r } = n;
						return r.fontWeights.medium;
					},
					(n) => {
						const { theme: r } = n;
						return r.transitions.default;
					},
					(n) => {
						const { variant: r = "default" } = n;
						return y[r];
					},
					(n) => {
						const { size: r = "default" } = n;
						return v[r];
					},
				),
				w = (0, l.forwardRef)((n, r) => {
					const {
							className: e,
							variant: t,
							size: l,
							asChild: s = !1,
							...u
						} = n,
						d = s ? i.DX : k;
					return (0, o.jsx)(d, {
						className: e,
						variant: t,
						size: l,
						ref: r,
						...u,
					});
				});
			function z() {
				const n = (0, t._)([
					"\n  display: flex;\n  height: ",
					";\n  width: 100%;\n  border-radius: ",
					";\n  border: 1px solid ",
					";\n  background-color: ",
					";\n  padding: 0.5rem 0.75rem;\n  font-size: ",
					";\n  color: ",
					";\n  transition: ",
					';\n  \n  /* File input styles */\n  &[type="file"] {\n    border: 0;\n    background-color: transparent;\n    font-size: ',
					";\n    font-weight: ",
					";\n    \n    &::file-selector-button {\n      border: 0;\n      background-color: transparent;\n      font-size: ",
					";\n      font-weight: ",
					";\n      color: ",
					";\n      margin-right: 0.5rem;\n    }\n  }\n  \n  /* Placeholder styles */\n  &::placeholder {\n    color: ",
					";\n  }\n  \n  /* Focus styles */\n  &:focus-visible {\n    outline: none;\n    box-shadow: 0 0 0 2px ",
					";\n    border-color: ",
					";\n  }\n  \n  /* Disabled styles */\n  &:disabled {\n    cursor: not-allowed;\n    opacity: 0.5;\n  }\n  \n  /* Responsive font size */\n  @media (max-width: ",
					") {\n    font-size: ",
					";\n  }\n  \n  @media (min-width: ",
					") {\n    font-size: ",
					";\n  }\n",
				]);
				return (z = () => n), n;
			}
			w.displayName = "Button";
			const F = s.Ay.input(
				z(),
				(n) => {
					const { theme: r } = n;
					return r.sizes.formControl;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.borderRadius;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.input;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.background;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.md;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.foreground;
				},
				(n) => {
					const { theme: r } = n;
					return r.transitions.default;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.sm;
				},
				(n) => {
					const { theme: r } = n;
					return r.fontWeights.medium;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.sm;
				},
				(n) => {
					const { theme: r } = n;
					return r.fontWeights.medium;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.foreground;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.mutedForeground;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.ring;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.ring;
				},
				(n) => {
					const { theme: r } = n;
					return r.breakpoints.md;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.md;
				},
				(n) => {
					const { theme: r } = n;
					return r.breakpoints.md;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.sm;
				},
			);
			function A() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover {\n      background-color: ",
					"cc;\n    }\n  ",
				]);
				return (A = () => n), n;
			}
			function _() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover {\n      background-color: ",
					"cc;\n    }\n  ",
				]);
				return (_ = () => n), n;
			}
			function H() {
				const n = (0, t._)([
					"\n    background-color: ",
					";\n    color: ",
					";\n    border: 1px solid transparent;\n    \n    &:hover {\n      background-color: ",
					"cc;\n    }\n  ",
				]);
				return (H = () => n), n;
			}
			function R() {
				const n = (0, t._)([
					"\n    background-color: transparent;\n    color: ",
					";\n    border: 1px solid ",
					";\n  ",
				]);
				return (R = () => n), n;
			}
			function C() {
				const n = (0, t._)([
					"\n  display: inline-flex;\n  align-items: center;\n  border-radius: 9999px;\n  padding: 0.125rem 0.625rem;\n  font-size: ",
					";\n  font-weight: ",
					";\n  transition: ",
					";\n  \n  &:focus {\n    outline: none;\n    box-shadow: 0 0 0 2px ",
					";\n  }\n  \n  /* Variant styles */\n  ",
					"\n",
				]);
				return (C = () => n), n;
			}
			(0, l.forwardRef)((n, r) => {
				const { type: e = "text", ...t } = n;
				return (0, o.jsx)(F, { ref: r, type: e, ...t });
			}).displayName = "Input";
			const N = {
					default: (0, s.AH)(
						A(),
						(n) => {
							const { theme: r } = n;
							return r.colors.primary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.primaryForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.primary;
						},
					),
					secondary: (0, s.AH)(
						_(),
						(n) => {
							const { theme: r } = n;
							return r.colors.secondary;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.secondaryForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.secondary;
						},
					),
					destructive: (0, s.AH)(
						H(),
						(n) => {
							const { theme: r } = n;
							return r.colors.destructive;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.destructiveForeground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.destructive;
						},
					),
					outline: (0, s.AH)(
						R(),
						(n) => {
							const { theme: r } = n;
							return r.colors.foreground;
						},
						(n) => {
							const { theme: r } = n;
							return r.colors.border;
						},
					),
				},
				j = s.Ay.div(
					C(),
					(n) => {
						const { theme: r } = n;
						return r.sizes.fonts.xs;
					},
					(n) => {
						const { theme: r } = n;
						return r.fontWeights.semibold;
					},
					(n) => {
						const { theme: r } = n;
						return r.transitions.default;
					},
					(n) => {
						const { theme: r } = n;
						return r.colors.ring;
					},
					(n) => {
						const { variant: r = "default" } = n;
						return N[r];
					},
				);
			function I() {
				const n = (0, t._)(["\n    padding: ", ";\n  "]);
				return (I = () => n), n;
			}
			function D() {
				const n = (0, t._)(["\n    padding: ", ";\n  "]);
				return (D = () => n), n;
			}
			function S() {
				const n = (0, t._)(["\n    padding: ", ";\n  "]);
				return (S = () => n), n;
			}
			function B() {
				const n = (0, t._)([
					"\n  border-radius: ",
					";\n  border: 1px solid ",
					";\n  background-color: ",
					";\n  color: ",
					";\n  box-shadow: ",
					";\n  transition: ",
					";\n  \n  ",
					"\n",
				]);
				return (B = () => n), n;
			}
			function T() {
				const n = (0, t._)([
					"\n  display: flex;\n  flex-direction: column;\n  gap: ",
					";\n  margin-bottom: ",
					";\n",
				]);
				return (T = () => n), n;
			}
			function G() {
				const n = (0, t._)([
					"\n  font-size: ",
					";\n  font-weight: ",
					";\n  line-height: ",
					";\n  letter-spacing: -0.025em;\n  margin: 0;\n",
				]);
				return (G = () => n), n;
			}
			function M() {
				const n = (0, t._)([
					"\n  font-size: ",
					";\n  color: ",
					";\n  line-height: ",
					";\n  margin: 0;\n",
				]);
				return (M = () => n), n;
			}
			function W() {
				const n = (0, t._)([
					"\n  /* Content has no default padding as it's handled by the card itself */\n",
				]);
				return (W = () => n), n;
			}
			function E() {
				const n = (0, t._)([
					"\n  display: flex;\n  align-items: center;\n  gap: ",
					";\n  margin-top: ",
					";\n",
				]);
				return (E = () => n), n;
			}
			(0, l.forwardRef)((n, r) => {
				const { variant: e = "default", ...t } = n;
				return (0, o.jsx)(j, { ref: r, variant: e, ...t });
			}).displayName = "Badge";
			const L = {
					sm: (0, s.AH)(I(), (n) => {
						const { theme: r } = n;
						return r.sizes.spacing.lg;
					}),
					md: (0, s.AH)(D(), (n) => {
						const { theme: r } = n;
						return r.sizes.spacing.xl;
					}),
					lg: (0, s.AH)(S(), (n) => {
						const { theme: r } = n;
						return r.sizes.spacing.xxl;
					}),
				},
				K = s.Ay.div(
					B(),
					(n) => {
						const { theme: r } = n;
						return r.sizes.borderRadius;
					},
					(n) => {
						const { theme: r } = n;
						return r.colors.border;
					},
					(n) => {
						const { theme: r } = n;
						return r.colors.card;
					},
					(n) => {
						const { theme: r } = n;
						return r.colors.cardForeground;
					},
					(n) => {
						const { theme: r } = n;
						return r.shadows.sm;
					},
					(n) => {
						const { theme: r } = n;
						return r.transitions.default;
					},
					(n) => {
						const { size: r = "md" } = n;
						return L[r];
					},
				);
			(0, l.forwardRef)((n, r) => {
				const { size: e = "md", ...t } = n;
				return (0, o.jsx)(K, { ref: r, size: e, ...t });
			}).displayName = "Card";
			const P = s.Ay.div(
				T(),
				(n) => {
					const { theme: r } = n;
					return r.sizes.spacing.sm;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.spacing.xl;
				},
			);
			(0, l.forwardRef)((n, r) => (0, o.jsx)(P, { ref: r, ...n })).displayName =
				"CardHeader";
			const V = s.Ay.h3(
				G(),
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.xl;
				},
				(n) => {
					const { theme: r } = n;
					return r.fontWeights.semibold;
				},
				(n) => {
					const { theme: r } = n;
					return r.lineHeights.tight;
				},
			);
			(0, l.forwardRef)((n, r) => (0, o.jsx)(V, { ref: r, ...n })).displayName =
				"CardTitle";
			const O = s.Ay.p(
				M(),
				(n) => {
					const { theme: r } = n;
					return r.sizes.fonts.sm;
				},
				(n) => {
					const { theme: r } = n;
					return r.colors.mutedForeground;
				},
				(n) => {
					const { theme: r } = n;
					return r.lineHeights.normal;
				},
			);
			(0, l.forwardRef)((n, r) => (0, o.jsx)(O, { ref: r, ...n })).displayName =
				"CardDescription";
			const U = s.Ay.div(W());
			(0, l.forwardRef)((n, r) => (0, o.jsx)(U, { ref: r, ...n })).displayName =
				"CardContent";
			const X = s.Ay.div(
				E(),
				(n) => {
					const { theme: r } = n;
					return r.sizes.spacing.md;
				},
				(n) => {
					const { theme: r } = n;
					return r.sizes.spacing.xl;
				},
			);
			(0, l.forwardRef)((n, r) => (0, o.jsx)(X, { ref: r, ...n })).displayName =
				"CardFooter";
			const $ = {
					sizes: {
						borderRadius: "0.5rem",
						formControl: "2.5rem",
						fonts: {
							xxs: "0.625rem",
							xs: "0.75rem",
							sm: "0.875rem",
							md: "1rem",
							lg: "1.125rem",
							xl: "1.25rem",
							xxl: "1.5rem",
							xxxl: "2rem",
						},
						spacing: {
							xxs: "0.125rem",
							xs: "0.25rem",
							sm: "0.5rem",
							md: "0.75rem",
							lg: "1rem",
							xl: "1.5rem",
							xxl: "2rem",
							xxxl: "3rem",
						},
					},
					fonts: {
						body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
						heading:
							"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
						mono: "'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace",
					},
					fontWeights: { normal: 400, medium: 500, semibold: 600, bold: 700 },
					lineHeights: { tight: 1.25, normal: 1.5, relaxed: 1.75 },
					zIndicies: {
						loadingOverlay: 9e3,
						dropdownMenu: 8e3,
						dialog: 7e3,
						popover: 6e3,
						tooltip: 5e3,
						sticky: 1e3,
					},
					shadows: {
						sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
						default:
							"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
						md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
						lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
						xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
						inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
					},
					transitions: {
						default: "all 0.2s ease-in-out",
						fast: "all 0.1s ease-in-out",
						slow: "all 0.3s ease-in-out",
					},
					breakpoints: {
						xs: "400px",
						sm: "600px",
						md: "900px",
						lg: "1280px",
						xl: "1440px",
						xxl: "1920px",
					},
					colors: {
						error: "#E74C3C",
						success: "#27AE60",
						warning: "#F1C40F",
						info: "#3498DB",
						white: "#FFFFFF",
						black: "#000000",
						transparent: "transparent",
					},
				},
				q = {
					...$,
					colors: {
						...$.colors,
						background: "hsl(0 0% 100%)",
						foreground: "hsl(0 0% 3.9%)",
						contentBg: "hsl(0 0% 100%)",
						card: "hsl(0 0% 100%)",
						cardForeground: "hsl(0 0% 3.9%)",
						popover: "hsl(0 0% 100%)",
						popoverForeground: "hsl(0 0% 3.9%)",
						primary: "hsl(0 0% 9%)",
						primaryForeground: "hsl(0 0% 98%)",
						secondary: "hsl(0 0% 96.1%)",
						secondaryForeground: "hsl(0 0% 9%)",
						muted: "hsl(0 0% 96.1%)",
						mutedForeground: "hsl(0 0% 45.1%)",
						accent: "hsl(0 0% 96.1%)",
						accentForeground: "hsl(0 0% 9%)",
						destructive: "hsl(0 84.2% 60.2%)",
						destructiveForeground: "hsl(0 0% 98%)",
						border: "hsl(0 0% 89.8%)",
						input: "hsl(0 0% 89.8%)",
						ring: "hsl(0 0% 3.9%)",
					},
				},
				J = {
					...$,
					colors: {
						...$.colors,
						background: "hsl(0 0% 3.9%)",
						foreground: "hsl(0 0% 98%)",
						contentBg: "hsl(0 0% 3.9%)",
						card: "hsl(0 0% 3.9%)",
						cardForeground: "hsl(0 0% 98%)",
						popover: "hsl(0 0% 3.9%)",
						popoverForeground: "hsl(0 0% 98%)",
						primary: "hsl(0 0% 98%)",
						primaryForeground: "hsl(0 0% 9%)",
						secondary: "hsl(0 0% 14.9%)",
						secondaryForeground: "hsl(0 0% 98%)",
						muted: "hsl(0 0% 14.9%)",
						mutedForeground: "hsl(0 0% 63.9%)",
						accent: "hsl(0 0% 14.9%)",
						accentForeground: "hsl(0 0% 98%)",
						destructive: "hsl(0 62.8% 30.6%)",
						destructiveForeground: "hsl(0 0% 98%)",
						border: "hsl(0 0% 14.9%)",
						input: "hsl(0 0% 14.9%)",
						ring: "hsl(0 0% 83.1%)",
					},
				};
			$.breakpoints;
			const Q = { LIGHT: "light", DARK: "dark" },
				Y = (0, l.createContext)(void 0);
			function Z(n) {
				const {
						children: r,
						defaultTheme: e = Q.LIGHT,
						storageKey: t = "dua-ui-theme",
						enableSystem: i = !0,
					} = n,
					[u, d] = (0, l.useState)(e);
				(0, l.useEffect)(() => {
					const n = localStorage.getItem(t);
					n && Object.values(Q).includes(n)
						? d(n)
						: i &&
							d(
								window.matchMedia("(prefers-color-scheme: dark)").matches
									? Q.DARK
									: Q.LIGHT,
							);
				}, [t, i]);
				const a = (n) => {
						d(n), localStorage.setItem(t, n);
					},
					c = u === Q.DARK ? J : q;
				return (0, o.jsx)(Y.Provider, {
					value: {
						theme: u,
						setTheme: a,
						toggleTheme: () => {
							a(u === Q.LIGHT ? Q.DARK : Q.LIGHT);
						},
					},
					children: (0, o.jsx)(s.NP, { theme: c, children: r }),
				});
			}
		},
	},
]);
