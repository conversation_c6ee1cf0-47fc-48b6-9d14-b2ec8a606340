exports.id=227,exports.ids=[227],exports.modules={11162:(e,t,n)=>{n.d(t,{Mz:()=>eY,i3:()=>eZ,UC:()=>eX,bL:()=>eq,Bk:()=>eN});var r=n(13072);const o=["top","right","bottom","left"],i=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>f[e])}function y(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function S(e,t,n){let r,{reference:o,floating:i}=e,l=v(t),a=m(v(t)),s=g(a),u=p(t),c="y"===l,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,w=o[s]/2-i[s]/2;switch(u){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[a]-=w*(n&&c?-1:1);break;case"end":r[a]+=w*(n&&c?-1:1)}return r}const C=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=S(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){const{name:i,fn:m}=a[n],{x:g,y:v,data:w,reset:y}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,f=null!=v?v:f,p={...p,[i]:{...p[i],...w}},y&&h<=50&&(h++,"object"==typeof y&&(y.placement&&(d=y.placement),y.rects&&(u=!0===y.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):y.rects),{x:c,y:f}=S(u,d,s)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function R(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===f?"reference":"floating":f],v=b(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),w="floating"===f?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),S=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},C=b(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:w,offsetParent:y,strategy:s}):w);return{top:(v.top-C.top+m.top)/S.y,bottom:(C.bottom-v.bottom+m.bottom)/S.y,left:(v.left-C.left+m.left)/S.x,right:(C.right-v.right+m.right)/S.x}}function A(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return o.some(t=>e[t]>=0)}async function P(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=p(n),a=h(n),s="y"===v(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,f=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:w}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&"number"==typeof w&&(g="end"===a?-1*w:w),s?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function j(){return"undefined"!=typeof window}function E(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!j()&&(e instanceof Node||e instanceof k(e).Node)}function D(e){return!!j()&&(e instanceof Element||e instanceof k(e).Element)}function M(e){return!!j()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function H(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function O(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=W(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){const t=F(),n=D(e)?W(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function F(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(E(e))}function W(e){return k(e).getComputedStyle(e)}function z(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _(e){if("html"===E(e))return e;const t=e.assignedSlot||e.parentNode||H(e)&&e.host||L(e);return H(t)?t.host:t}function G(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=function e(t){const n=_(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&O(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=k(o);if(i){const e=$(l);return t.concat(l,l.visualViewport||[],O(o)?o:[],e&&n?G(e):[])}return t.concat(o,G(o,[],n))}function $(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function K(e){let t=W(e),n=Number.parseFloat(t.width)||0,r=Number.parseFloat(t.height)||0,o=M(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,s=a(n)!==i||a(r)!==l;return s&&(n=i,r=l),{width:n,height:r,$:s}}function U(e){return D(e)?e:e.contextElement}function q(e){const t=U(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=K(t),l=(i?a(n.width):n.width)/r,s=(i?a(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}const Y=u(0);function X(e){const t=k(e);return F()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:Y}function Z(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=U(e),a=u(1);t&&(r?D(r)&&(a=q(r)):a=q(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===k(l))&&o)?X(l):u(0),c=(i.left+s.x)/a.x,f=(i.top+s.y)/a.y,d=i.width/a.x,p=i.height/a.y;if(l){let e=k(l),t=r&&D(r)?k(r):r,n=e,o=$(n);while(o&&r&&t!==n){const e=q(o),t=o.getBoundingClientRect(),r=W(o),i=t.left+(o.clientLeft+Number.parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+Number.parseFloat(r.paddingTop))*e.y;c*=e.x,f*=e.y,d*=e.x,p*=e.y,c+=i,f+=l,o=$(n=k(o))}}return b({width:d,height:p,x:c,y:f})}function J(e,t){const n=z(e).scrollLeft;return t?t.left+n:Z(L(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=((e,t)=> {let n=k(e),r=L(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;const e=F();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}})(e,n);else if("document"===t)r=((e)=> {let t=L(e),n=z(e),r=e.ownerDocument.body,o=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+J(e),s=-n.scrollTop;return"rtl"===W(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}})(L(e));else if(D(t))r=((e,t)=> {const n=Z(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=M(e)?q(e):u(1),l=e.clientWidth*i.x,a=e.clientHeight*i.y;return{width:l,height:a,x:o*i.x,y:r*i.y}})(t,n);else{const n=X(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===W(e).position}function en(e,t){if(!M(e)||"fixed"===W(e).position)return null;if(t)return t(e);let n=e.offsetParent;return L(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){const n=k(e);if(I(e))return n;if(!M(e)){let t=_(e);while(t&&!V(t)){if(D(t)&&!et(t))return t;t=_(t)}return n}let r=en(e,t);while(r&&["table","td","th"].includes(E(r))&&et(r))r=en(r,t);return r&&V(r)&&et(r)&&!B(r)?n:r||((e)=> {let t=_(e);while(M(t)&&!V(t)){if(B(t))return t;if(I(t))break;t=_(t)}return null})(e)||n}const eo=async function(e){const t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:((e,t,n)=> {let r=M(t),o=L(t),i="fixed"===n,l=Z(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!i){if(("body"!==E(t)||O(o))&&(a=z(t)),r){const e=Z(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=J(o))}i&&!r&&o&&(s.x=J(o));const c=!o||r||i?u(0):Q(o,a);return{x:l.left+a.scrollLeft-s.x-c.x,y:l.top+a.scrollTop-s.y-c.y,width:l.width,height:l.height}})(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:(e)=> {const{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=L(r),a=!!t&&I(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},c=u(1),f=u(0),d=M(r);if((d||!d&&!i)&&(("body"!==E(r)||O(l))&&(s=z(r)),M(r))){const e=Z(r);c=q(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}const p=!l||d||i?u(0):Q(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+f.x+p.x,y:n.y*c.y-s.scrollTop*c.y+f.y+p.y}},getDocumentElement:L,getClippingRect:function(e){const{element:t,boundary:n,rootBoundary:r,strategy:o}=e,a=[..."clippingAncestors"===n?I(t)?[]:((e,t)=> {const n=t.get(e);if(n)return n;let r=G(e,[],!1).filter(e=>D(e)&&"body"!==E(e)),o=null,i="fixed"===W(e).position,l=i?_(e):e;while(D(l)&&!V(l)){const t=W(l),n=B(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||O(l)&&!n&&function e(t,n){const r=_(t);return!(r===n||!D(r)||V(r))&&("fixed"===W(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=_(l)}return t.set(e,r),r})(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{const r=ee(t,n,o);return e.top=l(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,s,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:eo,getClientRects:(e)=> Array.from(e.getClientRects()),getDimensions:(e)=> {const{width:t,height:n}=K(e);return{width:t,height:n}},getScale:q,isElement:D,isRTL:(e)=> "rtl"===W(e).direction};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}const ea=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:s,elements:u,middlewareData:c}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let w=x(p),y={x:n,y:r},b=m(v(o)),S=g(b),C=await s.getDimensions(f),R="y"===b,A=R?"clientHeight":"clientWidth",T=a.reference[S]+a.reference[b]-y[b]-a.floating[S],P=y[b]-a.reference[b],j=await (null==s.getOffsetParent?void 0:s.getOffsetParent(f)),E=j?j[A]:0;E&&await (null==s.isElement?void 0:s.isElement(j))||(E=u.floating[A]||a.floating[S]);const k=E/2-C[S]/2-1,L=i(w[R?"top":"left"],k),N=i(w[R?"bottom":"right"],k),D=E-C[S]-N,M=E/2-C[S]/2+(T/2-P/2),H=l(L,i(M,D)),O=!c.arrow&&null!=h(o)&&M!==H&&a.reference[S]/2-(M<L?L:N)-C[S]/2<0,I=O?M<L?M-L:M-D:0;return{[b]:y[b]+I,data:{[b]:H,centerOffset:M-H-I,...O&&{alignmentOffset:I}},reset:O}}}),es=(e,t,n)=>{const r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return C(e,t,{...o,platform:i})};var eu=n(76129),ec="undefined"!=typeof document?r.useLayoutEffect:(()=> {});function ef(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!ef(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!ef(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){const n=ed(e);return Math.round(t*n)/n}function eh(e){const t=r.useRef(e);return ec(()=>{t.current=e}),t}const em=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...((e)=> (void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:l,middlewareData:a}=t,s=await P(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}))(e),options:[e,t]}),ev=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{const{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),f={x:n,y:r},h=await R(t,c),g=v(p(o)),w=m(g),y=f[w],x=f[g];if(a){const e="y"===w?"top":"left",t="y"===w?"bottom":"right",n=y+h[e],r=y-h[t];y=l(n,i(y,r))}if(s){const e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,i(x,r))}const b=u.fn({...t,[w]:y,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:a,[g]:s}}}}}))(e),options:[e,t]}),ew=(e,t)=>({...((e)=> (void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},f=v(o),h=m(f),g=c[h],w=c[f],y=d(a,t),x="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){const e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+x.mainAxis,n=i.reference[h]+i.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,S;const e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(b=l.offset)?void 0:b[f])||0)+(t?0:x.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(S=l.offset)?void 0:S[f])||0)-(t?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[h]:g,[f]:w}}}))(e),options:[e,t]}),ey=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;const{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:f,elements:x}=t,{mainAxis:b=!0,crossAxis:S=!0,fallbackPlacements:C,fallbackStrategy:A="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:P=!0,...j}=d(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};const E=p(a),k=v(c),L=p(c)===c,N=await (null==f.isRTL?void 0:f.isRTL(x.floating)),D=C||(L||!P?[y(c)]:((e)=> {const t=y(e);return[w(e),t,w(t)]})(c)),M="none"!==T;!C&&M&&D.push(...((e,t,n,r)=> {let o=h(e),i=((e,t,n)=> {const r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}})(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i})(c,P,T,N));let H=[c,...D],O=await R(t,j),I=[],B=(null==(r=s.flip)?void 0:r.overflows)||[];if(b&&I.push(O[E]),S){const e=((e,t,n)=> {void 0===n&&(n=!1);let r=h(e),o=m(v(e)),i=g(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=y(l)),[l,y(l)]})(a,u,N);I.push(O[e[0]],O[e[1]])}if(B=[...B,{placement:a,overflows:I}],!I.every(e=>e<=0)){const e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=H[e];if(t&&("alignment"!==S||k===v(t)||B.every(e=>e.overflows[0]>0&&v(e.placement)===k)))return{data:{index:e,overflows:B},reset:{placement:t}};let n=null==(i=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(A){case"bestFit":{const e=null==(l=B.filter(e=>{if(M){const t=v(e.placement);return t===k||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}))(e),options:[e,t]}),ex=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,a;const{placement:s,rects:u,platform:c,elements:f}=t,{apply:m=()=>{},...g}=d(e,t),w=await R(t,g),y=p(s),x=h(s),b="y"===v(s),{width:S,height:C}=u.floating;"top"===y||"bottom"===y?(o=y,a=x===(await (null==c.isRTL?void 0:c.isRTL(f.floating))?"start":"end")?"left":"right"):(a=y,o="end"===x?"top":"bottom");let A=C-w.top-w.bottom,T=S-w.left-w.right,P=i(C-w[o],A),j=i(S-w[a],T),E=!t.middlewareData.shift,k=P,L=j;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(L=T),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(k=A),E&&!x){const e=l(w.left,0),t=l(w.right,0),n=l(w.top,0),r=l(w.bottom,0);b?L=S-2*(0!==e||0!==t?e+t:l(w.left,w.right)):k=C-2*(0!==n||0!==r?n+r:l(w.top,w.bottom))}await m({...t,availableWidth:L,availableHeight:k});const N=await c.getDimensions(f.floating);return S!==N.width||C!==N.height?{reset:{rects:!0}}:{}}}))(e),options:[e,t]}),eb=(e,t)=>({...((e)=> (void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=d(e,t);switch(r){case"referenceHidden":{const e=A(await R(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{const e=A(await R(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}))(e),options:[e,t]}),eS=(e,t)=>({...em(e),options:[e,t]});var eC=n(60110),eR=n(45781),eA=r.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return(0,eR.jsx)(eC.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eA.displayName="Arrow";var eT=n(47851),eP=n(61779),ej=n(99210),eE=n(63904),ek="Popper",[eL,eN]=(0,eP.A)(ek),[eD,eM]=eL(ek),eH=e=>{const{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eR.jsx)(eD,{scope:t,anchor:o,onAnchorChange:i,children:n})};eH.displayName=ek;var eO="PopperAnchor",eI=r.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:o,...i}=e,l=eM(eO,n),a=r.useRef(null),s=(0,eT.s)(t,a);return r.useEffect(()=>{l.onAnchorChange(o?.current||a.current)}),o?null:(0,eR.jsx)(eC.sG.div,{...i,ref:s})});eI.displayName=eO;var eB="PopperContent",[eF,eV]=eL(eB),eW=r.forwardRef((e,t)=>{const{__scopePopper:n,side:o="bottom",sideOffset:a=0,align:u="center",alignOffset:c=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:w,...y}=e,x=eM(eB,n),[b,S]=r.useState(null),C=(0,eT.s)(t,e=>S(e)),[R,A]=r.useState(null),T=((e)=> {const[t,n]=r.useState(void 0);return(0,eE.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;const i=t[0];if("borderBoxSize"in i){const e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t})(R),P=T?.width??0,j=T?.height??0,E="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},k=Array.isArray(p)?p:[p],N=k.length>0,D={padding:E,boundary:k.filter(e$),altBoundary:N},{refs:M,floatingStyles:H,placement:O,isPositioned:I,middlewareData:B}=((e)=> {void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:c}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);ef(p,o)||h(o);const[m,g]=r.useState(null),[v,w]=r.useState(null),y=r.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),x=r.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),b=l||m,S=a||v,C=r.useRef(null),R=r.useRef(null),A=r.useRef(f),T=null!=u,P=eh(u),j=eh(i),E=eh(c),k=r.useCallback(()=>{if(!C.current||!R.current)return;const e={placement:t,strategy:n,middleware:p};j.current&&(e.platform=j.current),es(C.current,R.current,e).then(e=>{const t={...e,isPositioned:!1!==E.current};L.current&&!ef(A.current,t)&&(A.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,j,E]);ec(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);const L=r.useRef(!1);ec(()=>(L.current=!0,()=>{L.current=!1}),[]),ec(()=>{if(b&&(C.current=b),S&&(R.current=S),b&&S){if(P.current)return P.current(b,S,k);k()}},[b,S,k,P,T]);const N=r.useMemo(()=>({reference:C,floating:R,setReference:y,setFloating:x}),[y,x]),D=r.useMemo(()=>({reference:b,floating:S}),[b,S]),M=r.useMemo(()=>{const e={position:n,left:0,top:0};if(!D.floating)return e;const t=ep(D.floating,f.x),r=ep(D.floating,f.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,D.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:k,refs:N,elements:D,floatingStyles:M}),[f,k,N,D,M])})({strategy:"fixed",placement:o+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>((e,t,n,r)=> {let o;void 0===r&&(r={});const{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=U(e),h=a||u?[...p?G(p):[],...G(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&f?((e,t)=> {let n,r=null,o=L(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function u(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),a();const d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let v=s(h),w=s(o.clientWidth-(p+m)),y={rootMargin:-v+"px "+-w+"px "+-s(o.clientHeight-(h+g))+"px "+-s(p)+"px",threshold:l(0,i(1,f))||1},x=!0;function b(t){const r=t[0].intersectionRatio;if(r!==f){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),a})(p,n):null,g=-1,v=null;c&&(v=new ResizeObserver(e=>{const[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let w=d?Z(e):null;return d&&function t(){const r=Z(e);w&&!el(w,r)&&n(),w=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===v}),elements:{reference:x.anchor},middleware:[eg({mainAxis:a+j,alignmentAxis:c}),d&&ev({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ew():void 0,...D}),d&&ey({...D}),ex({...D,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),R&&eS({element:R,padding:f}),eK({arrowWidth:P,arrowHeight:j}),g&&eb({strategy:"referenceHidden",...D})]}),[F,V]=eU(O),W=(0,ej.c)(w);(0,eE.N)(()=>{I&&W?.()},[I,W]);const z=B.arrow?.x,_=B.arrow?.y,$=B.arrow?.centerOffset!==0,[K,q]=r.useState();return(0,eE.N)(()=>{b&&q(window.getComputedStyle(b).zIndex)},[b]),(0,eR.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:I?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:K,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eR.jsx)(eF,{scope:n,placedSide:F,onArrowChange:A,arrowX:z,arrowY:_,shouldHideArrow:$,children:(0,eR.jsx)(eC.sG.div,{"data-side":F,"data-align":V,...y,ref:C,style:{...y.style,animation:I?void 0:"none"}})})})});eW.displayName=eB;var ez="PopperArrow",e_={top:"bottom",right:"left",bottom:"top",left:"right"},eG=r.forwardRef((e,t)=> {const{__scopePopper:n,...r}=e,o=eV(ez,n),i=e_[o.placedSide];return(0,eR.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eR.jsx)(eA,{...r,ref:t,style:{...r.style,display:"block"}})})});function e$(e){return null!==e}eG.displayName=ez;var eK=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=eU(n),c={start:"0%",center:"50%",end:"100%"}[u],f=(o.arrow?.x??0)+l/2,d=(o.arrow?.y??0)+a/2,p="",h="";return"bottom"===s?(p=i?c:`${f}px`,h=`${-a}px`):"top"===s?(p=i?c:`${f}px`,h=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,h=i?c:`${d}px`):"left"===s&&(p=`${r.floating.width+a}px`,h=i?c:`${d}px`),{data:{x:p,y:h}}}});function eU(e){const[t,n="center"]=e.split("-");return[t,n]}var eq=eH,eY=eI,eX=eW,eZ=eG},12265:(e,t,n)=>{n.d(t,{UC:()=>eD,YJ:()=>eH,In:()=>eL,q7:()=>eI,VF:()=>eF,p4:()=>eB,JU:()=>eO,ZL:()=>eN,bL:()=>ej,wn:()=>eW,PP:()=>eV,wv:()=>ez,l9:()=>eE,WT:()=>ek,LM:()=>eM});var r=n(13072),o=n(76129);function i(e,[t,n]){return Math.min(n,Math.max(t,e))}var l=n(85271),a=n(113),s=n(47851),u=n(61779),c=n(39092),f=n(47311),d=n(91403),p=n(7678),h=n(35414),m=n(11162),g=n(1377),v=n(60110),w=n(74645),y=n(99210),x=n(52650),b=n(63904),S=n(45781),C=r.forwardRef((e,t)=>(0,S.jsx)(v.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));C.displayName="VisuallyHidden";var R=n(25097),A=n(42254),T=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],j="Select",[E,k,L]=(0,a.N)(j),[N,D]=(0,u.A)(j,[L,m.Bk]),M=(0,m.Bk)(),[H,O]=N(j),[I,B]=N(j),F=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:g,required:v,form:w}=e,y=M(t),[b,C]=r.useState(null),[R,A]=r.useState(null),[T,P]=r.useState(!1),j=(0,c.jH)(f),[k=!1,L]=(0,x.i)({prop:o,defaultProp:i,onChange:l}),[N,D]=(0,x.i)({prop:a,defaultProp:s,onChange:u}),O=r.useRef(null),B=!b||w||!!b.closest("form"),[F,V]=r.useState(new Set),W=Array.from(F).map(e=>e.props.value).join(";");return(0,S.jsx)(m.bL,{...y,children:(0,S.jsxs)(H,{required:v,scope:t,trigger:b,onTriggerChange:C,valueNode:R,onValueNodeChange:A,valueNodeHasChildren:T,onValueNodeHasChildrenChange:P,contentId:(0,h.B)(),value:N,onValueChange:D,open:k,onOpenChange:L,dir:j,triggerPointerDownPosRef:O,disabled:g,children:[(0,S.jsx)(E.Provider,{scope:t,children:(0,S.jsx)(I,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{V(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{V(t=>{const n=new Set(t);return n.delete(e),n})},[]),children:n})}),B?(0,S.jsxs)(eA,{"aria-hidden":!0,required:v,tabIndex:-1,name:d,autoComplete:p,value:N,onChange:e=>D(e.target.value),disabled:g,form:w,children:[void 0===N?(0,S.jsx)("option",{value:""}):null,Array.from(F)]},W):null]})})};F.displayName=j;var V="SelectTrigger",W=r.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:o=!1,...i}=e,a=M(n),u=O(V,n),c=u.disabled||o,f=(0,s.s)(t,u.onTriggerChange),d=k(n),p=r.useRef("touch"),[h,g,w]=eT(e=>{const t=d().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eP(t,e,n);void 0!==r&&u.onValueChange(r.value)}),y=e=>{c||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(m.Mz,{asChild:!0,...a,children:(0,S.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":eR(u.value)?"":void 0,...i,ref:f,onClick:(0,l.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==p.current&&y(e)}),onPointerDown:(0,l.m)(i.onPointerDown,e=>{p.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,l.m)(i.onKeyDown,e=>{const t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&T.includes(e.key)&&(y(),e.preventDefault())})})})});W.displayName=V;var z="SelectValue",_=r.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,u=O(z,n),{onValueNodeHasChildrenChange:c}=u,f=void 0!==i,d=(0,s.s)(t,u.onValueNodeChange);return(0,b.N)(()=>{c(f)},[c,f]),(0,S.jsx)(v.sG.span,{...a,ref:d,style:{pointerEvents:"none"},children:eR(u.value)?(0,S.jsx)(S.Fragment,{children:l}):i})});_.displayName=z;var G=r.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});G.displayName="SelectIcon";var $=e=>(0,S.jsx)(g.Z,{asChild:!0,...e});$.displayName="SelectPortal";var K="SelectContent",U=r.forwardRef((e,t)=>{const n=O(K,e.__scopeSelect),[i,l]=r.useState();return((0,b.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,S.jsx)(X,{...e,ref:t}):i?o.createPortal((0,S.jsx)(q,{scope:e.__scopeSelect,children:(0,S.jsx)(E.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),i):null});U.displayName=K;var[q,Y]=N(K),X=r.forwardRef((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:C,avoidCollisions:T,...P}=e,j=O(K,n),[E,L]=r.useState(null),[N,D]=r.useState(null),M=(0,s.s)(t,e=>L(e)),[H,I]=r.useState(null),[B,F]=r.useState(null),V=k(n),[W,z]=r.useState(!1),_=r.useRef(!1);r.useEffect(()=>{if(E)return(0,R.Eq)(E)},[E]),(0,d.Oh)();const G=r.useCallback(e=>{const[t,...n]=V().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(const n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&N&&(N.scrollTop=0),n===r&&N&&(N.scrollTop=N.scrollHeight),n?.focus(),document.activeElement!==o))return},[V,N]),$=r.useCallback(()=>G([H,E]),[G,H,E]);r.useEffect(()=>{W&&$()},[W,$]);const{onOpenChange:U,triggerPointerDownPosRef:Y}=j;r.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Y.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Y.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():E.contains(n.target)||U(!1),document.removeEventListener("pointermove",t),Y.current=null};return null!==Y.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[E,U,Y]),r.useEffect(()=>{const e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);const[X,Q]=eT(e=>{const t=V().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eP(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),ee=r.useCallback((e,t,n)=>{const r=!_.current&&!n;(void 0!==j.value&&j.value===t||r)&&(I(e),r&&(_.current=!0))},[j.value]),et=r.useCallback(()=>E?.focus(),[E]),en=r.useCallback((e,t,n)=>{const r=!_.current&&!n;(void 0!==j.value&&j.value===t||r)&&F(e)},[j.value]),er="popper"===o?J:Z,eo=er===J?{side:c,sideOffset:h,align:m,alignOffset:g,arrowPadding:v,collisionBoundary:y,collisionPadding:x,sticky:b,hideWhenDetached:C,avoidCollisions:T}:{};return(0,S.jsx)(q,{scope:n,content:E,viewport:N,onViewportChange:D,itemRefCallback:ee,selectedItem:H,onItemLeave:et,itemTextRefCallback:en,focusSelectedItem:$,selectedItemText:B,position:o,isPositioned:W,searchRef:X,children:(0,S.jsx)(A.A,{as:w.DX,allowPinchZoom:!0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:j.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,l.m)(i,e=>{j.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(f.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>j.onOpenChange(!1),children:(0,S.jsx)(er,{role:"listbox",id:j.contentId,"data-state":j.open?"open":"closed",dir:j.dir,onContextMenu:e=>e.preventDefault(),...P,...eo,onPlaced:()=>z(!0),ref:M,style:{display:"flex",flexDirection:"column",outline:"none",...P.style},onKeyDown:(0,l.m)(P.onKeyDown,e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});X.displayName="SelectContentImpl";var Z=r.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:o,...l}=e,a=O(K,n),u=Y(K,n),[c,f]=r.useState(null),[d,p]=r.useState(null),h=(0,s.s)(t,e=>p(e)),m=k(n),g=r.useRef(!1),w=r.useRef(!0),{viewport:y,selectedItem:x,selectedItemText:C,focusSelectedItem:R}=u,A=r.useCallback(()=>{if(a.trigger&&a.valueNode&&c&&d&&y&&x&&C){const e=a.trigger.getBoundingClientRect(),t=d.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),r=C.getBoundingClientRect();if("rtl"!==a.dir){const o=r.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,u=Math.max(s,t.width),f=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.left=f+"px"}else{const o=t.right-r.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,u=Math.max(s,t.width),f=i(l,[10,Math.max(10,window.innerWidth-10-u)]);c.style.minWidth=s+"px",c.style.right=f+"px"}const l=m(),s=window.innerHeight-20,u=y.scrollHeight,f=window.getComputedStyle(d),p=Number.parseInt(f.borderTopWidth,10),h=Number.parseInt(f.paddingTop,10),v=Number.parseInt(f.borderBottomWidth,10),w=p+h+u+Number.parseInt(f.paddingBottom,10)+v,b=Math.min(5*x.offsetHeight,w),S=window.getComputedStyle(y),R=Number.parseInt(S.paddingTop,10),A=Number.parseInt(S.paddingBottom,10),T=e.top+e.height/2-10,P=x.offsetHeight/2,j=p+h+(x.offsetTop+P);if(j<=T){const e=l.length>0&&x===l[l.length-1].ref.current;c.style.bottom="0px";const t=Math.max(s-T,P+(e?A:0)+(d.clientHeight-y.offsetTop-y.offsetHeight)+v);c.style.height=j+t+"px"}else{const e=l.length>0&&x===l[0].ref.current;c.style.top="0px";const t=Math.max(T,p+y.offsetTop+(e?R:0)+P);c.style.height=t+(w-j)+"px",y.scrollTop=j-T+y.offsetTop}c.style.margin="10px 0",c.style.minHeight=b+"px",c.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>g.current=!0)}},[m,a.trigger,a.valueNode,c,d,y,x,C,a.dir,o]);(0,b.N)(()=>A(),[A]);const[T,P]=r.useState();(0,b.N)(()=>{d&&P(window.getComputedStyle(d).zIndex)},[d]);const j=r.useCallback(e=>{e&&!0===w.current&&(A(),R?.(),w.current=!1)},[A,R]);return(0,S.jsx)(Q,{scope:n,contentWrapper:c,shouldExpandOnScrollRef:g,onScrollButtonChange:j,children:(0,S.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,S.jsx)(v.sG.div,{...l,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...l.style}})})})});Z.displayName="SelectItemAlignedPosition";var J=r.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=M(n);return(0,S.jsx)(m.UC,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});J.displayName="SelectPopperPosition";var[Q,ee]=N(K,{}),et="SelectViewport",en=r.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:o,...i}=e,a=Y(et,n),u=ee(et,n),c=(0,s.s)(t,a.onViewportChange),f=r.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(E.Slot,{scope:n,children:(0,S.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,l.m)(i.onScroll,e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if(r?.current&&n){const e=Math.abs(f.current-t.scrollTop);if(e>0){const r=window.innerHeight-20,o=Math.max(Number.parseFloat(n.style.minHeight),Number.parseFloat(n.style.height));if(o<r){const i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});en.displayName=et;var er="SelectGroup",[eo,ei]=N(er),el=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=(0,h.B)();return(0,S.jsx)(eo,{scope:n,id:o,children:(0,S.jsx)(v.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});el.displayName=er;var ea="SelectLabel",es=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=ei(ea,n);return(0,S.jsx)(v.sG.div,{id:o.id,...r,ref:t})});es.displayName=ea;var eu="SelectItem",[ec,ef]=N(eu),ed=r.forwardRef((e,t)=>{const{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,c=O(eu,n),f=Y(eu,n),d=c.value===o,[p,m]=r.useState(a??""),[g,w]=r.useState(!1),y=(0,s.s)(t,e=>f.itemRefCallback?.(e,o,i)),x=(0,h.B)(),b=r.useRef("touch"),C=()=>{i||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ec,{scope:n,value:o,disabled:i,textId:x,isSelected:d,onItemTextChange:r.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,S.jsx)(E.ItemSlot,{scope:n,value:o,disabled:i,textValue:p,children:(0,S.jsx)(v.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":d&&g,"data-state":d?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:y,onFocus:(0,l.m)(u.onFocus,()=>w(!0)),onBlur:(0,l.m)(u.onBlur,()=>w(!1)),onClick:(0,l.m)(u.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,l.m)(u.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,l.m)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,l.m)(u.onPointerMove,e=>{b.current=e.pointerType,i?f.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,l.m)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,l.m)(u.onKeyDown,e=>{(f.searchRef?.current===""||" "!==e.key)&&(P.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ed.displayName=eu;var ep="SelectItemText",eh=r.forwardRef((e,t)=>{const{__scopeSelect:n,className:i,style:l,...a}=e,u=O(ep,n),c=Y(ep,n),f=ef(ep,n),d=B(ep,n),[p,h]=r.useState(null),m=(0,s.s)(t,e=>h(e),f.onItemTextChange,e=>c.itemTextRefCallback?.(e,f.value,f.disabled)),g=p?.textContent,w=r.useMemo(()=>(0,S.jsx)("option",{value:f.value,disabled:f.disabled,children:g},f.value),[f.disabled,f.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=d;return(0,b.N)(()=>(y(w),()=>x(w)),[y,x,w]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(v.sG.span,{id:f.textId,...a,ref:m}),f.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(a.children,u.valueNode):null]})});eh.displayName=ep;var em="SelectItemIndicator",eg=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return ef(em,n).isSelected?(0,S.jsx)(v.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=em;var ev="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{const n=Y(ev,e.__scopeSelect),o=ee(ev,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){const e=()=> {l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=ev;var ey="SelectScrollDownButton",ex=r.forwardRef((e,t)=>{const n=Y(ey,e.__scopeSelect),o=ee(ey,e.__scopeSelect),[i,l]=r.useState(!1),a=(0,s.s)(t,o.onScrollButtonChange);return(0,b.N)(()=>{if(n.viewport&&n.isPositioned){const e=()=> {const e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,S.jsx)(eb,{...e,ref:a,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ex.displayName=ey;var eb=r.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:o,...i}=e,a=Y("SelectScrollButton",n),s=r.useRef(null),u=k(n),c=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>c(),[c]),(0,b.N)(()=>{const e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,l.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,l.m)(i.onPointerMove,()=>{a.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,l.m)(i.onPointerLeave,()=>{c()})})}),eS=r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return(0,S.jsx)(v.sG.div,{"aria-hidden":!0,...r,ref:t})});eS.displayName="SelectSeparator";var eC="SelectArrow";function eR(e){return""===e||void 0===e}r.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=M(n),i=O(eC,n),l=Y(eC,n);return i.open&&"popper"===l.position?(0,S.jsx)(m.i3,{...o,...r,ref:t}):null}).displayName=eC;var eA=r.forwardRef((e,t)=>{const{value:n,...o}=e,i=r.useRef(null),l=(0,s.s)(t,i),a=((e)=> {const t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])})(n);return r.useEffect(()=>{const e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){const r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,S.jsx)(C,{asChild:!0,children:(0,S.jsx)("select",{...o,ref:l,defaultValue:n})})});function eT(e){const t=(0,y.c)(e),n=r.useRef(""),o=r.useRef(0),i=r.useCallback(e=>{const r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),l=r.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,l]}function eP(e,t,n){var r,o;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,l=n?e.indexOf(n):-1,a=(r=e,o=Math.max(l,0),r.map((e,t)=>r[(o+t)%r.length]));1===i.length&&(a=a.filter(e=>e!==n));const s=a.find(e=>e.textValue.toLowerCase().startsWith(i.toLowerCase()));return s!==n?s:void 0}eA.displayName="BubbleSelect";var ej=F,eE=W,ek=_,eL=G,eN=$,eD=U,eM=en,eH=el,eO=es,eI=ed,eB=eh,eF=eg,eV=ew,eW=ex,ez=eS},28108:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(22752).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},48613:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(22752).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},71776:(e,t,n)=>{n.d(t,{A:()=>r});const r=(0,n(22752).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])}};