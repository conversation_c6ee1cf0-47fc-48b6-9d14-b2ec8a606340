(exports.id = 202),
	(exports.ids = [202]),
	(exports.modules = {
		2623: (e, o, r) => {
			Promise.resolve().then(r.bind(r, 59650));
		},
		4761: () => {},
		6801: (e, o, r) => {
			r.r(o), r.d(o, { default: () => m });
			var s = r(45781);
			r(65042);
			var n = r(28273),
				t = r(27130),
				i = r(84533),
				l = r(14298),
				d = r(18469),
				a = r(86487);
			const c = {
				apiKey: "AIzaSyAR2vW70A7yX95L1RSUi2hRSuh9HU0FXqM",
				authDomain: "dream-unit-agency.firebaseapp.com",
				projectId: "dream-unit-agency",
				storageBucket: "dream-unit-agency.firebasestorage.app",
				messagingSenderId: "364954502659",
				appId: "1:364954502659:web:c0ea1247be97d8fdf13036",
			};
			process.env.NEXT_PUBLIC_USE_EMULATORS;
			const p = ({ children: e }) => {
					const o = (0, t.sh)(),
						r = (0, i.xI)(o),
						n = (0, l.aU)(o),
						c = (0, d.Uz)(o),
						p = (0, a.c7)(o);
					return (0, s.jsx)(t.OJ, {
						sdk: r,
						children: (0, s.jsx)(t.z_, {
							sdk: n,
							children: (0, s.jsx)(t.VB, {
								sdk: c,
								children: (0, s.jsx)(t.Lg, { sdk: p, children: e }),
							}),
						}),
					});
				},
				u = ({ children: e }) =>
					c &&
					c.apiKey &&
					"YOUR_API_KEY" !== c.apiKey &&
					c.projectId &&
					"YOUR_PROJECT_ID" !== c.projectId
						? (0, s.jsx)(t.qB, {
								firebaseConfig: c,
								children: (0, s.jsx)(p, { children: e }),
							})
						: (console.warn(
								"Firebase configuration is incomplete or using placeholder values. Ensure your .env.local file is set up with your Firebase project's credentials. Firebase services may not initialize correctly. The app might rely on mock data mechanisms.",
							),
							(0, s.jsx)(s.Fragment, { children: e }));
			function m({ children: e }) {
				return (0, s.jsx)("html", {
					lang: "en",
					children: (0, s.jsx)("body", {
						children: (0, s.jsx)(u, {
							children: (0, s.jsx)(n.NP, {
								defaultTheme: n.D7.LIGHT,
								children: e,
							}),
						}),
					}),
				});
			}
		},
		15546: (e, o, r) => {
			Promise.resolve().then(r.t.bind(r, 85770, 23)),
				Promise.resolve().then(r.t.bind(r, 88204, 23)),
				Promise.resolve().then(r.t.bind(r, 82576, 23)),
				Promise.resolve().then(r.t.bind(r, 59507, 23)),
				Promise.resolve().then(r.t.bind(r, 61283, 23)),
				Promise.resolve().then(r.t.bind(r, 75147, 23)),
				Promise.resolve().then(r.t.bind(r, 83163, 23)),
				Promise.resolve().then(r.t.bind(r, 99773, 23));
		},
		28273: (e, o, r) => {
			r.d(o, { $n: () => c, D7: () => F, NP: () => A });
			var s = r(45781),
				n = r(13072),
				t = r(38422),
				i = r(74645);
			const l = {
					default: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.primary};
    color: ${({ theme: e }) => e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.primary}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
					destructive: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.destructive};
    color: ${({ theme: e }) => e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.destructive}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
					outline: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.background};
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid ${({ theme: e }) => e.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.accent};
      color: ${({ theme: e }) => e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
					secondary: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.secondary};
    color: ${({ theme: e }) => e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.secondary}cc;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
					ghost: (0, t.AH)`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.accent};
      color: ${({ theme: e }) => e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
					link: (0, t.AH)`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.primary};
    border: 1px solid transparent;
    text-decoration: underline;
    text-underline-offset: 4px;
    
    &:hover:not(:disabled) {
      text-decoration: none;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
				},
				d = {
					default: (0, t.AH)`
    height: 2.5rem;
    padding: 0.5rem 1rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  `,
					sm: (0, t.AH)`
    height: 2.25rem;
    padding: 0.5rem 0.75rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
    border-radius: calc(${({ theme: e }) => e.sizes.borderRadius} - 2px);
  `,
					lg: (0, t.AH)`
    height: 2.75rem;
    padding: 0.5rem 2rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.md};
    border-radius: calc(${({ theme: e }) => e.sizes.borderRadius} - 2px);
  `,
					icon: (0, t.AH)`
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
  `,
				},
				a = t.Ay.button`
  /* Base styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  font-weight: ${({ theme: e }) => e.fontWeights.medium};
  transition: ${({ theme: e }) => e.transitions.default};
  cursor: pointer;
  user-select: none;
  
  /* SVG styles */
  & svg {
    pointer-events: none;
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
  
  /* Disabled state */
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Variant styles */
  ${({ variant: e = "default" }) => l[e]}
  
  /* Size styles */
  ${({ size: e = "default" }) => d[e]}
`,
				c = (0, n.forwardRef)(
					({ className: e, variant: o, size: r, asChild: n = !1, ...t }, l) => {
						const d = n ? i.DX : a;
						return (0, s.jsx)(d, {
							className: e,
							variant: o,
							size: r,
							ref: l,
							...t,
						});
					},
				);
			c.displayName = "Button";
			const p = t.Ay.input`
  display: flex;
  height: ${({ theme: e }) => e.sizes.formControl};
  width: 100%;
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  border: 1px solid ${({ theme: e }) => e.colors.input};
  background-color: ${({ theme: e }) => e.colors.background};
  padding: 0.5rem 0.75rem;
  font-size: ${({ theme: e }) => e.sizes.fonts.md};
  color: ${({ theme: e }) => e.colors.foreground};
  transition: ${({ theme: e }) => e.transitions.default};
  
  /* File input styles */
  &[type="file"] {
    border: 0;
    background-color: transparent;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
    font-weight: ${({ theme: e }) => e.fontWeights.medium};
    
    &::file-selector-button {
      border: 0;
      background-color: transparent;
      font-size: ${({ theme: e }) => e.sizes.fonts.sm};
      font-weight: ${({ theme: e }) => e.fontWeights.medium};
      color: ${({ theme: e }) => e.colors.foreground};
      margin-right: 0.5rem;
    }
  }
  
  /* Placeholder styles */
  &::placeholder {
    color: ${({ theme: e }) => e.colors.mutedForeground};
  }
  
  /* Focus styles */
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    border-color: ${({ theme: e }) => e.colors.ring};
  }
  
  /* Disabled styles */
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  /* Responsive font size */
  @media (max-width: ${({ theme: e }) => e.breakpoints.md}) {
    font-size: ${({ theme: e }) => e.sizes.fonts.md};
  }
  
  @media (min-width: ${({ theme: e }) => e.breakpoints.md}) {
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  }
`;
			(0, n.forwardRef)(({ type: e = "text", ...o }, r) =>
				(0, s.jsx)(p, { ref: r, type: e, ...o }),
			).displayName = "Input";
			const u = {
					default: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.primary};
    color: ${({ theme: e }) => e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.primary}cc;
    }
  `,
					secondary: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.secondary};
    color: ${({ theme: e }) => e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.secondary}cc;
    }
  `,
					destructive: (0, t.AH)`
    background-color: ${({ theme: e }) => e.colors.destructive};
    color: ${({ theme: e }) => e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.destructive}cc;
    }
  `,
					outline: (0, t.AH)`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid ${({ theme: e }) => e.colors.border};
  `,
				},
				m = t.Ay.div`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: ${({ theme: e }) => e.sizes.fonts.xs};
  font-weight: ${({ theme: e }) => e.fontWeights.semibold};
  transition: ${({ theme: e }) => e.transitions.default};
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
  }
  
  /* Variant styles */
  ${({ variant: e = "default" }) => u[e]}
`;
			(0, n.forwardRef)(({ variant: e = "default", ...o }, r) =>
				(0, s.jsx)(m, { ref: r, variant: e, ...o }),
			).displayName = "Badge";
			const g = {
					sm: (0, t.AH)`
    padding: ${({ theme: e }) => e.sizes.spacing.lg};
  `,
					md: (0, t.AH)`
    padding: ${({ theme: e }) => e.sizes.spacing.xl};
  `,
					lg: (0, t.AH)`
    padding: ${({ theme: e }) => e.sizes.spacing.xxl};
  `,
				},
				h = t.Ay.div`
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  border: 1px solid ${({ theme: e }) => e.colors.border};
  background-color: ${({ theme: e }) => e.colors.card};
  color: ${({ theme: e }) => e.colors.cardForeground};
  box-shadow: ${({ theme: e }) => e.shadows.sm};
  transition: ${({ theme: e }) => e.transitions.default};
  
  ${({ size: e = "md" }) => g[e]}
`;
			(0, n.forwardRef)(({ size: e = "md", ...o }, r) =>
				(0, s.jsx)(h, { ref: r, size: e, ...o }),
			).displayName = "Card";
			const b = t.Ay.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme: e }) => e.sizes.spacing.sm};
  margin-bottom: ${({ theme: e }) => e.sizes.spacing.xl};
`;
			(0, n.forwardRef)((e, o) => (0, s.jsx)(b, { ref: o, ...e })).displayName =
				"CardHeader";
			const x = t.Ay.h3`
  font-size: ${({ theme: e }) => e.sizes.fonts.xl};
  font-weight: ${({ theme: e }) => e.fontWeights.semibold};
  line-height: ${({ theme: e }) => e.lineHeights.tight};
  letter-spacing: -0.025em;
  margin: 0;
`;
			(0, n.forwardRef)((e, o) => (0, s.jsx)(x, { ref: o, ...e })).displayName =
				"CardTitle";
			const f = t.Ay.p`
  font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  color: ${({ theme: e }) => e.colors.mutedForeground};
  line-height: ${({ theme: e }) => e.lineHeights.normal};
  margin: 0;
`;
			(0, n.forwardRef)((e, o) => (0, s.jsx)(f, { ref: o, ...e })).displayName =
				"CardDescription";
			const $ = t.Ay.div`
  /* Content has no default padding as it's handled by the card itself */
`;
			(0, n.forwardRef)((e, o) => (0, s.jsx)($, { ref: o, ...e })).displayName =
				"CardContent";
			const y = t.Ay.div`
  display: flex;
  align-items: center;
  gap: ${({ theme: e }) => e.sizes.spacing.md};
  margin-top: ${({ theme: e }) => e.sizes.spacing.xl};
`;
			(0, n.forwardRef)((e, o) => (0, s.jsx)(y, { ref: o, ...e })).displayName =
				"CardFooter";
			const v = {
					sizes: {
						borderRadius: "0.5rem",
						formControl: "2.5rem",
						fonts: {
							xxs: "0.625rem",
							xs: "0.75rem",
							sm: "0.875rem",
							md: "1rem",
							lg: "1.125rem",
							xl: "1.25rem",
							xxl: "1.5rem",
							xxxl: "2rem",
						},
						spacing: {
							xxs: "0.125rem",
							xs: "0.25rem",
							sm: "0.5rem",
							md: "0.75rem",
							lg: "1rem",
							xl: "1.5rem",
							xxl: "2rem",
							xxxl: "3rem",
						},
					},
					fonts: {
						body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
						heading:
							"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
						mono: "'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace",
					},
					fontWeights: { normal: 400, medium: 500, semibold: 600, bold: 700 },
					lineHeights: { tight: 1.25, normal: 1.5, relaxed: 1.75 },
					zIndicies: {
						loadingOverlay: 9e3,
						dropdownMenu: 8e3,
						dialog: 7e3,
						popover: 6e3,
						tooltip: 5e3,
						sticky: 1e3,
					},
					shadows: {
						sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
						default:
							"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
						md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
						lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
						xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
						inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
					},
					transitions: {
						default: "all 0.2s ease-in-out",
						fast: "all 0.1s ease-in-out",
						slow: "all 0.3s ease-in-out",
					},
					breakpoints: {
						xs: "400px",
						sm: "600px",
						md: "900px",
						lg: "1280px",
						xl: "1440px",
						xxl: "1920px",
					},
					colors: {
						error: "#E74C3C",
						success: "#27AE60",
						warning: "#F1C40F",
						info: "#3498DB",
						white: "#FFFFFF",
						black: "#000000",
						transparent: "transparent",
					},
				},
				z = {
					...v,
					colors: {
						...v.colors,
						background: "hsl(0 0% 100%)",
						foreground: "hsl(0 0% 3.9%)",
						contentBg: "hsl(0 0% 100%)",
						card: "hsl(0 0% 100%)",
						cardForeground: "hsl(0 0% 3.9%)",
						popover: "hsl(0 0% 100%)",
						popoverForeground: "hsl(0 0% 3.9%)",
						primary: "hsl(0 0% 9%)",
						primaryForeground: "hsl(0 0% 98%)",
						secondary: "hsl(0 0% 96.1%)",
						secondaryForeground: "hsl(0 0% 9%)",
						muted: "hsl(0 0% 96.1%)",
						mutedForeground: "hsl(0 0% 45.1%)",
						accent: "hsl(0 0% 96.1%)",
						accentForeground: "hsl(0 0% 9%)",
						destructive: "hsl(0 84.2% 60.2%)",
						destructiveForeground: "hsl(0 0% 98%)",
						border: "hsl(0 0% 89.8%)",
						input: "hsl(0 0% 89.8%)",
						ring: "hsl(0 0% 3.9%)",
					},
				},
				k = {
					...v,
					colors: {
						...v.colors,
						background: "hsl(0 0% 3.9%)",
						foreground: "hsl(0 0% 98%)",
						contentBg: "hsl(0 0% 3.9%)",
						card: "hsl(0 0% 3.9%)",
						cardForeground: "hsl(0 0% 98%)",
						popover: "hsl(0 0% 3.9%)",
						popoverForeground: "hsl(0 0% 98%)",
						primary: "hsl(0 0% 98%)",
						primaryForeground: "hsl(0 0% 9%)",
						secondary: "hsl(0 0% 14.9%)",
						secondaryForeground: "hsl(0 0% 98%)",
						muted: "hsl(0 0% 14.9%)",
						mutedForeground: "hsl(0 0% 63.9%)",
						accent: "hsl(0 0% 14.9%)",
						accentForeground: "hsl(0 0% 98%)",
						destructive: "hsl(0 62.8% 30.6%)",
						destructiveForeground: "hsl(0 0% 98%)",
						border: "hsl(0 0% 14.9%)",
						input: "hsl(0 0% 14.9%)",
						ring: "hsl(0 0% 83.1%)",
					},
				};
			v.breakpoints;
			const F = { LIGHT: "light", DARK: "dark" },
				w = (0, n.createContext)(void 0);
			function A({
				children: e,
				defaultTheme: o = F.LIGHT,
				storageKey: r = "dua-ui-theme",
				enableSystem: i = !0,
			}) {
				const [l, d] = (0, n.useState)(o),
					a = (e) => {
						d(e), localStorage.setItem(r, e);
					},
					c = l === F.DARK ? k : z;
				return (0, s.jsx)(w.Provider, {
					value: {
						theme: l,
						setTheme: a,
						toggleTheme: () => {
							a(l === F.LIGHT ? F.DARK : F.LIGHT);
						},
					},
					children: (0, s.jsx)(t.NP, { theme: c, children: e }),
				});
			}
		},
		33474: (e, o, r) => {
			Promise.resolve().then(r.t.bind(r, 60140, 23)),
				Promise.resolve().then(r.t.bind(r, 18946, 23)),
				Promise.resolve().then(r.t.bind(r, 74178, 23)),
				Promise.resolve().then(r.t.bind(r, 6229, 23)),
				Promise.resolve().then(r.t.bind(r, 31281, 23)),
				Promise.resolve().then(r.t.bind(r, 93833, 23)),
				Promise.resolve().then(r.t.bind(r, 97857, 23)),
				Promise.resolve().then(r.t.bind(r, 4947, 23));
		},
		34314: (e, o, r) => {
			function s() {
				return null;
			}
			r.r(o), r.d(o, { default: () => s });
		},
		39575: (e, o, r) => {
			Promise.resolve().then(r.bind(r, 6801));
		},
		59650: (e, o, r) => {
			r.r(o), r.d(o, { default: () => s });
			const s = (0, r(51129).registerClientReference)(
				() => {
					throw Error(
						"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
					);
				},
				"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
				"default",
			);
		},
		65042: () => {},
		68313: () => {},
	});
