(exports.id = 482),
	(exports.ids = [482]),
	(exports.modules = {
		113: (e, t, n) => {
			n.d(t, { N: () => c });
			var r = n(13072),
				o = n(61779),
				a = n(47851),
				i = n(74645),
				u = n(45781);
			function c(e) {
				const t = e + "CollectionProvider",
					[n, c] = (0, o.A)(t),
					[l, s] = n(t, {
						collectionRef: { current: null },
						itemMap: new Map(),
					}),
					d = (e) => {
						const { scope: t, children: n } = e,
							o = r.useRef(null),
							a = r.useRef(new Map()).current;
						return (0, u.jsx)(l, {
							scope: t,
							itemMap: a,
							collectionRef: o,
							children: n,
						});
					};
				d.displayName = t;
				const f = e + "CollectionSlot",
					v = r.forwardRef((e, t) => {
						const { scope: n, children: r } = e,
							o = s(f, n),
							c = (0, a.s)(t, o.collectionRef);
						return (0, u.jsx)(i.DX, { ref: c, children: r });
					});
				v.displayName = f;
				const m = e + "CollectionItemSlot",
					p = "data-radix-collection-item",
					h = r.forwardRef((e, t) => {
						const { scope: n, children: o, ...c } = e,
							l = r.useRef(null),
							d = (0, a.s)(t, l),
							f = s(m, n);
						return (
							r.useEffect(
								() => (
									f.itemMap.set(l, { ref: l, ...c }),
									() => void f.itemMap.delete(l)
								),
							),
							(0, u.jsx)(i.DX, { [p]: "", ref: d, children: o })
						);
					});
				return (
					(h.displayName = m),
					[
						{ Provider: d, Slot: v, ItemSlot: h },
						(t) => {
							const n = s(e + "CollectionConsumer", t);
							return r.useCallback(() => {
								const e = n.collectionRef.current;
								if (!e) return [];
								const t = Array.from(e.querySelectorAll(`[${p}]`));
								return Array.from(n.itemMap.values()).sort(
									(e, n) => t.indexOf(e.ref.current) - t.indexOf(n.ref.current),
								);
							}, [n.collectionRef, n.itemMap]);
						},
						c,
					]
				);
			}
		},
		1377: (e, t, n) => {
			n.d(t, { Z: () => c });
			var r = n(13072),
				o = n(76129),
				a = n(60110),
				i = n(63904),
				u = n(45781),
				c = r.forwardRef((e, t) => {
					const { container: n, ...c } = e,
						[l, s] = r.useState(!1);
					(0, i.N)(() => s(!0), []);
					const d = n || (l && globalThis?.document?.body);
					return d
						? o.createPortal((0, u.jsx)(a.sG.div, { ...c, ref: t }), d)
						: null;
				});
			c.displayName = "Portal";
		},
		7678: (e, t, n) => {
			n.d(t, { n: () => d });
			var r = n(13072),
				o = n(47851),
				a = n(60110),
				i = n(99210),
				u = n(45781),
				c = "focusScope.autoFocusOnMount",
				l = "focusScope.autoFocusOnUnmount",
				s = { bubbles: !1, cancelable: !0 },
				d = r.forwardRef((e, t) => {
					const {
							loop: n = !1,
							trapped: d = !1,
							onMountAutoFocus: h,
							onUnmountAutoFocus: y,
							...E
						} = e,
						[g, b] = r.useState(null),
						w = (0, i.c)(h),
						C = (0, i.c)(y),
						S = r.useRef(null),
						x = (0, o.s)(t, (e) => b(e)),
						N = r.useRef({
							paused: !1,
							pause() {
								this.paused = !0;
							},
							resume() {
								this.paused = !1;
							},
						}).current;
					r.useEffect(() => {
						if (d) {
							const e = (e) => {
									if (N.paused || !g) return;
									const t = e.target;
									g.contains(t)
										? (S.current = t)
										: m(S.current, { select: !0 });
								},
								t = (e) => {
									if (N.paused || !g) return;
									const t = e.relatedTarget;
									null === t || g.contains(t) || m(S.current, { select: !0 });
								};
							document.addEventListener("focusin", e),
								document.addEventListener("focusout", t);
							const n = new MutationObserver((e) => {
								if (document.activeElement === document.body)
									for (const t of e) t.removedNodes.length > 0 && m(g);
							});
							return (
								g && n.observe(g, { childList: !0, subtree: !0 }),
								() => {
									document.removeEventListener("focusin", e),
										document.removeEventListener("focusout", t),
										n.disconnect();
								}
							);
						}
					}, [d, g, N.paused]),
						r.useEffect(() => {
							if (g) {
								p.add(N);
								const e = document.activeElement;
								if (!g.contains(e)) {
									const t = new CustomEvent(c, s);
									g.addEventListener(c, w),
										g.dispatchEvent(t),
										t.defaultPrevented ||
											(((e, { select: t = !1 } = {}) => {
												const n = document.activeElement;
												for (const r of e)
													if (
														(m(r, { select: t }), document.activeElement !== n)
													)
														return;
											})(
												f(g).filter((e) => "A" !== e.tagName),
												{ select: !0 },
											),
											document.activeElement === e && m(g));
								}
								return () => {
									g.removeEventListener(c, w),
										setTimeout(() => {
											const t = new CustomEvent(l, s);
											g.addEventListener(l, C),
												g.dispatchEvent(t),
												t.defaultPrevented ||
													m(e ?? document.body, { select: !0 }),
												g.removeEventListener(l, C),
												p.remove(N);
										}, 0);
								};
							}
						}, [g, w, C, N]);
					const R = r.useCallback(
						(e) => {
							if ((!n && !d) || N.paused) return;
							const t =
									"Tab" === e.key && !e.altKey && !e.ctrlKey && !e.metaKey,
								r = document.activeElement;
							if (t && r) {
								const t = e.currentTarget,
									[o, a] = ((e) => {
										const t = f(e);
										return [v(t, e), v(t.reverse(), e)];
									})(t);
								o && a
									? e.shiftKey || r !== a
										? e.shiftKey &&
											r === o &&
											(e.preventDefault(), n && m(a, { select: !0 }))
										: (e.preventDefault(), n && m(o, { select: !0 }))
									: r === t && e.preventDefault();
							}
						},
						[n, d, N.paused],
					);
					return (0, u.jsx)(a.sG.div, {
						tabIndex: -1,
						...E,
						ref: x,
						onKeyDown: R,
					});
				});
			function f(e) {
				const t = [],
					n = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, {
						acceptNode: (e) => {
							const t = "INPUT" === e.tagName && "hidden" === e.type;
							return e.disabled || e.hidden || t
								? NodeFilter.FILTER_SKIP
								: e.tabIndex >= 0
									? NodeFilter.FILTER_ACCEPT
									: NodeFilter.FILTER_SKIP;
						},
					});
				while (n.nextNode()) t.push(n.currentNode);
				return t;
			}
			function v(e, t) {
				for (const n of e)
					if (
						!((e, { upTo: t }) => {
							if ("hidden" === getComputedStyle(e).visibility) return !0;
							while (e && (void 0 === t || e !== t)) {
								if ("none" === getComputedStyle(e).display) return !0;
								e = e.parentElement;
							}
							return !1;
						})(n, { upTo: t })
					)
						return n;
			}
			function m(e, { select: t = !1 } = {}) {
				if (e && e.focus) {
					var n;
					const r = document.activeElement;
					e.focus({ preventScroll: !0 }),
						e !== r &&
							(n = e) instanceof HTMLInputElement &&
							"select" in n &&
							t &&
							e.select();
				}
			}
			d.displayName = "FocusScope";
			var p = (() => {
				let e = [];
				return {
					add(t) {
						const n = e[0];
						t !== n && n?.pause(), (e = h(e, t)).unshift(t);
					},
					remove(t) {
						(e = h(e, t)), e[0]?.resume();
					},
				};
			})();
			function h(e, t) {
				const n = [...e],
					r = n.indexOf(t);
				return -1 !== r && n.splice(r, 1), n;
			}
		},
		25097: (e, t, n) => {
			n.d(t, { Eq: () => s });
			var r = (e) =>
					"undefined" == typeof document
						? null
						: (Array.isArray(e) ? e[0] : e).ownerDocument.body,
				o = new WeakMap(),
				a = new WeakMap(),
				i = {},
				u = 0,
				c = (e) => e && (e.host || c(e.parentNode)),
				l = (e, t, n, r) => {
					var l = (Array.isArray(e) ? e : [e])
						.map((e) => {
							if (t.contains(e)) return e;
							var n = c(e);
							return n && t.contains(n)
								? n
								: (console.error(
										"aria-hidden",
										e,
										"in not contained inside",
										t,
										". Doing nothing",
									),
									null);
						})
						.filter((e) => !!e);
					i[n] || (i[n] = new WeakMap());
					var s = i[n],
						d = [],
						f = new Set(),
						v = new Set(l),
						m = (e) => {
							!(!e || f.has(e)) && (f.add(e), m(e.parentNode));
						};
					l.forEach(m);
					var p = (e) => {
						!(!e || v.has(e)) &&
							Array.prototype.forEach.call(e.children, (e) => {
								if (f.has(e)) p(e);
								else
									try {
										var t = e.getAttribute(r),
											i = null !== t && "false" !== t,
											u = (o.get(e) || 0) + 1,
											c = (s.get(e) || 0) + 1;
										o.set(e, u),
											s.set(e, c),
											d.push(e),
											1 === u && i && a.set(e, !0),
											1 === c && e.setAttribute(n, "true"),
											i || e.setAttribute(r, "true");
									} catch (t) {
										console.error("aria-hidden: cannot operate on ", e, t);
									}
							});
					};
					return (
						p(t),
						f.clear(),
						u++,
						() => {
							d.forEach((e) => {
								var t = o.get(e) - 1,
									i = s.get(e) - 1;
								o.set(e, t),
									s.set(e, i),
									t || (a.has(e) || e.removeAttribute(r), a.delete(e)),
									i || e.removeAttribute(n);
							}),
								--u ||
									((o = new WeakMap()),
									(o = new WeakMap()),
									(a = new WeakMap()),
									(i = {}));
						}
					);
				},
				s = (e, t, n) => {
					void 0 === n && (n = "data-aria-hidden");
					var o = Array.from(Array.isArray(e) ? e : [e]),
						a = t || r(e);
					return a
						? (o.push.apply(
								o,
								Array.from(a.querySelectorAll("[aria-live], script")),
							),
							l(o, a, n, "aria-hidden"))
						: () => null;
				};
		},
		35414: (e, t, n) => {
			n.d(t, { B: () => c });
			var r,
				o = n(13072),
				a = n(63904),
				i = (r || (r = n.t(o, 2)))["useId".toString()] || (() => void 0),
				u = 0;
			function c(e) {
				const [t, n] = o.useState(i());
				return (
					(0, a.N)(() => {
						e || n((e) => e ?? String(u++));
					}, [e]),
					e || (t ? `radix-${t}` : "")
				);
			}
		},
		39092: (e, t, n) => {
			n.d(t, { jH: () => a });
			var r = n(13072);
			n(45781);
			var o = r.createContext(void 0);
			function a(e) {
				const t = r.useContext(o);
				return e || t || "ltr";
			}
		},
		42254: (e, t, n) => {
			n.d(t, { A: () => q });
			var r,
				o = n(74338),
				a = n(13072),
				i = "right-scroll-bar-position",
				u = "width-before-scroll-bar";
			function c(e, t) {
				return "function" == typeof e ? e(t) : e && (e.current = t), e;
			}
			var l = "undefined" != typeof window ? a.useLayoutEffect : a.useEffect,
				s = new WeakMap();
			function d(e) {
				return e;
			}
			var f = ((e) => {
					void 0 === e && (e = {});
					var t,
						n,
						r,
						a,
						i =
							((t = null),
							void 0 === n && (n = d),
							(r = []),
							(a = !1),
							{
								read: () => {
									if (a)
										throw Error(
											"Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.",
										);
									return r.length ? r[r.length - 1] : null;
								},
								useMedium: (e) => {
									var t = n(e, a);
									return (
										r.push(t),
										() => {
											r = r.filter((e) => e !== t);
										}
									);
								},
								assignSyncMedium: (e) => {
									for (a = !0; r.length; ) {
										var t = r;
										(r = []), t.forEach(e);
									}
									r = { push: (t) => e(t), filter: () => r };
								},
								assignMedium: (e) => {
									a = !0;
									var t = [];
									if (r.length) {
										var n = r;
										(r = []), n.forEach(e), (t = r);
									}
									var o = () => {
											var n = t;
											(t = []), n.forEach(e);
										},
										i = () => Promise.resolve().then(o);
									i(),
										(r = {
											push: (e) => {
												t.push(e), i();
											},
											filter: (e) => ((t = t.filter(e)), r),
										});
								},
							});
					return (i.options = (0, o.Cl)({ async: !0, ssr: !1 }, e)), i;
				})(),
				v = () => {},
				m = a.forwardRef((e, t) => {
					var n,
						r,
						i,
						u,
						d = a.useRef(null),
						m = a.useState({
							onScrollCapture: v,
							onWheelCapture: v,
							onTouchMoveCapture: v,
						}),
						p = m[0],
						h = m[1],
						y = e.forwardProps,
						E = e.children,
						g = e.className,
						b = e.removeScrollBar,
						w = e.enabled,
						C = e.shards,
						S = e.sideCar,
						x = e.noRelative,
						N = e.noIsolation,
						R = e.inert,
						L = e.allowPinchZoom,
						M = e.as,
						T = e.gapMode,
						P = (0, o.Tt)(e, [
							"forwardProps",
							"children",
							"className",
							"removeScrollBar",
							"enabled",
							"shards",
							"sideCar",
							"noRelative",
							"noIsolation",
							"inert",
							"allowPinchZoom",
							"as",
							"gapMode",
						]),
						k =
							((n = [d, t]),
							(r = (e) => n.forEach((t) => c(t, e))),
							((i = (0, a.useState)(() => ({
								value: null,
								callback: r,
								facade: {
									get current() {
										return i.value;
									},
									set current(value) {
										var e = i.value;
										e !== value && ((i.value = value), i.callback(value, e));
									},
								},
							}))[0]).callback = r),
							(u = i.facade),
							l(() => {
								var e = s.get(u);
								if (e) {
									var t = new Set(e),
										r = new Set(n),
										o = u.current;
									t.forEach((e) => {
										r.has(e) || c(e, null);
									}),
										r.forEach((e) => {
											t.has(e) || c(e, o);
										});
								}
								s.set(u, n);
							}, [n]),
							u),
						A = (0, o.Cl)((0, o.Cl)({}, P), p);
					return a.createElement(
						a.Fragment,
						null,
						w &&
							a.createElement(S, {
								sideCar: f,
								removeScrollBar: b,
								shards: C,
								noRelative: x,
								noIsolation: N,
								inert: R,
								setCallbacks: h,
								allowPinchZoom: !!L,
								lockRef: d,
								gapMode: T,
							}),
						y
							? a.cloneElement(
									a.Children.only(E),
									(0, o.Cl)((0, o.Cl)({}, A), { ref: k }),
								)
							: a.createElement(
									void 0 === M ? "div" : M,
									(0, o.Cl)({}, A, { className: g, ref: k }),
									E,
								),
					);
				});
			(m.defaultProps = { enabled: !0, removeScrollBar: !0, inert: !1 }),
				(m.classNames = { fullWidth: u, zeroRight: i });
			var p = (e) => {
				var t = e.sideCar,
					n = (0, o.Tt)(e, ["sideCar"]);
				if (!t)
					throw Error(
						"Sidecar: please provide `sideCar` property to import the right car",
					);
				var r = t.read();
				if (!r) throw Error("Sidecar medium not found");
				return a.createElement(r, (0, o.Cl)({}, n));
			};
			p.isSideCarExport = !0;
			var h = () => {
					var e = 0,
						t = null;
					return {
						add: (o) => {
							if (
								0 == e &&
								(t = (() => {
									if (!document) return null;
									var e = document.createElement("style");
									e.type = "text/css";
									var t = r || n.nc;
									return t && e.setAttribute("nonce", t), e;
								})())
							) {
								var a, i;
								(a = t).styleSheet
									? (a.styleSheet.cssText = o)
									: a.appendChild(document.createTextNode(o)),
									(i = t),
									(
										document.head || document.getElementsByTagName("head")[0]
									).appendChild(i);
							}
							e++;
						},
						remove: () => {
							--e ||
								!t ||
								(t.parentNode && t.parentNode.removeChild(t), (t = null));
						},
					};
				},
				y = () => {
					var e = h();
					return (t, n) => {
						a.useEffect(
							() => (
								e.add(t),
								() => {
									e.remove();
								}
							),
							[t && n],
						);
					};
				},
				E = () => {
					var e = y();
					return (t) => (e(t.styles, t.dynamic), null);
				},
				g = { left: 0, top: 0, right: 0, gap: 0 },
				b = (e) => Number.parseInt(e || "", 10) || 0,
				w = (e) => {
					var t = window.getComputedStyle(document.body),
						n = t["padding" === e ? "paddingLeft" : "marginLeft"],
						r = t["padding" === e ? "paddingTop" : "marginTop"],
						o = t["padding" === e ? "paddingRight" : "marginRight"];
					return [b(n), b(r), b(o)];
				},
				C = (e) => {
					if ((void 0 === e && (e = "margin"), "undefined" == typeof window))
						return g;
					var t = w(e),
						n = document.documentElement.clientWidth,
						r = window.innerWidth;
					return {
						left: t[0],
						top: t[1],
						right: t[2],
						gap: Math.max(0, r - n + t[2] - t[0]),
					};
				},
				S = E(),
				x = "data-scroll-locked",
				N = (e, t, n, r) => {
					var o = e.left,
						a = e.top,
						c = e.right,
						l = e.gap;
					return (
						void 0 === n && (n = "margin"),
						"\n  ."
							.concat("with-scroll-bars-hidden", " {\n   overflow: hidden ")
							.concat(r, ";\n   padding-right: ")
							.concat(l, "px ")
							.concat(r, ";\n  }\n  body[")
							.concat(x, "] {\n    overflow: hidden ")
							.concat(r, ";\n    overscroll-behavior: contain;\n    ")
							.concat(
								[
									t && "position: relative ".concat(r, ";"),
									"margin" === n &&
										"\n    padding-left: "
											.concat(o, "px;\n    padding-top: ")
											.concat(a, "px;\n    padding-right: ")
											.concat(
												c,
												"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ",
											)
											.concat(l, "px ")
											.concat(r, ";\n    "),
									"padding" === n &&
										"padding-right: ".concat(l, "px ").concat(r, ";"),
								]
									.filter(Boolean)
									.join(""),
								"\n  }\n  \n  .",
							)
							.concat(i, " {\n    right: ")
							.concat(l, "px ")
							.concat(r, ";\n  }\n  \n  .")
							.concat(u, " {\n    margin-right: ")
							.concat(l, "px ")
							.concat(r, ";\n  }\n  \n  .")
							.concat(i, " .")
							.concat(i, " {\n    right: 0 ")
							.concat(r, ";\n  }\n  \n  .")
							.concat(u, " .")
							.concat(u, " {\n    margin-right: 0 ")
							.concat(r, ";\n  }\n  \n  body[")
							.concat(x, "] {\n    ")
							.concat("--removed-body-scroll-bar-size", ": ")
							.concat(l, "px;\n  }\n")
					);
				},
				R = () => {
					var e = Number.parseInt(document.body.getAttribute(x) || "0", 10);
					return isFinite(e) ? e : 0;
				},
				L = () => {
					a.useEffect(
						() => (
							document.body.setAttribute(x, (R() + 1).toString()),
							() => {
								var e = R() - 1;
								e <= 0
									? document.body.removeAttribute(x)
									: document.body.setAttribute(x, e.toString());
							}
						),
						[],
					);
				},
				M = (e) => {
					var t = e.noRelative,
						n = e.noImportant,
						r = e.gapMode,
						o = void 0 === r ? "margin" : r;
					L();
					var i = a.useMemo(() => C(o), [o]);
					return a.createElement(S, {
						styles: N(i, !t, o, n ? "" : "!important"),
					});
				},
				T = !1;
			if ("undefined" != typeof window)
				try {
					var P = Object.defineProperty({}, "passive", {
						get: () => ((T = !0), !0),
					});
					window.addEventListener("test", P, P),
						window.removeEventListener("test", P, P);
				} catch (e) {
					T = !1;
				}
			var k = !!T && { passive: !1 },
				A = (e, t) => {
					if (!(e instanceof Element)) return !1;
					var n = window.getComputedStyle(e);
					return (
						"hidden" !== n[t] &&
						(n.overflowY !== n.overflowX ||
							"TEXTAREA" === e.tagName ||
							"visible" !== n[t])
					);
				},
				D = (e, t) => {
					var n = t.ownerDocument,
						r = t;
					do {
						if (
							("undefined" != typeof ShadowRoot &&
								r instanceof ShadowRoot &&
								(r = r.host),
							O(e, r))
						) {
							var o = W(e, r);
							if (o[1] > o[2]) return !0;
						}
						r = r.parentNode;
					} while (r && r !== n.body);
					return !1;
				},
				O = (e, t) => ("v" === e ? A(t, "overflowY") : A(t, "overflowX")),
				W = (e, t) =>
					"v" === e
						? [t.scrollTop, t.scrollHeight, t.clientHeight]
						: [t.scrollLeft, t.scrollWidth, t.clientWidth],
				F = (e, t, n, r, o) => {
					var a,
						i =
							((a = window.getComputedStyle(t).direction),
							"h" === e && "rtl" === a ? -1 : 1),
						u = i * r,
						c = n.target,
						l = t.contains(c),
						s = !1,
						d = u > 0,
						f = 0,
						v = 0;
					do {
						if (!c) break;
						var m = W(e, c),
							p = m[0],
							h = m[1] - m[2] - i * p;
						(p || h) && O(e, c) && ((f += h), (v += p));
						var y = c.parentNode;
						c = y && y.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? y.host : y;
					} while (
						(!l && c !== document.body) ||
						(l && (t.contains(c) || t === c))
					);
					return (
						d && ((o && 1 > Math.abs(f)) || (!o && u > f))
							? (s = !0)
							: !d && ((o && 1 > Math.abs(v)) || (!o && -u > v)) && (s = !0),
						s
					);
				},
				I = (e) =>
					"changedTouches" in e
						? [e.changedTouches[0].clientX, e.changedTouches[0].clientY]
						: [0, 0],
				j = (e) => [e.deltaX, e.deltaY],
				B = (e) => (e && "current" in e ? e.current : e),
				_ = 0,
				X = [];
			const $ =
				(f.useMedium((e) => {
					var t = a.useRef([]),
						n = a.useRef([0, 0]),
						r = a.useRef(),
						i = a.useState(_++)[0],
						u = a.useState(E)[0],
						c = a.useRef(e);
					a.useEffect(() => {
						c.current = e;
					}, [e]),
						a.useEffect(() => {
							if (e.inert) {
								document.body.classList.add("block-interactivity-".concat(i));
								var t = (0, o.fX)(
									[e.lockRef.current],
									(e.shards || []).map(B),
									!0,
								).filter(Boolean);
								return (
									t.forEach((e) =>
										e.classList.add("allow-interactivity-".concat(i)),
									),
									() => {
										document.body.classList.remove(
											"block-interactivity-".concat(i),
										),
											t.forEach((e) =>
												e.classList.remove("allow-interactivity-".concat(i)),
											);
									}
								);
							}
						}, [e.inert, e.lockRef.current, e.shards]);
					var l = a.useCallback((e, t) => {
							if (
								("touches" in e && 2 === e.touches.length) ||
								("wheel" === e.type && e.ctrlKey)
							)
								return !c.current.allowPinchZoom;
							var o,
								a = I(e),
								i = n.current,
								u = "deltaX" in e ? e.deltaX : i[0] - a[0],
								l = "deltaY" in e ? e.deltaY : i[1] - a[1],
								s = e.target,
								d = Math.abs(u) > Math.abs(l) ? "h" : "v";
							if ("touches" in e && "h" === d && "range" === s.type) return !1;
							var f = D(d, s);
							if (!f) return !0;
							if (
								(f ? (o = d) : ((o = "v" === d ? "h" : "v"), (f = D(d, s))), !f)
							)
								return !1;
							if (
								(!r.current &&
									"changedTouches" in e &&
									(u || l) &&
									(r.current = o),
								!o)
							)
								return !0;
							var v = r.current || o;
							return F(v, t, e, "h" === v ? u : l, !0);
						}, []),
						s = a.useCallback((e) => {
							if (X.length && X[X.length - 1] === u) {
								var n = "deltaY" in e ? j(e) : I(e),
									r = t.current.filter((t) => {
										var r;
										return (
											t.name === e.type &&
											(t.target === e.target || e.target === t.shadowParent) &&
											(r = t.delta)[0] === n[0] &&
											r[1] === n[1]
										);
									})[0];
								if (r && r.should) {
									e.cancelable && e.preventDefault();
									return;
								}
								if (!r) {
									var o = (c.current.shards || [])
										.map(B)
										.filter(Boolean)
										.filter((t) => t.contains(e.target));
									(o.length > 0 ? l(e, o[0]) : !c.current.noIsolation) &&
										e.cancelable &&
										e.preventDefault();
								}
							}
						}, []),
						d = a.useCallback((e, n, r, o) => {
							var a = {
								name: e,
								delta: n,
								target: r,
								should: o,
								shadowParent: ((e) => {
									for (var t = null; null !== e; )
										e instanceof ShadowRoot && ((t = e.host), (e = e.host)),
											(e = e.parentNode);
									return t;
								})(r),
							};
							t.current.push(a),
								setTimeout(() => {
									t.current = t.current.filter((e) => e !== a);
								}, 1);
						}, []),
						f = a.useCallback((e) => {
							(n.current = I(e)), (r.current = void 0);
						}, []),
						v = a.useCallback((t) => {
							d(t.type, j(t), t.target, l(t, e.lockRef.current));
						}, []),
						m = a.useCallback((t) => {
							d(t.type, I(t), t.target, l(t, e.lockRef.current));
						}, []);
					a.useEffect(
						() => (
							X.push(u),
							e.setCallbacks({
								onScrollCapture: v,
								onWheelCapture: v,
								onTouchMoveCapture: m,
							}),
							document.addEventListener("wheel", s, k),
							document.addEventListener("touchmove", s, k),
							document.addEventListener("touchstart", f, k),
							() => {
								(X = X.filter((e) => e !== u)),
									document.removeEventListener("wheel", s, k),
									document.removeEventListener("touchmove", s, k),
									document.removeEventListener("touchstart", f, k);
							}
						),
						[],
					);
					var p = e.removeScrollBar,
						h = e.inert;
					return a.createElement(
						a.Fragment,
						null,
						h
							? a.createElement(u, {
									styles: "\n  .block-interactivity-"
										.concat(
											i,
											" {pointer-events: none;}\n  .allow-interactivity-",
										)
										.concat(i, " {pointer-events: all;}\n"),
								})
							: null,
						p
							? a.createElement(M, {
									noRelative: e.noRelative,
									gapMode: e.gapMode,
								})
							: null,
					);
				}),
				p);
			var K = a.forwardRef((e, t) =>
				a.createElement(m, (0, o.Cl)({}, e, { ref: t, sideCar: $ })),
			);
			K.classNames = m.classNames;
			const q = K;
		},
		47311: (e, t, n) => {
			n.d(t, { qW: () => f });
			var r,
				o = n(13072),
				a = n(85271),
				i = n(60110),
				u = n(47851),
				c = n(99210),
				l = n(45781),
				s = "dismissableLayer.update",
				d = o.createContext({
					layers: new Set(),
					layersWithOutsidePointerEventsDisabled: new Set(),
					branches: new Set(),
				}),
				f = o.forwardRef((e, t) => {
					const {
							disableOutsidePointerEvents: n = !1,
							onEscapeKeyDown: f,
							onPointerDownOutside: p,
							onFocusOutside: h,
							onInteractOutside: y,
							onDismiss: E,
							...g
						} = e,
						b = o.useContext(d),
						[w, C] = o.useState(null),
						S = w?.ownerDocument ?? globalThis?.document,
						[, x] = o.useState({}),
						N = (0, u.s)(t, (e) => C(e)),
						R = Array.from(b.layers),
						[L] = [...b.layersWithOutsidePointerEventsDisabled].slice(-1),
						M = R.indexOf(L),
						T = w ? R.indexOf(w) : -1,
						P = b.layersWithOutsidePointerEventsDisabled.size > 0,
						k = T >= M,
						A = ((e, t = globalThis?.document) => {
							const n = (0, c.c)(e),
								r = o.useRef(!1),
								a = o.useRef(() => {});
							return (
								o.useEffect(() => {
									const e = (e) => {
											if (e.target && !r.current) {
												const r = () => {
														m("dismissableLayer.pointerDownOutside", n, o, {
															discrete: !0,
														});
													},
													o = { originalEvent: e };
												"touch" === e.pointerType
													? (t.removeEventListener("click", a.current),
														(a.current = r),
														t.addEventListener("click", a.current, {
															once: !0,
														}))
													: r();
											} else t.removeEventListener("click", a.current);
											r.current = !1;
										},
										o = window.setTimeout(() => {
											t.addEventListener("pointerdown", e);
										}, 0);
									return () => {
										window.clearTimeout(o),
											t.removeEventListener("pointerdown", e),
											t.removeEventListener("click", a.current);
									};
								}, [t, n]),
								{ onPointerDownCapture: () => (r.current = !0) }
							);
						})((e) => {
							const t = e.target,
								n = [...b.branches].some((e) => e.contains(t));
							!k || n || (p?.(e), y?.(e), e.defaultPrevented || E?.());
						}, S),
						D = ((e, t = globalThis?.document) => {
							const n = (0, c.c)(e),
								r = o.useRef(!1);
							return (
								o.useEffect(() => {
									const e = (e) => {
										e.target &&
											!r.current &&
											m(
												"dismissableLayer.focusOutside",
												n,
												{ originalEvent: e },
												{ discrete: !1 },
											);
									};
									return (
										t.addEventListener("focusin", e),
										() => t.removeEventListener("focusin", e)
									);
								}, [t, n]),
								{
									onFocusCapture: () => (r.current = !0),
									onBlurCapture: () => (r.current = !1),
								}
							);
						})((e) => {
							const t = e.target;
							[...b.branches].some((e) => e.contains(t)) ||
								(h?.(e), y?.(e), e.defaultPrevented || E?.());
						}, S);
					return (
						((e, t = globalThis?.document) => {
							const n = (0, c.c)(e);
							o.useEffect(() => {
								const e = (e) => {
									"Escape" === e.key && n(e);
								};
								return (
									t.addEventListener("keydown", e, { capture: !0 }),
									() => t.removeEventListener("keydown", e, { capture: !0 })
								);
							}, [n, t]);
						})((e) => {
							T === b.layers.size - 1 &&
								(f?.(e), !e.defaultPrevented && E && (e.preventDefault(), E()));
						}, S),
						o.useEffect(() => {
							if (w)
								return (
									n &&
										(0 === b.layersWithOutsidePointerEventsDisabled.size &&
											((r = S.body.style.pointerEvents),
											(S.body.style.pointerEvents = "none")),
										b.layersWithOutsidePointerEventsDisabled.add(w)),
									b.layers.add(w),
									v(),
									() => {
										n &&
											1 === b.layersWithOutsidePointerEventsDisabled.size &&
											(S.body.style.pointerEvents = r);
									}
								);
						}, [w, S, n, b]),
						o.useEffect(
							() => () => {
								w &&
									(b.layers.delete(w),
									b.layersWithOutsidePointerEventsDisabled.delete(w),
									v());
							},
							[w, b],
						),
						o.useEffect(() => {
							const e = () => x({});
							return (
								document.addEventListener(s, e),
								() => document.removeEventListener(s, e)
							);
						}, []),
						(0, l.jsx)(i.sG.div, {
							...g,
							ref: N,
							style: {
								pointerEvents: P ? (k ? "auto" : "none") : void 0,
								...e.style,
							},
							onFocusCapture: (0, a.m)(e.onFocusCapture, D.onFocusCapture),
							onBlurCapture: (0, a.m)(e.onBlurCapture, D.onBlurCapture),
							onPointerDownCapture: (0, a.m)(
								e.onPointerDownCapture,
								A.onPointerDownCapture,
							),
						})
					);
				});
			function v() {
				const e = new CustomEvent(s);
				document.dispatchEvent(e);
			}
			function m(e, t, n, { discrete: r }) {
				const o = n.originalEvent.target,
					a = new CustomEvent(e, { bubbles: !1, cancelable: !0, detail: n });
				t && o.addEventListener(e, t, { once: !0 }),
					r ? (0, i.hO)(o, a) : o.dispatchEvent(a);
			}
			(f.displayName = "DismissableLayer"),
				(o.forwardRef((e, t) => {
					const n = o.useContext(d),
						r = o.useRef(null),
						a = (0, u.s)(t, r);
					return (
						o.useEffect(() => {
							const e = r.current;
							if (e)
								return (
									n.branches.add(e),
									() => {
										n.branches.delete(e);
									}
								);
						}, [n.branches]),
						(0, l.jsx)(i.sG.div, { ...e, ref: a })
					);
				}).displayName = "DismissableLayerBranch");
		},
		52650: (e, t, n) => {
			n.d(t, { i: () => a });
			var r = n(13072),
				o = n(99210);
			function a({ prop: e, defaultProp: t, onChange: n = () => {} }) {
				const [a, i] = (({ defaultProp: e, onChange: t }) => {
						const n = r.useState(e),
							[a] = n,
							i = r.useRef(a),
							u = (0, o.c)(t);
						return (
							r.useEffect(() => {
								i.current !== a && (u(a), (i.current = a));
							}, [a, i, u]),
							n
						);
					})({ defaultProp: t, onChange: n }),
					u = void 0 !== e,
					c = u ? e : a,
					l = (0, o.c)(n);
				return [
					c,
					r.useCallback(
						(t) => {
							if (u) {
								const n = "function" == typeof t ? t(e) : t;
								n !== e && l(n);
							} else i(t);
						},
						[u, e, i, l],
					),
				];
			}
		},
		60110: (e, t, n) => {
			n.d(t, { hO: () => c, sG: () => u });
			var r = n(13072),
				o = n(76129),
				a = n(74645),
				i = n(45781),
				u = [
					"a",
					"button",
					"div",
					"form",
					"h2",
					"h3",
					"img",
					"input",
					"label",
					"li",
					"nav",
					"ol",
					"p",
					"span",
					"svg",
					"ul",
				].reduce((e, t) => {
					const n = r.forwardRef((e, n) => {
						const { asChild: r, ...o } = e,
							u = r ? a.DX : t;
						return (
							"undefined" != typeof window &&
								(window[Symbol.for("radix-ui")] = !0),
							(0, i.jsx)(u, { ...o, ref: n })
						);
					});
					return (n.displayName = `Primitive.${t}`), { ...e, [t]: n };
				}, {});
			function c(e, t) {
				e && o.flushSync(() => e.dispatchEvent(t));
			}
		},
		61779: (e, t, n) => {
			n.d(t, { A: () => i, q: () => a });
			var r = n(13072),
				o = n(45781);
			function a(e, t) {
				const n = r.createContext(t),
					a = (e) => {
						const { children: t, ...a } = e,
							i = r.useMemo(() => a, Object.values(a));
						return (0, o.jsx)(n.Provider, { value: i, children: t });
					};
				return (
					(a.displayName = e + "Provider"),
					[
						a,
						(o) => {
							const a = r.useContext(n);
							if (a) return a;
							if (void 0 !== t) return t;
							throw Error(`\`${o}\` must be used within \`${e}\``);
						},
					]
				);
			}
			function i(e, t = []) {
				let n = [],
					a = () => {
						const t = n.map((e) => r.createContext(e));
						return (n) => {
							const o = n?.[e] || t;
							return r.useMemo(
								() => ({ [`__scope${e}`]: { ...n, [e]: o } }),
								[n, o],
							);
						};
					};
				return (
					(a.scopeName = e),
					[
						(t, a) => {
							const i = r.createContext(a),
								u = n.length;
							n = [...n, a];
							const c = (t) => {
								const { scope: n, children: a, ...c } = t,
									l = n?.[e]?.[u] || i,
									s = r.useMemo(() => c, Object.values(c));
								return (0, o.jsx)(l.Provider, { value: s, children: a });
							};
							return (
								(c.displayName = t + "Provider"),
								[
									c,
									(n, o) => {
										const c = o?.[e]?.[u] || i,
											l = r.useContext(c);
										if (l) return l;
										if (void 0 !== a) return a;
										throw Error(`\`${n}\` must be used within \`${t}\``);
									},
								]
							);
						},
						((...e) => {
							const t = e[0];
							if (1 === e.length) return t;
							const n = () => {
								const n = e.map((e) => ({
									useScope: e(),
									scopeName: e.scopeName,
								}));
								return (e) => {
									const o = n.reduce((t, { useScope: n, scopeName: r }) => {
										const o = n(e)[`__scope${r}`];
										return { ...t, ...o };
									}, {});
									return r.useMemo(
										() => ({ [`__scope${t.scopeName}`]: o }),
										[o],
									);
								};
							};
							return (n.scopeName = t.scopeName), n;
						})(a, ...t),
					]
				);
			}
		},
		63904: (e, t, n) => {
			n.d(t, { N: () => o });
			var r = n(13072),
				o = globalThis?.document ? r.useLayoutEffect : () => {};
		},
		85271: (e, t, n) => {
			n.d(t, { m: () => r });
			function r(e, t, { checkForDefaultPrevented: n = !0 } = {}) {
				return (r) => {
					if ((e?.(r), !1 === n || !r.defaultPrevented)) return t?.(r);
				};
			}
		},
		91403: (e, t, n) => {
			n.d(t, { Oh: () => a });
			var r = n(13072),
				o = 0;
			function a() {
				r.useEffect(() => {
					const e = document.querySelectorAll("[data-radix-focus-guard]");
					return (
						document.body.insertAdjacentElement("afterbegin", e[0] ?? i()),
						document.body.insertAdjacentElement("beforeend", e[1] ?? i()),
						o++,
						() => {
							1 === o &&
								document
									.querySelectorAll("[data-radix-focus-guard]")
									.forEach((e) => e.remove()),
								o--;
						}
					);
				}, []);
			}
			function i() {
				const e = document.createElement("span");
				return (
					e.setAttribute("data-radix-focus-guard", ""),
					(e.tabIndex = 0),
					(e.style.outline = "none"),
					(e.style.opacity = "0"),
					(e.style.position = "fixed"),
					(e.style.pointerEvents = "none"),
					e
				);
			}
		},
		99210: (e, t, n) => {
			n.d(t, { c: () => o });
			var r = n(13072);
			function o(e) {
				const t = r.useRef(e);
				return (
					r.useEffect(() => {
						t.current = e;
					}),
					r.useMemo(
						() =>
							(...e) =>
								t.current?.(...e),
						[],
					)
				);
			}
		},
	});
