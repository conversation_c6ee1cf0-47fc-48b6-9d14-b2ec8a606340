(exports.id = 704),
	(exports.ids = [704]),
	(exports.modules = {
		2483: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getImgProps", {
					enumerable: !0,
					get: () => l,
				}),
				r(70234);
			const n = r(57218),
				i = r(27822);
			function o(e) {
				return void 0 !== e.default;
			}
			function a(e) {
				return void 0 === e
					? e
					: "number" == typeof e
						? Number.isFinite(e)
							? e
							: Number.NaN
						: "string" == typeof e && /^[0-9]+$/.test(e)
							? Number.parseInt(e, 10)
							: Number.NaN;
			}
			function l(e, t) {
				var r, l;
				let s,
					d,
					u,
					{
						src: c,
						sizes: f,
						unoptimized: p = !1,
						priority: m = !1,
						loading: g,
						className: h,
						quality: v,
						width: b,
						height: y,
						fill: _ = !1,
						style: x,
						overrideSrc: w,
						onLoad: S,
						onLoadingComplete: j,
						placeholder: O = "empty",
						blurDataURL: C,
						fetchPriority: E,
						decoding: P = "async",
						layout: M,
						objectFit: R,
						objectPosition: z,
						lazyBoundary: I,
						lazyRoot: A,
						...k
					} = e,
					{ imgConf: D, showAltText: T, blurComplete: N, defaultLoader: U } = t,
					F = D || i.imageConfigDefault;
				if ("allSizes" in F) s = F;
				else {
					const e = [...F.deviceSizes, ...F.imageSizes].sort((e, t) => e - t),
						t = F.deviceSizes.sort((e, t) => e - t),
						n = null == (r = F.qualities) ? void 0 : r.sort((e, t) => e - t);
					s = { ...F, allSizes: e, deviceSizes: t, qualities: n };
				}
				if (void 0 === U)
					throw Object.defineProperty(
						Error(
							"images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config",
						),
						"__NEXT_ERROR_CODE",
						{ value: "E163", enumerable: !1, configurable: !0 },
					);
				let L = k.loader || U;
				delete k.loader, delete k.srcSet;
				const G = "__next_img_default" in L;
				if (G) {
					if ("custom" === s.loader)
						throw Object.defineProperty(
							Error(
								'Image with src "' +
									c +
									'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader',
							),
							"__NEXT_ERROR_CODE",
							{ value: "E252", enumerable: !1, configurable: !0 },
						);
				} else {
					const e = L;
					L = (t) => {
						const { config: r, ...n } = t;
						return e(n);
					};
				}
				if (M) {
					"fill" === M && (_ = !0);
					const e = {
						intrinsic: { maxWidth: "100%", height: "auto" },
						responsive: { width: "100%", height: "auto" },
					}[M];
					e && (x = { ...x, ...e });
					const t = { responsive: "100vw", fill: "100vw" }[M];
					t && !f && (f = t);
				}
				let B = "",
					q = a(b),
					W = a(y);
				if ((l = c) && "object" == typeof l && (o(l) || void 0 !== l.src)) {
					const e = o(c) ? c.default : c;
					if (!e.src)
						throw Object.defineProperty(
							Error(
								"An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received " +
									JSON.stringify(e),
							),
							"__NEXT_ERROR_CODE",
							{ value: "E460", enumerable: !1, configurable: !0 },
						);
					if (!e.height || !e.width)
						throw Object.defineProperty(
							Error(
								"An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received " +
									JSON.stringify(e),
							),
							"__NEXT_ERROR_CODE",
							{ value: "E48", enumerable: !1, configurable: !0 },
						);
					if (
						((d = e.blurWidth),
						(u = e.blurHeight),
						(C = C || e.blurDataURL),
						(B = e.src),
						!_)
					) {
						if (q || W) {
							if (q && !W) {
								const t = q / e.width;
								W = Math.round(e.height * t);
							} else if (!q && W) {
								const t = W / e.height;
								q = Math.round(e.width * t);
							}
						} else (q = e.width), (W = e.height);
					}
				}
				let X = !m && ("lazy" === g || void 0 === g);
				(!(c = "string" == typeof c ? c : B) ||
					c.startsWith("data:") ||
					c.startsWith("blob:")) &&
					((p = !0), (X = !1)),
					s.unoptimized && (p = !0),
					G &&
						!s.dangerouslyAllowSVG &&
						c.split("?", 1)[0].endsWith(".svg") &&
						(p = !0);
				const H = a(v),
					V = Object.assign(
						_
							? {
									position: "absolute",
									height: "100%",
									width: "100%",
									left: 0,
									top: 0,
									right: 0,
									bottom: 0,
									objectFit: R,
									objectPosition: z,
								}
							: {},
						T ? {} : { color: "transparent" },
						x,
					),
					$ =
						N || "empty" === O
							? null
							: "blur" === O
								? 'url("data:image/svg+xml;charset=utf-8,' +
									(0, n.getImageBlurSvg)({
										widthInt: q,
										heightInt: W,
										blurWidth: d,
										blurHeight: u,
										blurDataURL: C || "",
										objectFit: V.objectFit,
									}) +
									'")'
								: 'url("' + O + '")',
					J = $
						? {
								backgroundSize: V.objectFit || "cover",
								backgroundPosition: V.objectPosition || "50% 50%",
								backgroundRepeat: "no-repeat",
								backgroundImage: $,
							}
						: {},
					Y = ((e) => {
						const {
							config: t,
							src: r,
							unoptimized: n,
							width: i,
							quality: o,
							sizes: a,
							loader: l,
						} = e;
						if (n) return { src: r, srcSet: void 0, sizes: void 0 };
						const { widths: s, kind: d } = ((e, t, r) => {
								const { deviceSizes: n, allSizes: i } = e;
								if (r) {
									const e = /(^|\s)(1?\d?\d)vw/g,
										t = [];
									for (let n; (n = e.exec(r)); n) t.push(Number.parseInt(n[2]));
									if (t.length) {
										const e = 0.01 * Math.min(...t);
										return {
											widths: i.filter((t) => t >= n[0] * e),
											kind: "w",
										};
									}
									return { widths: i, kind: "w" };
								}
								return "number" != typeof t
									? { widths: n, kind: "w" }
									: {
											widths: [
												...new Set(
													[t, 2 * t].map(
														(e) => i.find((t) => t >= e) || i[i.length - 1],
													),
												),
											],
											kind: "x",
										};
							})(t, i, a),
							u = s.length - 1;
						return {
							sizes: a || "w" !== d ? a : "100vw",
							srcSet: s
								.map(
									(e, n) =>
										l({ config: t, src: r, quality: o, width: e }) +
										" " +
										("w" === d ? e : n + 1) +
										d,
								)
								.join(", "),
							src: l({ config: t, src: r, quality: o, width: s[u] }),
						};
					})({
						config: s,
						src: c,
						unoptimized: p,
						width: q,
						quality: H,
						sizes: f,
						loader: L,
					});
				return {
					props: {
						...k,
						loading: X ? "lazy" : g,
						fetchPriority: E,
						width: q,
						height: W,
						decoding: P,
						className: h,
						style: { ...V, ...J },
						sizes: Y.sizes,
						srcSet: Y.srcSet,
						src: w || Y.src,
					},
					meta: { unoptimized: p, priority: m, placeholder: O, fill: _ },
				};
			}
		},
		3704: (e, t, r) => {
			r.d(t, { default: () => i.a });
			var n = r(38171),
				i = r.n(n);
		},
		3707: (e, t, r) => {
			e.exports = r(51175).vendored.contexts.HeadManagerContext;
		},
		7371: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "Image", { enumerable: !0, get: () => _ });
			const n = r(13825),
				i = r(82076),
				o = r(45781),
				a = i._(r(13072)),
				l = n._(r(76129)),
				s = n._(r(27434)),
				d = r(2483),
				u = r(27822),
				c = r(52773);
			r(70234);
			const f = r(92486),
				p = n._(r(58979)),
				m = r(70828),
				g = {
					deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
					imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
					path: "/_next/image",
					loader: "default",
					dangerouslyAllowSVG: !1,
					unoptimized: !0,
				};
			function h(e, t, r, n, i, o, a) {
				const l = null == e ? void 0 : e.src;
				e &&
					e["data-loaded-src"] !== l &&
					((e["data-loaded-src"] = l),
					("decode" in e ? e.decode() : Promise.resolve())
						.catch(() => {})
						.then(() => {
							if (e.parentElement && e.isConnected) {
								if (("empty" !== t && i(!0), null == r ? void 0 : r.current)) {
									const t = new Event("load");
									Object.defineProperty(t, "target", {
										writable: !1,
										value: e,
									});
									let n = !1,
										i = !1;
									r.current({
										...t,
										nativeEvent: t,
										currentTarget: e,
										target: e,
										isDefaultPrevented: () => n,
										isPropagationStopped: () => i,
										persist: () => {},
										preventDefault: () => {
											(n = !0), t.preventDefault();
										},
										stopPropagation: () => {
											(i = !0), t.stopPropagation();
										},
									});
								}
								(null == n ? void 0 : n.current) && n.current(e);
							}
						}));
			}
			function v(e) {
				return a.use ? { fetchPriority: e } : { fetchpriority: e };
			}
			globalThis.__NEXT_IMAGE_IMPORTED = !0;
			const b = (0, a.forwardRef)((e, t) => {
				const {
						src: r,
						srcSet: n,
						sizes: i,
						height: l,
						width: s,
						decoding: d,
						className: u,
						style: c,
						fetchPriority: f,
						placeholder: p,
						loading: g,
						unoptimized: b,
						fill: y,
						onLoadRef: _,
						onLoadingCompleteRef: x,
						setBlurComplete: w,
						setShowAltText: S,
						sizesInput: j,
						onLoad: O,
						onError: C,
						...E
					} = e,
					P = (0, a.useCallback)(
						(e) => {
							e && (C && (e.src = e.src), e.complete && h(e, p, _, x, w, b, j));
						},
						[r, p, _, x, w, C, b, j],
					),
					M = (0, m.useMergedRef)(t, P);
				return (0, o.jsx)("img", {
					...E,
					...v(f),
					loading: g,
					width: s,
					height: l,
					decoding: d,
					"data-nimg": y ? "fill" : "1",
					className: u,
					style: c,
					sizes: i,
					srcSet: n,
					src: r,
					ref: M,
					onLoad: (e) => {
						h(e.currentTarget, p, _, x, w, b, j);
					},
					onError: (e) => {
						S(!0), "empty" !== p && w(!0), C && C(e);
					},
				});
			});
			function y(e) {
				const { isAppRouter: t, imgAttributes: r } = e,
					n = {
						as: "image",
						imageSrcSet: r.srcSet,
						imageSizes: r.sizes,
						crossOrigin: r.crossOrigin,
						referrerPolicy: r.referrerPolicy,
						...v(r.fetchPriority),
					};
				return t && l.default.preload
					? (l.default.preload(r.src, n), null)
					: (0, o.jsx)(s.default, {
							children: (0, o.jsx)(
								"link",
								{ rel: "preload", href: r.srcSet ? void 0 : r.src, ...n },
								"__nimg-" + r.src + r.srcSet + r.sizes,
							),
						});
			}
			const _ = (0, a.forwardRef)((e, t) => {
				const r = (0, a.useContext)(f.RouterContext),
					n = (0, a.useContext)(c.ImageConfigContext),
					i = (0, a.useMemo)(() => {
						var e;
						const t = g || n || u.imageConfigDefault,
							r = [...t.deviceSizes, ...t.imageSizes].sort((e, t) => e - t),
							i = t.deviceSizes.sort((e, t) => e - t),
							o = null == (e = t.qualities) ? void 0 : e.sort((e, t) => e - t);
						return { ...t, allSizes: r, deviceSizes: i, qualities: o };
					}, [n]),
					{ onLoad: l, onLoadingComplete: s } = e,
					m = (0, a.useRef)(l);
				(0, a.useEffect)(() => {
					m.current = l;
				}, [l]);
				const h = (0, a.useRef)(s);
				(0, a.useEffect)(() => {
					h.current = s;
				}, [s]);
				const [v, _] = (0, a.useState)(!1),
					[x, w] = (0, a.useState)(!1),
					{ props: S, meta: j } = (0, d.getImgProps)(e, {
						defaultLoader: p.default,
						imgConf: i,
						blurComplete: v,
						showAltText: x,
					});
				return (0, o.jsxs)(o.Fragment, {
					children: [
						(0, o.jsx)(b, {
							...S,
							unoptimized: j.unoptimized,
							placeholder: j.placeholder,
							fill: j.fill,
							onLoadRef: m,
							onLoadingCompleteRef: h,
							setBlurComplete: _,
							setShowAltText: w,
							sizesInput: e.sizes,
							ref: t,
						}),
						j.priority
							? (0, o.jsx)(y, { isAppRouter: !r, imgAttributes: S })
							: null,
					],
				});
			});
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		27434: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => g, defaultHead: () => c });
			const n = r(13825),
				i = r(82076),
				o = r(45781),
				a = i._(r(13072)),
				l = n._(r(68153)),
				s = r(67617),
				d = r(3707),
				u = r(91922);
			function c(e) {
				void 0 === e && (e = !1);
				const t = [(0, o.jsx)("meta", { charSet: "utf-8" }, "charset")];
				return (
					e ||
						t.push(
							(0, o.jsx)(
								"meta",
								{ name: "viewport", content: "width=device-width" },
								"viewport",
							),
						),
					t
				);
			}
			function f(e, t) {
				return "string" == typeof t || "number" == typeof t
					? e
					: t.type === a.default.Fragment
						? e.concat(
								a.default.Children.toArray(t.props.children).reduce(
									(e, t) =>
										"string" == typeof t || "number" == typeof t
											? e
											: e.concat(t),
									[],
								),
							)
						: e.concat(t);
			}
			r(70234);
			const p = ["name", "httpEquiv", "charSet", "itemProp"];
			function m(e, t) {
				const { inAmpMode: r } = t;
				return e
					.reduce(f, [])
					.reverse()
					.concat(c(r).reverse())
					.filter(
						(() => {
							const e = new Set(),
								t = new Set(),
								r = new Set(),
								n = {};
							return (i) => {
								let o = !0,
									a = !1;
								if (
									i.key &&
									"number" != typeof i.key &&
									i.key.indexOf("$") > 0
								) {
									a = !0;
									const t = i.key.slice(i.key.indexOf("$") + 1);
									e.has(t) ? (o = !1) : e.add(t);
								}
								switch (i.type) {
									case "title":
									case "base":
										t.has(i.type) ? (o = !1) : t.add(i.type);
										break;
									case "meta":
										for (let e = 0, t = p.length; e < t; e++) {
											const t = p[e];
											if (i.props.hasOwnProperty(t)) {
												if ("charSet" === t) r.has(t) ? (o = !1) : r.add(t);
												else {
													const e = i.props[t],
														r = n[t] || new Set();
													("name" !== t || !a) && r.has(e)
														? (o = !1)
														: (r.add(e), (n[t] = r));
												}
											}
										}
								}
								return o;
							};
						})(),
					)
					.reverse()
					.map((e, t) => {
						const n = e.key || t;
						if (
							process.env.__NEXT_OPTIMIZE_FONTS &&
							!r &&
							"link" === e.type &&
							e.props.href &&
							[
								"https://fonts.googleapis.com/css",
								"https://use.typekit.net/",
							].some((t) => e.props.href.startsWith(t))
						) {
							const t = { ...(e.props || {}) };
							return (
								(t["data-href"] = t.href),
								(t.href = void 0),
								(t["data-optimized-fonts"] = !0),
								a.default.cloneElement(e, t)
							);
						}
						return a.default.cloneElement(e, { key: n });
					});
			}
			const g = (e) => {
				const { children: t } = e,
					r = (0, a.useContext)(s.AmpStateContext),
					n = (0, a.useContext)(d.HeadManagerContext);
				return (0, o.jsx)(l.default, {
					reduceComponentsToState: m,
					headManager: n,
					inAmpMode: (0, u.isInAmpMode)(r),
					children: t,
				});
			};
			("function" == typeof t.default ||
				("object" == typeof t.default && null !== t.default)) &&
				void 0 === t.default.__esModule &&
				(Object.defineProperty(t.default, "__esModule", { value: !0 }),
				Object.assign(t.default, t),
				(e.exports = t.default));
		},
		27822: (e, t) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { VALID_LOADERS: () => r, imageConfigDefault: () => n });
			const r = ["default", "imgix", "cloudinary", "akamai", "custom"],
				n = {
					deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
					imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
					path: "/_next/image",
					loader: "default",
					loaderFile: "",
					domains: [],
					disableStaticImages: !1,
					minimumCacheTTL: 60,
					formats: ["image/webp"],
					dangerouslyAllowSVG: !1,
					contentSecurityPolicy:
						"script-src 'none'; frame-src 'none'; sandbox;",
					contentDispositionType: "attachment",
					localPatterns: void 0,
					remotePatterns: [],
					qualities: void 0,
					unoptimized: !1,
				};
		},
		38171: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				!((e, t) => {
					for (var r in t)
						Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
				})(t, { default: () => s, getImageProps: () => l });
			const n = r(13825),
				i = r(2483),
				o = r(7371),
				a = n._(r(58979));
			function l(e) {
				const { props: t } = (0, i.getImgProps)(e, {
					defaultLoader: a.default,
					imgConf: {
						deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
						imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
						path: "/_next/image",
						loader: "default",
						dangerouslyAllowSVG: !1,
						unoptimized: !0,
					},
				});
				for (const [e, r] of Object.entries(t)) void 0 === r && delete t[e];
				return { props: t };
			}
			const s = o.Image;
		},
		52773: (e, t, r) => {
			e.exports = r(51175).vendored.contexts.ImageConfigContext;
		},
		57218: (e, t) => {
			function r(e) {
				const {
						widthInt: t,
						heightInt: r,
						blurWidth: n,
						blurHeight: i,
						blurDataURL: o,
						objectFit: a,
					} = e,
					l = n ? 40 * n : t,
					s = i ? 40 * i : r,
					d = l && s ? "viewBox='0 0 " + l + " " + s + "'" : "";
				return (
					"%3Csvg xmlns='http://www.w3.org/2000/svg' " +
					d +
					"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='" +
					(d
						? "none"
						: "contain" === a
							? "xMidYMid"
							: "cover" === a
								? "xMidYMid slice"
								: "none") +
					"' style='filter: url(%23b);' href='" +
					o +
					"'/%3E%3C/svg%3E"
				);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "getImageBlurSvg", {
					enumerable: !0,
					get: () => r,
				});
		},
		58979: (e, t) => {
			function r(e) {
				var t;
				const { config: r, src: n, width: i, quality: o } = e,
					a =
						o ||
						(null == (t = r.qualities)
							? void 0
							: t.reduce((e, t) =>
									Math.abs(t - 75) < Math.abs(e - 75) ? t : e,
								)) ||
						75;
				return (
					r.path +
					"?url=" +
					encodeURIComponent(n) +
					"&w=" +
					i +
					"&q=" +
					a +
					(n.startsWith("/_next/static/media/"), "")
				);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => n }),
				(r.__next_img_default = !0);
			const n = r;
		},
		67617: (e, t, r) => {
			e.exports = r(51175).vendored.contexts.AmpContext;
		},
		68153: (e, t, r) => {
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "default", { enumerable: !0, get: () => a });
			const n = r(13072),
				i = () => {},
				o = () => {};
			function a(e) {
				var t;
				const { headManager: r, reduceComponentsToState: a } = e;
				function l() {
					if (r && r.mountedInstances) {
						const t = n.Children.toArray(
							Array.from(r.mountedInstances).filter(Boolean),
						);
						r.updateHead(a(t, e));
					}
				}
				return (
					null == r || null == (t = r.mountedInstances) || t.add(e.children),
					l(),
					i(() => {
						var t;
						return (
							null == r ||
								null == (t = r.mountedInstances) ||
								t.add(e.children),
							() => {
								var t;
								null == r ||
									null == (t = r.mountedInstances) ||
									t.delete(e.children);
							}
						);
					}),
					i(
						() => (
							r && (r._pendingUpdate = l),
							() => {
								r && (r._pendingUpdate = l);
							}
						),
					),
					o(
						() => (
							r &&
								r._pendingUpdate &&
								(r._pendingUpdate(), (r._pendingUpdate = null)),
							() => {
								r &&
									r._pendingUpdate &&
									(r._pendingUpdate(), (r._pendingUpdate = null));
							}
						),
					),
					null
				);
			}
		},
		91922: (e, t) => {
			function r(e) {
				const {
					ampFirst: t = !1,
					hybrid: r = !1,
					hasQuery: n = !1,
				} = void 0 === e ? {} : e;
				return t || (r && n);
			}
			Object.defineProperty(t, "__esModule", { value: !0 }),
				Object.defineProperty(t, "isInAmpMode", {
					enumerable: !0,
					get: () => r,
				});
		},
		92486: (e, t, r) => {
			e.exports = r(51175).vendored.contexts.RouterContext;
		},
	});
