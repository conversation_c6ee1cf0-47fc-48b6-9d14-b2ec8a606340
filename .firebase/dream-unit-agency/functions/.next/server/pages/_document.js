(() => {
	var e = {};
	(e.id = 220),
		(e.ids = [220]),
		(e.modules = {
			8732: (e) => {
				e.exports = require("react/jsx-runtime");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			40361: (e) => {
				e.exports = require("next/dist/compiled/next-server/pages.runtime.prod.js");
			},
			82015: (e) => {
				e.exports = require("react");
			},
		});
	var r = require("../webpack-runtime.js");
	r.C(e);
	var s = (e) => r((r.s = e)),
		t = r.X(0, [432], () => s(64432));
	module.exports = t;
})();
