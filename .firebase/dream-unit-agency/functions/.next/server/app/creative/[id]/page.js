(() => {
	var e = {};
	(e.id = 716),
		(e.ids = [716]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			1437: (e, t, r) => {
				r.d(t, { A: () => c });
				var s = r(45781),
					i = r(28328),
					a = r.n(i),
					o = r(44238),
					l = r(72902),
					n = r(44301);
				const c = () =>
					(0, s.jsx)("header", {
						className: "border-b border-gray-100",
						children: (0, s.jsx)("div", {
							className: "max-w-4xl mx-auto px-6 py-6",
							children: (0, s.jsxs)("div", {
								className: "flex items-center justify-between",
								children: [
									(0, s.jsx)(a(), {
										href: "/",
										className: "text-2xl font-light tracking-wide",
										children: "DUA",
									}),
									(0, s.jsxs)("nav", {
										className: "hidden md:flex items-center space-x-8 text-sm",
										children: [
											(0, s.jsx)(a(), {
												href: "/",
												className:
													"text-gray-600 hover:text-black transition-colors",
												children: "Search",
											}),
											(0, s.jsx)("span", {
												className: "text-black",
												children: "Portfolio",
											}),
											(0, s.jsx)("span", {
												className: "text-gray-600",
												children: "About",
											}),
											(0, s.jsx)("span", {
												className: "text-gray-600",
												children: "Contact",
											}),
											(0, s.jsx)(l.A, { className: "w-4 h-4 text-gray-600" }),
										],
									}),
									(0, s.jsx)(o.$, {
										variant: "ghost",
										size: "icon",
										className: "md:hidden",
										children: (0, s.jsx)(n.A, { className: "h-5 w-5" }),
									}),
								],
							}),
						}),
					});
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			7923: (e, t, r) => {
				r.r(t), r.d(t, { default: () => a });
				var s = r(95479),
					i = r(12083);
				function a({ params: e }) {
					return (0, s.jsx)(i.default, { creativeId: e.id });
				}
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			12083: (e, t, r) => {
				r.d(t, { default: () => s });
				const s = (0, r(51129).registerClientReference)(
					() => {
						throw Error(
							"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/creative-profile-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
						);
					},
					"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/creative-profile-page.tsx",
					"default",
				);
			},
			13077: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 12083));
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			21345: (e, t, r) => {
				r.d(t, { A: () => s });
				const s = (0, r(22752).A)("Scissors", [
					["circle", { cx: "6", cy: "6", r: "3", key: "1lh9wr" }],
					["path", { d: "M8.12 8.12 12 12", key: "1alkpv" }],
					["path", { d: "M20 4 8.12 15.88", key: "xgtan2" }],
					["circle", { cx: "6", cy: "18", r: "3", key: "fqmcym" }],
					["path", { d: "M14.8 14.8 20 20", key: "ptml3r" }],
				]);
			},
			21820: (e) => {
				e.exports = require("os");
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			34931: (e, t, r) => {
				r.d(t, {
					KT: () => o,
					R4: () => l,
					TZ: () => n,
					Tg: () => a,
					Wm: () => c,
					qD: () => i,
					xk: () => s,
				});
				const s = {
						1: {
							name: "ALEX JOHNSON",
							type: "PHOTOGRAPHER",
							location: "New York, NY",
							followers: 1234,
							following: 567,
							bio: "Professional photographer specializing in portraits and fashion",
						},
						2: {
							name: "SARAH WILSON",
							type: "MODEL",
							location: "Los Angeles, CA",
							followers: 2456,
							following: 432,
							bio: "Fashion and commercial model",
						},
						3: {
							name: "MICHAEL CHEN",
							type: "VIDEOGRAPHER",
							location: "Chicago, IL",
							followers: 987,
							following: 234,
							bio: "Documentary and commercial videographer",
						},
						4: {
							name: "EMMA DAVIS",
							type: "MAKEUP ARTIST",
							location: "Miami, FL",
							followers: 1567,
							following: 345,
							bio: "Editorial and bridal makeup creative",
						},
						5: {
							name: "DAVID RODRIGUEZ",
							type: "PHOTOGRAPHER",
							location: "Austin, TX",
							followers: 2134,
							following: 678,
							bio: "Product and architecture photographer",
						},
					},
					i = [
						{
							id: "1",
							src: "/assets/creatives_portfolio/crt1.png",
							alt: "Sunset beach",
							likes: 324,
							comments: 18,
							position: 0,
						},
						{
							id: "2",
							src: "/assets/creatives_portfolio/crt2.png",
							alt: "Pasta dinner",
							likes: 189,
							comments: 12,
							position: 1,
						},
						{
							id: "3",
							src: "/assets/creatives_portfolio/crt3.png",
							alt: "Mountain view",
							likes: 456,
							comments: 23,
							position: 2,
						},
						{
							id: "4",
							src: "/assets/creatives_portfolio/crt4.png",
							alt: "Morning coffee",
							likes: 203,
							comments: 15,
							position: 3,
						},
						{
							id: "5",
							src: "/assets/creatives_portfolio/crt5.png",
							alt: "City lights",
							likes: 378,
							comments: 26,
							position: 4,
						},
						{
							id: "6",
							src: "/assets/creatives_portfolio/crt6.png",
							alt: "Golden retriever",
							likes: 567,
							comments: 41,
							position: 5,
						},
						{
							id: "7",
							src: "/assets/creatives_portfolio/crt8.png",
							alt: "Sushi dinner",
							likes: 234,
							comments: 19,
							position: 6,
						},
						{
							id: "8",
							src: "/assets/creatives_portfolio/crt7.png",
							alt: "Road trip",
							likes: 292,
							comments: 17,
							position: 7,
						},
						{
							id: "9",
							src: "/placeholder.svg?height=400&width=400&text=Colorful flower garden in spring",
							alt: "Spring flowers",
							likes: 445,
							comments: 31,
							position: 8,
						},
					],
					a = [
						{
							id: "job1",
							title: "Wedding Photography",
							client: "Sarah & Mike Johnson",
							date: "2024-01-15",
							time: "2:00 PM",
							location: "Central Park, NYC",
							status: "confirmed",
							type: "photography",
						},
						{
							id: "job2",
							title: "Corporate Headshots",
							client: "Tech Solutions Inc.",
							date: "2024-01-18",
							time: "10:00 AM",
							location: "Downtown Office",
							status: "pending",
							type: "photography",
						},
						{
							id: "job3",
							title: "Product Photography",
							client: "Fashion Brand Co.",
							date: "2024-01-22",
							time: "1:00 PM",
							location: "Studio A",
							status: "confirmed",
							type: "photography",
						},
						{
							id: "job4",
							title: "Event Coverage",
							client: "Marketing Agency",
							date: "2024-01-25",
							time: "6:00 PM",
							location: "Convention Center",
							status: "confirmed",
							type: "photography",
						},
					],
					o = [
						{
							id: "payment1",
							jobId: "job5",
							jobTitle: "Wedding Photography",
							client: "Jennifer & David Smith",
							amount: 2500,
							date: "2023-12-20",
							status: "paid",
							invoiceNumber: "INV-2023-001",
						},
						{
							id: "payment2",
							jobId: "job6",
							jobTitle: "Corporate Event",
							client: "Global Tech Inc.",
							amount: 1800,
							date: "2023-12-05",
							status: "paid",
							invoiceNumber: "INV-2023-002",
						},
						{
							id: "payment3",
							jobId: "job7",
							jobTitle: "Product Shoot",
							client: "Luxury Brands Co.",
							amount: 1200,
							date: "2023-11-28",
							status: "paid",
							invoiceNumber: "INV-2023-003",
						},
					],
					l = [
						{
							id: "project1",
							title: "Summer Lookbook",
							client: "Zara",
							projectType: "fashion",
							startDate: "2024-07-01",
							endDate: "2024-07-15",
							location: "Miami Beach",
							budget: 2e4,
							description:
								"Create a summer lookbook for Zara's new collection.",
							creativesNeeded: [
								"Photographer",
								"Model",
								"Makeup Artist",
								"Stylist",
							],
						},
						{
							id: "project2",
							title: "Corporate Headshots",
							client: "Google",
							projectType: "corporate",
							startDate: "2024-08-01",
							endDate: "2024-08-05",
							location: "Mountain View",
							budget: 1e4,
							description: "Take corporate headshots for Google's employees.",
							creativesNeeded: ["Photographer"],
						},
						{
							id: "project3",
							title: "Wedding Photography",
							client: "John and Jane Doe",
							projectType: "wedding",
							startDate: "2024-09-01",
							endDate: "2024-09-01",
							location: "Central Park",
							budget: 5e3,
							description: "Photograph John and Jane Doe's wedding.",
							creativesNeeded: ["Photographer"],
						},
					],
					n = [
						{
							id: "offer_rec1",
							projectTitle: "Summer Glow Campaign",
							clientName: "Sunlight Beauty Co.",
							role: "Lead Photographer",
							amount: 2200,
							offerDate: "2024-03-10",
							status: "pending",
							projectType: "Beauty Product Shoot",
							location: "Miami Beach, FL",
							description:
								"Looking for a photographer with a bright and airy style for our new sunscreen line.",
							isSentByMe: !1,
						},
						{
							id: "offer_rec2",
							projectTitle: "Urban Explorers Video",
							clientName: "City Adventures Magazine",
							role: "Videographer",
							amount: 1800,
							offerDate: "2024-03-05",
							status: "accepted",
							projectType: "Travel Documentary Short",
							location: "Various, NYC",
							description:
								"Short documentary piece following urban explorers. Drone skills a plus.",
							isSentByMe: !1,
						},
						{
							id: "offer_rec3",
							projectTitle: "Tech Conference Live Model",
							clientName: "Innovate Corp",
							role: "Promotional Model",
							amount: 750,
							offerDate: "2024-02-28",
							status: "declined",
							projectType: "Tech Event",
							location: "San Francisco, CA",
							description:
								"Need engaging models for our booth at the upcoming Innovate Summit.",
							isSentByMe: !1,
						},
					],
					c = [
						{
							id: "offer_sent1",
							projectTitle: "Indie Band Music Video",
							clientName: "The Wandering Souls (Band)",
							role: "Director of Photography",
							amount: 1500,
							offerDate: "2024-03-12",
							status: "pending",
							projectType: "Music Video",
							location: "Austin, TX",
							description:
								"Proposal to shoot and direct the photography for your upcoming music video.",
							isSentByMe: !0,
						},
						{
							id: "offer_sent2",
							projectTitle: "Artisan Bakery Branding",
							clientName: "The Sweet Spot Bakery",
							role: "Food Photographer",
							amount: 900,
							offerDate: "2024-03-02",
							status: "expired",
							projectType: "Branding & Lifestyle",
							location: "Portland, OR",
							description:
								"Offered to create a series of lifestyle and product shots for their new website.",
							isSentByMe: !0,
						},
						{
							id: "offer_sent3",
							projectTitle: "Local Cafe Social Media Content",
							clientName: "Corner Brew Cafe",
							role: "Content Creator (Photo/Video)",
							amount: 600,
							offerDate: "2024-02-20",
							status: "negotiating",
							projectType: "Social Media Marketing",
							location: "Local",
							description:
								"Proposed a monthly retainer for creating engaging social media content.",
							isSentByMe: !0,
						},
					];
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			44238: (e, t, r) => {
				r.d(t, { $: () => c, r: () => n });
				var s = r(45781),
					i = r(13072),
					a = r(74645),
					o = r(87990),
					l = r(77401);
				const n = (0, o.F)(
						"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
						{
							variants: {
								variant: {
									default:
										"bg-primary text-primary-foreground hover:bg-primary/90",
									destructive:
										"bg-destructive text-destructive-foreground hover:bg-destructive/90",
									outline:
										"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
									secondary:
										"bg-secondary text-secondary-foreground hover:bg-secondary/80",
									ghost: "hover:bg-accent hover:text-accent-foreground",
									link: "text-primary underline-offset-4 hover:underline",
								},
								size: {
									default: "h-10 px-4 py-2",
									sm: "h-9 rounded-md px-3",
									lg: "h-11 rounded-md px-8",
									icon: "h-10 w-10",
								},
							},
							defaultVariants: { variant: "default", size: "default" },
						},
					),
					c = i.forwardRef(
						(
							{ className: e, variant: t, size: r, asChild: i = !1, ...o },
							c,
						) => {
							const d = i ? a.DX : "button";
							return (0, s.jsx)(d, {
								className: (0, l.cn)(n({ variant: t, size: r, className: e })),
								ref: c,
								...o,
							});
						},
					);
				c.displayName = "Button";
			},
			44301: (e, t, r) => {
				r.d(t, { A: () => s });
				const s = (0, r(22752).A)("Menu", [
					["line", { x1: "4", x2: "20", y1: "12", y2: "12", key: "1e0a9i" }],
					["line", { x1: "4", x2: "20", y1: "6", y2: "6", key: "1owob3" }],
					["line", { x1: "4", x2: "20", y1: "18", y2: "18", key: "yk5zj1" }],
				]);
			},
			46909: (e, t, r) => {
				r.d(t, { A: () => s });
				const s = (0, r(22752).A)("Ellipsis", [
					["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }],
					["circle", { cx: "19", cy: "12", r: "1", key: "1wjl8i" }],
					["circle", { cx: "5", cy: "12", r: "1", key: "1pcz8c" }],
				]);
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			54933: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 68230));
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			68230: (e, t, r) => {
				r.d(t, { default: () => q });
				var s = r(45781),
					i = r(13072),
					a = r(34931);
				const o = ({ creative: e, photoCount: t }) =>
					(0, s.jsx)("div", {
						className: "border-b border-gray-100",
						children: (0, s.jsx)("div", {
							className: "max-w-4xl mx-auto px-6 py-16",
							children: (0, s.jsxs)("div", {
								className: "text-center mb-12",
								children: [
									(0, s.jsx)("h2", {
										className: "text-3xl font-light tracking-wide mb-3",
										children: e.name,
									}),
									(0, s.jsx)("p", {
										className: "text-gray-600 font-light tracking-wide mb-6",
										children: e.type,
									}),
									" ",
									(0, s.jsxs)("div", {
										className: "flex justify-center gap-12 text-sm font-light",
										children: [
											(0, s.jsxs)("span", {
												children: [
													(0, s.jsx)("strong", {
														className: "font-normal",
														children: t,
													}),
													" Works",
												],
											}),
											(0, s.jsxs)("span", {
												children: [
													(0, s.jsx)("strong", {
														className: "font-normal",
														children: e.followers,
													}),
													" Followers",
												],
											}),
											(0, s.jsxs)("span", {
												children: [
													(0, s.jsx)("strong", {
														className: "font-normal",
														children: e.following,
													}),
													" Following",
												],
											}),
										],
									}),
								],
							}),
						}),
					});
				var l = r(44238);
				const n = () => {
						const [e, t] = (0, i.useState)(() => 3);
						return (0, i.useEffect)(() => {}, []), e;
					},
					c = ({ activeTab: e, onOpenBookingModal: t }) => {
						const r = n(),
							i = [
								"Jan",
								"Feb",
								"Mar",
								"Apr",
								"May",
								"Jun",
								"Jul",
								"Aug",
								"Sep",
								"Oct",
								"Nov",
								"Dec",
							].slice(0, r);
						return (0, s.jsx)("div", {
							className: "border-b border-gray-100",
							children: (0, s.jsxs)("div", {
								className: "max-w-4xl mx-auto px-6 py-16",
								children: [
									(0, s.jsxs)("div", {
										className: "bg-gray-900 rounded p-8 mb-12",
										children: [
											(0, s.jsxs)("div", {
												className: "flex justify-between items-center mb-6",
												children: [
													(0, s.jsx)("span", {
														className:
															"text-white text-sm font-light tracking-wide",
														children:
															"income" === e
																? "Income Over Time"
																: "Availability",
													}),
													(0, s.jsxs)("div", {
														className: "flex items-center gap-6",
														children: [
															(0, s.jsxs)("span", {
																className: "flex items-center gap-2",
																children: [
																	(0, s.jsx)("div", {
																		className:
																			"w-2 h-2 rounded-full bg-green-400",
																	}),
																	(0, s.jsx)("span", {
																		className:
																			"text-gray-400 text-xs font-light tracking-wide",
																		children: "Available",
																	}),
																],
															}),
															(0, s.jsxs)("span", {
																className: "flex items-center gap-2",
																children: [
																	(0, s.jsx)("div", {
																		className:
																			"w-2 h-2 rounded-full bg-red-400",
																	}),
																	(0, s.jsx)("span", {
																		className:
																			"text-gray-400 text-xs font-light tracking-wide",
																		children: "Booked",
																	}),
																],
															}),
														],
													}),
												],
											}),
											"income" === e
												? (0, s.jsx)("div", {
														className: "h-48 relative",
														children: (0, s.jsxs)("svg", {
															className: "w-full h-full",
															viewBox: "0 0 400 200",
															children: [
																(0, s.jsx)("defs", {
																	children: (0, s.jsx)("pattern", {
																		id: "grid",
																		width: "40",
																		height: "20",
																		patternUnits: "userSpaceOnUse",
																		children: (0, s.jsx)("path", {
																			d: "M 40 0 L 0 0 0 20",
																			fill: "none",
																			stroke: "#374151",
																			strokeWidth: "0.5",
																			opacity: "0.3",
																		}),
																	}),
																}),
																(0, s.jsx)("rect", {
																	width: "100%",
																	height: "100%",
																	fill: "url(#grid)",
																}),
																(0, s.jsx)("line", {
																	x1: "20",
																	y1: "180",
																	x2: "380",
																	y2: "180",
																	stroke: "#4b5563",
																	strokeWidth: "1",
																}),
																(0, s.jsx)("line", {
																	x1: "20",
																	y1: "20",
																	x2: "20",
																	y2: "180",
																	stroke: "#4b5563",
																	strokeWidth: "1",
																}),
																[
																	{
																		x: 40,
																		width: 20,
																		height: 60,
																		amount: 1200,
																		date: "Jan 15",
																	},
																	{
																		x: 80,
																		width: 20,
																		height: 90,
																		amount: 1800,
																		date: "Feb 3",
																	},
																	{
																		x: 120,
																		width: 20,
																		height: 125,
																		amount: 2500,
																		date: "Mar 22",
																	},
																	{
																		x: 160,
																		width: 20,
																		height: 50,
																		amount: 1e3,
																		date: "Apr 10",
																	},
																	{
																		x: 200,
																		width: 20,
																		height: 75,
																		amount: 1500,
																		date: "May 5",
																	},
																	{
																		x: 240,
																		width: 20,
																		height: 110,
																		amount: 2200,
																		date: "Jun 18",
																	},
																	{
																		x: 280,
																		width: 20,
																		height: 40,
																		amount: 800,
																		date: "Jul 7",
																	},
																	{
																		x: 320,
																		width: 20,
																		height: 100,
																		amount: 2e3,
																		date: "Aug 29",
																	},
																	{
																		x: 360,
																		width: 20,
																		height: 80,
																		amount: 1600,
																		date: "Sep 14",
																	},
																].map((e, t) =>
																	(0, s.jsxs)(
																		"g",
																		{
																			className: "group",
																			children: [
																				(0, s.jsx)("rect", {
																					x: e.x,
																					y: 180 - e.height,
																					width: e.width,
																					height: e.height,
																					fill: "#10b981",
																					className:
																						"hover:fill-green-600 transition-colors cursor-pointer",
																					children: (0, s.jsxs)("title", {
																						children: [
																							"$",
																							e.amount.toLocaleString(),
																							" - ",
																							e.date,
																						],
																					}),
																				}),
																				(0, s.jsxs)("text", {
																					x: e.x + e.width / 2,
																					y: 175 - e.height,
																					fill: "#065f46",
																					fontSize: "10",
																					textAnchor: "middle",
																					className:
																						"opacity-0 group-hover:opacity-100 transition-opacity",
																					children: ["$", e.amount],
																				}),
																			],
																		},
																		t,
																	),
																),
																(0, s.jsx)("text", {
																	x: "15",
																	y: "30",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "end",
																	children: "$2.5K",
																}),
																(0, s.jsx)("text", {
																	x: "15",
																	y: "80",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "end",
																	children: "$2K",
																}),
																(0, s.jsx)("text", {
																	x: "15",
																	y: "130",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "end",
																	children: "$1K",
																}),
																(0, s.jsx)("text", {
																	x: "15",
																	y: "180",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "end",
																	children: "$0",
																}),
																(0, s.jsx)("text", {
																	x: "50",
																	y: "195",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "middle",
																	children: "Jan",
																}),
																(0, s.jsx)("text", {
																	x: "130",
																	y: "195",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "middle",
																	children: "Mar",
																}),
																(0, s.jsx)("text", {
																	x: "210",
																	y: "195",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "middle",
																	children: "May",
																}),
																(0, s.jsx)("text", {
																	x: "290",
																	y: "195",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "middle",
																	children: "Jul",
																}),
																(0, s.jsx)("text", {
																	x: "370",
																	y: "195",
																	fill: "#9ca3af",
																	fontSize: "10",
																	textAnchor: "middle",
																	children: "Sep",
																}),
															],
														}),
													})
												: (0, s.jsx)("div", {
														className: `grid grid-cols-${r} gap-8`,
														children: i.map((e, t) =>
															(0, s.jsxs)(
																"div",
																{
																	children: [
																		(0, s.jsx)("div", {
																			className:
																				"text-center text-gray-400 text-xs mb-4 font-light tracking-wide",
																			children: e,
																		}),
																		(0, s.jsx)("div", {
																			className: "grid grid-cols-7 gap-1 mb-3",
																			children: [
																				"Sun",
																				"Mon",
																				"Tue",
																				"Wed",
																				"Thu",
																				"Fri",
																				"Sat",
																			].map((e) =>
																				(0, s.jsx)(
																					"div",
																					{
																						className:
																							"text-center text-gray-500 text-xs font-light tracking-wide",
																						children: e,
																					},
																					e,
																				),
																			),
																		}),
																		(0, s.jsx)("div", {
																			className: "grid grid-cols-7 gap-1",
																			children: [...Array(31)].map((t, r) => {
																				const i = Math.random() > 0.7;
																				return (0, s.jsx)(
																					"div",
																					{
																						className: `w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ${i ? "bg-red-900 hover:bg-red-800 text-red-200" : "bg-green-900 hover:bg-green-800 text-green-200"} cursor-pointer transition-colors`,
																						title: `${e} ${r + 1}: ${i ? "Booked" : "Available"}`,
																						children: r + 1,
																					},
																					`${e}-day-${r + 1}`,
																				);
																			}),
																		}),
																	],
																},
																e,
															),
														),
													}),
										],
									}),
									(0, s.jsx)("div", {
										className: "text-center",
										children: (0, s.jsx)(l.$, {
											onClick: t,
											className:
												"bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide",
											children: "Send Booking Offer",
										}),
									}),
								],
							}),
						});
					};
				var d = r(22752);
				const x = (0, d.A)("ArrowRight", [
						["path", { d: "M5 12h14", key: "1ays0h" }],
						["path", { d: "m12 5 7 7-7 7", key: "xquz4c" }],
					]),
					g = (0, d.A)("ArrowDown", [
						["path", { d: "M12 5v14", key: "s699le" }],
						["path", { d: "m19 12-7 7-7-7", key: "1idqje" }],
					]),
					h = (0, d.A)("Maximize2", [
						["polyline", { points: "15 3 21 3 21 9", key: "mznyad" }],
						["polyline", { points: "9 21 3 21 3 15", key: "1avn1i" }],
						["line", { x1: "21", x2: "14", y1: "3", y2: "10", key: "ota7mn" }],
						["line", { x1: "3", x2: "10", y1: "21", y2: "14", key: "1atl0r" }],
					]),
					p = ({
						activeTab: e,
						onTabChange: t,
						photoCount: r,
						assignmentCount: i,
						totalIncomeFormatted: a,
						archiveCount: o,
						isSelectionMode: n,
						onToggleSelectionMode: c,
						onMergeTiles: d,
						onClearSelection: p,
						onConvertToText: u,
						onConvertToImage: m,
						selectedTilesCount: f,
					}) =>
						(0, s.jsx)("div", {
							className: "border-b border-gray-100",
							children: (0, s.jsxs)("div", {
								className: "max-w-4xl mx-auto px-6 py-8",
								children: [
									(0, s.jsxs)("div", {
										className: "flex items-center justify-center gap-12 mb-8",
										children: [
											(0, s.jsxs)("button", {
												onClick: () => t("photos"),
												className: `text-sm font-light tracking-wide ${"photos" === e ? "text-black" : "text-gray-400"}`,
												children: ["Work (", r, ")"],
											}),
											(0, s.jsxs)("button", {
												onClick: () => t("assignments"),
												className: `text-sm font-light tracking-wide ${"assignments" === e ? "text-black" : "text-gray-400"}`,
												children: ["Assignments (", i, ")"],
											}),
											(0, s.jsxs)("button", {
												onClick: () => t("income"),
												className: `text-sm font-light tracking-wide ${"income" === e ? "text-black" : "text-gray-400"}`,
												children: ["Income (", a, ")"],
											}),
											(0, s.jsxs)("button", {
												onClick: () => t("archive"),
												className: `text-sm font-light tracking-wide ${"archive" === e ? "text-black" : "text-gray-400"}`,
												children: ["Archive (", o, ")"],
											}),
										],
									}),
									"photos" === e &&
										c &&
										d &&
										p &&
										u &&
										m &&
										(0, s.jsxs)("div", {
											className: "flex items-center justify-center gap-4",
											children: [
												(0, s.jsx)(l.$, {
													variant: n ? "default" : "outline",
													size: "sm",
													onClick: c,
													className: "text-xs font-light tracking-wide",
													children: n ? "Exit Selection" : "Select Tiles",
												}),
												n &&
													(0, s.jsxs)(s.Fragment, {
														children: [
															(0, s.jsxs)(l.$, {
																variant: "outline",
																size: "sm",
																onClick: () => d("horizontal"),
																disabled: 2 > (f || 0),
																className: "text-xs font-light tracking-wide",
																children: [
																	(0, s.jsx)(x, { className: "w-3 h-3 mr-1" }),
																	"Horizontal",
																],
															}),
															(0, s.jsxs)(l.$, {
																variant: "outline",
																size: "sm",
																onClick: () => d("vertical"),
																disabled: 2 > (f || 0),
																className: "text-xs font-light tracking-wide",
																children: [
																	(0, s.jsx)(g, { className: "w-3 h-3 mr-1" }),
																	"Vertical",
																],
															}),
															(f || 0) >= 4 &&
																(0, s.jsxs)(l.$, {
																	variant: "outline",
																	size: "sm",
																	onClick: () => d("large"),
																	className: "text-xs font-light tracking-wide",
																	children: [
																		(0, s.jsx)(h, {
																			className: "w-3 h-3 mr-1",
																		}),
																		"Large",
																	],
																}),
															(0, s.jsx)(l.$, {
																variant: "outline",
																size: "sm",
																onClick: p,
																disabled: 0 === (f || 0),
																className: "text-xs font-light tracking-wide",
																children: "Clear",
															}),
															(0, s.jsx)(l.$, {
																variant: "outline",
																size: "sm",
																onClick: u,
																disabled: 0 === (f || 0),
																className: "text-xs font-light tracking-wide",
																children: "Convert to Text",
															}),
															(0, s.jsx)(l.$, {
																variant: "outline",
																size: "sm",
																onClick: m,
																disabled: 0 === (f || 0),
																className: "text-xs font-light tracking-wide",
																children: "Convert to Image",
															}),
														],
													}),
											],
										}),
								],
							}),
						});
				var u = r(3704),
					m = r(21345);
				const f = (e) => {
						switch (e) {
							case "horizontal":
								return "col-span-2 row-span-1";
							case "vertical":
								return "col-span-1 row-span-2";
							case "large":
								return "col-span-2 row-span-2";
							default:
								return "col-span-1 row-span-1";
						}
					},
					y = ({
						photos: e,
						selectedTiles: t,
						isSelectionMode: r,
						editingTile: i,
						tempText: a,
						onOpenPhoto: o,
						onToggleTileSelection: l,
						onStartEditingText: n,
						onSetTempText: c,
						onSaveTextEdit: d,
						onCancelTextEdit: x,
						splitTile: g,
						tempBackgroundColor: h,
						onSetTempBackgroundColor: p,
						tempFontSize: y,
						onSetTempFontSize: b,
						tempFontFamily: j,
						onSetTempFontFamily: v,
					}) => {
						const k = [
								"Arial",
								"Verdana",
								"Georgia",
								"Times New Roman",
								"Courier New",
							],
							w = ["12px", "16px", "20px", "24px", "32px"];
						return (0, s.jsx)("div", {
							className: "grid grid-cols-3 gap-2 auto-rows-[200px]",
							children: e.map((e) =>
								(0, s.jsxs)(
									"div",
									{
										className: `relative group cursor-pointer ${f(e.size)} ${t.has(e.id) ? "ring-2 ring-black" : ""} ${r ? "hover:ring-1 hover:ring-gray-400" : ""}`,
										onClick: () => {
											if ("text" === e.type && i !== e.id && !r) {
												n(e.id, e);
												return;
											}
											return r ? l(e.id) : o(e);
										},
										children: [
											"text" === e.type
												? (0, s.jsx)("div", {
														className:
															"w-full h-full flex items-center justify-center p-4 text-center relative",
														style: {
															backgroundColor:
																i === e.id ? h : e.backgroundColor || "#f3f4f6",
														},
														children:
															i === e.id
																? (0, s.jsxs)("div", {
																		className: "w-full h-full flex flex-col",
																		onClick: (e) => e.stopPropagation(),
																		children: [
																			(0, s.jsx)("textarea", {
																				value: a,
																				onChange: (e) => c(e.target.value),
																				className:
																					"flex-1 w-full p-2 border-none outline-none resize-none bg-transparent text-center font-light",
																				placeholder: "Enter your text...",
																				autoFocus: !0,
																				onClick: (e) => e.stopPropagation(),
																				style: {
																					backgroundColor: h,
																					fontSize: y,
																					fontFamily: j,
																					color: "#000000",
																				},
																			}),
																			(0, s.jsxs)("div", {
																				className:
																					"grid grid-cols-3 gap-2 mt-2",
																				children: [
																					(0, s.jsxs)("div", {
																						children: [
																							(0, s.jsx)("label", {
																								htmlFor: `bgColor-${e.id}`,
																								className:
																									"block text-xs font-light text-gray-600 mb-1",
																								children: "BG",
																							}),
																							(0, s.jsx)("input", {
																								type: "color",
																								id: `bgColor-${e.id}`,
																								value: h,
																								onChange: (e) =>
																									p(e.target.value),
																								className:
																									"w-full h-8 p-0 border-none rounded cursor-pointer",
																							}),
																						],
																					}),
																					(0, s.jsxs)("div", {
																						children: [
																							(0, s.jsx)("label", {
																								htmlFor: `fontSize-${e.id}`,
																								className:
																									"block text-xs font-light text-gray-600 mb-1",
																								children: "Size",
																							}),
																							(0, s.jsx)("select", {
																								id: `fontSize-${e.id}`,
																								value: y,
																								onChange: (e) =>
																									b(e.target.value),
																								className:
																									"w-full p-1 border border-gray-300 rounded text-xs font-light h-8",
																								children: w.map((e) =>
																									(0, s.jsx)(
																										"option",
																										{ value: e, children: e },
																										e,
																									),
																								),
																							}),
																						],
																					}),
																					(0, s.jsxs)("div", {
																						children: [
																							(0, s.jsx)("label", {
																								htmlFor: `fontFamily-${e.id}`,
																								className:
																									"block text-xs font-light text-gray-600 mb-1",
																								children: "Font",
																							}),
																							(0, s.jsx)("select", {
																								id: `fontFamily-${e.id}`,
																								value: j,
																								onChange: (e) =>
																									v(e.target.value),
																								className:
																									"w-full p-1 border border-gray-300 rounded text-xs font-light h-8 truncate",
																								children: k.map((e) =>
																									(0, s.jsx)(
																										"option",
																										{ value: e, children: e },
																										e,
																									),
																								),
																							}),
																						],
																					}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				className: "flex gap-1 mt-2",
																				children: [
																					(0, s.jsx)("button", {
																						onClick: (e) => {
																							e.stopPropagation(), d();
																						},
																						className:
																							"px-2 py-1 bg-black text-white text-xs rounded font-light",
																						children: "Save",
																					}),
																					(0, s.jsx)("button", {
																						onClick: (e) => {
																							e.stopPropagation(), x();
																						},
																						className:
																							"px-2 py-1 bg-gray-500 text-white text-xs rounded font-light",
																						children: "Cancel",
																					}),
																				],
																			}),
																		],
																	})
																: (0, s.jsx)("p", {
																		className:
																			"text-gray-800 font-light break-words cursor-text z-20 relative whitespace-pre-line",
																		style: {
																			backgroundColor:
																				e.backgroundColor || "#f3f4f6",
																			fontSize: e.fontSize || "16px",
																			fontFamily: e.fontFamily || "Arial",
																			color: "#000000",
																		},
																		onClick: (t) => {
																			t.stopPropagation(), n(e.id, e);
																		},
																		children:
																			e.textContent || "Click to edit text",
																	}),
													})
												: (0, s.jsx)(u.default, {
														src: e.src || "/placeholder.svg",
														alt: e.alt,
														fill: !0,
														className: "object-cover",
														sizes:
															"(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw",
													}),
											(0, s.jsxs)("div", {
												className: "absolute top-2 left-2 flex gap-1 z-20",
												children: [
													e.isMerged &&
														(0, s.jsx)("div", {
															className:
																"bg-black text-white rounded-full p-1 cursor-pointer",
															onClick: (t) => {
																t.stopPropagation(), g(e);
															},
															children: (0, s.jsx)(m.A, {
																className: "w-3 h-3",
															}),
														}),
													"text" === e.type &&
														r &&
														(0, s.jsx)("div", {
															className: "bg-black text-white rounded-full p-1",
															title: "Text Tile",
															children: (0, s.jsx)("span", {
																className: "text-xs font-light",
																children: "T",
															}),
														}),
												],
											}),
										],
									},
									e.id,
								),
							),
						});
					},
					b = ({
						viewMode: e,
						onViewModeChange: t,
						jobs: r,
						receivedOffers: i,
						sentOffers: a,
					}) => {
						let o = "Upcoming Assignments",
							n = r;
						"receivedOffers" === e
							? ((o = "Offers Received"), (n = i))
							: "sentOffers" === e && ((o = "Offers Sent"), (n = a));
						const c = (e) => {
							switch (e) {
								case "confirmed":
								case "accepted":
									return "bg-green-100 text-green-800";
								case "pending":
								case "negotiating":
									return "bg-yellow-100 text-yellow-800";
								case "declined":
								case "expired":
								case "withdrawn":
									return "bg-red-100 text-red-800";
								case "completed":
									return "bg-blue-100 text-blue-800";
								default:
									return "bg-gray-100 text-gray-800";
							}
						};
						return (0, s.jsxs)("div", {
							className: "space-y-6",
							children: [
								(0, s.jsxs)("div", {
									className: "flex justify-center gap-4 mb-8",
									children: [
										(0, s.jsxs)(l.$, {
											variant: "upcoming" === e ? "default" : "outline",
											onClick: () => t("upcoming"),
											className: "font-light tracking-wide",
											children: ["Upcoming Assignments (", r.length, ")"],
										}),
										(0, s.jsxs)(l.$, {
											variant: "receivedOffers" === e ? "default" : "outline",
											onClick: () => t("receivedOffers"),
											className: "font-light tracking-wide",
											children: ["Offers Received (", i.length, ")"],
										}),
										(0, s.jsxs)(l.$, {
											variant: "sentOffers" === e ? "default" : "outline",
											onClick: () => t("sentOffers"),
											className: "font-light tracking-wide",
											children: ["Offers Sent (", a.length, ")"],
										}),
									],
								}),
								(0, s.jsx)("h2", {
									className:
										"text-xl font-light tracking-wide text-center mb-12",
									children: o,
								}),
								0 === n.length
									? (0, s.jsx)("p", {
											className: "text-center text-gray-500 font-light",
											children: "No items to display in this view.",
										})
									: n.map((e) =>
											"time" in e
												? (0, s.jsx)(
														"div",
														{
															className:
																"border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg",
															children: (0, s.jsxs)("div", {
																className: "flex items-start justify-between",
																children: [
																	(0, s.jsxs)("div", {
																		className: "flex-1",
																		children: [
																			(0, s.jsx)("h3", {
																				className:
																					"font-light tracking-wide text-lg mb-2",
																				children: e.title,
																			}),
																			(0, s.jsxs)("p", {
																				className:
																					"text-gray-600 mb-1 font-light tracking-wide",
																				children: ["Client: ", e.client],
																			}),
																			(0, s.jsxs)("div", {
																				className:
																					"flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2",
																				children: [
																					(0, s.jsxs)("span", {
																						children: ["Date: ", e.date],
																					}),
																					(0, s.jsxs)("span", {
																						children: ["Time: ", e.time],
																					}),
																					(0, s.jsxs)("span", {
																						children: [
																							"Location: ",
																							e.location,
																						],
																					}),
																					(0, s.jsxs)("span", {
																						children: ["Type: ", e.type],
																					}),
																				],
																			}),
																		],
																	}),
																	(0, s.jsx)("div", {
																		className: "text-right ml-4 flex-shrink-0",
																		children: (0, s.jsx)("span", {
																			className: `px-3 py-1 text-xs font-light tracking-wide rounded-full ${c(e.status)}`,
																			children:
																				e.status.charAt(0).toUpperCase() +
																				e.status.slice(1),
																		}),
																	}),
																],
															}),
														},
														e.id,
													)
												: (0, s.jsx)(
														"div",
														{
															className:
																"border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg",
															children: (0, s.jsxs)("div", {
																className: "flex items-start justify-between",
																children: [
																	(0, s.jsxs)("div", {
																		className: "flex-1",
																		children: [
																			(0, s.jsx)("h3", {
																				className:
																					"font-light tracking-wide text-lg mb-2",
																				children: e.projectTitle,
																			}),
																			(0, s.jsxs)("p", {
																				className:
																					"text-gray-600 mb-1 font-light tracking-wide",
																				children: [
																					e.isSentByMe ? "To: " : "From: ",
																					" ",
																					e.clientName,
																				],
																			}),
																			(0, s.jsxs)("p", {
																				className:
																					"text-gray-600 mb-1 font-light tracking-wide",
																				children: ["Role: ", e.role],
																			}),
																			(0, s.jsxs)("p", {
																				className:
																					"text-gray-600 mb-1 font-light tracking-wide",
																				children: [
																					"Amount: $",
																					e.amount.toLocaleString(),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				className:
																					"flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2",
																				children: [
																					(0, s.jsxs)("span", {
																						children: [
																							"Offered: ",
																							e.offerDate,
																						],
																					}),
																					(0, s.jsxs)("span", {
																						children: ["Type: ", e.projectType],
																					}),
																					e.location &&
																						(0, s.jsxs)("span", {
																							children: [
																								"Location: ",
																								e.location,
																							],
																						}),
																				],
																			}),
																			e.description &&
																				(0, s.jsxs)("p", {
																					className:
																						"text-xs text-gray-500 mt-2 font-light italic",
																					children: ["Note: ", e.description],
																				}),
																		],
																	}),
																	(0, s.jsx)("div", {
																		className: "text-right ml-4 flex-shrink-0",
																		children: (0, s.jsx)("span", {
																			className: `px-3 py-1 text-xs font-light tracking-wide rounded-full ${c(e.status)}`,
																			children:
																				e.status.charAt(0).toUpperCase() +
																				e.status.slice(1),
																		}),
																	}),
																],
															}),
														},
														e.id,
													),
										),
							],
						});
					},
					j = ({ payments: e, totalIncome: t, onViewInvoice: r }) =>
						(0, s.jsxs)("div", {
							className: "space-y-8",
							children: [
								(0, s.jsxs)("div", {
									className: "text-center mb-12",
									children: [
										(0, s.jsx)("h2", {
											className: "text-xl font-light tracking-wide mb-6",
											children: "Total Income",
										}),
										(0, s.jsxs)("div", {
											className: "text-4xl font-light text-green-600 mb-3",
											children: ["$", t.toLocaleString()],
										}),
										(0, s.jsx)("p", {
											className: "text-gray-600 font-light tracking-wide",
											children: "Lifetime Earnings",
										}),
									],
								}),
								(0, s.jsx)("div", {
									className:
										"overflow-x-auto overflow-y-auto max-h-96 border border-gray-100 rounded",
									children: (0, s.jsxs)("table", {
										className: "w-full",
										children: [
											(0, s.jsx)("thead", {
												className: "bg-gray-50 sticky top-0",
												children: (0, s.jsxs)("tr", {
													children: [
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Job",
														}),
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Client",
														}),
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Amount",
														}),
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Date",
														}),
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Status",
														}),
														(0, s.jsx)("th", {
															className:
																"px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide",
															children: "Invoice",
														}),
													],
												}),
											}),
											(0, s.jsx)("tbody", {
												className: "bg-white divide-y divide-gray-100",
												children: e.map((e) =>
													(0, s.jsxs)(
														"tr",
														{
															className: "hover:bg-gray-50",
															children: [
																(0, s.jsx)("td", {
																	className:
																		"px-6 py-4 whitespace-nowrap text-sm font-light text-gray-900",
																	children: e.jobTitle,
																}),
																(0, s.jsx)("td", {
																	className:
																		"px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light",
																	children: e.client,
																}),
																(0, s.jsxs)("td", {
																	className:
																		"px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-light",
																	children: ["$", e.amount.toLocaleString()],
																}),
																(0, s.jsx)("td", {
																	className:
																		"px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light",
																	children: e.date,
																}),
																(0, s.jsx)("td", {
																	className: "px-6 py-4 whitespace-nowrap",
																	children: (0, s.jsx)("span", {
																		className: `px-2 py-1 text-xs font-light tracking-wide rounded-full ${"paid" === e.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`,
																		children:
																			e.status.charAt(0).toUpperCase() +
																			e.status.slice(1),
																	}),
																}),
																(0, s.jsx)("td", {
																	className:
																		"px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-light",
																	children: (0, s.jsx)("button", {
																		onClick: () => r(e.invoiceNumber),
																		className: "hover:text-blue-800 underline",
																		children: e.invoiceNumber,
																	}),
																}),
															],
														},
														e.id,
													),
												),
											}),
										],
									}),
								}),
							],
						}),
					v = ({ payments: e }) =>
						(0, s.jsxs)("div", {
							className: "space-y-6",
							children: [
								(0, s.jsx)("h2", {
									className:
										"text-xl font-light tracking-wide text-center mb-12",
									children: "Completed Work",
								}),
								e.map((e) =>
									(0, s.jsx)(
										"div",
										{
											className:
												"border border-gray-100 p-6 hover:shadow-sm transition-shadow",
											children: (0, s.jsxs)("div", {
												className: "flex items-start justify-between",
												children: [
													(0, s.jsxs)("div", {
														className: "flex-1",
														children: [
															(0, s.jsx)("h3", {
																className:
																	"font-light tracking-wide text-lg mb-2",
																children: e.jobTitle,
															}),
															(0, s.jsxs)("p", {
																className:
																	"text-gray-600 mb-2 font-light tracking-wide",
																children: ["Client: ", e.client],
															}),
															(0, s.jsx)("div", {
																className:
																	"text-sm text-gray-500 font-light tracking-wide",
																children: e.date,
															}),
														],
													}),
													(0, s.jsx)("div", {
														className: "text-right",
														children: (0, s.jsx)("span", {
															className: `px-3 py-1 text-xs font-light tracking-wide ${"paid" === e.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`,
															children:
																e.status.charAt(0).toUpperCase() +
																e.status.slice(1),
														}),
													}),
												],
											}),
										},
										e.id,
									),
								),
							],
						});
				var k = r(46909);
				const w = ({
						show: e,
						onClose: t,
						bookingForm: r,
						onFormChange: i,
						onFormProjectSelect: a,
						onSubmit: o,
						projects: n,
						isSubmitting: c,
					}) =>
						e
							? (0, s.jsx)("div", {
									className:
										"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
									children: (0, s.jsxs)("div", {
										className:
											"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
										children: [
											(0, s.jsxs)("div", {
												className:
													"flex items-center justify-between p-6 border-b border-gray-100",
												children: [
													(0, s.jsx)("h2", {
														className: "text-xl font-light tracking-wide",
														children: "Send Booking Offer",
													}),
													(0, s.jsx)("button", {
														onClick: t,
														children: (0, s.jsx)(k.A, { className: "w-5 h-5" }),
													}),
												],
											}),
											(0, s.jsxs)("form", {
												onSubmit: o,
												className:
													"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
												children: [
													(0, s.jsxs)("div", {
														className: "space-y-6",
														children: [
															(0, s.jsxs)("div", {
																children: [
																	(0, s.jsx)("h3", {
																		className:
																			"text-lg font-light tracking-wide mb-4",
																		children: "Select Project",
																	}),
																	(0, s.jsxs)("div", {
																		className: "mb-4",
																		children: [
																			(0, s.jsx)("label", {
																				className:
																					"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																				children: "Project *",
																			}),
																			(0, s.jsxs)("select", {
																				required: !0,
																				value: r.projectId || "",
																				onChange: (e) => {
																					a(
																						n.find(
																							(t) => t.id === e.target.value,
																						),
																					);
																				},
																				className:
																					"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																				children: [
																					(0, s.jsx)("option", {
																						value: "",
																						children: "Select a project",
																					}),
																					n.map((e) =>
																						(0, s.jsxs)(
																							"option",
																							{
																								value: e.id,
																								children: [
																									e.title,
																									" - ",
																									e.client,
																								],
																							},
																							e.id,
																						),
																					),
																				],
																			}),
																		],
																	}),
																],
															}),
															r.projectId &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-lg font-light tracking-wide mb-4",
																			children: "Project Details",
																		}),
																		(0, s.jsxs)("div", {
																			className:
																				"grid grid-cols-1 md:grid-cols-2 gap-4",
																			children: [
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsx)("label", {
																							className:
																								"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																							children: "Project Title",
																						}),
																						(0, s.jsx)("input", {
																							type: "text",
																							value: r.projectTitle,
																							readOnly: !0,
																							className:
																								"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																						}),
																					],
																				}),
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsx)("label", {
																							className:
																								"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																							children: "Project Type",
																						}),
																						(0, s.jsx)("input", {
																							type: "text",
																							value: r.projectType,
																							readOnly: !0,
																							className:
																								"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																						}),
																					],
																				}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className:
																				"grid grid-cols-1 md:grid-cols-3 gap-4 mt-4",
																			children: [
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsx)("label", {
																							className:
																								"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																							children: "Start Date",
																						}),
																						(0, s.jsx)("input", {
																							type: "text",
																							value: r.startDate,
																							readOnly: !0,
																							className:
																								"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																						}),
																					],
																				}),
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsx)("label", {
																							className:
																								"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																							children: "End Date",
																						}),
																						(0, s.jsx)("input", {
																							type: "text",
																							value: r.endDate,
																							readOnly: !0,
																							className:
																								"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																						}),
																					],
																				}),
																				(0, s.jsxs)("div", {
																					children: [
																						(0, s.jsx)("label", {
																							className:
																								"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																							children: "Budget (USD)",
																						}),
																						(0, s.jsx)("input", {
																							type: "text",
																							value: `$${Number.parseInt(r.budget || "0").toLocaleString()}`,
																							readOnly: !0,
																							className:
																								"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																						}),
																					],
																				}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className: "mt-4",
																			children: [
																				(0, s.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Location",
																				}),
																				(0, s.jsx)("input", {
																					type: "text",
																					value: r.location,
																					readOnly: !0,
																					className:
																						"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																				}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className: "mt-4",
																			children: [
																				(0, s.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Description",
																				}),
																				(0, s.jsx)("textarea", {
																					rows: 3,
																					value: r.description,
																					readOnly: !0,
																					className:
																						"w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light",
																				}),
																			],
																		}),
																	],
																}),
															r.projectId &&
																(0, s.jsxs)("div", {
																	children: [
																		(0, s.jsx)("h3", {
																			className:
																				"text-lg font-light tracking-wide mb-4",
																			children: "Offer Details",
																		}),
																		(0, s.jsxs)("div", {
																			className: "mb-4",
																			children: [
																				(0, s.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Role *",
																				}),
																				(0, s.jsxs)("select", {
																					required: !0,
																					value: r.role || "",
																					onChange: (e) =>
																						i("role", e.target.value),
																					className:
																						"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																					children: [
																						(0, s.jsx)("option", {
																							value: "",
																							children: "Select Role",
																						}),
																						n
																							.find((e) => e.id === r.projectId)
																							?.creativesNeeded?.map((e, t) =>
																								s.jsx(
																									"option",
																									{ value: e, children: e },
																									t,
																								),
																							) ||
																							(0, s.jsxs)(s.Fragment, {
																								children: [
																									(0, s.jsx)("option", {
																										value: "Photographer",
																										children: "Photographer",
																									}),
																									(0, s.jsx)("option", {
																										value: "Videographer",
																										children: "Videographer",
																									}),
																									(0, s.jsx)("option", {
																										value: "Model",
																										children: "Model",
																									}),
																									(0, s.jsx)("option", {
																										value: "Makeup Artist",
																										children: "Makeup Artist",
																									}),
																									(0, s.jsx)("option", {
																										value: "Hair Stylist",
																										children: "Hair Stylist",
																									}),
																									(0, s.jsx)("option", {
																										value: "Stylist",
																										children: "Stylist",
																									}),
																								],
																							}),
																					],
																				}),
																			],
																		}),
																		(0, s.jsxs)("div", {
																			className: "mt-4",
																			children: [
																				(0, s.jsx)("label", {
																					className:
																						"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																					children: "Additional Notes",
																				}),
																				(0, s.jsx)("textarea", {
																					rows: 3,
																					value: r.notes || "",
																					onChange: (e) =>
																						i("notes", e.target.value),
																					className:
																						"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																					placeholder:
																						"Any specific requirements or details for this creative...",
																				}),
																			],
																		}),
																	],
																}),
															(0, s.jsxs)("div", {
																children: [
																	" ",
																	(0, s.jsx)("h3", {
																		className:
																			"text-lg font-light tracking-wide mb-4",
																		children: "Rate Information",
																	}),
																	(0, s.jsxs)("div", {
																		className:
																			"grid grid-cols-1 md:grid-cols-2 gap-4",
																		children: [
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Proposed Rate *",
																					}),
																					(0, s.jsx)("input", {
																						type: "number",
																						required: !0,
																						value: r.proposedRate,
																						onChange: (e) =>
																							i("proposedRate", e.target.value),
																						className:
																							"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																						placeholder: "1500",
																					}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("label", {
																						className:
																							"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																						children: "Rate Type *",
																					}),
																					(0, s.jsxs)("select", {
																						required: !0,
																						value: r.rateType,
																						onChange: (e) =>
																							i("rateType", e.target.value),
																						className:
																							"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																						children: [
																							(0, s.jsx)("option", {
																								value: "",
																								children: "Select Rate Type",
																							}),
																							(0, s.jsx)("option", {
																								value: "hourly",
																								children: "Per Hour",
																							}),
																							(0, s.jsx)("option", {
																								value: "daily",
																								children: "Per Day",
																							}),
																							(0, s.jsx)("option", {
																								value: "project",
																								children: "Per Project",
																							}),
																							(0, s.jsx)("option", {
																								value: "weekly",
																								children: "Per Week",
																							}),
																							(0, s.jsx)("option", {
																								value: "monthly",
																								children: "Per Month",
																							}),
																						],
																					}),
																				],
																			}),
																		],
																	}),
																],
															}),
														],
													}),
													(0, s.jsxs)("div", {
														className:
															"flex gap-4 mt-8 pt-6 border-t border-gray-100",
														children: [
															(0, s.jsx)(l.$, {
																type: "button",
																variant: "outline",
																onClick: t,
																className: "flex-1 font-light tracking-wide",
																disabled: c,
																children: "Cancel",
															}),
															(0, s.jsxs)(l.$, {
																type: "submit",
																className:
																	"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
																disabled: !r.projectId || c,
																children: [
																	c ? "Sending..." : "Send Offer",
																	" ",
																],
															}),
														],
													}),
												],
											}),
										],
									}),
								})
							: null,
					N = (0, d.A)("Bookmark", [
						[
							"path",
							{
								d: "m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",
								key: "1fy3hk",
							},
						],
					]),
					C = ({ photo: e, onClose: t, likedPhotos: r, onToggleLike: i }) =>
						e
							? (0, s.jsx)("div", {
									className:
										"fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm",
									children: (0, s.jsxs)("div", {
										className:
											"bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row shadow-xl",
										children: [
											(0, s.jsx)("div", {
												className: "flex-1 relative min-h-[300px]",
												children: (0, s.jsx)(u.default, {
													src: e.src || "/placeholder.svg",
													alt: e.alt,
													fill: !0,
													className: "object-cover",
												}),
											}),
											(0, s.jsxs)("div", {
												className: "w-full md:w-80 flex flex-col",
												children: [
													(0, s.jsxs)("div", {
														className:
															"flex items-center justify-between p-4 border-b border-gray-100",
														children: [
															(0, s.jsx)("div", {
																className: "flex items-center gap-3",
																children: (0, s.jsx)("span", {
																	className: "font-light tracking-wide text-lg",
																	children: e.alt,
																}),
															}),
															(0, s.jsx)("button", {
																onClick: t,
																"aria-label": "Close modal",
																children: (0, s.jsx)(k.A, {
																	className: "w-5 h-5",
																}),
															}),
														],
													}),
													(0, s.jsx)("div", {
														className: "flex-1 p-4 overflow-y-auto",
													}),
													(0, s.jsx)("div", {
														className: "border-t border-gray-100 p-4",
														children: (0, s.jsx)("div", {
															className: "flex items-center justify-end mb-3",
															children: (0, s.jsx)(N, { className: "w-6 h-6" }),
														}),
													}),
												],
											}),
										],
									}),
								})
							: null,
					S = () => {
						const [e, t] = (0, i.useState)(new Set()),
							[r, s] = (0, i.useState)(!1),
							a = (0, i.useCallback)(
								(e) => {
									r &&
										t((t) => {
											const r = new Set(t);
											return r.has(e) ? r.delete(e) : r.add(e), r;
										});
								},
								[r],
							),
							o = (0, i.useCallback)(() => {
								t(new Set());
							}, []),
							l = (0, i.useCallback)(() => {
								s((e) => {
									const r = !e;
									return r || t(new Set()), r;
								});
							}, []);
						return {
							selectedTiles: e,
							isSelectionMode: r,
							selectedTilesCount: e.size,
							toggleTileSelection: a,
							clearSelection: o,
							toggleSelectionMode: l,
						};
					},
					T = ({ setPhotos: e }) => {
						const [t, r] = (0, i.useState)(null),
							[s, a] = (0, i.useState)(""),
							[o, l] = (0, i.useState)("#f3f4f6"),
							[n, c] = (0, i.useState)("16px"),
							[d, x] = (0, i.useState)("Arial"),
							g = (0, i.useCallback)((e, t) => {
								r(e),
									a(t.textContent || ""),
									l(t.backgroundColor || "#f3f4f6"),
									c(t.fontSize || "16px"),
									x(t.fontFamily || "Arial");
							}, []),
							h = (0, i.useCallback)((e) => {
								a(e);
							}, []),
							p = (0, i.useCallback)((e) => {
								l(e);
							}, []),
							u = (0, i.useCallback)((e) => {
								c(e);
							}, []),
							m = (0, i.useCallback)((e) => {
								x(e);
							}, []),
							f = (0, i.useCallback)(() => {
								t &&
									(e((e) =>
										e.map((e) =>
											e.id === t
												? {
														...e,
														textContent: s || "Empty text",
														backgroundColor: o,
														fontSize: n,
														fontFamily: d,
													}
												: e,
										),
									),
									r(null));
							}, [t, s, o, n, d, e]);
						return {
							editingTile: t,
							tempText: s,
							startEditingText: g,
							handleSetTempText: h,
							saveTextEdit: f,
							cancelTextEdit: (0, i.useCallback)(() => {
								r(null), a(""), l("#f3f4f6"), c("16px"), x("Arial");
							}, []),
							tempBackgroundColor: o,
							handleSetTempBackgroundColor: p,
							tempFontSize: n,
							handleSetTempFontSize: u,
							tempFontFamily: d,
							handleSetTempFontFamily: m,
						};
					},
					A = ({
						photos: e,
						setPhotos: t,
						selectedTiles: r,
						clearSelection: s,
						isSelectionMode: a,
						toggleSelectionMode: o,
					}) => {
						const l = (0, i.useCallback)(
								(i) => {
									let l, n, c, d;
									if (r.size < 2) {
										console.warn(
											"Merge attempt with fewer than 2 tiles selected.",
										);
										return;
									}
									const x = e
										.filter((e) => r.has(e.id))
										.sort((e, t) => (e.position || 0) - (t.position || 0));
									if (0 === x.length) {
										console.error(
											"Selected photos array is empty despite selectedTiles having items.",
										);
										return;
									}
									"large" === i &&
										(console.log("[Large Merge Debug] Initiating large merge."),
										console.log(
											"[Large Merge Debug] Selected tiles count (from selectedTiles.size):",
											r.size,
										),
										console.log(
											"[Large Merge Debug] Selected photos array length:",
											x.length,
										),
										console.log(
											"[Large Merge Debug] Selected photo IDs:",
											x.map((e) => e.id),
										),
										console.log(
											"[Large Merge Debug] Selected photo positions:",
											x.map((e) => e.position),
										));
									let g = x[0],
										h = x.find((e) => "image" === e.type || void 0 === e.type),
										p = "Merged content";
									h
										? ((l = "image"),
											(n = h.src),
											(p = h.alt),
											(c = void 0),
											(d = void 0))
										: ((l = "text"),
											(n = "/placeholder.svg?text=Merged+Text"),
											(p = "Merged text content"),
											(c =
												x.map((e) => e.textContent || "").join("\n") ||
												"Merged text"),
											(d = g.backgroundColor || "#f3f4f6")),
										"large" === i &&
											(console.log(
												"[Large Merge Debug] Leftmost overall photo ID for position:",
												g.id,
												"pos:",
												g.position,
											),
											console.log(
												"[Large Merge Debug] First potential image for content ID:",
												h ? h.id : "None",
											),
											console.log(
												"[Large Merge Debug] Determined mergedType:",
												l,
												"finalSrc:",
												n,
											));
									const u = g.position || 0,
										m = {
											id: `merged-${Date.now()}`,
											src: n,
											alt: p,
											likes: x.reduce((e, t) => e + (t.likes || 0), 0),
											comments: x.reduce((e, t) => e + (t.comments || 0), 0),
											size: i,
											isMerged: !0,
											originalPhotos: [...x],
											position: u,
											type: l,
											textContent: c,
											backgroundColor: d,
										};
									"large" === i &&
										console.log(
											"[Large Merge Debug] Created mergedPhoto object:",
											JSON.parse(JSON.stringify(m)),
										);
									const f = [...e.filter((e) => !r.has(e.id)), m].sort(
										(e, t) => (e.position || 0) - (t.position || 0),
									);
									"large" === i &&
										console.log(
											"[Large Merge Debug] New photos array length:",
											f.length,
										),
										t(f),
										s(),
										a && o(),
										"large" === i &&
											console.log(
												"[Large Merge Debug] Large merge process completed.",
											);
								},
								[e, r, t, s, a, o],
							),
							n = (0, i.useCallback)(
								(r) => {
									if (!r.isMerged || !r.originalPhotos) return;
									const s = r.position || 0;
									t(
										[
											...e.filter((e) => e.id !== r.id),
											...r.originalPhotos.map((e) => ({
												...e,
												position: null != e.position ? e.position : s,
												isMerged: !1,
												size: e.size || "standard",
												src: e.src,
											})),
										].sort((e, t) => (e.position || 0) - (t.position || 0)),
									);
								},
								[e, t],
							);
						return {
							mergeTiles: l,
							splitTile: n,
							convertToTextTile: (0, i.useCallback)(
								(e) => {
									t((t) =>
										t.map((t) =>
											t.id === e
												? {
														...t,
														type: "text",
														textContent: t.textContent || "Click to edit text",
														backgroundColor: t.backgroundColor || "#f3f4f6",
														src: "/placeholder.svg?text=Text+Content",
														fontSize: t.fontSize || "16px",
														fontFamily: t.fontFamily || "Arial",
													}
												: t,
										),
									);
								},
								[t],
							),
							convertToImageTile: (0, i.useCallback)(
								(e) => {
									t((t) =>
										t.map((t) => {
											if (t.id === e) {
												const e = t.originalPhotos?.find(
													(e) => "image" === e.type,
												)?.src;
												return {
													...t,
													type: "image",
													textContent: void 0,
													backgroundColor: void 0,
													src: t.src || e || "/placeholder.svg?text=Image",
												};
											}
											return t;
										}),
									);
								},
								[t],
							),
						};
					},
					M = {
						projectId: "",
						projectTitle: "",
						projectType: "",
						startDate: "",
						endDate: "",
						location: "",
						budget: "",
						description: "",
						role: "",
						proposedRate: "",
						rateType: "",
						notes: "",
					},
					P = () => {
						const [e, t] = (0, i.useState)(!1),
							[r, s] = (0, i.useState)(M),
							[a, o] = (0, i.useState)(!1),
							l = (0, i.useCallback)(() => {
								t(!0);
							}, []),
							n = (0, i.useCallback)(() => {
								t(!1), s(M);
							}, []),
							c = (0, i.useCallback)((e, t) => {
								s((r) => ({ ...r, [e]: t }));
							}, []),
							d = (0, i.useCallback)((e) => {
								e
									? s({
											...M,
											projectId: e.id,
											projectTitle: e.title,
											projectType: e.projectType || "",
											startDate: e.startDate || "",
											endDate: e.endDate || "",
											location: e.location || "",
											budget: e.budget.toString(),
											description: e.description || "",
										})
									: s(M);
							}, []),
							x = (0, i.useCallback)(
								(e) => {
									e.preventDefault(),
										a ||
											(o(!0),
											setTimeout(() => {
												console.log(
													"Booking offer submitted (simulated API call finished):",
													r,
												),
													n(),
													o(!1);
											}, 1500));
								},
								[r, n, a],
							);
						return {
							showBookingModal: e,
							bookingForm: r,
							isSubmitting: a,
							openBookingModal: l,
							closeBookingModal: n,
							updateBookingFormField: c,
							handleBookingFormProjectSelect: d,
							handleBookingSubmit: x,
						};
					},
					D = ({ initialLikedPhotos: e, onToggleLike: t }) => {
						const [r, s] = (0, i.useState)(null);
						return {
							selectedPhoto: r,
							openPhotoDetail: (0, i.useCallback)((e) => {
								s(e);
							}, []),
							closePhotoDetail: (0, i.useCallback)(() => {
								s(null);
							}, []),
						};
					};
				var z = r(1437);
				function q({ artistId: e = "1" }) {
					const [t, r] = (0, i.useState)(a.qD),
						[l, n] = (0, i.useState)(new Set()),
						[d, x] = (0, i.useState)("photos"),
						[g, h] = (0, i.useState)(null),
						[u, m] = (0, i.useState)("upcoming"),
						{
							selectedTiles: f,
							isSelectionMode: k,
							selectedTilesCount: N,
							toggleTileSelection: M,
							clearSelection: q,
							toggleSelectionMode: _,
						} = S(),
						{
							editingTile: $,
							tempText: I,
							startEditingText: O,
							handleSetTempText: L,
							saveTextEdit: F,
							cancelTextEdit: E,
							tempBackgroundColor: B,
							handleSetTempBackgroundColor: R,
							tempFontSize: V,
							handleSetTempFontSize: U,
							tempFontFamily: J,
							handleSetTempFontFamily: G,
						} = T({ setPhotos: r }),
						{
							mergeTiles: H,
							splitTile: W,
							convertToTextTile: K,
							convertToImageTile: X,
						} = A({
							photos: t,
							setPhotos: r,
							selectedTiles: f,
							clearSelection: q,
							isSelectionMode: k,
							toggleSelectionMode: _,
						}),
						{
							showBookingModal: Z,
							bookingForm: Y,
							openBookingModal: Q,
							closeBookingModal: ee,
							updateBookingFormField: et,
							handleBookingFormProjectSelect: er,
							handleBookingSubmit: es,
							isSubmitting: ei,
						} = P(),
						ea = a.KT.reduce((e, t) => e + t.amount, 0),
						eo = (e) => {
							n((t) => {
								const r = new Set(t);
								return r.has(e) ? r.delete(e) : r.add(e), r;
							});
						},
						{
							selectedPhoto: el,
							openPhotoDetail: en,
							closePhotoDetail: ec,
						} = D({ initialLikedPhotos: l, onToggleLike: eo }),
						ed = (0, i.useCallback)((e) => {
							h(e);
						}, []);
					(0, i.useCallback)(() => {
						h(null);
					}, []);
					const ex = a.xk[e] || a.xk["1"];
					return (0, s.jsxs)("div", {
						className: "min-h-screen bg-white font-light",
						children: [
							(0, s.jsx)(z.A, {}),
							(0, s.jsx)(o, { creative: ex, photoCount: t.length }),
							(0, s.jsx)(c, { activeTab: d, onOpenBookingModal: Q }),
							(0, s.jsx)(p, {
								activeTab: d,
								onTabChange: x,
								photoCount: t.length,
								assignmentCount: a.Tg.length,
								totalIncomeFormatted: `$${ea.toLocaleString()}`,
								archiveCount: a.KT.length,
								isSelectionMode: k,
								onToggleSelectionMode: _,
								onMergeTiles: H,
								onClearSelection: q,
								onConvertToText: () => {
									Array.from(f).forEach((e) => K(e)), q();
								},
								onConvertToImage: () => {
									Array.from(f).forEach((e) => X(e)), q();
								},
								selectedTilesCount: N,
							}),
							(0, s.jsxs)("div", {
								className: "max-w-4xl mx-auto px-6 py-16",
								children: [
									"photos" === d &&
										(0, s.jsx)(y, {
											photos: t,
											selectedTiles: f,
											isSelectionMode: k,
											editingTile: $,
											tempText: I,
											onOpenPhoto: (e) => {
												k || en(e);
											},
											onToggleTileSelection: M,
											onStartEditingText: O,
											onSetTempText: L,
											onSaveTextEdit: F,
											onCancelTextEdit: E,
											splitTile: W,
											tempBackgroundColor: B,
											onSetTempBackgroundColor: R,
											tempFontSize: V,
											onSetTempFontSize: U,
											tempFontFamily: J,
											onSetTempFontFamily: G,
										}),
									"assignments" === d &&
										(0, s.jsx)(b, {
											viewMode: u,
											onViewModeChange: m,
											jobs: a.Tg,
											receivedOffers: a.TZ,
											sentOffers: a.Wm,
										}),
									"income" === d &&
										(0, s.jsx)(j, {
											payments: a.KT,
											totalIncome: ea,
											onViewInvoice: ed,
										}),
									"archive" === d && (0, s.jsx)(v, { payments: a.KT }),
								],
							}),
							(0, s.jsx)(w, {
								show: Z,
								onClose: ee,
								bookingForm: Y,
								onFormChange: et,
								onFormProjectSelect: er,
								onSubmit: es,
								projects: a.R4,
								isSubmitting: ei,
							}),
							el &&
								(0, s.jsx)(C, {
									photo: el,
									onClose: ec,
									likedPhotos: l,
									onToggleLike: eo,
								}),
						],
					});
				}
			},
			72902: (e, t, r) => {
				r.d(t, { A: () => s });
				const s = (0, r(22752).A)("Search", [
					["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
					["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
				]);
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			74423: (e, t, r) => {
				r.r(t),
					r.d(t, {
						GlobalError: () => o.a,
						__next_app__: () => x,
						pages: () => d,
						routeModule: () => g,
						tree: () => c,
					});
				var s = r(7025),
					i = r(18198),
					a = r(82576),
					o = r.n(a),
					l = r(45239),
					n = {};
				for (const e in l)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (n[e] = () => l[e]);
				r.d(t, n);
				const c = {
						children: [
							"",
							{
								children: [
									"creative",
									{
										children: [
											"[id]",
											{
												children: [
													"__PAGE__",
													{},
													{
														page: [
															() => Promise.resolve().then(r.bind(r, 7923)),
															"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/creative/[id]/page.tsx",
														],
													},
												],
											},
											{},
										],
									},
									{},
								],
							},
							{
								layout: [
									() => Promise.resolve().then(r.bind(r, 59650)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
								],
								loading: [
									() => Promise.resolve().then(r.bind(r, 34314)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
								],
								"not-found": [
									() => Promise.resolve().then(r.t.bind(r, 4540, 23)),
									"next/dist/client/components/not-found-error",
								],
								forbidden: [
									() => Promise.resolve().then(r.t.bind(r, 53117, 23)),
									"next/dist/client/components/forbidden-error",
								],
								unauthorized: [
									() => Promise.resolve().then(r.t.bind(r, 6874, 23)),
									"next/dist/client/components/unauthorized-error",
								],
							},
						],
					}.children,
					d = [
						"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/creative/[id]/page.tsx",
					],
					x = { require: r, loadChunk: () => Promise.resolve() },
					g = new s.AppPageRouteModule({
						definition: {
							kind: i.RouteKind.APP_PAGE,
							page: "/creative/[id]/page",
							pathname: "/creative/[id]",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: c },
					});
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77401: (e, t, r) => {
				r.d(t, { cn: () => a });
				var s = r(42366),
					i = r(73927);
				function a(...e) {
					return (0, i.QP)((0, s.$)(e));
				}
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			81630: (e) => {
				e.exports = require("http");
			},
			91645: (e) => {
				e.exports = require("net");
			},
			94735: (e) => {
				e.exports = require("events");
			},
		});
	var t = require("../../../webpack-runtime.js");
	t.C(e);
	var r = (e) => t((t.s = e)),
		s = t.X(0, [959, 40, 704, 202], () => r(74423));
	module.exports = s;
})();
