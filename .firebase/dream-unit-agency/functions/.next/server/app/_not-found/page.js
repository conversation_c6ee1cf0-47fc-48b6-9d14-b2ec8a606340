(() => {
	var e = {};
	(e.id = 492),
		(e.ids = [492]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			6095: (e, r, t) => {
				t.r(r),
					t.d(r, {
						GlobalError: () => i.a,
						__next_app__: () => x,
						pages: () => a,
						routeModule: () => l,
						tree: () => d,
					});
				var o = t(7025),
					s = t(18198),
					n = t(82576),
					i = t.n(n),
					p = t(45239),
					u = {};
				for (const e in p)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (u[e] = () => p[e]);
				t.d(r, u);
				const d = {
						children: [
							"",
							{
								children: [
									"/_not-found",
									{
										children: [
											"__PAGE__",
											{},
											{
												page: [
													() => Promise.resolve().then(t.t.bind(t, 4540, 23)),
													"next/dist/client/components/not-found-error",
												],
											},
										],
									},
									{},
								],
							},
							{
								layout: [
									() => Promise.resolve().then(t.bind(t, 59650)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
								],
								loading: [
									() => Promise.resolve().then(t.bind(t, 34314)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
								],
								"not-found": [
									() => Promise.resolve().then(t.t.bind(t, 4540, 23)),
									"next/dist/client/components/not-found-error",
								],
								forbidden: [
									() => Promise.resolve().then(t.t.bind(t, 53117, 23)),
									"next/dist/client/components/forbidden-error",
								],
								unauthorized: [
									() => Promise.resolve().then(t.t.bind(t, 6874, 23)),
									"next/dist/client/components/unauthorized-error",
								],
							},
						],
					}.children,
					a = [],
					x = { require: t, loadChunk: () => Promise.resolve() },
					l = new o.AppPageRouteModule({
						definition: {
							kind: s.RouteKind.APP_PAGE,
							page: "/_not-found/page",
							pathname: "/_not-found",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: d },
					});
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			21820: (e) => {
				e.exports = require("os");
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			81630: (e) => {
				e.exports = require("http");
			},
			91645: (e) => {
				e.exports = require("net");
			},
			94735: (e) => {
				e.exports = require("events");
			},
		});
	var r = require("../../webpack-runtime.js");
	r.C(e);
	var t = (e) => r((r.s = e)),
		o = r.X(0, [959, 202], () => t(6095));
	module.exports = o;
})();
