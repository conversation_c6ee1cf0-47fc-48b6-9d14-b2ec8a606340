(() => {
	var e = {};
	(e.id = 988),
		(e.ids = [988]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			13964: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 31003));
			},
			14132: (e, t, r) => {
				r.d(t, { p: () => i });
				var s = r(45781),
					a = r(13072),
					o = r(77401);
				const i = a.forwardRef(({ className: e, type: t, ...r }, a) =>
					(0, s.jsx)("input", {
						type: t,
						className: (0, o.cn)(
							"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
							e,
						),
						ref: a,
						...r,
					}),
				);
				i.displayName = "Input";
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			21820: (e) => {
				e.exports = require("os");
			},
			27815: (e, t, r) => {
				r.d(t, { AM: () => n, Wv: () => d, hl: () => l });
				var s = r(45781),
					a = r(13072),
					o = r(41665),
					i = r(77401);
				const n = o.bL,
					d = o.l9,
					l = a.forwardRef(
						(
							{ className: e, align: t = "center", sideOffset: r = 4, ...a },
							n,
						) =>
							(0, s.jsx)(o.ZL, {
								children: (0, s.jsx)(o.UC, {
									ref: n,
									align: t,
									sideOffset: r,
									className: (0, i.cn)(
										"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
										e,
									),
									...a,
								}),
							}),
					);
				l.displayName = o.UC.displayName;
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			31003: (e, t, r) => {
				r.r(t), r.d(t, { default: () => s });
				const s = (0, r(51129).registerClientReference)(
					() => {
						throw Error(
							"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/search-projects/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
						);
					},
					"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/search-projects/page.tsx",
					"default",
				);
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			44238: (e, t, r) => {
				r.d(t, { $: () => l, r: () => d });
				var s = r(45781),
					a = r(13072),
					o = r(74645),
					i = r(87990),
					n = r(77401);
				const d = (0, i.F)(
						"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
						{
							variants: {
								variant: {
									default:
										"bg-primary text-primary-foreground hover:bg-primary/90",
									destructive:
										"bg-destructive text-destructive-foreground hover:bg-destructive/90",
									outline:
										"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
									secondary:
										"bg-secondary text-secondary-foreground hover:bg-secondary/80",
									ghost: "hover:bg-accent hover:text-accent-foreground",
									link: "text-primary underline-offset-4 hover:underline",
								},
								size: {
									default: "h-10 px-4 py-2",
									sm: "h-9 rounded-md px-3",
									lg: "h-11 rounded-md px-8",
									icon: "h-10 w-10",
								},
							},
							defaultVariants: { variant: "default", size: "default" },
						},
					),
					l = a.forwardRef(
						(
							{ className: e, variant: t, size: r, asChild: a = !1, ...i },
							l,
						) => {
							const c = a ? o.DX : "button";
							return (0, s.jsx)(c, {
								className: (0, n.cn)(d({ variant: t, size: r, className: e })),
								ref: l,
								...i,
							});
						},
					);
				l.displayName = "Button";
			},
			48414: (e, t, r) => {
				r.d(t, {
					bq: () => u,
					eb: () => x,
					gC: () => g,
					l6: () => c,
					yv: () => p,
				});
				var s = r(45781),
					a = r(13072),
					o = r(12265),
					i = r(71776),
					n = r(48613),
					d = r(28108),
					l = r(77401);
				const c = o.bL;
				o.YJ;
				const p = o.WT,
					u = a.forwardRef(({ className: e, children: t, ...r }, a) =>
						(0, s.jsxs)(o.l9, {
							ref: a,
							className: (0, l.cn)(
								"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",
								e,
							),
							...r,
							children: [
								t,
								(0, s.jsx)(o.In, {
									asChild: !0,
									children: (0, s.jsx)(i.A, {
										className: "h-4 w-4 opacity-50",
									}),
								}),
							],
						}),
					);
				u.displayName = o.l9.displayName;
				const m = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(o.PP, {
						ref: r,
						className: (0, l.cn)(
							"flex cursor-default items-center justify-center py-1",
							e,
						),
						...t,
						children: (0, s.jsx)(n.A, { className: "h-4 w-4" }),
					}),
				);
				m.displayName = o.PP.displayName;
				const f = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(o.wn, {
						ref: r,
						className: (0, l.cn)(
							"flex cursor-default items-center justify-center py-1",
							e,
						),
						...t,
						children: (0, s.jsx)(i.A, { className: "h-4 w-4" }),
					}),
				);
				f.displayName = o.wn.displayName;
				const g = a.forwardRef(
					({ className: e, children: t, position: r = "popper", ...a }, i) =>
						(0, s.jsx)(o.ZL, {
							children: (0, s.jsxs)(o.UC, {
								ref: i,
								className: (0, l.cn)(
									"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
									"popper" === r &&
										"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
									e,
								),
								position: r,
								...a,
								children: [
									(0, s.jsx)(m, {}),
									(0, s.jsx)(o.LM, {
										className: (0, l.cn)(
											"p-1",
											"popper" === r &&
												"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]",
										),
										children: t,
									}),
									(0, s.jsx)(f, {}),
								],
							}),
						}),
				);
				(g.displayName = o.UC.displayName),
					(a.forwardRef(({ className: e, ...t }, r) =>
						(0, s.jsx)(o.JU, {
							ref: r,
							className: (0, l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold", e),
							...t,
						}),
					).displayName = o.JU.displayName);
				const x = a.forwardRef(({ className: e, children: t, ...r }, a) =>
					(0, s.jsxs)(o.q7, {
						ref: a,
						className: (0, l.cn)(
							"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
							e,
						),
						...r,
						children: [
							(0, s.jsx)("span", {
								className:
									"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",
								children: (0, s.jsx)(o.VF, {
									children: (0, s.jsx)(d.A, { className: "h-4 w-4" }),
								}),
							}),
							(0, s.jsx)(o.p4, { children: t }),
						],
					}),
				);
				(x.displayName = o.q7.displayName),
					(a.forwardRef(({ className: e, ...t }, r) =>
						(0, s.jsx)(o.wv, {
							ref: r,
							className: (0, l.cn)("-mx-1 my-1 h-px bg-muted", e),
							...t,
						}),
					).displayName = o.wv.displayName);
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77401: (e, t, r) => {
				r.d(t, { cn: () => o });
				var s = r(42366),
					a = r(73927);
				function o(...e) {
					return (0, a.QP)((0, s.$)(e));
				}
			},
			77516: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 98349));
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			79927: (e, t, r) => {
				r.r(t),
					r.d(t, {
						GlobalError: () => i.a,
						__next_app__: () => p,
						pages: () => c,
						routeModule: () => u,
						tree: () => l,
					});
				var s = r(7025),
					a = r(18198),
					o = r(82576),
					i = r.n(o),
					n = r(45239),
					d = {};
				for (const e in n)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (d[e] = () => n[e]);
				r.d(t, d);
				const l = {
						children: [
							"",
							{
								children: [
									"search-projects",
									{
										children: [
											"__PAGE__",
											{},
											{
												page: [
													() => Promise.resolve().then(r.bind(r, 31003)),
													"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/search-projects/page.tsx",
												],
											},
										],
									},
									{},
								],
							},
							{
								layout: [
									() => Promise.resolve().then(r.bind(r, 59650)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
								],
								loading: [
									() => Promise.resolve().then(r.bind(r, 34314)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
								],
								"not-found": [
									() => Promise.resolve().then(r.t.bind(r, 4540, 23)),
									"next/dist/client/components/not-found-error",
								],
								forbidden: [
									() => Promise.resolve().then(r.t.bind(r, 53117, 23)),
									"next/dist/client/components/forbidden-error",
								],
								unauthorized: [
									() => Promise.resolve().then(r.t.bind(r, 6874, 23)),
									"next/dist/client/components/unauthorized-error",
								],
							},
						],
					}.children,
					c = [
						"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/search-projects/page.tsx",
					],
					p = { require: r, loadChunk: () => Promise.resolve() },
					u = new s.AppPageRouteModule({
						definition: {
							kind: a.RouteKind.APP_PAGE,
							page: "/search-projects/page",
							pathname: "/search-projects",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: l },
					});
			},
			81630: (e) => {
				e.exports = require("http");
			},
			91645: (e) => {
				e.exports = require("net");
			},
			93188: (e, t, r) => {
				r.d(t, { V: () => l });
				var s = r(45781);
				r(13072);
				var a = r(24185),
					o = r(2152),
					i = r(78490),
					n = r(77401),
					d = r(44238);
				function l({
					className: e,
					classNames: t,
					showOutsideDays: r = !0,
					...l
				}) {
					return (0, s.jsx)(i.hv, {
						showOutsideDays: r,
						className: (0, n.cn)("p-3", e),
						classNames: {
							months:
								"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
							month: "space-y-4",
							caption: "flex justify-center pt-1 relative items-center",
							caption_label: "text-sm font-medium",
							nav: "space-x-1 flex items-center",
							nav_button: (0, n.cn)(
								(0, d.r)({ variant: "outline" }),
								"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
							),
							nav_button_previous: "absolute left-1",
							nav_button_next: "absolute right-1",
							table: "w-full border-collapse space-y-1",
							head_row: "flex",
							head_cell:
								"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
							row: "flex w-full mt-2",
							cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
							day: (0, n.cn)(
								(0, d.r)({ variant: "ghost" }),
								"h-9 w-9 p-0 font-normal aria-selected:opacity-100",
							),
							day_range_end: "day-range-end",
							day_selected:
								"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
							day_today: "bg-accent text-accent-foreground",
							day_outside:
								"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground",
							day_disabled: "text-muted-foreground opacity-50",
							day_range_middle:
								"aria-selected:bg-accent aria-selected:text-accent-foreground",
							day_hidden: "invisible",
							...t,
						},
						components: {
							IconLeft: ({ ...e }) => (0, s.jsx)(a.A, { className: "h-4 w-4" }),
							IconRight: ({ ...e }) =>
								(0, s.jsx)(o.A, { className: "h-4 w-4" }),
						},
						...l,
					});
				}
				l.displayName = "Calendar";
			},
			94735: (e) => {
				e.exports = require("events");
			},
			98349: (e, t, r) => {
				r.r(t), r.d(t, { default: () => y });
				var s = r(45781),
					a = r(13072),
					o = r(44238),
					i = r(14132),
					n = r(48414),
					d = r(93188),
					l = r(27815),
					c = r(34849),
					p = r(3704),
					u = r(93476),
					m = r(28328),
					f = r.n(m),
					g = r(77401);
				const x = [
						"FASHION EDITORIAL",
						"COMMERCIAL AD CAMPAIGN",
						"MUSIC VIDEO PRODUCTION",
						"DOCUMENTARY FILM",
						"EVENT PHOTOGRAPHY/VIDEOGRAPHY",
						"BRAND COLLABORATION",
						"PRODUCT SHOOT",
						"CORPORATE VIDEO",
					],
					h = [
						{
							id: "proj1",
							title: "Urban Dreams Fashion Editorial",
							description:
								"A cutting-edge fashion shoot exploring modern urban aesthetics. Looking for a dynamic team.",
							projectType: "FASHION EDITORIAL",
							location: "New York, NY",
							coordinates: { lat: 40.7128, lng: -74.006 },
							startDate: "2024-07-10",
							endDate: "2024-07-12",
							projectImageUrl: "/assets/mood_boards/mb1.png",
							clientOrBrandName: "Vogue Magazine",
							budgetOrCompensation: "$5,000 - $8,000",
						},
						{
							id: "proj2",
							title: "EcoGadget Launch Campaign",
							description:
								"Commercial ad campaign for a new sustainable tech gadget. Seeking videographer and models.",
							projectType: "COMMERCIAL AD CAMPAIGN",
							location: "San Francisco, CA",
							coordinates: { lat: 37.7749, lng: -122.4194 },
							startDate: "2024-08-01",
							endDate: "2024-08-05",
							projectImageUrl: "/assets/mood_boards/mb2.png",
							clientOrBrandName: "GreenTech Inc.",
							budgetOrCompensation: "Est. $15,000",
						},
						{
							id: "proj3",
							title: "Indie Artist Music Video",
							description:
								"Creative music video for an up-and-coming indie pop creative. Theme: Surreal Nostalgia.",
							projectType: "MUSIC VIDEO PRODUCTION",
							location: "Los Angeles, CA",
							coordinates: { lat: 34.0522, lng: -118.2437 },
							startDate: "2024-07-20",
							endDate: "2024-07-22",
							projectImageUrl: "/assets/mood_boards/mb3.png",
							clientOrBrandName: "Luna Bloom (Artist)",
							budgetOrCompensation: "Profit Share + Expenses",
						},
						{
							id: "proj4",
							title: "Wildlife Conservation Documentary",
							description:
								"Feature documentary on local wildlife conservation efforts. Requires experienced wildlife photographer.",
							projectType: "DOCUMENTARY FILM",
							location: "Denver, CO",
							coordinates: { lat: 39.7392, lng: -104.9903 },
							startDate: "2024-09-01",
							endDate: "2024-10-15",
							projectImageUrl: "/assets/mood_boards/mb4.png",
							clientOrBrandName: "Nature's Voice Foundation",
							budgetOrCompensation: "Grant Funded",
						},
						{
							id: "proj5",
							title: "Tech Conference Highlights",
							description:
								"Event photography and videography for a major tech conference. Fast turnaround needed.",
							projectType: "EVENT PHOTOGRAPHY/VIDEOGRAPHY",
							location: "Austin, TX",
							coordinates: { lat: 30.2672, lng: -97.7431 },
							startDate: "2024-07-25",
							endDate: "2024-07-27",
							projectImageUrl: "/assets/mood_boards/mb5.png",
							clientOrBrandName: "Innovate Summit",
							budgetOrCompensation: "$3,000",
						},
						{
							id: "proj6",
							title: "Summer Collection Brand Collab",
							description:
								"Lifestyle brand looking for models and a photographer for their summer collection launch.",
							projectType: "BRAND COLLABORATION",
							location: "Miami, FL",
							coordinates: { lat: 25.7617, lng: -80.1918 },
							startDate: "2024-07-01",
							endDate: "2024-07-05",
							projectImageUrl: "/assets/mood_boards/mb6.png",
							clientOrBrandName: "SunKissed Apparel",
							budgetOrCompensation: "Product + $1,500",
						},
						{
							id: "proj7",
							title: "Artisanal Coffee Product Shoot",
							description:
								"High-quality product photography for a new line of artisanal coffee beans.",
							projectType: "PRODUCT SHOOT",
							location: "Portland, OR",
							coordinates: { lat: 45.5051, lng: -122.675 },
							startDate: "2024-08-10",
							endDate: "2024-08-11",
							projectImageUrl: "/assets/mood_boards/mb7.png",
							clientOrBrandName: "Roast & Co.",
							budgetOrCompensation: "$1,200",
						},
						{
							id: "proj8",
							title: "Startup Success Story Video",
							description:
								"Corporate video showcasing the journey and success of a local tech startup.",
							projectType: "CORPORATE VIDEO",
							location: "Seattle, WA",
							coordinates: { lat: 47.6062, lng: -122.3321 },
							startDate: "2024-09-15",
							endDate: "2024-09-18",
							projectImageUrl: "/assets/mood_boards/mb8.png",
							clientOrBrandName: "Innovate Solutions Ltd.",
							budgetOrCompensation: "$4,500",
						},
					];
				function y() {
					const [e, t] = (0, a.useState)(),
						[r, m] = (0, a.useState)(""),
						[y, b] = (0, a.useState)(""),
						[v, j] = (0, a.useState)(""),
						[w, N] = (0, a.useState)(!1),
						[C, O] = (0, a.useState)([]),
						[_, D] = (0, a.useState)([]),
						I = (0, a.useMemo)(
							() =>
								h.filter((t) => {
									const s =
											"" === r ||
											t.title.toLowerCase().includes(r.toLowerCase()) ||
											t.description.toLowerCase().includes(r.toLowerCase()) ||
											t.location.toLowerCase().includes(r.toLowerCase()),
										a = "" === y || "all" === y || t.projectType === y;
									return (
										s &&
										a &&
										(() => {
											if (!e || (!e.from && !e.to)) return !0;
											if (e.from && !e.to) return new Date(t.endDate) >= e.from;
											if (!e.from && e.to) return new Date(t.startDate) <= e.to;
											if (e.from && e.to) {
												const r = new Date(t.startDate),
													s = new Date(t.endDate);
												return r <= e.to && s >= e.from;
											}
											return !0;
										})()
									);
								}),
							[h, r, y, e],
						);
					(0, a.useMemo)(() => {
						D(
							I.map((e, t) => ({
								...e,
								display: !0,
								colSpan: 1,
								rowSpan: 1,
								isPrimaryOfMerge: !1,
								constituentIds: [e.id],
								originalIndex: t,
							})),
						),
							O([]);
					}, [I]);
					const A = (e) => {
							w &&
								O((t) =>
									t.includes(e) ? t.filter((t) => t !== e) : [...t, e],
								);
						},
						P = (e) => {
							if (2 !== C.length) {
								alert("Please select exactly two tiles to merge.");
								return;
							}
							const [t, r] = C,
								s = _.find((e) => e.id === t),
								a = _.find((e) => e.id === r);
							if (!s || !a) {
								console.error("Selected projects not found in display config.");
								return;
							}
							const o = s.originalIndex < a.originalIndex ? s : a,
								i = s.originalIndex < a.originalIndex ? a : s;
							if (o.constituentIds.some((e) => i.constituentIds.includes(e))) {
								alert(
									"Cannot merge tiles that are already part of the same group or a tile with itself.",
								),
									O([]);
								return;
							}
							D((t) =>
								t.map((t) =>
									t.id === o.id
										? {
												...t,
												colSpan:
													"horizontal" === e
														? o.colSpan + i.colSpan
														: o.colSpan,
												rowSpan:
													"vertical" === e ? o.rowSpan + i.rowSpan : o.rowSpan,
												isPrimaryOfMerge: !0,
												constituentIds: Array.from(
													new Set([...o.constituentIds, ...i.constituentIds]),
												),
												display: !0,
											}
										: t.id === i.id
											? { ...t, display: !1 }
											: t,
								),
							),
								O([]),
								console.log(`${e} merge: Primary: ${o.id}, Secondary: ${i.id}`);
						},
						q = (e) => {
							D((t) => {
								const r = t.find((t) => t.id === e && t.isPrimaryOfMerge);
								if (!r || !r.constituentIds) return t;
								const s = r.constituentIds;
								return t.map((e) =>
									s.includes(e.id)
										? {
												...(I.find((t) => t.id === e.id) || e),
												id: e.id,
												display: !0,
												colSpan: 1,
												rowSpan: 1,
												isPrimaryOfMerge: !1,
												constituentIds: [e.id],
												originalIndex: e.originalIndex,
											}
										: e,
								);
							}),
								O([]),
								console.log(`Unmerged group associated with: ${e}`);
						};
					return (0, s.jsxs)("div", {
						className: "min-h-screen bg-background flex flex-col",
						children: [
							(0, s.jsx)("header", {
								className:
									"bg-white border-b border-gray-200 sticky top-0 z-40",
								children: (0, s.jsx)("div", {
									className: "max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8",
									children: (0, s.jsxs)("div", {
										className: "flex items-center justify-between h-16",
										children: [
											(0, s.jsx)("div", {
												className: "flex items-center",
												children: (0, s.jsx)(f(), {
													href: "/",
													className:
														"text-2xl font-light tracking-wide text-gray-800",
													children: "DUA",
												}),
											}),
											(0, s.jsx)("div", {
												className: "flex items-center",
												children: (0, s.jsx)(f(), {
													href: "/creative/1",
													className:
														"text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors",
													children: "Dashboard",
												}),
											}),
										],
									}),
								}),
							}),
							(0, s.jsx)("main", {
								className: "flex-1 overflow-y-auto p-6",
								children: (0, s.jsxs)("div", {
									className: "max-w-screen-xl mx-auto",
									children: [
										(0, s.jsxs)("div", {
											className: "mb-8 p-6 bg-white rounded-lg shadow",
											children: [
												(0, s.jsxs)("div", {
													className:
														"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end",
													children: [
														(0, s.jsx)(i.p, {
															placeholder: "Search by title, desc, or location",
															value: r,
															onChange: (e) => m(e.target.value),
															className: "border-gray-200 bg-white font-light",
														}),
														(0, s.jsxs)(n.l6, {
															value: y,
															onValueChange: b,
															children: [
																(0, s.jsx)(n.bq, {
																	className:
																		"border-gray-200 bg-white font-light",
																	children: (0, s.jsx)(n.yv, {
																		placeholder: "All Project Types",
																	}),
																}),
																(0, s.jsxs)(n.gC, {
																	children: [
																		(0, s.jsx)(n.eb, {
																			value: "all",
																			children: "All Project Types",
																		}),
																		x.map((e) =>
																			(0, s.jsx)(
																				n.eb,
																				{
																					value: e,
																					className: "font-light",
																					children: e,
																				},
																				e,
																			),
																		),
																	],
																}),
															],
														}),
														(0, s.jsx)(i.p, {
															placeholder: "Location (e.g., City, State)",
															value: v,
															onChange: (e) => j(e.target.value),
															className: "border-gray-200 bg-white font-light",
														}),
														(0, s.jsxs)(l.AM, {
															children: [
																(0, s.jsx)(l.Wv, {
																	asChild: !0,
																	children: (0, s.jsxs)(o.$, {
																		variant: "outline",
																		className:
																			"justify-start text-left font-light border-gray-200 bg-white w-full",
																		children: [
																			(0, s.jsx)(u.A, {
																				className: "mr-2 h-4 w-4",
																			}),
																			e?.from
																				? e.to
																					? (0, s.jsxs)(s.Fragment, {
																							children: [
																								(0, c.GP)(e.from, "LLL dd, y"),
																								" - ",
																								(0, c.GP)(e.to, "LLL dd, y"),
																							],
																						})
																					: (0, c.GP)(e.from, "LLL dd, y")
																				: (0, s.jsx)("span", {
																						children: "Select date range",
																					}),
																		],
																	}),
																}),
																(0, s.jsx)(l.hl, {
																	className: "w-auto p-0",
																	align: "start",
																	children: (0, s.jsx)(d.V, {
																		initialFocus: !0,
																		mode: "range",
																		defaultMonth: e?.from,
																		selected: e,
																		onSelect: t,
																		numberOfMonths: 2,
																	}),
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													className: "flex justify-end space-x-2 mt-6",
													children: [
														(0, s.jsx)(o.$, {
															variant: w ? "destructive" : "outline",
															onClick: () => N(!w),
															children: w
																? "Exit Edit Mode"
																: "Enter Edit Mode",
														}),
														w &&
															2 === C.length &&
															(0, s.jsxs)(s.Fragment, {
																children: [
																	(0, s.jsx)(o.$, {
																		onClick: () => P("horizontal"),
																		size: "sm",
																		children: "Merge Horizontally",
																	}),
																	(0, s.jsx)(o.$, {
																		onClick: () => P("vertical"),
																		size: "sm",
																		children: "Merge Vertically",
																	}),
																],
															}),
													],
												}),
											],
										}),
										(0, s.jsx)("div", {
											className:
												"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",
											children: _.filter((e) => e.display).map((e) =>
												(0, s.jsxs)(
													"div",
													{
														className: (0, g.cn)(
															"group",
															w ? "cursor-pointer" : "",
															w &&
																C.includes(e.id) &&
																"ring-2 ring-blue-500 ring-offset-2 shadow-lg",
														),
														onClick: () => A(e.id),
														style: {
															gridColumn: `span ${e.colSpan}`,
															gridRow: `span ${e.rowSpan}`,
															minWidth: 0,
															overflow: "hidden",
															position: "relative",
														},
														children: [
															(0, s.jsx)(f(), {
																href: `/booker/project/${e.id}`,
																onClick: (e) => {
																	w && e.preventDefault();
																},
																className: "block h-full w-full",
																style: { minWidth: 0 },
																children: (0, s.jsxs)("div", {
																	className:
																		"relative mb-4 h-full flex flex-col w-full",
																	style: { minWidth: 0 },
																	children: [
																		(0, s.jsx)("div", {
																			className: "relative w-full",
																			style: {
																				paddingBottom: "75%",
																				overflow: "hidden",
																			},
																			children: (0, s.jsx)(p.default, {
																				src:
																					e.projectImageUrl ||
																					"/placeholder.svg",
																				alt: e.title,
																				fill: !0,
																				className:
																					"object-cover absolute top-0 left-0 w-full h-full transition-transform duration-500 group-hover:scale-105",
																			}),
																		}),
																		(0, s.jsxs)("div", {
																			className:
																				"p-2 space-y-1 flex-grow w-full",
																			style: {
																				minWidth: 0,
																				overflow: "hidden",
																			},
																			children: [
																				(0, s.jsx)("div", {
																					className:
																						"text-xs text-gray-400 font-light tracking-wide w-full",
																					children: e.projectType,
																				}),
																				(0, s.jsx)("h3", {
																					className:
																						"font-light text-black tracking-wide break-words w-full",
																					style: {
																						overflow: "hidden",
																						textOverflow: "ellipsis",
																						whiteSpace: "normal",
																					},
																					children: e.title,
																				}),
																				(0, s.jsx)("p", {
																					className:
																						"text-xs text-gray-600 font-light h-10 break-words w-full",
																					children: e.description,
																				}),
																				(0, s.jsx)("div", {
																					className:
																						"text-xs text-gray-500 font-light w-full",
																					children: e.location,
																				}),
																				(0, s.jsxs)("div", {
																					className:
																						"text-xs text-gray-500 font-light w-full",
																					children: [
																						(0, c.GP)(
																							new Date(e.startDate),
																							"MMM dd, yyyy",
																						),
																						" - ",
																						(0, c.GP)(
																							new Date(e.endDate),
																							"MMM dd, yyyy",
																						),
																					],
																				}),
																			],
																		}),
																	],
																}),
															}),
															w &&
																e.isPrimaryOfMerge &&
																(0, s.jsx)(o.$, {
																	variant: "destructive",
																	size: "sm",
																	className:
																		"absolute top-1 right-1 z-10 opacity-80 hover:opacity-100 p-1 h-auto leading-none",
																	onClick: (t) => {
																		t.stopPropagation(), q(e.id);
																	},
																	children: "Unmerge",
																}),
														],
													},
													e.id,
												),
											),
										}),
										0 === _.filter((e) => e.display).length &&
											(0, s.jsxs)("div", {
												className: "text-center py-24",
												children: [
													(0, s.jsx)("h3", {
														className: "text-xl font-light text-gray-900 mb-2",
														children: "No projects found",
													}),
													(0, s.jsx)("p", {
														className: "text-gray-500 font-light",
														children: "Try adjusting your search criteria",
													}),
												],
											}),
									],
								}),
							}),
						],
					});
				}
			},
		});
	var t = require("../../webpack-runtime.js");
	t.C(e);
	var r = (e) => t((t.s = e)),
		s = t.X(0, [959, 40, 482, 704, 227, 849, 620, 202], () => r(79927));
	module.exports = s;
})();
