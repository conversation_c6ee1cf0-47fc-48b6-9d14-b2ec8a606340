(() => {
	var e = {};
	(e.id = 974),
		(e.ids = [974]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			1183: (e, t, r) => {
				r.r(t),
					r.d(t, {
						GlobalError: () => n.a,
						__next_app__: () => u,
						pages: () => d,
						routeModule: () => p,
						tree: () => c,
					});
				var s = r(7025),
					a = r(18198),
					i = r(82576),
					n = r.n(i),
					o = r(45239),
					l = {};
				for (const e in o)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (l[e] = () => o[e]);
				r.d(t, l);
				const c = [
						"",
						{
							children: [
								"__PAGE__",
								{},
								{
									page: [
										() => Promise.resolve().then(r.bind(r, 39225)),
										"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/page.tsx",
									],
								},
							],
						},
						{
							layout: [
								() => Promise.resolve().then(r.bind(r, 59650)),
								"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
							],
							loading: [
								() => Promise.resolve().then(r.bind(r, 34314)),
								"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
							],
							"not-found": [
								() => Promise.resolve().then(r.t.bind(r, 4540, 23)),
								"next/dist/client/components/not-found-error",
							],
							forbidden: [
								() => Promise.resolve().then(r.t.bind(r, 53117, 23)),
								"next/dist/client/components/forbidden-error",
							],
							unauthorized: [
								() => Promise.resolve().then(r.t.bind(r, 6874, 23)),
								"next/dist/client/components/unauthorized-error",
							],
						},
					],
					d = [
						"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/page.tsx",
					],
					u = { require: r, loadChunk: () => Promise.resolve() },
					p = new s.AppPageRouteModule({
						definition: {
							kind: a.RouteKind.APP_PAGE,
							page: "/page",
							pathname: "/",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: c },
					});
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			7439: (e, t, r) => {
				var s = r(37995);
				r.o(s, "useRouter") && r.d(t, { useRouter: () => s.useRouter });
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			14132: (e, t, r) => {
				r.d(t, { p: () => n });
				var s = r(45781),
					a = r(13072),
					i = r(77401);
				const n = a.forwardRef(({ className: e, type: t, ...r }, a) =>
					(0, s.jsx)("input", {
						type: t,
						className: (0, i.cn)(
							"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
							e,
						),
						ref: a,
						...r,
					}),
				);
				n.displayName = "Input";
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			21247: (e, t, r) => {
				r.d(t, { default: () => s });
				const s = (0, r(51129).registerClientReference)(
					() => {
						throw Error(
							"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/landing-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
						);
					},
					"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/landing-page.tsx",
					"default",
				);
			},
			21820: (e) => {
				e.exports = require("os");
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			36621: (e, t, r) => {
				r.d(t, { C: () => n });
				var s = r(13072),
					a = r(47851),
					i = r(63904),
					n = (e) => {
						const { present: t, children: r } = e,
							n = ((e) => {
								var t, r;
								const [a, n] = s.useState(),
									l = s.useRef({}),
									c = s.useRef(e),
									d = s.useRef("none"),
									[u, p] =
										((t = e ? "mounted" : "unmounted"),
										(r = {
											mounted: {
												UNMOUNT: "unmounted",
												ANIMATION_OUT: "unmountSuspended",
											},
											unmountSuspended: {
												MOUNT: "mounted",
												ANIMATION_END: "unmounted",
											},
											unmounted: { MOUNT: "mounted" },
										}),
										s.useReducer((e, t) => r[e][t] ?? e, t));
								return (
									s.useEffect(() => {
										const e = o(l.current);
										d.current = "mounted" === u ? e : "none";
									}, [u]),
									(0, i.N)(() => {
										const t = l.current,
											r = c.current;
										if (r !== e) {
											const s = d.current,
												a = o(t);
											e
												? p("MOUNT")
												: "none" === a || t?.display === "none"
													? p("UNMOUNT")
													: r && s !== a
														? p("ANIMATION_OUT")
														: p("UNMOUNT"),
												(c.current = e);
										}
									}, [e, p]),
									(0, i.N)(() => {
										if (a) {
											let e;
											const t = a.ownerDocument.defaultView ?? window,
												r = (r) => {
													const s = o(l.current).includes(r.animationName);
													if (
														r.target === a &&
														s &&
														(p("ANIMATION_END"), !c.current)
													) {
														const r = a.style.animationFillMode;
														(a.style.animationFillMode = "forwards"),
															(e = t.setTimeout(() => {
																"forwards" === a.style.animationFillMode &&
																	(a.style.animationFillMode = r);
															}));
													}
												},
												s = (e) => {
													e.target === a && (d.current = o(l.current));
												};
											return (
												a.addEventListener("animationstart", s),
												a.addEventListener("animationcancel", r),
												a.addEventListener("animationend", r),
												() => {
													t.clearTimeout(e),
														a.removeEventListener("animationstart", s),
														a.removeEventListener("animationcancel", r),
														a.removeEventListener("animationend", r);
												}
											);
										}
										p("ANIMATION_END");
									}, [a, p]),
									{
										isPresent: ["mounted", "unmountSuspended"].includes(u),
										ref: s.useCallback((e) => {
											e && (l.current = getComputedStyle(e)), n(e);
										}, []),
									}
								);
							})(t),
							l =
								"function" == typeof r
									? r({ present: n.isPresent })
									: s.Children.only(r),
							c = (0, a.s)(
								n.ref,
								((e) => {
									let t = Object.getOwnPropertyDescriptor(e.props, "ref")?.get,
										r = t && "isReactWarning" in t && t.isReactWarning;
									return r
										? e.ref
										: (r =
													(t = Object.getOwnPropertyDescriptor(
														e,
														"ref",
													)?.get) &&
													"isReactWarning" in t &&
													t.isReactWarning)
											? e.props.ref
											: e.props.ref || e.ref;
								})(l),
							);
						return "function" == typeof r || n.isPresent
							? s.cloneElement(l, { ref: c })
							: null;
					};
				function o(e) {
					return e?.animationName || "none";
				}
				n.displayName = "Presence";
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			39225: (e, t, r) => {
				r.r(t), r.d(t, { default: () => i });
				var s = r(95479),
					a = r(21247);
				function i() {
					return (0, s.jsx)(a.default, {});
				}
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			44238: (e, t, r) => {
				r.d(t, { $: () => c, r: () => l });
				var s = r(45781),
					a = r(13072),
					i = r(74645),
					n = r(87990),
					o = r(77401);
				const l = (0, n.F)(
						"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
						{
							variants: {
								variant: {
									default:
										"bg-primary text-primary-foreground hover:bg-primary/90",
									destructive:
										"bg-destructive text-destructive-foreground hover:bg-destructive/90",
									outline:
										"border border-input bg-background hover:bg-accent hover:text-accent-foreground",
									secondary:
										"bg-secondary text-secondary-foreground hover:bg-secondary/80",
									ghost: "hover:bg-accent hover:text-accent-foreground",
									link: "text-primary underline-offset-4 hover:underline",
								},
								size: {
									default: "h-10 px-4 py-2",
									sm: "h-9 rounded-md px-3",
									lg: "h-11 rounded-md px-8",
									icon: "h-10 w-10",
								},
							},
							defaultVariants: { variant: "default", size: "default" },
						},
					),
					c = a.forwardRef(
						(
							{ className: e, variant: t, size: r, asChild: a = !1, ...n },
							c,
						) => {
							const d = a ? i.DX : "button";
							return (0, s.jsx)(d, {
								className: (0, o.cn)(l({ variant: t, size: r, className: e })),
								ref: c,
								...n,
							});
						},
					);
				c.displayName = "Button";
			},
			45553: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 72504));
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			55281: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 21247));
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			72504: (e, t, r) => {
				r.d(t, { default: () => e4 });
				var s = r(45781),
					a = r(13072),
					i = r(44238),
					n = r(14132),
					o = r(60110),
					l = a.forwardRef((e, t) =>
						(0, s.jsx)(o.sG.label, {
							...e,
							ref: t,
							onMouseDown: (t) => {
								t.target.closest("button, input, select, textarea") ||
									(e.onMouseDown?.(t),
									!t.defaultPrevented && t.detail > 1 && t.preventDefault());
							},
						}),
					);
				l.displayName = "Label";
				var c = r(87990),
					d = r(77401);
				const u = (0, c.F)(
						"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
					),
					p = a.forwardRef(({ className: e, ...t }, r) =>
						(0, s.jsx)(l, { ref: r, className: (0, d.cn)(u(), e), ...t }),
					);
				p.displayName = l.displayName;
				var m = r(85271),
					x = r(61779),
					f = r(113),
					h = r(47851),
					g = r(35414),
					v = r(99210),
					j = r(52650),
					b = r(39092),
					y = "rovingFocusGroup.onEntryFocus",
					N = { bubbles: !1, cancelable: !0 },
					w = "RovingFocusGroup",
					[k, C, D] = (0, f.N)(w),
					[R, q] = (0, x.A)(w, [D]),
					[_, I] = R(w),
					A = a.forwardRef((e, t) =>
						(0, s.jsx)(k.Provider, {
							scope: e.__scopeRovingFocusGroup,
							children: (0, s.jsx)(k.Slot, {
								scope: e.__scopeRovingFocusGroup,
								children: (0, s.jsx)(F, { ...e, ref: t }),
							}),
						}),
					);
				A.displayName = w;
				var F = a.forwardRef((e, t) => {
						const {
								__scopeRovingFocusGroup: r,
								orientation: i,
								loop: n = !1,
								dir: l,
								currentTabStopId: c,
								defaultCurrentTabStopId: d,
								onCurrentTabStopIdChange: u,
								onEntryFocus: p,
								preventScrollOnEntryFocus: x = !1,
								...f
							} = e,
							g = a.useRef(null),
							w = (0, h.s)(t, g),
							k = (0, b.jH)(l),
							[D = null, R] = (0, j.i)({
								prop: c,
								defaultProp: d,
								onChange: u,
							}),
							[q, I] = a.useState(!1),
							A = (0, v.c)(p),
							F = C(r),
							P = a.useRef(!1),
							[E, S] = a.useState(0);
						return (
							a.useEffect(() => {
								const e = g.current;
								if (e)
									return (
										e.addEventListener(y, A), () => e.removeEventListener(y, A)
									);
							}, [A]),
							(0, s.jsx)(_, {
								scope: r,
								orientation: i,
								dir: k,
								loop: n,
								currentTabStopId: D,
								onItemFocus: a.useCallback((e) => R(e), [R]),
								onItemShiftTab: a.useCallback(() => I(!0), []),
								onFocusableItemAdd: a.useCallback(() => S((e) => e + 1), []),
								onFocusableItemRemove: a.useCallback(() => S((e) => e - 1), []),
								children: (0, s.jsx)(o.sG.div, {
									tabIndex: q || 0 === E ? -1 : 0,
									"data-orientation": i,
									...f,
									ref: w,
									style: { outline: "none", ...e.style },
									onMouseDown: (0, m.m)(e.onMouseDown, () => {
										P.current = !0;
									}),
									onFocus: (0, m.m)(e.onFocus, (e) => {
										const t = !P.current;
										if (e.target === e.currentTarget && t && !q) {
											const t = new CustomEvent(y, N);
											if (
												(e.currentTarget.dispatchEvent(t), !t.defaultPrevented)
											) {
												const e = F().filter((e) => e.focusable);
												T(
													[
														e.find((e) => e.active),
														e.find((e) => e.id === D),
														...e,
													]
														.filter(Boolean)
														.map((e) => e.ref.current),
													x,
												);
											}
										}
										P.current = !1;
									}),
									onBlur: (0, m.m)(e.onBlur, () => I(!1)),
								}),
							})
						);
					}),
					P = "RovingFocusGroupItem",
					E = a.forwardRef((e, t) => {
						const {
								__scopeRovingFocusGroup: r,
								focusable: i = !0,
								active: n = !1,
								tabStopId: l,
								...c
							} = e,
							d = (0, g.B)(),
							u = l || d,
							p = I(P, r),
							x = p.currentTabStopId === u,
							f = C(r),
							{ onFocusableItemAdd: h, onFocusableItemRemove: v } = p;
						return (
							a.useEffect(() => {
								if (i) return h(), () => v();
							}, [i, h, v]),
							(0, s.jsx)(k.ItemSlot, {
								scope: r,
								id: u,
								focusable: i,
								active: n,
								children: (0, s.jsx)(o.sG.span, {
									tabIndex: x ? 0 : -1,
									"data-orientation": p.orientation,
									...c,
									ref: t,
									onMouseDown: (0, m.m)(e.onMouseDown, (e) => {
										i ? p.onItemFocus(u) : e.preventDefault();
									}),
									onFocus: (0, m.m)(e.onFocus, () => p.onItemFocus(u)),
									onKeyDown: (0, m.m)(e.onKeyDown, (e) => {
										if ("Tab" === e.key && e.shiftKey) {
											p.onItemShiftTab();
											return;
										}
										if (e.target !== e.currentTarget) return;
										const t = ((e, t, r) => {
											var s;
											const a =
												((s = e.key),
												"rtl" !== r
													? s
													: "ArrowLeft" === s
														? "ArrowRight"
														: "ArrowRight" === s
															? "ArrowLeft"
															: s);
											if (
												!(
													"vertical" === t &&
													["ArrowLeft", "ArrowRight"].includes(a)
												) &&
												!(
													"horizontal" === t &&
													["ArrowUp", "ArrowDown"].includes(a)
												)
											)
												return S[a];
										})(e, p.orientation, p.dir);
										if (void 0 !== t) {
											if (e.metaKey || e.ctrlKey || e.altKey || e.shiftKey)
												return;
											e.preventDefault();
											let r = f()
												.filter((e) => e.focusable)
												.map((e) => e.ref.current);
											if ("last" === t) r.reverse();
											else if ("prev" === t || "next" === t) {
												"prev" === t && r.reverse();
												const s = r.indexOf(e.currentTarget);
												r = p.loop
													? ((e, t) => e.map((r, s) => e[(t + s) % e.length]))(
															r,
															s + 1,
														)
													: r.slice(s + 1);
											}
											setTimeout(() => T(r));
										}
									}),
								}),
							})
						);
					});
				E.displayName = P;
				var S = {
					ArrowLeft: "prev",
					ArrowUp: "prev",
					ArrowRight: "next",
					ArrowDown: "next",
					PageUp: "first",
					Home: "first",
					PageDown: "last",
					End: "last",
				};
				function T(e, t = !1) {
					const r = document.activeElement;
					for (const s of e)
						if (
							s === r ||
							(s.focus({ preventScroll: t }), document.activeElement !== r)
						)
							return;
				}
				var M = r(36621),
					O = "Tabs",
					[U, G] = (0, x.A)(O, [q]),
					B = q(),
					[$, L] = U(O),
					z = a.forwardRef((e, t) => {
						const {
								__scopeTabs: r,
								value: a,
								onValueChange: i,
								defaultValue: n,
								orientation: l = "horizontal",
								dir: c,
								activationMode: d = "automatic",
								...u
							} = e,
							p = (0, b.jH)(c),
							[m, x] = (0, j.i)({ prop: a, onChange: i, defaultProp: n });
						return (0, s.jsx)($, {
							scope: r,
							baseId: (0, g.B)(),
							value: m,
							onValueChange: x,
							orientation: l,
							dir: p,
							activationMode: d,
							children: (0, s.jsx)(o.sG.div, {
								dir: p,
								"data-orientation": l,
								...u,
								ref: t,
							}),
						});
					});
				z.displayName = O;
				var V = "TabsList",
					K = a.forwardRef((e, t) => {
						const { __scopeTabs: r, loop: a = !0, ...i } = e,
							n = L(V, r),
							l = B(r);
						return (0, s.jsx)(A, {
							asChild: !0,
							...l,
							orientation: n.orientation,
							dir: n.dir,
							loop: a,
							children: (0, s.jsx)(o.sG.div, {
								role: "tablist",
								"aria-orientation": n.orientation,
								...i,
								ref: t,
							}),
						});
					});
				K.displayName = V;
				var W = "TabsTrigger",
					H = a.forwardRef((e, t) => {
						const { __scopeTabs: r, value: a, disabled: i = !1, ...n } = e,
							l = L(W, r),
							c = B(r),
							d = Z(l.baseId, a),
							u = Q(l.baseId, a),
							p = a === l.value;
						return (0, s.jsx)(E, {
							asChild: !0,
							...c,
							focusable: !i,
							active: p,
							children: (0, s.jsx)(o.sG.button, {
								type: "button",
								role: "tab",
								"aria-selected": p,
								"aria-controls": u,
								"data-state": p ? "active" : "inactive",
								"data-disabled": i ? "" : void 0,
								disabled: i,
								id: d,
								...n,
								ref: t,
								onMouseDown: (0, m.m)(e.onMouseDown, (e) => {
									i || 0 !== e.button || !1 !== e.ctrlKey
										? e.preventDefault()
										: l.onValueChange(a);
								}),
								onKeyDown: (0, m.m)(e.onKeyDown, (e) => {
									[" ", "Enter"].includes(e.key) && l.onValueChange(a);
								}),
								onFocus: (0, m.m)(e.onFocus, () => {
									const e = "manual" !== l.activationMode;
									p || i || !e || l.onValueChange(a);
								}),
							}),
						});
					});
				H.displayName = W;
				var X = "TabsContent",
					J = a.forwardRef((e, t) => {
						const {
								__scopeTabs: r,
								value: i,
								forceMount: n,
								children: l,
								...c
							} = e,
							d = L(X, r),
							u = Z(d.baseId, i),
							p = Q(d.baseId, i),
							m = i === d.value,
							x = a.useRef(m);
						return (
							a.useEffect(() => {
								const e = requestAnimationFrame(() => (x.current = !1));
								return () => cancelAnimationFrame(e);
							}, []),
							(0, s.jsx)(M.C, {
								present: n || m,
								children: ({ present: r }) =>
									(0, s.jsx)(o.sG.div, {
										"data-state": m ? "active" : "inactive",
										"data-orientation": d.orientation,
										role: "tabpanel",
										"aria-labelledby": u,
										hidden: !r,
										id: p,
										tabIndex: 0,
										...c,
										ref: t,
										style: {
											...e.style,
											animationDuration: x.current ? "0s" : void 0,
										},
										children: r && l,
									}),
							})
						);
					});
				function Z(e, t) {
					return `${e}-trigger-${t}`;
				}
				function Q(e, t) {
					return `${e}-content-${t}`;
				}
				J.displayName = X;
				const Y = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(K, {
						ref: r,
						className: (0, d.cn)(
							"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",
							e,
						),
						...t,
					}),
				);
				Y.displayName = K.displayName;
				const ee = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(H, {
						ref: r,
						className: (0, d.cn)(
							"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",
							e,
						),
						...t,
					}),
				);
				ee.displayName = H.displayName;
				const et = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(J, {
						ref: r,
						className: (0, d.cn)(
							"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
							e,
						),
						...t,
					}),
				);
				et.displayName = J.displayName;
				var er = r(47311),
					es = r(7678),
					ea = r(1377),
					ei = r(91403),
					en = r(42254),
					eo = r(25097),
					el = r(74645),
					ec = "Dialog",
					[ed, eu] = (0, x.A)(ec),
					[ep, em] = ed(ec),
					ex = (e) => {
						const {
								__scopeDialog: t,
								children: r,
								open: i,
								defaultOpen: n,
								onOpenChange: o,
								modal: l = !0,
							} = e,
							c = a.useRef(null),
							d = a.useRef(null),
							[u = !1, p] = (0, j.i)({ prop: i, defaultProp: n, onChange: o });
						return (0, s.jsx)(ep, {
							scope: t,
							triggerRef: c,
							contentRef: d,
							contentId: (0, g.B)(),
							titleId: (0, g.B)(),
							descriptionId: (0, g.B)(),
							open: u,
							onOpenChange: p,
							onOpenToggle: a.useCallback(() => p((e) => !e), [p]),
							modal: l,
							children: r,
						});
					};
				ex.displayName = ec;
				var ef = "DialogTrigger",
					eh = a.forwardRef((e, t) => {
						const { __scopeDialog: r, ...a } = e,
							i = em(ef, r),
							n = (0, h.s)(t, i.triggerRef);
						return (0, s.jsx)(o.sG.button, {
							type: "button",
							"aria-haspopup": "dialog",
							"aria-expanded": i.open,
							"aria-controls": i.contentId,
							"data-state": eS(i.open),
							...a,
							ref: n,
							onClick: (0, m.m)(e.onClick, i.onOpenToggle),
						});
					});
				eh.displayName = ef;
				var eg = "DialogPortal",
					[ev, ej] = ed(eg, { forceMount: void 0 }),
					eb = (e) => {
						const {
								__scopeDialog: t,
								forceMount: r,
								children: i,
								container: n,
							} = e,
							o = em(eg, t);
						return (0, s.jsx)(ev, {
							scope: t,
							forceMount: r,
							children: a.Children.map(i, (e) =>
								(0, s.jsx)(M.C, {
									present: r || o.open,
									children: (0, s.jsx)(ea.Z, {
										asChild: !0,
										container: n,
										children: e,
									}),
								}),
							),
						});
					};
				eb.displayName = eg;
				var ey = "DialogOverlay",
					eN = a.forwardRef((e, t) => {
						const r = ej(ey, e.__scopeDialog),
							{ forceMount: a = r.forceMount, ...i } = e,
							n = em(ey, e.__scopeDialog);
						return n.modal
							? (0, s.jsx)(M.C, {
									present: a || n.open,
									children: (0, s.jsx)(ew, { ...i, ref: t }),
								})
							: null;
					});
				eN.displayName = ey;
				var ew = a.forwardRef((e, t) => {
						const { __scopeDialog: r, ...a } = e,
							i = em(ey, r);
						return (0, s.jsx)(en.A, {
							as: el.DX,
							allowPinchZoom: !0,
							shards: [i.contentRef],
							children: (0, s.jsx)(o.sG.div, {
								"data-state": eS(i.open),
								...a,
								ref: t,
								style: { pointerEvents: "auto", ...a.style },
							}),
						});
					}),
					ek = "DialogContent",
					eC = a.forwardRef((e, t) => {
						const r = ej(ek, e.__scopeDialog),
							{ forceMount: a = r.forceMount, ...i } = e,
							n = em(ek, e.__scopeDialog);
						return (0, s.jsx)(M.C, {
							present: a || n.open,
							children: n.modal
								? (0, s.jsx)(eD, { ...i, ref: t })
								: (0, s.jsx)(eR, { ...i, ref: t }),
						});
					});
				eC.displayName = ek;
				var eD = a.forwardRef((e, t) => {
						const r = em(ek, e.__scopeDialog),
							i = a.useRef(null),
							n = (0, h.s)(t, r.contentRef, i);
						return (
							a.useEffect(() => {
								const e = i.current;
								if (e) return (0, eo.Eq)(e);
							}, []),
							(0, s.jsx)(eq, {
								...e,
								ref: n,
								trapFocus: r.open,
								disableOutsidePointerEvents: !0,
								onCloseAutoFocus: (0, m.m)(e.onCloseAutoFocus, (e) => {
									e.preventDefault(), r.triggerRef.current?.focus();
								}),
								onPointerDownOutside: (0, m.m)(e.onPointerDownOutside, (e) => {
									const t = e.detail.originalEvent,
										r = 0 === t.button && !0 === t.ctrlKey;
									(2 === t.button || r) && e.preventDefault();
								}),
								onFocusOutside: (0, m.m)(e.onFocusOutside, (e) =>
									e.preventDefault(),
								),
							})
						);
					}),
					eR = a.forwardRef((e, t) => {
						const r = em(ek, e.__scopeDialog),
							i = a.useRef(!1),
							n = a.useRef(!1);
						return (0, s.jsx)(eq, {
							...e,
							ref: t,
							trapFocus: !1,
							disableOutsidePointerEvents: !1,
							onCloseAutoFocus: (t) => {
								e.onCloseAutoFocus?.(t),
									t.defaultPrevented ||
										(i.current || r.triggerRef.current?.focus(),
										t.preventDefault()),
									(i.current = !1),
									(n.current = !1);
							},
							onInteractOutside: (t) => {
								e.onInteractOutside?.(t),
									t.defaultPrevented ||
										((i.current = !0),
										"pointerdown" !== t.detail.originalEvent.type ||
											(n.current = !0));
								const s = t.target;
								r.triggerRef.current?.contains(s) && t.preventDefault(),
									"focusin" === t.detail.originalEvent.type &&
										n.current &&
										t.preventDefault();
							},
						});
					}),
					eq = a.forwardRef((e, t) => {
						const {
								__scopeDialog: r,
								trapFocus: i,
								onOpenAutoFocus: n,
								onCloseAutoFocus: o,
								...l
							} = e,
							c = em(ek, r),
							d = a.useRef(null),
							u = (0, h.s)(t, d);
						return (
							(0, ei.Oh)(),
							(0, s.jsxs)(s.Fragment, {
								children: [
									(0, s.jsx)(es.n, {
										asChild: !0,
										loop: !0,
										trapped: i,
										onMountAutoFocus: n,
										onUnmountAutoFocus: o,
										children: (0, s.jsx)(er.qW, {
											role: "dialog",
											id: c.contentId,
											"aria-describedby": c.descriptionId,
											"aria-labelledby": c.titleId,
											"data-state": eS(c.open),
											...l,
											ref: u,
											onDismiss: () => c.onOpenChange(!1),
										}),
									}),
									(0, s.jsxs)(s.Fragment, {
										children: [
											(0, s.jsx)(eU, { titleId: c.titleId }),
											(0, s.jsx)(eG, {
												contentRef: d,
												descriptionId: c.descriptionId,
											}),
										],
									}),
								],
							})
						);
					}),
					e_ = "DialogTitle",
					eI = a.forwardRef((e, t) => {
						const { __scopeDialog: r, ...a } = e,
							i = em(e_, r);
						return (0, s.jsx)(o.sG.h2, { id: i.titleId, ...a, ref: t });
					});
				eI.displayName = e_;
				var eA = "DialogDescription",
					eF = a.forwardRef((e, t) => {
						const { __scopeDialog: r, ...a } = e,
							i = em(eA, r);
						return (0, s.jsx)(o.sG.p, { id: i.descriptionId, ...a, ref: t });
					});
				eF.displayName = eA;
				var eP = "DialogClose",
					eE = a.forwardRef((e, t) => {
						const { __scopeDialog: r, ...a } = e,
							i = em(eP, r);
						return (0, s.jsx)(o.sG.button, {
							type: "button",
							...a,
							ref: t,
							onClick: (0, m.m)(e.onClick, () => i.onOpenChange(!1)),
						});
					});
				function eS(e) {
					return e ? "open" : "closed";
				}
				eE.displayName = eP;
				var eT = "DialogTitleWarning",
					[eM, eO] = (0, x.q)(eT, {
						contentName: ek,
						titleName: e_,
						docsSlug: "dialog",
					}),
					eU = ({ titleId: e }) => {
						const t = eO(eT),
							r = `\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;
						return (
							a.useEffect(() => {
								e && !document.getElementById(e) && console.error(r);
							}, [r, e]),
							null
						);
					},
					eG = ({ contentRef: e, descriptionId: t }) => {
						const r = eO("DialogDescriptionWarning"),
							s = `Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;
						return (
							a.useEffect(() => {
								const r = e.current?.getAttribute("aria-describedby");
								t && r && !document.getElementById(t) && console.warn(s);
							}, [s, e, t]),
							null
						);
					},
					eB = r(22752);
				const e$ = (0, eB.A)("X", [
						["path", { d: "M18 6 6 18", key: "1bl5f8" }],
						["path", { d: "m6 6 12 12", key: "d8bk6v" }],
					]),
					eL = a.forwardRef(({ className: e, ...t }, r) =>
						(0, s.jsx)(eN, {
							ref: r,
							className: (0, d.cn)(
								"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
								e,
							),
							...t,
						}),
					);
				eL.displayName = eN.displayName;
				const ez = a.forwardRef(({ className: e, children: t, ...r }, a) =>
					(0, s.jsxs)(eb, {
						children: [
							(0, s.jsx)(eL, {}),
							(0, s.jsxs)(eC, {
								ref: a,
								className: (0, d.cn)(
									"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
									e,
								),
								...r,
								children: [
									t,
									(0, s.jsxs)(eE, {
										className:
											"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",
										children: [
											(0, s.jsx)(e$, { className: "h-4 w-4" }),
											(0, s.jsx)("span", {
												className: "sr-only",
												children: "Close",
											}),
										],
									}),
								],
							}),
						],
					}),
				);
				ez.displayName = eC.displayName;
				const eV = ({ className: e, ...t }) =>
					(0, s.jsx)("div", {
						className: (0, d.cn)(
							"flex flex-col space-y-1.5 text-center sm:text-left",
							e,
						),
						...t,
					});
				eV.displayName = "DialogHeader";
				const eK = a.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)(eI, {
						ref: r,
						className: (0, d.cn)(
							"text-lg font-semibold leading-none tracking-tight",
							e,
						),
						...t,
					}),
				);
				(eK.displayName = eI.displayName),
					(a.forwardRef(({ className: e, ...t }, r) =>
						(0, s.jsx)(eF, {
							ref: r,
							className: (0, d.cn)("text-sm text-muted-foreground", e),
							...t,
						}),
					).displayName = eF.displayName);
				var eW = r(72902);
				const eH = (0, eB.A)("User", [
						[
							"path",
							{ d: "M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2", key: "975kel" },
						],
						["circle", { cx: "12", cy: "7", r: "4", key: "17ys0d" }],
					]),
					eX = (0, eB.A)("Briefcase", [
						[
							"path",
							{ d: "M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16", key: "jecpp" },
						],
						[
							"rect",
							{
								width: "20",
								height: "14",
								x: "2",
								y: "6",
								rx: "2",
								key: "i6l2r4",
							},
						],
					]),
					eJ = (0, eB.A)("Shield", [
						[
							"path",
							{
								d: "M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",
								key: "oel41y",
							},
						],
					]);
				var eZ = r(28328),
					eQ = r.n(eZ),
					eY = r(28273),
					e0 = r(7439),
					e1 = r(27130),
					e2 = r(84533);
				function e4() {
					const [e, t] = (0, a.useState)(null),
						[r, o] = (0, a.useState)("creative"),
						[l, c] = (0, a.useState)(""),
						[d, u] = (0, a.useState)(""),
						[m, x] = (0, a.useState)(null),
						f = (0, e0.useRouter)(),
						h = (0, e1.As)(),
						g = (e) => {
							e.preventDefault(),
								"creative" === r
									? f.push("/search-projects")
									: f.push("/search-creatives"),
								t(null);
						},
						v = async (e) => {
							e.preventDefault(), x(null);
							try {
								const e = await (0, e2.eJ)(h, l, d);
								console.log("Signup successful:", e.user),
									t(null),
									c(""),
									u("");
							} catch (e) {
								console.error("Signup error:", e), x(e.message);
							}
						};
					return (0, s.jsxs)("div", {
						className: "min-h-screen bg-white",
						children: [
							(0, s.jsx)("header", {
								className: "border-b border-gray-100",
								children: (0, s.jsxs)("div", {
									className:
										"max-w-7xl mx-auto px-6 py-6 flex items-center justify-between",
									children: [
										(0, s.jsxs)("nav", {
											className: "flex items-center space-x-8",
											children: [
												(0, s.jsx)(eQ(), {
													href: "#",
													className:
														"text-gray-900 hover:text-gray-600 transition-colors",
													children: "Work",
												}),
												(0, s.jsx)(eQ(), {
													href: "#",
													className:
														"text-gray-900 hover:text-gray-600 transition-colors",
													children: "About",
												}),
												(0, s.jsx)(eQ(), {
													href: "#",
													className:
														"text-gray-900 hover:text-gray-600 transition-colors",
													children: "creatives",
												}),
												(0, s.jsx)(eQ(), {
													href: "#",
													className:
														"text-gray-900 hover:text-gray-600 transition-colors",
													children: "Bookers",
												}),
												(0, s.jsx)(eW.A, {
													className:
														"h-5 w-5 text-gray-400 cursor-pointer hover:text-gray-600",
												}),
											],
										}),
										(0, s.jsxs)("div", {
											className: "flex items-center space-x-6",
											children: [
												(0, s.jsx)("span", {
													className: "text-2xl font-light tracking-wide",
													children: "DUA",
												}),
												(0, s.jsxs)("div", {
													className: "flex items-center space-x-4",
													children: [
														(0, s.jsxs)(ex, {
															open: "login" === e,
															onOpenChange: (e) => t(e ? "login" : null),
															children: [
																(0, s.jsx)(eh, {
																	asChild: !0,
																	children: (0, s.jsx)(i.$, {
																		variant: "ghost",
																		className: "text-sm",
																		children: "Login",
																	}),
																}),
																(0, s.jsxs)(ez, {
																	className: "sm:max-w-md",
																	children: [
																		(0, s.jsx)(eV, {
																			children: (0, s.jsx)(eK, {
																				className: "text-center",
																				children: "Welcome back",
																			}),
																		}),
																		(0, s.jsxs)(z, {
																			value: r,
																			onValueChange: (e) => o(e),
																			children: [
																				(0, s.jsxs)(Y, {
																					className: "grid w-full grid-cols-2",
																					children: [
																						(0, s.jsx)(ee, {
																							value: "creative",
																							children: "Artist",
																						}),
																						(0, s.jsx)(ee, {
																							value: "booker",
																							children: "Booker",
																						}),
																					],
																				}),
																				(0, s.jsx)(et, {
																					value: "creative",
																					className: "space-y-4",
																					children: (0, s.jsxs)("form", {
																						onSubmit: g,
																						className: "space-y-4",
																						children: [
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "email",
																										children: "Email",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "email",
																										type: "email",
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "password",
																										children: "Password",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "password",
																										type: "password",
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsx)(i.$, {
																								type: "submit",
																								className: "w-full",
																								children: "Sign In",
																							}),
																						],
																					}),
																				}),
																				(0, s.jsx)(et, {
																					value: "booker",
																					className: "space-y-4",
																					children: (0, s.jsxs)("form", {
																						onSubmit: g,
																						className: "space-y-4",
																						children: [
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "booker-email",
																										children: "Email",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "booker-email",
																										type: "email",
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "booker-password",
																										children: "Password",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "booker-password",
																										type: "password",
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsx)(i.$, {
																								type: "submit",
																								className: "w-full",
																								children: "Sign In",
																							}),
																						],
																					}),
																				}),
																			],
																		}),
																	],
																}),
															],
														}),
														(0, s.jsxs)(ex, {
															open: "signup" === e,
															onOpenChange: (e) => t(e ? "signup" : null),
															children: [
																(0, s.jsx)(eh, {
																	asChild: !0,
																	children: (0, s.jsx)(i.$, {
																		className: "text-sm",
																		children: "Join",
																	}),
																}),
																(0, s.jsxs)(ez, {
																	className: "sm:max-w-md",
																	children: [
																		(0, s.jsx)(eV, {
																			children: (0, s.jsx)(eK, {
																				className: "text-center",
																				children: "Join DUA",
																			}),
																		}),
																		(0, s.jsxs)(z, {
																			value: r,
																			onValueChange: (e) => o(e),
																			children: [
																				(0, s.jsxs)(Y, {
																					className: "grid w-full grid-cols-2",
																					children: [
																						(0, s.jsx)(ee, {
																							value: "creative",
																							children: "Artist",
																						}),
																						(0, s.jsx)(ee, {
																							value: "booker",
																							children: "Booker",
																						}),
																					],
																				}),
																				(0, s.jsx)(et, {
																					value: "creative",
																					className: "space-y-4",
																					children: (0, s.jsxs)("form", {
																						onSubmit: v,
																						className: "space-y-4",
																						children: [
																							(0, s.jsxs)("div", {
																								className:
																									"grid grid-cols-2 gap-4",
																								children: [
																									(0, s.jsxs)("div", {
																										className: "space-y-2",
																										children: [
																											(0, s.jsx)(p, {
																												htmlFor: "first-name",
																												children: "First Name",
																											}),
																											(0, s.jsx)(n.p, {
																												id: "first-name",
																												required: !0,
																											}),
																										],
																									}),
																									(0, s.jsxs)("div", {
																										className: "space-y-2",
																										children: [
																											(0, s.jsx)(p, {
																												htmlFor: "last-name",
																												children: "Last Name",
																											}),
																											(0, s.jsx)(n.p, {
																												id: "last-name",
																												required: !0,
																											}),
																										],
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "creative-email",
																										children: "Email",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "creative-email",
																										type: "email",
																										value: l,
																										onChange: (e) =>
																											c(e.target.value),
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor:
																											"creative-password",
																										children: "Password",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "creative-password",
																										type: "password",
																										value: d,
																										onChange: (e) =>
																											u(e.target.value),
																										required: !0,
																									}),
																								],
																							}),
																							m &&
																								(0, s.jsx)("p", {
																									className:
																										"text-red-500 text-sm",
																									children: m,
																								}),
																							(0, s.jsx)(i.$, {
																								type: "submit",
																								className: "w-full",
																								children: "Create Account",
																							}),
																						],
																					}),
																				}),
																				(0, s.jsx)(et, {
																					value: "booker",
																					className: "space-y-4",
																					children: (0, s.jsxs)("form", {
																						onSubmit: v,
																						className: "space-y-4",
																						children: [
																							(0, s.jsxs)("div", {
																								className:
																									"grid grid-cols-2 gap-4",
																								children: [
																									(0, s.jsxs)("div", {
																										className: "space-y-2",
																										children: [
																											(0, s.jsx)(p, {
																												htmlFor:
																													"booker-first-name",
																												children: "First Name",
																											}),
																											(0, s.jsx)(n.p, {
																												id: "booker-first-name",
																												required: !0,
																											}),
																										],
																									}),
																									(0, s.jsxs)("div", {
																										className: "space-y-2",
																										children: [
																											(0, s.jsx)(p, {
																												htmlFor:
																													"booker-last-name",
																												children: "Last Name",
																											}),
																											(0, s.jsx)(n.p, {
																												id: "booker-last-name",
																												required: !0,
																											}),
																										],
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor: "company",
																										children: "Company",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "company",
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor:
																											"booker-signup-email",
																										children: "Email",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "booker-signup-email",
																										type: "email",
																										value: l,
																										onChange: (e) =>
																											c(e.target.value),
																										required: !0,
																									}),
																								],
																							}),
																							(0, s.jsxs)("div", {
																								className: "space-y-2",
																								children: [
																									(0, s.jsx)(p, {
																										htmlFor:
																											"booker-signup-password",
																										children: "Password",
																									}),
																									(0, s.jsx)(n.p, {
																										id: "booker-signup-password",
																										type: "password",
																										value: d,
																										onChange: (e) =>
																											u(e.target.value),
																										required: !0,
																									}),
																								],
																							}),
																							m &&
																								(0, s.jsx)("p", {
																									className:
																										"text-red-500 text-sm",
																									children: m,
																								}),
																							(0, s.jsx)(i.$, {
																								type: "submit",
																								className: "w-full",
																								children: "Create Account",
																							}),
																						],
																					}),
																				}),
																			],
																		}),
																	],
																}),
															],
														}),
													],
												}),
											],
										}),
									],
								}),
							}),
							(0, s.jsxs)("main", {
								className: "max-w-7xl mx-auto px-6 py-12",
								children: [
									(0, s.jsxs)("div", {
										className: "grid grid-cols-1 lg:grid-cols-3 gap-8 h-[80vh]",
										children: [
											(0, s.jsxs)("div", {
												className:
													"lg:col-span-2 relative group cursor-pointer overflow-hidden",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-gradient-to-br from-slate-900 to-slate-700",
													}),
													(0, s.jsx)("div", {
														className: "absolute inset-0 bg-black/20",
													}),
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-cover bg-center transition-transform duration-700 group-hover:scale-105",
														style: {
															backgroundImage:
																"url('/placeholder.svg?height=600&width=800')",
														},
													}),
													(0, s.jsxs)("div", {
														className:
															"relative h-full flex flex-col justify-end p-12 text-white",
														children: [
															(0, s.jsxs)("h1", {
																className:
																	"text-5xl lg:text-6xl font-light mb-4 leading-tight",
																children: [
																	"New Standard for Creatives,",
																	(0, s.jsx)("br", {}),
																	"Connect",
																	(0, s.jsx)("br", {}),
																	"Create",
																	(0, s.jsx)("br", {}),
																	"Collaborate",
																],
															}),
															(0, s.jsx)("p", {
																className: "text-xl font-light opacity-90",
																children:
																	"The platform for creative professionals",
															}),
															(0, s.jsx)("div", {
																className: "mt-6",
																children: (0, s.jsx)(eY.$n, {
																	children: "Get Started with DUA",
																}),
															}),
														],
													}),
												],
											}),
											(0, s.jsxs)("div", {
												className: "space-y-6",
												children: [
													(0, s.jsxs)("div", {
														className:
															"relative group cursor-pointer overflow-hidden h-64",
														children: [
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-gradient-to-br from-blue-600 to-purple-600",
															}),
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
																style: {
																	backgroundImage:
																		"url('/placeholder.svg?height=300&width=400')",
																},
															}),
															(0, s.jsxs)("div", {
																className:
																	"relative h-full flex flex-col justify-end p-8 text-white",
																children: [
																	(0, s.jsxs)("div", {
																		className: "flex items-center mb-3",
																		children: [
																			(0, s.jsx)(eH, {
																				className: "h-6 w-6 mr-3",
																			}),
																			(0, s.jsx)("span", {
																				className: "text-lg font-medium",
																				children: "For creatives",
																			}),
																		],
																	}),
																	(0, s.jsx)("p", {
																		className:
																			"text-sm opacity-90 leading-relaxed",
																		children:
																			"Showcase your portfolio and connect with opportunities",
																	}),
																],
															}),
														],
													}),
													(0, s.jsxs)("div", {
														className:
															"relative group cursor-pointer overflow-hidden h-64",
														children: [
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-gradient-to-br from-emerald-600 to-teal-600",
															}),
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
																style: {
																	backgroundImage:
																		"url('/placeholder.svg?height=300&width=400')",
																},
															}),
															(0, s.jsxs)("div", {
																className:
																	"relative h-full flex flex-col justify-end p-8 text-white",
																children: [
																	(0, s.jsxs)("div", {
																		className: "flex items-center mb-3",
																		children: [
																			(0, s.jsx)(eX, {
																				className: "h-6 w-6 mr-3",
																			}),
																			(0, s.jsx)("span", {
																				className: "text-lg font-medium",
																				children: "For Bookers",
																			}),
																		],
																	}),
																	(0, s.jsx)("p", {
																		className:
																			"text-sm opacity-90 leading-relaxed",
																		children:
																			"Discover and hire exceptional creative talent",
																	}),
																],
															}),
														],
													}),
													(0, s.jsxs)("div", {
														className:
															"relative group cursor-pointer overflow-hidden h-64",
														children: [
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-gradient-to-br from-amber-600 to-yellow-600",
															}),
															(0, s.jsx)("div", {
																className:
																	"absolute inset-0 bg-cover bg-center opacity-80 transition-transform duration-700 group-hover:scale-105",
																style: {
																	backgroundImage:
																		"url('/placeholder.svg?height=300&width=400')",
																},
															}),
															(0, s.jsxs)("div", {
																className:
																	"relative h-full flex flex-col justify-end p-8 text-white",
																children: [
																	(0, s.jsxs)("div", {
																		className: "flex items-center mb-3",
																		children: [
																			(0, s.jsx)(eJ, {
																				className: "h-6 w-6 mr-3",
																			}),
																			(0, s.jsx)("span", {
																				className: "text-lg font-medium",
																				children: "Payment Guaranteed",
																			}),
																		],
																	}),
																	(0, s.jsx)("p", {
																		className:
																			"text-sm opacity-90 leading-relaxed",
																		children:
																			"Secure payments with full protection for all transactions",
																	}),
																],
															}),
														],
													}),
												],
											}),
										],
									}),
									(0, s.jsxs)("div", {
										className:
											"mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",
										children: [
											(0, s.jsxs)("div", {
												className:
													"relative group cursor-pointer overflow-hidden h-48",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-gradient-to-br from-orange-500 to-red-500",
													}),
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
														style: {
															backgroundImage:
																"url('/placeholder.svg?height=200&width=300')",
														},
													}),
													(0, s.jsxs)("div", {
														className:
															"relative h-full flex flex-col justify-end p-6 text-white",
														children: [
															(0, s.jsx)("h3", {
																className: "text-lg font-medium mb-2",
																children: "Photography",
															}),
															(0, s.jsx)("p", {
																className: "text-sm opacity-90",
																children: "Professional photographers",
															}),
														],
													}),
												],
											}),
											(0, s.jsxs)("div", {
												className:
													"relative group cursor-pointer overflow-hidden h-48",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-gradient-to-br from-pink-500 to-rose-500",
													}),
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
														style: {
															backgroundImage:
																"url('/placeholder.svg?height=200&width=300')",
														},
													}),
													(0, s.jsxs)("div", {
														className:
															"relative h-full flex flex-col justify-end p-6 text-white",
														children: [
															(0, s.jsx)("h3", {
																className: "text-lg font-medium mb-2",
																children: "Design",
															}),
															(0, s.jsx)("p", {
																className: "text-sm opacity-90",
																children: "Creative visual designers",
															}),
														],
													}),
												],
											}),
											(0, s.jsxs)("div", {
												className:
													"relative group cursor-pointer overflow-hidden h-48",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-gradient-to-br from-indigo-500 to-blue-500",
													}),
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
														style: {
															backgroundImage:
																"url('/placeholder.svg?height=200&width=300')",
														},
													}),
													(0, s.jsxs)("div", {
														className:
															"relative h-full flex flex-col justify-end p-6 text-white",
														children: [
															(0, s.jsx)("h3", {
																className: "text-lg font-medium mb-2",
																children: "Video",
															}),
															(0, s.jsx)("p", {
																className: "text-sm opacity-90",
																children: "Video production experts",
															}),
														],
													}),
												],
											}),
											(0, s.jsxs)("div", {
												className:
													"relative group cursor-pointer overflow-hidden h-48",
												children: [
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-gradient-to-br from-violet-500 to-purple-500",
													}),
													(0, s.jsx)("div", {
														className:
															"absolute inset-0 bg-cover bg-center opacity-70 transition-transform duration-700 group-hover:scale-105",
														style: {
															backgroundImage:
																"url('/placeholder.svg?height=200&width=300')",
														},
													}),
													(0, s.jsxs)("div", {
														className:
															"relative h-full flex flex-col justify-end p-6 text-white",
														children: [
															(0, s.jsx)("h3", {
																className: "text-lg font-medium mb-2",
																children: "Content",
															}),
															(0, s.jsx)("p", {
																className: "text-sm opacity-90",
																children: "Content creators & writers",
															}),
														],
													}),
												],
											}),
										],
									}),
								],
							}),
							(0, s.jsx)("footer", {
								className: "border-t border-gray-100 mt-20",
								children: (0, s.jsxs)("div", {
									className: "max-w-7xl mx-auto px-6 py-12",
									children: [
										(0, s.jsxs)("div", {
											className: "grid grid-cols-1 md:grid-cols-4 gap-8",
											children: [
												(0, s.jsxs)("div", {
													children: [
														(0, s.jsx)("h3", {
															className: "font-medium mb-4",
															children: "Platform",
														}),
														(0, s.jsxs)("ul", {
															className: "space-y-2 text-sm text-gray-600",
															children: [
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "How it works",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Pricing",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Success stories",
																	}),
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													children: [
														(0, s.jsx)("h3", {
															className: "font-medium mb-4",
															children: "For creatives",
														}),
														(0, s.jsxs)("ul", {
															className: "space-y-2 text-sm text-gray-600",
															children: [
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Create portfolio",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Find work",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Resources",
																	}),
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													children: [
														(0, s.jsx)("h3", {
															className: "font-medium mb-4",
															children: "For Bookers",
														}),
														(0, s.jsxs)("ul", {
															className: "space-y-2 text-sm text-gray-600",
															children: [
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Find talent",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Post projects",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Hiring guide",
																	}),
																}),
															],
														}),
													],
												}),
												(0, s.jsxs)("div", {
													children: [
														(0, s.jsx)("h3", {
															className: "font-medium mb-4",
															children: "Support",
														}),
														(0, s.jsxs)("ul", {
															className: "space-y-2 text-sm text-gray-600",
															children: [
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Help center",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Contact",
																	}),
																}),
																(0, s.jsx)("li", {
																	children: (0, s.jsx)(eQ(), {
																		href: "#",
																		className: "hover:text-gray-900",
																		children: "Privacy",
																	}),
																}),
															],
														}),
													],
												}),
											],
										}),
										(0, s.jsx)("div", {
											className:
												"border-t border-gray-100 mt-8 pt-8 text-center text-sm text-gray-500",
											children: (0, s.jsx)("p", {
												children: "\xa9 2024 DUA. All rights reserved.",
											}),
										}),
									],
								}),
							}),
							"login" === e &&
								(0, s.jsx)("div", {
									className:
										"fixed inset-0 flex items-center justify-center bg-black/40 z-50",
									children: (0, s.jsxs)("form", {
										onSubmit: g,
										className:
											"bg-white p-8 rounded shadow-lg flex flex-col gap-4 min-w-[300px]",
										children: [
											(0, s.jsx)("h2", {
												className: "text-xl font-light mb-2",
												children: "Sign In",
											}),
											(0, s.jsxs)("div", {
												className: "flex gap-4 mb-2",
												children: [
													(0, s.jsxs)("label", {
														children: [
															(0, s.jsx)("input", {
																type: "radio",
																name: "userType",
																value: "creative",
																checked: "creative" === r,
																onChange: () => o("creative"),
															}),
															"Creative",
														],
													}),
													(0, s.jsxs)("label", {
														children: [
															(0, s.jsx)("input", {
																type: "radio",
																name: "userType",
																value: "booker",
																checked: "booker" === r,
																onChange: () => o("booker"),
															}),
															"Booker",
														],
													}),
												],
											}),
											(0, s.jsx)("input", {
												type: "text",
												placeholder: "Email",
												className: "border p-2 rounded",
												required: !0,
											}),
											(0, s.jsx)("input", {
												type: "password",
												placeholder: "Password",
												className: "border p-2 rounded",
												required: !0,
											}),
											(0, s.jsx)("button", {
												type: "submit",
												className:
													"bg-black text-white py-2 rounded font-light",
												children: "Sign In",
											}),
											(0, s.jsx)("button", {
												type: "button",
												className: "text-xs text-gray-500 mt-2",
												onClick: () => t(null),
												children: "Cancel",
											}),
										],
									}),
								}),
						],
					});
				}
			},
			72902: (e, t, r) => {
				r.d(t, { A: () => s });
				const s = (0, r(22752).A)("Search", [
					["circle", { cx: "11", cy: "11", r: "8", key: "4ej97u" }],
					["path", { d: "m21 21-4.3-4.3", key: "1qie3q" }],
				]);
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77401: (e, t, r) => {
				r.d(t, { cn: () => i });
				var s = r(42366),
					a = r(73927);
				function i(...e) {
					return (0, a.QP)((0, s.$)(e));
				}
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			81630: (e) => {
				e.exports = require("http");
			},
			91645: (e) => {
				e.exports = require("net");
			},
			94735: (e) => {
				e.exports = require("events");
			},
		});
	var t = require("../webpack-runtime.js");
	t.C(e);
	var r = (e) => t((t.s = e)),
		s = t.X(0, [959, 40, 482, 202], () => r(1183));
	module.exports = s;
})();
