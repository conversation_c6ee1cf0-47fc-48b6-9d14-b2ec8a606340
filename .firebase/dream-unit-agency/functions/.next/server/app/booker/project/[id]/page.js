(() => {
	var e = {};
	(e.id = 483),
		(e.ids = [483]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16213: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 82805));
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			20625: (e, t, r) => {
				r.r(t),
					r.d(t, {
						GlobalError: () => l.a,
						__next_app__: () => x,
						pages: () => c,
						routeModule: () => u,
						tree: () => o,
					});
				var s = r(7025),
					i = r(18198),
					a = r(82576),
					l = r.n(a),
					n = r(45239),
					d = {};
				for (const e in n)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (d[e] = () => n[e]);
				r.d(t, d);
				const o = {
						children: [
							"",
							{
								children: [
									"booker",
									{
										children: [
											"project",
											{
												children: [
													"[id]",
													{
														children: [
															"__PAGE__",
															{},
															{
																page: [
																	() =>
																		Promise.resolve().then(r.bind(r, 32040)),
																	"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/project/[id]/page.tsx",
																],
															},
														],
													},
													{},
												],
											},
											{},
										],
									},
									{},
								],
							},
							{
								layout: [
									() => Promise.resolve().then(r.bind(r, 59650)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
								],
								loading: [
									() => Promise.resolve().then(r.bind(r, 34314)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
								],
								"not-found": [
									() => Promise.resolve().then(r.t.bind(r, 4540, 23)),
									"next/dist/client/components/not-found-error",
								],
								forbidden: [
									() => Promise.resolve().then(r.t.bind(r, 53117, 23)),
									"next/dist/client/components/forbidden-error",
								],
								unauthorized: [
									() => Promise.resolve().then(r.t.bind(r, 6874, 23)),
									"next/dist/client/components/unauthorized-error",
								],
							},
						],
					}.children,
					c = [
						"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/project/[id]/page.tsx",
					],
					x = { require: r, loadChunk: () => Promise.resolve() },
					u = new s.AppPageRouteModule({
						definition: {
							kind: i.RouteKind.APP_PAGE,
							page: "/booker/project/[id]/page",
							pathname: "/booker/project/[id]",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: o },
					});
			},
			21820: (e) => {
				e.exports = require("os");
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			32040: (e, t, r) => {
				r.r(t), r.d(t, { default: () => s });
				const s = (0, r(51129).registerClientReference)(
					() => {
						throw Error(
							"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/project/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
						);
					},
					"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/project/[id]/page.tsx",
					"default",
				);
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			74357: (e, t, r) => {
				Promise.resolve().then(r.bind(r, 32040));
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			81630: (e) => {
				e.exports = require("http");
			},
			82805: (e, t, r) => {
				r.r(t), r.d(t, { default: () => w });
				var s = r(45781),
					i = r(13072),
					a = r(3704),
					l = r(28328),
					n = r.n(l),
					d = r(7439),
					o = r(22752);
				const c = (0, o.A)("ArrowLeft", [
						["path", { d: "m12 19-7-7 7-7", key: "1l729n" }],
						["path", { d: "M19 12H5", key: "x3x0zl" }],
					]),
					x = (0, o.A)("CircleX", [
						["circle", { cx: "12", cy: "12", r: "10", key: "1mglay" }],
						["path", { d: "m15 9-6 6", key: "1uzhvr" }],
						["path", { d: "m9 9 6 6", key: "z0biqf" }],
					]);
				var u = r(27095),
					p = r(44238),
					g = r(14132),
					h = r(77401);
				const m = i.forwardRef(({ className: e, ...t }, r) =>
					(0, s.jsx)("textarea", {
						className: (0, h.cn)(
							"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
							e,
						),
						ref: r,
						...t,
					}),
				);
				m.displayName = "Textarea";
				var v = r(48414),
					f = r(38653),
					b = r(1437),
					j = r(79231);
				const y = (e) => {
						if (!e) return "bg-gray-100 text-gray-800";
						switch (e.toLowerCase()) {
							case "active":
							case "confirmed":
							case "completed":
							case "accepted":
								return "bg-green-100 text-green-800";
							case "pending":
								return "bg-yellow-100 text-yellow-800";
							case "needed":
								return "bg-orange-100 text-orange-800";
							case "declined":
							case "cancelled":
								return "bg-red-100 text-red-800";
							case "draft":
							case "planning":
								return "bg-blue-100 text-blue-800";
							default:
								return "bg-gray-100 text-gray-800";
						}
					},
					N = [
						"Photography",
						"Videography",
						"Event Coverage",
						"Social Media Content",
						"Brand Identity",
						"Music Video Production",
						"Other",
					],
					w = ({ params: e }) => {
						const t = (0, d.useRouter)(),
							r = e.id,
							[l, o] = (0, i.useState)(() => f.R4.find((e) => e.id === r)),
							[h, w] = (0, i.useState)(!1),
							[k, C] = (0, i.useState)({}),
							[q, O] = (0, i.useState)(""),
							S = (0, i.useCallback)((e, t, r) => {
								const s = r.filter(
									(r) =>
										r.projectTitle === t &&
										r.projectType
											.toLowerCase()
											.includes(e.toLowerCase().split(" ")[0]),
								);
								return s.length > 0
									? `$${Math.min(...s.map((e) => e.amount)).toLocaleString()} - $${Math.max(...s.map((e) => e.amount)).toLocaleString()}`
									: "-";
							}, []),
							_ = (0, i.useCallback)((e, t, r) => {
								const s = r.filter(
										(r) =>
											r.projectTitle === t &&
											r.projectType
												.toLowerCase()
												.includes(e.toLowerCase().split(" ")[0]),
									),
									i = s.filter((e) => "accepted" === e.status),
									a = s.filter((e) => "pending" === e.status),
									l = s.filter((e) => "declined" === e.status);
								return i.length > 0
									? "Confirmed"
									: a.length > 0
										? "Pending"
										: l.length > 0 && s.length === l.length
											? "Declined"
											: "Needed";
							}, []);
						(0, i.useEffect)(() => {
							const e = f.R4.find((e) => e.id === r);
							o(e),
								e
									? C({
											...e,
											budget: e.budget.toString(),
											creativesNeeded: [...(e.creativesNeeded || [])],
											roleBudgetOverrides: { ...(e.roleBudgetOverrides || {}) },
											roleStatusOverrides: { ...(e.roleStatusOverrides || {}) },
										})
									: w(!1);
						}, [r]);
						const D = (e, t) => {
								C((r) => ({ ...r, [e]: t }));
							},
							P = (e, t) => {
								("" === t || (!isNaN(Number(t)) && Number(t) >= 0)) &&
									C((r) => ({ ...r, [e]: t }));
							},
							$ = (e) => {
								const t = (k.creativesNeeded || [])[e];
								C((r) => {
									const s = { ...(r.roleBudgetOverrides || {}) };
									delete s[t];
									const i = { ...(r.roleStatusOverrides || {}) };
									return (
										delete i[t],
										{
											...r,
											creativesNeeded: (r.creativesNeeded || []).filter(
												(t, r) => r !== e,
											),
											roleBudgetOverrides: s,
											roleStatusOverrides: i,
										}
									);
								});
							},
							B = (e, t) => {
								C((r) => ({
									...r,
									roleBudgetOverrides: {
										...(r.roleBudgetOverrides || {}),
										[e]: t,
									},
								}));
							},
							A = (e, t) => {
								C((r) => ({
									...r,
									roleStatusOverrides: {
										...(r.roleStatusOverrides || {}),
										[e]: t,
									},
								}));
							};
						return l
							? (h ? k.budget : l.budget.toString(),
								(0, s.jsxs)("div", {
									className: "min-h-screen bg-white",
									children: [
										(0, s.jsx)(b.A, {}),
										(0, s.jsx)(j.A, {
											children: (0, s.jsxs)("div", {
												className: "py-8",
												children: [
													(0, s.jsxs)("div", {
														className: "flex justify-between items-center mb-6",
														children: [
															(0, s.jsxs)(p.$, {
																onClick: () => t.back(),
																variant: "outline",
																className: "font-light tracking-wide",
																children: [
																	(0, s.jsx)(c, { className: "w-4 h-4 mr-2" }),
																	" Back to Profile",
																],
															}),
															h
																? (0, s.jsxs)("div", {
																		className: "flex gap-3",
																		children: [
																			(0, s.jsx)(p.$, {
																				onClick: () => {
																					w(!1),
																						l &&
																							C({
																								...l,
																								budget: l.budget.toString(),
																								creativesNeeded: [
																									...(l.creativesNeeded || []),
																								],
																								roleBudgetOverrides: {
																									...(l.roleBudgetOverrides ||
																										{}),
																								},
																								roleStatusOverrides: {
																									...(l.roleStatusOverrides ||
																										{}),
																								},
																							});
																				},
																				variant: "outline",
																				className: "font-light tracking-wide",
																				children: "Cancel",
																			}),
																			(0, s.jsx)(p.$, {
																				onClick: () => {
																					if (!l || !k) return;
																					const e = {
																						...l,
																						title: k.title || l.title,
																						client: k.client || l.client,
																						budget: k.budget
																							? Number.parseFloat(k.budget)
																							: l.budget,
																						projectType:
																							k.projectType || l.projectType,
																						startDate:
																							k.startDate || l.startDate,
																						endDate: k.endDate || l.endDate,
																						location: k.location || l.location,
																						description:
																							k.description || l.description,
																						creativesNeeded:
																							k.creativesNeeded ||
																							l.creativesNeeded,
																						roleBudgetOverrides:
																							k.roleBudgetOverrides ||
																							l.roleBudgetOverrides,
																						roleStatusOverrides:
																							k.roleStatusOverrides ||
																							l.roleStatusOverrides,
																					};
																					o(e);
																					const t = f.R4.findIndex(
																						(e) => e.id === r,
																					);
																					-1 !== t && (f.R4[t] = e), w(!1);
																				},
																				variant: "default",
																				className:
																					"font-light tracking-wide bg-green-600 hover:bg-green-700 text-white",
																				children: "Save Changes",
																			}),
																		],
																	})
																: (0, s.jsx)(p.$, {
																		onClick: () => {
																			if (!h && l) {
																				const e = l.creativesNeeded || [],
																					t = {},
																					r = {};
																				e.forEach((e) => {
																					(t[e] =
																						l.roleBudgetOverrides?.[e] ??
																						S(e, l.title, f.Ex)),
																						(r[e] =
																							l.roleStatusOverrides?.[e] ??
																							_(e, l.title, f.Ex));
																				}),
																					C({
																						...l,
																						budget: l.budget.toString(),
																						creativesNeeded: [...e],
																						roleBudgetOverrides: t,
																						roleStatusOverrides: r,
																					});
																			} else
																				h &&
																					l &&
																					C({
																						...l,
																						budget: l.budget.toString(),
																						creativesNeeded: [
																							...(l.creativesNeeded || []),
																						],
																						roleBudgetOverrides: {
																							...(l.roleBudgetOverrides || {}),
																						},
																						roleStatusOverrides: {
																							...(l.roleStatusOverrides || {}),
																						},
																					});
																			w(!h);
																		},
																		variant: "default",
																		className:
																			"font-light tracking-wide bg-black text-white hover:bg-gray-800",
																		children: "Edit Project",
																	}),
														],
													}),
													(0, s.jsxs)("div", {
														className:
															"bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row",
														children: [
															(0, s.jsx)("div", {
																className:
																	"w-full md:w-1/3 relative min-h-[300px] md:min-h-0 bg-gray-100",
																children: (0, s.jsx)(a.default, {
																	src: l.src || "/placeholder.svg",
																	alt: l.alt || l.title,
																	fill: !0,
																	className: "object-cover",
																}),
															}),
															(0, s.jsxs)("div", {
																className: "flex-1 flex flex-col",
																children: [
																	(0, s.jsx)("div", {
																		className:
																			"flex items-center justify-between p-6 border-b border-gray-200",
																		children: h
																			? (0, s.jsx)(g.p, {
																					value: k.title || "",
																					onChange: (e) =>
																						D("title", e.target.value),
																					placeholder: "Project Title",
																					className:
																						"text-2xl font-light tracking-wide text-gray-800 border-b-2 border-gray-300 focus:border-blue-500 py-1",
																				})
																			: (0, s.jsx)("h1", {
																					className:
																						"text-2xl font-light tracking-wide text-gray-800",
																					children: l.title,
																				}),
																	}),
																	(0, s.jsxs)("div", {
																		className:
																			"flex-1 p-6 overflow-y-auto space-y-5",
																		children: [
																			(0, s.jsx)("div", {
																				children: (0, s.jsx)("span", {
																					className: `px-3 py-1 text-xs font-light tracking-wide rounded ${"active" === l.status ? "bg-blue-100 text-blue-800" : "completed" === l.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}
                  `,
																					children:
																						l.status.charAt(0).toUpperCase() +
																						l.status.slice(1),
																				}),
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Client",
																					}),
																					h
																						? (0, s.jsx)(g.p, {
																								value: k.client || "",
																								onChange: (e) =>
																									D("client", e.target.value),
																								placeholder: "Client Name",
																								className: "font-light",
																							})
																						: (0, s.jsx)("p", {
																								className:
																									"font-light text-gray-700",
																								children: l.client,
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Budget (USD)",
																					}),
																					h
																						? (0, s.jsx)(g.p, {
																								type: "text",
																								value: k.budget || "",
																								onChange: (e) =>
																									P("budget", e.target.value),
																								placeholder: "15000",
																								className: "font-light",
																							})
																						: (0, s.jsxs)("p", {
																								className:
																									"font-light text-gray-700",
																								children: [
																									"$",
																									l.budget.toLocaleString(),
																								],
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Project Type",
																					}),
																					h
																						? (0, s.jsxs)(v.l6, {
																								value: k.projectType || "",
																								onValueChange: (e) =>
																									D("projectType", e),
																								children: [
																									(0, s.jsx)(v.bq, {
																										className: "font-light",
																										children: (0, s.jsx)(v.yv, {
																											placeholder:
																												"Select Type",
																										}),
																									}),
																									(0, s.jsx)(v.gC, {
																										children: N.map((e) =>
																											(0, s.jsx)(
																												v.eb,
																												{
																													value: e,
																													children: e,
																												},
																												e,
																											),
																										),
																									}),
																								],
																							})
																						: (0, s.jsx)("p", {
																								className:
																									"font-light text-gray-700",
																								children: l.projectType,
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Timeline",
																					}),
																					h
																						? (0, s.jsxs)("div", {
																								className:
																									"flex gap-4 items-center",
																								children: [
																									(0, s.jsx)(g.p, {
																										type: "date",
																										value: k.startDate || "",
																										onChange: (e) =>
																											D(
																												"startDate",
																												e.target.value,
																											),
																										className: "font-light",
																									}),
																									(0, s.jsx)("span", {
																										children: "to",
																									}),
																									(0, s.jsx)(g.p, {
																										type: "date",
																										value: k.endDate || "",
																										onChange: (e) =>
																											D(
																												"endDate",
																												e.target.value,
																											),
																										className: "font-light",
																									}),
																								],
																							})
																						: (0, s.jsxs)("p", {
																								className:
																									"font-light text-gray-700",
																								children: [
																									l.startDate,
																									l.endDate &&
																									l.endDate !== l.startDate
																										? ` to ${l.endDate}`
																										: "",
																								],
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Location",
																					}),
																					h
																						? (0, s.jsx)(g.p, {
																								value: k.location || "",
																								onChange: (e) =>
																									D("location", e.target.value),
																								placeholder:
																									"e.g., Downtown Studio, Los Angeles",
																								className: "font-light",
																							})
																						: (0, s.jsx)("p", {
																								className:
																									"font-light text-gray-700",
																								children: l.location,
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-sm font-light text-gray-500 tracking-wide mb-1",
																						children: "Description",
																					}),
																					h
																						? (0, s.jsx)(m, {
																								value: k.description || "",
																								onChange: (e) =>
																									D(
																										"description",
																										e.target.value,
																									),
																								placeholder:
																									"Project description...",
																								className:
																									"font-light min-h-[100px]",
																							})
																						: (0, s.jsx)("p", {
																								className:
																									"text-gray-700 font-light whitespace-pre-wrap",
																								children: l.description,
																							}),
																				],
																			}),
																			(0, s.jsxs)("div", {
																				className:
																					"border-t border-gray-200 pt-5 mt-5",
																				children: [
																					(0, s.jsx)("h3", {
																						className:
																							"text-base font-light text-gray-600 tracking-wide mb-4",
																						children: "Project Staffing Status",
																					}),
																					h &&
																						(0, s.jsxs)("div", {
																							className:
																								"mb-4 p-3 border rounded-md bg-gray-50",
																							children: [
																								(0, s.jsx)("h4", {
																									className:
																										"text-sm font-medium text-gray-700 mb-2",
																									children: "Edit Roles:",
																								}),
																								(k.creativesNeeded || []).map(
																									(e, t) =>
																										(0, s.jsxs)(
																											"div",
																											{
																												className:
																													"flex items-center justify-between mb-2 p-2 border rounded bg-white",
																												children: [
																													(0, s.jsx)("span", {
																														className:
																															"text-sm text-gray-800",
																														children: e,
																													}),
																													(0, s.jsxs)(p.$, {
																														variant: "ghost",
																														size: "sm",
																														onClick: () => $(t),
																														className:
																															"text-red-500 hover:text-red-700",
																														children: [
																															(0, s.jsx)(x, {
																																className:
																																	"w-4 h-4 mr-1",
																															}),
																															" Remove",
																														],
																													}),
																												],
																											},
																											t,
																										),
																								),
																								(0, s.jsxs)("div", {
																									className:
																										"flex items-center gap-2 mt-3",
																									children: [
																										(0, s.jsx)(g.p, {
																											value: q,
																											onChange: (e) =>
																												O(e.target.value),
																											placeholder:
																												"New Role Name",
																											className: "flex-grow",
																										}),
																										(0, s.jsxs)(p.$, {
																											onClick: () => {
																												if ("" !== q.trim()) {
																													const e = q.trim();
																													C((t) => ({
																														...t,
																														creativesNeeded: [
																															...(t.creativesNeeded ||
																																[]),
																															e,
																														],
																														roleBudgetOverrides:
																															{
																																...(t.roleBudgetOverrides ||
																																	{}),
																																[e]: "-",
																															},
																														roleStatusOverrides:
																															{
																																...(t.roleStatusOverrides ||
																																	{}),
																																[e]: "Needed",
																															},
																													})),
																														O("");
																												}
																											},
																											variant: "outline",
																											size: "sm",
																											children: [
																												(0, s.jsx)(u.A, {
																													className:
																														"w-4 h-4 mr-1",
																												}),
																												" Add Role",
																											],
																										}),
																									],
																								}),
																							],
																						}),
																					(0, s.jsx)("div", {
																						className: "overflow-x-auto",
																						children: (0, s.jsxs)("table", {
																							className: "w-full text-sm",
																							children: [
																								(0, s.jsx)("thead", {
																									className:
																										"border-b border-gray-200",
																									children: (0, s.jsxs)("tr", {
																										children: [
																											(0, s.jsx)("th", {
																												className:
																													"text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																												children: "Role",
																											}),
																											(0, s.jsx)("th", {
																												className:
																													"text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																												children: "Offers Sent",
																											}),
																											(0, s.jsx)("th", {
																												className:
																													"text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																												children:
																													"Budget Range",
																											}),
																											(0, s.jsx)("th", {
																												className:
																													"text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide",
																												children: "Status",
																											}),
																										],
																									}),
																								}),
																								(0, s.jsxs)("tbody", {
																									className:
																										"divide-y divide-gray-100",
																									children: [
																										(h
																											? k.creativesNeeded || []
																											: l.creativesNeeded || []
																										).map((e, t) => {
																											let r, i;
																											h
																												? ((r =
																														k
																															.roleBudgetOverrides?.[
																															e
																														] || "-"),
																													k
																														.roleStatusOverrides?.[
																														e
																													])
																												: ((r =
																														l
																															.roleBudgetOverrides?.[
																															e
																														] ??
																														S(
																															e,
																															l.title,
																															f.Ex,
																														)),
																													l
																														.roleStatusOverrides?.[
																														e
																													] ??
																														_(
																															e,
																															l.title,
																															f.Ex,
																														));
																											const a = f.Ex.filter(
																												(t) =>
																													t.projectTitle ===
																														l.title &&
																													t.projectType
																														.toLowerCase()
																														.includes(
																															e
																																.toLowerCase()
																																.split(" ")[0],
																														),
																											);
																											return (0, s.jsxs)(
																												"tr",
																												{
																													className:
																														"hover:bg-gray-50 transition-colors",
																													children: [
																														(0, s.jsx)("td", {
																															className:
																																"py-3 px-2 font-light text-gray-900 whitespace-nowrap",
																															children: e,
																														}),
																														(0, s.jsx)("td", {
																															className:
																																"py-3 px-2",
																															children: (0,
																															s.jsx)("div", {
																																className:
																																	"flex flex-wrap gap-1",
																																children:
																																	a.length > 0
																																		? a.map(
																																				(
																																					e,
																																					t,
																																				) =>
																																					(0,
																																					s.jsxs)(
																																						n(),
																																						{
																																							href: `/creative/${e.artistId}`,
																																							className:
																																								"flex items-center gap-1 bg-gray-100 hover:bg-gray-200 rounded-full px-2 py-1 border border-gray-200 transition-colors cursor-pointer",
																																							children:
																																								[
																																									(0,
																																									s.jsx)(
																																										"div",
																																										{
																																											className:
																																												"w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center overflow-hidden",
																																											children:
																																												(0,
																																												s.jsx)(
																																													"span",
																																													{
																																														className:
																																															"text-[10px] font-light text-gray-600",
																																														children:
																																															e.artistName
																																																.split(
																																																	" ",
																																																)
																																																.map(
																																																	(
																																																		e,
																																																	) =>
																																																		e[0],
																																																)
																																																.join(
																																																	"",
																																																),
																																													},
																																												),
																																										},
																																									),
																																									(0,
																																									s.jsx)(
																																										"span",
																																										{
																																											className:
																																												"text-xs text-gray-700 mr-1 font-light",
																																											children:
																																												e.artistName.split(
																																													" ",
																																												)[0],
																																										},
																																									),
																																									(0,
																																									s.jsx)(
																																										"span",
																																										{
																																											className: `px-1.5 py-0.5 text-[10px] font-light rounded-full ${"accepted" === e.status ? "bg-green-500 text-white" : "declined" === e.status ? "bg-red-500 text-white" : "expired" === e.status ? "bg-gray-500 text-white" : "bg-yellow-500 text-white"}
                                            `,
																																											children:
																																												"accepted" ===
																																												e.status
																																													? "✓"
																																													: "declined" ===
																																															e.status
																																														? "✗"
																																														: "expired" ===
																																																e.status
																																															? "⏰"
																																															: "⏳",
																																										},
																																									),
																																								],
																																						},
																																						t,
																																					),
																																			)
																																		: (0,
																																			s.jsxs)(
																																				n(),
																																				{
																																					href: `/?role=${encodeURIComponent(e)}&project=${encodeURIComponent(l.title)}&location=${encodeURIComponent(l.location || "")}&budget=${l.budget}&startDate=${l.startDate || ""}&endDate=${l.endDate || ""}`,
																																					className:
																																						"inline-flex items-center gap-1 bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 rounded px-3 py-1 border border-blue-200 transition-colors text-xs font-light tracking-wide",
																																					children:
																																						[
																																							(0,
																																							s.jsx)(
																																								"svg",
																																								{
																																									className:
																																										"w-3 h-3",
																																									fill: "none",
																																									stroke:
																																										"currentColor",
																																									viewBox:
																																										"0 0 24 24",
																																									children:
																																										(0,
																																										s.jsx)(
																																											"path",
																																											{
																																												strokeLinecap:
																																													"round",
																																												strokeLinejoin:
																																													"round",
																																												strokeWidth: 2,
																																												d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
																																											},
																																										),
																																								},
																																							),
																																							"Search ",
																																							e.toUpperCase(),
																																						],
																																				},
																																			),
																															}),
																														}),
																														(0, s.jsx)("td", {
																															className:
																																"py-3 px-2 text-right font-light text-gray-900 whitespace-nowrap",
																															children: h
																																? (0, s.jsx)(
																																		g.p,
																																		{
																																			value:
																																				k
																																					.roleBudgetOverrides?.[
																																					e
																																				] || "",
																																			onChange:
																																				(t) =>
																																					B(
																																						e,
																																						t
																																							.target
																																							.value,
																																					),
																																			placeholder:
																																				"e.g., $1k-$1.5k or -",
																																			className:
																																				"font-light text-sm py-1 px-2 min-w-[120px] text-right",
																																		},
																																	)
																																: r,
																														}),
																														(0, s.jsx)("td", {
																															className:
																																"px-6 py-4 whitespace-nowrap text-sm",
																															children: h
																																? (0, s.jsxs)(
																																		v.l6,
																																		{
																																			value:
																																				k
																																					.roleStatusOverrides?.[
																																					e
																																				] ||
																																				"Needed",
																																			onValueChange:
																																				(t) =>
																																					A(
																																						e,
																																						t,
																																					),
																																			children:
																																				[
																																					(0,
																																					s.jsx)(
																																						v.bq,
																																						{
																																							className:
																																								"w-full text-xs",
																																							children:
																																								(0,
																																								s.jsx)(
																																									v.yv,
																																									{},
																																								),
																																						},
																																					),
																																					(0,
																																					s.jsxs)(
																																						v.gC,
																																						{
																																							children:
																																								[
																																									(0,
																																									s.jsx)(
																																										v.eb,
																																										{
																																											value:
																																												"Needed",
																																											children:
																																												"Needed",
																																										},
																																									),
																																									(0,
																																									s.jsx)(
																																										v.eb,
																																										{
																																											value:
																																												"Pending",
																																											children:
																																												"Pending",
																																										},
																																									),
																																									(0,
																																									s.jsx)(
																																										v.eb,
																																										{
																																											value:
																																												"Confirmed",
																																											children:
																																												"Confirmed",
																																										},
																																									),
																																									(0,
																																									s.jsx)(
																																										v.eb,
																																										{
																																											value:
																																												"Declined",
																																											children:
																																												"Declined",
																																										},
																																									),
																																								],
																																						},
																																					),
																																				],
																																		},
																																	)
																																: (() => {
																																		const t =
																																			l
																																				.roleStatusOverrides?.[
																																				e
																																			] ||
																																			_(
																																				e,
																																				l.title,
																																				f.Ex,
																																			);
																																		return "Needed" ===
																																			t
																																			? (0,
																																				s.jsx)(
																																					p.$,
																																					{
																																						variant:
																																							"outline",
																																						size: "sm",
																																						onClick:
																																							() =>
																																								console.log(
																																									`Apply button clicked for ${e}`,
																																								),
																																						children:
																																							"Apply",
																																					},
																																				)
																																			: (0,
																																				s.jsx)(
																																					"span",
																																					{
																																						className: `px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${y(t)}`,
																																						children:
																																							t,
																																					},
																																				);
																																	})(),
																														}),
																													],
																												},
																												e + t,
																											);
																										}),
																										0 ===
																											(h
																												? k.creativesNeeded ||
																													[]
																												: l.creativesNeeded ||
																													[]
																											).length &&
																											(0, s.jsx)("tr", {
																												children: (0, s.jsxs)(
																													"td",
																													{
																														colSpan: 4,
																														className:
																															"py-6 text-center text-gray-500 font-light",
																														children: [
																															"No creative requirements specified. ",
																															h &&
																																"Add roles using the form above.",
																														],
																													},
																												),
																											}),
																									],
																								}),
																							],
																						}),
																					}),
																					(0, s.jsxs)("div", {
																						className:
																							"mt-4 flex justify-between text-xs text-gray-500 font-light",
																						children: [
																							(0, s.jsxs)("span", {
																								children: [
																									h
																										? k.creativesNeeded
																												?.length || 0
																										: l.creativesNeeded
																												?.length || 0,
																									" roles needed",
																								],
																							}),
																							(0, s.jsxs)("span", {
																								children: [
																									f.Ex.filter(
																										(e) =>
																											e.projectTitle ===
																											l.title,
																									).length,
																									" offers sent",
																								],
																							}),
																						],
																					}),
																				],
																			}),
																		],
																	}),
																],
															}),
														],
													}),
												],
											}),
										}),
									],
								}))
							: (0, s.jsxs)(s.Fragment, {
									children: [
										(0, s.jsx)(b.A, {}),
										(0, s.jsx)(j.A, {
											children: (0, s.jsxs)("div", {
												className: "text-center py-10",
												children: [
													(0, s.jsx)("h1", {
														className: "text-2xl font-light mb-4",
														children: "Project Not Found",
													}),
													(0, s.jsxs)(p.$, {
														onClick: () => t.back(),
														variant: "outline",
														children: [
															(0, s.jsx)(c, { className: "w-4 h-4 mr-2" }),
															" Go Back",
														],
													}),
												],
											}),
										}),
									],
								});
					};
			},
			91645: (e) => {
				e.exports = require("net");
			},
			94735: (e) => {
				e.exports = require("events");
			},
		});
	var t = require("../../../../webpack-runtime.js");
	t.C(e);
	var r = (e) => t((t.s = e)),
		s = t.X(0, [959, 40, 482, 704, 227, 202, 577], () => r(20625));
	module.exports = s;
})();
