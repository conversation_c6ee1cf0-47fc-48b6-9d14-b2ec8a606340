globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/booker/[id]/page"] = {
	moduleLoading: { prefix: "/_next/", crossOrigin: null },
	ssrModuleMapping: {
		326: { "*": { id: "18946", name: "*", chunks: [], async: false } },
		567: { "*": { id: "98349", name: "*", chunks: [], async: false } },
		602: { "*": { id: "68230", name: "*", chunks: [], async: false } },
		1063: { "*": { id: "31281", name: "*", chunks: [], async: false } },
		1483: { "*": { id: "4947", name: "*", chunks: [], async: false } },
		1954: { "*": { id: "74178", name: "*", chunks: [], async: false } },
		5125: { "*": { id: "97857", name: "*", chunks: [], async: false } },
		5234: { "*": { id: "60140", name: "*", chunks: [], async: false } },
		6990: { "*": { id: "6801", name: "*", chunks: [], async: false } },
		7812: { "*": { id: "72504", name: "*", chunks: [], async: false } },
		7939: { "*": { id: "93833", name: "*", chunks: [], async: false } },
		8619: { "*": { id: "6229", name: "*", chunks: [], async: false } },
		8672: { "*": { id: "35499", name: "*", chunks: [], async: false } },
		8819: { "*": { id: "82805", name: "*", chunks: [], async: false } },
		9233: { "*": { id: "23751", name: "*", chunks: [], async: false } },
	},
	edgeSSRModuleMapping: {},
	clientModules: {
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-page.js":
			{ id: 5234, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-page.js":
			{ id: 5234, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/client-segment.js":
			{ id: 326, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/client-segment.js":
			{ id: 326, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/error-boundary.js":
			{ id: 1954, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/error-boundary.js":
			{ id: 1954, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
			{ id: 8619, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":
			{ id: 8619, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/layout-router.js":
			{ id: 1063, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/layout-router.js":
			{ id: 1063, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/async-metadata.js":
			{ id: 7939, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":
			{ id: 7939, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/metadata/metadata-boundary.js":
			{ id: 5125, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":
			{ id: 5125, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/render-from-template-context.js":
			{ id: 1483, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/client/components/render-from-template-context.js":
			{ id: 1483, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx":
			{
				id: 6990,
				name: "*",
				chunks: [
					"113",
					"static/chunks/3f9dfec0-c564d8602981a34a.js",
					"315",
					"static/chunks/956a9536-1b2259b3ea716422.js",
					"85",
					"static/chunks/9d0843fd-d387c6148dee1e67.js",
					"475",
					"static/chunks/475-58eebc13c6087e31.js",
					"442",
					"static/chunks/442-9de5845d8af31acf.js",
					"737",
					"static/chunks/737-0d6c00b0f45554b2.js",
					"177",
					"static/chunks/app/layout-10085dbe898e628a.js",
				],
				async: false,
			},
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/search-creatives.tsx":
			{ id: 9233, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/booker-profile-page.tsx":
			{
				id: 8672,
				name: "*",
				chunks: [
					"874",
					"static/chunks/874-5e86250f2c0178ab.js",
					"637",
					"static/chunks/637-572dd5f989023b8a.js",
					"186",
					"static/chunks/186-0c24d93d208105d6.js",
					"398",
					"static/chunks/398-10e1d7b1cc473242.js",
					"752",
					"static/chunks/752-1f91c60301b23dcc.js",
					"285",
					"static/chunks/285-ac916553db7f97b3.js",
					"665",
					"static/chunks/app/booker/%5Bid%5D/page-6274bf851ef7d0f1.js",
				],
				async: false,
			},
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/creative-profile-page.tsx":
			{ id: 602, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/project/[id]/page.tsx":
			{ id: 8819, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/search-projects/page.tsx":
			{ id: 567, name: "*", chunks: [], async: false },
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/landing-page.tsx":
			{
				id: 7812,
				name: "*",
				chunks: [
					"113",
					"static/chunks/3f9dfec0-c564d8602981a34a.js",
					"315",
					"static/chunks/956a9536-1b2259b3ea716422.js",
					"85",
					"static/chunks/9d0843fd-d387c6148dee1e67.js",
					"874",
					"static/chunks/874-5e86250f2c0178ab.js",
					"637",
					"static/chunks/637-572dd5f989023b8a.js",
					"475",
					"static/chunks/475-58eebc13c6087e31.js",
					"566",
					"static/chunks/566-ff34ebabaf0907ba.js",
					"737",
					"static/chunks/737-0d6c00b0f45554b2.js",
					"974",
					"static/chunks/app/page-d49b6ec70538a01e.js",
				],
				async: false,
			},
	},
	entryCSSFiles: {
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/":
			[],
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout":
			[{ inlined: false, path: "static/css/a2ddbd0f963f815e.css" }],
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading":
			[],
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/page":
			[],
		"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/[id]/page":
			[],
	},
	rscModuleMapping: {
		326: { "*": { id: "88204", name: "*", chunks: [], async: false } },
		567: { "*": { id: "31003", name: "*", chunks: [], async: false } },
		602: { "*": { id: "12083", name: "*", chunks: [], async: false } },
		1063: { "*": { id: "61283", name: "*", chunks: [], async: false } },
		1483: { "*": { id: "99773", name: "*", chunks: [], async: false } },
		1954: { "*": { id: "82576", name: "*", chunks: [], async: false } },
		5125: { "*": { id: "83163", name: "*", chunks: [], async: false } },
		5234: { "*": { id: "85770", name: "*", chunks: [], async: false } },
		6990: { "*": { id: "59650", name: "*", chunks: [], async: false } },
		7812: { "*": { id: "21247", name: "*", chunks: [], async: false } },
		7939: { "*": { id: "75147", name: "*", chunks: [], async: false } },
		8619: { "*": { id: "59507", name: "*", chunks: [], async: false } },
		8672: { "*": { id: "22920", name: "*", chunks: [], async: false } },
		8819: { "*": { id: "32040", name: "*", chunks: [], async: false } },
		9233: { "*": { id: "55253", name: "*", chunks: [], async: false } },
	},
	edgeRscModuleMapping: {},
};
