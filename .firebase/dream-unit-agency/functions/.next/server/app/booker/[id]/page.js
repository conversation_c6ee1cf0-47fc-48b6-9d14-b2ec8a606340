(() => {
	var e = {};
	(e.id = 665),
		(e.ids = [665]),
		(e.modules = {
			643: (e) => {
				e.exports = require("node:perf_hooks");
			},
			3295: (e) => {
				e.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");
			},
			4573: (e) => {
				e.exports = require("node:buffer");
			},
			8676: (e, t, s) => {
				Promise.resolve().then(s.bind(s, 22920));
			},
			10846: (e) => {
				e.exports = require("next/dist/compiled/next-server/app-page.runtime.prod.js");
			},
			14985: (e) => {
				e.exports = require("dns");
			},
			16698: (e) => {
				e.exports = require("node:async_hooks");
			},
			19121: (e) => {
				e.exports = require("next/dist/server/app-render/action-async-storage.external.js");
			},
			19771: (e) => {
				e.exports = require("process");
			},
			21345: (e, t, s) => {
				s.d(t, { A: () => i });
				const i = (0, s(22752).A)("Scissors", [
					["circle", { cx: "6", cy: "6", r: "3", key: "1lh9wr" }],
					["path", { d: "M8.12 8.12 12 12", key: "1alkpv" }],
					["path", { d: "M20 4 8.12 15.88", key: "xgtan2" }],
					["circle", { cx: "6", cy: "18", r: "3", key: "fqmcym" }],
					["path", { d: "M14.8 14.8 20 20", key: "ptml3r" }],
				]);
			},
			21820: (e) => {
				e.exports = require("os");
			},
			22920: (e, t, s) => {
				s.d(t, { default: () => i });
				const i = (0, s(51129).registerClientReference)(
					() => {
						throw Error(
							"Attempted to call the default export of \"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/booker-profile-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.",
						);
					},
					"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/booker-profile-page.tsx",
					"default",
				);
			},
			27910: (e) => {
				e.exports = require("stream");
			},
			28354: (e) => {
				e.exports = require("util");
			},
			29021: (e) => {
				e.exports = require("fs");
			},
			29294: (e) => {
				e.exports = require("next/dist/server/app-render/work-async-storage.external.js");
			},
			32467: (e) => {
				e.exports = require("node:http2");
			},
			32644: (e, t, s) => {
				Promise.resolve().then(s.bind(s, 35499));
			},
			33873: (e) => {
				e.exports = require("path");
			},
			34589: (e) => {
				e.exports = require("node:assert");
			},
			34631: (e) => {
				e.exports = require("tls");
			},
			34931: (e, t, s) => {
				s.d(t, {
					KT: () => l,
					R4: () => n,
					TZ: () => o,
					Tg: () => a,
					Wm: () => c,
					qD: () => r,
					xk: () => i,
				});
				const i = {
						1: {
							name: "ALEX JOHNSON",
							type: "PHOTOGRAPHER",
							location: "New York, NY",
							followers: 1234,
							following: 567,
							bio: "Professional photographer specializing in portraits and fashion",
						},
						2: {
							name: "SARAH WILSON",
							type: "MODEL",
							location: "Los Angeles, CA",
							followers: 2456,
							following: 432,
							bio: "Fashion and commercial model",
						},
						3: {
							name: "MICHAEL CHEN",
							type: "VIDEOGRAPHER",
							location: "Chicago, IL",
							followers: 987,
							following: 234,
							bio: "Documentary and commercial videographer",
						},
						4: {
							name: "EMMA DAVIS",
							type: "MAKEUP ARTIST",
							location: "Miami, FL",
							followers: 1567,
							following: 345,
							bio: "Editorial and bridal makeup creative",
						},
						5: {
							name: "DAVID RODRIGUEZ",
							type: "PHOTOGRAPHER",
							location: "Austin, TX",
							followers: 2134,
							following: 678,
							bio: "Product and architecture photographer",
						},
					},
					r = [
						{
							id: "1",
							src: "/assets/creatives_portfolio/crt1.png",
							alt: "Sunset beach",
							likes: 324,
							comments: 18,
							position: 0,
						},
						{
							id: "2",
							src: "/assets/creatives_portfolio/crt2.png",
							alt: "Pasta dinner",
							likes: 189,
							comments: 12,
							position: 1,
						},
						{
							id: "3",
							src: "/assets/creatives_portfolio/crt3.png",
							alt: "Mountain view",
							likes: 456,
							comments: 23,
							position: 2,
						},
						{
							id: "4",
							src: "/assets/creatives_portfolio/crt4.png",
							alt: "Morning coffee",
							likes: 203,
							comments: 15,
							position: 3,
						},
						{
							id: "5",
							src: "/assets/creatives_portfolio/crt5.png",
							alt: "City lights",
							likes: 378,
							comments: 26,
							position: 4,
						},
						{
							id: "6",
							src: "/assets/creatives_portfolio/crt6.png",
							alt: "Golden retriever",
							likes: 567,
							comments: 41,
							position: 5,
						},
						{
							id: "7",
							src: "/assets/creatives_portfolio/crt8.png",
							alt: "Sushi dinner",
							likes: 234,
							comments: 19,
							position: 6,
						},
						{
							id: "8",
							src: "/assets/creatives_portfolio/crt7.png",
							alt: "Road trip",
							likes: 292,
							comments: 17,
							position: 7,
						},
						{
							id: "9",
							src: "/placeholder.svg?height=400&width=400&text=Colorful flower garden in spring",
							alt: "Spring flowers",
							likes: 445,
							comments: 31,
							position: 8,
						},
					],
					a = [
						{
							id: "job1",
							title: "Wedding Photography",
							client: "Sarah & Mike Johnson",
							date: "2024-01-15",
							time: "2:00 PM",
							location: "Central Park, NYC",
							status: "confirmed",
							type: "photography",
						},
						{
							id: "job2",
							title: "Corporate Headshots",
							client: "Tech Solutions Inc.",
							date: "2024-01-18",
							time: "10:00 AM",
							location: "Downtown Office",
							status: "pending",
							type: "photography",
						},
						{
							id: "job3",
							title: "Product Photography",
							client: "Fashion Brand Co.",
							date: "2024-01-22",
							time: "1:00 PM",
							location: "Studio A",
							status: "confirmed",
							type: "photography",
						},
						{
							id: "job4",
							title: "Event Coverage",
							client: "Marketing Agency",
							date: "2024-01-25",
							time: "6:00 PM",
							location: "Convention Center",
							status: "confirmed",
							type: "photography",
						},
					],
					l = [
						{
							id: "payment1",
							jobId: "job5",
							jobTitle: "Wedding Photography",
							client: "Jennifer & David Smith",
							amount: 2500,
							date: "2023-12-20",
							status: "paid",
							invoiceNumber: "INV-2023-001",
						},
						{
							id: "payment2",
							jobId: "job6",
							jobTitle: "Corporate Event",
							client: "Global Tech Inc.",
							amount: 1800,
							date: "2023-12-05",
							status: "paid",
							invoiceNumber: "INV-2023-002",
						},
						{
							id: "payment3",
							jobId: "job7",
							jobTitle: "Product Shoot",
							client: "Luxury Brands Co.",
							amount: 1200,
							date: "2023-11-28",
							status: "paid",
							invoiceNumber: "INV-2023-003",
						},
					],
					n = [
						{
							id: "project1",
							title: "Summer Lookbook",
							client: "Zara",
							projectType: "fashion",
							startDate: "2024-07-01",
							endDate: "2024-07-15",
							location: "Miami Beach",
							budget: 2e4,
							description:
								"Create a summer lookbook for Zara's new collection.",
							creativesNeeded: [
								"Photographer",
								"Model",
								"Makeup Artist",
								"Stylist",
							],
						},
						{
							id: "project2",
							title: "Corporate Headshots",
							client: "Google",
							projectType: "corporate",
							startDate: "2024-08-01",
							endDate: "2024-08-05",
							location: "Mountain View",
							budget: 1e4,
							description: "Take corporate headshots for Google's employees.",
							creativesNeeded: ["Photographer"],
						},
						{
							id: "project3",
							title: "Wedding Photography",
							client: "John and Jane Doe",
							projectType: "wedding",
							startDate: "2024-09-01",
							endDate: "2024-09-01",
							location: "Central Park",
							budget: 5e3,
							description: "Photograph John and Jane Doe's wedding.",
							creativesNeeded: ["Photographer"],
						},
					],
					o = [
						{
							id: "offer_rec1",
							projectTitle: "Summer Glow Campaign",
							clientName: "Sunlight Beauty Co.",
							role: "Lead Photographer",
							amount: 2200,
							offerDate: "2024-03-10",
							status: "pending",
							projectType: "Beauty Product Shoot",
							location: "Miami Beach, FL",
							description:
								"Looking for a photographer with a bright and airy style for our new sunscreen line.",
							isSentByMe: !1,
						},
						{
							id: "offer_rec2",
							projectTitle: "Urban Explorers Video",
							clientName: "City Adventures Magazine",
							role: "Videographer",
							amount: 1800,
							offerDate: "2024-03-05",
							status: "accepted",
							projectType: "Travel Documentary Short",
							location: "Various, NYC",
							description:
								"Short documentary piece following urban explorers. Drone skills a plus.",
							isSentByMe: !1,
						},
						{
							id: "offer_rec3",
							projectTitle: "Tech Conference Live Model",
							clientName: "Innovate Corp",
							role: "Promotional Model",
							amount: 750,
							offerDate: "2024-02-28",
							status: "declined",
							projectType: "Tech Event",
							location: "San Francisco, CA",
							description:
								"Need engaging models for our booth at the upcoming Innovate Summit.",
							isSentByMe: !1,
						},
					],
					c = [
						{
							id: "offer_sent1",
							projectTitle: "Indie Band Music Video",
							clientName: "The Wandering Souls (Band)",
							role: "Director of Photography",
							amount: 1500,
							offerDate: "2024-03-12",
							status: "pending",
							projectType: "Music Video",
							location: "Austin, TX",
							description:
								"Proposal to shoot and direct the photography for your upcoming music video.",
							isSentByMe: !0,
						},
						{
							id: "offer_sent2",
							projectTitle: "Artisan Bakery Branding",
							clientName: "The Sweet Spot Bakery",
							role: "Food Photographer",
							amount: 900,
							offerDate: "2024-03-02",
							status: "expired",
							projectType: "Branding & Lifestyle",
							location: "Portland, OR",
							description:
								"Offered to create a series of lifestyle and product shots for their new website.",
							isSentByMe: !0,
						},
						{
							id: "offer_sent3",
							projectTitle: "Local Cafe Social Media Content",
							clientName: "Corner Brew Cafe",
							role: "Content Creator (Photo/Video)",
							amount: 600,
							offerDate: "2024-02-20",
							status: "negotiating",
							projectType: "Social Media Marketing",
							location: "Local",
							description:
								"Proposed a monthly retainer for creating engaging social media content.",
							isSentByMe: !0,
						},
					];
			},
			35499: (e, t, s) => {
				s.d(t, { default: () => Z });
				var i = s(45781),
					r = s(13072),
					a = s(22752);
				const l = (0, a.A)("CreditCard", [
					[
						"rect",
						{
							width: "20",
							height: "14",
							x: "2",
							y: "5",
							rx: "2",
							key: "ynyp8z",
						},
					],
					["line", { x1: "2", x2: "22", y1: "10", y2: "10", key: "1b3vmo" }],
				]);
				var n = s(44238),
					o = s(7439),
					c = s(38653),
					d = s(34931);
				const x = () => {
						const [e, t] = (0, r.useState)(null),
							[s, i] = (0, r.useState)(!1);
						return {
							selectedProject: e,
							openProjectModal: (e) => {
								t(e);
							},
							closeProjectModal: () => {
								t(null);
							},
							showPaymentModal: s,
							openPaymentModal: () => {
								i(!0);
							},
							closePaymentModal: () => {
								i(!1);
							},
						};
					},
					h = ({ projects: e, setProjects: t, initialMergeCount: s = 0 }) => {
						const [i, a] = (0, r.useState)(new Set()),
							[l, n] = (0, r.useState)(!1),
							[o, c] = (0, r.useState)(s),
							d = (0, r.useCallback)(() => {
								n((e) => !e), a(new Set());
							}, []),
							x = (0, r.useCallback)(
								(e) => {
									l &&
										a((t) => {
											const s = new Set(t);
											return s.has(e) ? s.delete(e) : s.add(e), s;
										});
								},
								[l],
							),
							h = (0, r.useCallback)(() => {
								a(new Set());
							}, []),
							g = (0, r.useCallback)(
								(s) => {
									if (i.size < 2) return;
									const r = e.filter((e) => i.has(e.id));
									if (0 === r.length) return;
									const l = Math.min(...r.map((e) => e.position || 0)),
										d = r.some((e) => "text" === e.type),
										x = {
											id: `merged-${Date.now()}-${o}`,
											src: "/placeholder.svg?height=800&width=800&text=Merged Projects",
											alt: `Merged: ${r.map((e) => e.title.substring(0, 10)).join(", ")}...`,
											title: `Merged: ${r.map((e) => e.title).join(" + ")}`,
											client: "Multiple Clients",
											budget: r.reduce((e, t) => e + t.budget, 0),
											status: "active",
											size: s,
											isMerged: !0,
											originalProjects: [...r],
											position: l,
											type: d ? "text" : "image",
											textContent: d
												? r
														.map((e) =>
															"text" === e.type
																? e.textContent || "Empty text"
																: e.title,
														)
														.join("\n\n") || "Merged content"
												: void 0,
											backgroundColor: d ? "#f3f4f6" : void 0,
										};
									t(
										[...e.filter((e) => !i.has(e.id)), x].sort(
											(e, t) => (e.position || 0) - (t.position || 0),
										),
									),
										a(new Set()),
										n(!1),
										c((e) => e + 1);
								},
								[e, i, t, o],
							),
							p = (0, r.useCallback)(
								(s, i) => {
									if ((i.stopPropagation(), !s.isMerged || !s.originalProjects))
										return;
									const r = s.position || 0,
										a = s.originalProjects.map((e, t) => ({
											...e,
											position: void 0 !== e.position ? e.position : r + t,
											isMerged: !1,
											size: e.size || "standard",
										}));
									t(
										[...e.filter((e) => e.id !== s.id), ...a].sort(
											(e, t) => (e.position || 0) - (t.position || 0),
										),
									);
								},
								[e, t],
							);
						return {
							selectedTiles: i,
							isSelectionMode: l,
							mergeCount: o,
							toggleTileSelection: x,
							clearSelection: h,
							toggleSelectionMode: d,
							mergeTiles: g,
							splitTile: p,
							convertToTextTile: (0, r.useCallback)(
								(e) => {
									t((t) =>
										t.map((t) =>
											t.id === e
												? {
														...t,
														type: "text",
														textContent: t.textContent || "Click to edit text",
														backgroundColor: t.backgroundColor || "#f3f4f6",
														src: void 0,
														alt: t.alt || "Text tile",
													}
												: t,
										),
									);
								},
								[t],
							),
							convertToImageTile: (0, r.useCallback)(
								(e) => {
									t((t) =>
										t.map((t) =>
											t.id === e
												? {
														...t,
														type: "image",
														textContent: void 0,
														backgroundColor: void 0,
														src:
															t.src ||
															"/placeholder.svg?height=400&width=400&text=Image",
														alt: t.alt || "Image tile",
													}
												: t,
										),
									);
								},
								[t],
							),
						};
					},
					g = ({ setProjects: e }) => {
						const [t, s] = (0, r.useState)(null),
							[i, a] = (0, r.useState)(""),
							l = (0, r.useCallback)((e, t = "") => {
								s(e), a(t);
							}, []),
							n = (0, r.useCallback)((e) => {
								a(e);
							}, []),
							o = (0, r.useCallback)(() => {
								t &&
									(e((e) =>
										e.map((e) =>
											e.id === t ? { ...e, textContent: i || "Empty text" } : e,
										),
									),
									s(null),
									a(""));
							}, [t, i, e]);
						return {
							editingTile: t,
							tempText: i,
							startEditingText: l,
							handleSetTempText: n,
							saveTextEdit: o,
							cancelTextEdit: (0, r.useCallback)(() => {
								s(null), a("");
							}, []),
						};
					},
					p = { rating: 0, review: "" },
					m = () => {
						const [e, t] = (0, r.useState)(!1),
							[s, i] = (0, r.useState)(null),
							[a, l] = (0, r.useState)(p),
							n = (0, r.useCallback)((e) => {
								i(e),
									l({ rating: e.rating || 0, review: e.review || "" }),
									t(!0);
							}, []),
							o = (0, r.useCallback)(() => {
								t(!1), i(null), l(p);
							}, []),
							c = (0, r.useCallback)((e, t) => {
								l((s) => ({ ...s, [e]: t }));
							}, []),
							d = (0, r.useCallback)(
								(e) => {
									e.preventDefault(),
										console.log(
											"Review submitted:",
											a,
											"for collaboration:",
											s?.id,
										),
										alert("Review updated successfully! (Placeholder)"),
										o();
								},
								[a, s, o],
							);
						return {
							showReviewModal: e,
							selectedCollaboration: s,
							reviewForm: a,
							openReviewModal: n,
							closeReviewModal: o,
							updateReviewFormField: c,
							handleReviewSubmit: d,
						};
					},
					u = {
						title: "",
						client: "",
						budget: "",
						projectType: "",
						startDate: "",
						endDate: "",
						location: "",
						description: "",
						creativesNeeded: [],
						type: "image",
						size: "standard",
						isMerged: !1,
						textContent: "",
						backgroundColor: "#f3f4f6",
					},
					f = ({ projects: e, setProjects: t }) => {
						const [s, i] = (0, r.useState)(!1),
							[a, l] = (0, r.useState)(null),
							[n, o] = (0, r.useState)(u),
							c = (0, r.useCallback)((e) => {
								e
									? (l(e),
										o({
											...u,
											...e,
											id: e.id,
											budget: e.budget.toString(),
											title: e.title || "",
											client: e.client || "",
											projectType: e.projectType || "",
											startDate: e.startDate || "",
											endDate: e.endDate || "",
											location: e.location || "",
											description: e.description || "",
											creativesNeeded: e.creativesNeeded || [],
											type: e.type || "image",
											size: e.size || "standard",
											isMerged: "boolean" == typeof e.isMerged && e.isMerged,
											textContent:
												e.textContent || ("text" === e.type && e.title) || "",
											backgroundColor:
												e.backgroundColor ||
												("text" === e.type ? "#f3f4f6" : void 0),
										}))
									: (l(null), o(u)),
									i(!0);
							}, []),
							d = (0, r.useCallback)(() => {
								i(!1), l(null), o(u);
							}, []),
							x = (0, r.useCallback)((e, t) => {
								o((s) => ({ ...s, [e]: t }));
							}, []),
							h = (0, r.useCallback)(() => {
								const e = prompt(
									"Enter creative role (e.g., Photographer, Model, etc.):",
								);
								e &&
									e.trim() &&
									o((t) => ({
										...t,
										creativesNeeded: [...t.creativesNeeded, e.trim()],
									}));
							}, []),
							g = (0, r.useCallback)((e) => {
								o((t) => ({
									...t,
									creativesNeeded: t.creativesNeeded.filter((t, s) => s !== e),
								}));
							}, []),
							p = (0, r.useCallback)(
								(s) => {
									if ((s.preventDefault(), a)) {
										const e = {
											...a,
											...n,
											budget: Number.parseInt(n.budget, 10) || 0,
										};
										"text" === e.type
											? ((e.src = void 0),
												(e.alt = void 0),
												void 0 === e.textContent &&
													(e.textContent = e.title || ""),
												void 0 === e.backgroundColor &&
													(e.backgroundColor = "#f3f4f6"))
											: "image" !== e.type ||
												((e.textContent = void 0),
												(e.backgroundColor = void 0),
												e.src ||
													(e.src =
														"/placeholder.svg?height=400&width=400&text=" +
														encodeURIComponent(e.title || "Project")),
												e.alt || (e.alt = e.title || "Project Image")),
											t((t) => t.map((t) => (t.id === a.id ? e : t))),
											alert("Project updated successfully! (Placeholder)");
									} else {
										const s = {
											id: `project-${Date.now()}`,
											title: n.title,
											client: n.client,
											budget: Number.parseInt(n.budget, 10) || 0,
											projectType: n.projectType,
											startDate: n.startDate,
											endDate: n.endDate,
											location: n.location,
											description: n.description,
											creativesNeeded: n.creativesNeeded,
											type: n.type || "image",
											size: n.size || "standard",
											isMerged: "boolean" == typeof n.isMerged && n.isMerged,
											textContent:
												"text" === n.type
													? n.textContent || n.title || ""
													: void 0,
											backgroundColor:
												"text" === n.type
													? n.backgroundColor || "#f3f4f6"
													: void 0,
											src:
												"image" === n.type
													? "/placeholder.svg?height=400&width=400&text=" +
														encodeURIComponent(n.title || "New Project")
													: void 0,
											alt:
												"image" === n.type ? n.title || "New Project" : void 0,
											status: "planning",
											position: e.length,
										};
										t((e) => [...e, s]),
											alert("Project added successfully! (Placeholder)");
									}
									d();
								},
								[n, e, t, d, a],
							);
						return {
							showAddProjectModal: s,
							editingProject: a,
							newProjectForm: n,
							openAddProjectModal: c,
							closeAddProjectModal: d,
							updateNewProjectFormField: x,
							handleAddProjectSubmit: p,
							addArtistRoleToForm: h,
							removeArtistRoleFromForm: g,
						};
					};
				var j = s(1437);
				const b = ({
						name: e,
						type: t,
						projectCount: s,
						offersSentCount: r,
						collaborationsCount: a,
						membersCount: l,
					}) =>
						(0, i.jsxs)("div", {
							className: "text-center mb-12",
							children: [
								(0, i.jsx)("h2", {
									className: "text-3xl font-light tracking-wide mb-3",
									children: e,
								}),
								(0, i.jsx)("p", {
									className: "text-gray-600 font-light tracking-wide mb-6",
									children: t,
								}),
								(0, i.jsxs)("div", {
									className: "flex justify-center gap-12 text-sm font-light",
									children: [
										(0, i.jsxs)("span", {
											children: [
												(0, i.jsx)("strong", {
													className: "font-normal",
													children: s,
												}),
												" Projects",
											],
										}),
										(0, i.jsxs)("span", {
											children: [
												(0, i.jsx)("strong", {
													className: "font-normal",
													children: r,
												}),
												" Offers Sent",
											],
										}),
										(0, i.jsxs)("span", {
											children: [
												(0, i.jsx)("strong", {
													className: "font-normal",
													children: a,
												}),
												" Collaborations",
											],
										}),
										(0, i.jsxs)("span", {
											children: [
												(0, i.jsx)("strong", {
													className: "font-normal",
													children: l,
												}),
												" Members",
											],
										}),
									],
								}),
							],
						}),
					y = [
						{
							id: "act1",
							userName: "Alice Wonderland",
							action: "invited",
							target: "Bob The Builder",
							timestamp: "2 hours ago",
							status: "active",
						},
						{
							id: "act2",
							userName: "Bob The Builder",
							action: "updated settings for",
							target: "Spring Fashion Campaign",
							timestamp: "1 day ago",
							status: "completed",
						},
						{
							id: "act3",
							userName: "Charlie Chaplin",
							action: "viewed",
							target: "Product Launch Event",
							timestamp: "3 days ago",
							status: "completed",
						},
						{
							id: "act4",
							userName: "Alice Wonderland",
							action: "changed permission for",
							target: "Diana Prince to Viewer",
							timestamp: "5 days ago",
							status: "completed",
						},
					],
					v = ({ activeTab: e }) =>
						(0, i.jsxs)("div", {
							className: "bg-gray-900 rounded p-8 mb-12",
							children: [
								(0, i.jsxs)("div", {
									className: "flex justify-between items-center mb-6",
									children: [
										(0, i.jsx)("span", {
											className: "text-white text-sm font-light tracking-wide",
											children:
												"budget" === e
													? "Budget Breakdown"
													: "members" === e
														? "Latest Member Activity"
														: "Project Timeline",
										}),
										"members" !== e &&
											(0, i.jsxs)("div", {
												className: "flex items-center gap-6",
												children: [
													(0, i.jsxs)("span", {
														className: "flex items-center gap-2",
														children: [
															(0, i.jsx)("div", {
																className: "w-2 h-2 rounded-full bg-blue-400",
															}),
															(0, i.jsx)("span", {
																className:
																	"text-gray-400 text-xs font-light tracking-wide",
																children: "Active",
															}),
														],
													}),
													(0, i.jsxs)("span", {
														className: "flex items-center gap-2",
														children: [
															(0, i.jsx)("div", {
																className: "w-2 h-2 rounded-full bg-green-400",
															}),
															(0, i.jsx)("span", {
																className:
																	"text-gray-400 text-xs font-light tracking-wide",
																children: "Completed",
															}),
														],
													}),
												],
											}),
									],
								}),
								"budget" === e
									? (0, i.jsx)("div", {
											className: "h-48 relative",
											children: (0, i.jsxs)("svg", {
												className: "w-full h-full",
												viewBox: "0 0 400 200",
												children: [
													(0, i.jsx)("defs", {
														children: (0, i.jsx)("pattern", {
															id: "grid",
															width: "40",
															height: "20",
															patternUnits: "userSpaceOnUse",
															children: (0, i.jsx)("path", {
																d: "M 40 0 L 0 0 0 20",
																fill: "none",
																stroke: "#374151",
																strokeWidth: "0.5",
																opacity: "0.3",
															}),
														}),
													}),
													(0, i.jsx)("rect", {
														width: "100%",
														height: "100%",
														fill: "url(#grid)",
													}),
													(0, i.jsx)("line", {
														x1: "20",
														y1: "180",
														x2: "380",
														y2: "180",
														stroke: "#4b5563",
														strokeWidth: "1",
													}),
													(0, i.jsx)("line", {
														x1: "20",
														y1: "20",
														x2: "20",
														y2: "180",
														stroke: "#4b5563",
														strokeWidth: "1",
													}),
													[
														{
															x: 40,
															width: 20,
															height: 70,
															amount: 3500,
															category: "Photography",
														},
														{
															x: 80,
															width: 20,
															height: 56,
															amount: 2800,
															category: "Videography",
														},
														{
															x: 120,
															width: 20,
															height: 24,
															amount: 1200,
															category: "Makeup",
														},
														{
															x: 160,
															width: 20,
															height: 80,
															amount: 4e3,
															category: "Modeling",
														},
														{
															x: 200,
															width: 20,
															height: 60,
															amount: 3e3,
															category: "Styling",
														},
														{
															x: 240,
															width: 20,
															height: 40,
															amount: 2e3,
															category: "Location",
														},
														{
															x: 280,
															width: 20,
															height: 100,
															amount: 5e3,
															category: "Equipment",
														},
														{
															x: 320,
															width: 20,
															height: 32,
															amount: 1600,
															category: "Catering",
														},
													].map((e, t) =>
														(0, i.jsxs)(
															"g",
															{
																className: "group",
																children: [
																	(0, i.jsx)("rect", {
																		x: e.x,
																		y: 180 - e.height,
																		width: e.width,
																		height: e.height,
																		fill: "#3b82f6",
																		className:
																			"hover:fill-blue-600 transition-colors cursor-pointer",
																		children: (0, i.jsxs)("title", {
																			children: [
																				"$",
																				e.amount.toLocaleString(),
																				" - ",
																				e.category,
																			],
																		}),
																	}),
																	(0, i.jsxs)("text", {
																		x: e.x + e.width / 2,
																		y: 175 - e.height,
																		fill: "#1e40af",
																		fontSize: "10",
																		textAnchor: "middle",
																		className:
																			"opacity-0 group-hover:opacity-100 transition-opacity",
																		children: ["$", e.amount],
																	}),
																],
															},
															t,
														),
													),
													(0, i.jsx)("text", {
														x: "15",
														y: "30",
														fill: "#9ca3af",
														fontSize: "10",
														textAnchor: "end",
														children: "$5K",
													}),
													(0, i.jsx)("text", {
														x: "15",
														y: "80",
														fill: "#9ca3af",
														fontSize: "10",
														textAnchor: "end",
														children: "$4K",
													}),
													(0, i.jsx)("text", {
														x: "15",
														y: "130",
														fill: "#9ca3af",
														fontSize: "10",
														textAnchor: "end",
														children: "$2K",
													}),
													(0, i.jsx)("text", {
														x: "15",
														y: "180",
														fill: "#9ca3af",
														fontSize: "10",
														textAnchor: "end",
														children: "$0",
													}),
													(0, i.jsx)("text", {
														x: "50",
														y: "195",
														fill: "#9ca3af",
														fontSize: "8",
														textAnchor: "middle",
														children: "Photo",
													}),
													(0, i.jsx)("text", {
														x: "130",
														y: "195",
														fill: "#9ca3af",
														fontSize: "8",
														textAnchor: "middle",
														children: "Makeup",
													}),
													(0, i.jsx)("text", {
														x: "210",
														y: "195",
														fill: "#9ca3af",
														fontSize: "8",
														textAnchor: "middle",
														children: "Styling",
													}),
													(0, i.jsx)("text", {
														x: "290",
														y: "195",
														fill: "#9ca3af",
														fontSize: "8",
														textAnchor: "middle",
														children: "Equipment",
													}),
												],
											}),
										})
									: "members" === e
										? (0, i.jsx)("div", {
												className:
													"h-auto min-h-48 relative text-gray-300 p-4 text-sm font-light",
												children:
													y.length > 0
														? (0, i.jsx)("ul", {
																className: "space-y-3",
																children: y.map((e) =>
																	(0, i.jsxs)(
																		"li",
																		{
																			className: "flex items-start",
																			children: [
																				(0, i.jsx)("span", {
																					className:
																						"text-xs text-gray-500 whitespace-nowrap mr-4 w-24 text-right",
																					children: e.timestamp,
																				}),
																				(0, i.jsxs)("div", {
																					className: `flex-grow ${"active" === e.status ? "text-blue-400" : "text-green-400"}`,
																					children: [
																						(0, i.jsx)("span", {
																							className: "font-medium",
																							children: e.userName,
																						}),
																						(0, i.jsxs)("span", {
																							className: `${"active" === e.status ? "text-blue-300" : "text-green-300"} opacity-80`,
																							children: [" ", e.action, " "],
																						}),
																						e.target &&
																							(0, i.jsx)("span", {
																								className: `font-normal ${"active" === e.status ? "text-blue-400" : "text-green-400"}`,
																								children: e.target,
																							}),
																					],
																				}),
																			],
																		},
																		e.id,
																	),
																),
															})
														: (0, i.jsx)("p", {
																className: "text-center text-gray-400",
																children: "No recent member activity.",
															}),
											})
										: (0, i.jsx)("div", {
												className: "grid grid-cols-3 gap-8",
												children: ["Jan", "Feb", "Mar"].map((e, t) =>
													(0, i.jsxs)(
														"div",
														{
															children: [
																(0, i.jsx)("div", {
																	className:
																		"text-center text-gray-400 text-xs mb-4 font-light tracking-wide",
																	children: e,
																}),
																(0, i.jsx)("div", {
																	className: "grid grid-cols-7 gap-1 mb-3",
																	children: [
																		"Sun",
																		"Mon",
																		"Tue",
																		"Wed",
																		"Thu",
																		"Fri",
																		"Sat",
																	].map((e) =>
																		(0, i.jsx)(
																			"div",
																			{
																				className:
																					"text-center text-gray-500 text-xs font-light tracking-wide",
																				children: e,
																			},
																			e,
																		),
																	),
																}),
																(0, i.jsx)("div", {
																	className: "grid grid-cols-7 gap-1",
																	children: [...Array(31)].map((t, s) => {
																		const r = Math.random() > 0.8,
																			a = Math.random() > 0.6;
																		return (0, i.jsx)(
																			"div",
																			{
																				className: `w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ${r ? (a ? "bg-green-900 hover:bg-green-800 text-green-200" : "bg-blue-900 hover:bg-blue-800 text-blue-200") : "bg-gray-800 hover:bg-gray-700 text-gray-400"} cursor-pointer transition-colors`,
																				title: `${e} ${s + 1}: ${r ? (a ? "Completed" : "Active") : "Available"}`,
																				children: s + 1,
																			},
																			`${e}-day-${s + 1}`,
																		);
																	}),
																}),
															],
														},
														e,
													),
												),
											}),
							],
						}),
					N = ({
						activeTab: e,
						onTabChange: t,
						projectCount: s,
						offersSentCount: r,
						totalBudgetFormatted: a,
						archiveCount: l,
						membersCount: o,
						isSelectionMode: c,
						onOpenAddProjectModal: d,
						onMergeTiles: x,
						onClearSelection: h,
						onConvertToText: g,
						onConvertToImage: p,
						selectedTilesCount: m,
					}) =>
						(0, i.jsx)("div", {
							className: "border-b border-gray-100",
							children: (0, i.jsxs)("div", {
								className: "max-w-4xl mx-auto px-6 py-8",
								children: [
									(0, i.jsxs)("div", {
										className: "flex items-center justify-center gap-12 mb-8",
										children: [
											(0, i.jsxs)("button", {
												onClick: () => t("projects"),
												className: `text-sm font-light tracking-wide ${"projects" === e ? "text-black" : "text-gray-400"}`,
												children: ["Projects (", s, ")"],
											}),
											(0, i.jsxs)("button", {
												onClick: () => t("offers"),
												className: `text-sm font-light tracking-wide ${"offers" === e ? "text-black" : "text-gray-400"}`,
												children: ["Offers Sent (", r, ")"],
											}),
											(0, i.jsxs)("button", {
												onClick: () => t("budget"),
												className: `text-sm font-light tracking-wide ${"budget" === e ? "text-black" : "text-gray-400"}`,
												children: ["Budget (", a, ")"],
											}),
											(0, i.jsxs)("button", {
												onClick: () => t("archive"),
												className: `text-sm font-light tracking-wide ${"archive" === e ? "text-black" : "text-gray-400"}`,
												children: ["Archive (", l, ")"],
											}),
											(0, i.jsxs)("button", {
												onClick: () => t("members"),
												className: `text-sm font-light tracking-wide ${"members" === e ? "text-black" : "text-gray-400"}`,
												children: ["Members (", o, ")"],
											}),
										],
									}),
									"projects" === e &&
										(0, i.jsxs)("div", {
											className: "flex items-center justify-center gap-4",
											children: [
												(0, i.jsx)(n.$, {
													variant: "outline",
													size: "sm",
													className: "text-xs font-light tracking-wide",
													onClick: d,
													children: "+ Add Project",
												}),
												c &&
													(0, i.jsxs)(i.Fragment, {
														children: [
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: () => x("horizontal"),
																disabled: m < 2,
																children: "Merge H",
															}),
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: () => x("vertical"),
																disabled: m < 2,
																children: "Merge V",
															}),
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: () => x("large"),
																disabled: m < 2,
																children: "Merge L",
															}),
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: g,
																disabled: 0 === m,
																children: "To Text",
															}),
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: p,
																disabled: 0 === m,
																children: "To Image",
															}),
															(0, i.jsx)(n.$, {
																variant: "outline",
																size: "sm",
																onClick: h,
																disabled: 0 === m,
																children: "Clear Sel.",
															}),
														],
													}),
											],
										}),
								],
							}),
						});
				var w = s(3704),
					k = s(28328),
					C = s.n(k),
					S = s(46909);
				const M = ({
						selectedProject: e,
						offersSent: t,
						onClose: s,
						onEditProject: r,
					}) =>
						e
							? (0, i.jsx)("div", {
									className:
										"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4",
									children: (0, i.jsxs)("div", {
										className:
											"bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row",
										children: [
											(0, i.jsx)("div", {
												className:
													"w-full md:w-80 relative min-h-[250px] md:min-h-0",
												children: (0, i.jsx)(w.default, {
													src: e.src || "/placeholder.svg",
													alt: e.alt,
													fill: !0,
													className: "object-cover",
												}),
											}),
											(0, i.jsxs)("div", {
												className: "flex-1 flex flex-col",
												children: [
													(0, i.jsxs)("div", {
														className:
															"flex items-center justify-between p-4 border-b border-gray-100",
														children: [
															(0, i.jsx)("div", {
																className: "flex items-center gap-3",
																children: (0, i.jsx)("span", {
																	className: "font-light tracking-wide",
																	children: e.title,
																}),
															}),
															(0, i.jsx)("button", {
																onClick: s,
																children: (0, i.jsx)(S.A, {
																	className: "w-5 h-5",
																}),
															}),
														],
													}),
													(0, i.jsx)("div", {
														className: "flex-1 p-4 overflow-y-auto",
														children: (0, i.jsxs)("div", {
															className: "space-y-4",
															children: [
																(0, i.jsx)("div", {
																	className: "mb-4",
																	children: (0, i.jsx)("span", {
																		className: `px-3 py-1 text-xs font-light tracking-wide rounded ${"active" === e.status ? "bg-blue-100 text-blue-800" : "completed" === e.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`,
																		children:
																			e.status.charAt(0).toUpperCase() +
																			e.status.slice(1),
																	}),
																}),
																(0, i.jsxs)("div", {
																	children: [
																		(0, i.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Client",
																		}),
																		(0, i.jsx)("p", {
																			className: "font-light",
																			children: e.client,
																		}),
																	],
																}),
																(0, i.jsxs)("div", {
																	children: [
																		(0, i.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-1",
																			children: "Budget",
																		}),
																		(0, i.jsxs)("p", {
																			className: "font-light",
																			children: [
																				"$",
																				e.budget.toLocaleString(),
																			],
																		}),
																	],
																}),
																e.projectType &&
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"text-sm font-light text-gray-500 tracking-wide mb-1",
																				children: "Project Type",
																			}),
																			(0, i.jsx)("p", {
																				className: "font-light",
																				children: e.projectType,
																			}),
																		],
																	}),
																e.startDate &&
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"text-sm font-light text-gray-500 tracking-wide mb-1",
																				children: "Timeline",
																			}),
																			(0, i.jsxs)("p", {
																				className: "font-light",
																				children: [
																					e.startDate,
																					e.endDate && e.endDate !== e.startDate
																						? ` to ${e.endDate}`
																						: "",
																				],
																			}),
																		],
																	}),
																e.location &&
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"text-sm font-light text-gray-500 tracking-wide mb-1",
																				children: "Location",
																			}),
																			(0, i.jsx)("p", {
																				className: "font-light",
																				children: e.location,
																			}),
																		],
																	}),
																e.description &&
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"text-sm font-light text-gray-500 tracking-wide mb-1",
																				children: "Description",
																			}),
																			(0, i.jsx)("p", {
																				className: "text-gray-700 font-light",
																				children: e.description,
																			}),
																		],
																	}),
																(0, i.jsxs)("div", {
																	className: "border-t border-gray-100 pt-4",
																	children: [
																		(0, i.jsx)("h3", {
																			className:
																				"text-sm font-light text-gray-500 tracking-wide mb-3",
																			children: "Project Staffing Status",
																		}),
																		(0, i.jsx)("div", {
																			className: "overflow-x-auto",
																			children: (0, i.jsxs)("table", {
																				className: "w-full text-sm",
																				children: [
																					(0, i.jsx)("thead", {
																						children: (0, i.jsxs)("tr", {
																							className:
																								"border-b border-gray-100",
																							children: [
																								(0, i.jsx)("th", {
																									className:
																										"text-left py-2 text-xs font-light text-gray-500 tracking-wide",
																									children: "Role",
																								}),
																								(0, i.jsx)("th", {
																									className:
																										"text-left py-2 text-xs font-light text-gray-500 tracking-wide",
																									children: "Offers Sent",
																								}),
																								(0, i.jsx)("th", {
																									className:
																										"text-right py-2 text-xs font-light text-gray-500 tracking-wide",
																									children: "Budget Range",
																								}),
																								(0, i.jsx)("th", {
																									className:
																										"text-right py-2 text-xs font-light text-gray-500 tracking-wide",
																									children: "Status",
																								}),
																							],
																						}),
																					}),
																					(0, i.jsx)("tbody", {
																						className:
																							"divide-y divide-gray-100",
																						children:
																							e.creativesNeeded &&
																							e.creativesNeeded.length > 0
																								? e.creativesNeeded.map(
																										(s, r) => {
																											const a = t.filter(
																													(t) =>
																														t.projectTitle ===
																															e.title &&
																														t.projectType
																															.toLowerCase()
																															.includes(
																																s
																																	.toLowerCase()
																																	.split(
																																		" ",
																																	)[0],
																															),
																												),
																												l = a.filter(
																													(e) =>
																														"accepted" ===
																														e.status,
																												),
																												n = a.filter(
																													(e) =>
																														"pending" ===
																														e.status,
																												),
																												o = a.filter(
																													(e) =>
																														"declined" ===
																														e.status,
																												),
																												c =
																													l.length > 0
																														? {
																																status:
																																	"Confirmed",
																																color:
																																	"bg-green-100 text-green-800",
																															}
																														: n.length > 0
																															? {
																																	status:
																																		"Pending",
																																	color:
																																		"bg-yellow-100 text-yellow-800",
																																}
																															: o.length > 0 &&
																																	a.length ===
																																		o.length
																																? {
																																		status:
																																			"Declined",
																																		color:
																																			"bg-red-100 text-red-800",
																																	}
																																: {
																																		status:
																																			"Needed",
																																		color:
																																			"bg-orange-100 text-orange-800",
																																	},
																												d =
																													a.length > 0
																														? `$${Math.min(...a.map((e) => e.amount)).toLocaleString()} - $${Math.max(...a.map((e) => e.amount)).toLocaleString()}`
																														: "-";
																											return (0, i.jsxs)(
																												"tr",
																												{
																													className:
																														"hover:bg-gray-50",
																													children: [
																														(0, i.jsx)("td", {
																															className:
																																"py-3 font-light text-gray-900",
																															children: s,
																														}),
																														(0, i.jsx)("td", {
																															className: "py-3",
																															children: (0,
																															i.jsx)("div", {
																																className:
																																	"flex flex-wrap gap-1",
																																children:
																																	a.length > 0
																																		? a.map(
																																				(
																																					e,
																																					t,
																																				) =>
																																					(0,
																																					i.jsxs)(
																																						C(),
																																						{
																																							href: `/creative/${e.artistId}`,
																																							className:
																																								"flex items-center gap-1 bg-gray-50 hover:bg-gray-100 rounded-full px-2 py-1 border transition-colors cursor-pointer",
																																							children:
																																								[
																																									(0,
																																									i.jsx)(
																																										"div",
																																										{
																																											className:
																																												"w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center",
																																											children:
																																												(0,
																																												i.jsx)(
																																													"span",
																																													{
																																														className:
																																															"text-xs font-light text-gray-600",
																																														children:
																																															e.artistName
																																																.split(
																																																	" ",
																																																)
																																																.map(
																																																	(
																																																		e,
																																																	) =>
																																																		e[0],
																																																)
																																																.join(
																																																	"",
																																																),
																																													},
																																												),
																																										},
																																									),
																																									(0,
																																									i.jsx)(
																																										"span",
																																										{
																																											className:
																																												"text-xs text-gray-900 mr-1 font-light",
																																											children:
																																												e.artistName.split(
																																													" ",
																																												)[0],
																																										},
																																									),
																																									(0,
																																									i.jsx)(
																																										"span",
																																										{
																																											className: `px-1 py-0.5 text-xs font-light rounded-full ${"accepted" === e.status ? "bg-green-500 text-white" : "declined" === e.status ? "bg-red-500 text-white" : "expired" === e.status ? "bg-gray-500 text-white" : "bg-yellow-500 text-white"}`,
																																											children:
																																												"accepted" ===
																																												e.status
																																													? "✓"
																																													: "declined" ===
																																															e.status
																																														? "✗"
																																														: "expired" ===
																																																e.status
																																															? "⏰"
																																															: "⏳",
																																										},
																																									),
																																								],
																																						},
																																						t,
																																					),
																																			)
																																		: (0,
																																			i.jsxs)(
																																				C(),
																																				{
																																					href: `/?role=${encodeURIComponent(s)}&project=${encodeURIComponent(e.title)}&location=${encodeURIComponent(e.location || "")}&budget=${e.budget}&startDate=${e.startDate}&endDate=${e.endDate}`,
																																					className:
																																						"inline-flex items-center gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 hover:text-blue-800 rounded px-3 py-1 border border-blue-200 transition-colors text-xs font-light tracking-wide",
																																					children:
																																						[
																																							(0,
																																							i.jsx)(
																																								"svg",
																																								{
																																									className:
																																										"w-3 h-3",
																																									fill: "none",
																																									stroke:
																																										"currentColor",
																																									viewBox:
																																										"0 0 24 24",
																																									children:
																																										(0,
																																										i.jsx)(
																																											"path",
																																											{
																																												strokeLinecap:
																																													"round",
																																												strokeLinejoin:
																																													"round",
																																												strokeWidth: 2,
																																												d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z",
																																											},
																																										),
																																								},
																																							),
																																							"Search ",
																																							s.toUpperCase(),
																																						],
																																				},
																																			),
																															}),
																														}),
																														(0, i.jsx)("td", {
																															className:
																																"py-3 text-right font-light text-gray-900",
																															children: d,
																														}),
																														(0, i.jsx)("td", {
																															className:
																																"py-3 text-right",
																															children: (0,
																															i.jsx)("span", {
																																className: `px-2 py-1 text-xs font-light tracking-wide rounded-full ${c.color}`,
																																children:
																																	c.status,
																															}),
																														}),
																													],
																												},
																												r,
																											);
																										},
																									)
																								: (0, i.jsx)("tr", {
																										children: (0, i.jsx)("td", {
																											colSpan: 4,
																											className:
																												"py-6 text-center text-gray-500",
																											children: (0, i.jsx)(
																												"p",
																												{
																													className:
																														"text-sm font-light",
																													children:
																														"No creative requirements specified",
																												},
																											),
																										}),
																									}),
																					}),
																				],
																			}),
																		}),
																		(0, i.jsxs)("div", {
																			className:
																				"mt-4 flex justify-between text-xs text-gray-500 font-light",
																			children: [
																				(0, i.jsx)("span", {
																					children: e.creativesNeeded
																						? `${e.creativesNeeded.length} roles needed`
																						: "0 roles needed",
																				}),
																				(0, i.jsxs)("span", {
																					children: [
																						t.filter(
																							(t) => t.projectTitle === e.title,
																						).length,
																						" offers sent",
																					],
																				}),
																			],
																		}),
																	],
																}),
															],
														}),
													}),
													(0, i.jsx)("div", {
														className: "border-t border-gray-100 p-4",
														children: (0, i.jsx)("div", {
															className: "flex gap-2",
															children: (0, i.jsx)(n.$, {
																variant: "outline",
																className: "flex-1 font-light tracking-wide",
																onClick: () => r(e),
																children: "Edit Project",
															}),
														}),
													}),
												],
											}),
										],
									}),
								})
							: null,
					P = ({
						showModal: e,
						onClose: t,
						formData: s,
						onFormChange: r,
						onSubmit: a,
						onAddArtistRole: l,
						onRemoveArtistRole: o,
						isEditing: c,
					}) =>
						e
							? (0, i.jsx)("div", {
									className:
										"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
									children: (0, i.jsxs)("div", {
										className:
											"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
										children: [
											(0, i.jsxs)("div", {
												className:
													"flex items-center justify-between p-6 border-b border-gray-100",
												children: [
													(0, i.jsx)("h2", {
														className: "text-xl font-light tracking-wide",
														children: c ? "Edit Project" : "Add New Project",
													}),
													(0, i.jsx)("button", {
														onClick: t,
														children: (0, i.jsx)(S.A, { className: "w-5 h-5" }),
													}),
												],
											}),
											(0, i.jsxs)("form", {
												onSubmit: a,
												className:
													"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
												children: [
													(0, i.jsxs)("div", {
														className: "space-y-6",
														children: [
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Project Title *",
																	}),
																	(0, i.jsx)("input", {
																		type: "text",
																		required: !0,
																		value: s.title,
																		onChange: (e) => r("title", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder:
																			"e.g., Summer Fashion Campaign",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Client *",
																	}),
																	(0, i.jsx)("input", {
																		type: "text",
																		required: !0,
																		value: s.client,
																		onChange: (e) =>
																			r("client", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder: "e.g., Nike",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Budget (USD) *",
																	}),
																	(0, i.jsx)("input", {
																		type: "number",
																		required: !0,
																		value: s.budget,
																		onChange: (e) =>
																			r("budget", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder: "15000",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Project Type *",
																	}),
																	(0, i.jsxs)("select", {
																		required: !0,
																		value: s.projectType,
																		onChange: (e) =>
																			r("projectType", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		children: [
																			(0, i.jsx)("option", {
																				value: "",
																				children: "Select Type",
																			}),
																			(0, i.jsx)("option", {
																				value: "Photography",
																				children: "Photography",
																			}),
																			(0, i.jsx)("option", {
																				value: "Videography",
																				children: "Videography",
																			}),
																			(0, i.jsx)("option", {
																				value: "Event Coverage",
																				children: "Event Coverage",
																			}),
																			(0, i.jsx)("option", {
																				value: "Social Media Content",
																				children: "Social Media Content",
																			}),
																			(0, i.jsx)("option", {
																				value: "Brand Identity",
																				children: "Brand Identity",
																			}),
																			(0, i.jsx)("option", {
																				value: "Music Video Production",
																				children: "Music Video Production",
																			}),
																			(0, i.jsx)("option", {
																				value: "Other",
																				children: "Other",
																			}),
																		],
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Start Date *",
																	}),
																	(0, i.jsx)("input", {
																		type: "date",
																		required: !0,
																		value: s.startDate,
																		onChange: (e) =>
																			r("startDate", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "End Date",
																	}),
																	(0, i.jsx)("input", {
																		type: "date",
																		value: s.endDate,
																		onChange: (e) =>
																			r("endDate", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Location *",
																	}),
																	(0, i.jsx)("input", {
																		type: "text",
																		required: !0,
																		value: s.location,
																		onChange: (e) =>
																			r("location", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder: "e.g., Los Angeles, CA",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Description",
																	}),
																	(0, i.jsx)("textarea", {
																		rows: 3,
																		value: s.description,
																		onChange: (e) =>
																			r("description", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder:
																			"Describe your project requirements and vision...",
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsxs)("div", {
																		className:
																			"flex items-center justify-between mb-4",
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"text-lg font-light tracking-wide",
																				children: "creatives Needed",
																			}),
																			(0, i.jsx)(n.$, {
																				type: "button",
																				variant: "outline",
																				size: "sm",
																				onClick: l,
																				className:
																					"text-xs font-light tracking-wide",
																				children: "+ Add Role",
																			}),
																		],
																	}),
																	(0, i.jsxs)("div", {
																		className: "flex flex-wrap gap-2",
																		children: [
																			s.creativesNeeded.map((e, t) =>
																				(0, i.jsxs)(
																					"div",
																					{
																						className:
																							"flex items-center gap-2 bg-gray-100 rounded-full px-3 py-1",
																						children: [
																							(0, i.jsx)("span", {
																								className: "text-sm font-light",
																								children: e,
																							}),
																							(0, i.jsx)("button", {
																								type: "button",
																								onClick: () => o(t),
																								className:
																									"text-red-500 hover:text-red-700",
																								children: "\xd7",
																							}),
																						],
																					},
																					t,
																				),
																			),
																			0 === s.creativesNeeded.length &&
																				(0, i.jsx)("p", {
																					className:
																						"text-gray-500 text-sm font-light",
																					children:
																						"No creative roles added yet.",
																				}),
																		],
																	}),
																],
															}),
														],
													}),
													(0, i.jsxs)("div", {
														className:
															"flex gap-4 mt-8 pt-6 border-t border-gray-100",
														children: [
															(0, i.jsx)(n.$, {
																type: "button",
																variant: "outline",
																onClick: t,
																className: "flex-1 font-light tracking-wide",
																children: "Cancel",
															}),
															(0, i.jsx)(n.$, {
																type: "submit",
																className:
																	"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
																children: c ? "Save Changes" : "Create Project",
															}),
														],
													}),
												],
											}),
										],
									}),
								})
							: null,
					T = (0, a.A)("Star", [
						[
							"path",
							{
								d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",
								key: "r04s7s",
							},
						],
					]),
					A = ({
						showModal: e,
						selectedCollaboration: t,
						reviewForm: s,
						onClose: r,
						onFormChange: a,
						onSubmit: l,
					}) =>
						e && t
							? (0, i.jsx)("div", {
									className:
										"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
									children: (0, i.jsxs)("div", {
										className:
											"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
										children: [
											(0, i.jsxs)("div", {
												className:
													"flex items-center justify-between p-6 border-b border-gray-100",
												children: [
													(0, i.jsx)("h2", {
														className: "text-xl font-light tracking-wide",
														children: "Review Artist",
													}),
													(0, i.jsx)("button", {
														onClick: r,
														children: (0, i.jsx)(S.A, { className: "w-5 h-5" }),
													}),
												],
											}),
											(0, i.jsxs)("form", {
												onSubmit: l,
												className:
													"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",
												children: [
													(0, i.jsxs)("div", {
														className: "space-y-6",
														children: [
															(0, i.jsxs)("div", {
																className: "flex items-center gap-4",
																children: [
																	(0, i.jsx)(w.default, {
																		src: t.artistAvatar || "/placeholder.svg",
																		alt: t.artistName,
																		width: 60,
																		height: 60,
																		className: "rounded-full object-cover",
																	}),
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("h3", {
																				className: "font-light text-lg",
																				children: t.artistName,
																			}),
																			(0, i.jsx)("p", {
																				className: "text-gray-600 font-light",
																				children: t.projectTitle,
																			}),
																		],
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Rating *",
																	}),
																	(0, i.jsx)("div", {
																		className: "flex gap-2",
																		children: [1, 2, 3, 4, 5].map((e) =>
																			(0, i.jsx)(
																				"button",
																				{
																					type: "button",
																					onClick: () => a("rating", e),
																					className: "focus:outline-none",
																					children: (0, i.jsx)(T, {
																						className: `w-8 h-8 ${e <= s.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300 hover:text-yellow-300"} transition-colors`,
																					}),
																				},
																				e,
																			),
																		),
																	}),
																],
															}),
															(0, i.jsxs)("div", {
																children: [
																	(0, i.jsx)("label", {
																		className:
																			"block text-sm font-light text-gray-700 mb-2 tracking-wide",
																		children: "Review",
																	}),
																	(0, i.jsx)("textarea", {
																		rows: 4,
																		value: s.review,
																		onChange: (e) =>
																			a("review", e.target.value),
																		className:
																			"w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black",
																		placeholder:
																			"Share your experience working with this creative...",
																	}),
																],
															}),
														],
													}),
													(0, i.jsxs)("div", {
														className:
															"flex gap-4 mt-8 pt-6 border-t border-gray-100",
														children: [
															(0, i.jsx)(n.$, {
																type: "button",
																variant: "outline",
																onClick: r,
																className: "flex-1 font-light tracking-wide",
																children: "Cancel",
															}),
															(0, i.jsx)(n.$, {
																type: "submit",
																className:
																	"flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide",
																children: "Save Review",
															}),
														],
													}),
												],
											}),
										],
									}),
								})
							: null,
					$ = (0, a.A)("Trash2", [
						["path", { d: "M3 6h18", key: "d0wm0j" }],
						[
							"path",
							{ d: "M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6", key: "4alrt4" },
						],
						[
							"path",
							{ d: "M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2", key: "v07s0e" },
						],
						["line", { x1: "10", x2: "10", y1: "11", y2: "17", key: "1uufr5" }],
						["line", { x1: "14", x2: "14", y1: "11", y2: "17", key: "xtxkd" }],
					]),
					D = (0, a.A)("Plus", [
						["path", { d: "M5 12h14", key: "1ays0h" }],
						["path", { d: "M12 5v14", key: "s699le" }],
					]),
					_ = [
						{
							id: "pm_1",
							type: "Visa",
							last4: "1234",
							expiry: "08/25",
							isDefault: !0,
						},
						{
							id: "pm_2",
							type: "Mastercard",
							last4: "5678",
							expiry: "12/26",
							isDefault: !1,
						},
						{
							id: "pm_3",
							type: "Amex",
							last4: "9012",
							expiry: "03/24",
							isDefault: !1,
						},
					],
					q = ({ showModal: e, onClose: t }) =>
						e
							? (0, i.jsx)("div", {
									className:
										"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",
									children: (0, i.jsxs)("div", {
										className:
											"bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden",
										children: [
											(0, i.jsxs)("div", {
												className:
													"flex items-center justify-between p-6 border-b border-gray-100",
												children: [
													(0, i.jsx)("h2", {
														className: "text-xl font-light tracking-wide",
														children: "Manage Payment Methods",
													}),
													(0, i.jsx)("button", {
														onClick: t,
														children: (0, i.jsx)(S.A, { className: "w-5 h-5" }),
													}),
												],
											}),
											(0, i.jsxs)("div", {
												className:
													"p-6 overflow-y-auto max-h-[calc(90vh-180px)]",
												children: [
													" ",
													(0, i.jsxs)("div", {
														className: "space-y-4 mb-6",
														children: [
															_.map((e) =>
																(0, i.jsxs)(
																	"div",
																	{
																		className:
																			"border border-gray-200 rounded p-4 flex items-center justify-between hover:shadow-sm transition-shadow",
																		children: [
																			(0, i.jsxs)("div", {
																				className: "flex items-center gap-3",
																				children: [
																					(0, i.jsx)(l, {
																						className: `w-6 h-6 ${e.isDefault ? "text-blue-600" : "text-gray-500"}`,
																					}),
																					(0, i.jsxs)("div", {
																						children: [
																							(0, i.jsxs)("span", {
																								className:
																									"font-light tracking-wide text-sm text-gray-800",
																								children: [
																									e.type,
																									" ending in ",
																									e.last4,
																								],
																							}),
																							(0, i.jsxs)("span", {
																								className:
																									"block text-xs text-gray-500 font-light",
																								children: [
																									"Expires ",
																									e.expiry,
																								],
																							}),
																						],
																					}),
																					e.isDefault &&
																						(0, i.jsx)("span", {
																							className:
																								"px-2 py-0.5 text-xs font-light tracking-wide bg-blue-100 text-blue-700 rounded-full",
																							children: "Default",
																						}),
																				],
																			}),
																			(0, i.jsxs)("div", {
																				className: "flex items-center gap-2",
																				children: [
																					!e.isDefault &&
																						(0, i.jsx)(n.$, {
																							variant: "outline",
																							size: "sm",
																							className:
																								"text-xs font-light tracking-wide",
																							children: "Set as Default",
																						}),
																					(0, i.jsx)(n.$, {
																						variant: "ghost",
																						size: "sm",
																						className:
																							"text-red-500 hover:text-red-700",
																						children: (0, i.jsx)($, {
																							className: "w-4 h-4",
																						}),
																					}),
																				],
																			}),
																		],
																	},
																	e.id,
																),
															),
															0 === _.length &&
																(0, i.jsx)("p", {
																	className:
																		"text-center text-gray-500 font-light py-8",
																	children: "No payment methods saved.",
																}),
														],
													}),
												],
											}),
											(0, i.jsx)("div", {
												className:
													"p-6 border-t border-gray-100 flex justify-end",
												children: (0, i.jsxs)(n.$, {
													className:
														"bg-black text-white hover:bg-gray-800 font-light tracking-wide",
													children: [
														(0, i.jsx)(D, { className: "w-4 h-4 mr-2" }),
														"Add New Card",
													],
												}),
											}),
										],
									}),
								})
							: null;
				var z = s(21345);
				const L = ({
						projects: e,
						selectedTiles: t,
						isSelectionMode: s,
						editingTile: r,
						tempText: a,
						onTileClick: l,
						onStartEditingText: o,
						onSetTempText: c,
						onSaveTextEdit: d,
						onCancelTextEdit: x,
						onSplitTile: h,
					}) =>
						(0, i.jsx)("div", {
							className: "grid grid-cols-3 gap-2",
							children: e.map((e) =>
								(0, i.jsxs)(
									"div",
									{
										className: `relative group cursor-pointer aspect-[3/4] ${t.has(e.id) ? "ring-2 ring-black" : ""} ${s ? "hover:ring-1 hover:ring-gray-400" : ""}`,
										onClick: () => l(e),
										children: [
											"text" === e.type
												? (0, i.jsx)("div", {
														className:
															"w-full h-full flex items-center justify-center p-4 text-center relative",
														style: {
															backgroundColor: e.backgroundColor || "#f3f4f6",
														},
														children:
															r === e.id
																? (0, i.jsxs)("div", {
																		className: "w-full h-full flex flex-col",
																		onClick: (e) => e.stopPropagation(),
																		children: [
																			(0, i.jsx)("textarea", {
																				value: a,
																				onChange: (e) => c(e.target.value),
																				className:
																					"flex-1 w-full p-2 border-none outline-none resize-none bg-transparent text-center font-light",
																				placeholder: "Enter your text...",
																				autoFocus: !0,
																				onClick: (e) => e.stopPropagation(),
																			}),
																			(0, i.jsxs)("div", {
																				className: "flex gap-1 mt-2",
																				children: [
																					(0, i.jsx)("button", {
																						onClick: (e) => {
																							e.stopPropagation(), d();
																						},
																						className:
																							"px-2 py-1 bg-black text-white text-xs rounded font-light",
																						children: "Save",
																					}),
																					(0, i.jsx)("button", {
																						onClick: (e) => {
																							e.stopPropagation(), x();
																						},
																						className:
																							"px-2 py-1 bg-gray-500 text-white text-xs rounded font-light",
																						children: "Cancel",
																					}),
																				],
																			}),
																		],
																	})
																: (0, i.jsx)("p", {
																		className:
																			"text-gray-800 font-light break-words cursor-text z-20 relative whitespace-pre-line",
																		onClick: (t) => {
																			t.stopPropagation(),
																				s || o(e.id, e.textContent || "");
																		},
																		children:
																			e.textContent || "Click to edit text",
																	}),
													})
												: (0, i.jsx)(w.default, {
														src: e.src || "/placeholder.svg",
														alt: e.alt,
														fill: !0,
														className: "object-cover",
														sizes:
															"(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw",
													}),
											(0, i.jsxs)("div", {
												className:
													"absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm p-3 border-t border-gray-100",
												children: [
													(0, i.jsx)("h3", {
														className:
															"font-light text-sm tracking-wide mb-1 truncate",
														children: e.title,
													}),
													(0, i.jsx)("p", {
														className:
															"text-xs text-gray-600 mb-1 truncate font-light",
														children: e.client,
													}),
													(0, i.jsxs)("div", {
														className:
															"flex items-center justify-between text-xs text-gray-500",
														children: [
															(0, i.jsx)("span", {
																className: "truncate font-light",
																children: e.projectType || "Creative Project",
															}),
															(0, i.jsxs)("span", {
																className: "font-light text-black",
																children: [
																	"$",
																	(e.budget / 1e3).toFixed(0),
																	"K",
																],
															}),
														],
													}),
													e.location &&
														(0, i.jsx)("p", {
															className:
																"text-xs text-gray-500 mt-1 truncate font-light",
															children: e.location,
														}),
												],
											}),
											(0, i.jsx)("div", {
												className: "absolute top-2 right-2 flex gap-1 z-20",
												children: (0, i.jsx)("div", {
													className: `px-2 py-1 text-xs font-light tracking-wide rounded ${"active" === e.status ? "bg-blue-100 text-blue-800" : "completed" === e.status ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"}`,
													children:
														e.status.charAt(0).toUpperCase() +
														e.status.slice(1),
												}),
											}),
											(0, i.jsxs)("div", {
												className: "absolute top-2 left-2 flex gap-1 z-20",
												children: [
													e.isMerged &&
														(0, i.jsx)("div", {
															className: "bg-black text-white rounded-full p-1",
															title: "Merged Tile",
															children: (0, i.jsx)(z.A, {
																className: "w-3 h-3",
															}),
														}),
													"text" === e.type &&
														(0, i.jsx)("div", {
															className: "bg-black text-white rounded-full p-1",
															title: "Text Tile",
															children: (0, i.jsx)("span", {
																className: "text-xs font-light",
																children: "T",
															}),
														}),
												],
											}),
											("text" !== e.type || s) &&
												(0, i.jsx)("div", {
													className: `absolute inset-0 bottom-20 bg-black/0 hover:bg-black/30 transition-all duration-200 flex items-center justify-center opacity-0 ${r === e.id ? "opacity-0 pointer-events-none" : "group-hover:opacity-100"} z-10`,
													children: s
														? (0, i.jsx)("div", {
																className:
																	"text-white font-light text-lg drop-shadow-lg tracking-wide",
																children: t.has(e.id) ? "✓ Selected" : "Select",
															})
														: (0, i.jsxs)("div", {
																className:
																	"flex flex-col items-center gap-2 text-white",
																children: [
																	(0, i.jsxs)("div", {
																		className: "text-center",
																		children: [
																			(0, i.jsx)("h3", {
																				className:
																					"font-light text-lg drop-shadow-lg",
																				children: e.title,
																			}),
																			(0, i.jsxs)("p", {
																				className:
																					"text-sm drop-shadow-lg font-light",
																				children: [
																					"$",
																					e.budget.toLocaleString(),
																				],
																			}),
																		],
																	}),
																	e.isMerged &&
																		(0, i.jsx)(n.$, {
																			size: "sm",
																			variant: "secondary",
																			onClick: (t) => h(e, t),
																			className:
																				"bg-white/80 hover:bg-white text-black text-xs font-light tracking-wide",
																			children: "Split",
																		}),
																],
															}),
												}),
										],
									},
									e.id,
								),
							),
						}),
					E = ({ offers: e }) =>
						0 === e.length
							? (0, i.jsx)("div", {
									className: "text-center py-12",
									children: (0, i.jsx)("p", {
										className: "text-gray-500 font-light tracking-wide",
										children: "No offers have been sent yet.",
									}),
								})
							: (0, i.jsxs)("div", {
									className: "space-y-6",
									children: [
										(0, i.jsx)("h2", {
											className:
												"text-xl font-light tracking-wide text-center mb-12",
											children: "Offers Sent to creatives",
										}),
										e.map((e) =>
											(0, i.jsx)(
												"div",
												{
													className:
														"border border-gray-100 p-6 hover:shadow-sm transition-shadow",
													children: (0, i.jsxs)("div", {
														className: "flex items-start justify-between",
														children: [
															(0, i.jsxs)("div", {
																className: "flex-1",
																children: [
																	(0, i.jsx)("h3", {
																		className:
																			"font-light tracking-wide text-lg mb-2",
																		children: e.projectTitle,
																	}),
																	(0, i.jsxs)("p", {
																		className:
																			"text-gray-600 mb-2 font-light tracking-wide",
																		children: [
																			"Artist:",
																			(0, i.jsx)(C(), {
																				href: `/creative/${e.artistId}`,
																				className:
																					"text-blue-600 hover:text-blue-800 underline ml-1",
																				children: e.artistName,
																			}),
																		],
																	}),
																	(0, i.jsxs)("div", {
																		className:
																			"flex items-center gap-6 text-sm text-gray-500 font-light tracking-wide",
																		children: [
																			(0, i.jsx)("span", {
																				children: e.projectType,
																			}),
																			(0, i.jsxs)("span", {
																				children: [
																					"$",
																					e.amount.toLocaleString(),
																				],
																			}),
																			(0, i.jsx)("span", {
																				children: e.location,
																			}),
																			(0, i.jsx)("span", { children: e.date }),
																		],
																	}),
																],
															}),
															(0, i.jsx)("div", {
																className: "text-right",
																children: (0, i.jsx)("span", {
																	className: `px-3 py-1 text-xs font-light tracking-wide ${"accepted" === e.status ? "bg-green-100 text-green-800" : "declined" === e.status ? "bg-red-100 text-red-800" : "expired" === e.status ? "bg-gray-100 text-gray-800" : "bg-yellow-100 text-yellow-800"}`,
																	children:
																		e.status.charAt(0).toUpperCase() +
																		e.status.slice(1),
																}),
															}),
														],
													}),
												},
												e.id,
											),
										),
									],
								});
				var B = s(34849);
				const I = ({
					totalBudget: e = 0,
					depositedBudget: t = 0,
					budgetLeft: s = 0,
					projects: r = [],
					transactions: a = [],
				}) => {
					const l = (e) => {
							switch (e) {
								case "deposit":
									return "text-green-600";
								case "payment":
								case "fee":
									return "text-red-600";
								case "refund":
									return "text-blue-600";
								default:
									return "text-gray-800";
							}
						},
						n = (e) => {
							if ("payment" === e.type && e.creativeName) {
								let t = `Paid to: ${e.creativeName}`;
								return e.projectName && (t += ` for ${e.projectName}`), t;
							}
							return e.description;
						};
					return (0, i.jsxs)("div", {
						className: "py-8",
						children: [
							(0, i.jsxs)("div", {
								className:
									"grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-16 px-4",
								children: [
									(0, i.jsxs)("div", {
										children: [
											(0, i.jsx)("h2", {
												className:
													"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
												children: "Deposited Budget",
											}),
											(0, i.jsxs)("div", {
												className: "text-4xl font-light text-green-600 mb-2",
												children: ["$", (t || 0).toLocaleString()],
											}),
										],
									}),
									(0, i.jsxs)("div", {
										children: [
											(0, i.jsx)("h2", {
												className:
													"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
												children: "Total Allocated",
											}),
											(0, i.jsxs)("div", {
												className: "text-4xl font-light text-blue-600 mb-2",
												children: ["$", (e || 0).toLocaleString()],
											}),
											(0, i.jsx)("p", {
												className:
													"text-gray-500 text-sm font-light tracking-wide",
												children: "Across All Projects",
											}),
										],
									}),
									(0, i.jsxs)("div", {
										children: [
											(0, i.jsx)("h2", {
												className:
													"text-lg font-light tracking-wide text-gray-500 mb-3 uppercase",
												children: "Budget Left",
											}),
											(0, i.jsxs)("div", {
												className: "text-4xl font-light text-orange-500 mb-2",
												children: ["$", (s || 0).toLocaleString()],
											}),
										],
									}),
								],
							}),
							r.length > 0 &&
								(0, i.jsxs)("div", {
									className: "mb-16 px-4",
									children: [
										(0, i.jsx)("h3", {
											className:
												"text-2xl font-light tracking-wide mb-6 text-center text-gray-700",
											children: "Project Allocations",
										}),
										(0, i.jsx)("div", {
											className: "bg-white shadow overflow-hidden rounded-lg",
											children: (0, i.jsx)("ul", {
												className: "divide-y divide-gray-200",
												children: r.map((e) =>
													(0, i.jsx)(
														"li",
														{
															className:
																"px-6 py-4 hover:bg-gray-50 transition-colors",
															children: (0, i.jsxs)("div", {
																className: "flex items-center justify-between",
																children: [
																	(0, i.jsxs)("div", {
																		children: [
																			(0, i.jsx)("p", {
																				className:
																					"text-md font-medium text-gray-800 truncate",
																				children: e.name,
																			}),
																			e.status &&
																				(0, i.jsxs)("p", {
																					className: "text-xs text-gray-500",
																					children: ["Status: ", e.status],
																				}),
																		],
																	}),
																	(0, i.jsxs)("div", {
																		className:
																			"text-md text-blue-600 font-light",
																		children: [
																			"$",
																			e.allocatedBudget.toLocaleString(),
																		],
																	}),
																],
															}),
														},
														e.id,
													),
												),
											}),
										}),
									],
								}),
							a.length > 0 &&
								(0, i.jsxs)("div", {
									className: "px-4 mt-16",
									children: [
										(0, i.jsx)("h3", {
											className:
												"text-2xl font-light tracking-wide mb-6 text-center text-gray-700",
											children: "Payment History",
										}),
										(0, i.jsx)("div", {
											className: "bg-white shadow overflow-hidden rounded-lg",
											children: (0, i.jsxs)("table", {
												className: "min-w-full divide-y divide-gray-200",
												children: [
													(0, i.jsx)("thead", {
														className: "bg-gray-50",
														children: (0, i.jsxs)("tr", {
															children: [
																(0, i.jsx)("th", {
																	scope: "col",
																	className:
																		"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
																	children: "Date",
																}),
																(0, i.jsx)("th", {
																	scope: "col",
																	className:
																		"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",
																	children: "Details",
																}),
																(0, i.jsx)("th", {
																	scope: "col",
																	className:
																		"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",
																	children: "Amount",
																}),
															],
														}),
													}),
													(0, i.jsx)("tbody", {
														className: "bg-white divide-y divide-gray-200",
														children: a.map((e) =>
															(0, i.jsxs)(
																"tr",
																{
																	className:
																		"hover:bg-gray-50 transition-colors",
																	children: [
																		(0, i.jsx)("td", {
																			className:
																				"px-6 py-4 whitespace-nowrap text-sm text-gray-500",
																			children: (0, B.GP)(
																				new Date(e.date),
																				"MMM dd, yyyy",
																			),
																		}),
																		(0, i.jsx)("td", {
																			className:
																				"px-6 py-4 text-sm text-gray-800",
																			children: n(e),
																		}),
																		(0, i.jsxs)("td", {
																			className: `px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${l(e.type)}`,
																			children: [
																				"deposit" === e.type ||
																				"refund" === e.type
																					? "+"
																					: "-",
																				"$",
																				Math.abs(e.amount).toLocaleString(),
																			],
																		}),
																	],
																},
																e.id,
															),
														),
													}),
												],
											}),
										}),
									],
								}),
							0 === r.length &&
								0 === a.length &&
								(0, i.jsx)("div", {
									className: "text-center text-gray-400 font-light mt-12 px-4",
									children: (0, i.jsx)("p", {
										children:
											"No project allocations or transaction history to display.",
									}),
								}),
						],
					});
				};
				var R = s(72902);
				const O = (0, a.A)("PenLine", [
					["path", { d: "M12 20h9", key: "t2du7b" }],
					[
						"path",
						{
							d: "M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",
							key: "1ykcvy",
						},
					],
				]);
				var U = s(14132),
					V = s(48414);
				const F = "__ALL_TYPES__",
					G = ({ collaborations: e, onOpenReviewModal: t }) => {
						const [s, a] = (0, r.useState)(""),
							[l, o] = (0, r.useState)(""),
							c = (0, r.useMemo)(
								() =>
									Array.from(
										new Set(
											e
												.map((e) => e.projectType)
												.filter((e) => e && "" !== e.trim()),
										),
									).sort(),
								[e],
							),
							d = e.filter((e) => {
								const t = s.toLowerCase(),
									i =
										"" === t ||
										e.projectTitle.toLowerCase().includes(t) ||
										e.artistName.toLowerCase().includes(t),
									r = "" === l || e.projectType === l;
								return i && r;
							});
						return 0 === e.length
							? (0, i.jsx)("div", {
									className: "text-center py-12",
									children: (0, i.jsx)("p", {
										className: "text-gray-500 font-light tracking-wide",
										children: "No past collaborations to display.",
									}),
								})
							: (0, i.jsxs)("div", {
									children: [
										(0, i.jsx)("h2", {
											className:
												"text-xl font-light tracking-wide text-center mb-6",
											children: "Past Collaborations",
										}),
										(0, i.jsxs)("div", {
											className:
												"mb-8 max-w-xl mx-auto flex flex-col md:flex-row gap-4 items-center",
											children: [
												(0, i.jsxs)("div", {
													className: "relative flex-grow w-full md:w-auto",
													children: [
														(0, i.jsx)("div", {
															className:
																"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",
															children: (0, i.jsx)(R.A, {
																className: "h-4 w-4 text-gray-400",
															}),
														}),
														(0, i.jsx)(U.p, {
															type: "text",
															placeholder: "Search by project or creative...",
															value: s,
															onChange: (e) => a(e.target.value),
															className:
																"w-full pl-10 pr-4 py-2 h-10 border-gray-200 rounded-md font-light focus:border-gray-300 focus:ring-0",
														}),
													],
												}),
												(0, i.jsx)("div", {
													className: "w-full md:w-auto md:min-w-[200px]",
													children: (0, i.jsxs)(V.l6, {
														value: l,
														onValueChange: (e) => {
															e === F ? o("") : o(e);
														},
														children: [
															(0, i.jsx)(V.bq, {
																className:
																	"w-full h-10 border-gray-200 rounded-md font-light focus:ring-0",
																children: (0, i.jsx)(V.yv, {
																	placeholder: "Filter by type...",
																}),
															}),
															(0, i.jsxs)(V.gC, {
																children: [
																	(0, i.jsx)(V.eb, {
																		value: F,
																		className: "font-light",
																		children: "All Types",
																	}),
																	c.map((e) =>
																		(0, i.jsx)(
																			V.eb,
																			{
																				value: e,
																				className: "font-light",
																				children: e,
																			},
																			e,
																		),
																	),
																],
															}),
														],
													}),
												}),
											],
										}),
										0 === d.length &&
											(s || l) &&
											(0, i.jsx)("div", {
												className: "text-center py-12",
												children: (0, i.jsx)("p", {
													className: "text-gray-500 font-light tracking-wide",
													children:
														"No collaborations found matching your criteria.",
												}),
											}),
										(0, i.jsx)("div", {
											className:
												"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8",
											children: d.map((e) =>
												(0, i.jsxs)(
													"div",
													{
														className:
															"group bg-white rounded-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300 ease-in-out flex flex-col overflow-hidden",
														children: [
															(0, i.jsx)("div", {
																className:
																	"relative aspect-[3/4] w-full overflow-hidden bg-gray-50",
																children: (0, i.jsx)(w.default, {
																	src: e.artistAvatar || "/placeholder.svg",
																	alt: e.artistName,
																	fill: !0,
																	className:
																		"object-cover transition-transform duration-300 group-hover:scale-105",
																}),
															}),
															(0, i.jsxs)("div", {
																className: "p-4 flex flex-col flex-grow",
																children: [
																	(0, i.jsx)("h3", {
																		className:
																			"text-base font-light tracking-wide text-gray-800 mb-1 truncate",
																		children: e.projectTitle,
																	}),
																	(0, i.jsxs)("p", {
																		className:
																			"text-sm text-gray-500 font-light mb-2",
																		children: [
																			"Artist: ",
																			(0, i.jsx)("span", {
																				className: "text-gray-700",
																				children: e.artistName,
																			}),
																		],
																	}),
																	(0, i.jsxs)("div", {
																		className:
																			"text-xs text-gray-400 font-light mb-3 space-y-0.5",
																		children: [
																			(0, i.jsxs)("p", {
																				children: ["Type: ", e.projectType],
																			}),
																			(0, i.jsxs)("p", {
																				children: [
																					"Budget: $",
																					e.budget.toLocaleString(),
																				],
																			}),
																			(0, i.jsxs)("p", {
																				children: [
																					"Completed: ",
																					e.completedDate,
																				],
																			}),
																		],
																	}),
																	(0, i.jsxs)("div", {
																		className: "flex items-center gap-1 mb-2",
																		children: [
																			[1, 2, 3, 4, 5].map((t) =>
																				(0, i.jsx)(
																					T,
																					{
																						className: `w-3.5 h-3.5 ${t <= (e.rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`,
																					},
																					t,
																				),
																			),
																			e.rating
																				? (0, i.jsxs)("span", {
																						className:
																							"text-xs text-gray-500 ml-1 font-light",
																						children: ["(", e.rating, "/5)"],
																					})
																				: (0, i.jsx)("span", {
																						className:
																							"text-xs text-gray-400 font-light",
																						children: "Not rated",
																					}),
																		],
																	}),
																	e.review &&
																		(0, i.jsxs)("p", {
																			className:
																				"text-xs text-gray-600 italic font-light mb-3 flex-grow leading-relaxed line-clamp-3",
																			children: ['"', e.review, '"'],
																		}),
																	!e.review &&
																		(0, i.jsx)("div", {
																			className: "flex-grow min-h-[2rem]",
																		}),
																	(0, i.jsx)("div", {
																		className: "mt-auto pt-3",
																		children: (0, i.jsxs)(n.$, {
																			variant: "outline",
																			size: "sm",
																			onClick: () => t(e),
																			className:
																				"w-full text-xs font-light tracking-wide hover:border-gray-400",
																			children: [
																				(0, i.jsx)(O, {
																					className: "w-3 h-3 mr-1.5",
																				}),
																				e.review || e.rating
																					? "Edit Review"
																					: "Add Review",
																			],
																		}),
																	}),
																],
															}),
														],
													},
													e.id,
												),
											),
										}),
									],
								});
					};
				var H = s(79231),
					W = s(27095);
				const J = ({ members: e, onInviteMember: t }) =>
						(0, i.jsxs)("div", {
							className: "py-8",
							children: [
								(0, i.jsxs)("div", {
									className:
										"flex justify-between items-center mb-6 px-4 md:px-0",
									children: [
										(0, i.jsx)("h2", {
											className:
												"text-2xl font-light tracking-wide text-gray-700",
											children: "Members",
										}),
										(0, i.jsxs)(n.$, {
											variant: "outline",
											size: "sm",
											onClick: t,
											className: "text-xs font-light tracking-wide",
											children: [
												(0, i.jsx)(W.A, { className: "w-4 h-4 mr-2" }),
												"+ Member",
											],
										}),
									],
								}),
								e.length > 0
									? (0, i.jsx)("ul", {
											className: "space-y-2",
											children: e.map((e) =>
												(0, i.jsxs)(
													"li",
													{
														className:
															"p-3 border border-gray-200 rounded-md text-sm bg-white shadow-sm hover:shadow-md transition-shadow",
														children: [
															(0, i.jsx)("span", {
																className: "font-medium text-gray-800",
																children: e.name,
															}),
															" - ",
															(0, i.jsx)("span", {
																className: "capitalize text-gray-600",
																children: e.permission,
															}),
														],
													},
													e.id,
												),
											),
										})
									: (0, i.jsxs)("div", {
											className: "text-center text-gray-500 font-light py-10",
											children: [
												(0, i.jsx)("p", {
													children:
														"No members have been added to this booker profile yet.",
												}),
												(0, i.jsxs)(n.$, {
													variant: "default",
													size: "sm",
													onClick: t,
													className: "mt-4 text-xs font-light tracking-wide",
													children: [
														(0, i.jsx)(W.A, { className: "w-4 h-4 mr-2" }),
														"Invite First Member",
													],
												}),
											],
										}),
							],
						}),
					K = [
						{
							id: "m1",
							name: "Alice Wonderland",
							avatarUrl: "/avatars/alice.png",
							permission: "admin",
						},
						{
							id: "m2",
							name: "Bob The Builder",
							avatarUrl: "/avatars/bob.png",
							permission: "editor",
						},
						{
							id: "m3",
							name: "Charlie Chaplin",
							avatarUrl: "/avatars/charlie.png",
							permission: "viewer",
						},
						{ id: "m4", name: "Diana Prince", permission: "viewer" },
					];
				function Z({ bookerId: e = "1" }) {
					const t = (0, o.useRouter)(),
						[s, a] = (0, r.useState)(c.R4),
						[p, u] = (0, r.useState)(new Set()),
						[y, w] = (0, r.useState)("projects"),
						[k, C] = (0, r.useState)(K),
						{
							selectedProject: S,
							openProjectModal: T,
							closeProjectModal: $,
							showPaymentModal: D,
							openPaymentModal: _,
							closePaymentModal: z,
						} = x(),
						{
							selectedTiles: B,
							isSelectionMode: R,
							toggleTileSelection: O,
							clearSelection: U,
							mergeTiles: V,
							splitTile: F,
							convertToTextTile: W,
							convertToImageTile: Z,
						} = h({ projects: s, setProjects: a }),
						{
							editingTile: Y,
							tempText: X,
							startEditingText: Q,
							handleSetTempText: ee,
							saveTextEdit: et,
							cancelTextEdit: es,
						} = g({ setProjects: a }),
						{
							showReviewModal: ei,
							selectedCollaboration: er,
							reviewForm: ea,
							openReviewModal: el,
							closeReviewModal: en,
							updateReviewFormField: eo,
							handleReviewSubmit: ec,
						} = m(),
						{
							showAddProjectModal: ed,
							editingProject: ex,
							newProjectForm: eh,
							openAddProjectModal: eg,
							closeAddProjectModal: ep,
							updateNewProjectFormField: em,
							handleAddProjectSubmit: eu,
							addArtistRoleToForm: ef,
							removeArtistRoleFromForm: ej,
						} = f({ projects: s, setProjects: a }),
						eb = c.gL.reduce((e, t) => e + t.amount, 0),
						ey = c.Qh[e] || c.Qh["1"],
						ev = d.R4.map((e) => ({
							id: e.id,
							name: e.title,
							allocatedBudget: e.budget,
							status: "Active",
						})),
						eN = d.KT.map((e) => ({
							id: e.id,
							date: e.date,
							description: e.jobTitle,
							amount: e.amount,
							type: e.status,
							creativeName: void 0,
							projectName: void 0,
						})),
						ew = ev.reduce((e, t) => e + t.allocatedBudget, 0);
					return (0, i.jsxs)("div", {
						className: "min-h-screen bg-white font-light",
						children: [
							(0, i.jsx)(j.A, {}),
							(0, i.jsx)("div", {
								className: "border-b border-gray-100",
								children: (0, i.jsxs)(H.A, {
									children: [
										(0, i.jsx)(b, {
											name: ey.name,
											type: ey.type,
											projectCount: s.length,
											offersSentCount: c.Ex.length,
											collaborationsCount: c.Zd.length,
											membersCount: k.length,
										}),
										(0, i.jsx)(v, { activeTab: y }),
										"budget" === y &&
											(0, i.jsx)("div", {
												className: "text-center",
												children: (0, i.jsxs)(n.$, {
													onClick: _,
													className:
														"bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide",
													children: [
														(0, i.jsx)(l, { className: "w-4 h-4 mr-2" }),
														"Manage Payment Methods",
													],
												}),
											}),
									],
								}),
							}),
							(0, i.jsx)(N, {
								activeTab: y,
								onTabChange: w,
								projectCount: s.length,
								offersSentCount: c.Ex.length,
								totalBudgetFormatted: `$${eb.toLocaleString()}`,
								archiveCount: c.Zd.length,
								membersCount: k.length,
								isSelectionMode: R,
								onOpenAddProjectModal: eg,
								onMergeTiles: V,
								onClearSelection: U,
								onConvertToText: () => {
									Array.from(B).forEach(W), U();
								},
								onConvertToImage: () => {
									Array.from(B).forEach(Z), U();
								},
								selectedTilesCount: B.size,
							}),
							(0, i.jsx)(H.A, {
								children:
									"projects" === y
										? (0, i.jsx)(L, {
												projects: s,
												selectedTiles: B,
												isSelectionMode: R,
												editingTile: Y,
												tempText: X,
												onTileClick: (e) => {
													if ("text" === e.type && Y !== e.id && !R) {
														Q(e.id, e.textContent || "");
														return;
													}
													R ? O(e.id) : t.push(`/booker/project/${e.id}`);
												},
												onStartEditingText: Q,
												onSetTempText: ee,
												onSaveTextEdit: et,
												onCancelTextEdit: es,
												onSplitTile: F,
											})
										: "offers" === y
											? (0, i.jsx)(E, { offers: c.Ex })
											: "budget" === y
												? (0, i.jsx)(I, {
														totalBudget: ew,
														depositedBudget: 15e4,
														budgetLeft: 15e4 - ew,
														projects: ev,
														transactions: eN,
													})
												: "members" === y
													? (0, i.jsx)(J, {
															members: k,
															onInviteMember: () => {
																console.log(
																	"Invite new member button clicked. TODO: Implement invitation modal.",
																),
																	alert(
																		"Placeholder: Invite new member functionality to be implemented.",
																	);
															},
														})
													: (0, i.jsx)(G, {
															collaborations: c.Zd,
															onOpenReviewModal: el,
														}),
							}),
							S &&
								(0, i.jsx)(M, {
									selectedProject: S,
									offersSent: c.Ex,
									onClose: $,
									onEditProject: (e) => {
										eg(e), $();
									},
								}),
							(0, i.jsx)(P, {
								showModal: ed,
								onClose: ep,
								formData: eh,
								onFormChange: em,
								onSubmit: eu,
								onAddArtistRole: ef,
								onRemoveArtistRole: ej,
								isEditing: !!ex,
							}),
							(0, i.jsx)(A, {
								showModal: ei,
								selectedCollaboration: er,
								reviewForm: ea,
								onClose: en,
								onFormChange: eo,
								onSubmit: ec,
							}),
							(0, i.jsx)(q, { showModal: D, onClose: z }),
						],
					});
				}
			},
			37067: (e) => {
				e.exports = require("node:http");
			},
			37540: (e) => {
				e.exports = require("node:console");
			},
			38522: (e) => {
				e.exports = require("node:zlib");
			},
			41204: (e) => {
				e.exports = require("string_decoder");
			},
			41692: (e) => {
				e.exports = require("node:tls");
			},
			41792: (e) => {
				e.exports = require("node:querystring");
			},
			46909: (e, t, s) => {
				s.d(t, { A: () => i });
				const i = (0, s(22752).A)("Ellipsis", [
					["circle", { cx: "12", cy: "12", r: "1", key: "41hilf" }],
					["circle", { cx: "19", cy: "12", r: "1", key: "1wjl8i" }],
					["circle", { cx: "5", cy: "12", r: "1", key: "1pcz8c" }],
				]);
			},
			50601: (e, t, s) => {
				s.r(t),
					s.d(t, {
						GlobalError: () => l.a,
						__next_app__: () => x,
						pages: () => d,
						routeModule: () => h,
						tree: () => c,
					});
				var i = s(7025),
					r = s(18198),
					a = s(82576),
					l = s.n(a),
					n = s(45239),
					o = {};
				for (const e in n)
					0 >
						[
							"default",
							"tree",
							"pages",
							"GlobalError",
							"__next_app__",
							"routeModule",
						].indexOf(e) && (o[e] = () => n[e]);
				s.d(t, o);
				const c = {
						children: [
							"",
							{
								children: [
									"booker",
									{
										children: [
											"[id]",
											{
												children: [
													"__PAGE__",
													{},
													{
														page: [
															() => Promise.resolve().then(s.bind(s, 79282)),
															"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/[id]/page.tsx",
														],
													},
												],
											},
											{},
										],
									},
									{},
								],
							},
							{
								layout: [
									() => Promise.resolve().then(s.bind(s, 59650)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/layout.tsx",
								],
								loading: [
									() => Promise.resolve().then(s.bind(s, 34314)),
									"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/loading.tsx",
								],
								"not-found": [
									() => Promise.resolve().then(s.t.bind(s, 4540, 23)),
									"next/dist/client/components/not-found-error",
								],
								forbidden: [
									() => Promise.resolve().then(s.t.bind(s, 53117, 23)),
									"next/dist/client/components/forbidden-error",
								],
								unauthorized: [
									() => Promise.resolve().then(s.t.bind(s, 6874, 23)),
									"next/dist/client/components/unauthorized-error",
								],
							},
						],
					}.children,
					d = [
						"/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/app/booker/[id]/page.tsx",
					],
					x = { require: s, loadChunk: () => Promise.resolve() },
					h = new i.AppPageRouteModule({
						definition: {
							kind: r.RouteKind.APP_PAGE,
							page: "/booker/[id]/page",
							pathname: "/booker/[id]",
							bundlePath: "",
							filename: "",
							appPaths: [],
						},
						userland: { loaderTree: c },
					});
			},
			53053: (e) => {
				e.exports = require("node:diagnostics_channel");
			},
			55511: (e) => {
				e.exports = require("crypto");
			},
			57075: (e) => {
				e.exports = require("node:stream");
			},
			57975: (e) => {
				e.exports = require("node:util");
			},
			63033: (e) => {
				e.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");
			},
			73136: (e) => {
				e.exports = require("node:url");
			},
			73429: (e) => {
				e.exports = require("node:util/types");
			},
			73496: (e) => {
				e.exports = require("http2");
			},
			74075: (e) => {
				e.exports = require("zlib");
			},
			75919: (e) => {
				e.exports = require("node:worker_threads");
			},
			77030: (e) => {
				e.exports = require("node:net");
			},
			77598: (e) => {
				e.exports = require("node:crypto");
			},
			78474: (e) => {
				e.exports = require("node:events");
			},
			79282: (e, t, s) => {
				s.r(t), s.d(t, { default: () => a });
				var i = s(95479),
					r = s(22920);
				function a({ params: e }) {
					return (0, i.jsx)(r.default, { bookerId: e.id });
				}
			},
			79428: (e) => {
				e.exports = require("buffer");
			},
			79551: (e) => {
				e.exports = require("url");
			},
			81630: (e) => {
				e.exports = require("http");
			},
			91645: (e) => {
				e.exports = require("net");
			},
			94735: (e) => {
				e.exports = require("events");
			},
		});
	var t = require("../../../webpack-runtime.js");
	t.C(e);
	var s = (e) => t((t.s = e)),
		i = t.X(0, [959, 40, 482, 704, 227, 849, 202, 577], () => s(50601));
	module.exports = i;
})();
