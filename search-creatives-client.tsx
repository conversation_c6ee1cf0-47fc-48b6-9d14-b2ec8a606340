"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { format } from "date-fns";
import { CalendarIcon, Menu, Search, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useUser } from "reactfire";
import type { Creative } from "@/src/types/collections/creative";
import { useDashboardNavigation } from "@/components/hooks/use-dashboard-navigation";

const creativeTypes = [
	"PHOTOGRAPHER",
	"VIDEOGRAPHER",
	"<PERSON><PERSON><PERSON>",
	"MAKEUP ARTIST",
	"HAIR STYLIST",
	"FASHION STYLIST",
	"SET DESIGNER",
	"PROP MASTER",
	"LIGHTING TECHNICIAN",
	"SOUND ENGINEER",
];

interface PaginationInfo {
	currentPage: number;
	totalPages: number;
	totalCount: number;
	hasNextPage: boolean;
	hasPrevPage: boolean;
	searchApplied: boolean;
}

interface SearchCreativesClientProps {
	serverCreativesData: Creative[];
	paginationInfo: PaginationInfo;
}

export default function SearchCreativesClient({
	serverCreativesData,
	paginationInfo
}: SearchCreativesClientProps) {
	const [date, setDate] = useState<Date | undefined>(new Date());
	const [showOnlyAvailable, setShowOnlyAvailable] = useState(false);

	const router = useRouter();
	const searchParams = useSearchParams();

	// Get current search values from URL
	const currentSearchTerm = searchParams.get('q') || "";
	const currentSelectedType = searchParams.get('type') || "";
	const currentLocationSearch = searchParams.get('location') || "";

	// Local state for form inputs
	const [searchTerm, setSearchTerm] = useState(currentSearchTerm);
	const [selectedType, setSelectedType] = useState(currentSelectedType);
	const [locationSearch, setLocationSearch] = useState(currentLocationSearch);

	// Check authentication status
	const { status: userStatus, data: user } = useUser();

	// Get dynamic dashboard URL
	const { dashboardUrl } = useDashboardNavigation();

	// Check authentication before showing data
	if (userStatus === "loading") {
		return (
			<div className="min-h-screen bg-white font-light flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
					<p className="text-gray-600">Checking authentication...</p>
				</div>
			</div>
		);
	}

	if (!user) {
		return (
			<div className="min-h-screen bg-white font-light flex items-center justify-center">
				<div className="text-center max-w-md mx-auto px-6">
					<div className="mb-6">
						<Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
						<h1 className="text-2xl font-light text-gray-900 mb-2">
							Authentication Required
						</h1>
						<p className="text-gray-600 mb-6">
							You need to be signed in to search for creatives on the DUA platform.
						</p>
						<Button
							onClick={() => router.push('/')}
							className="bg-black text-white hover:bg-gray-800"
						>
							Sign In
						</Button>
					</div>
				</div>
			</div>
		);
	}

	// Use server-provided data (already filtered and paginated)
	const creatives = serverCreativesData;



	// No loading state needed since data is provided by server

	// Handle search form submission
	const handleSearch = () => {
		const params = new URLSearchParams();

		if (searchTerm.trim()) {
			params.set('q', searchTerm.trim());
		}
		if (selectedType && selectedType !== "all") {
			params.set('type', selectedType);
		}
		if (locationSearch.trim()) {
			params.set('location', locationSearch.trim());
		}

		// Reset to page 1 when searching
		params.set('page', '1');

		router.push(`/search-creatives?${params.toString()}`);
	};

	// Handle clearing search
	const handleClearSearch = () => {
		setSearchTerm("");
		setSelectedType("");
		setLocationSearch("");
		router.push('/search-creatives');
	};

	// For now, we'll show a placeholder for availability since the real data structure doesn't have the same availability format
	const isAvailableOnDate = (_creative: Creative) => {
		// This would need to be implemented based on the actual availability data structure
		// For now, return true as a placeholder
		return true;
	};

	// Pagination navigation functions
	const goToPage = (page: number) => {
		const params = new URLSearchParams(searchParams.toString());
		params.set('page', page.toString());
		router.push(`/search-creatives?${params.toString()}`);
	};

	const goToNextPage = () => {
		if (paginationInfo.hasNextPage) {
			goToPage(paginationInfo.currentPage + 1);
		}
	};

	const goToPrevPage = () => {
		if (paginationInfo.hasPrevPage) {
			goToPage(paginationInfo.currentPage - 1);
		}
	};

	return (
		<div className="min-h-screen bg-white font-light">
			{/* Header */}
			<header className="border-b border-gray-100">
				<div className="max-w-7xl mx-auto px-6 py-6">
					<div className="flex items-center justify-between">
						<Link href="/" className="text-2xl font-light tracking-wide">
							DUA
						</Link>

						<nav className="hidden md:flex items-center space-x-8 text-sm">
							<span className="text-black">Search</span>
							{dashboardUrl ? (
								<Link
									href={dashboardUrl}
									className="text-gray-600 hover:text-black transition-colors"
								>
									Dashboard
								</Link>
							) : (
								<span className="text-gray-400">Dashboard</span>
							)}
							<span className="text-gray-600">About</span>
							<span className="text-gray-600">Contact</span>
							<Search className="w-4 h-4 text-gray-600" />
						</nav>

						<Button variant="ghost" size="icon" className="md:hidden">
							<Menu className="h-5 w-5" />
						</Button>
					</div>
				</div>
			</header>

			{/* Search Filters */}
			<div className="border-b border-gray-100 bg-gray-50/30">
				<div className="max-w-7xl mx-auto px-6 py-8">
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
						<Input
							placeholder="Search by name, tags, or location"
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
							className="border-gray-200 bg-white font-light"
						/>

						<Select value={selectedType} onValueChange={setSelectedType}>
							<SelectTrigger className="border-gray-200 bg-white font-light">
								<SelectValue placeholder="All Types" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">All Types</SelectItem>
								{creativeTypes.map((type) => (
									<SelectItem key={type} value={type} className="font-light">
										{type}
									</SelectItem>
								))}
							</SelectContent>
						</Select>

						<Input
							placeholder="Location"
							value={locationSearch}
							onChange={(e) => setLocationSearch(e.target.value)}
							onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
							className="border-gray-200 bg-white font-light"
						/>

						<div className="flex gap-2">
							<Button
								onClick={handleSearch}
								className="flex-1 bg-gray-900 hover:bg-gray-800 text-white font-light"
							>
								<Search className="w-4 h-4 mr-2" />
								Search
							</Button>
							{(currentSearchTerm || currentSelectedType || currentLocationSearch) && (
								<Button
									onClick={handleClearSearch}
									variant="outline"
									className="px-3 border-gray-200 font-light"
								>
									Clear
								</Button>
							)}
						</div>
					</div>

					<div className="flex items-center">
						<input
							type="checkbox"
							id="available"
							checked={showOnlyAvailable}
							onChange={() => setShowOnlyAvailable(!showOnlyAvailable)}
							className="mr-2"
						/>
						<label
							htmlFor="available"
							className="text-sm text-gray-600 font-light"
						>
							Show only available on selected date
						</label>
					</div>
				</div>
			</div>



			{/* Results Info */}
			<div className="max-w-7xl mx-auto px-6 py-4 border-b border-gray-100">
				<div className="text-sm text-gray-600 font-light">
					{paginationInfo.searchApplied ? (
						<>Showing {creatives.length} results on page {paginationInfo.currentPage} of {paginationInfo.totalPages} ({paginationInfo.totalCount} total matches)</>
					) : (
						<>Showing {creatives.length} creatives (page {paginationInfo.currentPage} of {paginationInfo.totalPages})</>
					)}
				</div>
			</div>

			{/* Results Grid */}
			<div className="max-w-7xl mx-auto px-6 py-16">
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
					{creatives.map((creative: Creative) => (
						<Link key={creative.id} href={`/creative/${creative.id}`}>
							<div className="group cursor-pointer">
								<div className="relative aspect-[3/4] mb-4 overflow-hidden bg-gray-100">
									<Image
										src={creative.portfolioPhotos?.[0] || "/placeholder.svg"}
										alt={creative.displayName || (creative as any).name || "Creative"}
										fill
										className="object-cover transition-transform duration-500 group-hover:scale-105"
									/>
									{/* Availability indicator */}
									{isAvailableOnDate(creative) && (
										<div className="absolute top-4 right-4 w-3 h-3 bg-green-500 rounded-full"></div>
									)}
								</div>

								<div className="space-y-1">
									<div className="text-xs text-gray-400 font-light tracking-wide">
										{creative.creativeType || "CREATIVE"}
									</div>
									<h3 className="font-light text-black tracking-wide">
										{creative.displayName || (creative as any).name || "Creative"}
									</h3>
									<div className="text-xs text-gray-500 font-light">
										{creative.location || creative.address?.city || "Location not specified"}
									</div>
									{creative.tags && creative.tags.length > 0 && (
										<div className="text-xs text-gray-400 font-light">
											{creative.tags.slice(0, 3).join(", ")}
										</div>
									)}
								</div>
							</div>
						</Link>
					))}
				</div>

				{creatives.length === 0 && (
					<div className="text-center py-24">
						<h3 className="text-xl font-light text-gray-900 mb-2">
							No creatives found
						</h3>
						<p className="text-gray-500 font-light">
							Try adjusting your search criteria
						</p>
					</div>
				)}
			</div>

			{/* Pagination Controls */}
			{paginationInfo.totalPages > 1 && (
				<div className="max-w-7xl mx-auto px-6 py-8">
					<div className="flex items-center justify-between">
						{/* Results info */}
						<div className="text-sm text-gray-600 font-light">
							Showing {((paginationInfo.currentPage - 1) * 20) + 1} to{" "}
							{Math.min(paginationInfo.currentPage * 20, paginationInfo.totalCount)} of{" "}
							{paginationInfo.totalCount} creatives
						</div>

						{/* Pagination buttons */}
						<div className="flex items-center space-x-2">
							<Button
								variant="outline"
								size="sm"
								onClick={goToPrevPage}
								disabled={!paginationInfo.hasPrevPage}
								className="flex items-center space-x-1"
							>
								<ChevronLeft className="h-4 w-4" />
								<span>Previous</span>
							</Button>

							{/* Page numbers */}
							<div className="flex items-center space-x-1">
								{Array.from({ length: Math.min(5, paginationInfo.totalPages) }, (_, i) => {
									const pageNum = Math.max(1, paginationInfo.currentPage - 2) + i;
									if (pageNum > paginationInfo.totalPages) return null;

									return (
										<Button
											key={pageNum}
											variant={pageNum === paginationInfo.currentPage ? "default" : "outline"}
											size="sm"
											onClick={() => goToPage(pageNum)}
											className="w-8 h-8 p-0"
										>
											{pageNum}
										</Button>
									);
								})}
							</div>

							<Button
								variant="outline"
								size="sm"
								onClick={goToNextPage}
								disabled={!paginationInfo.hasNextPage}
								className="flex items-center space-x-1"
							>
								<span>Next</span>
								<ChevronRight className="h-4 w-4" />
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
