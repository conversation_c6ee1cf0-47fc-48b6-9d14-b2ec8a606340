"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Heart, MessageCircle, MoreHorizontal, UserPlus } from "lucide-react";
import { useState } from "react";

interface Profile {
	id: string;
	username: string;
	displayName: string;
	avatar: string;
	isFollowing: boolean;
	isVerified: boolean;
	mutualFriends: number;
}

const profiles: Profile[] = [
	{
		id: "1",
		username: "alex_photo",
		displayName: "<PERSON>",
		avatar: "/placeholder.svg?height=150&width=150&text=AJ",
		isFollowing: false,
		isVerified: true,
		mutualFriends: 12,
	},
	{
		id: "2",
		username: "sarah_travels",
		displayName: "<PERSON>",
		avatar: "/placeholder.svg?height=150&width=150&text=SW",
		isFollowing: true,
		isVerified: false,
		mutualFriends: 8,
	},
	{
		id: "3",
		username: "mike_fitness",
		displayName: "<PERSON>",
		avatar: "/placeholder.svg?height=150&width=150&text=MC",
		isFollowing: false,
		isVerified: true,
		mutualFriends: 5,
	},
	{
		id: "4",
		username: "emma_art",
		displayName: "Emma Davis",
		avatar: "/placeholder.svg?height=150&width=150&text=ED",
		isFollowing: true,
		isVerified: false,
		mutualFriends: 15,
	},
	{
		id: "5",
		username: "david_music",
		displayName: "David Rodriguez",
		avatar: "/placeholder.svg?height=150&width=150&text=DR",
		isFollowing: false,
		isVerified: true,
		mutualFriends: 3,
	},
	{
		id: "6",
		username: "lisa_food",
		displayName: "Lisa Thompson",
		avatar: "/placeholder.svg?height=150&width=150&text=LT",
		isFollowing: false,
		isVerified: false,
		mutualFriends: 7,
	},
	{
		id: "7",
		username: "james_tech",
		displayName: "James Anderson",
		avatar: "/placeholder.svg?height=150&width=150&text=JA",
		isFollowing: true,
		isVerified: true,
		mutualFriends: 20,
	},
	{
		id: "8",
		username: "maria_fashion",
		displayName: "Maria Garcia",
		avatar: "/placeholder.svg?height=150&width=150&text=MG",
		isFollowing: false,
		isVerified: false,
		mutualFriends: 9,
	},
	{
		id: "9",
		username: "ryan_sports",
		displayName: "Ryan Miller",
		avatar: "/placeholder.svg?height=150&width=150&text=RM",
		isFollowing: true,
		isVerified: true,
		mutualFriends: 11,
	},
];

export default function InstagramProfileViewer() {
	const [followingStates, setFollowingStates] = useState<
		Record<string, boolean>
	>(
		profiles.reduce(
			(acc, profile) => ({
				...acc,
				[profile.id]: profile.isFollowing,
			}),
			{},
		),
	);

	const [likedProfiles, setLikedProfiles] = useState<Record<string, boolean>>(
		{},
	);

	const toggleFollow = (profileId: string) => {
		setFollowingStates((prev) => ({
			...prev,
			[profileId]: !prev[profileId],
		}));
	};

	const toggleLike = (profileId: string) => {
		setLikedProfiles((prev) => ({
			...prev,
			[profileId]: !prev[profileId],
		}));
	};

	return (
		<div className="min-h-screen bg-gray-50 p-4">
			<div className="max-w-6xl mx-auto">
				{/* Header */}
				<div className="mb-8 text-center">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						Discover People
					</h1>
					<p className="text-gray-600">Find and connect with amazing people</p>
				</div>

				{/* Profile Grid */}
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
					{profiles.map((profile) => (
						<Card
							key={profile.id}
							className="overflow-hidden hover:shadow-lg transition-shadow duration-300"
						>
							<CardContent className="p-0">
								{/* Profile Header */}
								<div className="relative bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 h-24">
									<Button
										variant="ghost"
										size="icon"
										className="absolute top-2 right-2 text-white hover:bg-white/20"
									>
										<MoreHorizontal className="w-4 h-4" />
									</Button>
								</div>

								{/* Avatar */}
								<div className="relative -mt-12 flex justify-center">
									<div className="relative">
										<Avatar className="w-24 h-24 border-4 border-white shadow-lg">
											<AvatarImage
												src={profile.avatar || "/placeholder.svg"}
												alt={profile.displayName}
											/>
											<AvatarFallback className="text-lg font-semibold">
												{profile.displayName
													.split(" ")
													.map((n) => n[0])
													.join("")}
											</AvatarFallback>
										</Avatar>
										{profile.isVerified && (
											<div className="absolute -bottom-1 -right-1 bg-blue-500 rounded-full p-1">
												<svg
													className="w-3 h-3 text-white"
													fill="currentColor"
													viewBox="0 0 20 20"
												>
													<path
														fillRule="evenodd"
														d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
														clipRule="evenodd"
													/>
												</svg>
											</div>
										)}
									</div>
								</div>

								{/* Profile Info */}
								<div className="p-4 pt-2 text-center">
									<h3 className="font-semibold text-lg text-gray-900 mb-1">
										{profile.displayName}
									</h3>
									<p className="text-gray-500 text-sm mb-2">
										@{profile.username}
									</p>

									{profile.mutualFriends > 0 && (
										<Badge variant="secondary" className="mb-3 text-xs">
											{profile.mutualFriends} mutual friends
										</Badge>
									)}

									{/* Action Buttons */}
									<div className="flex gap-2 justify-center mb-3">
										<Button
											variant={
												followingStates[profile.id] ? "outline" : "default"
											}
											size="sm"
											onClick={() => toggleFollow(profile.id)}
											className="flex-1 max-w-24"
										>
											<UserPlus className="w-3 h-3 mr-1" />
											{followingStates[profile.id] ? "Following" : "Follow"}
										</Button>
										<Button
											variant="outline"
											size="sm"
											onClick={() => toggleLike(profile.id)}
											className={`${likedProfiles[profile.id] ? "text-red-500 border-red-200" : ""}`}
										>
											<Heart
												className={`w-3 h-3 ${likedProfiles[profile.id] ? "fill-current" : ""}`}
											/>
										</Button>
										<Button variant="outline" size="sm">
											<MessageCircle className="w-3 h-3" />
										</Button>
									</div>
								</div>
							</CardContent>
						</Card>
					))}
				</div>

				{/* Load More Button */}
				<div className="text-center mt-8">
					<Button variant="outline" size="lg">
						Load More Profiles
					</Button>
				</div>
			</div>
		</div>
	);
}
