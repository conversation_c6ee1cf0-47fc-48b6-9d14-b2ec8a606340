"use client";

import type React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { getGridClasses } from "@/components/utils/grid-utils";
import type { <PERSON> } from "@/src/types/collections/booker";
import {
	CreditCard,
	Edit3,
	<PERSON><PERSON>,
	More<PERSON><PERSON><PERSON>,
	Scissors,
	Search,
	Star,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import BookerProfileHeader from "./components/booker-profile/booker-profile-header";
import BudgetOverviewDisplay from "./components/booker-profile/budget-overview-display";
import BudgetTabContent from "./components/booker-profile/budget-tab-content";
import CollaborationsTabContent from "./components/booker-profile/collaborations-tab-content";
import MembersTabContent from "./components/booker-profile/members-tab-content";
import AddProjectModal from "./components/booker-profile/modals/add-project-modal";
import PaymentModal from "./components/booker-profile/modals/payment-modal";
import ReviewModal from "./components/booker-profile/modals/review-modal";
import OffersSentList from "./components/booker-profile/offers-sent-list";
import ProfileTabs, {
	type Tab,
} from "./components/booker-profile/profile-tabs";
import InvoiceList from "./components/invoice/invoice-list";
import ProjectGrid from "./components/booker-profile/project-grid";
import { useAddProjectForm } from "./components/hooks/use-add-project-form";
import { useReviewModal } from "./components/hooks/use-review-modal";
import { useBookerProjects } from "./components/hooks/useBookerProjects";
import PageSectionContainer from "./components/layout/page-section-container";
import SiteHeader from "./components/layout/site-header";
import type {
	BudgetItem,
	Offer,
	PastCollaboration,
	Payment,
	Project,
	Invoice,
	EscrowTransaction,
} from "./components/types";

// This type is now imported from profile-tabs.tsx to ensure consistency.
// type BookerTab = "collaborations" | "projects" | "offers" | "budget" | "archive" | "members";

interface BookerProfilePageProps {
	serverBookerData: Booker;
	serverProjects: Project[];
	serverCollaborations: PastCollaboration[];
	serverOffers: Offer[];
	serverPayments: Payment[];
	serverInvoices: Invoice[];
	serverEscrowTransactions: EscrowTransaction[];
}

// Define Member interface
interface Member {
	id: string;
	name: string;
	avatarUrl?: string;
	permission: "admin" | "editor" | "viewer";
}

// Mock Member Data
const mockMembers: Member[] = [
	{
		id: "m1",
		name: "Alice Wonderland",
		avatarUrl: "/avatars/alice.png",
		permission: "admin",
	},
	{
		id: "m2",
		name: "Bob The Builder",
		avatarUrl: "/avatars/bob.png",
		permission: "editor",
	},
	{
		id: "m3",
		name: "Charlie Chaplin",
		avatarUrl: "/avatars/charlie.png",
		permission: "viewer",
	},
	{ id: "m4", name: "Diana Prince", permission: "viewer" },
];

// Define interfaces expected by BudgetTabContent
interface ProjectBudget {
	id: string;
	name: string;
	allocatedBudget: number;
	status: string;
}

interface Transaction {
	id: string;
	date: string;
	description: string;
	amount: number;
	type: Payment["status"] | string;
	creativeName?: string;
	projectName?: string;
}

export default function BookerProfilePage({
	serverBookerData,
	serverProjects,
	serverCollaborations,
	serverOffers,
	serverPayments,
	serverInvoices,
	serverEscrowTransactions,
}: BookerProfilePageProps) {
	const router = useRouter();

	// Data is now passed directly as props.
	const bookerData = serverBookerData;
	const projects = serverProjects;
	const collaborations = serverCollaborations;
	const offers = serverOffers;
	const payments = serverPayments;
	const invoices = serverInvoices;
	const escrowTransactions = serverEscrowTransactions;

	// The useBookerProjects hook is for mutations (create, delete), so it still needs the ID.
	const { createProject, deleteProject, sendOffer } = useBookerProjects(
		bookerData.id,
		bookerData?.displayName || "Booker",
	);

	// Keep other local state for now
	const [activeTab, setActiveTab] = useState<Tab>("projects");
	const [members, setMembers] = useState<Member[]>(mockMembers);

	// Modal-related state and hooks for project details are removed.
	// We will navigate to a separate page for project details.
	const [showPaymentModal, setShowPaymentModal] = useState(false);
	const openPaymentModal = () => setShowPaymentModal(true);
	const closePaymentModal = () => setShowPaymentModal(false);

	const {
		showReviewModal,
		selectedCollaboration,
		reviewForm,
		openReviewModal,
		closeReviewModal,
		updateReviewFormField,
		handleReviewSubmit,
	} = useReviewModal();

	const {
		showAddProjectModal,
		newProjectForm,
		openAddProjectModal,
		closeAddProjectModal,
		updateNewProjectFormField,
		handleAddProjectSubmit,
		addArtistRoleToForm,
		removeArtistRoleFromForm,
		isSubmitting,
	} = useAddProjectForm({ createProject });

	// Data transformation for BudgetTabContent
	const formattedProjectsForBudgetTab: ProjectBudget[] = (projects || []).map(
		(p) => ({
			id: p.id,
			name: p.title,
			allocatedBudget: p.budget,
			status: p.status,
		}),
	);

	const formattedTransactionsForBudgetTab: Transaction[] = (payments || []).map(
		(t) => ({
			id: t.id,
			date: new Date(t.date).toLocaleDateString(),
			description: `Payment for project ${t.projectId}`, // Placeholder description
			amount: t.amount,
			type: t.status,
			creativeName: undefined, // This would require fetching creative data
			projectName: projects?.find((p) => p.id === t.projectId)?.title,
		}),
	);

	// Budget calculations
	const totalAllocatedBudget = formattedProjectsForBudgetTab.reduce(
		(sum, p) => sum + p.allocatedBudget,
		0,
	);
	const depositedBudget = 150000; // Mock value for deposited budget
	const budgetLeft = depositedBudget - totalAllocatedBudget;
	const totalBudgetFormatted = `$${(totalAllocatedBudget / 1000).toFixed(0)}k`;

	const handleInviteMember = () => {
		// Placeholder: Implement invitation modal logic here
		console.log(
			"Invite new member button clicked. TODO: Implement invitation modal.",
		);
		// For now, let's simulate adding a new member for UI testing
		// const newMember: Member = {
		//   id: `m${members.length + 1}`,
		//   name: "New Member",
		//   permission: "viewer",
		//   avatarUrl: undefined // Or a placeholder avatar
		// };
		// setMembers(prevMembers => [...prevMembers, newMember]);
		alert("Placeholder: Invite new member functionality to be implemented.");
	};

	const activeCollaborations =
		collaborations?.filter((c) => c.status !== "completed") || [];
	const pastCollaborations =
		collaborations?.filter((c) => c.status === "completed") || [];

	const renderContent = () => {
		if (projects.length === 0) {
			return <div className="text-center p-8">Loading projects...</div>;
		}

		switch (activeTab) {
			case "projects":
				return (
					<ProjectGrid
						projects={projects}
						// onProjectClick is removed, linking will be handled inside ProjectGrid
					/>
				);
			case "offers":
				return <OffersSentList offers={offers} onOfferUpdate={() => window.location.reload()} />;
			case "budget":
				return (
					<BudgetTabContent
						totalBudget={totalAllocatedBudget}
						depositedBudget={depositedBudget}
						budgetLeft={budgetLeft}
						projects={formattedProjectsForBudgetTab}
						transactions={formattedTransactionsForBudgetTab}
					/>
				);
			case "invoices":
				return (
					<InvoiceList
						invoices={invoices}
						escrowTransactions={escrowTransactions}
						userRole="booker"
						onInvoiceUpdate={() => window.location.reload()}
					/>
				);
			case "collaborations":
				return (
					<CollaborationsTabContent
						title="Active Collaborations"
						collaborations={activeCollaborations}
						onOpenReviewModal={openReviewModal}
					/>
				);
			case "members":
				return (
					<MembersTabContent
						members={members}
						onInviteMember={handleInviteMember}
					/>
				);
			case "archive":
				return (
					<CollaborationsTabContent
						title="Past Collaborations"
						collaborations={pastCollaborations}
						onOpenReviewModal={openReviewModal}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="min-h-screen bg-white font-light">
			<SiteHeader />

			{/* Profile Section */}
			<div className="border-b border-gray-100">
				<PageSectionContainer>
					<BookerProfileHeader
						name={bookerData?.displayName || "Booker"}
						type={bookerData?.company || "Company"}
						projectCount={projects.length}
						offersSentCount={offers.length}
						collaborationsCount={pastCollaborations.length}
						membersCount={members.length}
					/>
					<BudgetOverviewDisplay activeTab={activeTab} projects={projects} />
					{activeTab === "budget" && (
						<div className="text-center">
							<Button
								onClick={openPaymentModal}
								className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide"
							>
								<CreditCard className="w-4 h-4 mr-2" />
								Manage Payment Methods
							</Button>
						</div>
					)}
				</PageSectionContainer>
			</div>

			{/* Navigation Tabs and Project Grid Controls */}
			<PageSectionContainer>
				<div className="flex justify-between items-center mb-6">
					<ProfileTabs
						activeTab={activeTab}
						onTabChange={setActiveTab}
						projectCount={projects.length}
						offersSentCount={offers.length}
						totalBudgetFormatted={totalBudgetFormatted}
						invoicesCount={invoices.length}
						collaborationsCount={activeCollaborations.length}
						archiveCount={pastCollaborations.length}
						membersCount={members.length}
						onOpenAddProjectModal={openAddProjectModal}
					/>
				</div>
				{renderContent()}
			</PageSectionContainer>

			{/* Modals */}
			<AddProjectModal
				showModal={showAddProjectModal}
				onClose={closeAddProjectModal}
				formData={newProjectForm}
				onFormChange={updateNewProjectFormField}
				onSubmit={handleAddProjectSubmit}
				onAddArtistRole={addArtistRoleToForm}
				onRemoveArtistRole={removeArtistRoleFromForm}
				isSubmitting={isSubmitting}
			/>

			{showReviewModal && selectedCollaboration && (
				<ReviewModal
					showModal={showReviewModal}
					selectedCollaboration={selectedCollaboration}
					reviewForm={reviewForm}
					onClose={closeReviewModal}
					onFormChange={updateReviewFormField}
					onSubmit={handleReviewSubmit}
				/>
			)}

			<PaymentModal showModal={showPaymentModal} onClose={closePaymentModal} />
		</div>
	);
}
