# DUA Platform Signup Flow

## Overview

The DUA platform now features a comprehensive multi-step signup process that follows the Firebase endpoints documentation pattern. The flow separates authentication (email/password) from profile creation, providing a better user experience and following best practices.

## Signup Flow Architecture

### 1. User Type Selection (`/signup`)
- Landing page where users choose between Creative or Booker
- Clean, visual interface with clear descriptions of each user type
- Links to dedicated signup pages for each type

### 2. Authentication Creation
- **Creative Signup** (`/signup/creative`)
- **Booker Signup** (`/signup/booker`)
- Collects basic information: first name, last name, email, password
- Creates Firebase Auth user account
- Stores pending profile data in localStorage
- Redirects to profile creation page

### 3. Profile Creation
- **Creative Profile** (`/creative/create-profile`)
- **Booker Profile** (`/booker/create-profile`)
- Collects detailed professional information
- Calls appropriate Firebase functions (`create_creative` or `create_booker`)
- Clears localStorage data upon successful creation

## User Journey

### Creative Signup Journey
1. User visits `/signup` and selects "I'm a Creative"
2. Redirected to `/signup/creative`
3. Fills out basic info (name, email, password)
4. Firebase Auth account created
5. Redirected to `/creative/create-profile`
6. Completes detailed profile (display name, story, creative types, etc.)
7. `create_creative` Firebase function called
8. Profile submitted for admin approval
9. Redirected to creative profile page

### Booker Signup Journey
1. User visits `/signup` and selects "I'm a Booker"
2. Redirected to `/signup/booker`
3. Fills out basic info (name, email, password, optional company)
4. Firebase Auth account created
5. Redirected to `/booker/create-profile`
6. Completes detailed profile (display name, company, bio, etc.)
7. `create_booker` Firebase function called
8. Profile created and ready to use
9. Redirected to booker profile page

## Key Features

### Security & Validation
- Email/password validation with Firebase Auth
- Form validation on both client and server side
- Error handling for common auth issues (email in use, weak password, etc.)
- Secure data transfer between signup steps using localStorage

### User Experience
- Progressive disclosure - collect basic info first, then detailed profile
- Clear visual feedback and loading states
- Responsive design for all screen sizes
- Consistent branding and styling
- Helpful next steps information

### Technical Implementation
- Follows Firebase endpoints documentation patterns
- Separation of concerns between auth and profile creation
- Clean state management with localStorage for temporary data
- TypeScript for type safety
- Proper error handling and user feedback

## Firebase Integration

### Authentication
- Uses `createUserWithEmailAndPassword` for account creation
- Handles Firebase Auth errors gracefully
- Maintains user session across profile creation

### Profile Creation
- **Creatives**: Calls `create_creative` endpoint with required fields:
  - `display_name`
  - `story`
  - `creative_type` (array)
  - `instagram`
  - `phone_number`

- **Bookers**: Calls `create_booker` endpoint with required fields:
  - `display_name`
  - `phone_number`
  - `company`
  - `bio`

### Admin Approval Process
- Creative profiles require admin approval after creation
- Booker profiles are immediately active
- Users receive email notifications about approval status

## Navigation Updates

### Landing Page
- "Join" button now links to `/signup` instead of opening modal
- Simplified authentication modal (login only)
- Removed complex signup modal in favor of dedicated pages

### Legal Compliance
- Terms & Conditions and Privacy Policy links included
- Clear consent messaging during signup process

## File Structure

```
app/
├── signup/
│   ├── page.tsx                 # User type selection
│   ├── creative/
│   │   └── page.tsx            # Creative auth signup
│   └── booker/
│       └── page.tsx            # Booker auth signup
├── creative/
│   └── create-profile/
│       └── page.tsx            # Creative profile creation
└── booker/
    └── create-profile/
        └── page.tsx            # Booker profile creation
```

## Future Enhancements

- Email verification step
- Social login options (Google, LinkedIn)
- Progressive profile completion
- Onboarding tutorials
- Profile completion tracking
- Admin dashboard for managing approvals

## Testing

The signup flow has been tested with:
- Form validation
- Firebase Auth integration
- Error handling
- Responsive design
- Cross-browser compatibility
- Navigation flow

All pages are accessible and functional at:
- `/signup` - User type selection
- `/signup/creative` - Creative signup
- `/signup/booker` - Booker signup
- Landing page "Join" button integration
