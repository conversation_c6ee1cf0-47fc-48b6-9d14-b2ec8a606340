# Admin Functionality - DUA Platform

## Overview

The DUA platform now includes admin functionality that allows designated administrators to view and manage all users who have created accounts on the platform. This includes both creatives and bookers who have qualified or been invited to be part of the platform.

## Admin Access

### Admin Users

Admin access is currently controlled by a hardcoded list of email addresses in the following files:
- `components/hooks/use-admin-check.ts`
- `src/lib/admin/admin-auth.ts`

Current admin emails:
- `<EMAIL>`
- `<EMAIL>`

### Security Note

⚠️ **Important**: In production, admin email lists should be moved to environment variables or a secure database rather than being hardcoded in the source code.

## Admin Features

### 1. Admin Dashboard (`/admin`)

The admin dashboard provides:
- **User Statistics**: Overview of total users, active users, creatives, and bookers
- **User List**: Comprehensive table showing all platform users
- **Search Functionality**: Search users by email or display name
- **User Details**: View user type, verification status, creation date, and last sign-in

### 2. User Information Display

For each user, the admin can see:
- **Basic Info**: Name, email, user ID
- **User Type**: Creative, Booker, Both, or No Profile
- **Status**: Email verification, account status, active/inactive
- **Timestamps**: Account creation date and last sign-in time
- **Profile Data**: Associated creative and booker profiles

### 3. User Filtering

The admin dashboard now includes filtering functionality:
- **Filter by User Type**: All Users, Creatives, Bookers, Both, No Profile
- **Real-time Counts**: Each filter shows the number of users in that category
- **Combined Search & Filter**: Search results are filtered based on the selected user type
- **Visual Indicators**: Active filter is highlighted, showing current selection

### 4. Detailed Profile Views

For users with creative and/or booker profiles, admins can expand rows to see detailed information:

#### Creative Profile Details:
- **Basic Info**: Name, type, location, specialties
- **Rates**: Hourly and day rates (if set)
- **Bio**: Creative's biography and description
- **Portfolio Data**: Associated portfolio information

#### Booker Profile Details:
- **Basic Info**: Name, type, location, company
- **Bio**: Booker's biography and description
- **Project Data**: Associated project information

#### Offers Information:
- **Sent Offers**: Offers sent by the user (if they're a booker)
- **Received Offers**: Offers received by the user (if they're a creative)
- **Offer Details**: Project title, status, budget, creation date
- **Offer Type**: Clearly marked as "Sent" or "Received"

## Technical Implementation

### Components

1. **AdminAuthGuard** (`components/admin/admin-auth-guard.tsx`)
   - Protects admin routes from unauthorized access
   - Redirects non-admin users to home page

2. **AdminUserList** (`components/admin/admin-user-list.tsx`)
   - Main component displaying user data and statistics
   - Handles search and pagination

3. **Admin Layout** (`app/admin/layout.tsx`)
   - Provides consistent layout for admin pages
   - Includes admin navigation and header

### Server Actions

1. **getAllUsers** (`src/actions/adminActions.ts`)
   - Fetches all users from Firebase Auth
   - Combines with Firestore user data
   - Supports pagination

2. **getUserStats** (`src/actions/adminActions.ts`)
   - Provides platform statistics
   - Counts users, creatives, bookers, and active users

3. **searchUsers** (`src/actions/adminActions.ts`)
   - Searches users by email or display name

4. **getUserProfileDetails** (`src/actions/adminActions.ts`)
   - Fetches detailed creative and booker profile information
   - Retrieves offers sent and received by the user
   - Combines data from multiple Firestore collections

### Hooks

1. **useAdminCheck** (`components/hooks/use-admin-check.ts`)
   - Reusable hook to check if current user is admin
   - Used across components for consistent admin checking

### Authentication

1. **Admin Auth Utils** (`src/lib/admin/admin-auth.ts`)
   - Server-side admin authentication utilities
   - Verifies admin access for server actions

## Navigation

Admin users will see an "Admin" link in the main navigation bar (SiteHeader component) that takes them to the admin dashboard. The link is only visible to users with admin privileges.

## Data Sources

The admin functionality pulls data from:
- **Firebase Auth**: User authentication data (email, creation date, last sign-in)
- **Firestore Collections**:
  - `user-root-accounts`: User profile associations and activity status
  - `creatives`: Creative profile data
  - `bookers`: Booker profile data

## Future Enhancements

Potential improvements for the admin functionality:

1. **Enhanced Security**:
   - Move admin emails to environment variables
   - Implement role-based permissions in database
   - Add audit logging for admin actions

2. **Additional Features**:
   - User management actions (enable/disable accounts)
   - Detailed user activity logs
   - Export functionality for user data
   - Advanced filtering and sorting options

3. **Performance**:
   - Implement proper pagination for large user lists
   - Add caching for frequently accessed data
   - Optimize search functionality

## Usage

1. **Access Admin Dashboard**:
   - Log in with an admin email address
   - Click the "Admin" link in the navigation bar
   - Or navigate directly to `/admin`

2. **View User Statistics**:
   - Statistics cards show at the top of the dashboard
   - Real-time counts of users, creatives, bookers, and active users

3. **Search Users**:
   - Use the search bar to find users by email or name
   - Results update in real-time

4. **Filter Users by Type**:
   - Use filter buttons to show specific user types
   - Filters include: All Users, Creatives, Bookers, Both, No Profile
   - Each filter shows the count of users in that category

5. **View Detailed Profiles**:
   - Click "Details" button for users with profiles
   - Expand rows to see creative/booker profile information
   - View offers sent and received by the user

6. **View User Details**:
   - User table shows comprehensive information
   - Color-coded badges indicate user types and status

## Security Considerations

- Admin routes are protected by authentication guards
- Server actions include admin verification (currently disabled for demo)
- Client-side admin checks prevent UI exposure to non-admins
- All admin functionality requires user authentication

## Development Notes

- The admin functionality is built using Next.js App Router
- Server Components are used for data fetching
- Client Components handle interactivity and real-time updates
- Firebase Admin SDK is used for server-side operations
