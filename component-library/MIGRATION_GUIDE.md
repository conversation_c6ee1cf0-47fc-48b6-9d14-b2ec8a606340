# Migration Guide: From Tailwind to DUA Component Library

This guide helps you migrate from the existing Tailwind CSS components to the new DUA Component Library.

## Overview

The DUA Component Library provides the same functionality as your existing shadcn/ui components but with:
- Pure CSS styling using styled-components
- No Tailwind CSS dependency
- Built-in theming system
- Better TypeScript integration
- Consistent design tokens

## Component Migration

### Button Component

**Before (Tailwind/shadcn):**
```tsx
import { Button } from "@/components/ui/button"

<Button variant="outline" size="sm">
  Click me
</Button>
```

**After (DUA Library):**
```tsx
import { Button } from "dua-component-library"

<Button variant="outline" size="sm">
  Click me
</Button>
```

The API remains exactly the same! Only the import changes.

### Card Component

**Before:**
```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card"

<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
</Card>
```

**After:**
```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "dua-component-library"

<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
</Card>
```

### Input Component

**Before:**
```tsx
import { Input } from "@/components/ui/input"

<Input type="email" placeholder="Email" />
```

**After:**
```tsx
import { Input } from "dua-component-library"

<Input type="email" placeholder="Email" />
```

### Badge Component

**Before:**
```tsx
import { Badge } from "@/components/ui/badge"

<Badge variant="destructive">Error</Badge>
```

**After:**
```tsx
import { Badge } from "dua-component-library"

<Badge variant="destructive">Error</Badge>
```

## Theme Setup

### Replace Theme Provider

**Before:**
```tsx
import { ThemeProvider } from "@/components/theme-provider"

<ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
  <App />
</ThemeProvider>
```

**After:**
```tsx
import { ThemeProvider } from "dua-component-library"

<ThemeProvider defaultTheme="dark" storageKey="dua-ui-theme">
  <App />
</ThemeProvider>
```

### Theme Switching

**Before:**
```tsx
import { useTheme } from "@/components/theme-provider"

function ModeToggle() {
  const { setTheme } = useTheme()
  
  return (
    <Button onClick={() => setTheme("dark")}>
      Dark Mode
    </Button>
  )
}
```

**After:**
```tsx
import { useTheme } from "dua-component-library"

function ModeToggle() {
  const { setTheme, toggleTheme } = useTheme()
  
  return (
    <Button onClick={toggleTheme}>
      Toggle Theme
    </Button>
  )
}
```

## Removing Tailwind Dependencies

### 1. Update package.json

Remove these dependencies:
```json
{
  "devDependencies": {
    "tailwindcss": "^3.4.17",
    "tailwindcss-animate": "^1.0.7",
    "autoprefixer": "^10.4.20",
    "postcss": "^8"
  },
  "dependencies": {
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "tailwind-merge": "^2.5.5"
  }
}
```

Add the component library:
```json
{
  "dependencies": {
    "dua-component-library": "^0.1.0",
    "styled-components": "^6.1.13"
  },
  "devDependencies": {
    "@types/styled-components": "^5.1.34"
  }
}
```

### 2. Remove Configuration Files

Delete these files:
- `tailwind.config.ts`
- `postcss.config.mjs`
- `components.json`

### 3. Update Global Styles

**Before (styles/globals.css):**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ... rest of CSS variables ... */
```

**After:**
```css
/* Remove all Tailwind imports and CSS variables */
/* The component library handles all styling */

body {
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}
```

### 4. Update Utility Functions

**Before (lib/utils.ts):**
```ts
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

**After:**
The `cn` utility is no longer needed since we're not using class-based styling.

## Step-by-Step Migration Process

### 1. Install the Component Library
```bash
pnpm add dua-component-library styled-components
pnpm add -D @types/styled-components
```

### 2. Update Root Layout/App
```tsx
// app/layout.tsx or pages/_app.tsx
import { ThemeProvider } from 'dua-component-library'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider defaultTheme="light" enableSystem>
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
```

### 3. Replace Component Imports
Use find and replace to update imports:

Find: `from "@/components/ui/button"`
Replace: `from "dua-component-library"`

Find: `from "@/components/ui/card"`
Replace: `from "dua-component-library"`

Continue for all components...

### 4. Remove Old Component Files
Delete the entire `components/ui` directory since these are now provided by the library.

### 5. Clean Up Dependencies
```bash
pnpm remove tailwindcss tailwindcss-animate autoprefixer postcss class-variance-authority clsx tailwind-merge
```

### 6. Test Your Application
Run your application and verify all components render correctly with the new library.

## Benefits After Migration

- ✅ **Smaller Bundle**: No Tailwind CSS runtime
- ✅ **Better Performance**: Styled-components with optimized CSS-in-JS
- ✅ **Consistent Theming**: Centralized design tokens
- ✅ **Better DX**: Improved TypeScript support
- ✅ **No Build Config**: No Tailwind configuration needed
- ✅ **Theme Switching**: Built-in light/dark mode support

## Troubleshooting

### Missing Components
If you're using components not yet available in the library, you can:
1. Request the component be added
2. Create it following the library patterns
3. Temporarily keep the old Tailwind version

### Styling Differences
The component library maintains visual consistency but may have slight differences. Check the Storybook documentation for exact appearances.

### Performance
The new library should perform better due to:
- No Tailwind CSS runtime
- Optimized styled-components
- Better tree shaking
