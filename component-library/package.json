{"name": "dua-component-library", "version": "0.1.0", "description": "DUA Component Library - A modern React component library with styled-components", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc && vite build", "dev": "vite build --watch", "storybook": "storybook dev -p 6007 --ci", "build:storybook": "storybook build", "test": "vitest", "coverage": "vitest --coverage", "check:format": "eslint . --max-warnings 0", "check:types": "tsc --noEmit", "prepare-publish": "pnpm run build && cp package.json dist/ && cp README.md dist/", "publish:pkg": "pnpm run prepare-publish && pnpm publish dist/ --access public"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "dependencies": {"@emotion/babel-plugin": "^11.13.5", "@emotion/react": "^11.14.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "date-fns": "^3.6.0", "lucide-react": "^0.454.0", "react-hook-form": "^7.54.1", "recharts": "^2.15.0", "styled-components": "^6.1.13", "zod": "^3.24.1"}, "devDependencies": {"@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/test": "^8.6.14", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "@vitejs/plugin-react": "^4.3.3", "eslint": "latest", "eslint-config-airbnb": "latest", "eslint-config-airbnb-typescript": "latest", "eslint-plugin-import": "latest", "eslint-plugin-jsx-a11y": "latest", "eslint-plugin-react": "latest", "eslint-plugin-react-hooks": "latest", "eslint-plugin-storybook": "^0.10.1", "rollup-plugin-external-deps": "^0.0.2", "storybook": "^8.6.14", "typescript": "^5.6.3", "vite": "^5.4.10", "vite-plugin-dts": "^4.3.0", "vitest": "^2.1.4"}, "repository": {"type": "git", "url": "https://github.com/your-username/dua-component-library.git"}, "keywords": ["react", "component-library", "styled-components", "typescript", "design-system"], "author": "Your Name", "license": "MIT"}