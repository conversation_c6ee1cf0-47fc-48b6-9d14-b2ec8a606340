{
	"compilerOptions": {
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2020", "DOM", "DOM.Iterable"],
		"module": "ESNext",
		"skipLibCheck": true,

		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true,

		/* Original Options Below */
		"outDir": "./dist",

		/* Path mapping */
		"baseUrl": ".",
		"paths": {
			"@/*": ["./*"],
			"dua-component-library": ["./component-library/src/index.ts"]
		}
	},
	"include": [
		"src/**/*.ts",
		"src/**/*.tsx",
		".storybook/**/*.ts",
		".storybook/**/*.tsx"
	],
	"exclude": ["node_modules", "dist", "**/*.test.tsx"]
}
