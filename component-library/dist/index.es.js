import * as w from "react";
import {
	useContext as Fe,
	forwardRef as V,
	useEffect as vr,
	useState as xr,
	createContext as yr,
} from "react";
import * as Ce from "react/jsx-runtime";
import { Fragment as br, jsx as le } from "react/jsx-runtime";
import H, { css as S, ThemeProvider as $r } from "styled-components";
function Cr(e) {
	if (e.sheet) return e.sheet;
	for (var r = 0; r < document.styleSheets.length; r++)
		if (document.styleSheets[r].ownerNode === e) return document.styleSheets[r];
}
function Sr(e) {
	var r = document.createElement("style");
	return (
		r.setAttribute("data-emotion", e.key),
		e.nonce !== void 0 && r.setAttribute("nonce", e.nonce),
		r.appendChild(document.createTextNode("")),
		r.setAttribute("data-s", ""),
		r
	);
}
var wr = /* @__PURE__ */ (() => {
		function e(t) {
			(this._insertTag = (o) => {
				var s;
				this.tags.length === 0
					? this.insertionPoint
						? (s = this.insertionPoint.nextSibling)
						: this.prepend
							? (s = this.container.firstChild)
							: (s = this.before)
					: (s = this.tags[this.tags.length - 1].nextSibling),
					this.container.insertBefore(o, s),
					this.tags.push(o);
			}),
				(this.isSpeedy = t.speedy === void 0 ? !0 : t.speedy),
				(this.tags = []),
				(this.ctr = 0),
				(this.nonce = t.nonce),
				(this.key = t.key),
				(this.container = t.container),
				(this.prepend = t.prepend),
				(this.insertionPoint = t.insertionPoint),
				(this.before = null);
		}
		var r = e.prototype;
		return (
			(r.hydrate = function (n) {
				n.forEach(this._insertTag);
			}),
			(r.insert = function (n) {
				this.ctr % (this.isSpeedy ? 65e3 : 1) === 0 &&
					this._insertTag(Sr(this));
				var o = this.tags[this.tags.length - 1];
				if (this.isSpeedy) {
					var s = Cr(o);
					try {
						s.insertRule(n, s.cssRules.length);
					} catch {}
				} else o.appendChild(document.createTextNode(n));
				this.ctr++;
			}),
			(r.flush = function () {
				this.tags.forEach((n) => {
					var o;
					return (o = n.parentNode) == null ? void 0 : o.removeChild(n);
				}),
					(this.tags = []),
					(this.ctr = 0);
			}),
			e
		);
	})(),
	F = "-ms-",
	ne = "-moz-",
	m = "-webkit-",
	Me = "comm",
	he = "rule",
	ge = "decl",
	Er = "@import",
	ze = "@keyframes",
	Tr = "@layer",
	Rr = Math.abs,
	oe = String.fromCharCode,
	kr = Object.assign;
function Ar(e, r) {
	return P(e, 0) ^ 45
		? (((((((r << 2) ^ P(e, 0)) << 2) ^ P(e, 1)) << 2) ^ P(e, 2)) << 2) ^
				P(e, 3)
		: 0;
}
function Ie(e) {
	return e.trim();
}
function Pr(e, r) {
	return (e = r.exec(e)) ? e[0] : e;
}
function p(e, r, t) {
	return e.replace(r, t);
}
function de(e, r) {
	return e.indexOf(r);
}
function P(e, r) {
	return e.charCodeAt(r) | 0;
}
function q(e, r, t) {
	return e.slice(r, t);
}
function L(e) {
	return e.length;
}
function be(e) {
	return e.length;
}
function Q(e, r) {
	return r.push(e), e;
}
function _r(e, r) {
	return e.map(r).join("");
}
var se = 1,
	G = 1,
	Oe = 0,
	M = 0,
	R = 0,
	j = "";
function ae(e, r, t, n, o, s, i) {
	return {
		value: e,
		root: r,
		parent: t,
		type: n,
		props: o,
		children: s,
		line: se,
		column: G,
		length: i,
		return: "",
	};
}
function U(e, r) {
	return kr(ae("", null, null, "", null, null, 0), e, { length: -e.length }, r);
}
function Fr() {
	return R;
}
function Mr() {
	return (R = M > 0 ? P(j, --M) : 0), G--, R === 10 && ((G = 1), se--), R;
}
function I() {
	return (R = M < Oe ? P(j, M++) : 0), G++, R === 10 && ((G = 1), se++), R;
}
function Y() {
	return P(j, M);
}
function ee() {
	return M;
}
function X(e, r) {
	return q(j, e, r);
}
function K(e) {
	switch (e) {
		case 0:
		case 9:
		case 10:
		case 13:
		case 32:
			return 5;
		case 33:
		case 43:
		case 44:
		case 47:
		case 62:
		case 64:
		case 126:
		case 59:
		case 123:
		case 125:
			return 4;
		case 58:
			return 3;
		case 34:
		case 39:
		case 40:
		case 91:
			return 2;
		case 41:
		case 93:
			return 1;
	}
	return 0;
}
function Ne(e) {
	return (se = G = 1), (Oe = L((j = e))), (M = 0), [];
}
function De(e) {
	return (j = ""), e;
}
function re(e) {
	return Ie(X(M - 1, fe(e === 91 ? e + 2 : e === 40 ? e + 1 : e)));
}
function zr(e) {
	while ((R = Y()) && R < 33) I();
	return K(e) > 2 || K(R) > 3 ? "" : " ";
}
function Ir(e, r) {
	while (
		--r &&
		I() &&
		!(R < 48 || R > 102 || (R > 57 && R < 65) || (R > 70 && R < 97))
	);
	return X(e, ee() + (r < 6 && Y() == 32 && I() == 32));
}
function fe(e) {
	while (I())
		switch (R) {
			case e:
				return M;
			case 34:
			case 39:
				e !== 34 && e !== 39 && fe(R);
				break;
			case 40:
				e === 41 && fe(e);
				break;
			case 92:
				I();
				break;
		}
	return M;
}
function Or(e, r) {
	while (I() && e + R !== 57) if (e + R === 84 && Y() === 47) break;
	return "/*" + X(r, M - 1) + "*" + oe(e === 47 ? e : I());
}
function Nr(e) {
	while (!K(Y())) I();
	return X(e, M);
}
function Dr(e) {
	return De(te("", null, null, null, [""], (e = Ne(e)), 0, [0], e));
}
function te(e, r, t, n, o, s, i, l, d) {
	for (
		var u = 0,
			f = 0,
			h = i,
			z = 0,
			k = 0,
			C = 0,
			x = 1,
			A = 1,
			$ = 1,
			T = 0,
			v = "",
			E = o,
			a = s,
			_ = n,
			y = v;
		A;
	)
		switch (((C = T), (T = I()))) {
			case 40:
				if (C != 108 && P(y, h - 1) == 58) {
					de((y += p(re(T), "&", "&\f")), "&\f") != -1 && ($ = -1);
					break;
				}
			case 34:
			case 39:
			case 91:
				y += re(T);
				break;
			case 9:
			case 10:
			case 13:
			case 32:
				y += zr(C);
				break;
			case 92:
				y += Ir(ee() - 1, 7);
				continue;
			case 47:
				switch (Y()) {
					case 42:
					case 47:
						Q(Lr(Or(I(), ee()), r, t), d);
						break;
					default:
						y += "/";
				}
				break;
			case 123 * x:
				l[u++] = L(y) * $;
			case 125 * x:
			case 59:
			case 0:
				switch (T) {
					case 0:
					case 125:
						A = 0;
					case 59 + f:
						$ == -1 && (y = p(y, /\f/g, "")),
							k > 0 &&
								L(y) - h &&
								Q(
									k > 32
										? we(y + ";", n, t, h - 1)
										: we(p(y, " ", "") + ";", n, t, h - 2),
									d,
								);
						break;
					case 59:
						y += ";";
					default:
						if (
							(Q((_ = Se(y, r, t, u, f, o, l, v, (E = []), (a = []), h)), s),
							T === 123)
						)
							if (f === 0) te(y, r, _, _, E, s, h, l, a);
							else
								switch (z === 99 && P(y, 3) === 110 ? 100 : z) {
									case 100:
									case 108:
									case 109:
									case 115:
										te(
											e,
											_,
											_,
											n && Q(Se(e, _, _, 0, 0, o, l, v, o, (E = []), h), a),
											o,
											a,
											h,
											l,
											n ? E : a,
										);
										break;
									default:
										te(y, _, _, _, [""], a, 0, l, a);
								}
				}
				(u = f = k = 0), (x = $ = 1), (v = y = ""), (h = i);
				break;
			case 58:
				(h = 1 + L(y)), (k = C);
			default:
				if (x < 1) {
					if (T == 123) --x;
					else if (T == 125 && x++ == 0 && Mr() == 125) continue;
				}
				switch (((y += oe(T)), T * x)) {
					case 38:
						$ = f > 0 ? 1 : ((y += "\f"), -1);
						break;
					case 44:
						(l[u++] = (L(y) - 1) * $), ($ = 1);
						break;
					case 64:
						Y() === 45 && (y += re(I())),
							(z = Y()),
							(f = h = L((v = y += Nr(ee())))),
							T++;
						break;
					case 45:
						C === 45 && L(y) == 2 && (x = 0);
				}
		}
	return s;
}
function Se(e, r, t, n, o, s, i, l, d, u, f) {
	for (
		var h = o - 1, z = o === 0 ? s : [""], k = be(z), C = 0, x = 0, A = 0;
		C < n;
		++C
	)
		for (var $ = 0, T = q(e, h + 1, (h = Rr((x = i[C])))), v = e; $ < k; ++$)
			(v = Ie(x > 0 ? z[$] + " " + T : p(T, /&\f/g, z[$]))) && (d[A++] = v);
	return ae(e, r, t, o === 0 ? he : l, d, u, f);
}
function Lr(e, r, t) {
	return ae(e, r, t, Me, oe(Fr()), q(e, 2, -2), 0);
}
function we(e, r, t, n) {
	return ae(e, r, t, ge, q(e, 0, n), q(e, n + 1, -1), n);
}
function B(e, r) {
	for (var t = "", n = be(e), o = 0; o < n; o++) t += r(e[o], o, e, r) || "";
	return t;
}
function Wr(e, r, t, n) {
	switch (e.type) {
		case Tr:
			if (e.children.length) break;
		case Er:
		case ge:
			return (e.return = e.return || e.value);
		case Me:
			return "";
		case ze:
			return (e.return = e.value + "{" + B(e.children, n) + "}");
		case he:
			e.value = e.props.join(",");
	}
	return L((t = B(e.children, n))) ? (e.return = e.value + "{" + t + "}") : "";
}
function Yr(e) {
	var r = be(e);
	return (t, n, o, s) => {
		for (var i = "", l = 0; l < r; l++) i += e[l](t, n, o, s) || "";
		return i;
	};
}
function Vr(e) {
	return (r) => {
		r.root || ((r = r.return) && e(r));
	};
}
function Hr(e) {
	var r = /* @__PURE__ */ Object.create(null);
	return (t) => (r[t] === void 0 && (r[t] = e(t)), r[t]);
}
var Br = (r, t, n) => {
		for (
			var o = 0, s = 0;
			(o = s), (s = Y()), o === 38 && s === 12 && (t[n] = 1), !K(s);
		)
			I();
		return X(r, M);
	},
	Gr = (r, t) => {
		var n = -1,
			o = 44;
		do
			switch (K(o)) {
				case 0:
					o === 38 && Y() === 12 && (t[n] = 1), (r[n] += Br(M - 1, t, n));
					break;
				case 2:
					r[n] += re(o);
					break;
				case 4:
					if (o === 44) {
						(r[++n] = Y() === 58 ? "&\f" : ""), (t[n] = r[n].length);
						break;
					}
				default:
					r[n] += oe(o);
			}
		while ((o = I()));
		return r;
	},
	jr = (r, t) => De(Gr(Ne(r), t)),
	Ee = /* @__PURE__ */ new WeakMap(),
	Ur = (r) => {
		if (
			!(
				r.type !== "rule" ||
				!r.parent || // positive .length indicates that this rule contains pseudo
				// negative .length indicates that this rule has been already prefixed
				r.length < 1
			)
		) {
			for (
				var t = r.value,
					n = r.parent,
					o = r.column === n.column && r.line === n.line;
				n.type !== "rule";
			)
				if (((n = n.parent), !n)) return;
			if (
				!(r.props.length === 1 && t.charCodeAt(0) !== 58 && !Ee.get(n)) &&
				!o
			) {
				Ee.set(r, !0);
				for (
					var s = [], i = jr(t, s), l = n.props, d = 0, u = 0;
					d < i.length;
					d++
				)
					for (var f = 0; f < l.length; f++, u++)
						r.props[u] = s[d] ? i[d].replace(/&\f/g, l[f]) : l[f] + " " + i[d];
			}
		}
	},
	qr = (r) => {
		if (r.type === "decl") {
			var t = r.value;
			// charcode for l
			t.charCodeAt(0) === 108 && // charcode for b
				t.charCodeAt(2) === 98 &&
				((r.return = ""), (r.value = ""));
		}
	};
function Le(e, r) {
	switch (Ar(e, r)) {
		case 5103:
			return m + "print-" + e + e;
		case 5737:
		case 4201:
		case 3177:
		case 3433:
		case 1641:
		case 4457:
		case 2921:
		case 5572:
		case 6356:
		case 5844:
		case 3191:
		case 6645:
		case 3005:
		case 6391:
		case 5879:
		case 5623:
		case 6135:
		case 4599:
		case 4855:
		case 4215:
		case 6389:
		case 5109:
		case 5365:
		case 5621:
		case 3829:
			return m + e + e;
		case 5349:
		case 4246:
		case 4810:
		case 6968:
		case 2756:
			return m + e + ne + e + F + e + e;
		case 6828:
		case 4268:
			return m + e + F + e + e;
		case 6165:
			return m + e + F + "flex-" + e + e;
		case 5187:
			return (
				m + e + p(e, /(\w+).+(:[^]+)/, m + "box-$1$2" + F + "flex-$1$2") + e
			);
		case 5443:
			return m + e + F + "flex-item-" + p(e, /flex-|-self/, "") + e;
		case 4675:
			return (
				m + e + F + "flex-line-pack" + p(e, /align-content|flex-|-self/, "") + e
			);
		case 5548:
			return m + e + F + p(e, "shrink", "negative") + e;
		case 5292:
			return m + e + F + p(e, "basis", "preferred-size") + e;
		case 6060:
			return (
				m +
				"box-" +
				p(e, "-grow", "") +
				m +
				e +
				F +
				p(e, "grow", "positive") +
				e
			);
		case 4554:
			return m + p(e, /([^-])(transform)/g, "$1" + m + "$2") + e;
		case 6187:
			return (
				p(p(p(e, /(zoom-|grab)/, m + "$1"), /(image-set)/, m + "$1"), e, "") + e
			);
		case 5495:
		case 3959:
			return p(e, /(image-set\([^]*)/, m + "$1$`$1");
		case 4968:
			return (
				p(
					p(e, /(.+:)(flex-)?(.*)/, m + "box-pack:$3" + F + "flex-pack:$3"),
					/s.+-b[^;]+/,
					"justify",
				) +
				m +
				e +
				e
			);
		case 4095:
		case 3583:
		case 4068:
		case 2532:
			return p(e, /(.+)-inline(.+)/, m + "$1$2") + e;
		case 8116:
		case 7059:
		case 5753:
		case 5535:
		case 5445:
		case 5701:
		case 4933:
		case 4677:
		case 5533:
		case 5789:
		case 5021:
		case 4765:
			if (L(e) - 1 - r > 6)
				switch (P(e, r + 1)) {
					case 109:
						if (P(e, r + 4) !== 45) break;
					case 102:
						return (
							p(
								e,
								/(.+:)(.+)-([^]+)/,
								"$1" +
									m +
									"$2-$3$1" +
									ne +
									(P(e, r + 3) == 108 ? "$3" : "$2-$3"),
							) + e
						);
					case 115:
						return ~de(e, "stretch")
							? Le(p(e, "stretch", "fill-available"), r) + e
							: e;
				}
			break;
		case 4949:
			if (P(e, r + 1) !== 115) break;
		case 6444:
			switch (P(e, L(e) - 3 - (~de(e, "!important") && 10))) {
				case 107:
					return p(e, ":", ":" + m) + e;
				case 101:
					return (
						p(
							e,
							/(.+:)([^;!]+)(;|!.+)?/,
							"$1" +
								m +
								(P(e, 14) === 45 ? "inline-" : "") +
								"box$3$1" +
								m +
								"$2$3$1" +
								F +
								"$2box$3",
						) + e
					);
			}
			break;
		case 5936:
			switch (P(e, r + 11)) {
				case 114:
					return m + e + F + p(e, /[svh]\w+-[tblr]{2}/, "tb") + e;
				case 108:
					return m + e + F + p(e, /[svh]\w+-[tblr]{2}/, "tb-rl") + e;
				case 45:
					return m + e + F + p(e, /[svh]\w+-[tblr]{2}/, "lr") + e;
			}
			return m + e + F + e + e;
	}
	return e;
}
var Kr = (r, t, n, o) => {
		if (r.length > -1 && !r.return)
			switch (r.type) {
				case ge:
					r.return = Le(r.value, r.length);
					break;
				case ze:
					return B(
						[
							U(r, {
								value: p(r.value, "@", "@" + m),
							}),
						],
						o,
					);
				case he:
					if (r.length)
						return _r(r.props, (s) => {
							switch (Pr(s, /(::plac\w+|:read-\w+)/)) {
								case ":read-only":
								case ":read-write":
									return B(
										[
											U(r, {
												props: [p(s, /:(read-\w+)/, ":" + ne + "$1")],
											}),
										],
										o,
									);
								case "::placeholder":
									return B(
										[
											U(r, {
												props: [p(s, /:(plac\w+)/, ":" + m + "input-$1")],
											}),
											U(r, {
												props: [p(s, /:(plac\w+)/, ":" + ne + "$1")],
											}),
											U(r, {
												props: [p(s, /:(plac\w+)/, F + "input-$1")],
											}),
										],
										o,
									);
							}
							return "";
						});
			}
	},
	Zr = [Kr],
	Xr = (r) => {
		var t = r.key;
		if (t === "css") {
			var n = document.querySelectorAll("style[data-emotion]:not([data-s])");
			Array.prototype.forEach.call(n, (x) => {
				var A = x.getAttribute("data-emotion");
				A.indexOf(" ") !== -1 &&
					(document.head.appendChild(x), x.setAttribute("data-s", ""));
			});
		}
		var o = r.stylisPlugins || Zr,
			s = {},
			i,
			l = [];
		(i = r.container || document.head),
			Array.prototype.forEach.call(
				// this means we will ignore elements which don't have a space in them which
				// means that the style elements we're looking at are only Emotion 11 server-rendered style elements
				document.querySelectorAll('style[data-emotion^="' + t + ' "]'),
				(x) => {
					for (
						var A = x.getAttribute("data-emotion").split(" "), $ = 1;
						$ < A.length;
						$++
					)
						s[A[$]] = !0;
					l.push(x);
				},
			);
		var d,
			u = [Ur, qr];
		var f,
			h = [
				Wr,
				Vr((x) => {
					f.insert(x);
				}),
			],
			z = Yr(u.concat(o, h)),
			k = (A) => B(Dr(A), z);
		d = (A, $, T, v) => {
			(f = T),
				k(A ? A + "{" + $.styles + "}" : $.styles),
				v && (C.inserted[$.name] = !0);
		};
		var C = {
			key: t,
			sheet: new wr({
				key: t,
				container: i,
				nonce: r.nonce,
				speedy: r.speedy,
				prepend: r.prepend,
				insertionPoint: r.insertionPoint,
			}),
			nonce: r.nonce,
			inserted: s,
			registered: {},
			insert: d,
		};
		return C.sheet.hydrate(l), C;
	},
	ue = { exports: {} },
	g = {};
/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Te;
function Jr() {
	if (Te) return g;
	Te = 1;
	var e = typeof Symbol == "function" && Symbol.for,
		r = e ? Symbol.for("react.element") : 60103,
		t = e ? Symbol.for("react.portal") : 60106,
		n = e ? Symbol.for("react.fragment") : 60107,
		o = e ? Symbol.for("react.strict_mode") : 60108,
		s = e ? Symbol.for("react.profiler") : 60114,
		i = e ? Symbol.for("react.provider") : 60109,
		l = e ? Symbol.for("react.context") : 60110,
		d = e ? Symbol.for("react.async_mode") : 60111,
		u = e ? Symbol.for("react.concurrent_mode") : 60111,
		f = e ? Symbol.for("react.forward_ref") : 60112,
		h = e ? Symbol.for("react.suspense") : 60113,
		z = e ? Symbol.for("react.suspense_list") : 60120,
		k = e ? Symbol.for("react.memo") : 60115,
		C = e ? Symbol.for("react.lazy") : 60116,
		x = e ? Symbol.for("react.block") : 60121,
		A = e ? Symbol.for("react.fundamental") : 60117,
		$ = e ? Symbol.for("react.responder") : 60118,
		T = e ? Symbol.for("react.scope") : 60119;
	function v(a) {
		if (typeof a == "object" && a !== null) {
			var _ = a.$$typeof;
			switch (_) {
				case r:
					switch (((a = a.type), a)) {
						case d:
						case u:
						case n:
						case s:
						case o:
						case h:
							return a;
						default:
							switch (((a = a && a.$$typeof), a)) {
								case l:
								case f:
								case C:
								case k:
								case i:
									return a;
								default:
									return _;
							}
					}
				case t:
					return _;
			}
		}
	}
	function E(a) {
		return v(a) === u;
	}
	return (
		(g.AsyncMode = d),
		(g.ConcurrentMode = u),
		(g.ContextConsumer = l),
		(g.ContextProvider = i),
		(g.Element = r),
		(g.ForwardRef = f),
		(g.Fragment = n),
		(g.Lazy = C),
		(g.Memo = k),
		(g.Portal = t),
		(g.Profiler = s),
		(g.StrictMode = o),
		(g.Suspense = h),
		(g.isAsyncMode = (a) => E(a) || v(a) === d),
		(g.isConcurrentMode = E),
		(g.isContextConsumer = (a) => v(a) === l),
		(g.isContextProvider = (a) => v(a) === i),
		(g.isElement = (a) =>
			typeof a == "object" && a !== null && a.$$typeof === r),
		(g.isForwardRef = (a) => v(a) === f),
		(g.isFragment = (a) => v(a) === n),
		(g.isLazy = (a) => v(a) === C),
		(g.isMemo = (a) => v(a) === k),
		(g.isPortal = (a) => v(a) === t),
		(g.isProfiler = (a) => v(a) === s),
		(g.isStrictMode = (a) => v(a) === o),
		(g.isSuspense = (a) => v(a) === h),
		(g.isValidElementType = (a) =>
			typeof a == "string" ||
			typeof a == "function" ||
			a === n ||
			a === u ||
			a === s ||
			a === o ||
			a === h ||
			a === z ||
			(typeof a == "object" &&
				a !== null &&
				(a.$$typeof === C ||
					a.$$typeof === k ||
					a.$$typeof === i ||
					a.$$typeof === l ||
					a.$$typeof === f ||
					a.$$typeof === A ||
					a.$$typeof === $ ||
					a.$$typeof === T ||
					a.$$typeof === x))),
		(g.typeOf = v),
		g
	);
}
var b = {};
/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var Re;
function Qr() {
	return (
		Re ||
			((Re = 1),
			process.env.NODE_ENV !== "production" &&
				(() => {
					var e = typeof Symbol == "function" && Symbol.for,
						r = e ? Symbol.for("react.element") : 60103,
						t = e ? Symbol.for("react.portal") : 60106,
						n = e ? Symbol.for("react.fragment") : 60107,
						o = e ? Symbol.for("react.strict_mode") : 60108,
						s = e ? Symbol.for("react.profiler") : 60114,
						i = e ? Symbol.for("react.provider") : 60109,
						l = e ? Symbol.for("react.context") : 60110,
						d = e ? Symbol.for("react.async_mode") : 60111,
						u = e ? Symbol.for("react.concurrent_mode") : 60111,
						f = e ? Symbol.for("react.forward_ref") : 60112,
						h = e ? Symbol.for("react.suspense") : 60113,
						z = e ? Symbol.for("react.suspense_list") : 60120,
						k = e ? Symbol.for("react.memo") : 60115,
						C = e ? Symbol.for("react.lazy") : 60116,
						x = e ? Symbol.for("react.block") : 60121,
						A = e ? Symbol.for("react.fundamental") : 60117,
						$ = e ? Symbol.for("react.responder") : 60118,
						T = e ? Symbol.for("react.scope") : 60119;
					function v(c) {
						return (
							typeof c == "string" ||
							typeof c == "function" || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
							c === n ||
							c === u ||
							c === s ||
							c === o ||
							c === h ||
							c === z ||
							(typeof c == "object" &&
								c !== null &&
								(c.$$typeof === C ||
									c.$$typeof === k ||
									c.$$typeof === i ||
									c.$$typeof === l ||
									c.$$typeof === f ||
									c.$$typeof === A ||
									c.$$typeof === $ ||
									c.$$typeof === T ||
									c.$$typeof === x))
						);
					}
					function E(c) {
						if (typeof c == "object" && c !== null) {
							var ie = c.$$typeof;
							switch (ie) {
								case r:
									var J = c.type;
									switch (J) {
										case d:
										case u:
										case n:
										case s:
										case o:
										case h:
											return J;
										default:
											var $e = J && J.$$typeof;
											switch ($e) {
												case l:
												case f:
												case C:
												case k:
												case i:
													return $e;
												default:
													return ie;
											}
									}
								case t:
									return ie;
							}
						}
					}
					var a = d,
						_ = u,
						y = l,
						Ke = i,
						Ze = r,
						Xe = f,
						Je = n,
						Qe = C,
						er = k,
						rr = t,
						tr = s,
						nr = o,
						or = h,
						xe = !1;
					function sr(c) {
						return (
							xe ||
								((xe = !0),
								console.warn(
									"The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.",
								)),
							ve(c) || E(c) === d
						);
					}
					function ve(c) {
						return E(c) === u;
					}
					function ar(c) {
						return E(c) === l;
					}
					function ir(c) {
						return E(c) === i;
					}
					function cr(c) {
						return typeof c == "object" && c !== null && c.$$typeof === r;
					}
					function lr(c) {
						return E(c) === f;
					}
					function dr(c) {
						return E(c) === n;
					}
					function fr(c) {
						return E(c) === C;
					}
					function ur(c) {
						return E(c) === k;
					}
					function mr(c) {
						return E(c) === t;
					}
					function pr(c) {
						return E(c) === s;
					}
					function hr(c) {
						return E(c) === o;
					}
					function gr(c) {
						return E(c) === h;
					}
					(b.AsyncMode = a),
						(b.ConcurrentMode = _),
						(b.ContextConsumer = y),
						(b.ContextProvider = Ke),
						(b.Element = Ze),
						(b.ForwardRef = Xe),
						(b.Fragment = Je),
						(b.Lazy = Qe),
						(b.Memo = er),
						(b.Portal = rr),
						(b.Profiler = tr),
						(b.StrictMode = nr),
						(b.Suspense = or),
						(b.isAsyncMode = sr),
						(b.isConcurrentMode = ve),
						(b.isContextConsumer = ar),
						(b.isContextProvider = ir),
						(b.isElement = cr),
						(b.isForwardRef = lr),
						(b.isFragment = dr),
						(b.isLazy = fr),
						(b.isMemo = ur),
						(b.isPortal = mr),
						(b.isProfiler = pr),
						(b.isStrictMode = hr),
						(b.isSuspense = gr),
						(b.isValidElementType = v),
						(b.typeOf = E);
				})()),
		b
	);
}
process.env.NODE_ENV === "production"
	? (ue.exports = Jr())
	: (ue.exports = Qr());
var et = ue.exports,
	We = et,
	rt = {
		$$typeof: !0,
		render: !0,
		defaultProps: !0,
		displayName: !0,
		propTypes: !0,
	},
	tt = {
		$$typeof: !0,
		compare: !0,
		defaultProps: !0,
		displayName: !0,
		propTypes: !0,
		type: !0,
	},
	Ye = {};
Ye[We.ForwardRef] = rt;
Ye[We.Memo] = tt;
var nt = !0;
function ot(e, r, t) {
	var n = "";
	return (
		t.split(" ").forEach((o) => {
			e[o] !== void 0 ? r.push(e[o] + ";") : o && (n += o + " ");
		}),
		n
	);
}
var Ve = (r, t, n) => {
		var o = r.key + "-" + t.name;
		// we only need to add the styles to the registered cache if the
		// class name could be used further down
		// the tree but if it's a string tag, we know it won't
		// so we don't have to add it to registered cache.
		// this improves memory usage since we can avoid storing the whole style string
		(n === !1 || // we need to always store it if we're in compat mode and
			// in node since emotion-server relies on whether a style is in
			// the registered cache to know whether a style is global or not
			// also, note that this check will be dead code eliminated in the browser
			nt === !1) &&
			r.registered[o] === void 0 &&
			(r.registered[o] = t.styles);
	},
	st = (r, t, n) => {
		Ve(r, t, n);
		var o = r.key + "-" + t.name;
		if (r.inserted[t.name] === void 0) {
			var s = t;
			do r.insert(t === s ? "." + o : "", s, r.sheet, !0), (s = s.next);
			while (s !== void 0);
		}
	};
function at(e) {
	for (var r = 0, t, n = 0, o = e.length; o >= 4; ++n, o -= 4)
		(t =
			(e.charCodeAt(n) & 255) |
			((e.charCodeAt(++n) & 255) << 8) |
			((e.charCodeAt(++n) & 255) << 16) |
			((e.charCodeAt(++n) & 255) << 24)),
			(t =
				/* Math.imul(k, m): */
				(t & 65535) * 1540483477 + (((t >>> 16) * 59797) << 16)),
			(t ^= /* k >>> r: */ t >>> 24),
			(r =
				/* Math.imul(k, m): */
				((t & 65535) * 1540483477 +
					(((t >>> 16) * 59797) << 16)) /* Math.imul(h, m): */ ^
				((r & 65535) * 1540483477 + (((r >>> 16) * 59797) << 16)));
	switch (o) {
		case 3:
			r ^= (e.charCodeAt(n + 2) & 255) << 16;
		case 2:
			r ^= (e.charCodeAt(n + 1) & 255) << 8;
		case 1:
			(r ^= e.charCodeAt(n) & 255),
				(r =
					/* Math.imul(h, m): */
					(r & 65535) * 1540483477 + (((r >>> 16) * 59797) << 16));
	}
	return (
		(r ^= r >>> 13),
		(r =
			/* Math.imul(h, m): */
			(r & 65535) * 1540483477 + (((r >>> 16) * 59797) << 16)),
		((r ^ (r >>> 15)) >>> 0).toString(36)
	);
}
var it = {
		animationIterationCount: 1,
		aspectRatio: 1,
		borderImageOutset: 1,
		borderImageSlice: 1,
		borderImageWidth: 1,
		boxFlex: 1,
		boxFlexGroup: 1,
		boxOrdinalGroup: 1,
		columnCount: 1,
		columns: 1,
		flex: 1,
		flexGrow: 1,
		flexPositive: 1,
		flexShrink: 1,
		flexNegative: 1,
		flexOrder: 1,
		gridRow: 1,
		gridRowEnd: 1,
		gridRowSpan: 1,
		gridRowStart: 1,
		gridColumn: 1,
		gridColumnEnd: 1,
		gridColumnSpan: 1,
		gridColumnStart: 1,
		msGridRow: 1,
		msGridRowSpan: 1,
		msGridColumn: 1,
		msGridColumnSpan: 1,
		fontWeight: 1,
		lineHeight: 1,
		opacity: 1,
		order: 1,
		orphans: 1,
		scale: 1,
		tabSize: 1,
		widows: 1,
		zIndex: 1,
		zoom: 1,
		WebkitLineClamp: 1,
		// SVG-related properties
		fillOpacity: 1,
		floodOpacity: 1,
		stopOpacity: 1,
		strokeDasharray: 1,
		strokeDashoffset: 1,
		strokeMiterlimit: 1,
		strokeOpacity: 1,
		strokeWidth: 1,
	},
	ct = /[A-Z]|^ms/g,
	lt = /_EMO_([^_]+?)_([^]*?)_EMO_/g,
	He = (r) => r.charCodeAt(1) === 45,
	ke = (r) => r != null && typeof r != "boolean",
	ce = /* @__PURE__ */ Hr((e) =>
		He(e) ? e : e.replace(ct, "-$&").toLowerCase(),
	),
	Ae = (r, t) => {
		switch (r) {
			case "animation":
			case "animationName":
				if (typeof t == "string")
					return t.replace(
						lt,
						(n, o, s) => (
							(W = {
								name: o,
								styles: s,
								next: W,
							}),
							o
						),
					);
		}
		return it[r] !== 1 && !He(r) && typeof t == "number" && t !== 0
			? t + "px"
			: t;
	};
function Z(e, r, t) {
	if (t == null) return "";
	var n = t;
	if (n.__emotion_styles !== void 0) return n;
	switch (typeof t) {
		case "boolean":
			return "";
		case "object": {
			var o = t;
			if (o.anim === 1)
				return (
					(W = {
						name: o.name,
						styles: o.styles,
						next: W,
					}),
					o.name
				);
			var s = t;
			if (s.styles !== void 0) {
				var i = s.next;
				if (i !== void 0)
					while (i !== void 0)
						(W = {
							name: i.name,
							styles: i.styles,
							next: W,
						}),
							(i = i.next);
				var l = s.styles + ";";
				return l;
			}
			return dt(e, r, t);
		}
		case "function": {
			if (e !== void 0) {
				var d = W,
					u = t(e);
				return (W = d), Z(e, r, u);
			}
			break;
		}
	}
	var f = t;
	return f;
}
function dt(e, r, t) {
	var n = "";
	if (Array.isArray(t))
		for (var o = 0; o < t.length; o++) n += Z(e, r, t[o]) + ";";
	else
		for (var s in t) {
			var i = t[s];
			if (typeof i != "object") {
				var l = i;
				ke(l) && (n += ce(s) + ":" + Ae(s, l) + ";");
			} else if (Array.isArray(i) && typeof i[0] == "string" && r == null)
				for (var d = 0; d < i.length; d++)
					ke(i[d]) && (n += ce(s) + ":" + Ae(s, i[d]) + ";");
			else {
				var u = Z(e, r, i);
				switch (s) {
					case "animation":
					case "animationName": {
						n += ce(s) + ":" + u + ";";
						break;
					}
					default:
						n += s + "{" + u + "}";
				}
			}
		}
	return n;
}
var Pe = /label:\s*([^\s;{]+)\s*(;|$)/g,
	W;
function ft(e, r, t) {
	if (
		e.length === 1 &&
		typeof e[0] == "object" &&
		e[0] !== null &&
		e[0].styles !== void 0
	)
		return e[0];
	var n = !0,
		o = "";
	W = void 0;
	var s = e[0];
	if (s == null || s.raw === void 0) (n = !1), (o += Z(t, r, s));
	else {
		var i = s;
		o += i[0];
	}
	for (var l = 1; l < e.length; l++)
		if (((o += Z(t, r, e[l])), n)) {
			var d = s;
			o += d[l];
		}
	Pe.lastIndex = 0;
	for (var u = "", f; (f = Pe.exec(o)) !== null; ) u += "-" + f[1];
	var h = at(o) + u;
	return {
		name: h,
		styles: o,
		next: W,
	};
}
var ut = (r) => r(),
	mt = w.useInsertionEffect ? w.useInsertionEffect : !1,
	pt = mt || ut,
	Be = /* @__PURE__ */ w.createContext(
		// we're doing this to avoid preconstruct's dead code elimination in this one case
		// because this module is primarily intended for the browser and node
		// but it's also required in react native and similar environments sometimes
		// and we could have a special build just for that
		// but this is much easier and the native packages
		// might use a different theme context in the future anyway
		typeof HTMLElement < "u"
			? /* @__PURE__ */ Xr({
					key: "css",
				})
			: null,
	);
Be.Provider;
var ht = (r) =>
		V((t, n) => {
			var o = Fe(Be);
			return r(t, o, n);
		}),
	gt = /* @__PURE__ */ w.createContext({}),
	ye = {}.hasOwnProperty,
	me = "__EMOTION_TYPE_PLEASE_DO_NOT_USE__",
	bt = (r, t) => {
		var n = {};
		for (var o in t) ye.call(t, o) && (n[o] = t[o]);
		return (n[me] = r), n;
	},
	yt = (r) => {
		var t = r.cache,
			n = r.serialized,
			o = r.isStringTag;
		return Ve(t, n, o), pt(() => st(t, n, o)), null;
	},
	xt = /* @__PURE__ */ ht((e, r, t) => {
		var n = e.css;
		typeof n == "string" && r.registered[n] !== void 0 && (n = r.registered[n]);
		var o = e[me],
			s = [n],
			i = "";
		typeof e.className == "string"
			? (i = ot(r.registered, s, e.className))
			: e.className != null && (i = e.className + " ");
		var l = ft(s, void 0, w.useContext(gt));
		i += r.key + "-" + l.name;
		var d = {};
		for (var u in e) ye.call(e, u) && u !== "css" && u !== me && (d[u] = e[u]);
		return (
			(d.className = i),
			t && (d.ref = t),
			/* @__PURE__ */ w.createElement(
				w.Fragment,
				null,
				/* @__PURE__ */ w.createElement(yt, {
					cache: r,
					serialized: l,
					isStringTag: typeof o == "string",
				}),
				/* @__PURE__ */ w.createElement(o, d),
			)
		);
	}),
	vt = xt,
	N = (r, t, n) =>
		ye.call(t, "css") ? Ce.jsx(vt, bt(r, t), n) : Ce.jsx(r, t, n);
function _e(e, r) {
	if (typeof e == "function") return e(r);
	e != null && (e.current = r);
}
function $t(...e) {
	return (r) => {
		let t = !1;
		const n = e.map((o) => {
			const s = _e(o, r);
			return !t && typeof s == "function" && (t = !0), s;
		});
		if (t)
			return () => {
				for (let o = 0; o < n.length; o++) {
					const s = n[o];
					typeof s == "function" ? s() : _e(e[o], null);
				}
			};
	};
}
var Ge = w.forwardRef((e, r) => {
	const { children: t, ...n } = e,
		o = w.Children.toArray(t),
		s = o.find(St);
	if (s) {
		const i = s.props.children,
			l = o.map((d) =>
				d === s
					? w.Children.count(i) > 1
						? w.Children.only(null)
						: w.isValidElement(i)
							? i.props.children
							: null
					: d,
			);
		return /* @__PURE__ */ le(pe, {
			...n,
			ref: r,
			children: w.isValidElement(i) ? w.cloneElement(i, void 0, l) : null,
		});
	}
	return /* @__PURE__ */ le(pe, { ...n, ref: r, children: t });
});
Ge.displayName = "Slot";
var pe = w.forwardRef((e, r) => {
	const { children: t, ...n } = e;
	if (w.isValidElement(t)) {
		const o = Et(t);
		return w.cloneElement(t, {
			...wt(n, t.props),
			// @ts-ignore
			ref: r ? $t(r, o) : o,
		});
	}
	return w.Children.count(t) > 1 ? w.Children.only(null) : null;
});
pe.displayName = "SlotClone";
var Ct = ({ children: e }) => /* @__PURE__ */ le(br, { children: e });
function St(e) {
	return w.isValidElement(e) && e.type === Ct;
}
function wt(e, r) {
	const t = { ...r };
	for (const n in r) {
		const o = e[n],
			s = r[n];
		/^on[A-Z]/.test(n)
			? o && s
				? (t[n] = (...l) => {
						s(...l), o(...l);
					})
				: o && (t[n] = o)
			: n === "style"
				? (t[n] = { ...o, ...s })
				: n === "className" && (t[n] = [o, s].filter(Boolean).join(" "));
	}
	return { ...e, ...t };
}
function Et(e) {
	var n, o;
	let r =
			(n = Object.getOwnPropertyDescriptor(e.props, "ref")) == null
				? void 0
				: n.get,
		t = r && "isReactWarning" in r && r.isReactWarning;
	return t
		? e.ref
		: ((r =
				(o = Object.getOwnPropertyDescriptor(e, "ref")) == null
					? void 0
					: o.get),
			(t = r && "isReactWarning" in r && r.isReactWarning),
			t ? e.props.ref : e.props.ref || e.ref);
}
const Tt = {
		default: S`
    background-color: ${({ theme: e }) => e.colors.primary};
    color: ${({ theme: e }) => e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.primary}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
		destructive: S`
    background-color: ${({ theme: e }) => e.colors.destructive};
    color: ${({ theme: e }) => e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.destructive}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
		outline: S`
    background-color: ${({ theme: e }) => e.colors.background};
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid ${({ theme: e }) => e.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.accent};
      color: ${({ theme: e }) => e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
		secondary: S`
    background-color: ${({ theme: e }) => e.colors.secondary};
    color: ${({ theme: e }) => e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.secondary}cc;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
		ghost: S`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme: e }) => e.colors.accent};
      color: ${({ theme: e }) => e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
		link: S`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.primary};
    border: 1px solid transparent;
    text-decoration: underline;
    text-underline-offset: 4px;
    
    &:hover:not(:disabled) {
      text-decoration: none;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    }
  `,
	},
	Rt = {
		default: S`
    height: 2.5rem;
    padding: 0.5rem 1rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  `,
		sm: S`
    height: 2.25rem;
    padding: 0.5rem 0.75rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
    border-radius: calc(${({ theme: e }) => e.sizes.borderRadius} - 2px);
  `,
		lg: S`
    height: 2.75rem;
    padding: 0.5rem 2rem;
    font-size: ${({ theme: e }) => e.sizes.fonts.md};
    border-radius: calc(${({ theme: e }) => e.sizes.borderRadius} - 2px);
  `,
		icon: S`
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
  `,
	},
	kt = H.button`
  /* Base styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  font-weight: ${({ theme: e }) => e.fontWeights.medium};
  transition: ${({ theme: e }) => e.transitions.default};
  cursor: pointer;
  user-select: none;
  
  /* SVG styles */
  & svg {
    pointer-events: none;
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
  
  /* Disabled state */
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Variant styles */
  ${({ variant: e = "default" }) => Tt[e]}
  
  /* Size styles */
  ${({ size: e = "default" }) => Rt[e]}
`,
	At = V(({ className: e, variant: r, size: t, asChild: n = !1, ...o }, s) =>
		/* @__PURE__ */ N(n ? Ge : kt, {
			className: e,
			variant: r,
			size: t,
			ref: s,
			...o,
		}),
	);
At.displayName = "Button";
const Pt = H.input`
  display: flex;
  height: ${({ theme: e }) => e.sizes.formControl};
  width: 100%;
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  border: 1px solid ${({ theme: e }) => e.colors.input};
  background-color: ${({ theme: e }) => e.colors.background};
  padding: 0.5rem 0.75rem;
  font-size: ${({ theme: e }) => e.sizes.fonts.md};
  color: ${({ theme: e }) => e.colors.foreground};
  transition: ${({ theme: e }) => e.transitions.default};
  
  /* File input styles */
  &[type="file"] {
    border: 0;
    background-color: transparent;
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
    font-weight: ${({ theme: e }) => e.fontWeights.medium};
    
    &::file-selector-button {
      border: 0;
      background-color: transparent;
      font-size: ${({ theme: e }) => e.sizes.fonts.sm};
      font-weight: ${({ theme: e }) => e.fontWeights.medium};
      color: ${({ theme: e }) => e.colors.foreground};
      margin-right: 0.5rem;
    }
  }
  
  /* Placeholder styles */
  &::placeholder {
    color: ${({ theme: e }) => e.colors.mutedForeground};
  }
  
  /* Focus styles */
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
    border-color: ${({ theme: e }) => e.colors.ring};
  }
  
  /* Disabled styles */
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  /* Responsive font size */
  @media (max-width: ${({ theme: e }) => e.breakpoints.md}) {
    font-size: ${({ theme: e }) => e.sizes.fonts.md};
  }
  
  @media (min-width: ${({ theme: e }) => e.breakpoints.md}) {
    font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  }
`,
	_t = V(({ type: e = "text", ...r }, t) =>
		/* @__PURE__ */ N(Pt, { ref: t, type: e, ...r }),
	);
_t.displayName = "Input";
const Ft = {
		default: S`
    background-color: ${({ theme: e }) => e.colors.primary};
    color: ${({ theme: e }) => e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.primary}cc;
    }
  `,
		secondary: S`
    background-color: ${({ theme: e }) => e.colors.secondary};
    color: ${({ theme: e }) => e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.secondary}cc;
    }
  `,
		destructive: S`
    background-color: ${({ theme: e }) => e.colors.destructive};
    color: ${({ theme: e }) => e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme: e }) => e.colors.destructive}cc;
    }
  `,
		outline: S`
    background-color: transparent;
    color: ${({ theme: e }) => e.colors.foreground};
    border: 1px solid ${({ theme: e }) => e.colors.border};
  `,
	},
	Mt = H.div`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: ${({ theme: e }) => e.sizes.fonts.xs};
  font-weight: ${({ theme: e }) => e.fontWeights.semibold};
  transition: ${({ theme: e }) => e.transitions.default};
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme: e }) => e.colors.ring};
  }
  
  /* Variant styles */
  ${({ variant: e = "default" }) => Ft[e]}
`,
	zt = V(({ variant: e = "default", ...r }, t) =>
		/* @__PURE__ */ N(Mt, { ref: t, variant: e, ...r }),
	);
zt.displayName = "Badge";
const It = {
		sm: S`
    padding: ${({ theme: e }) => e.sizes.spacing.lg};
  `,
		md: S`
    padding: ${({ theme: e }) => e.sizes.spacing.xl};
  `,
		lg: S`
    padding: ${({ theme: e }) => e.sizes.spacing.xxl};
  `,
	},
	Ot = H.div`
  border-radius: ${({ theme: e }) => e.sizes.borderRadius};
  border: 1px solid ${({ theme: e }) => e.colors.border};
  background-color: ${({ theme: e }) => e.colors.card};
  color: ${({ theme: e }) => e.colors.cardForeground};
  box-shadow: ${({ theme: e }) => e.shadows.sm};
  transition: ${({ theme: e }) => e.transitions.default};
  
  ${({ size: e = "md" }) => It[e]}
`,
	Nt = V(({ size: e = "md", ...r }, t) =>
		/* @__PURE__ */ N(Ot, { ref: t, size: e, ...r }),
	);
Nt.displayName = "Card";
const Dt = H.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme: e }) => e.sizes.spacing.sm};
  margin-bottom: ${({ theme: e }) => e.sizes.spacing.xl};
`,
	Lt = V((e, r) => /* @__PURE__ */ N(Dt, { ref: r, ...e }));
Lt.displayName = "CardHeader";
const Wt = H.h3`
  font-size: ${({ theme: e }) => e.sizes.fonts.xl};
  font-weight: ${({ theme: e }) => e.fontWeights.semibold};
  line-height: ${({ theme: e }) => e.lineHeights.tight};
  letter-spacing: -0.025em;
  margin: 0;
`,
	Yt = V((e, r) => /* @__PURE__ */ N(Wt, { ref: r, ...e }));
Yt.displayName = "CardTitle";
const Vt = H.p`
  font-size: ${({ theme: e }) => e.sizes.fonts.sm};
  color: ${({ theme: e }) => e.colors.mutedForeground};
  line-height: ${({ theme: e }) => e.lineHeights.normal};
  margin: 0;
`,
	Ht = V((e, r) => /* @__PURE__ */ N(Vt, { ref: r, ...e }));
Ht.displayName = "CardDescription";
const Bt = H.div`
  /* Content has no default padding as it's handled by the card itself */
`,
	Gt = V((e, r) => /* @__PURE__ */ N(Bt, { ref: r, ...e }));
Gt.displayName = "CardContent";
const jt = H.div`
  display: flex;
  align-items: center;
  gap: ${({ theme: e }) => e.sizes.spacing.md};
  margin-top: ${({ theme: e }) => e.sizes.spacing.xl};
`,
	Ut = V((e, r) => /* @__PURE__ */ N(jt, { ref: r, ...e }));
Ut.displayName = "CardFooter";
const O = {
		sizes: {
			borderRadius: "0.5rem",
			formControl: "2.5rem",
			fonts: {
				xxs: "0.625rem",
				// 10px
				xs: "0.75rem",
				// 12px
				sm: "0.875rem",
				// 14px
				md: "1rem",
				// 16px
				lg: "1.125rem",
				// 18px
				xl: "1.25rem",
				// 20px
				xxl: "1.5rem",
				// 24px
				xxxl: "2rem",
				// 32px
			},
			spacing: {
				xxs: "0.125rem",
				// 2px
				xs: "0.25rem",
				// 4px
				sm: "0.5rem",
				// 8px
				md: "0.75rem",
				// 12px
				lg: "1rem",
				// 16px
				xl: "1.5rem",
				// 24px
				xxl: "2rem",
				// 32px
				xxxl: "3rem",
				// 48px
			},
		},
		fonts: {
			body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
			heading:
				"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
			mono: "'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace",
		},
		fontWeights: {
			normal: 400,
			medium: 500,
			semibold: 600,
			bold: 700,
		},
		lineHeights: {
			tight: 1.25,
			normal: 1.5,
			relaxed: 1.75,
		},
		zIndicies: {
			loadingOverlay: 9e3,
			dropdownMenu: 8e3,
			dialog: 7e3,
			popover: 6e3,
			tooltip: 5e3,
			sticky: 1e3,
		},
		shadows: {
			sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
			default: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
			md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
			lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
			xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
			inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
		},
		transitions: {
			default: "all 0.2s ease-in-out",
			fast: "all 0.1s ease-in-out",
			slow: "all 0.3s ease-in-out",
		},
		breakpoints: {
			xs: "400px",
			sm: "600px",
			md: "900px",
			lg: "1280px",
			xl: "1440px",
			xxl: "1920px",
		},
		colors: {
			// Semantic colors
			error: "#E74C3C",
			success: "#27AE60",
			warning: "#F1C40F",
			info: "#3498DB",
			// Neutral grays
			white: "#FFFFFF",
			black: "#000000",
			transparent: "transparent",
		},
	},
	je = {
		...O,
		colors: {
			...O.colors,
			// Background colors
			background: "hsl(0 0% 100%)",
			foreground: "hsl(0 0% 3.9%)",
			contentBg: "hsl(0 0% 100%)",
			// UI colors
			card: "hsl(0 0% 100%)",
			cardForeground: "hsl(0 0% 3.9%)",
			popover: "hsl(0 0% 100%)",
			popoverForeground: "hsl(0 0% 3.9%)",
			// Brand colors
			primary: "hsl(0 0% 9%)",
			primaryForeground: "hsl(0 0% 98%)",
			secondary: "hsl(0 0% 96.1%)",
			secondaryForeground: "hsl(0 0% 9%)",
			// Utility colors
			muted: "hsl(0 0% 96.1%)",
			mutedForeground: "hsl(0 0% 45.1%)",
			accent: "hsl(0 0% 96.1%)",
			accentForeground: "hsl(0 0% 9%)",
			destructive: "hsl(0 84.2% 60.2%)",
			destructiveForeground: "hsl(0 0% 98%)",
			// Form colors
			border: "hsl(0 0% 89.8%)",
			input: "hsl(0 0% 89.8%)",
			ring: "hsl(0 0% 3.9%)",
		},
	},
	Ue = {
		...O,
		colors: {
			...O.colors,
			// Background colors
			background: "hsl(0 0% 3.9%)",
			foreground: "hsl(0 0% 98%)",
			contentBg: "hsl(0 0% 3.9%)",
			// UI colors
			card: "hsl(0 0% 3.9%)",
			cardForeground: "hsl(0 0% 98%)",
			popover: "hsl(0 0% 3.9%)",
			popoverForeground: "hsl(0 0% 98%)",
			// Brand colors
			primary: "hsl(0 0% 98%)",
			primaryForeground: "hsl(0 0% 9%)",
			secondary: "hsl(0 0% 14.9%)",
			secondaryForeground: "hsl(0 0% 98%)",
			// Utility colors
			muted: "hsl(0 0% 14.9%)",
			mutedForeground: "hsl(0 0% 63.9%)",
			accent: "hsl(0 0% 14.9%)",
			accentForeground: "hsl(0 0% 98%)",
			destructive: "hsl(0 62.8% 30.6%)",
			destructiveForeground: "hsl(0 0% 98%)",
			// Form colors
			border: "hsl(0 0% 14.9%)",
			input: "hsl(0 0% 14.9%)",
			ring: "hsl(0 0% 83.1%)",
		},
	},
	Xt = {
		xs: (e) => S`
    @media (min-width: ${O.breakpoints.xs}) {
      ${e}
    }
  `,
		sm: (e) => S`
    @media (min-width: ${O.breakpoints.sm}) {
      ${e}
    }
  `,
		md: (e) => S`
    @media (min-width: ${O.breakpoints.md}) {
      ${e}
    }
  `,
		lg: (e) => S`
    @media (min-width: ${O.breakpoints.lg}) {
      ${e}
    }
  `,
		xl: (e) => S`
    @media (min-width: ${O.breakpoints.xl}) {
      ${e}
    }
  `,
		xxl: (e) => S`
    @media (min-width: ${O.breakpoints.xxl}) {
      ${e}
    }
  `,
	},
	Jt = O.breakpoints,
	D = {
		LIGHT: "light",
		DARK: "dark",
	},
	Qt = (e) => {
		switch (e) {
			case D.DARK:
				return Ue;
			case D.LIGHT:
			default:
				return je;
		}
	},
	qe = yr(void 0);
function en({
	children: e,
	defaultTheme: r = D.LIGHT,
	storageKey: t = "dua-ui-theme",
	enableSystem: n = !0,
}) {
	const [o, s] = xr(r);
	vr(() => {
		const f = localStorage.getItem(t);
		if (f && Object.values(D).includes(f)) s(f);
		else if (n) {
			const h = window.matchMedia("(prefers-color-scheme: dark)").matches
				? D.DARK
				: D.LIGHT;
			s(h);
		}
	}, [t, n]);
	const i = (f) => {
			s(f), localStorage.setItem(t, f);
		},
		l = () => {
			const f = o === D.LIGHT ? D.DARK : D.LIGHT;
			i(f);
		},
		d = o === D.DARK ? Ue : je,
		u = {
			theme: o,
			setTheme: i,
			toggleTheme: l,
		};
	return /* @__PURE__ */ N(qe.Provider, {
		value: u,
		children: /* @__PURE__ */ N($r, { theme: d, children: e }),
	});
}
function rn() {
	const e = Fe(qe);
	if (e === void 0)
		throw new Error("useTheme must be used within a ThemeProvider");
	return e;
}
export {
	zt as Badge,
	At as Button,
	Nt as Card,
	Gt as CardContent,
	Ht as CardDescription,
	Ut as CardFooter,
	Lt as CardHeader,
	Yt as CardTitle,
	_t as Input,
	D as THEME_NAMES,
	en as ThemeProvider,
	O as base,
	Ue as darkTheme,
	je as defaultTheme,
	Jt as device,
	Qt as getTheme,
	je as lightTheme,
	Xt as media,
	rn as useTheme,
};
//# sourceMappingURL=index.es.js.map
