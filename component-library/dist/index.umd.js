((y,V)=> {typeof exports=="object"&&typeof module<"u"?V(exports,require("react/jsx-runtime"),require("react"),require("styled-components")):typeof define=="function"&&define.amd?define(["exports","react/jsx-runtime","react","styled-components"],V):(y=typeof globalThis<"u"?globalThis:y||self,V(y.DuaComponentLibrary={},y["react/jsx-runtime"],y.React,y.styled))})(this,(y,V,F,m)=> {function $e(e){const r=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(e){for(const n in e)if(n!=="default"){const t=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,t.get?t:{enumerable:!0,get:()=>e[n]})}}return r.default=e,Object.freeze(r)}const Ce=$e(V),E=$e(F);function or(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}function sr(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),e.nonce!==void 0&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var ar=(()=> {function e(n){this._insertTag=(o)=> {var s;this.tags.length===0?this.insertionPoint?s=this.insertionPoint.nextSibling:this.prepend?s=this.container.firstChild:s=this.before:s=this.tags[this.tags.length-1].nextSibling,this.container.insertBefore(o,s),this.tags.push(o)},this.isSpeedy=n.speedy===void 0?!0:n.speedy,this.tags=[],this.ctr=0,this.nonce=n.nonce,this.key=n.key,this.container=n.container,this.prepend=n.prepend,this.insertionPoint=n.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(t){t.forEach(this._insertTag)},r.insert=function(t){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(sr(this));var o=this.tags[this.tags.length-1];if(this.isSpeedy){var s=or(o);try{s.insertRule(t,s.cssRules.length)}catch{}}else o.appendChild(document.createTextNode(t));this.ctr++},r.flush=function(){this.tags.forEach((t)=> {var o;return(o=t.parentNode)==null?void 0:o.removeChild(t)}),this.tags=[],this.ctr=0},e})(),M="-ms-",J="-moz-",p="-webkit-",we="comm",fe="rule",de="decl",ir="@import",Se="@keyframes",cr="@layer",fr=Math.abs,ee=String.fromCharCode,dr=Object.assign;function ur(e,r){return P(e,0)^45?(((r<<2^P(e,0))<<2^P(e,1))<<2^P(e,2))<<2^P(e,3):0}function Ee(e){return e.trim()}function lr(e,r){return(e=r.exec(e))?e[0]:e}function h(e,r,n){return e.replace(r,n)}function ue(e,r){return e.indexOf(r)}function P(e,r){return e.charCodeAt(r)|0}function U(e,r,n){return e.slice(r,n)}function Y(e){return e.length}function le(e){return e.length}function re(e,r){return r.push(e),e}function mr(e,r){return e.map(r).join("")}var ne=1,B=1,Te=0,z=0,k=0,G="";function te(e,r,n,t,o,s,i){return{value:e,root:r,parent:n,type:t,props:o,children:s,line:ne,column:B,length:i,return:""}}function K(e,r){return dr(te("",null,null,"",null,null,0),e,{length:-e.length},r)}function pr(){return k}function hr(){return k=z>0?P(G,--z):0,B--,k===10&&(B=1,ne--),k}function I(){return k=z<Te?P(G,z++):0,B++,k===10&&(B=1,ne++),k}function j(){return P(G,z)}function oe(){return z}function Z(e,r){return U(G,e,r)}function Q(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function ke(e){return ne=B=1,Te=Y(G=e),z=0,[]}function _e(e){return G="",e}function se(e){return Ee(Z(z-1,me(e===91?e+2:e===40?e+1:e)))}function gr(e){while((k=j())&&k<33)I();return Q(e)>2||Q(k)>3?"":" "}function br(e,r){while(--r&&I()&&!(k<48||k>102||k>57&&k<65||k>70&&k<97));return Z(e,oe()+(r<6&&j()==32&&I()==32))}function me(e){while(I())switch(k){case e:return z;case 34:case 39:e!==34&&e!==39&&me(k);break;case 40:e===41&&me(e);break;case 92:I();break}return z}function xr(e,r){while(I()&&e+k!==57)if(e+k===84&&j()===47)break;return"/*"+Z(r,z-1)+"*"+ee(e===47?e:I())}function yr(e){while(!Q(j()))I();return Z(e,z)}function vr(e){return _e(ae("",null,null,null,[""],e=ke(e),0,[0],e))}function ae(e,r,n,t,o,s,i,f,d){for(var l=0,u=0,x=i,N=0,R=0,S=0,$=1,A=1,w=1,_=0,C="",T=o,a=s,O=t,v=C;A;)switch(S=_,_=I()){case 40:if(S!=108&&P(v,x-1)==58){ue(v+=h(se(_),"&","&\f"),"&\f")!=-1&&(w=-1);break}case 34:case 39:case 91:v+=se(_);break;case 9:case 10:case 13:case 32:v+=gr(S);break;case 92:v+=br(oe()-1,7);continue;case 47:switch(j()){case 42:case 47:re($r(xr(I(),oe()),r,n),d);break;default:v+="/"}break;case 123*$:f[l++]=Y(v)*w;case 125*$:case 59:case 0:switch(_){case 0:case 125:A=0;case 59+u:w==-1&&(v=h(v,/\f/g,"")),R>0&&Y(v)-x&&re(R>32?Ae(v+";",t,n,x-1):Ae(h(v," ","")+";",t,n,x-2),d);break;case 59:v+=";";default:if(re(O=Re(v,r,n,l,u,o,f,C,T=[],a=[],x),s),_===123)if(u===0)ae(v,r,O,O,T,s,x,f,a);else switch(N===99&&P(v,3)===110?100:N){case 100:case 108:case 109:case 115:ae(e,O,O,t&&re(Re(e,O,O,0,0,o,f,C,o,T=[],x),a),o,a,x,f,t?T:a);break;default:ae(v,O,O,O,[""],a,0,f,a)}}l=u=R=0,$=w=1,C=v="",x=i;break;case 58:x=1+Y(v),R=S;default:if($<1){if(_==123)--$;else if(_==125&&$++==0&&hr()==125)continue}switch(v+=ee(_),_*$){case 38:w=u>0?1:(v+="\f",-1);break;case 44:f[l++]=(Y(v)-1)*w,w=1;break;case 64:j()===45&&(v+=se(I())),N=j(),u=x=Y(C=v+=yr(oe())),_++;break;case 45:S===45&&Y(v)==2&&($=0)}}return s}function Re(e,r,n,t,o,s,i,f,d,l,u){for(var x=o-1,N=o===0?s:[""],R=le(N),S=0,$=0,A=0;S<t;++S)for(var w=0,_=U(e,x+1,x=fr($=i[S])),C=e;w<R;++w)(C=Ee($>0?N[w]+" "+_:h(_,/&\f/g,N[w])))&&(d[A++]=C);return te(e,r,n,o===0?fe:f,d,l,u)}function $r(e,r,n){return te(e,r,n,we,ee(pr()),U(e,2,-2),0)}function Ae(e,r,n,t){return te(e,r,n,de,U(e,0,t),U(e,t+1,-1),t)}function q(e,r){for(var n="",t=le(e),o=0;o<t;o++)n+=r(e[o],o,e,r)||"";return n}function Cr(e,r,n,t){switch(e.type){case cr:if(e.children.length)break;case ir:case de:return e.return=e.return||e.value;case we:return"";case Se:return e.return=e.value+"{"+q(e.children,t)+"}";case fe:e.value=e.props.join(",")}return Y(n=q(e.children,t))?e.return=e.value+"{"+n+"}":""}function wr(e){var r=le(e);return (n,t,o,s)=> {for(var i="",f=0;f<r;f++)i+=e[f](n,t,o,s)||"";return i}}function Sr(e){return (r)=> {r.root||(r=r.return)&&e(r)}}function Er(e){var r=Object.create(null);return (n)=> (r[n]===void 0&&(r[n]=e(n)),r[n])}var Tr=(r,n,t)=> {for(var o=0,s=0;o=s,s=j(),o===38&&s===12&&(n[t]=1),!Q(s);)I();return Z(r,z)},kr=(r,n)=> {var t=-1,o=44;do switch(Q(o)){case 0:o===38&&j()===12&&(n[t]=1),r[t]+=Tr(z-1,n,t);break;case 2:r[t]+=se(o);break;case 4:if(o===44){r[++t]=j()===58?"&\f":"",n[t]=r[t].length;break}default:r[t]+=ee(o)}while(o=I());return r},_r=(r,n)=> _e(kr(ke(r),n)),Pe=new WeakMap,Rr=(r)=> {if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var n=r.value,t=r.parent,o=r.column===t.column&&r.line===t.line;t.type!=="rule";)if(t=t.parent,!t)return;if(!(r.props.length===1&&n.charCodeAt(0)!==58&&!Pe.get(t))&&!o){Pe.set(r,!0);for(var s=[],i=_r(n,s),f=t.props,d=0,l=0;d<i.length;d++)for(var u=0;u<f.length;u++,l++)r.props[l]=s[d]?i[d].replace(/&\f/g,f[u]):f[u]+" "+i[d]}}},Ar=(r)=> {if(r.type==="decl"){var n=r.value;n.charCodeAt(0)===108&&n.charCodeAt(2)===98&&(r.return="",r.value="")}};function Fe(e,r){switch(ur(e,r)){case 5103:return p+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return p+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return p+e+J+e+M+e+e;case 6828:case 4268:return p+e+M+e+e;case 6165:return p+e+M+"flex-"+e+e;case 5187:return p+e+h(e,/(\w+).+(:[^]+)/,p+"box-$1$2"+M+"flex-$1$2")+e;case 5443:return p+e+M+"flex-item-"+h(e,/flex-|-self/,"")+e;case 4675:return p+e+M+"flex-line-pack"+h(e,/align-content|flex-|-self/,"")+e;case 5548:return p+e+M+h(e,"shrink","negative")+e;case 5292:return p+e+M+h(e,"basis","preferred-size")+e;case 6060:return p+"box-"+h(e,"-grow","")+p+e+M+h(e,"grow","positive")+e;case 4554:return p+h(e,/([^-])(transform)/g,"$1"+p+"$2")+e;case 6187:return h(h(h(e,/(zoom-|grab)/,p+"$1"),/(image-set)/,p+"$1"),e,"")+e;case 5495:case 3959:return h(e,/(image-set\([^]*)/,p+"$1$`$1");case 4968:return h(h(e,/(.+:)(flex-)?(.*)/,p+"box-pack:$3"+M+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+p+e+e;case 4095:case 3583:case 4068:case 2532:return h(e,/(.+)-inline(.+)/,p+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Y(e)-1-r>6)switch(P(e,r+1)){case 109:if(P(e,r+4)!==45)break;case 102:return h(e,/(.+:)(.+)-([^]+)/,"$1"+p+"$2-$3$1"+J+(P(e,r+3)==108?"$3":"$2-$3"))+e;case 115:return~ue(e,"stretch")?Fe(h(e,"stretch","fill-available"),r)+e:e}break;case 4949:if(P(e,r+1)!==115)break;case 6444:switch(P(e,Y(e)-3-(~ue(e,"!important")&&10))){case 107:return h(e,":",":"+p)+e;case 101:return h(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+p+(P(e,14)===45?"inline-":"")+"box$3$1"+p+"$2$3$1"+M+"$2box$3")+e}break;case 5936:switch(P(e,r+11)){case 114:return p+e+M+h(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return p+e+M+h(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return p+e+M+h(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return p+e+M+e+e}return e}var Pr=(r,n,t,o)=> {if(r.length>-1&&!r.return)switch(r.type){case de:r.return=Fe(r.value,r.length);break;case Se:return q([K(r,{value:h(r.value,"@","@"+p)})],o);case fe:if(r.length)return mr(r.props,(s)=> {switch(lr(s,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return q([K(r,{props:[h(s,/:(read-\w+)/,":"+J+"$1")]})],o);case"::placeholder":return q([K(r,{props:[h(s,/:(plac\w+)/,":"+p+"input-$1")]}),K(r,{props:[h(s,/:(plac\w+)/,":"+J+"$1")]}),K(r,{props:[h(s,/:(plac\w+)/,M+"input-$1")]})],o)}return""})}},Fr=[Pr],Mr=(r)=> {var n=r.key;if(n==="css"){var t=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(t,($)=> {var A=$.getAttribute("data-emotion");A.indexOf(" ")!==-1&&(document.head.appendChild($),$.setAttribute("data-s",""))})}var o=r.stylisPlugins||Fr,s={},i,f=[];i=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+n+' "]'),($)=> {for(var A=$.getAttribute("data-emotion").split(" "),w=1;w<A.length;w++)s[A[w]]=!0;f.push($)});var d,l=[Rr,Ar];var u,x=[Cr,Sr(($)=> {u.insert($)})],N=wr(l.concat(o,x)),R=(A)=> q(vr(A),N);d=(A,w,_,C)=> {u=_,R(A?A+"{"+w.styles+"}":w.styles),C&&(S.inserted[w.name]=!0)}var S={key:n,sheet:new ar({key:n,container:i,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:s,registered:{},insert:d};return S.sheet.hydrate(f),S},pe={exports:{}},g={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Me;function Or(){if(Me)return g;Me=1;var e=typeof Symbol=="function"&&Symbol.for,r=e?Symbol.for("react.element"):60103,n=e?Symbol.for("react.portal"):60106,t=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,s=e?Symbol.for("react.profiler"):60114,i=e?Symbol.for("react.provider"):60109,f=e?Symbol.for("react.context"):60110,d=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,x=e?Symbol.for("react.suspense"):60113,N=e?Symbol.for("react.suspense_list"):60120,R=e?Symbol.for("react.memo"):60115,S=e?Symbol.for("react.lazy"):60116,$=e?Symbol.for("react.block"):60121,A=e?Symbol.for("react.fundamental"):60117,w=e?Symbol.for("react.responder"):60118,_=e?Symbol.for("react.scope"):60119;function C(a){if(typeof a=="object"&&a!==null){var O=a.$$typeof;switch(O){case r:switch(a=a.type,a){case d:case l:case t:case s:case o:case x:return a;default:switch(a=a&&a.$$typeof,a){case f:case u:case S:case R:case i:return a;default:return O}}case n:return O}}}function T(a){return C(a)===l}return g.AsyncMode=d,g.ConcurrentMode=l,g.ContextConsumer=f,g.ContextProvider=i,g.Element=r,g.ForwardRef=u,g.Fragment=t,g.Lazy=S,g.Memo=R,g.Portal=n,g.Profiler=s,g.StrictMode=o,g.Suspense=x,g.isAsyncMode=(a)=> T(a)||C(a)===d,g.isConcurrentMode=T,g.isContextConsumer=(a)=> C(a)===f,g.isContextProvider=(a)=> C(a)===i,g.isElement=(a)=> typeof a=="object"&&a!==null&&a.$$typeof===r,g.isForwardRef=(a)=> C(a)===u,g.isFragment=(a)=> C(a)===t,g.isLazy=(a)=> C(a)===S,g.isMemo=(a)=> C(a)===R,g.isPortal=(a)=> C(a)===n,g.isProfiler=(a)=> C(a)===s,g.isStrictMode=(a)=> C(a)===o,g.isSuspense=(a)=> C(a)===x,g.isValidElementType=(a)=> typeof a=="string"||typeof a=="function"||a===t||a===l||a===s||a===o||a===x||a===N||typeof a=="object"&&a!==null&&(a.$$typeof===S||a.$$typeof===R||a.$$typeof===i||a.$$typeof===f||a.$$typeof===u||a.$$typeof===A||a.$$typeof===w||a.$$typeof===_||a.$$typeof===$),g.typeOf=C,g}var b={};/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oe;function zr(){return Oe||(Oe=1,process.env.NODE_ENV!=="production"&&(()=> {var e=typeof Symbol=="function"&&Symbol.for,r=e?Symbol.for("react.element"):60103,n=e?Symbol.for("react.portal"):60106,t=e?Symbol.for("react.fragment"):60107,o=e?Symbol.for("react.strict_mode"):60108,s=e?Symbol.for("react.profiler"):60114,i=e?Symbol.for("react.provider"):60109,f=e?Symbol.for("react.context"):60110,d=e?Symbol.for("react.async_mode"):60111,l=e?Symbol.for("react.concurrent_mode"):60111,u=e?Symbol.for("react.forward_ref"):60112,x=e?Symbol.for("react.suspense"):60113,N=e?Symbol.for("react.suspense_list"):60120,R=e?Symbol.for("react.memo"):60115,S=e?Symbol.for("react.lazy"):60116,$=e?Symbol.for("react.block"):60121,A=e?Symbol.for("react.fundamental"):60117,w=e?Symbol.for("react.responder"):60118,_=e?Symbol.for("react.scope"):60119;function C(c){return typeof c=="string"||typeof c=="function"||c===t||c===l||c===s||c===o||c===x||c===N||typeof c=="object"&&c!==null&&(c.$$typeof===S||c.$$typeof===R||c.$$typeof===i||c.$$typeof===f||c.$$typeof===u||c.$$typeof===A||c.$$typeof===w||c.$$typeof===_||c.$$typeof===$)}function T(c){if(typeof c=="object"&&c!==null){var ve=c.$$typeof;switch(ve){case r:var ce=c.type;switch(ce){case d:case l:case t:case s:case o:case x:return ce;default:var tr=ce&&ce.$$typeof;switch(tr){case f:case u:case S:case R:case i:return tr;default:return ve}}case n:return ve}}}var a=d,O=l,v=f,kn=i,_n=r,Rn=u,An=t,Pn=S,Fn=R,Mn=n,On=s,zn=o,In=x,rr=!1;function Nn(c){return rr||(rr=!0,console.warn("The ReactIs.isAsyncMode() alias has been deprecated, and will be removed in React 17+. Update your code to use ReactIs.isConcurrentMode() instead. It has the exact same API.")),nr(c)||T(c)===d}function nr(c){return T(c)===l}function Dn(c){return T(c)===f}function Ln(c){return T(c)===i}function Wn(c){return typeof c=="object"&&c!==null&&c.$$typeof===r}function Yn(c){return T(c)===u}function jn(c){return T(c)===t}function Hn(c){return T(c)===S}function Vn(c){return T(c)===R}function Bn(c){return T(c)===n}function Gn(c){return T(c)===s}function qn(c){return T(c)===o}function Un(c){return T(c)===x}b.AsyncMode=a,b.ConcurrentMode=O,b.ContextConsumer=v,b.ContextProvider=kn,b.Element=_n,b.ForwardRef=Rn,b.Fragment=An,b.Lazy=Pn,b.Memo=Fn,b.Portal=Mn,b.Profiler=On,b.StrictMode=zn,b.Suspense=In,b.isAsyncMode=Nn,b.isConcurrentMode=nr,b.isContextConsumer=Dn,b.isContextProvider=Ln,b.isElement=Wn,b.isForwardRef=Yn,b.isFragment=jn,b.isLazy=Hn,b.isMemo=Vn,b.isPortal=Bn,b.isProfiler=Gn,b.isStrictMode=qn,b.isSuspense=Un,b.isValidElementType=C,b.typeOf=T})()),b}process.env.NODE_ENV==="production"?pe.exports=Or():pe.exports=zr();var Ir=pe.exports,ze=Ir,Nr={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Dr={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Ie={};Ie[ze.ForwardRef]=Nr,Ie[ze.Memo]=Dr;var Lr=!0;function Wr(e,r,n){var t="";return n.split(" ").forEach((o)=> {e[o]!==void 0?r.push(e[o]+";"):o&&(t+=o+" ")}),t}var Ne=(r,n,t)=> {var o=r.key+"-"+n.name;(t===!1||Lr===!1)&&r.registered[o]===void 0&&(r.registered[o]=n.styles)},Yr=(r,n,t)=> {Ne(r,n,t);var o=r.key+"-"+n.name;if(r.inserted[n.name]===void 0){var s=n;do r.insert(n===s?"."+o:"",s,r.sheet,!0),s=s.next;while(s!==void 0)}};function jr(e){for(var r=0,n,t=0,o=e.length;o>=4;++t,o-=4)n=e.charCodeAt(t)&255|(e.charCodeAt(++t)&255)<<8|(e.charCodeAt(++t)&255)<<16|(e.charCodeAt(++t)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,r=(n&65535)*1540483477+((n>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(o){case 3:r^=(e.charCodeAt(t+2)&255)<<16;case 2:r^=(e.charCodeAt(t+1)&255)<<8;case 1:r^=e.charCodeAt(t)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var Hr={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Vr=/[A-Z]|^ms/g,Br=/_EMO_([^_]+?)_([^]*?)_EMO_/g,De=(r)=> r.charCodeAt(1)===45,Le=(r)=> r!=null&&typeof r!="boolean",he=Er((e)=> De(e)?e:e.replace(Vr,"-$&").toLowerCase()),We=(r,n)=> {switch(r){case"animation":case"animationName":if(typeof n=="string")return n.replace(Br,(t,o,s)=> (H={name:o,styles:s,next:H},o))}return Hr[r]!==1&&!De(r)&&typeof n=="number"&&n!==0?n+"px":n};function X(e,r,n){if(n==null)return"";var t=n;if(t.__emotion_styles!==void 0)return t;switch(typeof n){case"boolean":return"";case"object":{var o=n;if(o.anim===1)return H={name:o.name,styles:o.styles,next:H},o.name;var s=n;if(s.styles!==void 0){var i=s.next;if(i!==void 0)while(i!==void 0)H={name:i.name,styles:i.styles,next:H},i=i.next;var f=s.styles+";";return f}return Gr(e,r,n)}case"function":{if(e!==void 0){var d=H,l=n(e);return H=d,X(e,r,l)}break}}var u=n;return u}function Gr(e,r,n){var t="";if(Array.isArray(n))for(var o=0;o<n.length;o++)t+=X(e,r,n[o])+";";else for(var s in n){var i=n[s];if(typeof i!="object"){var f=i;Le(f)&&(t+=he(s)+":"+We(s,f)+";")}else if(Array.isArray(i)&&typeof i[0]=="string"&&r==null)for(var d=0;d<i.length;d++)Le(i[d])&&(t+=he(s)+":"+We(s,i[d])+";");else{var l=X(e,r,i);switch(s){case"animation":case"animationName":{t+=he(s)+":"+l+";";break}default:t+=s+"{"+l+"}"}}}return t}var Ye=/label:\s*([^\s;{]+)\s*(;|$)/g,H;function qr(e,r,n){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var t=!0,o="";H=void 0;var s=e[0];if(s==null||s.raw===void 0)t=!1,o+=X(n,r,s);else{var i=s;o+=i[0]}for(var f=1;f<e.length;f++)if(o+=X(n,r,e[f]),t){var d=s;o+=d[f]}Ye.lastIndex=0;for(var l="",u;(u=Ye.exec(o))!==null;)l+="-"+u[1];var x=jr(o)+l;return{name:x,styles:o,next:H}}var Ur=(r)=> r(),Kr=E.useInsertionEffect?E.useInsertionEffect:!1,Zr=Kr||Ur,je=E.createContext(typeof HTMLElement<"u"?Mr({key:"css"}):null);je.Provider;var Qr=(r)=> F.forwardRef((n,t)=> {var o=F.useContext(je);return r(n,o,t)}),Xr=E.createContext({}),ge={}.hasOwnProperty,be="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",Jr=(r,n)=> {var t={};for(var o in n)ge.call(n,o)&&(t[o]=n[o]);return t[be]=r,t},en=(r)=> {var n=r.cache,t=r.serialized,o=r.isStringTag;return Ne(n,t,o),Zr(()=> Yr(n,t,o)),null},rn=Qr((e,r,n)=> {var t=e.css;typeof t=="string"&&r.registered[t]!==void 0&&(t=r.registered[t]);var o=e[be],s=[t],i="";typeof e.className=="string"?i=Wr(r.registered,s,e.className):e.className!=null&&(i=e.className+" ");var f=qr(s,void 0,E.useContext(Xr));i+=r.key+"-"+f.name;var d={};for(var l in e)ge.call(e,l)&&l!=="css"&&l!==be&&(d[l]=e[l]);return d.className=i,n&&(d.ref=n),E.createElement(E.Fragment,null,E.createElement(en,{cache:r,serialized:f,isStringTag:typeof o=="string"}),E.createElement(o,d))}),nn=rn,L=(r,n,t)=> ge.call(n,"css")?Ce.jsx(nn,Jr(r,n),t):Ce.jsx(r,n,t);function He(e,r){if(typeof e=="function")return e(r);e!=null&&(e.current=r)}function tn(...e){return r=>{let n=!1;const t=e.map(o=>{const s=He(o,r);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<t.length;o++){const s=t[o];typeof s=="function"?s():He(e[o],null)}}}}var Ve=E.forwardRef((e,r)=>{const{children:n,...t}=e,o=E.Children.toArray(n),s=o.find(sn);if(s){const i=s.props.children,f=o.map(d=>d===s?E.Children.count(i)>1?E.Children.only(null):E.isValidElement(i)?i.props.children:null:d);return V.jsx(xe,{...t,ref:r,children:E.isValidElement(i)?E.cloneElement(i,void 0,f):null})}return V.jsx(xe,{...t,ref:r,children:n})});Ve.displayName="Slot";var xe=E.forwardRef((e,r)=>{const{children:n,...t}=e;if(E.isValidElement(n)){const o=cn(n);return E.cloneElement(n,{...an(t,n.props),ref:r?tn(r,o):o})}return E.Children.count(n)>1?E.Children.only(null):null});xe.displayName="SlotClone";var on=({children:e})=>V.jsx(V.Fragment,{children:e});function sn(e){return E.isValidElement(e)&&e.type===on}function an(e,r){const n={...r};for(const t in r){const o=e[t],s=r[t];/^on[A-Z]/.test(t)?o&&s?n[t]=(...f)=>{s(...f),o(...f)}:o&&(n[t]=o):t==="style"?n[t]={...o,...s}:t==="className"&&(n[t]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function cn(e){var t,o;let r=(t=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:t.get,n=r&&"isReactWarning"in r&&r.isReactWarning;return n?e.ref:(r=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=r&&"isReactWarning"in r&&r.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}const fn={default:m.css`
    background-color: ${({theme:e})=>e.colors.primary};
    color: ${({theme:e})=>e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({theme:e})=>e.colors.primary}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `,destructive:m.css`
    background-color: ${({theme:e})=>e.colors.destructive};
    color: ${({theme:e})=>e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({theme:e})=>e.colors.destructive}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `,outline:m.css`
    background-color: ${({theme:e})=>e.colors.background};
    color: ${({theme:e})=>e.colors.foreground};
    border: 1px solid ${({theme:e})=>e.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${({theme:e})=>e.colors.accent};
      color: ${({theme:e})=>e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `,secondary:m.css`
    background-color: ${({theme:e})=>e.colors.secondary};
    color: ${({theme:e})=>e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({theme:e})=>e.colors.secondary}cc;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `,ghost:m.css`
    background-color: transparent;
    color: ${({theme:e})=>e.colors.foreground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({theme:e})=>e.colors.accent};
      color: ${({theme:e})=>e.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `,link:m.css`
    background-color: transparent;
    color: ${({theme:e})=>e.colors.primary};
    border: 1px solid transparent;
    text-decoration: underline;
    text-underline-offset: 4px;
    
    &:hover:not(:disabled) {
      text-decoration: none;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    }
  `},dn={default:m.css`
    height: 2.5rem;
    padding: 0.5rem 1rem;
    font-size: ${({theme:e})=>e.sizes.fonts.sm};
  `,sm:m.css`
    height: 2.25rem;
    padding: 0.5rem 0.75rem;
    font-size: ${({theme:e})=>e.sizes.fonts.sm};
    border-radius: calc(${({theme:e})=>e.sizes.borderRadius} - 2px);
  `,lg:m.css`
    height: 2.75rem;
    padding: 0.5rem 2rem;
    font-size: ${({theme:e})=>e.sizes.fonts.md};
    border-radius: calc(${({theme:e})=>e.sizes.borderRadius} - 2px);
  `,icon:m.css`
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
  `},un=m.button`
  /* Base styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: ${({theme:e})=>e.sizes.borderRadius};
  font-weight: ${({theme:e})=>e.fontWeights.medium};
  transition: ${({theme:e})=>e.transitions.default};
  cursor: pointer;
  user-select: none;
  
  /* SVG styles */
  & svg {
    pointer-events: none;
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
  
  /* Disabled state */
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Variant styles */
  ${({variant:e="default"})=>fn[e]}
  
  /* Size styles */
  ${({size:e="default"})=>dn[e]}
`,Be=F.forwardRef(({className:e,variant:r,size:n,asChild:t=!1,...o},s)=>L(t?Ve:un,{className:e,variant:r,size:n,ref:s,...o}));Be.displayName="Button";const ln=m.input`
  display: flex;
  height: ${({theme:e})=>e.sizes.formControl};
  width: 100%;
  border-radius: ${({theme:e})=>e.sizes.borderRadius};
  border: 1px solid ${({theme:e})=>e.colors.input};
  background-color: ${({theme:e})=>e.colors.background};
  padding: 0.5rem 0.75rem;
  font-size: ${({theme:e})=>e.sizes.fonts.md};
  color: ${({theme:e})=>e.colors.foreground};
  transition: ${({theme:e})=>e.transitions.default};
  
  /* File input styles */
  &[type="file"] {
    border: 0;
    background-color: transparent;
    font-size: ${({theme:e})=>e.sizes.fonts.sm};
    font-weight: ${({theme:e})=>e.fontWeights.medium};
    
    &::file-selector-button {
      border: 0;
      background-color: transparent;
      font-size: ${({theme:e})=>e.sizes.fonts.sm};
      font-weight: ${({theme:e})=>e.fontWeights.medium};
      color: ${({theme:e})=>e.colors.foreground};
      margin-right: 0.5rem;
    }
  }
  
  /* Placeholder styles */
  &::placeholder {
    color: ${({theme:e})=>e.colors.mutedForeground};
  }
  
  /* Focus styles */
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
    border-color: ${({theme:e})=>e.colors.ring};
  }
  
  /* Disabled styles */
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  /* Responsive font size */
  @media (max-width: ${({theme:e})=>e.breakpoints.md}) {
    font-size: ${({theme:e})=>e.sizes.fonts.md};
  }
  
  @media (min-width: ${({theme:e})=>e.breakpoints.md}) {
    font-size: ${({theme:e})=>e.sizes.fonts.sm};
  }
`,Ge=F.forwardRef(({type:e="text",...r},n)=>L(ln,{ref:n,type:e,...r}));Ge.displayName="Input";const mn={default:m.css`
    background-color: ${({theme:e})=>e.colors.primary};
    color: ${({theme:e})=>e.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({theme:e})=>e.colors.primary}cc;
    }
  `,secondary:m.css`
    background-color: ${({theme:e})=>e.colors.secondary};
    color: ${({theme:e})=>e.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({theme:e})=>e.colors.secondary}cc;
    }
  `,destructive:m.css`
    background-color: ${({theme:e})=>e.colors.destructive};
    color: ${({theme:e})=>e.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({theme:e})=>e.colors.destructive}cc;
    }
  `,outline:m.css`
    background-color: transparent;
    color: ${({theme:e})=>e.colors.foreground};
    border: 1px solid ${({theme:e})=>e.colors.border};
  `},pn=m.div`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: ${({theme:e})=>e.sizes.fonts.xs};
  font-weight: ${({theme:e})=>e.fontWeights.semibold};
  transition: ${({theme:e})=>e.transitions.default};
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({theme:e})=>e.colors.ring};
  }
  
  /* Variant styles */
  ${({variant:e="default"})=>mn[e]}
`,qe=F.forwardRef(({variant:e="default",...r},n)=>L(pn,{ref:n,variant:e,...r}));qe.displayName="Badge";const hn={sm:m.css`
    padding: ${({theme:e})=>e.sizes.spacing.lg};
  `,md:m.css`
    padding: ${({theme:e})=>e.sizes.spacing.xl};
  `,lg:m.css`
    padding: ${({theme:e})=>e.sizes.spacing.xxl};
  `},gn=m.div`
  border-radius: ${({theme:e})=>e.sizes.borderRadius};
  border: 1px solid ${({theme:e})=>e.colors.border};
  background-color: ${({theme:e})=>e.colors.card};
  color: ${({theme:e})=>e.colors.cardForeground};
  box-shadow: ${({theme:e})=>e.shadows.sm};
  transition: ${({theme:e})=>e.transitions.default};
  
  ${({size:e="md"})=>hn[e]}
`,Ue=F.forwardRef(({size:e="md",...r},n)=>L(gn,{ref:n,size:e,...r}));Ue.displayName="Card";const bn=m.div`
  display: flex;
  flex-direction: column;
  gap: ${({theme:e})=>e.sizes.spacing.sm};
  margin-bottom: ${({theme:e})=>e.sizes.spacing.xl};
`,Ke=F.forwardRef((e,r)=>L(bn,{ref:r,...e}));Ke.displayName="CardHeader";const xn=m.h3`
  font-size: ${({theme:e})=>e.sizes.fonts.xl};
  font-weight: ${({theme:e})=>e.fontWeights.semibold};
  line-height: ${({theme:e})=>e.lineHeights.tight};
  letter-spacing: -0.025em;
  margin: 0;
`,Ze=F.forwardRef((e,r)=>L(xn,{ref:r,...e}));Ze.displayName="CardTitle";const yn=m.p`
  font-size: ${({theme:e})=>e.sizes.fonts.sm};
  color: ${({theme:e})=>e.colors.mutedForeground};
  line-height: ${({theme:e})=>e.lineHeights.normal};
  margin: 0;
`,Qe=F.forwardRef((e,r)=>L(yn,{ref:r,...e}));Qe.displayName="CardDescription";const vn=m.div`
  /* Content has no default padding as it's handled by the card itself */
`,Xe=F.forwardRef((e,r)=>L(vn,{ref:r,...e}));Xe.displayName="CardContent";const $n=m.div`
  display: flex;
  align-items: center;
  gap: ${({theme:e})=>e.sizes.spacing.md};
  margin-top: ${({theme:e})=>e.sizes.spacing.xl};
`,Je=F.forwardRef((e,r)=>L($n,{ref:r,...e}));Je.displayName="CardFooter";const D={sizes:{borderRadius:"0.5rem",formControl:"2.5rem",fonts:{xxs:"0.625rem",xs:"0.75rem",sm:"0.875rem",md:"1rem",lg:"1.125rem",xl:"1.25rem",xxl:"1.5rem",xxxl:"2rem"},spacing:{xxs:"0.125rem",xs:"0.25rem",sm:"0.5rem",md:"0.75rem",lg:"1rem",xl:"1.5rem",xxl:"2rem",xxxl:"3rem"}},fonts:{body:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",heading:"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",mono:"'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace"},fontWeights:{normal:400,medium:500,semibold:600,bold:700},lineHeights:{tight:1.25,normal:1.5,relaxed:1.75},zIndicies:{loadingOverlay:9e3,dropdownMenu:8e3,dialog:7e3,popover:6e3,tooltip:5e3,sticky:1e3},shadows:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",default:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},transitions:{default:"all 0.2s ease-in-out",fast:"all 0.1s ease-in-out",slow:"all 0.3s ease-in-out"},breakpoints:{xs:"400px",sm:"600px",md:"900px",lg:"1280px",xl:"1440px",xxl:"1920px"},colors:{error:"#E74C3C",success:"#27AE60",warning:"#F1C40F",info:"#3498DB",white:"#FFFFFF",black:"#000000",transparent:"transparent"}},ie={...D,colors:{...D.colors,background:"hsl(0 0% 100%)",foreground:"hsl(0 0% 3.9%)",contentBg:"hsl(0 0% 100%)",card:"hsl(0 0% 100%)",cardForeground:"hsl(0 0% 3.9%)",popover:"hsl(0 0% 100%)",popoverForeground:"hsl(0 0% 3.9%)",primary:"hsl(0 0% 9%)",primaryForeground:"hsl(0 0% 98%)",secondary:"hsl(0 0% 96.1%)",secondaryForeground:"hsl(0 0% 9%)",muted:"hsl(0 0% 96.1%)",mutedForeground:"hsl(0 0% 45.1%)",accent:"hsl(0 0% 96.1%)",accentForeground:"hsl(0 0% 9%)",destructive:"hsl(0 84.2% 60.2%)",destructiveForeground:"hsl(0 0% 98%)",border:"hsl(0 0% 89.8%)",input:"hsl(0 0% 89.8%)",ring:"hsl(0 0% 3.9%)"}},ye={...D,colors:{...D.colors,background:"hsl(0 0% 3.9%)",foreground:"hsl(0 0% 98%)",contentBg:"hsl(0 0% 3.9%)",card:"hsl(0 0% 3.9%)",cardForeground:"hsl(0 0% 98%)",popover:"hsl(0 0% 3.9%)",popoverForeground:"hsl(0 0% 98%)",primary:"hsl(0 0% 98%)",primaryForeground:"hsl(0 0% 9%)",secondary:"hsl(0 0% 14.9%)",secondaryForeground:"hsl(0 0% 98%)",muted:"hsl(0 0% 14.9%)",mutedForeground:"hsl(0 0% 63.9%)",accent:"hsl(0 0% 14.9%)",accentForeground:"hsl(0 0% 98%)",destructive:"hsl(0 62.8% 30.6%)",destructiveForeground:"hsl(0 0% 98%)",border:"hsl(0 0% 14.9%)",input:"hsl(0 0% 14.9%)",ring:"hsl(0 0% 83.1%)"}},Cn={xs:e=>m.css`
    @media (min-width: ${D.breakpoints.xs}) {
      ${e}
    }
  `,sm:e=>m.css`
    @media (min-width: ${D.breakpoints.sm}) {
      ${e}
    }
  `,md:e=>m.css`
    @media (min-width: ${D.breakpoints.md}) {
      ${e}
    }
  `,lg:e=>m.css`
    @media (min-width: ${D.breakpoints.lg}) {
      ${e}
    }
  `,xl:e=>m.css`
    @media (min-width: ${D.breakpoints.xl}) {
      ${e}
    }
  `,xxl:e=>m.css`
    @media (min-width: ${D.breakpoints.xxl}) {
      ${e}
    }
  `},wn=D.breakpoints,W={LIGHT:"light",DARK:"dark"},Sn=e=>{switch(e){case W.DARK:return ye;case W.LIGHT:default:return ie}},er=F.createContext(void 0);function En({children:e,defaultTheme:r=W.LIGHT,storageKey:n="dua-ui-theme",enableSystem:t=!0}){const[o,s]=F.useState(r);F.useEffect(()=>{const u=localStorage.getItem(n);if(u&&Object.values(W).includes(u))s(u);else if(t){const x=window.matchMedia("(prefers-color-scheme: dark)").matches?W.DARK:W.LIGHT;s(x)}},[n,t]);const i=u=>{s(u),localStorage.setItem(n,u)},f=()=>{const u=o===W.LIGHT?W.DARK:W.LIGHT;i(u)},d=o===W.DARK?ye:ie,l={theme:o,setTheme:i,toggleTheme:f};return L(er.Provider,{value:l,children:L(m.ThemeProvider,{theme:d,children:e})})}function Tn(){const e=F.useContext(er);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}y.Badge=qe,y.Button=Be,y.Card=Ue,y.CardContent=Xe,y.CardDescription=Qe,y.CardFooter=Je,y.CardHeader=Ke,y.CardTitle=Ze,y.Input=Ge,y.THEME_NAMES=W,y.ThemeProvider=En,y.base=D,y.darkTheme=ye,y.defaultTheme=ie,y.device=wn,y.getTheme=Sn,y.lightTheme=ie,y.media=Cn,y.useTheme=Tn,Object.defineProperty(y,Symbol.toStringTag,{value:"Module"})});
//# sourceMappingURL=index.umd.js.map
