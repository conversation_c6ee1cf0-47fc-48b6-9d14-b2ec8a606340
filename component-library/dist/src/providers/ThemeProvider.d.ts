import type { default as React } from "react";
import type { ThemeName } from "../theme";
interface ThemeContextType {
	theme: ThemeName;
	setTheme: (theme: ThemeName) => void;
	toggleTheme: () => void;
}
export interface ThemeProviderProps {
	children: React.ReactNode;
	defaultTheme?: ThemeName;
	storageKey?: string;
	enableSystem?: boolean;
}
export declare function ThemeProvider({
	children,
	defaultTheme,
	storageKey,
	enableSystem,
}: ThemeProviderProps): import("react/jsx-runtime").JSX.Element;
export declare function useTheme(): ThemeContextType;
