export declare const media: {
	xs: (styles: string) => import("styled-components").RuleSet<object>;
	sm: (styles: string) => import("styled-components").RuleSet<object>;
	md: (styles: string) => import("styled-components").RuleSet<object>;
	lg: (styles: string) => import("styled-components").RuleSet<object>;
	xl: (styles: string) => import("styled-components").RuleSet<object>;
	xxl: (styles: string) => import("styled-components").RuleSet<object>;
};
export declare const device: {
	xs: string;
	sm: string;
	md: string;
	lg: string;
	xl: string;
	xxl: string;
};
