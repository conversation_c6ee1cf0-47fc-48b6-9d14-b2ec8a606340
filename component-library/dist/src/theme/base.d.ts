export declare const base: {
	sizes: {
		borderRadius: string;
		formControl: string;
		fonts: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
		spacing: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
	};
	fonts: {
		body: string;
		heading: string;
		mono: string;
	};
	fontWeights: {
		normal: number;
		medium: number;
		semibold: number;
		bold: number;
	};
	lineHeights: {
		tight: number;
		normal: number;
		relaxed: number;
	};
	zIndicies: {
		loadingOverlay: number;
		dropdownMenu: number;
		dialog: number;
		popover: number;
		tooltip: number;
		sticky: number;
	};
	shadows: {
		sm: string;
		default: string;
		md: string;
		lg: string;
		xl: string;
		inner: string;
	};
	transitions: {
		default: string;
		fast: string;
		slow: string;
	};
	breakpoints: {
		xs: string;
		sm: string;
		md: string;
		lg: string;
		xl: string;
		xxl: string;
	};
	colors: {
		error: string;
		success: string;
		warning: string;
		info: string;
		white: string;
		black: string;
		transparent: string;
	};
};
export type BaseTheme = typeof base;
