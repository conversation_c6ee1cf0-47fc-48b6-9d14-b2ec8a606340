import type { lightTheme as importedLightTheme } from "./light";
export { base } from "./base";
export { lightTheme } from "./light";
export { darkTheme } from "./dark";
export { media, device } from "./media";
export type { BaseTheme } from "./base";
export declare const defaultTheme: {
	sizes: {
		borderRadius: string;
		formControl: string;
		fonts: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
		spacing: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
	};
	fonts: {
		body: string;
		heading: string;
		mono: string;
	};
	fontWeights: {
		normal: number;
		medium: number;
		semibold: number;
		bold: number;
	};
	lineHeights: {
		tight: number;
		normal: number;
		relaxed: number;
	};
	zIndicies: {
		loadingOverlay: number;
		dropdownMenu: number;
		dialog: number;
		popover: number;
		tooltip: number;
		sticky: number;
	};
	shadows: {
		sm: string;
		default: string;
		md: string;
		lg: string;
		xl: string;
		inner: string;
	};
	transitions: {
		default: string;
		fast: string;
		slow: string;
	};
	breakpoints: {
		xs: string;
		sm: string;
		md: string;
		lg: string;
		xl: string;
		xxl: string;
	};
	colors: {
		error: string;
		success: string;
		warning: string;
		info: string;
		white: string;
		black: string;
		transparent: string;
	};
} & {
	colors: import("./base").BaseTheme["colors"] & {
		background: string;
		foreground: string;
		contentBg: string;
		card: string;
		cardForeground: string;
		popover: string;
		popoverForeground: string;
		primary: string;
		primaryForeground: string;
		secondary: string;
		secondaryForeground: string;
		muted: string;
		mutedForeground: string;
		accent: string;
		accentForeground: string;
		destructive: string;
		destructiveForeground: string;
		border: string;
		input: string;
		ring: string;
	};
};
export type Theme = typeof importedLightTheme;
export declare const THEME_NAMES: {
	readonly LIGHT: "light";
	readonly DARK: "dark";
};
export type ThemeName = (typeof THEME_NAMES)[keyof typeof THEME_NAMES];
export declare const getTheme: (themeName: ThemeName) => {
	sizes: {
		borderRadius: string;
		formControl: string;
		fonts: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
		spacing: {
			xxs: string;
			xs: string;
			sm: string;
			md: string;
			lg: string;
			xl: string;
			xxl: string;
			xxxl: string;
		};
	};
	fonts: {
		body: string;
		heading: string;
		mono: string;
	};
	fontWeights: {
		normal: number;
		medium: number;
		semibold: number;
		bold: number;
	};
	lineHeights: {
		tight: number;
		normal: number;
		relaxed: number;
	};
	zIndicies: {
		loadingOverlay: number;
		dropdownMenu: number;
		dialog: number;
		popover: number;
		tooltip: number;
		sticky: number;
	};
	shadows: {
		sm: string;
		default: string;
		md: string;
		lg: string;
		xl: string;
		inner: string;
	};
	transitions: {
		default: string;
		fast: string;
		slow: string;
	};
	breakpoints: {
		xs: string;
		sm: string;
		md: string;
		lg: string;
		xl: string;
		xxl: string;
	};
	colors: {
		error: string;
		success: string;
		warning: string;
		info: string;
		white: string;
		black: string;
		transparent: string;
	};
} & {
	colors: import("./base").BaseTheme["colors"] & {
		background: string;
		foreground: string;
		contentBg: string;
		card: string;
		cardForeground: string;
		popover: string;
		popoverForeground: string;
		primary: string;
		primaryForeground: string;
		secondary: string;
		secondaryForeground: string;
		muted: string;
		mutedForeground: string;
		accent: string;
		accentForeground: string;
		destructive: string;
		destructiveForeground: string;
		border: string;
		input: string;
		ring: string;
	};
};
