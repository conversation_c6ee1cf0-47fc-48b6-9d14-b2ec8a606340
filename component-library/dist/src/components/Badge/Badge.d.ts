import type { HTMLAttributes } from "react";
declare const variants: {
	default: import("styled-components").RuleSet<object>;
	secondary: import("styled-components").RuleSet<object>;
	destructive: import("styled-components").RuleSet<object>;
	outline: import("styled-components").RuleSet<object>;
};
export interface BadgeProps extends HTMLAttributes<HTMLDivElement> {
	variant?: keyof typeof variants;
}
declare const Badge: import("react").ForwardRefExoticComponent<
	BadgeProps & import("react").RefAttributes<HTMLDivElement>
>;
export { Badge };
