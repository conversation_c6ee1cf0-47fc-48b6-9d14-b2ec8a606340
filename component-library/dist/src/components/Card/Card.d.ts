import type { HTMLAttributes } from "react";
declare const sizes: {
	sm: import("styled-components").RuleSet<object>;
	md: import("styled-components").RuleSet<object>;
	lg: import("styled-components").RuleSet<object>;
};
export interface CardProps extends HTMLAttributes<HTMLDivElement> {
	size?: keyof typeof sizes;
}
declare const Card: import("react").ForwardRefExoticComponent<
	CardProps & import("react").RefAttributes<HTMLDivElement>
>;
export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {}
declare const CardHeader: import("react").ForwardRefExoticComponent<
	CardHeaderProps & import("react").RefAttributes<HTMLDivElement>
>;
export interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {}
declare const CardTitle: import("react").ForwardRefExoticComponent<
	CardTitleProps & import("react").RefAttributes<HTMLHeadingElement>
>;
export interface CardDescriptionProps
	extends HTMLAttributes<HTMLParagraphElement> {}
declare const CardDescription: import("react").ForwardRefExoticComponent<
	CardDescriptionProps & import("react").RefAttributes<HTMLParagraphElement>
>;
export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {}
declare const CardContent: import("react").ForwardRefExoticComponent<
	CardContentProps & import("react").RefAttributes<HTMLDivElement>
>;
export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {}
declare const CardFooter: import("react").ForwardRefExoticComponent<
	CardFooterProps & import("react").RefAttributes<HTMLDivElement>
>;
export {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
	CardFooter,
};
