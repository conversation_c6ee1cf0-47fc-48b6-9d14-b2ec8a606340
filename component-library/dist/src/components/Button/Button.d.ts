import type { ButtonHTMLAttributes } from "react";
declare const variants: {
	default: import("styled-components").RuleSet<object>;
	destructive: import("styled-components").RuleSet<object>;
	outline: import("styled-components").RuleSet<object>;
	secondary: import("styled-components").RuleSet<object>;
	ghost: import("styled-components").RuleSet<object>;
	link: import("styled-components").RuleSet<object>;
};
declare const sizes: {
	default: import("styled-components").RuleSet<object>;
	sm: import("styled-components").RuleSet<object>;
	lg: import("styled-components").RuleSet<object>;
	icon: import("styled-components").RuleSet<object>;
};
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
	variant?: keyof typeof variants;
	size?: keyof typeof sizes;
	asChild?: boolean;
}
declare const Button: import("react").ForwardRefExoticComponent<
	ButtonProps & import("react").RefAttributes<HTMLButtonElement>
>;
export { Button };
