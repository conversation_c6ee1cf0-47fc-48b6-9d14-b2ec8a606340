🩺 The doctor is checking the health of your Storybook..
╭ Incompatible packages found ────────────────────────────────────────────────────────────────────────╮
│                                                                                                     │
│   You are currently using Storybook 9.0.3 but you have packages which are incompatible with it:     │
│   - @storybook/blocks@8.6.14 which depends on ^8.6.14                                               │
│    Repo: https://github.com/storybookjs/storybook/tree/next/code/lib/blocks                         │
│   - @storybook/test@8.6.14 which depends on 8.6.14                                                  │
│    Repo: https://github.com/storybookjs/storybook/tree/next/code/lib/test                           │
│                                                                                                     │
│                                                                                                     │
│   Please consider updating your packages or contacting the maintainers for compatibility details.   │
│   For more on Storybook 9 compatibility, see the linked GitHub issue:                               │
│   https://github.com/storybookjs/storybook/issues/30944                                             │
│                                                                                                     │
╰─────────────────────────────────────────────────────────────────────────────────────────────────────╯

You can always recheck the health of your project by running:
npx storybook doctor

Full logs are available in /Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/component-library/doctor-storybook.log

