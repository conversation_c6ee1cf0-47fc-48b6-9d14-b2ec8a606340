# DUA Component Library

A modern React component library built with styled-components, inspired by the GACO Library architecture. This library provides a comprehensive design system with reusable components, theming support, and Storybook documentation.

## Features

- 🎨 **Modern Design System**: Comprehensive theme system with light/dark modes
- ⚡ **TypeScript First**: Full TypeScript support with comprehensive type definitions
- 💅 **Styled Components**: CSS-in-JS styling with theme integration
- 📚 **Storybook Integration**: Interactive component documentation
- 🔧 **Tree Shaking**: Optimized for dead code elimination
- ♿ **Accessibility**: ARIA attributes and semantic HTML
- 📱 **Responsive**: Mobile-first design approach

## Installation

```bash
pnpm add dua-component-library styled-components
# or
yarn add dua-component-library styled-components
# or
npm install dua-component-library styled-components
```

## Quick Start

```tsx
import React from 'react';
import { 
  ThemeProvider, 
  Button, 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent 
} from 'dua-component-library';

function App() {
  return (
    <ThemeProvider defaultTheme="light">
      <Card>
        <CardHeader>
          <CardTitle>Welcome to DUA Components</CardTitle>
        </CardHeader>
        <CardContent>
          <Button>Get Started</Button>
        </CardContent>
      </Card>
    </ThemeProvider>
  );
}
```

## Components

### Atoms
- **Button**: Versatile button component with multiple variants and sizes
- **Input**: Form input with validation states and accessibility features
- **Badge**: Status and label indicators

### Molecules  
- **Card**: Flexible container with header, content, and footer slots

## Theming

The library includes a comprehensive theming system with:

- Light and dark themes out of the box
- Responsive breakpoints
- Design tokens for consistent spacing, typography, and colors
- Easy theme customization

```tsx
import { ThemeProvider, useTheme } from 'dua-component-library';

function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <Button onClick={toggleTheme}>
      Switch to {theme === 'light' ? 'dark' : 'light'} mode
    </Button>
  );
}
```

## Development

```bash
# Install dependencies
pnpm install

# Start Storybook development server
pnpm storybook

# Build the library
pnpm build

# Run tests
pnpm test

# Type checking
pnpm check:types
```

## Architecture

This library follows the GACO Library architecture pattern:

- **Component Structure**: Each component has its own folder with component, stories, and index files
- **Theme System**: Centralized design tokens with light/dark variants
- **Styled Components**: CSS-in-JS with theme integration
- **TypeScript**: Comprehensive type definitions for better DX
- **Storybook**: Interactive documentation and testing

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

MIT License - see the [LICENSE](LICENSE) file for details.
