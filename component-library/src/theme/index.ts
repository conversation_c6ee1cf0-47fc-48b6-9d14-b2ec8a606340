import { darkTheme as importedDarkTheme } from "./dark";
import { lightTheme as importedLightTheme } from "./light";

export { base } from "./base";
export { lightTheme } from "./light";
export { darkTheme } from "./dark";
export { media, device } from "./media";

export type { BaseTheme } from "./base";

// Default theme (light)
export const defaultTheme = importedLightTheme;

// Theme type for styled-components
export type Theme = typeof importedLightTheme;

// Theme names
export const THEME_NAMES = {
	LIGHT: "light",
	DARK: "dark",
} as const;

export type ThemeName = (typeof THEME_NAMES)[keyof typeof THEME_NAMES];

// Theme selection helper
export const getTheme = (themeName: ThemeName) => {
	switch (themeName) {
		case THEME_NAMES.DARK:
			return importedDarkTheme;
		case THEME_NAMES.LIGHT:
		default:
			return importedLightTheme;
	}
};
