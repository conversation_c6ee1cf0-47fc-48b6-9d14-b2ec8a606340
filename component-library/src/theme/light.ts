import { type BaseTheme, base } from "./base";

// Light theme extending base theme
export const lightTheme: BaseTheme & {
	colors: BaseTheme["colors"] & {
		// Background colors
		background: string;
		foreground: string;
		contentBg: string;

		// UI colors
		card: string;
		cardForeground: string;
		popover: string;
		popoverForeground: string;

		// Brand colors
		primary: string;
		primaryForeground: string;
		secondary: string;
		secondaryForeground: string;

		// Utility colors
		muted: string;
		mutedForeground: string;
		accent: string;
		accentForeground: string;
		destructive: string;
		destructiveForeground: string;

		// Form colors
		border: string;
		input: string;
		ring: string;
	};
} = {
	...base,
	colors: {
		...base.colors,
		// Background colors
		background: "hsl(0 0% 100%)",
		foreground: "hsl(0 0% 3.9%)",
		contentBg: "hsl(0 0% 100%)",

		// UI colors
		card: "hsl(0 0% 100%)",
		cardForeground: "hsl(0 0% 3.9%)",
		popover: "hsl(0 0% 100%)",
		popoverForeground: "hsl(0 0% 3.9%)",

		// Brand colors
		primary: "hsl(0 0% 9%)",
		primaryForeground: "hsl(0 0% 98%)",
		secondary: "hsl(0 0% 96.1%)",
		secondaryForeground: "hsl(0 0% 9%)",

		// Utility colors
		muted: "hsl(0 0% 96.1%)",
		mutedForeground: "hsl(0 0% 45.1%)",
		accent: "hsl(0 0% 96.1%)",
		accentForeground: "hsl(0 0% 9%)",
		destructive: "hsl(0 84.2% 60.2%)",
		destructiveForeground: "hsl(0 0% 98%)",

		// Form colors
		border: "hsl(0 0% 89.8%)",
		input: "hsl(0 0% 89.8%)",
		ring: "hsl(0 0% 3.9%)",
	},
};
