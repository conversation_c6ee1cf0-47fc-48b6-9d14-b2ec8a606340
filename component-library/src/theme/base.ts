// Base theme values and design tokens
export const base = {
	sizes: {
		borderRadius: "0.5rem",
		formControl: "2.5rem",
		fonts: {
			xxs: "0.625rem", // 10px
			xs: "0.75rem", // 12px
			sm: "0.875rem", // 14px
			md: "1rem", // 16px
			lg: "1.125rem", // 18px
			xl: "1.25rem", // 20px
			xxl: "1.5rem", // 24px
			xxxl: "2rem", // 32px
		},
		spacing: {
			xxs: "0.125rem", // 2px
			xs: "0.25rem", // 4px
			sm: "0.5rem", // 8px
			md: "0.75rem", // 12px
			lg: "1rem", // 16px
			xl: "1.5rem", // 24px
			xxl: "2rem", // 32px
			xxxl: "3rem", // 48px
		},
	},
	fonts: {
		body: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
		heading:
			"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
		mono: "'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace",
	},
	fontWeights: {
		normal: 400,
		medium: 500,
		semibold: 600,
		bold: 700,
	},
	lineHeights: {
		tight: 1.25,
		normal: 1.5,
		relaxed: 1.75,
	},
	zIndicies: {
		loadingOverlay: 9000,
		dropdownMenu: 8000,
		dialog: 7000,
		popover: 6000,
		tooltip: 5000,
		sticky: 1000,
	},
	shadows: {
		sm: "0 1px 2px 0 rgb(0 0 0 / 0.05)",
		default: "0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",
		md: "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
		lg: "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
		xl: "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
		inner: "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
	},
	transitions: {
		default: "all 0.2s ease-in-out",
		fast: "all 0.1s ease-in-out",
		slow: "all 0.3s ease-in-out",
	},
	breakpoints: {
		xs: "400px",
		sm: "600px",
		md: "900px",
		lg: "1280px",
		xl: "1440px",
		xxl: "1920px",
	},
	colors: {
		// Semantic colors
		error: "#E74C3C",
		success: "#27AE60",
		warning: "#F1C40F",
		info: "#3498DB",
		// Neutral grays
		white: "#FFFFFF",
		black: "#000000",
		transparent: "transparent",
	},
};

export type BaseTheme = typeof base;
