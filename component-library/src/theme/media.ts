import { css } from "styled-components";
import { base } from "./base";

// Media query helpers
export const media = {
	xs: (styles: string) => css`
    @media (min-width: ${base.breakpoints.xs}) {
      ${styles}
    }
  `,
	sm: (styles: string) => css`
    @media (min-width: ${base.breakpoints.sm}) {
      ${styles}
    }
  `,
	md: (styles: string) => css`
    @media (min-width: ${base.breakpoints.md}) {
      ${styles}
    }
  `,
	lg: (styles: string) => css`
    @media (min-width: ${base.breakpoints.lg}) {
      ${styles}
    }
  `,
	xl: (styles: string) => css`
    @media (min-width: ${base.breakpoints.xl}) {
      ${styles}
    }
  `,
	xxl: (styles: string) => css`
    @media (min-width: ${base.breakpoints.xxl}) {
      ${styles}
    }
  `,
};

// Device size utilities
export const device = base.breakpoints;
