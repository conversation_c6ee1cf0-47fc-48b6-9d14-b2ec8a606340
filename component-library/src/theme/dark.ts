import { type BaseTheme, base } from "./base";

// Dark theme extending base theme
export const darkTheme: BaseTheme & {
	colors: BaseTheme["colors"] & {
		// Background colors
		background: string;
		foreground: string;
		contentBg: string;

		// UI colors
		card: string;
		cardForeground: string;
		popover: string;
		popoverForeground: string;

		// Brand colors
		primary: string;
		primaryForeground: string;
		secondary: string;
		secondaryForeground: string;

		// Utility colors
		muted: string;
		mutedForeground: string;
		accent: string;
		accentForeground: string;
		destructive: string;
		destructiveForeground: string;

		// Form colors
		border: string;
		input: string;
		ring: string;
	};
} = {
	...base,
	colors: {
		...base.colors,
		// Background colors
		background: "hsl(0 0% 3.9%)",
		foreground: "hsl(0 0% 98%)",
		contentBg: "hsl(0 0% 3.9%)",

		// UI colors
		card: "hsl(0 0% 3.9%)",
		cardForeground: "hsl(0 0% 98%)",
		popover: "hsl(0 0% 3.9%)",
		popoverForeground: "hsl(0 0% 98%)",

		// Brand colors
		primary: "hsl(0 0% 98%)",
		primaryForeground: "hsl(0 0% 9%)",
		secondary: "hsl(0 0% 14.9%)",
		secondaryForeground: "hsl(0 0% 98%)",

		// Utility colors
		muted: "hsl(0 0% 14.9%)",
		mutedForeground: "hsl(0 0% 63.9%)",
		accent: "hsl(0 0% 14.9%)",
		accentForeground: "hsl(0 0% 98%)",
		destructive: "hsl(0 62.8% 30.6%)",
		destructiveForeground: "hsl(0 0% 98%)",

		// Form colors
		border: "hsl(0 0% 14.9%)",
		input: "hsl(0 0% 14.9%)",
		ring: "hsl(0 0% 83.1%)",
	},
};
