import { Slot } from "@radix-ui/react-slot";
import { type ButtonHTMLAttributes, forwardRef } from "react";
import styled, { css } from "styled-components";

// Button variants as CSS objects
const variants = {
	default: css`
    background-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.primary}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
	destructive: css`
    background-color: ${({ theme }) => theme.colors.destructive};
    color: ${({ theme }) => theme.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.destructive}dd;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
	outline: css`
    background-color: ${({ theme }) => theme.colors.background};
    color: ${({ theme }) => theme.colors.foreground};
    border: 1px solid ${({ theme }) => theme.colors.border};
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.accent};
      color: ${({ theme }) => theme.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
	secondary: css`
    background-color: ${({ theme }) => theme.colors.secondary};
    color: ${({ theme }) => theme.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.secondary}cc;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
	ghost: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.foreground};
    border: 1px solid transparent;
    
    &:hover:not(:disabled) {
      background-color: ${({ theme }) => theme.colors.accent};
      color: ${({ theme }) => theme.colors.accentForeground};
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
	link: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.primary};
    border: 1px solid transparent;
    text-decoration: underline;
    text-underline-offset: 4px;
    
    &:hover:not(:disabled) {
      text-decoration: none;
    }
    
    &:focus-visible {
      outline: none;
      box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    }
  `,
};

// Button sizes as CSS objects
const sizes = {
	default: css`
    height: 2.5rem;
    padding: 0.5rem 1rem;
    font-size: ${({ theme }) => theme.sizes.fonts.sm};
  `,
	sm: css`
    height: 2.25rem;
    padding: 0.5rem 0.75rem;
    font-size: ${({ theme }) => theme.sizes.fonts.sm};
    border-radius: calc(${({ theme }) => theme.sizes.borderRadius} - 2px);
  `,
	lg: css`
    height: 2.75rem;
    padding: 0.5rem 2rem;
    font-size: ${({ theme }) => theme.sizes.fonts.md};
    border-radius: calc(${({ theme }) => theme.sizes.borderRadius} - 2px);
  `,
	icon: css`
    height: 2.5rem;
    width: 2.5rem;
    padding: 0;
  `,
};

// Styled button component
const StyledButton = styled.button<ButtonProps>`
  /* Base styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: ${({ theme }) => theme.sizes.borderRadius};
  font-weight: ${({ theme }) => theme.fontWeights.medium};
  transition: ${({ theme }) => theme.transitions.default};
  cursor: pointer;
  user-select: none;
  
  /* SVG styles */
  & svg {
    pointer-events: none;
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
  }
  
  /* Disabled state */
  &:disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Variant styles */
  ${({ variant = "default" }) => variants[variant]}
  
  /* Size styles */
  ${({ size = "default" }) => sizes[size]}
`;

// Component interface
export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
	variant?: keyof typeof variants;
	size?: keyof typeof sizes;
	asChild?: boolean;
}

// ForwardRef component
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
	({ className, variant, size, asChild = false, ...props }, ref) => {
		const Comp = asChild ? Slot : StyledButton;
		return (
			<Comp
				className={className}
				variant={variant}
				size={size}
				ref={ref}
				{...props}
			/>
		);
	},
);

Button.displayName = "Button";

export { Button };
