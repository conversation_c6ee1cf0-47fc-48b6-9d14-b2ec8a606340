import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { But<PERSON> } from "../Button";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "./Card";

const meta: Meta<typeof Card> = {
	title: "Molecules/Card",
	component: Card,
	parameters: {
		layout: "centered",
	},
	decorators: [
		(Story) => (
			<div style={{ width: "400px", padding: "20px" }}>
				<Story />
			</div>
		),
	],
};

export default meta;
type Story = StoryObj<typeof Card>;

export const Default: Story = {
	render: () => (
		<Card>
			<CardHeader>
				<CardTitle>Card Title</CardTitle>
				<CardDescription>
					Card description goes here. This is a brief explanation of what this
					card contains.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<p>
					This is the main content of the card. You can put any content here.
				</p>
			</CardContent>
			<CardFooter>
				<Button variant="outline">Cancel</Button>
				<Button>Action</Button>
			</CardFooter>
		</Card>
	),
};

export const Simple: Story = {
	render: () => (
		<Card>
			<CardContent>
				<p>A simple card with just content.</p>
			</CardContent>
		</Card>
	),
};

export const WithoutFooter: Story = {
	render: () => (
		<Card>
			<CardHeader>
				<CardTitle>Without Footer</CardTitle>
				<CardDescription>
					This card doesn't have a footer section.
				</CardDescription>
			</CardHeader>
			<CardContent>
				<p>Main content goes here without any footer actions.</p>
			</CardContent>
		</Card>
	),
};
