import { type HTMLAttributes, forwardRef } from "react";
import styled, { css } from "styled-components";

// Card size variants
const sizes = {
	sm: css`
    padding: ${({ theme }) => theme.sizes.spacing.lg};
  `,
	md: css`
    padding: ${({ theme }) => theme.sizes.spacing.xl};
  `,
	lg: css`
    padding: ${({ theme }) => theme.sizes.spacing.xxl};
  `,
};

// Base Card Component
const StyledCard = styled.div<CardProps>`
  border-radius: ${({ theme }) => theme.sizes.borderRadius};
  border: 1px solid ${({ theme }) => theme.colors.border};
  background-color: ${({ theme }) => theme.colors.card};
  color: ${({ theme }) => theme.colors.cardForeground};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  transition: ${({ theme }) => theme.transitions.default};
  
  ${({ size = "md" }) => sizes[size]}
`;

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
	size?: keyof typeof sizes;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
	({ size = "md", ...props }, ref) => {
		return <StyledCard ref={ref} size={size} {...props} />;
	},
);

Card.displayName = "Card";

// Card Header Component
const StyledCardHeader = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.sizes.spacing.sm};
  margin-bottom: ${({ theme }) => theme.sizes.spacing.xl};
`;

export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {}

const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>((props, ref) => {
	return <StyledCardHeader ref={ref} {...props} />;
});

CardHeader.displayName = "CardHeader";

// Card Title Component
const StyledCardTitle = styled.h3`
  font-size: ${({ theme }) => theme.sizes.fonts.xl};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  line-height: ${({ theme }) => theme.lineHeights.tight};
  letter-spacing: -0.025em;
  margin: 0;
`;

export interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {}

const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
	(props, ref) => {
		return <StyledCardTitle ref={ref} {...props} />;
	},
);

CardTitle.displayName = "CardTitle";

// Card Description Component
const StyledCardDescription = styled.p`
  font-size: ${({ theme }) => theme.sizes.fonts.sm};
  color: ${({ theme }) => theme.colors.mutedForeground};
  line-height: ${({ theme }) => theme.lineHeights.normal};
  margin: 0;
`;

export interface CardDescriptionProps
	extends HTMLAttributes<HTMLParagraphElement> {}

const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
	(props, ref) => {
		return <StyledCardDescription ref={ref} {...props} />;
	},
);

CardDescription.displayName = "CardDescription";

// Card Content Component
const StyledCardContent = styled.div`
  /* Content has no default padding as it's handled by the card itself */
`;

export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {}

const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
	(props, ref) => {
		return <StyledCardContent ref={ref} {...props} />;
	},
);

CardContent.displayName = "CardContent";

// Card Footer Component
const StyledCardFooter = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.sizes.spacing.md};
  margin-top: ${({ theme }) => theme.sizes.spacing.xl};
`;

export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {}

const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>((props, ref) => {
	return <StyledCardFooter ref={ref} {...props} />;
});

CardFooter.displayName = "CardFooter";

export {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
	CardFooter,
};
