import { type HTMLAttributes, forwardRef } from "react";
import styled, { css } from "styled-components";

// Badge variants as CSS objects
const variants = {
	default: css`
    background-color: ${({ theme }) => theme.colors.primary};
    color: ${({ theme }) => theme.colors.primaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme }) => theme.colors.primary}cc;
    }
  `,
	secondary: css`
    background-color: ${({ theme }) => theme.colors.secondary};
    color: ${({ theme }) => theme.colors.secondaryForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme }) => theme.colors.secondary}cc;
    }
  `,
	destructive: css`
    background-color: ${({ theme }) => theme.colors.destructive};
    color: ${({ theme }) => theme.colors.destructiveForeground};
    border: 1px solid transparent;
    
    &:hover {
      background-color: ${({ theme }) => theme.colors.destructive}cc;
    }
  `,
	outline: css`
    background-color: transparent;
    color: ${({ theme }) => theme.colors.foreground};
    border: 1px solid ${({ theme }) => theme.colors.border};
  `,
};

// Styled Badge component
const StyledBadge = styled.div<BadgeProps>`
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: ${({ theme }) => theme.sizes.fonts.xs};
  font-weight: ${({ theme }) => theme.fontWeights.semibold};
  transition: ${({ theme }) => theme.transitions.default};
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
  }
  
  /* Variant styles */
  ${({ variant = "default" }) => variants[variant]}
`;

export interface BadgeProps extends HTMLAttributes<HTMLDivElement> {
	variant?: keyof typeof variants;
}

const Badge = forwardRef<HTMLDivElement, BadgeProps>(
	({ variant = "default", ...props }, ref) => {
		return <StyledBadge ref={ref} variant={variant} {...props} />;
	},
);

Badge.displayName = "Badge";

export { Badge };
