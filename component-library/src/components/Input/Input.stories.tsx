import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Input } from "./Input";

const meta: Meta<typeof Input> = {
	title: "Atoms/Input",
	component: Input,
	parameters: {
		layout: "centered",
	},
	decorators: [
		(Story) => (
			<div style={{ width: "300px" }}>
				<Story />
			</div>
		),
	],
	argTypes: {
		type: {
			control: { type: "select" },
			options: ["text", "email", "password", "number", "tel", "url"],
		},
		disabled: {
			control: { type: "boolean" },
		},
		placeholder: {
			control: { type: "text" },
		},
	},
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
	args: {
		placeholder: "Enter your text...",
	},
};

export const Email: Story = {
	args: {
		type: "email",
		placeholder: "Enter your email...",
	},
};

export const Password: Story = {
	args: {
		type: "password",
		placeholder: "Enter your password...",
	},
};

export const Disabled: Story = {
	args: {
		placeholder: "Disabled input",
		disabled: true,
	},
};

export const WithValue: Story = {
	args: {
		value: "Pre-filled value",
		readOnly: true,
	},
};
