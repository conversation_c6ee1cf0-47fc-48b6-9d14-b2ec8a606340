import { type InputHTMLAttributes, forwardRef } from "react";
import styled from "styled-components";

// Styled Input component
const StyledInput = styled.input`
  display: flex;
  height: ${({ theme }) => theme.sizes.formControl};
  width: 100%;
  border-radius: ${({ theme }) => theme.sizes.borderRadius};
  border: 1px solid ${({ theme }) => theme.colors.input};
  background-color: ${({ theme }) => theme.colors.background};
  padding: 0.5rem 0.75rem;
  font-size: ${({ theme }) => theme.sizes.fonts.md};
  color: ${({ theme }) => theme.colors.foreground};
  transition: ${({ theme }) => theme.transitions.default};
  
  /* File input styles */
  &[type="file"] {
    border: 0;
    background-color: transparent;
    font-size: ${({ theme }) => theme.sizes.fonts.sm};
    font-weight: ${({ theme }) => theme.fontWeights.medium};
    
    &::file-selector-button {
      border: 0;
      background-color: transparent;
      font-size: ${({ theme }) => theme.sizes.fonts.sm};
      font-weight: ${({ theme }) => theme.fontWeights.medium};
      color: ${({ theme }) => theme.colors.foreground};
      margin-right: 0.5rem;
    }
  }
  
  /* Placeholder styles */
  &::placeholder {
    color: ${({ theme }) => theme.colors.mutedForeground};
  }
  
  /* Focus styles */
  &:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px ${({ theme }) => theme.colors.ring};
    border-color: ${({ theme }) => theme.colors.ring};
  }
  
  /* Disabled styles */
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  /* Responsive font size */
  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.sizes.fonts.md};
  }
  
  @media (min-width: ${({ theme }) => theme.breakpoints.md}) {
    font-size: ${({ theme }) => theme.sizes.fonts.sm};
  }
`;

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {}

const Input = forwardRef<HTMLInputElement, InputProps>(
	({ type = "text", ...props }, ref) => {
		return <StyledInput ref={ref} type={type} {...props} />;
	},
);

Input.displayName = "Input";

export { Input };
