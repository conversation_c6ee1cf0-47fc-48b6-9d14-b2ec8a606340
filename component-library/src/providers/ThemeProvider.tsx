import type React from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { ThemeProvider as StyledThemeProvider } from "styled-components";
import {
	THEME_NAMES,
	type Theme,
	type ThemeName,
	darkTheme,
	lightTheme,
} from "../theme";

interface ThemeContextType {
	theme: ThemeName;
	setTheme: (theme: ThemeName) => void;
	toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export interface ThemeProviderProps {
	children: React.ReactNode;
	defaultTheme?: ThemeName;
	storageKey?: string;
	enableSystem?: boolean;
}

export function ThemeProvider({
	children,
	defaultTheme = THEME_NAMES.LIGHT,
	storageKey = "dua-ui-theme",
	enableSystem = true,
}: ThemeProviderProps) {
	const [theme, setThemeState] = useState<ThemeName>(defaultTheme);

	useEffect(() => {
		const stored = localStorage.getItem(storageKey) as ThemeName;
		if (stored && Object.values(THEME_NAMES).includes(stored)) {
			setThemeState(stored);
		} else if (enableSystem) {
			const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
				.matches
				? THEME_NAMES.DARK
				: THEME_NAMES.LIGHT;
			setThemeState(systemTheme);
		}
	}, [storageKey, enableSystem]);

	const setTheme = (newTheme: ThemeName) => {
		setThemeState(newTheme);
		localStorage.setItem(storageKey, newTheme);
	};

	const toggleTheme = () => {
		const newTheme =
			theme === THEME_NAMES.LIGHT ? THEME_NAMES.DARK : THEME_NAMES.LIGHT;
		setTheme(newTheme);
	};

	const themeObject: Theme =
		theme === THEME_NAMES.DARK ? darkTheme : lightTheme;

	const contextValue: ThemeContextType = {
		theme,
		setTheme,
		toggleTheme,
	};

	return (
		<ThemeContext.Provider value={contextValue}>
			<StyledThemeProvider theme={themeObject}>{children}</StyledThemeProvider>
		</ThemeContext.Provider>
	);
}

export function useTheme() {
	const context = useContext(ThemeContext);
	if (context === undefined) {
		throw new Error("useTheme must be used within a ThemeProvider");
	}
	return context;
}
