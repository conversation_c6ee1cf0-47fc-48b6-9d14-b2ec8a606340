#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules/storybook/bin/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules/storybook/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules/storybook/bin/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules/storybook/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/storybook@8.6.14/node_modules:/Users/<USER>/Documents/.projects/fe-experiment/dua_fe_prototype_1/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../storybook/bin/index.cjs" "$@"
else
  exec node  "$basedir/../storybook/bin/index.cjs" "$@"
fi
