{"key": "lastEvents", "content": {"boot": {"body": {"eventType": "boot", "eventId": "bNWpsz00TRCJgSGMI1Qg7", "sessionId": "s13A2i01GVR305xLyKesZ", "payload": {"eventType": "dev"}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1748807944536}, "version-update": {"body": {"eventType": "version-update", "eventId": "v_C-p5RtVUgM1ioxTh5aB", "sessionId": "s13A2i01GVR305xLyKesZ", "metadata": {"generatedAt": 1748802727800, "userSince": 1748799805816, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"vitest": "2.1.9"}, "hasRouterPackage": false, "packageManager": {"type": "pnpm", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.6.14", "language": "typescript", "storybookPackages": {"@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-onboarding": {"version": "8.6.14"}, "@storybook/blocks": {"version": "8.6.14"}, "@storybook/react": {"version": "8.6.14"}, "@storybook/react-vite": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "eslint-plugin-storybook": {"version": "0.10.2"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-essentials": {"version": "8.6.14"}}}, "payload": {}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1748802728299}, "dev": {"body": {"eventType": "dev", "eventId": "eW4gfuD7TbfDlr2FLn2Pc", "sessionId": "s13A2i01GVR305xLyKesZ", "metadata": {"generatedAt": 1748807952632, "userSince": 1748799805816, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"vitest": "2.1.9"}, "hasRouterPackage": false, "packageManager": {"type": "pnpm", "agent": "pnpm"}, "typescriptOptions": {"check": false, "reactDocgen": "react-docgen-typescript", "reactDocgenTypescriptOptions": {"shouldExtractLiteralValuesFromEnum": true}}, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "storybookVersion": "8.6.14", "storybookVersionSpecifier": "^8.6.14", "language": "typescript", "storybookPackages": {"@storybook/addon-interactions": {"version": "8.6.14"}, "@storybook/addon-links": {"version": "8.6.14"}, "@storybook/addon-onboarding": {"version": "8.6.14"}, "@storybook/blocks": {"version": "8.6.14"}, "@storybook/react": {"version": "8.6.14"}, "@storybook/react-vite": {"version": "8.6.14"}, "@storybook/test": {"version": "8.6.14"}, "eslint-plugin-storybook": {"version": "0.10.2"}, "storybook": {"version": "8.6.14"}}, "addons": {"@storybook/addon-essentials": {"version": "8.6.14"}}}, "payload": {"versionStatus": "cached", "storyIndex": {"storyCount": 21, "componentCount": 4, "pageStoryCount": 0, "playStoryCount": 0, "autodocsCount": 0, "mdxCount": 0, "exampleStoryCount": 0, "exampleDocsCount": 0, "onboardingStoryCount": 0, "onboardingDocsCount": 0, "version": 5}, "storyStats": {"factory": 0, "play": 0, "render": 3, "loaders": 0, "beforeEach": 0, "globals": 0, "tags": 0, "storyFn": 0, "mount": 0, "moduleMock": 0}}, "context": {"inCI": false, "isTTY": true, "platform": "macOS", "nodeVersion": "20.17.0", "cliVersion": "8.6.14"}}, "timestamp": 1748807953422}}}