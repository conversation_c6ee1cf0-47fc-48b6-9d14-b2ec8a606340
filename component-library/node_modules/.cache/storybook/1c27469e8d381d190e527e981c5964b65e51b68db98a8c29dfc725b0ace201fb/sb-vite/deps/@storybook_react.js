import "./chunk-2VLGG4VU.js";
import {
  L
} from "./chunk-74HTNI4C.js";
import {
  entry_preview_docs_exports
} from "./chunk-KSGZU3K4.js";
import "./chunk-OQJ5TUTM.js";
import "./chunk-F2TZRQKA.js";
import {
  entry_preview_exports,
  renderToCanvas
} from "./chunk-YJBJFSU3.js";
import "./chunk-N3Y3TPJK.js";
import {
  require_react
} from "./chunk-BZ4VO6P4.js";
import "./chunk-KQ2JNRIV.js";
import {
  require_preview_api
} from "./chunk-6GFF2EK4.js";
import {
  require_global
} from "./chunk-DXIOBCSA.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../node_modules/.pnpm/@storybook+react@8.6.14_@storybook+test@8.6.14_storybook@8.6.14__react-dom@19.1.0_react_c6bbd3c434101a7e8fca31d540f5dc4d/node_modules/@storybook/react/dist/chunk-ZGTCCPPZ.mjs
function __definePreview(preview) {
  return L({ ...preview, addons: [entry_preview_exports, entry_preview_docs_exports, ...preview.addons ?? []] });
}

// ../node_modules/.pnpm/@storybook+react@8.6.14_@storybook+test@8.6.14_storybook@8.6.14__react-dom@19.1.0_react_c6bbd3c434101a7e8fca31d540f5dc4d/node_modules/@storybook/react/dist/index.mjs
var import_global = __toESM(require_global(), 1);
var React = __toESM(require_react(), 1);
var import_preview_api = __toESM(require_preview_api(), 1);
var { window: globalWindow } = import_global.global;
globalWindow && (globalWindow.STORYBOOK_ENV = "react");
function setProjectAnnotations(projectAnnotations) {
  return (0, import_preview_api.setDefaultProjectAnnotations)(INTERNAL_DEFAULT_PROJECT_ANNOTATIONS), (0, import_preview_api.setProjectAnnotations)(projectAnnotations);
}
var INTERNAL_DEFAULT_PROJECT_ANNOTATIONS = { ...entry_preview_exports, renderToCanvas: async (renderContext, canvasElement) => {
  if (renderContext.storyContext.testingLibraryRender == null) return renderToCanvas(renderContext, canvasElement);
  let { storyContext: { context, unboundStoryFn: Story, testingLibraryRender: render } } = renderContext, { unmount } = render(React.createElement(Story, { ...context }), { container: context.canvasElement });
  return unmount;
} };
function composeStory(story, componentAnnotations, projectAnnotations, exportsName) {
  return (0, import_preview_api.composeStory)(story, componentAnnotations, projectAnnotations, globalThis.globalProjectAnnotations ?? INTERNAL_DEFAULT_PROJECT_ANNOTATIONS, exportsName);
}
function composeStories(csfExports, projectAnnotations) {
  return (0, import_preview_api.composeStories)(csfExports, projectAnnotations, composeStory);
}
var _a;
typeof module < "u" && ((_a = module == null ? void 0 : module.hot) == null ? void 0 : _a.decline());
export {
  INTERNAL_DEFAULT_PROJECT_ANNOTATIONS,
  __definePreview,
  composeStories,
  composeStory,
  setProjectAnnotations
};
//# sourceMappingURL=@storybook_react.js.map
