import {
  __export
} from "./chunk-XMOJ2FAB.js";
import "./chunk-LK32TJAX.js";

// ../node_modules/.pnpm/@storybook+addon-docs@8.6.14_@types+react@19.1.6_storybook@8.6.14/node_modules/@storybook/addon-docs/dist/chunk-PRSJUHPQ.mjs
var preview_exports = {};
__export(preview_exports, { parameters: () => parameters });
var excludeTags = Object.entries(globalThis.TAGS_OPTIONS ?? {}).reduce((acc, entry) => {
  let [tag, option] = entry;
  return option.excludeFromDocsStories && (acc[tag] = true), acc;
}, {});
var parameters = { docs: { renderer: async () => {
  let { DocsRenderer } = await import("./DocsRenderer-CFRXHY34-A47K6XO4.js");
  return new DocsRenderer();
}, stories: { filter: (story) => {
  var _a;
  return (story.tags || []).filter((tag) => excludeTags[tag]).length === 0 && !((_a = story.parameters.docs) == null ? void 0 : _a.disable);
} } } };
export {
  parameters
};
//# sourceMappingURL=@storybook_addon-essentials_docs_preview.js.map
