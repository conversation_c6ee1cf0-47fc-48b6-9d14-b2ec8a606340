{"version": 3, "sources": ["../../../../../../../node_modules/.pnpm/@storybook+addon-backgrounds@8.6.14_storybook@8.6.14/node_modules/@storybook/addon-backgrounds/dist/preview.mjs"], "sourcesContent": ["import { useEffect, useMemo } from 'storybook/internal/preview-api';\nimport { global } from '@storybook/global';\nimport { logger } from 'storybook/internal/client-logger';\nimport { dedent } from 'ts-dedent';\n\nvar PARAM_KEY=\"backgrounds\";var DEFAULT_BACKGROUNDS={light:{name:\"light\",value:\"#F8F8F8\"},dark:{name:\"dark\",value:\"#333\"}};var{document,window}=global,isReduceMotionEnabled=()=>!!window?.matchMedia(\"(prefers-reduced-motion: reduce)\")?.matches,clearStyles=selector=>{(Array.isArray(selector)?selector:[selector]).forEach(clearStyle);},clearStyle=selector=>{let element=document.getElementById(selector);element&&element.parentElement?.removeChild(element);},addGridStyle=(selector,css)=>{let existingStyle=document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css,document.head.appendChild(style);}},addBackgroundStyle=(selector,css,storyId)=>{let existingStyle=document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css;let gridStyleSelector=`addon-backgrounds-grid${storyId?`-docs-${storyId}`:\"\"}`,existingGridStyle=document.getElementById(gridStyleSelector);existingGridStyle?existingGridStyle.parentElement?.insertBefore(style,existingGridStyle):document.head.appendChild(style);}};var defaultGrid={cellSize:100,cellAmount:10,opacity:.8},BG_SELECTOR_BASE=\"addon-backgrounds\",GRID_SELECTOR_BASE=\"addon-backgrounds-grid\",transitionStyle=isReduceMotionEnabled()?\"\":\"transition: background-color 0.3s;\",withBackgroundAndGrid=(StoryFn,context)=>{let{globals,parameters:parameters2,viewMode,id}=context,{options=DEFAULT_BACKGROUNDS,disable,grid=defaultGrid}=parameters2[PARAM_KEY]||{},data=globals[PARAM_KEY]||{},backgroundName=data.value,item=backgroundName?options[backgroundName]:void 0,value=item?.value||\"transparent\",showGrid=data.grid||!1,shownBackground=!!item&&!disable,backgroundSelector=viewMode===\"docs\"?`#anchor--${id} .docs-story`:\".sb-show-main\",gridSelector=viewMode===\"docs\"?`#anchor--${id} .docs-story`:\".sb-show-main\",isLayoutPadded=parameters2.layout===void 0||parameters2.layout===\"padded\",defaultOffset=viewMode===\"docs\"?20:isLayoutPadded?16:0,{cellAmount,cellSize,opacity,offsetX=defaultOffset,offsetY=defaultOffset}=grid,backgroundSelectorId=viewMode===\"docs\"?`${BG_SELECTOR_BASE}-docs-${id}`:`${BG_SELECTOR_BASE}-color`,backgroundTarget=viewMode===\"docs\"?id:null;useEffect(()=>{let backgroundStyles=`\n    ${backgroundSelector} {\n      background: ${value} !important;\n      ${transitionStyle}\n      }`;if(!shownBackground){clearStyles(backgroundSelectorId);return}addBackgroundStyle(backgroundSelectorId,backgroundStyles,backgroundTarget);},[backgroundSelector,backgroundSelectorId,backgroundTarget,shownBackground,value]);let gridSelectorId=viewMode===\"docs\"?`${GRID_SELECTOR_BASE}-docs-${id}`:`${GRID_SELECTOR_BASE}`;return useEffect(()=>{if(!showGrid){clearStyles(gridSelectorId);return}let gridSize=[`${cellSize*cellAmount}px ${cellSize*cellAmount}px`,`${cellSize*cellAmount}px ${cellSize*cellAmount}px`,`${cellSize}px ${cellSize}px`,`${cellSize}px ${cellSize}px`].join(\", \"),gridStyles=`\n        ${gridSelector} {\n          background-size: ${gridSize} !important;\n          background-position: ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px !important;\n          background-blend-mode: difference !important;\n          background-image: linear-gradient(rgba(130, 130, 130, ${opacity}) 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ${opacity}) 1px, transparent 1px),\n           linear-gradient(rgba(130, 130, 130, ${opacity/2}) 1px, transparent 1px),\n           linear-gradient(90deg, rgba(130, 130, 130, ${opacity/2}) 1px, transparent 1px) !important;\n        }\n      `;addGridStyle(gridSelectorId,gridStyles);},[cellAmount,cellSize,gridSelector,gridSelectorId,showGrid,offsetX,offsetY,opacity]),StoryFn()};var getBackgroundColorByName=(currentSelectedValue,backgrounds=[],defaultName)=>{if(currentSelectedValue===\"transparent\")return \"transparent\";if(backgrounds.find(background=>background.value===currentSelectedValue)||currentSelectedValue)return currentSelectedValue;let defaultBackground=backgrounds.find(background=>background.name===defaultName);if(defaultBackground)return defaultBackground.value;if(defaultName){let availableColors=backgrounds.map(background=>background.name).join(\", \");logger.warn(dedent`\n        Backgrounds Addon: could not find the default color \"${defaultName}\".\n        These are the available colors for your story based on your configuration:\n        ${availableColors}.\n      `);}return \"transparent\"};var withBackground=(StoryFn,context)=>{let{globals,parameters:parameters2}=context,globalsBackgroundColor=globals[PARAM_KEY]?.value,backgroundsConfig=parameters2[PARAM_KEY],selectedBackgroundColor=useMemo(()=>backgroundsConfig.disable?\"transparent\":getBackgroundColorByName(globalsBackgroundColor,backgroundsConfig.values,backgroundsConfig.default),[backgroundsConfig,globalsBackgroundColor]),isActive=useMemo(()=>selectedBackgroundColor&&selectedBackgroundColor!==\"transparent\",[selectedBackgroundColor]),selector=context.viewMode===\"docs\"?`#anchor--${context.id} .docs-story`:\".sb-show-main\",backgroundStyles=useMemo(()=>`\n      ${selector} {\n        background: ${selectedBackgroundColor} !important;\n        ${isReduceMotionEnabled()?\"\":\"transition: background-color 0.3s;\"}\n      }\n    `,[selectedBackgroundColor,selector]);return useEffect(()=>{let selectorId=context.viewMode===\"docs\"?`addon-backgrounds-docs-${context.id}`:\"addon-backgrounds-color\";if(!isActive){clearStyles(selectorId);return}addBackgroundStyle(selectorId,backgroundStyles,context.viewMode===\"docs\"?context.id:null);},[isActive,backgroundStyles,context]),StoryFn()};var withGrid=(StoryFn,context)=>{let{globals,parameters:parameters2}=context,gridParameters=parameters2[PARAM_KEY].grid,isActive=globals[PARAM_KEY]?.grid===!0&&gridParameters.disable!==!0,{cellAmount,cellSize,opacity}=gridParameters,isInDocs=context.viewMode===\"docs\",defaultOffset=parameters2.layout===void 0||parameters2.layout===\"padded\"?16:0,offsetX=gridParameters.offsetX??(isInDocs?20:defaultOffset),offsetY=gridParameters.offsetY??(isInDocs?20:defaultOffset),gridStyles=useMemo(()=>{let selector=context.viewMode===\"docs\"?`#anchor--${context.id} .docs-story`:\".sb-show-main\",backgroundSize=[`${cellSize*cellAmount}px ${cellSize*cellAmount}px`,`${cellSize*cellAmount}px ${cellSize*cellAmount}px`,`${cellSize}px ${cellSize}px`,`${cellSize}px ${cellSize}px`].join(\", \");return `\n      ${selector} {\n        background-size: ${backgroundSize} !important;\n        background-position: ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px, ${offsetX}px ${offsetY}px !important;\n        background-blend-mode: difference !important;\n        background-image: linear-gradient(rgba(130, 130, 130, ${opacity}) 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ${opacity}) 1px, transparent 1px),\n         linear-gradient(rgba(130, 130, 130, ${opacity/2}) 1px, transparent 1px),\n         linear-gradient(90deg, rgba(130, 130, 130, ${opacity/2}) 1px, transparent 1px) !important;\n      }\n    `},[cellSize]);return useEffect(()=>{let selectorId=context.viewMode===\"docs\"?`addon-backgrounds-grid-docs-${context.id}`:\"addon-backgrounds-grid\";if(!isActive){clearStyles(selectorId);return}addGridStyle(selectorId,gridStyles);},[isActive,gridStyles,context]),StoryFn()};var decorators=globalThis.FEATURES?.backgroundsStoryGlobals?[withBackgroundAndGrid]:[withGrid,withBackground],parameters={[PARAM_KEY]:{grid:{cellSize:20,opacity:.5,cellAmount:5},disable:!1,...!globalThis.FEATURES?.backgroundsStoryGlobals&&{values:Object.values(DEFAULT_BACKGROUNDS)}}},modern={[PARAM_KEY]:{value:void 0,grid:!1}},initialGlobals=globalThis.FEATURES?.backgroundsStoryGlobals?modern:{[PARAM_KEY]:null};\n\nexport { decorators, initialGlobals, parameters };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,yBAAmC;AACnC,oBAAuB;AACvB,2BAAuB;AAGvB,IAAI,YAAU;AAAc,IAAI,sBAAoB,EAAC,OAAM,EAAC,MAAK,SAAQ,OAAM,UAAS,GAAE,MAAK,EAAC,MAAK,QAAO,OAAM,OAAM,EAAC;AAAE,IAAG,EAAC,UAAS,OAAM,IAAE;AAArB,IAA4B,wBAAsB,MAAE;AAL/K,MAAAA;AAKiL,UAAC,GAACA,MAAA,iCAAQ,WAAW,wCAAnB,gBAAAA,IAAwD;AAAA;AAAhH,IAAwH,cAAY,cAAU;AAAC,GAAC,MAAM,QAAQ,QAAQ,IAAE,WAAS,CAAC,QAAQ,GAAG,QAAQ,UAAU;AAAE;AAAjN,IAAmN,aAAW,cAAU;AALnW,MAAAA;AAKoW,MAAI,UAAQ,SAAS,eAAe,QAAQ;AAAE,eAASA,MAAA,QAAQ,kBAAR,gBAAAA,IAAuB,YAAY;AAAS;AAA5U,IAA8U,eAAa,CAAC,UAAS,QAAM;AAAC,MAAI,gBAAc,SAAS,eAAe,QAAQ;AAAE,MAAG,cAAc,eAAc,cAAY,QAAM,cAAc,YAAU;AAAA,OAAU;AAAC,QAAI,QAAM,SAAS,cAAc,OAAO;AAAE,UAAM,aAAa,MAAK,QAAQ,GAAE,MAAM,YAAU,KAAI,SAAS,KAAK,YAAY,KAAK;AAAA,EAAE;AAAC;AAAtnB,IAAwnB,qBAAmB,CAAC,UAAS,KAAI,YAAU;AAL9xB,MAAAA;AAK+xB,MAAI,gBAAc,SAAS,eAAe,QAAQ;AAAE,MAAG,cAAc,eAAc,cAAY,QAAM,cAAc,YAAU;AAAA,OAAU;AAAC,QAAI,QAAM,SAAS,cAAc,OAAO;AAAE,UAAM,aAAa,MAAK,QAAQ,GAAE,MAAM,YAAU;AAAI,QAAI,oBAAkB,yBAAyB,UAAQ,SAAS,OAAO,KAAG,EAAE,IAAG,oBAAkB,SAAS,eAAe,iBAAiB;AAAE,yBAAkBA,MAAA,kBAAkB,kBAAlB,gBAAAA,IAAiC,aAAa,OAAM,qBAAmB,SAAS,KAAK,YAAY,KAAK;AAAA,EAAE;AAAC;AAAE,IAAI,cAAY,EAAC,UAAS,KAAI,YAAW,IAAG,SAAQ,IAAE;AAAtD,IAAwD,mBAAiB;AAAzE,IAA6F,qBAAmB;AAAhH,IAAyI,kBAAgB,sBAAsB,IAAE,KAAG;AAApL,IAAyN,wBAAsB,CAAC,SAAQ,YAAU;AAAC,MAAG,EAAC,SAAQ,YAAW,aAAY,UAAS,GAAE,IAAE,SAAQ,EAAC,UAAQ,qBAAoB,SAAQ,OAAK,YAAW,IAAE,YAAY,SAAS,KAAG,CAAC,GAAE,OAAK,QAAQ,SAAS,KAAG,CAAC,GAAE,iBAAe,KAAK,OAAM,OAAK,iBAAe,QAAQ,cAAc,IAAE,QAAO,SAAM,6BAAM,UAAO,eAAc,WAAS,KAAK,QAAM,OAAG,kBAAgB,CAAC,CAAC,QAAM,CAAC,SAAQ,qBAAmB,aAAW,SAAO,YAAY,EAAE,iBAAe,iBAAgB,eAAa,aAAW,SAAO,YAAY,EAAE,iBAAe,iBAAgB,iBAAe,YAAY,WAAS,UAAQ,YAAY,WAAS,UAAS,gBAAc,aAAW,SAAO,KAAG,iBAAe,KAAG,GAAE,EAAC,YAAW,UAAS,SAAQ,UAAQ,eAAc,UAAQ,cAAa,IAAE,MAAK,uBAAqB,aAAW,SAAO,GAAG,gBAAgB,SAAS,EAAE,KAAG,GAAG,gBAAgB,UAAS,mBAAiB,aAAW,SAAO,KAAG;AAAK,oCAAU,MAAI;AAAC,QAAI,mBAAiB;AAAA,MAC13E,kBAAkB;AAAA,oBACJ,KAAK;AAAA,QACjB,eAAe;AAAA;AACd,QAAG,CAAC,iBAAgB;AAAC,kBAAY,oBAAoB;AAAE;AAAA,IAAM;AAAC,uBAAmB,sBAAqB,kBAAiB,gBAAgB;AAAA,EAAE,GAAE,CAAC,oBAAmB,sBAAqB,kBAAiB,iBAAgB,KAAK,CAAC;AAAE,MAAI,iBAAe,aAAW,SAAO,GAAG,kBAAkB,SAAS,EAAE,KAAG,GAAG,kBAAkB;AAAG,aAAO,8BAAU,MAAI;AAAC,QAAG,CAAC,UAAS;AAAC,kBAAY,cAAc;AAAE;AAAA,IAAM;AAAC,QAAI,WAAS,CAAC,GAAG,WAAS,UAAU,MAAM,WAAS,UAAU,MAAK,GAAG,WAAS,UAAU,MAAM,WAAS,UAAU,MAAK,GAAG,QAAQ,MAAM,QAAQ,MAAK,GAAG,QAAQ,MAAM,QAAQ,IAAI,EAAE,KAAK,IAAI,GAAE,aAAW;AAAA,UAC5kB,YAAY;AAAA,6BACO,QAAQ;AAAA,iCACJ,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO;AAAA;AAAA,kEAEpE,OAAO;AAAA,wDACjB,OAAO;AAAA,iDACd,UAAQ,CAAC;AAAA,wDACF,UAAQ,CAAC;AAAA;AAAA;AAEzD,iBAAa,gBAAe,UAAU;AAAA,EAAE,GAAE,CAAC,YAAW,UAAS,cAAa,gBAAe,UAAS,SAAQ,SAAQ,OAAO,CAAC,GAAE,QAAQ;AAAC;AAAE,IAAI,2BAAyB,CAAC,sBAAqB,cAAY,CAAC,GAAE,gBAAc;AAAC,MAAG,yBAAuB,cAAc,QAAO;AAAc,MAAG,YAAY,KAAK,gBAAY,WAAW,UAAQ,oBAAoB,KAAG,qBAAqB,QAAO;AAAqB,MAAI,oBAAkB,YAAY,KAAK,gBAAY,WAAW,SAAO,WAAW;AAAE,MAAG,kBAAkB,QAAO,kBAAkB;AAAM,MAAG,aAAY;AAAC,QAAI,kBAAgB,YAAY,IAAI,gBAAY,WAAW,IAAI,EAAE,KAAK,IAAI;AAAE,gCAAO,KAAK;AAAA,+DACzkB,WAAW;AAAA;AAAA,UAEhE,eAAe;AAAA,OAClB;AAAA,EAAE;AAAC,SAAO;AAAa;AAAE,IAAI,iBAAe,CAAC,SAAQ,YAAU;AAvBtE,MAAAA;AAuBuE,MAAG,EAAC,SAAQ,YAAW,YAAW,IAAE,SAAQ,0BAAuBA,MAAA,QAAQ,SAAS,MAAjB,gBAAAA,IAAoB,OAAM,oBAAkB,YAAY,SAAS,GAAE,8BAAwB,4BAAQ,MAAI,kBAAkB,UAAQ,gBAAc,yBAAyB,wBAAuB,kBAAkB,QAAO,kBAAkB,OAAO,GAAE,CAAC,mBAAkB,sBAAsB,CAAC,GAAE,eAAS,4BAAQ,MAAI,2BAAyB,4BAA0B,eAAc,CAAC,uBAAuB,CAAC,GAAE,WAAS,QAAQ,aAAW,SAAO,YAAY,QAAQ,EAAE,iBAAe,iBAAgB,uBAAiB,4BAAQ,MAAI;AAAA,QACvoB,QAAQ;AAAA,sBACM,uBAAuB;AAAA,UACnC,sBAAsB,IAAE,KAAG,oCAAoC;AAAA;AAAA,OAEnE,CAAC,yBAAwB,QAAQ,CAAC;AAAE,aAAO,8BAAU,MAAI;AAAC,QAAI,aAAW,QAAQ,aAAW,SAAO,0BAA0B,QAAQ,EAAE,KAAG;AAA0B,QAAG,CAAC,UAAS;AAAC,kBAAY,UAAU;AAAE;AAAA,IAAM;AAAC,uBAAmB,YAAW,kBAAiB,QAAQ,aAAW,SAAO,QAAQ,KAAG,IAAI;AAAA,EAAE,GAAE,CAAC,UAAS,kBAAiB,OAAO,CAAC,GAAE,QAAQ;AAAC;AAAE,IAAI,WAAS,CAAC,SAAQ,YAAU;AA5BnY,MAAAA;AA4BoY,MAAG,EAAC,SAAQ,YAAW,YAAW,IAAE,SAAQ,iBAAe,YAAY,SAAS,EAAE,MAAK,aAASA,MAAA,QAAQ,SAAS,MAAjB,gBAAAA,IAAoB,UAAO,QAAI,eAAe,YAAU,MAAG,EAAC,YAAW,UAAS,QAAO,IAAE,gBAAe,WAAS,QAAQ,aAAW,QAAO,gBAAc,YAAY,WAAS,UAAQ,YAAY,WAAS,WAAS,KAAG,GAAE,UAAQ,eAAe,YAAU,WAAS,KAAG,gBAAe,UAAQ,eAAe,YAAU,WAAS,KAAG,gBAAe,iBAAW,4BAAQ,MAAI;AAAC,QAAI,WAAS,QAAQ,aAAW,SAAO,YAAY,QAAQ,EAAE,iBAAe,iBAAgB,iBAAe,CAAC,GAAG,WAAS,UAAU,MAAM,WAAS,UAAU,MAAK,GAAG,WAAS,UAAU,MAAM,WAAS,UAAU,MAAK,GAAG,QAAQ,MAAM,QAAQ,MAAK,GAAG,QAAQ,MAAM,QAAQ,IAAI,EAAE,KAAK,IAAI;AAAE,WAAO;AAAA,QACxmC,QAAQ;AAAA,2BACW,cAAc;AAAA,+BACV,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO;AAAA;AAAA,gEAEpE,OAAO;AAAA,sDACjB,OAAO;AAAA,+CACd,UAAQ,CAAC;AAAA,sDACF,UAAQ,CAAC;AAAA;AAAA;AAAA,EAE1D,GAAE,CAAC,QAAQ,CAAC;AAAE,aAAO,8BAAU,MAAI;AAAC,QAAI,aAAW,QAAQ,aAAW,SAAO,+BAA+B,QAAQ,EAAE,KAAG;AAAyB,QAAG,CAAC,UAAS;AAAC,kBAAY,UAAU;AAAE;AAAA,IAAM;AAAC,iBAAa,YAAW,UAAU;AAAA,EAAE,GAAE,CAAC,UAAS,YAAW,OAAO,CAAC,GAAE,QAAQ;AAAC;AAtClR;AAsCoR,IAAI,eAAW,gBAAW,aAAX,mBAAqB,2BAAwB,CAAC,qBAAqB,IAAE,CAAC,UAAS,cAAc;AAtChY,IAAAA;AAsCoR,IAA8G,aAAW,EAAC,CAAC,SAAS,GAAE,EAAC,MAAK,EAAC,UAAS,IAAG,SAAQ,KAAG,YAAW,EAAC,GAAE,SAAQ,OAAG,GAAG,GAACA,MAAA,WAAW,aAAX,gBAAAA,IAAqB,4BAAyB,EAAC,QAAO,OAAO,OAAO,mBAAmB,EAAC,EAAC,EAAC;AAA3R,IAA6R,SAAO,EAAC,CAAC,SAAS,GAAE,EAAC,OAAM,QAAO,MAAK,MAAE,EAAC;AAtC3lB,IAAAA;AAsCoR,IAAyU,mBAAeA,MAAA,WAAW,aAAX,gBAAAA,IAAqB,2BAAwB,SAAO,EAAC,CAAC,SAAS,GAAE,KAAI;", "names": ["_a"]}