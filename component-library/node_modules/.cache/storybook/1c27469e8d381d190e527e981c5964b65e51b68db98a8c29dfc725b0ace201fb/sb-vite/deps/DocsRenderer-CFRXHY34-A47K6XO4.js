import {
  renderElement,
  unmountElement
} from "./chunk-QC53BCJT.js";
import {
  AnchorMdx,
  CodeOrSourceMdx,
  Docs,
  HeadersMdx
} from "./chunk-IY7FUPJF.js";
import "./chunk-3JSVE6UH.js";
import "./chunk-N36CJ7HT.js";
import "./chunk-K7763LNH.js";
import "./chunk-2VLGG4VU.js";
import "./chunk-74HTNI4C.js";
import "./chunk-OQJ5TUTM.js";
import "./chunk-F2TZRQKA.js";
import "./chunk-4LBPNBH5.js";
import "./chunk-4Z74DQ7G.js";
import {
  require_react
} from "./chunk-BZ4VO6P4.js";
import "./chunk-XMOJ2FAB.js";
import "./chunk-IP3H6NNM.js";
import "./chunk-KQ2JNRIV.js";
import "./chunk-3T3HEUF2.js";
import "./chunk-6GFF2EK4.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../node_modules/.pnpm/@storybook+addon-docs@8.6.14_@types+react@19.1.6_storybook@8.6.14/node_modules/@storybook/addon-docs/dist/chunk-NUUEMKO5.mjs
var import_react = __toESM(require_react(), 1);
var defaultComponents = { code: CodeOrSourceMdx, a: AnchorMdx, ...HeadersMdx };
var ErrorBoundary = class extends import_react.Component {
  constructor() {
    super(...arguments);
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    return { hasError: true };
  }
  componentDidCatch(err) {
    let { showException } = this.props;
    showException(err);
  }
  render() {
    let { hasError } = this.state, { children } = this.props;
    return hasError ? null : import_react.default.createElement(import_react.default.Fragment, null, children);
  }
};
var DocsRenderer = class {
  constructor() {
    this.render = async (context, docsParameter, element) => {
      let components = { ...defaultComponents, ...docsParameter == null ? void 0 : docsParameter.components }, TDocs = Docs;
      return new Promise((resolve, reject) => {
        import("./react-XZKIVDBS.js").then(({ MDXProvider }) => renderElement(import_react.default.createElement(ErrorBoundary, { showException: reject, key: Math.random() }, import_react.default.createElement(MDXProvider, { components }, import_react.default.createElement(TDocs, { context, docsParameter }))), element)).then(() => resolve());
      });
    }, this.unmount = (element) => {
      unmountElement(element);
    };
  }
};
export {
  DocsRenderer,
  defaultComponents
};
//# sourceMappingURL=DocsRenderer-CFRXHY34-A47K6XO4.js.map
