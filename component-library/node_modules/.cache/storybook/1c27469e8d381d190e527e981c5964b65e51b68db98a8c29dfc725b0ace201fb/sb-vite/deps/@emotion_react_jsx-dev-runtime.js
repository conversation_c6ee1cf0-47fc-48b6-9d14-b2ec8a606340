import {
  require_jsx_dev_runtime
} from "./chunk-T5YSHTHU.js";
import {
  Emotion$1,
  createEmotionProps,
  hasOwn,
  require_hoist_non_react_statics_cjs
} from "./chunk-QALRF32W.js";
import {
  require_react
} from "./chunk-BZ4VO6P4.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../node_modules/.pnpm/@emotion+react@11.14.0_@types+react@19.1.6_react@19.1.0/node_modules/@emotion/react/jsx-dev-runtime/dist/emotion-react-jsx-dev-runtime.browser.development.esm.js
var ReactJSXRuntimeDev = __toESM(require_jsx_dev_runtime());
var import_react = __toESM(require_react());
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());
var Fragment2 = ReactJSXRuntimeDev.Fragment;
var jsxDEV2 = function jsxDEV3(type, props, key, isStaticChildren, source, self) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntimeDev.jsxDEV(type, props, key, isStaticChildren, source, self);
  }
  return ReactJSXRuntimeDev.jsxDEV(Emotion$1, createEmotionProps(type, props), key, isStaticChildren, source, self);
};
export {
  Fragment2 as Fragment,
  jsxDEV2 as jsxDEV
};
//# sourceMappingURL=@emotion_react_jsx-dev-runtime.js.map
