{"version": 3, "sources": ["../../../../../../../node_modules/.pnpm/@storybook+addon-outline@8.6.14_storybook@8.6.14/node_modules/@storybook/addon-outline/dist/preview.mjs"], "sourcesContent": ["import { useMemo, useEffect } from 'storybook/internal/preview-api';\nimport { global } from '@storybook/global';\nimport { dedent } from 'ts-dedent';\n\nvar PARAM_KEY=\"outline\";var clearStyles=selector=>{(Array.isArray(selector)?selector:[selector]).forEach(clearStyle);},clearStyle=input=>{let selector=typeof input==\"string\"?input:input.join(\"\"),element=global.document.getElementById(selector);element&&element.parentElement&&element.parentElement.removeChild(element);},addOutlineStyles=(selector,css)=>{let existingStyle=global.document.getElementById(selector);if(existingStyle)existingStyle.innerHTML!==css&&(existingStyle.innerHTML=css);else {let style=global.document.createElement(\"style\");style.setAttribute(\"id\",selector),style.innerHTML=css,global.document.head.appendChild(style);}};function outlineCSS(selector){return dedent`\n    ${selector} body {\n      outline: 1px solid #2980b9 !important;\n    }\n\n    ${selector} article {\n      outline: 1px solid #3498db !important;\n    }\n\n    ${selector} nav {\n      outline: 1px solid #0088c3 !important;\n    }\n\n    ${selector} aside {\n      outline: 1px solid #33a0ce !important;\n    }\n\n    ${selector} section {\n      outline: 1px solid #66b8da !important;\n    }\n\n    ${selector} header {\n      outline: 1px solid #99cfe7 !important;\n    }\n\n    ${selector} footer {\n      outline: 1px solid #cce7f3 !important;\n    }\n\n    ${selector} h1 {\n      outline: 1px solid #162544 !important;\n    }\n\n    ${selector} h2 {\n      outline: 1px solid #314e6e !important;\n    }\n\n    ${selector} h3 {\n      outline: 1px solid #3e5e85 !important;\n    }\n\n    ${selector} h4 {\n      outline: 1px solid #449baf !important;\n    }\n\n    ${selector} h5 {\n      outline: 1px solid #c7d1cb !important;\n    }\n\n    ${selector} h6 {\n      outline: 1px solid #4371d0 !important;\n    }\n\n    ${selector} main {\n      outline: 1px solid #2f4f90 !important;\n    }\n\n    ${selector} address {\n      outline: 1px solid #1a2c51 !important;\n    }\n\n    ${selector} div {\n      outline: 1px solid #036cdb !important;\n    }\n\n    ${selector} p {\n      outline: 1px solid #ac050b !important;\n    }\n\n    ${selector} hr {\n      outline: 1px solid #ff063f !important;\n    }\n\n    ${selector} pre {\n      outline: 1px solid #850440 !important;\n    }\n\n    ${selector} blockquote {\n      outline: 1px solid #f1b8e7 !important;\n    }\n\n    ${selector} ol {\n      outline: 1px solid #ff050c !important;\n    }\n\n    ${selector} ul {\n      outline: 1px solid #d90416 !important;\n    }\n\n    ${selector} li {\n      outline: 1px solid #d90416 !important;\n    }\n\n    ${selector} dl {\n      outline: 1px solid #fd3427 !important;\n    }\n\n    ${selector} dt {\n      outline: 1px solid #ff0043 !important;\n    }\n\n    ${selector} dd {\n      outline: 1px solid #e80174 !important;\n    }\n\n    ${selector} figure {\n      outline: 1px solid #ff00bb !important;\n    }\n\n    ${selector} figcaption {\n      outline: 1px solid #bf0032 !important;\n    }\n\n    ${selector} table {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    ${selector} caption {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    ${selector} thead {\n      outline: 1px solid #98daca !important;\n    }\n\n    ${selector} tbody {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    ${selector} tfoot {\n      outline: 1px solid #22746b !important;\n    }\n\n    ${selector} tr {\n      outline: 1px solid #86c0b2 !important;\n    }\n\n    ${selector} th {\n      outline: 1px solid #a1e7d6 !important;\n    }\n\n    ${selector} td {\n      outline: 1px solid #3f5a54 !important;\n    }\n\n    ${selector} col {\n      outline: 1px solid #6c9a8f !important;\n    }\n\n    ${selector} colgroup {\n      outline: 1px solid #6c9a9d !important;\n    }\n\n    ${selector} button {\n      outline: 1px solid #da8301 !important;\n    }\n\n    ${selector} datalist {\n      outline: 1px solid #c06000 !important;\n    }\n\n    ${selector} fieldset {\n      outline: 1px solid #d95100 !important;\n    }\n\n    ${selector} form {\n      outline: 1px solid #d23600 !important;\n    }\n\n    ${selector} input {\n      outline: 1px solid #fca600 !important;\n    }\n\n    ${selector} keygen {\n      outline: 1px solid #b31e00 !important;\n    }\n\n    ${selector} label {\n      outline: 1px solid #ee8900 !important;\n    }\n\n    ${selector} legend {\n      outline: 1px solid #de6d00 !important;\n    }\n\n    ${selector} meter {\n      outline: 1px solid #e8630c !important;\n    }\n\n    ${selector} optgroup {\n      outline: 1px solid #b33600 !important;\n    }\n\n    ${selector} option {\n      outline: 1px solid #ff8a00 !important;\n    }\n\n    ${selector} output {\n      outline: 1px solid #ff9619 !important;\n    }\n\n    ${selector} progress {\n      outline: 1px solid #e57c00 !important;\n    }\n\n    ${selector} select {\n      outline: 1px solid #e26e0f !important;\n    }\n\n    ${selector} textarea {\n      outline: 1px solid #cc5400 !important;\n    }\n\n    ${selector} details {\n      outline: 1px solid #33848f !important;\n    }\n\n    ${selector} summary {\n      outline: 1px solid #60a1a6 !important;\n    }\n\n    ${selector} command {\n      outline: 1px solid #438da1 !important;\n    }\n\n    ${selector} menu {\n      outline: 1px solid #449da6 !important;\n    }\n\n    ${selector} del {\n      outline: 1px solid #bf0000 !important;\n    }\n\n    ${selector} ins {\n      outline: 1px solid #400000 !important;\n    }\n\n    ${selector} img {\n      outline: 1px solid #22746b !important;\n    }\n\n    ${selector} iframe {\n      outline: 1px solid #64a7a0 !important;\n    }\n\n    ${selector} embed {\n      outline: 1px solid #98daca !important;\n    }\n\n    ${selector} object {\n      outline: 1px solid #00cc99 !important;\n    }\n\n    ${selector} param {\n      outline: 1px solid #37ffc4 !important;\n    }\n\n    ${selector} video {\n      outline: 1px solid #6ee866 !important;\n    }\n\n    ${selector} audio {\n      outline: 1px solid #027353 !important;\n    }\n\n    ${selector} source {\n      outline: 1px solid #012426 !important;\n    }\n\n    ${selector} canvas {\n      outline: 1px solid #a2f570 !important;\n    }\n\n    ${selector} track {\n      outline: 1px solid #59a600 !important;\n    }\n\n    ${selector} map {\n      outline: 1px solid #7be500 !important;\n    }\n\n    ${selector} area {\n      outline: 1px solid #305900 !important;\n    }\n\n    ${selector} a {\n      outline: 1px solid #ff62ab !important;\n    }\n\n    ${selector} em {\n      outline: 1px solid #800b41 !important;\n    }\n\n    ${selector} strong {\n      outline: 1px solid #ff1583 !important;\n    }\n\n    ${selector} i {\n      outline: 1px solid #803156 !important;\n    }\n\n    ${selector} b {\n      outline: 1px solid #cc1169 !important;\n    }\n\n    ${selector} u {\n      outline: 1px solid #ff0430 !important;\n    }\n\n    ${selector} s {\n      outline: 1px solid #f805e3 !important;\n    }\n\n    ${selector} small {\n      outline: 1px solid #d107b2 !important;\n    }\n\n    ${selector} abbr {\n      outline: 1px solid #4a0263 !important;\n    }\n\n    ${selector} q {\n      outline: 1px solid #240018 !important;\n    }\n\n    ${selector} cite {\n      outline: 1px solid #64003c !important;\n    }\n\n    ${selector} dfn {\n      outline: 1px solid #b4005a !important;\n    }\n\n    ${selector} sub {\n      outline: 1px solid #dba0c8 !important;\n    }\n\n    ${selector} sup {\n      outline: 1px solid #cc0256 !important;\n    }\n\n    ${selector} time {\n      outline: 1px solid #d6606d !important;\n    }\n\n    ${selector} code {\n      outline: 1px solid #e04251 !important;\n    }\n\n    ${selector} kbd {\n      outline: 1px solid #5e001f !important;\n    }\n\n    ${selector} samp {\n      outline: 1px solid #9c0033 !important;\n    }\n\n    ${selector} var {\n      outline: 1px solid #d90047 !important;\n    }\n\n    ${selector} mark {\n      outline: 1px solid #ff0053 !important;\n    }\n\n    ${selector} bdi {\n      outline: 1px solid #bf3668 !important;\n    }\n\n    ${selector} bdo {\n      outline: 1px solid #6f1400 !important;\n    }\n\n    ${selector} ruby {\n      outline: 1px solid #ff7b93 !important;\n    }\n\n    ${selector} rt {\n      outline: 1px solid #ff2f54 !important;\n    }\n\n    ${selector} rp {\n      outline: 1px solid #803e49 !important;\n    }\n\n    ${selector} span {\n      outline: 1px solid #cc2643 !important;\n    }\n\n    ${selector} br {\n      outline: 1px solid #db687d !important;\n    }\n\n    ${selector} wbr {\n      outline: 1px solid #db175b !important;\n    }`}var withOutline=(StoryFn,context)=>{let{globals}=context,isActive=[!0,\"true\"].includes(globals[PARAM_KEY]),isInDocs=context.viewMode===\"docs\",outlineStyles=useMemo(()=>outlineCSS(isInDocs?'[data-story-block=\"true\"]':\".sb-show-main\"),[context]);return useEffect(()=>{let selectorId=isInDocs?`addon-outline-docs-${context.id}`:\"addon-outline\";return isActive?addOutlineStyles(selectorId,outlineStyles):clearStyles(selectorId),()=>{clearStyles(selectorId);}},[isActive,outlineStyles,context]),StoryFn()};var decorators=[withOutline],initialGlobals={[PARAM_KEY]:!1};\n\nexport { decorators, initialGlobals };\n"], "mappings": ";;;;;;;;;;;;;;AAAA,yBAAmC;AACnC,oBAAuB;AAGvB,IAAI,YAAU;AAAU,IAAI,cAAY,cAAU;AAAC,GAAC,MAAM,QAAQ,QAAQ,IAAE,WAAS,CAAC,QAAQ,GAAG,QAAQ,UAAU;AAAE;AAA7F,IAA+F,aAAW,WAAO;AAAC,MAAI,WAAS,OAAO,SAAO,WAAS,QAAM,MAAM,KAAK,EAAE,GAAE,UAAQ,qBAAO,SAAS,eAAe,QAAQ;AAAE,aAAS,QAAQ,iBAAe,QAAQ,cAAc,YAAY,OAAO;AAAE;AAAvS,IAAyS,mBAAiB,CAAC,UAAS,QAAM;AAAC,MAAI,gBAAc,qBAAO,SAAS,eAAe,QAAQ;AAAE,MAAG,cAAc,eAAc,cAAY,QAAM,cAAc,YAAU;AAAA,OAAU;AAAC,QAAI,QAAM,qBAAO,SAAS,cAAc,OAAO;AAAE,UAAM,aAAa,MAAK,QAAQ,GAAE,MAAM,YAAU,KAAI,qBAAO,SAAS,KAAK,YAAY,KAAK;AAAA,EAAE;AAAC;AAAE,SAAS,WAAW,UAAS;AAAC,SAAO;AAAA,MACnqB,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIR,QAAQ;AAAA;AAAA;AAER;AAAC,IAAI,cAAY,CAAC,SAAQ,YAAU;AAAC,MAAG,EAAC,QAAO,IAAE,SAAQ,WAAS,CAAC,MAAG,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC,GAAE,WAAS,QAAQ,aAAW,QAAO,oBAAc,4BAAQ,MAAI,WAAW,WAAS,8BAA4B,eAAe,GAAE,CAAC,OAAO,CAAC;AAAE,aAAO,8BAAU,MAAI;AAAC,QAAI,aAAW,WAAS,sBAAsB,QAAQ,EAAE,KAAG;AAAgB,WAAO,WAAS,iBAAiB,YAAW,aAAa,IAAE,YAAY,UAAU,GAAE,MAAI;AAAC,kBAAY,UAAU;AAAA,IAAE;AAAA,EAAC,GAAE,CAAC,UAAS,eAAc,OAAO,CAAC,GAAE,QAAQ;AAAC;AAAE,IAAI,aAAW,CAAC,WAAW;AAA3B,IAA6B,iBAAe,EAAC,CAAC,SAAS,GAAE,MAAE;", "names": []}