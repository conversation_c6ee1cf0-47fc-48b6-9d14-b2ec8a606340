{"version": 3, "sources": ["../../../../sb-vite-plugin-externals/@storybook/core/preview-errors.js", "../../../../../../../node_modules/.pnpm/jsdoc-type-pratt-parser@4.1.0/node_modules/jsdoc-type-pratt-parser/dist/index.js", "../../../../../../../node_modules/.pnpm/@storybook+core@8.6.14_storybook@8.6.14/node_modules/@storybook/core/dist/docs-tools/index.js"], "sourcesContent": ["module.exports = __STORYBOOK_MODULE_CORE_EVENTS_PREVIEW_ERRORS__;", "(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n    typeof define === 'function' && define.amd ? define(['exports'], factory) :\n    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.jtpp = {}));\n})(this, (function (exports) { 'use strict';\n\n    function tokenToString(token) {\n        if (token.text !== undefined && token.text !== '') {\n            return `'${token.type}' with value '${token.text}'`;\n        }\n        else {\n            return `'${token.type}'`;\n        }\n    }\n    class NoParsletFoundError extends Error {\n        constructor(token) {\n            super(`No parslet found for token: ${tokenToString(token)}`);\n            this.token = token;\n            Object.setPrototypeOf(this, NoParsletFoundError.prototype);\n        }\n        getToken() {\n            return this.token;\n        }\n    }\n    class EarlyEndOfParseError extends Error {\n        constructor(token) {\n            super(`The parsing ended early. The next token was: ${tokenToString(token)}`);\n            this.token = token;\n            Object.setPrototypeOf(this, EarlyEndOfParseError.prototype);\n        }\n        getToken() {\n            return this.token;\n        }\n    }\n    class UnexpectedTypeError extends Error {\n        constructor(result, message) {\n            let error = `Unexpected type: '${result.type}'.`;\n            if (message !== undefined) {\n                error += ` Message: ${message}`;\n            }\n            super(error);\n            Object.setPrototypeOf(this, UnexpectedTypeError.prototype);\n        }\n    }\n    // export class UnexpectedTokenError extends Error {\n    //   private expected: Token\n    //   private found: Token\n    //\n    //   constructor (expected: Token, found: Token) {\n    //     super(`The parsing ended early. The next token was: ${tokenToString(token)}`)\n    //\n    //     this.token = token\n    //\n    //     Object.setPrototypeOf(this, EarlyEndOfParseError.prototype)\n    //   }\n    //\n    //   getToken() {\n    //     return this.token\n    //   }\n    // }\n\n    function makePunctuationRule(type) {\n        return text => {\n            if (text.startsWith(type)) {\n                return { type, text: type };\n            }\n            else {\n                return null;\n            }\n        };\n    }\n    function getQuoted(text) {\n        let position = 0;\n        let char;\n        const mark = text[0];\n        let escaped = false;\n        if (mark !== '\\'' && mark !== '\"') {\n            return null;\n        }\n        while (position < text.length) {\n            position++;\n            char = text[position];\n            if (!escaped && char === mark) {\n                position++;\n                break;\n            }\n            escaped = !escaped && char === '\\\\';\n        }\n        if (char !== mark) {\n            throw new Error('Unterminated String');\n        }\n        return text.slice(0, position);\n    }\n    const identifierStartRegex = /[$_\\p{ID_Start}]|\\\\u\\p{Hex_Digit}{4}|\\\\u\\{0*(?:\\p{Hex_Digit}{1,5}|10\\p{Hex_Digit}{4})\\}/u;\n    // A hyphen is not technically allowed, but to keep it liberal for now,\n    //  adding it here\n    const identifierContinueRegex = /[$\\-\\p{ID_Continue}\\u200C\\u200D]|\\\\u\\p{Hex_Digit}{4}|\\\\u\\{0*(?:\\p{Hex_Digit}{1,5}|10\\p{Hex_Digit}{4})\\}/u;\n    function getIdentifier(text) {\n        let char = text[0];\n        if (!identifierStartRegex.test(char)) {\n            return null;\n        }\n        let position = 1;\n        do {\n            char = text[position];\n            if (!identifierContinueRegex.test(char)) {\n                break;\n            }\n            position++;\n        } while (position < text.length);\n        return text.slice(0, position);\n    }\n    // we are a bit more liberal than TypeScript here and allow `NaN`, `Infinity` and `-Infinity`\n    const numberRegex = /^(NaN|-?((\\d*\\.\\d+|\\d+)([Ee][+-]?\\d+)?|Infinity))/;\n    function getNumber(text) {\n        var _a, _b;\n        return (_b = (_a = numberRegex.exec(text)) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : null;\n    }\n    const identifierRule = text => {\n        const value = getIdentifier(text);\n        if (value == null) {\n            return null;\n        }\n        return {\n            type: 'Identifier',\n            text: value\n        };\n    };\n    function makeKeyWordRule(type) {\n        return text => {\n            if (!text.startsWith(type)) {\n                return null;\n            }\n            const prepends = text[type.length];\n            if (prepends !== undefined && identifierContinueRegex.test(prepends)) {\n                return null;\n            }\n            return {\n                type,\n                text: type\n            };\n        };\n    }\n    const stringValueRule = text => {\n        const value = getQuoted(text);\n        if (value == null) {\n            return null;\n        }\n        return {\n            type: 'StringValue',\n            text: value\n        };\n    };\n    const eofRule = text => {\n        if (text.length > 0) {\n            return null;\n        }\n        return {\n            type: 'EOF',\n            text: ''\n        };\n    };\n    const numberRule = text => {\n        const value = getNumber(text);\n        if (value === null) {\n            return null;\n        }\n        return {\n            type: 'Number',\n            text: value\n        };\n    };\n    const rules = [\n        eofRule,\n        makePunctuationRule('=>'),\n        makePunctuationRule('('),\n        makePunctuationRule(')'),\n        makePunctuationRule('{'),\n        makePunctuationRule('}'),\n        makePunctuationRule('['),\n        makePunctuationRule(']'),\n        makePunctuationRule('|'),\n        makePunctuationRule('&'),\n        makePunctuationRule('<'),\n        makePunctuationRule('>'),\n        makePunctuationRule(','),\n        makePunctuationRule(';'),\n        makePunctuationRule('*'),\n        makePunctuationRule('?'),\n        makePunctuationRule('!'),\n        makePunctuationRule('='),\n        makePunctuationRule(':'),\n        makePunctuationRule('...'),\n        makePunctuationRule('.'),\n        makePunctuationRule('#'),\n        makePunctuationRule('~'),\n        makePunctuationRule('/'),\n        makePunctuationRule('@'),\n        makeKeyWordRule('undefined'),\n        makeKeyWordRule('null'),\n        makeKeyWordRule('function'),\n        makeKeyWordRule('this'),\n        makeKeyWordRule('new'),\n        makeKeyWordRule('module'),\n        makeKeyWordRule('event'),\n        makeKeyWordRule('external'),\n        makeKeyWordRule('typeof'),\n        makeKeyWordRule('keyof'),\n        makeKeyWordRule('readonly'),\n        makeKeyWordRule('import'),\n        makeKeyWordRule('is'),\n        makeKeyWordRule('in'),\n        makeKeyWordRule('asserts'),\n        numberRule,\n        identifierRule,\n        stringValueRule\n    ];\n    const breakingWhitespaceRegex = /^\\s*\\n\\s*/;\n    class Lexer {\n        static create(text) {\n            const current = this.read(text);\n            text = current.text;\n            const next = this.read(text);\n            text = next.text;\n            return new Lexer(text, undefined, current.token, next.token);\n        }\n        constructor(text, previous, current, next) {\n            this.text = '';\n            this.text = text;\n            this.previous = previous;\n            this.current = current;\n            this.next = next;\n        }\n        static read(text, startOfLine = false) {\n            startOfLine = startOfLine || breakingWhitespaceRegex.test(text);\n            text = text.trim();\n            for (const rule of rules) {\n                const partial = rule(text);\n                if (partial !== null) {\n                    const token = Object.assign(Object.assign({}, partial), { startOfLine });\n                    text = text.slice(token.text.length);\n                    return { text, token };\n                }\n            }\n            throw new Error('Unexpected Token ' + text);\n        }\n        advance() {\n            const next = Lexer.read(this.text);\n            return new Lexer(next.text, this.current, this.next, next.token);\n        }\n    }\n\n    /**\n     * Throws an error if the provided result is not a {@link RootResult}\n     */\n    function assertRootResult(result) {\n        if (result === undefined) {\n            throw new Error('Unexpected undefined');\n        }\n        if (result.type === 'JsdocTypeKeyValue' || result.type === 'JsdocTypeParameterList' ||\n            result.type === 'JsdocTypeProperty' || result.type === 'JsdocTypeReadonlyProperty' ||\n            result.type === 'JsdocTypeObjectField' || result.type === 'JsdocTypeJsdocObjectField' ||\n            result.type === 'JsdocTypeIndexSignature' || result.type === 'JsdocTypeMappedType') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function assertPlainKeyValueOrRootResult(result) {\n        if (result.type === 'JsdocTypeKeyValue') {\n            return assertPlainKeyValueResult(result);\n        }\n        return assertRootResult(result);\n    }\n    function assertPlainKeyValueOrNameResult(result) {\n        if (result.type === 'JsdocTypeName') {\n            return result;\n        }\n        return assertPlainKeyValueResult(result);\n    }\n    function assertPlainKeyValueResult(result) {\n        if (result.type !== 'JsdocTypeKeyValue') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function assertNumberOrVariadicNameResult(result) {\n        var _a;\n        if (result.type === 'JsdocTypeVariadic') {\n            if (((_a = result.element) === null || _a === void 0 ? void 0 : _a.type) === 'JsdocTypeName') {\n                return result;\n            }\n            throw new UnexpectedTypeError(result);\n        }\n        if (result.type !== 'JsdocTypeNumber' && result.type !== 'JsdocTypeName') {\n            throw new UnexpectedTypeError(result);\n        }\n        return result;\n    }\n    function isSquaredProperty(result) {\n        return result.type === 'JsdocTypeIndexSignature' || result.type === 'JsdocTypeMappedType';\n    }\n\n    // higher precedence = higher importance\n    var Precedence;\n    (function (Precedence) {\n        Precedence[Precedence[\"ALL\"] = 0] = \"ALL\";\n        Precedence[Precedence[\"PARAMETER_LIST\"] = 1] = \"PARAMETER_LIST\";\n        Precedence[Precedence[\"OBJECT\"] = 2] = \"OBJECT\";\n        Precedence[Precedence[\"KEY_VALUE\"] = 3] = \"KEY_VALUE\";\n        Precedence[Precedence[\"INDEX_BRACKETS\"] = 4] = \"INDEX_BRACKETS\";\n        Precedence[Precedence[\"UNION\"] = 5] = \"UNION\";\n        Precedence[Precedence[\"INTERSECTION\"] = 6] = \"INTERSECTION\";\n        Precedence[Precedence[\"PREFIX\"] = 7] = \"PREFIX\";\n        Precedence[Precedence[\"INFIX\"] = 8] = \"INFIX\";\n        Precedence[Precedence[\"TUPLE\"] = 9] = \"TUPLE\";\n        Precedence[Precedence[\"SYMBOL\"] = 10] = \"SYMBOL\";\n        Precedence[Precedence[\"OPTIONAL\"] = 11] = \"OPTIONAL\";\n        Precedence[Precedence[\"NULLABLE\"] = 12] = \"NULLABLE\";\n        Precedence[Precedence[\"KEY_OF_TYPE_OF\"] = 13] = \"KEY_OF_TYPE_OF\";\n        Precedence[Precedence[\"FUNCTION\"] = 14] = \"FUNCTION\";\n        Precedence[Precedence[\"ARROW\"] = 15] = \"ARROW\";\n        Precedence[Precedence[\"ARRAY_BRACKETS\"] = 16] = \"ARRAY_BRACKETS\";\n        Precedence[Precedence[\"GENERIC\"] = 17] = \"GENERIC\";\n        Precedence[Precedence[\"NAME_PATH\"] = 18] = \"NAME_PATH\";\n        Precedence[Precedence[\"PARENTHESIS\"] = 19] = \"PARENTHESIS\";\n        Precedence[Precedence[\"SPECIAL_TYPES\"] = 20] = \"SPECIAL_TYPES\";\n    })(Precedence || (Precedence = {}));\n\n    class Parser {\n        constructor(grammar, textOrLexer, baseParser) {\n            this.grammar = grammar;\n            if (typeof textOrLexer === 'string') {\n                this._lexer = Lexer.create(textOrLexer);\n            }\n            else {\n                this._lexer = textOrLexer;\n            }\n            this.baseParser = baseParser;\n        }\n        get lexer() {\n            return this._lexer;\n        }\n        /**\n         * Parses a given string and throws an error if the parse ended before the end of the string.\n         */\n        parse() {\n            const result = this.parseType(Precedence.ALL);\n            if (this.lexer.current.type !== 'EOF') {\n                throw new EarlyEndOfParseError(this.lexer.current);\n            }\n            return result;\n        }\n        /**\n         * Parses with the current lexer and asserts that the result is a {@link RootResult}.\n         */\n        parseType(precedence) {\n            return assertRootResult(this.parseIntermediateType(precedence));\n        }\n        /**\n         * The main parsing function. First it tries to parse the current state in the prefix step, and then it continues\n         * to parse the state in the infix step.\n         */\n        parseIntermediateType(precedence) {\n            const result = this.tryParslets(null, precedence);\n            if (result === null) {\n                throw new NoParsletFoundError(this.lexer.current);\n            }\n            return this.parseInfixIntermediateType(result, precedence);\n        }\n        /**\n         * In the infix parsing step the parser continues to parse the current state with all parslets until none returns\n         * a result.\n         */\n        parseInfixIntermediateType(left, precedence) {\n            let result = this.tryParslets(left, precedence);\n            while (result !== null) {\n                left = result;\n                result = this.tryParslets(left, precedence);\n            }\n            return left;\n        }\n        /**\n         * Tries to parse the current state with all parslets in the grammar and returns the first non null result.\n         */\n        tryParslets(left, precedence) {\n            for (const parslet of this.grammar) {\n                const result = parslet(this, precedence, left);\n                if (result !== null) {\n                    return result;\n                }\n            }\n            return null;\n        }\n        /**\n         * If the given type equals the current type of the {@link Lexer} advance the lexer. Return true if the lexer was\n         * advanced.\n         */\n        consume(types) {\n            if (!Array.isArray(types)) {\n                types = [types];\n            }\n            if (types.includes(this.lexer.current.type)) {\n                this._lexer = this.lexer.advance();\n                return true;\n            }\n            else {\n                return false;\n            }\n        }\n        acceptLexerState(parser) {\n            this._lexer = parser.lexer;\n        }\n    }\n\n    function isQuestionMarkUnknownType(next) {\n        return next === 'EOF' || next === '|' || next === ',' || next === ')' || next === '>';\n    }\n\n    const nullableParslet = (parser, precedence, left) => {\n        const type = parser.lexer.current.type;\n        const next = parser.lexer.next.type;\n        const accept = ((left == null) && type === '?' && !isQuestionMarkUnknownType(next)) ||\n            ((left != null) && type === '?');\n        if (!accept) {\n            return null;\n        }\n        parser.consume('?');\n        if (left == null) {\n            return {\n                type: 'JsdocTypeNullable',\n                element: parser.parseType(Precedence.NULLABLE),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        }\n        else {\n            return {\n                type: 'JsdocTypeNullable',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    };\n\n    function composeParslet(options) {\n        const parslet = (parser, curPrecedence, left) => {\n            const type = parser.lexer.current.type;\n            const next = parser.lexer.next.type;\n            if (left === null) {\n                if ('parsePrefix' in options) {\n                    if (options.accept(type, next)) {\n                        return options.parsePrefix(parser);\n                    }\n                }\n            }\n            else {\n                if ('parseInfix' in options) {\n                    if (options.precedence > curPrecedence && options.accept(type, next)) {\n                        return options.parseInfix(parser, left);\n                    }\n                }\n            }\n            return null;\n        };\n        // for debugging\n        Object.defineProperty(parslet, 'name', {\n            value: options.name\n        });\n        return parslet;\n    }\n\n    const optionalParslet = composeParslet({\n        name: 'optionalParslet',\n        accept: type => type === '=',\n        precedence: Precedence.OPTIONAL,\n        parsePrefix: parser => {\n            parser.consume('=');\n            return {\n                type: 'JsdocTypeOptional',\n                element: parser.parseType(Precedence.OPTIONAL),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        },\n        parseInfix: (parser, left) => {\n            parser.consume('=');\n            return {\n                type: 'JsdocTypeOptional',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    });\n\n    const numberParslet = composeParslet({\n        name: 'numberParslet',\n        accept: type => type === 'Number',\n        parsePrefix: parser => {\n            const value = parseFloat(parser.lexer.current.text);\n            parser.consume('Number');\n            return {\n                type: 'JsdocTypeNumber',\n                value\n            };\n        }\n    });\n\n    const parenthesisParslet = composeParslet({\n        name: 'parenthesisParslet',\n        accept: type => type === '(',\n        parsePrefix: parser => {\n            parser.consume('(');\n            if (parser.consume(')')) {\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements: []\n                };\n            }\n            const result = parser.parseIntermediateType(Precedence.ALL);\n            if (!parser.consume(')')) {\n                throw new Error('Unterminated parenthesis');\n            }\n            if (result.type === 'JsdocTypeParameterList') {\n                return result;\n            }\n            else if (result.type === 'JsdocTypeKeyValue') {\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements: [result]\n                };\n            }\n            return {\n                type: 'JsdocTypeParenthesis',\n                element: assertRootResult(result)\n            };\n        }\n    });\n\n    const specialTypesParslet = composeParslet({\n        name: 'specialTypesParslet',\n        accept: (type, next) => (type === '?' && isQuestionMarkUnknownType(next)) ||\n            type === 'null' || type === 'undefined' || type === '*',\n        parsePrefix: parser => {\n            if (parser.consume('null')) {\n                return {\n                    type: 'JsdocTypeNull'\n                };\n            }\n            if (parser.consume('undefined')) {\n                return {\n                    type: 'JsdocTypeUndefined'\n                };\n            }\n            if (parser.consume('*')) {\n                return {\n                    type: 'JsdocTypeAny'\n                };\n            }\n            if (parser.consume('?')) {\n                return {\n                    type: 'JsdocTypeUnknown'\n                };\n            }\n            throw new Error('Unacceptable token: ' + parser.lexer.current.text);\n        }\n    });\n\n    const notNullableParslet = composeParslet({\n        name: 'notNullableParslet',\n        accept: type => type === '!',\n        precedence: Precedence.NULLABLE,\n        parsePrefix: parser => {\n            parser.consume('!');\n            return {\n                type: 'JsdocTypeNotNullable',\n                element: parser.parseType(Precedence.NULLABLE),\n                meta: {\n                    position: 'prefix'\n                }\n            };\n        },\n        parseInfix: (parser, left) => {\n            parser.consume('!');\n            return {\n                type: 'JsdocTypeNotNullable',\n                element: assertRootResult(left),\n                meta: {\n                    position: 'suffix'\n                }\n            };\n        }\n    });\n\n    function createParameterListParslet({ allowTrailingComma }) {\n        return composeParslet({\n            name: 'parameterListParslet',\n            accept: type => type === ',',\n            precedence: Precedence.PARAMETER_LIST,\n            parseInfix: (parser, left) => {\n                const elements = [\n                    assertPlainKeyValueOrRootResult(left)\n                ];\n                parser.consume(',');\n                do {\n                    try {\n                        const next = parser.parseIntermediateType(Precedence.PARAMETER_LIST);\n                        elements.push(assertPlainKeyValueOrRootResult(next));\n                    }\n                    catch (e) {\n                        if (allowTrailingComma && e instanceof NoParsletFoundError) {\n                            break;\n                        }\n                        else {\n                            throw e;\n                        }\n                    }\n                } while (parser.consume(','));\n                if (elements.length > 0 && elements.slice(0, -1).some(e => e.type === 'JsdocTypeVariadic')) {\n                    throw new Error('Only the last parameter may be a rest parameter');\n                }\n                return {\n                    type: 'JsdocTypeParameterList',\n                    elements\n                };\n            }\n        });\n    }\n\n    const genericParslet = composeParslet({\n        name: 'genericParslet',\n        accept: (type, next) => type === '<' || (type === '.' && next === '<'),\n        precedence: Precedence.GENERIC,\n        parseInfix: (parser, left) => {\n            const dot = parser.consume('.');\n            parser.consume('<');\n            const objects = [];\n            do {\n                objects.push(parser.parseType(Precedence.PARAMETER_LIST));\n            } while (parser.consume(','));\n            if (!parser.consume('>')) {\n                throw new Error('Unterminated generic parameter list');\n            }\n            return {\n                type: 'JsdocTypeGeneric',\n                left: assertRootResult(left),\n                elements: objects,\n                meta: {\n                    brackets: 'angle',\n                    dot\n                }\n            };\n        }\n    });\n\n    const unionParslet = composeParslet({\n        name: 'unionParslet',\n        accept: type => type === '|',\n        precedence: Precedence.UNION,\n        parseInfix: (parser, left) => {\n            parser.consume('|');\n            const elements = [];\n            do {\n                elements.push(parser.parseType(Precedence.UNION));\n            } while (parser.consume('|'));\n            return {\n                type: 'JsdocTypeUnion',\n                elements: [assertRootResult(left), ...elements]\n            };\n        }\n    });\n\n    const baseGrammar = [\n        nullableParslet,\n        optionalParslet,\n        numberParslet,\n        parenthesisParslet,\n        specialTypesParslet,\n        notNullableParslet,\n        createParameterListParslet({\n            allowTrailingComma: true\n        }),\n        genericParslet,\n        unionParslet,\n        optionalParslet\n    ];\n\n    function createNamePathParslet({ allowSquareBracketsOnAnyType, allowJsdocNamePaths, pathGrammar }) {\n        return function namePathParslet(parser, precedence, left) {\n            if ((left == null) || precedence >= Precedence.NAME_PATH) {\n                return null;\n            }\n            const type = parser.lexer.current.type;\n            const next = parser.lexer.next.type;\n            const accept = (type === '.' && next !== '<') ||\n                (type === '[' && (allowSquareBracketsOnAnyType || left.type === 'JsdocTypeName')) ||\n                (allowJsdocNamePaths && (type === '~' || type === '#'));\n            if (!accept) {\n                return null;\n            }\n            let pathType;\n            let brackets = false;\n            if (parser.consume('.')) {\n                pathType = 'property';\n            }\n            else if (parser.consume('[')) {\n                pathType = 'property-brackets';\n                brackets = true;\n            }\n            else if (parser.consume('~')) {\n                pathType = 'inner';\n            }\n            else {\n                parser.consume('#');\n                pathType = 'instance';\n            }\n            const pathParser = pathGrammar !== null\n                ? new Parser(pathGrammar, parser.lexer, parser)\n                : parser;\n            const parsed = pathParser.parseIntermediateType(Precedence.NAME_PATH);\n            parser.acceptLexerState(pathParser);\n            let right;\n            switch (parsed.type) {\n                case 'JsdocTypeName':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value,\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                    break;\n                case 'JsdocTypeNumber':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value.toString(10),\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                    break;\n                case 'JsdocTypeStringValue':\n                    right = {\n                        type: 'JsdocTypeProperty',\n                        value: parsed.value,\n                        meta: {\n                            quote: parsed.meta.quote\n                        }\n                    };\n                    break;\n                case 'JsdocTypeSpecialNamePath':\n                    if (parsed.specialType === 'event') {\n                        right = parsed;\n                    }\n                    else {\n                        throw new UnexpectedTypeError(parsed, 'Type \\'JsdocTypeSpecialNamePath\\' is only allowed with specialType \\'event\\'');\n                    }\n                    break;\n                default:\n                    throw new UnexpectedTypeError(parsed, 'Expecting \\'JsdocTypeName\\', \\'JsdocTypeNumber\\', \\'JsdocStringValue\\' or \\'JsdocTypeSpecialNamePath\\'');\n            }\n            if (brackets && !parser.consume(']')) {\n                const token = parser.lexer.current;\n                throw new Error(`Unterminated square brackets. Next token is '${token.type}' ` +\n                    `with text '${token.text}'`);\n            }\n            return {\n                type: 'JsdocTypeNamePath',\n                left: assertRootResult(left),\n                right,\n                pathType\n            };\n        };\n    }\n\n    function createNameParslet({ allowedAdditionalTokens }) {\n        return composeParslet({\n            name: 'nameParslet',\n            accept: type => type === 'Identifier' || type === 'this' || type === 'new' || allowedAdditionalTokens.includes(type),\n            parsePrefix: parser => {\n                const { type, text } = parser.lexer.current;\n                parser.consume(type);\n                return {\n                    type: 'JsdocTypeName',\n                    value: text\n                };\n            }\n        });\n    }\n\n    const stringValueParslet = composeParslet({\n        name: 'stringValueParslet',\n        accept: type => type === 'StringValue',\n        parsePrefix: parser => {\n            const text = parser.lexer.current.text;\n            parser.consume('StringValue');\n            return {\n                type: 'JsdocTypeStringValue',\n                value: text.slice(1, -1),\n                meta: {\n                    quote: text[0] === '\\'' ? 'single' : 'double'\n                }\n            };\n        }\n    });\n\n    function createSpecialNamePathParslet({ pathGrammar, allowedTypes }) {\n        return composeParslet({\n            name: 'specialNamePathParslet',\n            accept: type => allowedTypes.includes(type),\n            parsePrefix: parser => {\n                const type = parser.lexer.current.type;\n                parser.consume(type);\n                if (!parser.consume(':')) {\n                    return {\n                        type: 'JsdocTypeName',\n                        value: type\n                    };\n                }\n                let result;\n                let token = parser.lexer.current;\n                if (parser.consume('StringValue')) {\n                    result = {\n                        type: 'JsdocTypeSpecialNamePath',\n                        value: token.text.slice(1, -1),\n                        specialType: type,\n                        meta: {\n                            quote: token.text[0] === '\\'' ? 'single' : 'double'\n                        }\n                    };\n                }\n                else {\n                    let value = '';\n                    const allowed = ['Identifier', '@', '/'];\n                    while (allowed.some(type => parser.consume(type))) {\n                        value += token.text;\n                        token = parser.lexer.current;\n                    }\n                    result = {\n                        type: 'JsdocTypeSpecialNamePath',\n                        value,\n                        specialType: type,\n                        meta: {\n                            quote: undefined\n                        }\n                    };\n                }\n                const moduleParser = new Parser(pathGrammar, parser.lexer, parser);\n                const moduleResult = moduleParser.parseInfixIntermediateType(result, Precedence.ALL);\n                parser.acceptLexerState(moduleParser);\n                return assertRootResult(moduleResult);\n            }\n        });\n    }\n\n    const basePathGrammar = [\n        createNameParslet({\n            allowedAdditionalTokens: ['external', 'module']\n        }),\n        stringValueParslet,\n        numberParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar: null\n        })\n    ];\n    const pathGrammar = [\n        ...basePathGrammar,\n        createSpecialNamePathParslet({\n            allowedTypes: ['event'],\n            pathGrammar: basePathGrammar\n        })\n    ];\n\n    function getParameters(value) {\n        let parameters;\n        if (value.type === 'JsdocTypeParameterList') {\n            parameters = value.elements;\n        }\n        else if (value.type === 'JsdocTypeParenthesis') {\n            parameters = [value.element];\n        }\n        else {\n            throw new UnexpectedTypeError(value);\n        }\n        return parameters.map(p => assertPlainKeyValueOrRootResult(p));\n    }\n    function getUnnamedParameters(value) {\n        const parameters = getParameters(value);\n        if (parameters.some(p => p.type === 'JsdocTypeKeyValue')) {\n            throw new Error('No parameter should be named');\n        }\n        return parameters;\n    }\n    function createFunctionParslet({ allowNamedParameters, allowNoReturnType, allowWithoutParenthesis, allowNewAsFunctionKeyword }) {\n        return composeParslet({\n            name: 'functionParslet',\n            accept: (type, next) => type === 'function' || (allowNewAsFunctionKeyword && type === 'new' && next === '('),\n            parsePrefix: parser => {\n                const newKeyword = parser.consume('new');\n                parser.consume('function');\n                const hasParenthesis = parser.lexer.current.type === '(';\n                if (!hasParenthesis) {\n                    if (!allowWithoutParenthesis) {\n                        throw new Error('function is missing parameter list');\n                    }\n                    return {\n                        type: 'JsdocTypeName',\n                        value: 'function'\n                    };\n                }\n                let result = {\n                    type: 'JsdocTypeFunction',\n                    parameters: [],\n                    arrow: false,\n                    constructor: newKeyword,\n                    parenthesis: hasParenthesis\n                };\n                const value = parser.parseIntermediateType(Precedence.FUNCTION);\n                if (allowNamedParameters === undefined) {\n                    result.parameters = getUnnamedParameters(value);\n                }\n                else if (newKeyword && value.type === 'JsdocTypeFunction' && value.arrow) {\n                    result = value;\n                    result.constructor = true;\n                    return result;\n                }\n                else {\n                    result.parameters = getParameters(value);\n                    for (const p of result.parameters) {\n                        if (p.type === 'JsdocTypeKeyValue' && (!allowNamedParameters.includes(p.key))) {\n                            throw new Error(`only allowed named parameters are ${allowNamedParameters.join(', ')} but got ${p.type}`);\n                        }\n                    }\n                }\n                if (parser.consume(':')) {\n                    result.returnType = parser.parseType(Precedence.PREFIX);\n                }\n                else {\n                    if (!allowNoReturnType) {\n                        throw new Error('function is missing return type');\n                    }\n                }\n                return result;\n            }\n        });\n    }\n\n    function createVariadicParslet({ allowPostfix, allowEnclosingBrackets }) {\n        return composeParslet({\n            name: 'variadicParslet',\n            accept: type => type === '...',\n            precedence: Precedence.PREFIX,\n            parsePrefix: parser => {\n                parser.consume('...');\n                const brackets = allowEnclosingBrackets && parser.consume('[');\n                try {\n                    const element = parser.parseType(Precedence.PREFIX);\n                    if (brackets && !parser.consume(']')) {\n                        throw new Error('Unterminated variadic type. Missing \\']\\'');\n                    }\n                    return {\n                        type: 'JsdocTypeVariadic',\n                        element: assertRootResult(element),\n                        meta: {\n                            position: 'prefix',\n                            squareBrackets: brackets\n                        }\n                    };\n                }\n                catch (e) {\n                    if (e instanceof NoParsletFoundError) {\n                        if (brackets) {\n                            throw new Error('Empty square brackets for variadic are not allowed.');\n                        }\n                        return {\n                            type: 'JsdocTypeVariadic',\n                            meta: {\n                                position: undefined,\n                                squareBrackets: false\n                            }\n                        };\n                    }\n                    else {\n                        throw e;\n                    }\n                }\n            },\n            parseInfix: allowPostfix\n                ? (parser, left) => {\n                    parser.consume('...');\n                    return {\n                        type: 'JsdocTypeVariadic',\n                        element: assertRootResult(left),\n                        meta: {\n                            position: 'suffix',\n                            squareBrackets: false\n                        }\n                    };\n                }\n                : undefined\n        });\n    }\n\n    const symbolParslet = composeParslet({\n        name: 'symbolParslet',\n        accept: type => type === '(',\n        precedence: Precedence.SYMBOL,\n        parseInfix: (parser, left) => {\n            if (left.type !== 'JsdocTypeName') {\n                throw new Error('Symbol expects a name on the left side. (Reacting on \\'(\\')');\n            }\n            parser.consume('(');\n            const result = {\n                type: 'JsdocTypeSymbol',\n                value: left.value\n            };\n            if (!parser.consume(')')) {\n                const next = parser.parseIntermediateType(Precedence.SYMBOL);\n                result.element = assertNumberOrVariadicNameResult(next);\n                if (!parser.consume(')')) {\n                    throw new Error('Symbol does not end after value');\n                }\n            }\n            return result;\n        }\n    });\n\n    const arrayBracketsParslet = composeParslet({\n        name: 'arrayBracketsParslet',\n        precedence: Precedence.ARRAY_BRACKETS,\n        accept: (type, next) => type === '[' && next === ']',\n        parseInfix: (parser, left) => {\n            parser.consume('[');\n            parser.consume(']');\n            return {\n                type: 'JsdocTypeGeneric',\n                left: {\n                    type: 'JsdocTypeName',\n                    value: 'Array'\n                },\n                elements: [\n                    assertRootResult(left)\n                ],\n                meta: {\n                    brackets: 'square',\n                    dot: false\n                }\n            };\n        }\n    });\n\n    function createObjectParslet({ objectFieldGrammar, allowKeyTypes }) {\n        return composeParslet({\n            name: 'objectParslet',\n            accept: type => type === '{',\n            parsePrefix: parser => {\n                parser.consume('{');\n                const result = {\n                    type: 'JsdocTypeObject',\n                    meta: {\n                        separator: 'comma'\n                    },\n                    elements: []\n                };\n                if (!parser.consume('}')) {\n                    let separator;\n                    const fieldParser = new Parser(objectFieldGrammar, parser.lexer, parser);\n                    while (true) {\n                        fieldParser.acceptLexerState(parser);\n                        let field = fieldParser.parseIntermediateType(Precedence.OBJECT);\n                        parser.acceptLexerState(fieldParser);\n                        if (field === undefined && allowKeyTypes) {\n                            field = parser.parseIntermediateType(Precedence.OBJECT);\n                        }\n                        let optional = false;\n                        if (field.type === 'JsdocTypeNullable') {\n                            optional = true;\n                            field = field.element;\n                        }\n                        if (field.type === 'JsdocTypeNumber' || field.type === 'JsdocTypeName' || field.type === 'JsdocTypeStringValue') {\n                            let quote;\n                            if (field.type === 'JsdocTypeStringValue') {\n                                quote = field.meta.quote;\n                            }\n                            result.elements.push({\n                                type: 'JsdocTypeObjectField',\n                                key: field.value.toString(),\n                                right: undefined,\n                                optional,\n                                readonly: false,\n                                meta: {\n                                    quote\n                                }\n                            });\n                        }\n                        else if (field.type === 'JsdocTypeObjectField' || field.type === 'JsdocTypeJsdocObjectField') {\n                            result.elements.push(field);\n                        }\n                        else {\n                            throw new UnexpectedTypeError(field);\n                        }\n                        if (parser.lexer.current.startOfLine) {\n                            separator = 'linebreak';\n                        }\n                        else if (parser.consume(',')) {\n                            separator = 'comma';\n                        }\n                        else if (parser.consume(';')) {\n                            separator = 'semicolon';\n                        }\n                        else {\n                            break;\n                        }\n                        const type = parser.lexer.current.type;\n                        if (type === '}') {\n                            break;\n                        }\n                    }\n                    result.meta.separator = separator !== null && separator !== void 0 ? separator : 'comma'; // TODO: use undefined here\n                    if (!parser.consume('}')) {\n                        throw new Error('Unterminated record type. Missing \\'}\\'');\n                    }\n                }\n                return result;\n            }\n        });\n    }\n\n    function createObjectFieldParslet({ allowSquaredProperties, allowKeyTypes, allowReadonly, allowOptional }) {\n        return composeParslet({\n            name: 'objectFieldParslet',\n            precedence: Precedence.KEY_VALUE,\n            accept: type => type === ':',\n            parseInfix: (parser, left) => {\n                var _a;\n                let optional = false;\n                let readonlyProperty = false;\n                if (allowOptional && left.type === 'JsdocTypeNullable') {\n                    optional = true;\n                    left = left.element;\n                }\n                if (allowReadonly && left.type === 'JsdocTypeReadonlyProperty') {\n                    readonlyProperty = true;\n                    left = left.element;\n                }\n                // object parslet uses a special grammar and for the value we want to switch back to the parent\n                const parentParser = (_a = parser.baseParser) !== null && _a !== void 0 ? _a : parser;\n                parentParser.acceptLexerState(parser);\n                if (left.type === 'JsdocTypeNumber' || left.type === 'JsdocTypeName' || left.type === 'JsdocTypeStringValue' ||\n                    isSquaredProperty(left)) {\n                    if (isSquaredProperty(left) && !allowSquaredProperties) {\n                        throw new UnexpectedTypeError(left);\n                    }\n                    parentParser.consume(':');\n                    let quote;\n                    if (left.type === 'JsdocTypeStringValue') {\n                        quote = left.meta.quote;\n                    }\n                    const right = parentParser.parseType(Precedence.KEY_VALUE);\n                    parser.acceptLexerState(parentParser);\n                    return {\n                        type: 'JsdocTypeObjectField',\n                        key: isSquaredProperty(left) ? left : left.value.toString(),\n                        right,\n                        optional,\n                        readonly: readonlyProperty,\n                        meta: {\n                            quote\n                        }\n                    };\n                }\n                else {\n                    if (!allowKeyTypes) {\n                        throw new UnexpectedTypeError(left);\n                    }\n                    parentParser.consume(':');\n                    const right = parentParser.parseType(Precedence.KEY_VALUE);\n                    parser.acceptLexerState(parentParser);\n                    return {\n                        type: 'JsdocTypeJsdocObjectField',\n                        left: assertRootResult(left),\n                        right\n                    };\n                }\n            }\n        });\n    }\n\n    function createKeyValueParslet({ allowOptional, allowVariadic }) {\n        return composeParslet({\n            name: 'keyValueParslet',\n            precedence: Precedence.KEY_VALUE,\n            accept: type => type === ':',\n            parseInfix: (parser, left) => {\n                let optional = false;\n                let variadic = false;\n                if (allowOptional && left.type === 'JsdocTypeNullable') {\n                    optional = true;\n                    left = left.element;\n                }\n                if (allowVariadic && left.type === 'JsdocTypeVariadic' && left.element !== undefined) {\n                    variadic = true;\n                    left = left.element;\n                }\n                if (left.type !== 'JsdocTypeName') {\n                    throw new UnexpectedTypeError(left);\n                }\n                parser.consume(':');\n                const right = parser.parseType(Precedence.KEY_VALUE);\n                return {\n                    type: 'JsdocTypeKeyValue',\n                    key: left.value,\n                    right,\n                    optional,\n                    variadic\n                };\n            }\n        });\n    }\n\n    const jsdocBaseGrammar = [\n        ...baseGrammar,\n        createFunctionParslet({\n            allowWithoutParenthesis: true,\n            allowNamedParameters: ['this', 'new'],\n            allowNoReturnType: true,\n            allowNewAsFunctionKeyword: false\n        }),\n        stringValueParslet,\n        createSpecialNamePathParslet({\n            allowedTypes: ['module', 'external', 'event'],\n            pathGrammar\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: true,\n            allowPostfix: true\n        }),\n        createNameParslet({\n            allowedAdditionalTokens: ['keyof']\n        }),\n        symbolParslet,\n        arrayBracketsParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar\n        })\n    ];\n    const jsdocGrammar = [\n        ...jsdocBaseGrammar,\n        createObjectParslet({\n            // jsdoc syntax allows full types as keys, so we need to pull in the full grammar here\n            // we leave out the object type deliberately\n            objectFieldGrammar: [\n                createNameParslet({\n                    allowedAdditionalTokens: ['module', 'in']\n                }),\n                createObjectFieldParslet({\n                    allowSquaredProperties: false,\n                    allowKeyTypes: true,\n                    allowOptional: false,\n                    allowReadonly: false\n                }),\n                ...jsdocBaseGrammar\n            ],\n            allowKeyTypes: true\n        }),\n        createKeyValueParslet({\n            allowOptional: true,\n            allowVariadic: true\n        })\n    ];\n\n    const typeOfParslet = composeParslet({\n        name: 'typeOfParslet',\n        accept: type => type === 'typeof',\n        parsePrefix: parser => {\n            parser.consume('typeof');\n            return {\n                type: 'JsdocTypeTypeof',\n                element: assertRootResult(parser.parseType(Precedence.KEY_OF_TYPE_OF))\n            };\n        }\n    });\n\n    const objectFieldGrammar$1 = [\n        createNameParslet({\n            allowedAdditionalTokens: ['module', 'keyof', 'event', 'external', 'in']\n        }),\n        nullableParslet,\n        optionalParslet,\n        stringValueParslet,\n        numberParslet,\n        createObjectFieldParslet({\n            allowSquaredProperties: false,\n            allowKeyTypes: false,\n            allowOptional: false,\n            allowReadonly: false\n        })\n    ];\n    const closureGrammar = [\n        ...baseGrammar,\n        createObjectParslet({\n            allowKeyTypes: false,\n            objectFieldGrammar: objectFieldGrammar$1\n        }),\n        createNameParslet({\n            allowedAdditionalTokens: ['event', 'external', 'in']\n        }),\n        typeOfParslet,\n        createFunctionParslet({\n            allowWithoutParenthesis: false,\n            allowNamedParameters: ['this', 'new'],\n            allowNoReturnType: true,\n            allowNewAsFunctionKeyword: false\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: false,\n            allowPostfix: false\n        }),\n        // additional name parslet is needed for some special cases\n        createNameParslet({\n            allowedAdditionalTokens: ['keyof']\n        }),\n        createSpecialNamePathParslet({\n            allowedTypes: ['module'],\n            pathGrammar\n        }),\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: false,\n            allowJsdocNamePaths: true,\n            pathGrammar\n        }),\n        createKeyValueParslet({\n            allowOptional: false,\n            allowVariadic: false\n        }),\n        symbolParslet\n    ];\n\n    const assertsParslet = composeParslet({\n        name: 'assertsParslet',\n        accept: type => type === 'asserts',\n        parsePrefix: (parser) => {\n            parser.consume('asserts');\n            const left = parser.parseIntermediateType(Precedence.SYMBOL);\n            if (left.type !== 'JsdocTypeName') {\n                throw new UnexpectedTypeError(left, 'A typescript asserts always has to have a name on the left side.');\n            }\n            parser.consume('is');\n            return {\n                type: 'JsdocTypeAsserts',\n                left,\n                right: assertRootResult(parser.parseIntermediateType(Precedence.INFIX))\n            };\n        }\n    });\n\n    function createTupleParslet({ allowQuestionMark }) {\n        return composeParslet({\n            name: 'tupleParslet',\n            accept: type => type === '[',\n            parsePrefix: parser => {\n                parser.consume('[');\n                const result = {\n                    type: 'JsdocTypeTuple',\n                    elements: []\n                };\n                if (parser.consume(']')) {\n                    return result;\n                }\n                const typeList = parser.parseIntermediateType(Precedence.ALL);\n                if (typeList.type === 'JsdocTypeParameterList') {\n                    if (typeList.elements[0].type === 'JsdocTypeKeyValue') {\n                        result.elements = typeList.elements.map(assertPlainKeyValueResult);\n                    }\n                    else {\n                        result.elements = typeList.elements.map(assertRootResult);\n                    }\n                }\n                else {\n                    if (typeList.type === 'JsdocTypeKeyValue') {\n                        result.elements = [assertPlainKeyValueResult(typeList)];\n                    }\n                    else {\n                        result.elements = [assertRootResult(typeList)];\n                    }\n                }\n                if (!parser.consume(']')) {\n                    throw new Error('Unterminated \\'[\\'');\n                }\n                if (!allowQuestionMark && result.elements.some((e) => e.type === 'JsdocTypeUnknown')) {\n                    throw new Error('Question mark in tuple not allowed');\n                }\n                return result;\n            }\n        });\n    }\n\n    const keyOfParslet = composeParslet({\n        name: 'keyOfParslet',\n        accept: type => type === 'keyof',\n        parsePrefix: parser => {\n            parser.consume('keyof');\n            return {\n                type: 'JsdocTypeKeyof',\n                element: assertRootResult(parser.parseType(Precedence.KEY_OF_TYPE_OF))\n            };\n        }\n    });\n\n    const importParslet = composeParslet({\n        name: 'importParslet',\n        accept: type => type === 'import',\n        parsePrefix: parser => {\n            parser.consume('import');\n            if (!parser.consume('(')) {\n                throw new Error('Missing parenthesis after import keyword');\n            }\n            const path = parser.parseType(Precedence.PREFIX);\n            if (path.type !== 'JsdocTypeStringValue') {\n                throw new Error('Only string values are allowed as paths for imports');\n            }\n            if (!parser.consume(')')) {\n                throw new Error('Missing closing parenthesis after import keyword');\n            }\n            return {\n                type: 'JsdocTypeImport',\n                element: path\n            };\n        }\n    });\n\n    const readonlyPropertyParslet = composeParslet({\n        name: 'readonlyPropertyParslet',\n        accept: type => type === 'readonly',\n        parsePrefix: parser => {\n            parser.consume('readonly');\n            return {\n                type: 'JsdocTypeReadonlyProperty',\n                element: parser.parseType(Precedence.KEY_VALUE)\n            };\n        }\n    });\n\n    const arrowFunctionParslet = composeParslet({\n        name: 'arrowFunctionParslet',\n        precedence: Precedence.ARROW,\n        accept: type => type === '=>',\n        parseInfix: (parser, left) => {\n            parser.consume('=>');\n            return {\n                type: 'JsdocTypeFunction',\n                parameters: getParameters(left).map(assertPlainKeyValueOrNameResult),\n                arrow: true,\n                constructor: false,\n                parenthesis: true,\n                returnType: parser.parseType(Precedence.OBJECT)\n            };\n        }\n    });\n\n    const intersectionParslet = composeParslet({\n        name: 'intersectionParslet',\n        accept: type => type === '&',\n        precedence: Precedence.INTERSECTION,\n        parseInfix: (parser, left) => {\n            parser.consume('&');\n            const elements = [];\n            do {\n                elements.push(parser.parseType(Precedence.INTERSECTION));\n            } while (parser.consume('&'));\n            return {\n                type: 'JsdocTypeIntersection',\n                elements: [assertRootResult(left), ...elements]\n            };\n        }\n    });\n\n    const predicateParslet = composeParslet({\n        name: 'predicateParslet',\n        precedence: Precedence.INFIX,\n        accept: type => type === 'is',\n        parseInfix: (parser, left) => {\n            if (left.type !== 'JsdocTypeName') {\n                throw new UnexpectedTypeError(left, 'A typescript predicate always has to have a name on the left side.');\n            }\n            parser.consume('is');\n            return {\n                type: 'JsdocTypePredicate',\n                left,\n                right: assertRootResult(parser.parseIntermediateType(Precedence.INFIX))\n            };\n        }\n    });\n\n    const objectSquaredPropertyParslet = composeParslet({\n        name: 'objectSquareBracketPropertyParslet',\n        accept: type => type === '[',\n        parsePrefix: parser => {\n            if (parser.baseParser === undefined) {\n                throw new Error('Only allowed inside object grammar');\n            }\n            parser.consume('[');\n            const key = parser.lexer.current.text;\n            parser.consume('Identifier');\n            let result;\n            if (parser.consume(':')) {\n                const parentParser = parser.baseParser;\n                parentParser.acceptLexerState(parser);\n                result = {\n                    type: 'JsdocTypeIndexSignature',\n                    key,\n                    right: parentParser.parseType(Precedence.INDEX_BRACKETS)\n                };\n                parser.acceptLexerState(parentParser);\n            }\n            else if (parser.consume('in')) {\n                const parentParser = parser.baseParser;\n                parentParser.acceptLexerState(parser);\n                result = {\n                    type: 'JsdocTypeMappedType',\n                    key,\n                    right: parentParser.parseType(Precedence.ARRAY_BRACKETS)\n                };\n                parser.acceptLexerState(parentParser);\n            }\n            else {\n                throw new Error('Missing \\':\\' or \\'in\\' inside square bracketed property.');\n            }\n            if (!parser.consume(']')) {\n                throw new Error('Unterminated square brackets');\n            }\n            return result;\n        }\n    });\n\n    const objectFieldGrammar = [\n        readonlyPropertyParslet,\n        createNameParslet({\n            allowedAdditionalTokens: ['module', 'event', 'keyof', 'event', 'external', 'in']\n        }),\n        nullableParslet,\n        optionalParslet,\n        stringValueParslet,\n        numberParslet,\n        createObjectFieldParslet({\n            allowSquaredProperties: true,\n            allowKeyTypes: false,\n            allowOptional: true,\n            allowReadonly: true\n        }),\n        objectSquaredPropertyParslet\n    ];\n    const typescriptGrammar = [\n        ...baseGrammar,\n        createObjectParslet({\n            allowKeyTypes: false,\n            objectFieldGrammar\n        }),\n        typeOfParslet,\n        keyOfParslet,\n        importParslet,\n        stringValueParslet,\n        createFunctionParslet({\n            allowWithoutParenthesis: true,\n            allowNoReturnType: false,\n            allowNamedParameters: ['this', 'new', 'args'],\n            allowNewAsFunctionKeyword: true\n        }),\n        createTupleParslet({\n            allowQuestionMark: false\n        }),\n        createVariadicParslet({\n            allowEnclosingBrackets: false,\n            allowPostfix: false\n        }),\n        assertsParslet,\n        createNameParslet({\n            allowedAdditionalTokens: ['event', 'external', 'in']\n        }),\n        createSpecialNamePathParslet({\n            allowedTypes: ['module'],\n            pathGrammar\n        }),\n        arrayBracketsParslet,\n        arrowFunctionParslet,\n        createNamePathParslet({\n            allowSquareBracketsOnAnyType: true,\n            allowJsdocNamePaths: false,\n            pathGrammar\n        }),\n        intersectionParslet,\n        predicateParslet,\n        createKeyValueParslet({\n            allowVariadic: true,\n            allowOptional: true\n        })\n    ];\n\n    /**\n     * This function parses the given expression in the given mode and produces a {@link RootResult}.\n     * @param expression\n     * @param mode\n     */\n    function parse(expression, mode) {\n        switch (mode) {\n            case 'closure':\n                return (new Parser(closureGrammar, expression)).parse();\n            case 'jsdoc':\n                return (new Parser(jsdocGrammar, expression)).parse();\n            case 'typescript':\n                return (new Parser(typescriptGrammar, expression)).parse();\n        }\n    }\n    /**\n     * This function tries to parse the given expression in multiple modes and returns the first successful\n     * {@link RootResult}. By default it tries `'typescript'`, `'closure'` and `'jsdoc'` in this order. If\n     * no mode was successful it throws the error that was produced by the last parsing attempt.\n     * @param expression\n     * @param modes\n     */\n    function tryParse(expression, modes = ['typescript', 'closure', 'jsdoc']) {\n        let error;\n        for (const mode of modes) {\n            try {\n                return parse(expression, mode);\n            }\n            catch (e) {\n                error = e;\n            }\n        }\n        throw error;\n    }\n\n    function transform(rules, parseResult) {\n        const rule = rules[parseResult.type];\n        if (rule === undefined) {\n            throw new Error(`In this set of transform rules exists no rule for type ${parseResult.type}.`);\n        }\n        return rule(parseResult, aParseResult => transform(rules, aParseResult));\n    }\n    function notAvailableTransform(parseResult) {\n        throw new Error('This transform is not available. Are you trying the correct parsing mode?');\n    }\n    function extractSpecialParams(source) {\n        const result = {\n            params: []\n        };\n        for (const param of source.parameters) {\n            if (param.type === 'JsdocTypeKeyValue') {\n                if (param.key === 'this') {\n                    result.this = param.right;\n                }\n                else if (param.key === 'new') {\n                    result.new = param.right;\n                }\n                else {\n                    result.params.push(param);\n                }\n            }\n            else {\n                result.params.push(param);\n            }\n        }\n        return result;\n    }\n\n    function applyPosition(position, target, value) {\n        return position === 'prefix' ? value + target : target + value;\n    }\n    function quote(value, quote) {\n        switch (quote) {\n            case 'double':\n                return `\"${value}\"`;\n            case 'single':\n                return `'${value}'`;\n            case undefined:\n                return value;\n        }\n    }\n    function stringifyRules() {\n        return {\n            JsdocTypeParenthesis: (result, transform) => `(${result.element !== undefined ? transform(result.element) : ''})`,\n            JsdocTypeKeyof: (result, transform) => `keyof ${transform(result.element)}`,\n            JsdocTypeFunction: (result, transform) => {\n                if (!result.arrow) {\n                    let stringified = result.constructor ? 'new' : 'function';\n                    if (!result.parenthesis) {\n                        return stringified;\n                    }\n                    stringified += `(${result.parameters.map(transform).join(', ')})`;\n                    if (result.returnType !== undefined) {\n                        stringified += `: ${transform(result.returnType)}`;\n                    }\n                    return stringified;\n                }\n                else {\n                    if (result.returnType === undefined) {\n                        throw new Error('Arrow function needs a return type.');\n                    }\n                    let stringified = `(${result.parameters.map(transform).join(', ')}) => ${transform(result.returnType)}`;\n                    if (result.constructor) {\n                        stringified = 'new ' + stringified;\n                    }\n                    return stringified;\n                }\n            },\n            JsdocTypeName: result => result.value,\n            JsdocTypeTuple: (result, transform) => `[${result.elements.map(transform).join(', ')}]`,\n            JsdocTypeVariadic: (result, transform) => result.meta.position === undefined\n                ? '...'\n                : applyPosition(result.meta.position, transform(result.element), '...'),\n            JsdocTypeNamePath: (result, transform) => {\n                const left = transform(result.left);\n                const right = transform(result.right);\n                switch (result.pathType) {\n                    case 'inner':\n                        return `${left}~${right}`;\n                    case 'instance':\n                        return `${left}#${right}`;\n                    case 'property':\n                        return `${left}.${right}`;\n                    case 'property-brackets':\n                        return `${left}[${right}]`;\n                }\n            },\n            JsdocTypeStringValue: result => quote(result.value, result.meta.quote),\n            JsdocTypeAny: () => '*',\n            JsdocTypeGeneric: (result, transform) => {\n                if (result.meta.brackets === 'square') {\n                    const element = result.elements[0];\n                    const transformed = transform(element);\n                    if (element.type === 'JsdocTypeUnion' || element.type === 'JsdocTypeIntersection') {\n                        return `(${transformed})[]`;\n                    }\n                    else {\n                        return `${transformed}[]`;\n                    }\n                }\n                else {\n                    return `${transform(result.left)}${result.meta.dot ? '.' : ''}<${result.elements.map(transform).join(', ')}>`;\n                }\n            },\n            JsdocTypeImport: (result, transform) => `import(${transform(result.element)})`,\n            JsdocTypeObjectField: (result, transform) => {\n                let text = '';\n                if (result.readonly) {\n                    text += 'readonly ';\n                }\n                if (typeof result.key === 'string') {\n                    text += quote(result.key, result.meta.quote);\n                }\n                else {\n                    text += transform(result.key);\n                }\n                if (result.optional) {\n                    text += '?';\n                }\n                if (result.right === undefined) {\n                    return text;\n                }\n                else {\n                    return text + `: ${transform(result.right)}`;\n                }\n            },\n            JsdocTypeJsdocObjectField: (result, transform) => {\n                return `${transform(result.left)}: ${transform(result.right)}`;\n            },\n            JsdocTypeKeyValue: (result, transform) => {\n                let text = result.key;\n                if (result.optional) {\n                    text += '?';\n                }\n                if (result.variadic) {\n                    text = '...' + text;\n                }\n                if (result.right === undefined) {\n                    return text;\n                }\n                else {\n                    return text + `: ${transform(result.right)}`;\n                }\n            },\n            JsdocTypeSpecialNamePath: result => `${result.specialType}:${quote(result.value, result.meta.quote)}`,\n            JsdocTypeNotNullable: (result, transform) => applyPosition(result.meta.position, transform(result.element), '!'),\n            JsdocTypeNull: () => 'null',\n            JsdocTypeNullable: (result, transform) => applyPosition(result.meta.position, transform(result.element), '?'),\n            JsdocTypeNumber: result => result.value.toString(),\n            JsdocTypeObject: (result, transform) => `{${result.elements.map(transform).join((result.meta.separator === 'comma' ? ',' : ';') + ' ')}}`,\n            JsdocTypeOptional: (result, transform) => applyPosition(result.meta.position, transform(result.element), '='),\n            JsdocTypeSymbol: (result, transform) => `${result.value}(${result.element !== undefined ? transform(result.element) : ''})`,\n            JsdocTypeTypeof: (result, transform) => `typeof ${transform(result.element)}`,\n            JsdocTypeUndefined: () => 'undefined',\n            JsdocTypeUnion: (result, transform) => result.elements.map(transform).join(' | '),\n            JsdocTypeUnknown: () => '?',\n            JsdocTypeIntersection: (result, transform) => result.elements.map(transform).join(' & '),\n            JsdocTypeProperty: result => quote(result.value, result.meta.quote),\n            JsdocTypePredicate: (result, transform) => `${transform(result.left)} is ${transform(result.right)}`,\n            JsdocTypeIndexSignature: (result, transform) => `[${result.key}: ${transform(result.right)}]`,\n            JsdocTypeMappedType: (result, transform) => `[${result.key} in ${transform(result.right)}]`,\n            JsdocTypeAsserts: (result, transform) => `asserts ${transform(result.left)} is ${transform(result.right)}`\n        };\n    }\n    const storedStringifyRules = stringifyRules();\n    function stringify(result) {\n        return transform(storedStringifyRules, result);\n    }\n\n    const reservedWords = [\n        'null',\n        'true',\n        'false',\n        'break',\n        'case',\n        'catch',\n        'class',\n        'const',\n        'continue',\n        'debugger',\n        'default',\n        'delete',\n        'do',\n        'else',\n        'export',\n        'extends',\n        'finally',\n        'for',\n        'function',\n        'if',\n        'import',\n        'in',\n        'instanceof',\n        'new',\n        'return',\n        'super',\n        'switch',\n        'this',\n        'throw',\n        'try',\n        'typeof',\n        'var',\n        'void',\n        'while',\n        'with',\n        'yield'\n    ];\n    function makeName(value) {\n        const result = {\n            type: 'NameExpression',\n            name: value\n        };\n        if (reservedWords.includes(value)) {\n            result.reservedWord = true;\n        }\n        return result;\n    }\n    const catharsisTransformRules = {\n        JsdocTypeOptional: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.optional = true;\n            return transformed;\n        },\n        JsdocTypeNullable: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.nullable = true;\n            return transformed;\n        },\n        JsdocTypeNotNullable: (result, transform) => {\n            const transformed = transform(result.element);\n            transformed.nullable = false;\n            return transformed;\n        },\n        JsdocTypeVariadic: (result, transform) => {\n            if (result.element === undefined) {\n                throw new Error('dots without value are not allowed in catharsis mode');\n            }\n            const transformed = transform(result.element);\n            transformed.repeatable = true;\n            return transformed;\n        },\n        JsdocTypeAny: () => ({\n            type: 'AllLiteral'\n        }),\n        JsdocTypeNull: () => ({\n            type: 'NullLiteral'\n        }),\n        JsdocTypeStringValue: result => makeName(quote(result.value, result.meta.quote)),\n        JsdocTypeUndefined: () => ({\n            type: 'UndefinedLiteral'\n        }),\n        JsdocTypeUnknown: () => ({\n            type: 'UnknownLiteral'\n        }),\n        JsdocTypeFunction: (result, transform) => {\n            const params = extractSpecialParams(result);\n            const transformed = {\n                type: 'FunctionType',\n                params: params.params.map(transform)\n            };\n            if (params.this !== undefined) {\n                transformed.this = transform(params.this);\n            }\n            if (params.new !== undefined) {\n                transformed.new = transform(params.new);\n            }\n            if (result.returnType !== undefined) {\n                transformed.result = transform(result.returnType);\n            }\n            return transformed;\n        },\n        JsdocTypeGeneric: (result, transform) => ({\n            type: 'TypeApplication',\n            applications: result.elements.map(o => transform(o)),\n            expression: transform(result.left)\n        }),\n        JsdocTypeSpecialNamePath: result => makeName(result.specialType + ':' + quote(result.value, result.meta.quote)),\n        JsdocTypeName: result => {\n            if (result.value !== 'function') {\n                return makeName(result.value);\n            }\n            else {\n                return {\n                    type: 'FunctionType',\n                    params: []\n                };\n            }\n        },\n        JsdocTypeNumber: result => makeName(result.value.toString()),\n        JsdocTypeObject: (result, transform) => {\n            const transformed = {\n                type: 'RecordType',\n                fields: []\n            };\n            for (const field of result.elements) {\n                if (field.type !== 'JsdocTypeObjectField' && field.type !== 'JsdocTypeJsdocObjectField') {\n                    transformed.fields.push({\n                        type: 'FieldType',\n                        key: transform(field),\n                        value: undefined\n                    });\n                }\n                else {\n                    transformed.fields.push(transform(field));\n                }\n            }\n            return transformed;\n        },\n        JsdocTypeObjectField: (result, transform) => {\n            if (typeof result.key !== 'string') {\n                throw new Error('Index signatures and mapped types are not supported');\n            }\n            return {\n                type: 'FieldType',\n                key: makeName(quote(result.key, result.meta.quote)),\n                value: result.right === undefined ? undefined : transform(result.right)\n            };\n        },\n        JsdocTypeJsdocObjectField: (result, transform) => ({\n            type: 'FieldType',\n            key: transform(result.left),\n            value: transform(result.right)\n        }),\n        JsdocTypeUnion: (result, transform) => ({\n            type: 'TypeUnion',\n            elements: result.elements.map(e => transform(e))\n        }),\n        JsdocTypeKeyValue: (result, transform) => {\n            return {\n                type: 'FieldType',\n                key: makeName(result.key),\n                value: result.right === undefined ? undefined : transform(result.right)\n            };\n        },\n        JsdocTypeNamePath: (result, transform) => {\n            const leftResult = transform(result.left);\n            let rightValue;\n            if (result.right.type === 'JsdocTypeSpecialNamePath') {\n                rightValue = transform(result.right).name;\n            }\n            else {\n                rightValue = quote(result.right.value, result.right.meta.quote);\n            }\n            const joiner = result.pathType === 'inner' ? '~' : result.pathType === 'instance' ? '#' : '.';\n            return makeName(`${leftResult.name}${joiner}${rightValue}`);\n        },\n        JsdocTypeSymbol: result => {\n            let value = '';\n            let element = result.element;\n            let trailingDots = false;\n            if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeVariadic') {\n                if (element.meta.position === 'prefix') {\n                    value = '...';\n                }\n                else {\n                    trailingDots = true;\n                }\n                element = element.element;\n            }\n            if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeName') {\n                value += element.value;\n            }\n            else if ((element === null || element === void 0 ? void 0 : element.type) === 'JsdocTypeNumber') {\n                value += element.value.toString();\n            }\n            if (trailingDots) {\n                value += '...';\n            }\n            return makeName(`${result.value}(${value})`);\n        },\n        JsdocTypeParenthesis: (result, transform) => transform(assertRootResult(result.element)),\n        JsdocTypeMappedType: notAvailableTransform,\n        JsdocTypeIndexSignature: notAvailableTransform,\n        JsdocTypeImport: notAvailableTransform,\n        JsdocTypeKeyof: notAvailableTransform,\n        JsdocTypeTuple: notAvailableTransform,\n        JsdocTypeTypeof: notAvailableTransform,\n        JsdocTypeIntersection: notAvailableTransform,\n        JsdocTypeProperty: notAvailableTransform,\n        JsdocTypePredicate: notAvailableTransform,\n        JsdocTypeAsserts: notAvailableTransform\n    };\n    function catharsisTransform(result) {\n        return transform(catharsisTransformRules, result);\n    }\n\n    function getQuoteStyle(quote) {\n        switch (quote) {\n            case undefined:\n                return 'none';\n            case 'single':\n                return 'single';\n            case 'double':\n                return 'double';\n        }\n    }\n    function getMemberType(type) {\n        switch (type) {\n            case 'inner':\n                return 'INNER_MEMBER';\n            case 'instance':\n                return 'INSTANCE_MEMBER';\n            case 'property':\n                return 'MEMBER';\n            case 'property-brackets':\n                return 'MEMBER';\n        }\n    }\n    function nestResults(type, results) {\n        if (results.length === 2) {\n            return {\n                type,\n                left: results[0],\n                right: results[1]\n            };\n        }\n        else {\n            return {\n                type,\n                left: results[0],\n                right: nestResults(type, results.slice(1))\n            };\n        }\n    }\n    const jtpRules = {\n        JsdocTypeOptional: (result, transform) => ({\n            type: 'OPTIONAL',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_EQUAL_SIGN' : 'SUFFIX_EQUALS_SIGN'\n            }\n        }),\n        JsdocTypeNullable: (result, transform) => ({\n            type: 'NULLABLE',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_QUESTION_MARK' : 'SUFFIX_QUESTION_MARK'\n            }\n        }),\n        JsdocTypeNotNullable: (result, transform) => ({\n            type: 'NOT_NULLABLE',\n            value: transform(result.element),\n            meta: {\n                syntax: result.meta.position === 'prefix' ? 'PREFIX_BANG' : 'SUFFIX_BANG'\n            }\n        }),\n        JsdocTypeVariadic: (result, transform) => {\n            const transformed = {\n                type: 'VARIADIC',\n                meta: {\n                    syntax: result.meta.position === 'prefix'\n                        ? 'PREFIX_DOTS'\n                        : result.meta.position === 'suffix' ? 'SUFFIX_DOTS' : 'ONLY_DOTS'\n                }\n            };\n            if (result.element !== undefined) {\n                transformed.value = transform(result.element);\n            }\n            return transformed;\n        },\n        JsdocTypeName: result => ({\n            type: 'NAME',\n            name: result.value\n        }),\n        JsdocTypeTypeof: (result, transform) => ({\n            type: 'TYPE_QUERY',\n            name: transform(result.element)\n        }),\n        JsdocTypeTuple: (result, transform) => ({\n            type: 'TUPLE',\n            entries: result.elements.map(transform)\n        }),\n        JsdocTypeKeyof: (result, transform) => ({\n            type: 'KEY_QUERY',\n            value: transform(result.element)\n        }),\n        JsdocTypeImport: result => ({\n            type: 'IMPORT',\n            path: {\n                type: 'STRING_VALUE',\n                quoteStyle: getQuoteStyle(result.element.meta.quote),\n                string: result.element.value\n            }\n        }),\n        JsdocTypeUndefined: () => ({\n            type: 'NAME',\n            name: 'undefined'\n        }),\n        JsdocTypeAny: () => ({\n            type: 'ANY'\n        }),\n        JsdocTypeFunction: (result, transform) => {\n            const specialParams = extractSpecialParams(result);\n            const transformed = {\n                type: result.arrow ? 'ARROW' : 'FUNCTION',\n                params: specialParams.params.map(param => {\n                    if (param.type === 'JsdocTypeKeyValue') {\n                        if (param.right === undefined) {\n                            throw new Error('Function parameter without \\':\\' is not expected to be \\'KEY_VALUE\\'');\n                        }\n                        return {\n                            type: 'NAMED_PARAMETER',\n                            name: param.key,\n                            typeName: transform(param.right)\n                        };\n                    }\n                    else {\n                        return transform(param);\n                    }\n                }),\n                new: null,\n                returns: null\n            };\n            if (specialParams.this !== undefined) {\n                transformed.this = transform(specialParams.this);\n            }\n            else if (!result.arrow) {\n                transformed.this = null;\n            }\n            if (specialParams.new !== undefined) {\n                transformed.new = transform(specialParams.new);\n            }\n            if (result.returnType !== undefined) {\n                transformed.returns = transform(result.returnType);\n            }\n            return transformed;\n        },\n        JsdocTypeGeneric: (result, transform) => {\n            const transformed = {\n                type: 'GENERIC',\n                subject: transform(result.left),\n                objects: result.elements.map(transform),\n                meta: {\n                    syntax: result.meta.brackets === 'square' ? 'SQUARE_BRACKET' : result.meta.dot ? 'ANGLE_BRACKET_WITH_DOT' : 'ANGLE_BRACKET'\n                }\n            };\n            if (result.meta.brackets === 'square' && result.elements[0].type === 'JsdocTypeFunction' && !result.elements[0].parenthesis) {\n                transformed.objects[0] = {\n                    type: 'NAME',\n                    name: 'function'\n                };\n            }\n            return transformed;\n        },\n        JsdocTypeObjectField: (result, transform) => {\n            if (typeof result.key !== 'string') {\n                throw new Error('Index signatures and mapped types are not supported');\n            }\n            if (result.right === undefined) {\n                return {\n                    type: 'RECORD_ENTRY',\n                    key: result.key,\n                    quoteStyle: getQuoteStyle(result.meta.quote),\n                    value: null,\n                    readonly: false\n                };\n            }\n            let right = transform(result.right);\n            if (result.optional) {\n                right = {\n                    type: 'OPTIONAL',\n                    value: right,\n                    meta: {\n                        syntax: 'SUFFIX_KEY_QUESTION_MARK'\n                    }\n                };\n            }\n            return {\n                type: 'RECORD_ENTRY',\n                key: result.key.toString(),\n                quoteStyle: getQuoteStyle(result.meta.quote),\n                value: right,\n                readonly: false\n            };\n        },\n        JsdocTypeJsdocObjectField: () => {\n            throw new Error('Keys may not be typed in jsdoctypeparser.');\n        },\n        JsdocTypeKeyValue: (result, transform) => {\n            if (result.right === undefined) {\n                return {\n                    type: 'RECORD_ENTRY',\n                    key: result.key,\n                    quoteStyle: 'none',\n                    value: null,\n                    readonly: false\n                };\n            }\n            let right = transform(result.right);\n            if (result.optional) {\n                right = {\n                    type: 'OPTIONAL',\n                    value: right,\n                    meta: {\n                        syntax: 'SUFFIX_KEY_QUESTION_MARK'\n                    }\n                };\n            }\n            return {\n                type: 'RECORD_ENTRY',\n                key: result.key,\n                quoteStyle: 'none',\n                value: right,\n                readonly: false\n            };\n        },\n        JsdocTypeObject: (result, transform) => {\n            const entries = [];\n            for (const field of result.elements) {\n                if (field.type === 'JsdocTypeObjectField' || field.type === 'JsdocTypeJsdocObjectField') {\n                    entries.push(transform(field));\n                }\n            }\n            return {\n                type: 'RECORD',\n                entries\n            };\n        },\n        JsdocTypeSpecialNamePath: result => {\n            if (result.specialType !== 'module') {\n                throw new Error(`jsdoctypeparser does not support type ${result.specialType} at this point.`);\n            }\n            return {\n                type: 'MODULE',\n                value: {\n                    type: 'FILE_PATH',\n                    quoteStyle: getQuoteStyle(result.meta.quote),\n                    path: result.value\n                }\n            };\n        },\n        JsdocTypeNamePath: (result, transform) => {\n            let hasEventPrefix = false;\n            let name;\n            let quoteStyle;\n            if (result.right.type === 'JsdocTypeSpecialNamePath' && result.right.specialType === 'event') {\n                hasEventPrefix = true;\n                name = result.right.value;\n                quoteStyle = getQuoteStyle(result.right.meta.quote);\n            }\n            else {\n                name = result.right.value;\n                quoteStyle = getQuoteStyle(result.right.meta.quote);\n            }\n            const transformed = {\n                type: getMemberType(result.pathType),\n                owner: transform(result.left),\n                name,\n                quoteStyle,\n                hasEventPrefix\n            };\n            if (transformed.owner.type === 'MODULE') {\n                const tModule = transformed.owner;\n                transformed.owner = transformed.owner.value;\n                tModule.value = transformed;\n                return tModule;\n            }\n            else {\n                return transformed;\n            }\n        },\n        JsdocTypeUnion: (result, transform) => nestResults('UNION', result.elements.map(transform)),\n        JsdocTypeParenthesis: (result, transform) => ({\n            type: 'PARENTHESIS',\n            value: transform(assertRootResult(result.element))\n        }),\n        JsdocTypeNull: () => ({\n            type: 'NAME',\n            name: 'null'\n        }),\n        JsdocTypeUnknown: () => ({\n            type: 'UNKNOWN'\n        }),\n        JsdocTypeStringValue: result => ({\n            type: 'STRING_VALUE',\n            quoteStyle: getQuoteStyle(result.meta.quote),\n            string: result.value\n        }),\n        JsdocTypeIntersection: (result, transform) => nestResults('INTERSECTION', result.elements.map(transform)),\n        JsdocTypeNumber: result => ({\n            type: 'NUMBER_VALUE',\n            number: result.value.toString()\n        }),\n        JsdocTypeSymbol: notAvailableTransform,\n        JsdocTypeProperty: notAvailableTransform,\n        JsdocTypePredicate: notAvailableTransform,\n        JsdocTypeMappedType: notAvailableTransform,\n        JsdocTypeIndexSignature: notAvailableTransform,\n        JsdocTypeAsserts: notAvailableTransform\n    };\n    function jtpTransform(result) {\n        return transform(jtpRules, result);\n    }\n\n    function identityTransformRules() {\n        return {\n            JsdocTypeIntersection: (result, transform) => ({\n                type: 'JsdocTypeIntersection',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeGeneric: (result, transform) => ({\n                type: 'JsdocTypeGeneric',\n                left: transform(result.left),\n                elements: result.elements.map(transform),\n                meta: {\n                    dot: result.meta.dot,\n                    brackets: result.meta.brackets\n                }\n            }),\n            JsdocTypeNullable: result => result,\n            JsdocTypeUnion: (result, transform) => ({\n                type: 'JsdocTypeUnion',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeUnknown: result => result,\n            JsdocTypeUndefined: result => result,\n            JsdocTypeTypeof: (result, transform) => ({\n                type: 'JsdocTypeTypeof',\n                element: transform(result.element)\n            }),\n            JsdocTypeSymbol: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeSymbol',\n                    value: result.value\n                };\n                if (result.element !== undefined) {\n                    transformed.element = transform(result.element);\n                }\n                return transformed;\n            },\n            JsdocTypeOptional: (result, transform) => ({\n                type: 'JsdocTypeOptional',\n                element: transform(result.element),\n                meta: {\n                    position: result.meta.position\n                }\n            }),\n            JsdocTypeObject: (result, transform) => ({\n                type: 'JsdocTypeObject',\n                meta: {\n                    separator: 'comma'\n                },\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeNumber: result => result,\n            JsdocTypeNull: result => result,\n            JsdocTypeNotNullable: (result, transform) => ({\n                type: 'JsdocTypeNotNullable',\n                element: transform(result.element),\n                meta: {\n                    position: result.meta.position\n                }\n            }),\n            JsdocTypeSpecialNamePath: result => result,\n            JsdocTypeObjectField: (result, transform) => ({\n                type: 'JsdocTypeObjectField',\n                key: result.key,\n                right: result.right === undefined ? undefined : transform(result.right),\n                optional: result.optional,\n                readonly: result.readonly,\n                meta: result.meta\n            }),\n            JsdocTypeJsdocObjectField: (result, transform) => ({\n                type: 'JsdocTypeJsdocObjectField',\n                left: transform(result.left),\n                right: transform(result.right)\n            }),\n            JsdocTypeKeyValue: (result, transform) => {\n                return {\n                    type: 'JsdocTypeKeyValue',\n                    key: result.key,\n                    right: result.right === undefined ? undefined : transform(result.right),\n                    optional: result.optional,\n                    variadic: result.variadic\n                };\n            },\n            JsdocTypeImport: (result, transform) => ({\n                type: 'JsdocTypeImport',\n                element: transform(result.element)\n            }),\n            JsdocTypeAny: result => result,\n            JsdocTypeStringValue: result => result,\n            JsdocTypeNamePath: result => result,\n            JsdocTypeVariadic: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeVariadic',\n                    meta: {\n                        position: result.meta.position,\n                        squareBrackets: result.meta.squareBrackets\n                    }\n                };\n                if (result.element !== undefined) {\n                    transformed.element = transform(result.element);\n                }\n                return transformed;\n            },\n            JsdocTypeTuple: (result, transform) => ({\n                type: 'JsdocTypeTuple',\n                elements: result.elements.map(transform)\n            }),\n            JsdocTypeName: result => result,\n            JsdocTypeFunction: (result, transform) => {\n                const transformed = {\n                    type: 'JsdocTypeFunction',\n                    arrow: result.arrow,\n                    parameters: result.parameters.map(transform),\n                    constructor: result.constructor,\n                    parenthesis: result.parenthesis\n                };\n                if (result.returnType !== undefined) {\n                    transformed.returnType = transform(result.returnType);\n                }\n                return transformed;\n            },\n            JsdocTypeKeyof: (result, transform) => ({\n                type: 'JsdocTypeKeyof',\n                element: transform(result.element)\n            }),\n            JsdocTypeParenthesis: (result, transform) => ({\n                type: 'JsdocTypeParenthesis',\n                element: transform(result.element)\n            }),\n            JsdocTypeProperty: result => result,\n            JsdocTypePredicate: (result, transform) => ({\n                type: 'JsdocTypePredicate',\n                left: transform(result.left),\n                right: transform(result.right)\n            }),\n            JsdocTypeIndexSignature: (result, transform) => ({\n                type: 'JsdocTypeIndexSignature',\n                key: result.key,\n                right: transform(result.right)\n            }),\n            JsdocTypeMappedType: (result, transform) => ({\n                type: 'JsdocTypeMappedType',\n                key: result.key,\n                right: transform(result.right)\n            }),\n            JsdocTypeAsserts: (result, transform) => ({\n                type: 'JsdocTypeAsserts',\n                left: transform(result.left),\n                right: transform(result.right)\n            })\n        };\n    }\n\n    const visitorKeys = {\n        JsdocTypeAny: [],\n        JsdocTypeFunction: ['parameters', 'returnType'],\n        JsdocTypeGeneric: ['left', 'elements'],\n        JsdocTypeImport: [],\n        JsdocTypeIndexSignature: ['right'],\n        JsdocTypeIntersection: ['elements'],\n        JsdocTypeKeyof: ['element'],\n        JsdocTypeKeyValue: ['right'],\n        JsdocTypeMappedType: ['right'],\n        JsdocTypeName: [],\n        JsdocTypeNamePath: ['left', 'right'],\n        JsdocTypeNotNullable: ['element'],\n        JsdocTypeNull: [],\n        JsdocTypeNullable: ['element'],\n        JsdocTypeNumber: [],\n        JsdocTypeObject: ['elements'],\n        JsdocTypeObjectField: ['right'],\n        JsdocTypeJsdocObjectField: ['left', 'right'],\n        JsdocTypeOptional: ['element'],\n        JsdocTypeParenthesis: ['element'],\n        JsdocTypeSpecialNamePath: [],\n        JsdocTypeStringValue: [],\n        JsdocTypeSymbol: ['element'],\n        JsdocTypeTuple: ['elements'],\n        JsdocTypeTypeof: ['element'],\n        JsdocTypeUndefined: [],\n        JsdocTypeUnion: ['elements'],\n        JsdocTypeUnknown: [],\n        JsdocTypeVariadic: ['element'],\n        JsdocTypeProperty: [],\n        JsdocTypePredicate: ['left', 'right'],\n        JsdocTypeAsserts: ['left', 'right']\n    };\n\n    function _traverse(node, parentNode, property, onEnter, onLeave) {\n        onEnter === null || onEnter === void 0 ? void 0 : onEnter(node, parentNode, property);\n        const keysToVisit = visitorKeys[node.type];\n        for (const key of keysToVisit) {\n            const value = node[key];\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    for (const element of value) {\n                        _traverse(element, node, key, onEnter, onLeave);\n                    }\n                }\n                else {\n                    _traverse(value, node, key, onEnter, onLeave);\n                }\n            }\n        }\n        onLeave === null || onLeave === void 0 ? void 0 : onLeave(node, parentNode, property);\n    }\n    /**\n     * A function to traverse an AST. It traverses it depth first.\n     * @param node the node to start traversing at.\n     * @param onEnter node visitor function that will be called on entering the node. This corresponds to preorder traversing.\n     * @param onLeave node visitor function that will be called on leaving the node. This corresponds to postorder traversing.\n     */\n    function traverse(node, onEnter, onLeave) {\n        _traverse(node, undefined, undefined, onEnter, onLeave);\n    }\n\n    exports.catharsisTransform = catharsisTransform;\n    exports.identityTransformRules = identityTransformRules;\n    exports.jtpTransform = jtpTransform;\n    exports.parse = parse;\n    exports.stringify = stringify;\n    exports.stringifyRules = stringifyRules;\n    exports.transform = transform;\n    exports.traverse = traverse;\n    exports.tryParse = tryParse;\n    exports.visitorKeys = visitorKeys;\n\n}));\n", "var De = Object.defineProperty;\nvar o = (e, t) => De(e, \"name\", { value: t, configurable: !0 });\n\n// src/docs-tools/argTypes/convert/flow/convert.ts\nimport { UnknownArgTypesError as Te } from \"@storybook/core/preview-errors\";\nvar he = /* @__PURE__ */ o((e) => e.name === \"literal\", \"isLiteral\"), be = /* @__PURE__ */ o((e) => e.value.replace(/['|\"]/g, \"\"), \"toEnumOp\\\ntion\"), Pe = /* @__PURE__ */ o((e) => {\n  switch (e.type) {\n    case \"function\":\n      return { name: \"function\" };\n    case \"object\":\n      let t = {};\n      return e.signature.properties.forEach((r) => {\n        t[r.key] = d(r.value);\n      }), {\n        name: \"object\",\n        value: t\n      };\n    default:\n      throw new Te({ type: e, language: \"Flow\" });\n  }\n}, \"convertSig\"), d = /* @__PURE__ */ o((e) => {\n  let { name: t, raw: r } = e, n = {};\n  switch (typeof r < \"u\" && (n.raw = r), e.name) {\n    case \"literal\":\n      return { ...n, name: \"other\", value: e.value };\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n    case \"boolean\":\n      return { ...n, name: t };\n    case \"Array\":\n      return { ...n, name: \"array\", value: e.elements.map(d) };\n    case \"signature\":\n      return { ...n, ...Pe(e) };\n    case \"union\":\n      return e.elements?.every(he) ? { ...n, name: \"enum\", value: e.elements?.map(be) } : { ...n, name: t, value: e.elements?.map(d) };\n    case \"intersection\":\n      return { ...n, name: t, value: e.elements?.map(d) };\n    default:\n      return { ...n, name: \"other\", value: t };\n  }\n}, \"convert\");\n\n// ../node_modules/es-toolkit/dist/object/mapValues.mjs\nfunction j(e, t) {\n  let r = {}, n = Object.keys(e);\n  for (let s = 0; s < n.length; s++) {\n    let i = n[s], p = e[i];\n    r[i] = t(p, i, e);\n  }\n  return r;\n}\no(j, \"mapValues\");\n\n// src/docs-tools/argTypes/convert/utils.ts\nvar W = /^['\"]|['\"]$/g, Se = /* @__PURE__ */ o((e) => e.replace(W, \"\"), \"trimQuotes\"), Oe = /* @__PURE__ */ o((e) => W.test(e), \"includesQuo\\\ntes\"), h = /* @__PURE__ */ o((e) => {\n  let t = Se(e);\n  return Oe(e) || Number.isNaN(Number(t)) ? t : Number(t);\n}, \"parseLiteral\");\n\n// src/docs-tools/argTypes/convert/proptypes/convert.ts\nvar ve = /^\\(.*\\) => /, x = /* @__PURE__ */ o((e) => {\n  let { name: t, raw: r, computed: n, value: s } = e, i = {};\n  switch (typeof r < \"u\" && (i.raw = r), t) {\n    case \"enum\": {\n      let a = n ? s : s.map((c) => h(c.value));\n      return { ...i, name: t, value: a };\n    }\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n      return { ...i, name: t };\n    case \"func\":\n      return { ...i, name: \"function\" };\n    case \"bool\":\n    case \"boolean\":\n      return { ...i, name: \"boolean\" };\n    case \"arrayOf\":\n    case \"array\":\n      return { ...i, name: \"array\", value: s && x(s) };\n    case \"object\":\n      return { ...i, name: t };\n    case \"objectOf\":\n      return { ...i, name: t, value: x(s) };\n    case \"shape\":\n    case \"exact\":\n      let p = j(s, (a) => x(a));\n      return { ...i, name: \"object\", value: p };\n    case \"union\":\n      return { ...i, name: \"union\", value: s.map((a) => x(a)) };\n    case \"instanceOf\":\n    case \"element\":\n    case \"elementType\":\n    default: {\n      if (t?.indexOf(\"|\") > 0)\n        try {\n          let u = t.split(\"|\").map((m) => JSON.parse(m));\n          return { ...i, name: \"enum\", value: u };\n        } catch {\n        }\n      let a = s ? `${t}(${s})` : t, c = ve.test(t) ? \"function\" : \"other\";\n      return { ...i, name: c, value: a };\n    }\n  }\n}, \"convert\");\n\n// src/docs-tools/argTypes/convert/typescript/convert.ts\nimport { UnknownArgTypesError as we } from \"@storybook/core/preview-errors\";\nvar Ee = /* @__PURE__ */ o((e) => {\n  switch (e.type) {\n    case \"function\":\n      return { name: \"function\" };\n    case \"object\":\n      let t = {};\n      return e.signature.properties.forEach((r) => {\n        t[r.key] = D(r.value);\n      }), {\n        name: \"object\",\n        value: t\n      };\n    default:\n      throw new we({ type: e, language: \"Typescript\" });\n  }\n}, \"convertSig\"), D = /* @__PURE__ */ o((e) => {\n  let { name: t, raw: r } = e, n = {};\n  switch (typeof r < \"u\" && (n.raw = r), e.name) {\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n    case \"boolean\":\n      return { ...n, name: t };\n    case \"Array\":\n      return { ...n, name: \"array\", value: e.elements.map(D) };\n    case \"signature\":\n      return { ...n, ...Ee(e) };\n    case \"union\":\n      let s;\n      return e.elements?.every((i) => i.name === \"literal\") ? s = {\n        ...n,\n        name: \"enum\",\n        // @ts-expect-error fix types\n        value: e.elements?.map((i) => h(i.value))\n      } : s = { ...n, name: t, value: e.elements?.map(D) }, s;\n    case \"intersection\":\n      return { ...n, name: t, value: e.elements?.map(D) };\n    default:\n      return { ...n, name: \"other\", value: t };\n  }\n}, \"convert\");\n\n// src/docs-tools/argTypes/convert/index.ts\nvar b = /* @__PURE__ */ o((e) => {\n  let { type: t, tsType: r, flowType: n } = e;\n  try {\n    if (t != null)\n      return x(t);\n    if (r != null)\n      return D(r);\n    if (n != null)\n      return d(n);\n  } catch (s) {\n    console.error(s);\n  }\n  return null;\n}, \"convert\");\n\n// src/docs-tools/argTypes/docgen/types.ts\nvar je = /* @__PURE__ */ ((s) => (s.JAVASCRIPT = \"JavaScript\", s.FLOW = \"Flow\", s.TYPESCRIPT = \"TypeScript\", s.UNKNOWN = \"Unknown\", s))(je ||\n{});\n\n// src/docs-tools/argTypes/docgen/utils/defaultValue.ts\nvar ke = [\"null\", \"undefined\"];\nfunction T(e) {\n  return ke.some((t) => t === e);\n}\no(T, \"isDefaultValueBlacklisted\");\n\n// src/docs-tools/argTypes/docgen/utils/string.ts\nvar M = /* @__PURE__ */ o((e) => {\n  if (!e)\n    return \"\";\n  if (typeof e == \"string\")\n    return e;\n  throw new Error(`Description: expected string, got: ${JSON.stringify(e)}`);\n}, \"str\");\n\n// src/docs-tools/argTypes/docgen/utils/docgenInfo.ts\nfunction z(e) {\n  return !!e.__docgenInfo;\n}\no(z, \"hasDocgen\");\nfunction $(e) {\n  return e != null && Object.keys(e).length > 0;\n}\no($, \"isValidDocgenSection\");\nfunction Y(e, t) {\n  return z(e) ? e.__docgenInfo[t] : null;\n}\no(Y, \"getDocgenSection\");\nfunction q(e) {\n  return z(e) ? M(e.__docgenInfo.description) : \"\";\n}\no(q, \"getDocgenDescription\");\n\n// ../node_modules/comment-parser/es6/primitives.js\nvar f;\n(function(e) {\n  e.start = \"/**\", e.nostart = \"/***\", e.delim = \"*\", e.end = \"*/\";\n})(f = f || (f = {}));\n\n// ../node_modules/comment-parser/es6/util.js\nfunction k(e) {\n  return /^\\s+$/.test(e);\n}\no(k, \"isSpace\");\nfunction G(e) {\n  let t = e.match(/\\r+$/);\n  return t == null ? [\"\", e] : [e.slice(-t[0].length), e.slice(0, -t[0].length)];\n}\no(G, \"splitCR\");\nfunction y(e) {\n  let t = e.match(/^\\s+/);\n  return t == null ? [\"\", e] : [e.slice(0, t[0].length), e.slice(t[0].length)];\n}\no(y, \"splitSpace\");\nfunction K(e) {\n  return e.split(/\\n/);\n}\no(K, \"splitLines\");\nfunction X(e = {}) {\n  return Object.assign({ tag: \"\", name: \"\", type: \"\", optional: !1, description: \"\", problems: [], source: [] }, e);\n}\no(X, \"seedSpec\");\nfunction F(e = {}) {\n  return Object.assign({ start: \"\", delimiter: \"\", postDelimiter: \"\", tag: \"\", postTag: \"\", name: \"\", postName: \"\", type: \"\", postType: \"\", description: \"\",\n  end: \"\", lineEnd: \"\" }, e);\n}\no(F, \"seedTokens\");\n\n// ../node_modules/comment-parser/es6/parser/block-parser.js\nvar Fe = /^@\\S+/;\nfunction J({ fence: e = \"```\" } = {}) {\n  let t = Je(e), r = /* @__PURE__ */ o((n, s) => t(n) ? !s : s, \"toggleFence\");\n  return /* @__PURE__ */ o(function(s) {\n    let i = [[]], p = !1;\n    for (let a of s)\n      Fe.test(a.tokens.description) && !p ? i.push([a]) : i[i.length - 1].push(a), p = r(a.tokens.description, p);\n    return i;\n  }, \"parseBlock\");\n}\no(J, \"getParser\");\nfunction Je(e) {\n  return typeof e == \"string\" ? (t) => t.split(e).length % 2 === 0 : e;\n}\no(Je, \"getFencer\");\n\n// ../node_modules/comment-parser/es6/parser/source-parser.js\nfunction N({ startLine: e = 0, markers: t = f } = {}) {\n  let r = null, n = e;\n  return /* @__PURE__ */ o(function(i) {\n    let p = i, a = F();\n    if ([a.lineEnd, p] = G(p), [a.start, p] = y(p), r === null && p.startsWith(t.start) && !p.startsWith(t.nostart) && (r = [], a.delimiter =\n    p.slice(0, t.start.length), p = p.slice(t.start.length), [a.postDelimiter, p] = y(p)), r === null)\n      return n++, null;\n    let c = p.trimRight().endsWith(t.end);\n    if (a.delimiter === \"\" && p.startsWith(t.delim) && !p.startsWith(t.end) && (a.delimiter = t.delim, p = p.slice(t.delim.length), [a.postDelimiter,\n    p] = y(p)), c) {\n      let u = p.trimRight();\n      a.end = p.slice(u.length - t.end.length), p = u.slice(0, -t.end.length);\n    }\n    if (a.description = p, r.push({ number: n, source: i, tokens: a }), n++, c) {\n      let u = r.slice();\n      return r = null, u;\n    }\n    return null;\n  }, \"parseSource\");\n}\no(N, \"getParser\");\n\n// ../node_modules/comment-parser/es6/parser/spec-parser.js\nfunction R({ tokenizers: e }) {\n  return /* @__PURE__ */ o(function(r) {\n    var n;\n    let s = X({ source: r });\n    for (let i of e)\n      if (s = i(s), !((n = s.problems[s.problems.length - 1]) === null || n === void 0) && n.critical)\n        break;\n    return s;\n  }, \"parseSpec\");\n}\no(R, \"getParser\");\n\n// ../node_modules/comment-parser/es6/parser/tokenizers/tag.js\nfunction P() {\n  return (e) => {\n    let { tokens: t } = e.source[0], r = t.description.match(/\\s*(@(\\S+))(\\s*)/);\n    return r === null ? (e.problems.push({\n      code: \"spec:tag:prefix\",\n      message: 'tag should start with \"@\" symbol',\n      line: e.source[0].number,\n      critical: !0\n    }), e) : (t.tag = r[1], t.postTag = r[3], t.description = t.description.slice(r[0].length), e.tag = r[2], e);\n  };\n}\no(P, \"tagTokenizer\");\n\n// ../node_modules/comment-parser/es6/parser/tokenizers/type.js\nfunction S(e = \"compact\") {\n  let t = Re(e);\n  return (r) => {\n    let n = 0, s = [];\n    for (let [a, { tokens: c }] of r.source.entries()) {\n      let u = \"\";\n      if (a === 0 && c.description[0] !== \"{\")\n        return r;\n      for (let m of c.description)\n        if (m === \"{\" && n++, m === \"}\" && n--, u += m, n === 0)\n          break;\n      if (s.push([c, u]), n === 0)\n        break;\n    }\n    if (n !== 0)\n      return r.problems.push({\n        code: \"spec:type:unpaired-curlies\",\n        message: \"unpaired curlies\",\n        line: r.source[0].number,\n        critical: !0\n      }), r;\n    let i = [], p = s[0][0].postDelimiter.length;\n    for (let [a, [c, u]] of s.entries())\n      c.type = u, a > 0 && (c.type = c.postDelimiter.slice(p) + u, c.postDelimiter = c.postDelimiter.slice(0, p)), [c.postType, c.description] =\n      y(c.description.slice(u.length)), i.push(c.type);\n    return i[0] = i[0].slice(1), i[i.length - 1] = i[i.length - 1].slice(0, -1), r.type = t(i), r;\n  };\n}\no(S, \"typeTokenizer\");\nvar Ne = /* @__PURE__ */ o((e) => e.trim(), \"trim\");\nfunction Re(e) {\n  return e === \"compact\" ? (t) => t.map(Ne).join(\"\") : e === \"preserve\" ? (t) => t.join(`\n`) : e;\n}\no(Re, \"getJoiner\");\n\n// ../node_modules/comment-parser/es6/parser/tokenizers/name.js\nvar Ae = /* @__PURE__ */ o((e) => e && e.startsWith('\"') && e.endsWith('\"'), \"isQuoted\");\nfunction O() {\n  let e = /* @__PURE__ */ o((t, { tokens: r }, n) => r.type === \"\" ? t : n, \"typeEnd\");\n  return (t) => {\n    let { tokens: r } = t.source[t.source.reduce(e, 0)], n = r.description.trimLeft(), s = n.split('\"');\n    if (s.length > 1 && s[0] === \"\" && s.length % 2 === 1)\n      return t.name = s[1], r.name = `\"${s[1]}\"`, [r.postName, r.description] = y(n.slice(r.name.length)), t;\n    let i = 0, p = \"\", a = !1, c;\n    for (let m of n) {\n      if (i === 0 && k(m))\n        break;\n      m === \"[\" && i++, m === \"]\" && i--, p += m;\n    }\n    if (i !== 0)\n      return t.problems.push({\n        code: \"spec:name:unpaired-brackets\",\n        message: \"unpaired brackets\",\n        line: t.source[0].number,\n        critical: !0\n      }), t;\n    let u = p;\n    if (p[0] === \"[\" && p[p.length - 1] === \"]\") {\n      a = !0, p = p.slice(1, -1);\n      let m = p.split(\"=\");\n      if (p = m[0].trim(), m[1] !== void 0 && (c = m.slice(1).join(\"=\").trim()), p === \"\")\n        return t.problems.push({\n          code: \"spec:name:empty-name\",\n          message: \"empty name\",\n          line: t.source[0].number,\n          critical: !0\n        }), t;\n      if (c === \"\")\n        return t.problems.push({\n          code: \"spec:name:empty-default\",\n          message: \"empty default value\",\n          line: t.source[0].number,\n          critical: !0\n        }), t;\n      if (!Ae(c) && /=(?!>)/.test(c))\n        return t.problems.push({\n          code: \"spec:name:invalid-default\",\n          message: \"invalid default value syntax\",\n          line: t.source[0].number,\n          critical: !0\n        }), t;\n    }\n    return t.optional = a, t.name = p, r.name = u, c !== void 0 && (t.default = c), [r.postName, r.description] = y(n.slice(r.name.length)),\n    t;\n  };\n}\no(O, \"nameTokenizer\");\n\n// ../node_modules/comment-parser/es6/parser/tokenizers/description.js\nfunction v(e = \"compact\", t = f) {\n  let r = A(e);\n  return (n) => (n.description = r(n.source, t), n);\n}\no(v, \"descriptionTokenizer\");\nfunction A(e) {\n  return e === \"compact\" ? Ve : e === \"preserve\" ? Be : e;\n}\no(A, \"getJoiner\");\nfunction Ve(e, t = f) {\n  return e.map(({ tokens: { description: r } }) => r.trim()).filter((r) => r !== \"\").join(\" \");\n}\no(Ve, \"compactJoiner\");\nvar Ce = /* @__PURE__ */ o((e, { tokens: t }, r) => t.type === \"\" ? e : r, \"lineNo\"), _e = /* @__PURE__ */ o(({ tokens: e }) => (e.delimiter ===\n\"\" ? e.start : e.postDelimiter.slice(1)) + e.description, \"getDescription\");\nfunction Be(e, t = f) {\n  if (e.length === 0)\n    return \"\";\n  e[0].tokens.description === \"\" && e[0].tokens.delimiter === t.start && (e = e.slice(1));\n  let r = e[e.length - 1];\n  return r !== void 0 && r.tokens.description === \"\" && r.tokens.end.endsWith(t.end) && (e = e.slice(0, -1)), e = e.slice(e.reduce(Ce, 0)), e.\n  map(_e).join(`\n`);\n}\no(Be, \"preserveJoiner\");\n\n// ../node_modules/comment-parser/es6/parser/index.js\nfunction V({ startLine: e = 0, fence: t = \"```\", spacing: r = \"compact\", markers: n = f, tokenizers: s = [\n  P(),\n  S(r),\n  O(),\n  v(r)\n] } = {}) {\n  if (e < 0 || e % 1 > 0)\n    throw new Error(\"Invalid startLine\");\n  let i = N({ startLine: e, markers: n }), p = J({ fence: t }), a = R({ tokenizers: s }), c = A(r);\n  return function(u) {\n    let m = [];\n    for (let ge of K(u)) {\n      let E = i(ge);\n      if (E === null)\n        continue;\n      let L = p(E), U = L.slice(1).map(a);\n      m.push({\n        description: c(L[0], n),\n        tags: U,\n        source: E,\n        problems: U.reduce((de, xe) => de.concat(xe.problems), [])\n      });\n    }\n    return m;\n  };\n}\no(V, \"getParser\");\n\n// ../node_modules/comment-parser/es6/stringifier/index.js\nfunction Ie(e) {\n  return e.start + e.delimiter + e.postDelimiter + e.tag + e.postTag + e.type + e.postType + e.name + e.postName + e.description + e.end + e.\n  lineEnd;\n}\no(Ie, \"join\");\nfunction C() {\n  return (e) => e.source.map(({ tokens: t }) => Ie(t)).join(`\n`);\n}\no(C, \"getStringifier\");\n\n// ../node_modules/comment-parser/es6/stringifier/inspect.js\nvar Le = {\n  line: 0,\n  start: 0,\n  delimiter: 0,\n  postDelimiter: 0,\n  tag: 0,\n  postTag: 0,\n  name: 0,\n  postName: 0,\n  type: 0,\n  postType: 0,\n  description: 0,\n  end: 0,\n  lineEnd: 0\n};\nvar Mr = Object.keys(Le);\n\n// ../node_modules/comment-parser/es6/index.js\nfunction H(e, t = {}) {\n  return V(t)(e);\n}\no(H, \"parse\");\nvar lo = C();\n\n// src/docs-tools/argTypes/jsdocParser.ts\nimport {\n  parse as Ue,\n  stringifyRules as We,\n  transform as Me\n} from \"jsdoc-type-pratt-parser\";\nfunction ze(e) {\n  return e != null && e.includes(\"@\");\n}\no(ze, \"containsJsDoc\");\nfunction $e(e) {\n  let n = `/**\n` + (e ?? \"\").split(`\n`).map((i) => ` * ${i}`).join(`\n`) + `\n*/`, s = H(n, {\n    spacing: \"preserve\"\n  });\n  if (!s || s.length === 0)\n    throw new Error(\"Cannot parse JSDoc tags.\");\n  return s[0];\n}\no($e, \"parse\");\nvar Ye = {\n  tags: [\"param\", \"arg\", \"argument\", \"returns\", \"ignore\", \"deprecated\"]\n}, Q = /* @__PURE__ */ o((e, t = Ye) => {\n  if (!ze(e))\n    return {\n      includesJsDoc: !1,\n      ignore: !1\n    };\n  let r = $e(e), n = qe(r, t.tags);\n  return n.ignore ? {\n    includesJsDoc: !0,\n    ignore: !0\n  } : {\n    includesJsDoc: !0,\n    ignore: !1,\n    // Always use the parsed description to ensure JSDoc is removed from the description.\n    description: r.description.trim(),\n    extractedTags: n\n  };\n}, \"parseJsDoc\");\nfunction qe(e, t) {\n  let r = {\n    params: null,\n    deprecated: null,\n    returns: null,\n    ignore: !1\n  };\n  for (let n of e.tags)\n    if (!(t !== void 0 && !t.includes(n.tag)))\n      if (n.tag === \"ignore\") {\n        r.ignore = !0;\n        break;\n      } else\n        switch (n.tag) {\n          // arg & argument are aliases for param.\n          case \"param\":\n          case \"arg\":\n          case \"argument\": {\n            let s = Ke(n);\n            s != null && (r.params == null && (r.params = []), r.params.push(s));\n            break;\n          }\n          case \"deprecated\": {\n            let s = Xe(n);\n            s != null && (r.deprecated = s);\n            break;\n          }\n          case \"returns\": {\n            let s = He(n);\n            s != null && (r.returns = s);\n            break;\n          }\n          default:\n            break;\n        }\n  return r;\n}\no(qe, \"extractJsDocTags\");\nfunction Ge(e) {\n  return e.replace(/[\\.-]$/, \"\");\n}\no(Ge, \"normaliseParamName\");\nfunction Ke(e) {\n  if (!e.name || e.name === \"-\")\n    return null;\n  let t = te(e.type);\n  return {\n    name: e.name,\n    type: t,\n    description: ee(e.description),\n    getPrettyName: /* @__PURE__ */ o(() => Ge(e.name), \"getPrettyName\"),\n    getTypeName: /* @__PURE__ */ o(() => t ? re(t) : null, \"getTypeName\")\n  };\n}\no(Ke, \"extractParam\");\nfunction Xe(e) {\n  return e.name ? Z(e.name, e.description) : null;\n}\no(Xe, \"extractDeprecated\");\nfunction Z(e, t) {\n  let r = e === \"\" ? t : `${e} ${t}`;\n  return ee(r);\n}\no(Z, \"joinNameAndDescription\");\nfunction ee(e) {\n  let t = e.replace(/^- /g, \"\").trim();\n  return t === \"\" ? null : t;\n}\no(ee, \"normaliseDescription\");\nfunction He(e) {\n  let t = te(e.type);\n  return t ? {\n    type: t,\n    description: Z(e.name, e.description),\n    getTypeName: /* @__PURE__ */ o(() => re(t), \"getTypeName\")\n  } : null;\n}\no(He, \"extractReturns\");\nvar g = We(), Qe = g.JsdocTypeObject;\ng.JsdocTypeAny = () => \"any\";\ng.JsdocTypeObject = (e, t) => `(${Qe(e, t)})`;\ng.JsdocTypeOptional = (e, t) => t(e.element);\ng.JsdocTypeNullable = (e, t) => t(e.element);\ng.JsdocTypeNotNullable = (e, t) => t(e.element);\ng.JsdocTypeUnion = (e, t) => e.elements.map(t).join(\"|\");\nfunction te(e) {\n  try {\n    return Ue(e, \"typescript\");\n  } catch {\n    return null;\n  }\n}\no(te, \"extractType\");\nfunction re(e) {\n  return Me(g, e);\n}\no(re, \"extractTypeName\");\n\n// src/docs-tools/argTypes/utils.ts\nvar ho = 90, bo = 50;\nfunction B(e) {\n  return e.length > 90;\n}\no(B, \"isTooLongForTypeSummary\");\nfunction oe(e) {\n  return e.length > 50;\n}\no(oe, \"isTooLongForDefaultValueSummary\");\nfunction l(e, t) {\n  return e === t ? { summary: e } : { summary: e, detail: t };\n}\no(l, \"createSummaryValue\");\nvar Po = /* @__PURE__ */ o((e) => e.replace(/\\\\r\\\\n/g, \"\\\\n\"), \"normalizeNewlines\");\n\n// src/docs-tools/argTypes/docgen/flow/createDefaultValue.ts\nfunction ne(e, t) {\n  if (e != null) {\n    let { value: r } = e;\n    if (!T(r))\n      return oe(r) ? l(t?.name, r) : l(r);\n  }\n  return null;\n}\no(ne, \"createDefaultValue\");\n\n// src/docs-tools/argTypes/docgen/flow/createType.ts\nfunction se({ name: e, value: t, elements: r, raw: n }) {\n  return t ?? (r != null ? r.map(se).join(\" | \") : n ?? e);\n}\no(se, \"generateUnionElement\");\nfunction Ze({ name: e, raw: t, elements: r }) {\n  return r != null ? l(r.map(se).join(\" | \")) : t != null ? l(t.replace(/^\\|\\s*/, \"\")) : l(e);\n}\no(Ze, \"generateUnion\");\nfunction et({ type: e, raw: t }) {\n  return t != null ? l(t) : l(e);\n}\no(et, \"generateFuncSignature\");\nfunction tt({ type: e, raw: t }) {\n  return t != null ? B(t) ? l(e, t) : l(t) : l(e);\n}\no(tt, \"generateObjectSignature\");\nfunction rt(e) {\n  let { type: t } = e;\n  return t === \"object\" ? tt(e) : et(e);\n}\no(rt, \"generateSignature\");\nfunction ot({ name: e, raw: t }) {\n  return t != null ? B(t) ? l(e, t) : l(t) : l(e);\n}\no(ot, \"generateDefault\");\nfunction ie(e) {\n  if (e == null)\n    return null;\n  switch (e.name) {\n    case \"union\":\n      return Ze(e);\n    case \"signature\":\n      return rt(e);\n    default:\n      return ot(e);\n  }\n}\no(ie, \"createType\");\n\n// src/docs-tools/argTypes/docgen/flow/createPropDef.ts\nvar pe = /* @__PURE__ */ o((e, t) => {\n  let { flowType: r, description: n, required: s, defaultValue: i } = t;\n  return {\n    name: e,\n    type: ie(r),\n    required: s,\n    description: n,\n    defaultValue: ne(i ?? null, r ?? null)\n  };\n}, \"createFlowPropDef\");\n\n// src/docs-tools/argTypes/docgen/typeScript/createDefaultValue.ts\nfunction ae({ defaultValue: e }) {\n  if (e != null) {\n    let { value: t } = e;\n    if (!T(t))\n      return l(t);\n  }\n  return null;\n}\no(ae, \"createDefaultValue\");\n\n// src/docs-tools/argTypes/docgen/typeScript/createType.ts\nfunction ce({ tsType: e, required: t }) {\n  if (e == null)\n    return null;\n  let r = e.name;\n  return t || (r = r.replace(\" | undefined\", \"\")), l(\n    [\"Array\", \"Record\", \"signature\"].includes(e.name) ? e.raw : r\n  );\n}\no(ce, \"createType\");\n\n// src/docs-tools/argTypes/docgen/typeScript/createPropDef.ts\nvar le = /* @__PURE__ */ o((e, t) => {\n  let { description: r, required: n } = t;\n  return {\n    name: e,\n    type: ce(t),\n    required: n,\n    description: r,\n    defaultValue: ae(t)\n  };\n}, \"createTsPropDef\");\n\n// src/docs-tools/argTypes/docgen/createPropDef.ts\nfunction nt(e) {\n  return e != null ? l(e.name) : null;\n}\no(nt, \"createType\");\nfunction st(e) {\n  let { computed: t, func: r } = e;\n  return typeof t > \"u\" && typeof r > \"u\";\n}\no(st, \"isReactDocgenTypescript\");\nfunction it(e) {\n  return e ? e.name === \"string\" ? !0 : e.name === \"enum\" ? Array.isArray(e.value) && e.value.every(\n    ({ value: t }) => typeof t == \"string\" && t[0] === '\"' && t[t.length - 1] === '\"'\n  ) : !1 : !1;\n}\no(it, \"isStringValued\");\nfunction pt(e, t) {\n  if (e != null) {\n    let { value: r } = e;\n    if (!T(r))\n      return st(e) && it(t) ? l(JSON.stringify(r)) : l(r);\n  }\n  return null;\n}\no(pt, \"createDefaultValue\");\nfunction ue(e, t, r) {\n  let { description: n, required: s, defaultValue: i } = r;\n  return {\n    name: e,\n    type: nt(t),\n    required: s,\n    description: n,\n    defaultValue: pt(i, t)\n  };\n}\no(ue, \"createBasicPropDef\");\nfunction w(e, t) {\n  if (t?.includesJsDoc) {\n    let { description: r, extractedTags: n } = t;\n    r != null && (e.description = t.description);\n    let s = {\n      ...n,\n      params: n?.params?.map(\n        (i) => ({\n          name: i.getPrettyName(),\n          description: i.description\n        })\n      )\n    };\n    Object.values(s).filter(Boolean).length > 0 && (e.jsDocTags = s);\n  }\n  return e;\n}\no(w, \"applyJsDocResult\");\nvar at = /* @__PURE__ */ o((e, t, r) => {\n  let n = ue(e, t.type, t);\n  return n.sbType = b(t), w(n, r);\n}, \"javaScriptFactory\"), ct = /* @__PURE__ */ o((e, t, r) => {\n  let n = le(e, t);\n  return n.sbType = b(t), w(n, r);\n}, \"tsFactory\"), lt = /* @__PURE__ */ o((e, t, r) => {\n  let n = pe(e, t);\n  return n.sbType = b(t), w(n, r);\n}, \"flowFactory\"), ut = /* @__PURE__ */ o((e, t, r) => {\n  let n = ue(e, { name: \"unknown\" }, t);\n  return w(n, r);\n}, \"unknownFactory\"), I = /* @__PURE__ */ o((e) => {\n  switch (e) {\n    case \"JavaScript\":\n      return at;\n    case \"TypeScript\":\n      return ct;\n    case \"Flow\":\n      return lt;\n    default:\n      return ut;\n  }\n}, \"getPropDefFactory\");\n\n// src/docs-tools/argTypes/docgen/extractDocgenProps.ts\nvar me = /* @__PURE__ */ o((e) => e.type != null ? \"JavaScript\" : e.flowType != null ? \"Flow\" : e.tsType != null ? \"TypeScript\" : \"Unknown\",\n\"getTypeSystem\"), mt = /* @__PURE__ */ o((e) => {\n  let t = me(e[0]), r = I(t);\n  return e.map((n) => {\n    let s = n;\n    return n.type?.elements && (s = {\n      ...n,\n      type: {\n        ...n.type,\n        value: n.type.elements\n      }\n    }), fe(s.name, s, t, r);\n  });\n}, \"extractComponentSectionArray\"), ft = /* @__PURE__ */ o((e) => {\n  let t = Object.keys(e), r = me(e[t[0]]), n = I(r);\n  return t.map((s) => {\n    let i = e[s];\n    return i != null ? fe(s, i, r, n) : null;\n  }).filter(Boolean);\n}, \"extractComponentSectionObject\"), on = /* @__PURE__ */ o((e, t) => {\n  let r = Y(e, t);\n  return $(r) ? Array.isArray(r) ? mt(r) : ft(r) : [];\n}, \"extractComponentProps\");\nfunction fe(e, t, r, n) {\n  let s = Q(t.description);\n  return s.includesJsDoc && s.ignore ? null : {\n    propDef: n(e, t, s),\n    jsDocTags: s.extractedTags,\n    docgenInfo: t,\n    typeSystem: r\n  };\n}\no(fe, \"extractProp\");\nfunction nn(e) {\n  return e != null ? q(e) : \"\";\n}\no(nn, \"extractComponentDescription\");\n\n// src/docs-tools/argTypes/enhanceArgTypes.ts\nimport { combineParameters as yt } from \"@storybook/core/preview-api\";\nvar cn = /* @__PURE__ */ o((e) => {\n  let {\n    component: t,\n    argTypes: r,\n    parameters: { docs: n = {} }\n  } = e, { extractArgTypes: s } = n, i = s && t ? s(t) : {};\n  return i ? yt(i, r) : r;\n}, \"enhanceArgTypes\");\n\n// src/docs-tools/shared.ts\nvar ye = \"storybook/docs\", mn = `${ye}/panel`, fn = \"docs\", yn = `${ye}/snippet-rendered`, gt = /* @__PURE__ */ ((n) => (n.AUTO = \"auto\", n.\nCODE = \"code\", n.DYNAMIC = \"dynamic\", n))(gt || {});\n\n// src/docs-tools/hasDocsOrControls.ts\nvar dt = /(addons\\/|addon-|addon-essentials\\/)(docs|controls)/, dn = /* @__PURE__ */ o((e) => e.presetsList?.some((t) => dt.test(t.name)), \"\\\nhasDocsOrControls\");\nexport {\n  ye as ADDON_ID,\n  bo as MAX_DEFAULT_VALUE_SUMMARY_LENGTH,\n  ho as MAX_TYPE_SUMMARY_LENGTH,\n  mn as PANEL_ID,\n  fn as PARAM_KEY,\n  yn as SNIPPET_RENDERED,\n  gt as SourceType,\n  je as TypeSystem,\n  b as convert,\n  l as createSummaryValue,\n  cn as enhanceArgTypes,\n  nn as extractComponentDescription,\n  on as extractComponentProps,\n  mt as extractComponentSectionArray,\n  ft as extractComponentSectionObject,\n  q as getDocgenDescription,\n  Y as getDocgenSection,\n  z as hasDocgen,\n  dn as hasDocsOrControls,\n  T as isDefaultValueBlacklisted,\n  oe as isTooLongForDefaultValueSummary,\n  B as isTooLongForTypeSummary,\n  $ as isValidDocgenSection,\n  Po as normalizeNewlines,\n  Q as parseJsDoc,\n  M as str\n};\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU;AAAA;AAAA;;;ACAjB;AAAA;AAAA,KAAC,SAAU,QAAQ,SAAS;AACxB,aAAO,YAAY,YAAY,OAAO,WAAW,cAAc,QAAQ,OAAO,IAC9E,OAAO,WAAW,cAAc,OAAO,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,KACvE,SAAS,OAAO,eAAe,cAAc,aAAa,UAAU,MAAM,QAAQ,OAAO,OAAO,CAAC,CAAC;AAAA,IACvG,GAAG,SAAO,SAAUA,UAAS;AAAE;AAE3B,eAAS,cAAc,OAAO;AAC1B,YAAI,MAAM,SAAS,UAAa,MAAM,SAAS,IAAI;AAC/C,iBAAO,IAAI,MAAM,IAAI,iBAAiB,MAAM,IAAI;AAAA,QACpD,OACK;AACD,iBAAO,IAAI,MAAM,IAAI;AAAA,QACzB;AAAA,MACJ;AAAA,MACA,MAAM,4BAA4B,MAAM;AAAA,QACpC,YAAY,OAAO;AACf,gBAAM,+BAA+B,cAAc,KAAK,CAAC,EAAE;AAC3D,eAAK,QAAQ;AACb,iBAAO,eAAe,MAAM,oBAAoB,SAAS;AAAA,QAC7D;AAAA,QACA,WAAW;AACP,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,MAAM,6BAA6B,MAAM;AAAA,QACrC,YAAY,OAAO;AACf,gBAAM,gDAAgD,cAAc,KAAK,CAAC,EAAE;AAC5E,eAAK,QAAQ;AACb,iBAAO,eAAe,MAAM,qBAAqB,SAAS;AAAA,QAC9D;AAAA,QACA,WAAW;AACP,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,MACA,MAAM,4BAA4B,MAAM;AAAA,QACpC,YAAY,QAAQ,SAAS;AACzB,cAAI,QAAQ,qBAAqB,OAAO,IAAI;AAC5C,cAAI,YAAY,QAAW;AACvB,qBAAS,aAAa,OAAO;AAAA,UACjC;AACA,gBAAM,KAAK;AACX,iBAAO,eAAe,MAAM,oBAAoB,SAAS;AAAA,QAC7D;AAAA,MACJ;AAkBA,eAAS,oBAAoB,MAAM;AAC/B,eAAO,UAAQ;AACX,cAAI,KAAK,WAAW,IAAI,GAAG;AACvB,mBAAO,EAAE,MAAM,MAAM,KAAK;AAAA,UAC9B,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,MACJ;AACA,eAAS,UAAU,MAAM;AACrB,YAAI,WAAW;AACf,YAAI;AACJ,cAAM,OAAO,KAAK,CAAC;AACnB,YAAI,UAAU;AACd,YAAI,SAAS,OAAQ,SAAS,KAAK;AAC/B,iBAAO;AAAA,QACX;AACA,eAAO,WAAW,KAAK,QAAQ;AAC3B;AACA,iBAAO,KAAK,QAAQ;AACpB,cAAI,CAAC,WAAW,SAAS,MAAM;AAC3B;AACA;AAAA,UACJ;AACA,oBAAU,CAAC,WAAW,SAAS;AAAA,QACnC;AACA,YAAI,SAAS,MAAM;AACf,gBAAM,IAAI,MAAM,qBAAqB;AAAA,QACzC;AACA,eAAO,KAAK,MAAM,GAAG,QAAQ;AAAA,MACjC;AACA,YAAM,uBAAuB,WAAC,qGAAwF,GAAC;AAGvH,YAAM,0BAA0B,WAAC,wHAAwG,GAAC;AAC1I,eAAS,cAAc,MAAM;AACzB,YAAI,OAAO,KAAK,CAAC;AACjB,YAAI,CAAC,qBAAqB,KAAK,IAAI,GAAG;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,WAAW;AACf,WAAG;AACC,iBAAO,KAAK,QAAQ;AACpB,cAAI,CAAC,wBAAwB,KAAK,IAAI,GAAG;AACrC;AAAA,UACJ;AACA;AAAA,QACJ,SAAS,WAAW,KAAK;AACzB,eAAO,KAAK,MAAM,GAAG,QAAQ;AAAA,MACjC;AAEA,YAAM,cAAc;AACpB,eAAS,UAAU,MAAM;AACrB,YAAI,IAAI;AACR,gBAAQ,MAAM,KAAK,YAAY,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK;AAAA,MAC5H;AACA,YAAM,iBAAiB,UAAQ;AAC3B,cAAM,QAAQ,cAAc,IAAI;AAChC,YAAI,SAAS,MAAM;AACf,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,eAAS,gBAAgB,MAAM;AAC3B,eAAO,UAAQ;AACX,cAAI,CAAC,KAAK,WAAW,IAAI,GAAG;AACxB,mBAAO;AAAA,UACX;AACA,gBAAM,WAAW,KAAK,KAAK,MAAM;AACjC,cAAI,aAAa,UAAa,wBAAwB,KAAK,QAAQ,GAAG;AAClE,mBAAO;AAAA,UACX;AACA,iBAAO;AAAA,YACH;AAAA,YACA,MAAM;AAAA,UACV;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,kBAAkB,UAAQ;AAC5B,cAAM,QAAQ,UAAU,IAAI;AAC5B,YAAI,SAAS,MAAM;AACf,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,UAAU,UAAQ;AACpB,YAAI,KAAK,SAAS,GAAG;AACjB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,aAAa,UAAQ;AACvB,cAAM,QAAQ,UAAU,IAAI;AAC5B,YAAI,UAAU,MAAM;AAChB,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,UACH,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,MACJ;AACA,YAAM,QAAQ;AAAA,QACV;AAAA,QACA,oBAAoB,IAAI;AAAA,QACxB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,KAAK;AAAA,QACzB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,oBAAoB,GAAG;AAAA,QACvB,gBAAgB,WAAW;AAAA,QAC3B,gBAAgB,MAAM;AAAA,QACtB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,MAAM;AAAA,QACtB,gBAAgB,KAAK;AAAA,QACrB,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,OAAO;AAAA,QACvB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,OAAO;AAAA,QACvB,gBAAgB,UAAU;AAAA,QAC1B,gBAAgB,QAAQ;AAAA,QACxB,gBAAgB,IAAI;AAAA,QACpB,gBAAgB,IAAI;AAAA,QACpB,gBAAgB,SAAS;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,YAAM,0BAA0B;AAAA,MAChC,MAAM,MAAM;AAAA,QACR,OAAO,OAAO,MAAM;AAChB,gBAAM,UAAU,KAAK,KAAK,IAAI;AAC9B,iBAAO,QAAQ;AACf,gBAAM,OAAO,KAAK,KAAK,IAAI;AAC3B,iBAAO,KAAK;AACZ,iBAAO,IAAI,MAAM,MAAM,QAAW,QAAQ,OAAO,KAAK,KAAK;AAAA,QAC/D;AAAA,QACA,YAAY,MAAM,UAAU,SAAS,MAAM;AACvC,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,WAAW;AAChB,eAAK,UAAU;AACf,eAAK,OAAO;AAAA,QAChB;AAAA,QACA,OAAO,KAAK,MAAM,cAAc,OAAO;AACnC,wBAAc,eAAe,wBAAwB,KAAK,IAAI;AAC9D,iBAAO,KAAK,KAAK;AACjB,qBAAW,QAAQ,OAAO;AACtB,kBAAM,UAAU,KAAK,IAAI;AACzB,gBAAI,YAAY,MAAM;AAClB,oBAAM,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,OAAO,GAAG,EAAE,YAAY,CAAC;AACvE,qBAAO,KAAK,MAAM,MAAM,KAAK,MAAM;AACnC,qBAAO,EAAE,MAAM,MAAM;AAAA,YACzB;AAAA,UACJ;AACA,gBAAM,IAAI,MAAM,sBAAsB,IAAI;AAAA,QAC9C;AAAA,QACA,UAAU;AACN,gBAAM,OAAO,MAAM,KAAK,KAAK,IAAI;AACjC,iBAAO,IAAI,MAAM,KAAK,MAAM,KAAK,SAAS,KAAK,MAAM,KAAK,KAAK;AAAA,QACnE;AAAA,MACJ;AAKA,eAAS,iBAAiB,QAAQ;AAC9B,YAAI,WAAW,QAAW;AACtB,gBAAM,IAAI,MAAM,sBAAsB;AAAA,QAC1C;AACA,YAAI,OAAO,SAAS,uBAAuB,OAAO,SAAS,4BACvD,OAAO,SAAS,uBAAuB,OAAO,SAAS,+BACvD,OAAO,SAAS,0BAA0B,OAAO,SAAS,+BAC1D,OAAO,SAAS,6BAA6B,OAAO,SAAS,uBAAuB;AACpF,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,gCAAgC,QAAQ;AAC7C,YAAI,OAAO,SAAS,qBAAqB;AACrC,iBAAO,0BAA0B,MAAM;AAAA,QAC3C;AACA,eAAO,iBAAiB,MAAM;AAAA,MAClC;AACA,eAAS,gCAAgC,QAAQ;AAC7C,YAAI,OAAO,SAAS,iBAAiB;AACjC,iBAAO;AAAA,QACX;AACA,eAAO,0BAA0B,MAAM;AAAA,MAC3C;AACA,eAAS,0BAA0B,QAAQ;AACvC,YAAI,OAAO,SAAS,qBAAqB;AACrC,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,iCAAiC,QAAQ;AAC9C,YAAI;AACJ,YAAI,OAAO,SAAS,qBAAqB;AACrC,gBAAM,KAAK,OAAO,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,iBAAiB;AAC1F,mBAAO;AAAA,UACX;AACA,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,YAAI,OAAO,SAAS,qBAAqB,OAAO,SAAS,iBAAiB;AACtE,gBAAM,IAAI,oBAAoB,MAAM;AAAA,QACxC;AACA,eAAO;AAAA,MACX;AACA,eAAS,kBAAkB,QAAQ;AAC/B,eAAO,OAAO,SAAS,6BAA6B,OAAO,SAAS;AAAA,MACxE;AAGA,UAAI;AACJ,OAAC,SAAUC,aAAY;AACnB,QAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACpC,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,QAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,QAAAA,YAAWA,YAAW,WAAW,IAAI,CAAC,IAAI;AAC1C,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,CAAC,IAAI;AAC/C,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,QAAAA,YAAWA,YAAW,QAAQ,IAAI,CAAC,IAAI;AACvC,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,QAAAA,YAAWA,YAAW,QAAQ,IAAI,EAAE,IAAI;AACxC,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,EAAE,IAAI;AAChD,QAAAA,YAAWA,YAAW,UAAU,IAAI,EAAE,IAAI;AAC1C,QAAAA,YAAWA,YAAW,OAAO,IAAI,EAAE,IAAI;AACvC,QAAAA,YAAWA,YAAW,gBAAgB,IAAI,EAAE,IAAI;AAChD,QAAAA,YAAWA,YAAW,SAAS,IAAI,EAAE,IAAI;AACzC,QAAAA,YAAWA,YAAW,WAAW,IAAI,EAAE,IAAI;AAC3C,QAAAA,YAAWA,YAAW,aAAa,IAAI,EAAE,IAAI;AAC7C,QAAAA,YAAWA,YAAW,eAAe,IAAI,EAAE,IAAI;AAAA,MACnD,GAAG,eAAe,aAAa,CAAC,EAAE;AAAA,MAElC,MAAM,OAAO;AAAA,QACT,YAAY,SAAS,aAAa,YAAY;AAC1C,eAAK,UAAU;AACf,cAAI,OAAO,gBAAgB,UAAU;AACjC,iBAAK,SAAS,MAAM,OAAO,WAAW;AAAA,UAC1C,OACK;AACD,iBAAK,SAAS;AAAA,UAClB;AACA,eAAK,aAAa;AAAA,QACtB;AAAA,QACA,IAAI,QAAQ;AACR,iBAAO,KAAK;AAAA,QAChB;AAAA;AAAA;AAAA;AAAA,QAIA,QAAQ;AACJ,gBAAM,SAAS,KAAK,UAAU,WAAW,GAAG;AAC5C,cAAI,KAAK,MAAM,QAAQ,SAAS,OAAO;AACnC,kBAAM,IAAI,qBAAqB,KAAK,MAAM,OAAO;AAAA,UACrD;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA,QAIA,UAAU,YAAY;AAClB,iBAAO,iBAAiB,KAAK,sBAAsB,UAAU,CAAC;AAAA,QAClE;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,sBAAsB,YAAY;AAC9B,gBAAM,SAAS,KAAK,YAAY,MAAM,UAAU;AAChD,cAAI,WAAW,MAAM;AACjB,kBAAM,IAAI,oBAAoB,KAAK,MAAM,OAAO;AAAA,UACpD;AACA,iBAAO,KAAK,2BAA2B,QAAQ,UAAU;AAAA,QAC7D;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,2BAA2B,MAAM,YAAY;AACzC,cAAI,SAAS,KAAK,YAAY,MAAM,UAAU;AAC9C,iBAAO,WAAW,MAAM;AACpB,mBAAO;AACP,qBAAS,KAAK,YAAY,MAAM,UAAU;AAAA,UAC9C;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA,QAIA,YAAY,MAAM,YAAY;AAC1B,qBAAW,WAAW,KAAK,SAAS;AAChC,kBAAM,SAAS,QAAQ,MAAM,YAAY,IAAI;AAC7C,gBAAI,WAAW,MAAM;AACjB,qBAAO;AAAA,YACX;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA;AAAA;AAAA;AAAA;AAAA,QAKA,QAAQ,OAAO;AACX,cAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACvB,oBAAQ,CAAC,KAAK;AAAA,UAClB;AACA,cAAI,MAAM,SAAS,KAAK,MAAM,QAAQ,IAAI,GAAG;AACzC,iBAAK,SAAS,KAAK,MAAM,QAAQ;AACjC,mBAAO;AAAA,UACX,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,QACA,iBAAiB,QAAQ;AACrB,eAAK,SAAS,OAAO;AAAA,QACzB;AAAA,MACJ;AAEA,eAAS,0BAA0B,MAAM;AACrC,eAAO,SAAS,SAAS,SAAS,OAAO,SAAS,OAAO,SAAS,OAAO,SAAS;AAAA,MACtF;AAEA,YAAM,kBAAkB,CAAC,QAAQ,YAAY,SAAS;AAClD,cAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,cAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,cAAM,SAAW,QAAQ,QAAS,SAAS,OAAO,CAAC,0BAA0B,IAAI,KAC3E,QAAQ,QAAS,SAAS;AAChC,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AACA,eAAO,QAAQ,GAAG;AAClB,YAAI,QAAQ,MAAM;AACd,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,eAAe,SAAS;AAC7B,cAAM,UAAU,CAAC,QAAQ,eAAe,SAAS;AAC7C,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,gBAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,cAAI,SAAS,MAAM;AACf,gBAAI,iBAAiB,SAAS;AAC1B,kBAAI,QAAQ,OAAO,MAAM,IAAI,GAAG;AAC5B,uBAAO,QAAQ,YAAY,MAAM;AAAA,cACrC;AAAA,YACJ;AAAA,UACJ,OACK;AACD,gBAAI,gBAAgB,SAAS;AACzB,kBAAI,QAAQ,aAAa,iBAAiB,QAAQ,OAAO,MAAM,IAAI,GAAG;AAClE,uBAAO,QAAQ,WAAW,QAAQ,IAAI;AAAA,cAC1C;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAEA,eAAO,eAAe,SAAS,QAAQ;AAAA,UACnC,OAAO,QAAQ;AAAA,QACnB,CAAC;AACD,eAAO;AAAA,MACX;AAEA,YAAM,kBAAkB,eAAe;AAAA,QACnC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,gBAAM,QAAQ,WAAW,OAAO,MAAM,QAAQ,IAAI;AAClD,iBAAO,QAAQ,QAAQ;AACvB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,UAAU,CAAC;AAAA,YACf;AAAA,UACJ;AACA,gBAAM,SAAS,OAAO,sBAAsB,WAAW,GAAG;AAC1D,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,0BAA0B;AAAA,UAC9C;AACA,cAAI,OAAO,SAAS,0BAA0B;AAC1C,mBAAO;AAAA,UACX,WACS,OAAO,SAAS,qBAAqB;AAC1C,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,UAAU,CAAC,MAAM;AAAA,YACrB;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,MAAM;AAAA,UACpC;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,sBAAsB,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,QAAQ,CAAC,MAAM,SAAU,SAAS,OAAO,0BAA0B,IAAI,KACnE,SAAS,UAAU,SAAS,eAAe,SAAS;AAAA,QACxD,aAAa,YAAU;AACnB,cAAI,OAAO,QAAQ,MAAM,GAAG;AACxB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,WAAW,GAAG;AAC7B,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,mBAAO;AAAA,cACH,MAAM;AAAA,YACV;AAAA,UACJ;AACA,gBAAM,IAAI,MAAM,yBAAyB,OAAO,MAAM,QAAQ,IAAI;AAAA,QACtE;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,aAAa,YAAU;AACnB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,QAAQ;AAAA,YAC7C,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,IAAI;AAAA,YAC9B,MAAM;AAAA,cACF,UAAU;AAAA,YACd;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,2BAA2B,EAAE,mBAAmB,GAAG;AACxD,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,WAAW;AAAA,UACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,kBAAM,WAAW;AAAA,cACb,gCAAgC,IAAI;AAAA,YACxC;AACA,mBAAO,QAAQ,GAAG;AAClB,eAAG;AACC,kBAAI;AACA,sBAAM,OAAO,OAAO,sBAAsB,WAAW,cAAc;AACnE,yBAAS,KAAK,gCAAgC,IAAI,CAAC;AAAA,cACvD,SACO,GAAG;AACN,oBAAI,sBAAsB,aAAa,qBAAqB;AACxD;AAAA,gBACJ,OACK;AACD,wBAAM;AAAA,gBACV;AAAA,cACJ;AAAA,YACJ,SAAS,OAAO,QAAQ,GAAG;AAC3B,gBAAI,SAAS,SAAS,KAAK,SAAS,MAAM,GAAG,EAAE,EAAE,KAAK,OAAK,EAAE,SAAS,mBAAmB,GAAG;AACxF,oBAAM,IAAI,MAAM,iDAAiD;AAAA,YACrE;AACA,mBAAO;AAAA,cACH,MAAM;AAAA,cACN;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,iBAAiB,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,QAAQ,CAAC,MAAM,SAAS,SAAS,OAAQ,SAAS,OAAO,SAAS;AAAA,QAClE,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAM,MAAM,OAAO,QAAQ,GAAG;AAC9B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,UAAU,CAAC;AACjB,aAAG;AACC,oBAAQ,KAAK,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UAC5D,SAAS,OAAO,QAAQ,GAAG;AAC3B,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,qCAAqC;AAAA,UACzD;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM,iBAAiB,IAAI;AAAA,YAC3B,UAAU;AAAA,YACV,MAAM;AAAA,cACF,UAAU;AAAA,cACV;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,eAAe,eAAe;AAAA,QAChC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,WAAW,CAAC;AAClB,aAAG;AACC,qBAAS,KAAK,OAAO,UAAU,WAAW,KAAK,CAAC;AAAA,UACpD,SAAS,OAAO,QAAQ,GAAG;AAC3B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,UAAU,CAAC,iBAAiB,IAAI,GAAG,GAAG,QAAQ;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,cAAc;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,2BAA2B;AAAA,UACvB,oBAAoB;AAAA,QACxB,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAEA,eAAS,sBAAsB,EAAE,8BAA8B,qBAAqB,aAAAC,aAAY,GAAG;AAC/F,eAAO,SAAS,gBAAgB,QAAQ,YAAY,MAAM;AACtD,cAAK,QAAQ,QAAS,cAAc,WAAW,WAAW;AACtD,mBAAO;AAAA,UACX;AACA,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,gBAAM,OAAO,OAAO,MAAM,KAAK;AAC/B,gBAAM,SAAU,SAAS,OAAO,SAAS,OACpC,SAAS,QAAQ,gCAAgC,KAAK,SAAS,oBAC/D,wBAAwB,SAAS,OAAO,SAAS;AACtD,cAAI,CAAC,QAAQ;AACT,mBAAO;AAAA,UACX;AACA,cAAI;AACJ,cAAI,WAAW;AACf,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,uBAAW;AAAA,UACf,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,uBAAW;AACX,uBAAW;AAAA,UACf,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,uBAAW;AAAA,UACf,OACK;AACD,mBAAO,QAAQ,GAAG;AAClB,uBAAW;AAAA,UACf;AACA,gBAAM,aAAaA,iBAAgB,OAC7B,IAAI,OAAOA,cAAa,OAAO,OAAO,MAAM,IAC5C;AACN,gBAAM,SAAS,WAAW,sBAAsB,WAAW,SAAS;AACpE,iBAAO,iBAAiB,UAAU;AAClC,cAAI;AACJ,kBAAQ,OAAO,MAAM;AAAA,YACjB,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO;AAAA,gBACd,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO,MAAM,SAAS,EAAE;AAAA,gBAC/B,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,sBAAQ;AAAA,gBACJ,MAAM;AAAA,gBACN,OAAO,OAAO;AAAA,gBACd,MAAM;AAAA,kBACF,OAAO,OAAO,KAAK;AAAA,gBACvB;AAAA,cACJ;AACA;AAAA,YACJ,KAAK;AACD,kBAAI,OAAO,gBAAgB,SAAS;AAChC,wBAAQ;AAAA,cACZ,OACK;AACD,sBAAM,IAAI,oBAAoB,QAAQ,0EAA8E;AAAA,cACxH;AACA;AAAA,YACJ;AACI,oBAAM,IAAI,oBAAoB,QAAQ,gGAAwG;AAAA,UACtJ;AACA,cAAI,YAAY,CAAC,OAAO,QAAQ,GAAG,GAAG;AAClC,kBAAM,QAAQ,OAAO,MAAM;AAC3B,kBAAM,IAAI,MAAM,gDAAgD,MAAM,IAAI,gBACxD,MAAM,IAAI,GAAG;AAAA,UACnC;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM,iBAAiB,IAAI;AAAA,YAC3B;AAAA,YACA;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,kBAAkB,EAAE,wBAAwB,GAAG;AACpD,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS,gBAAgB,SAAS,UAAU,SAAS,SAAS,wBAAwB,SAAS,IAAI;AAAA,UACnH,aAAa,YAAU;AACnB,kBAAM,EAAE,MAAM,KAAK,IAAI,OAAO,MAAM;AACpC,mBAAO,QAAQ,IAAI;AACnB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,OAAO;AAAA,YACX;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,qBAAqB,eAAe;AAAA,QACtC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,gBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,iBAAO,QAAQ,aAAa;AAC5B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,OAAO,KAAK,MAAM,GAAG,EAAE;AAAA,YACvB,MAAM;AAAA,cACF,OAAO,KAAK,CAAC,MAAM,MAAO,WAAW;AAAA,YACzC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,6BAA6B,EAAE,aAAAA,cAAa,aAAa,GAAG;AACjE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,aAAa,SAAS,IAAI;AAAA,UAC1C,aAAa,YAAU;AACnB,kBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,mBAAO,QAAQ,IAAI;AACnB,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,OAAO;AAAA,cACX;AAAA,YACJ;AACA,gBAAI;AACJ,gBAAI,QAAQ,OAAO,MAAM;AACzB,gBAAI,OAAO,QAAQ,aAAa,GAAG;AAC/B,uBAAS;AAAA,gBACL,MAAM;AAAA,gBACN,OAAO,MAAM,KAAK,MAAM,GAAG,EAAE;AAAA,gBAC7B,aAAa;AAAA,gBACb,MAAM;AAAA,kBACF,OAAO,MAAM,KAAK,CAAC,MAAM,MAAO,WAAW;AAAA,gBAC/C;AAAA,cACJ;AAAA,YACJ,OACK;AACD,kBAAI,QAAQ;AACZ,oBAAM,UAAU,CAAC,cAAc,KAAK,GAAG;AACvC,qBAAO,QAAQ,KAAK,CAAAC,UAAQ,OAAO,QAAQA,KAAI,CAAC,GAAG;AAC/C,yBAAS,MAAM;AACf,wBAAQ,OAAO,MAAM;AAAA,cACzB;AACA,uBAAS;AAAA,gBACL,MAAM;AAAA,gBACN;AAAA,gBACA,aAAa;AAAA,gBACb,MAAM;AAAA,kBACF,OAAO;AAAA,gBACX;AAAA,cACJ;AAAA,YACJ;AACA,kBAAM,eAAe,IAAI,OAAOD,cAAa,OAAO,OAAO,MAAM;AACjE,kBAAM,eAAe,aAAa,2BAA2B,QAAQ,WAAW,GAAG;AACnF,mBAAO,iBAAiB,YAAY;AACpC,mBAAO,iBAAiB,YAAY;AAAA,UACxC;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,kBAAkB;AAAA,QACpB,kBAAkB;AAAA,UACd,yBAAyB,CAAC,YAAY,QAAQ;AAAA,QAClD,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AACA,YAAM,cAAc;AAAA,QAChB,GAAG;AAAA,QACH,6BAA6B;AAAA,UACzB,cAAc,CAAC,OAAO;AAAA,UACtB,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AAEA,eAAS,cAAc,OAAO;AAC1B,YAAI;AACJ,YAAI,MAAM,SAAS,0BAA0B;AACzC,uBAAa,MAAM;AAAA,QACvB,WACS,MAAM,SAAS,wBAAwB;AAC5C,uBAAa,CAAC,MAAM,OAAO;AAAA,QAC/B,OACK;AACD,gBAAM,IAAI,oBAAoB,KAAK;AAAA,QACvC;AACA,eAAO,WAAW,IAAI,OAAK,gCAAgC,CAAC,CAAC;AAAA,MACjE;AACA,eAAS,qBAAqB,OAAO;AACjC,cAAM,aAAa,cAAc,KAAK;AACtC,YAAI,WAAW,KAAK,OAAK,EAAE,SAAS,mBAAmB,GAAG;AACtD,gBAAM,IAAI,MAAM,8BAA8B;AAAA,QAClD;AACA,eAAO;AAAA,MACX;AACA,eAAS,sBAAsB,EAAE,sBAAsB,mBAAmB,yBAAyB,0BAA0B,GAAG;AAC5H,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,CAAC,MAAM,SAAS,SAAS,cAAe,6BAA6B,SAAS,SAAS,SAAS;AAAA,UACxG,aAAa,YAAU;AACnB,kBAAM,aAAa,OAAO,QAAQ,KAAK;AACvC,mBAAO,QAAQ,UAAU;AACzB,kBAAM,iBAAiB,OAAO,MAAM,QAAQ,SAAS;AACrD,gBAAI,CAAC,gBAAgB;AACjB,kBAAI,CAAC,yBAAyB;AAC1B,sBAAM,IAAI,MAAM,oCAAoC;AAAA,cACxD;AACA,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,OAAO;AAAA,cACX;AAAA,YACJ;AACA,gBAAI,SAAS;AAAA,cACT,MAAM;AAAA,cACN,YAAY,CAAC;AAAA,cACb,OAAO;AAAA,cACP,aAAa;AAAA,cACb,aAAa;AAAA,YACjB;AACA,kBAAM,QAAQ,OAAO,sBAAsB,WAAW,QAAQ;AAC9D,gBAAI,yBAAyB,QAAW;AACpC,qBAAO,aAAa,qBAAqB,KAAK;AAAA,YAClD,WACS,cAAc,MAAM,SAAS,uBAAuB,MAAM,OAAO;AACtE,uBAAS;AACT,qBAAO,cAAc;AACrB,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,aAAa,cAAc,KAAK;AACvC,yBAAW,KAAK,OAAO,YAAY;AAC/B,oBAAI,EAAE,SAAS,uBAAwB,CAAC,qBAAqB,SAAS,EAAE,GAAG,GAAI;AAC3E,wBAAM,IAAI,MAAM,qCAAqC,qBAAqB,KAAK,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE;AAAA,gBAC5G;AAAA,cACJ;AAAA,YACJ;AACA,gBAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,qBAAO,aAAa,OAAO,UAAU,WAAW,MAAM;AAAA,YAC1D,OACK;AACD,kBAAI,CAAC,mBAAmB;AACpB,sBAAM,IAAI,MAAM,iCAAiC;AAAA,cACrD;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,sBAAsB,EAAE,cAAc,uBAAuB,GAAG;AACrE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,WAAW;AAAA,UACvB,aAAa,YAAU;AACnB,mBAAO,QAAQ,KAAK;AACpB,kBAAM,WAAW,0BAA0B,OAAO,QAAQ,GAAG;AAC7D,gBAAI;AACA,oBAAM,UAAU,OAAO,UAAU,WAAW,MAAM;AAClD,kBAAI,YAAY,CAAC,OAAO,QAAQ,GAAG,GAAG;AAClC,sBAAM,IAAI,MAAM,yCAA2C;AAAA,cAC/D;AACA,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,SAAS,iBAAiB,OAAO;AAAA,gBACjC,MAAM;AAAA,kBACF,UAAU;AAAA,kBACV,gBAAgB;AAAA,gBACpB;AAAA,cACJ;AAAA,YACJ,SACO,GAAG;AACN,kBAAI,aAAa,qBAAqB;AAClC,oBAAI,UAAU;AACV,wBAAM,IAAI,MAAM,qDAAqD;AAAA,gBACzE;AACA,uBAAO;AAAA,kBACH,MAAM;AAAA,kBACN,MAAM;AAAA,oBACF,UAAU;AAAA,oBACV,gBAAgB;AAAA,kBACpB;AAAA,gBACJ;AAAA,cACJ,OACK;AACD,sBAAM;AAAA,cACV;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,YAAY,eACN,CAAC,QAAQ,SAAS;AAChB,mBAAO,QAAQ,KAAK;AACpB,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,SAAS,iBAAiB,IAAI;AAAA,cAC9B,MAAM;AAAA,gBACF,UAAU;AAAA,gBACV,gBAAgB;AAAA,cACpB;AAAA,YACJ;AAAA,UACJ,IACE;AAAA,QACV,CAAC;AAAA,MACL;AAEA,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,MAAM,2DAA6D;AAAA,UACjF;AACA,iBAAO,QAAQ,GAAG;AAClB,gBAAM,SAAS;AAAA,YACX,MAAM;AAAA,YACN,OAAO,KAAK;AAAA,UAChB;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,OAAO,OAAO,sBAAsB,WAAW,MAAM;AAC3D,mBAAO,UAAU,iCAAiC,IAAI;AACtD,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,oBAAM,IAAI,MAAM,iCAAiC;AAAA,YACrD;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB,eAAe;AAAA,QACxC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,CAAC,MAAM,SAAS,SAAS,OAAO,SAAS;AAAA,QACjD,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,iBAAO,QAAQ,GAAG;AAClB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,MAAM;AAAA,cACF,MAAM;AAAA,cACN,OAAO;AAAA,YACX;AAAA,YACA,UAAU;AAAA,cACN,iBAAiB,IAAI;AAAA,YACzB;AAAA,YACA,MAAM;AAAA,cACF,UAAU;AAAA,cACV,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,oBAAoB,EAAE,oBAAAE,qBAAoB,cAAc,GAAG;AAChE,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,aAAa,YAAU;AACnB,mBAAO,QAAQ,GAAG;AAClB,kBAAM,SAAS;AAAA,cACX,MAAM;AAAA,cACN,MAAM;AAAA,gBACF,WAAW;AAAA,cACf;AAAA,cACA,UAAU,CAAC;AAAA,YACf;AACA,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAI;AACJ,oBAAM,cAAc,IAAI,OAAOA,qBAAoB,OAAO,OAAO,MAAM;AACvE,qBAAO,MAAM;AACT,4BAAY,iBAAiB,MAAM;AACnC,oBAAI,QAAQ,YAAY,sBAAsB,WAAW,MAAM;AAC/D,uBAAO,iBAAiB,WAAW;AACnC,oBAAI,UAAU,UAAa,eAAe;AACtC,0BAAQ,OAAO,sBAAsB,WAAW,MAAM;AAAA,gBAC1D;AACA,oBAAI,WAAW;AACf,oBAAI,MAAM,SAAS,qBAAqB;AACpC,6BAAW;AACX,0BAAQ,MAAM;AAAA,gBAClB;AACA,oBAAI,MAAM,SAAS,qBAAqB,MAAM,SAAS,mBAAmB,MAAM,SAAS,wBAAwB;AAC7G,sBAAIC;AACJ,sBAAI,MAAM,SAAS,wBAAwB;AACvC,oBAAAA,SAAQ,MAAM,KAAK;AAAA,kBACvB;AACA,yBAAO,SAAS,KAAK;AAAA,oBACjB,MAAM;AAAA,oBACN,KAAK,MAAM,MAAM,SAAS;AAAA,oBAC1B,OAAO;AAAA,oBACP;AAAA,oBACA,UAAU;AAAA,oBACV,MAAM;AAAA,sBACF,OAAAA;AAAA,oBACJ;AAAA,kBACJ,CAAC;AAAA,gBACL,WACS,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AAC1F,yBAAO,SAAS,KAAK,KAAK;AAAA,gBAC9B,OACK;AACD,wBAAM,IAAI,oBAAoB,KAAK;AAAA,gBACvC;AACA,oBAAI,OAAO,MAAM,QAAQ,aAAa;AAClC,8BAAY;AAAA,gBAChB,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,8BAAY;AAAA,gBAChB,WACS,OAAO,QAAQ,GAAG,GAAG;AAC1B,8BAAY;AAAA,gBAChB,OACK;AACD;AAAA,gBACJ;AACA,sBAAM,OAAO,OAAO,MAAM,QAAQ;AAClC,oBAAI,SAAS,KAAK;AACd;AAAA,gBACJ;AAAA,cACJ;AACA,qBAAO,KAAK,YAAY,cAAc,QAAQ,cAAc,SAAS,YAAY;AACjF,kBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,sBAAM,IAAI,MAAM,uCAAyC;AAAA,cAC7D;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,yBAAyB,EAAE,wBAAwB,eAAe,eAAe,cAAc,GAAG;AACvG,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,YAAY,WAAW;AAAA,UACvB,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAI;AACJ,gBAAI,WAAW;AACf,gBAAI,mBAAmB;AACvB,gBAAI,iBAAiB,KAAK,SAAS,qBAAqB;AACpD,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,iBAAiB,KAAK,SAAS,6BAA6B;AAC5D,iCAAmB;AACnB,qBAAO,KAAK;AAAA,YAChB;AAEA,kBAAM,gBAAgB,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,KAAK;AAC/E,yBAAa,iBAAiB,MAAM;AACpC,gBAAI,KAAK,SAAS,qBAAqB,KAAK,SAAS,mBAAmB,KAAK,SAAS,0BAClF,kBAAkB,IAAI,GAAG;AACzB,kBAAI,kBAAkB,IAAI,KAAK,CAAC,wBAAwB;AACpD,sBAAM,IAAI,oBAAoB,IAAI;AAAA,cACtC;AACA,2BAAa,QAAQ,GAAG;AACxB,kBAAIA;AACJ,kBAAI,KAAK,SAAS,wBAAwB;AACtC,gBAAAA,SAAQ,KAAK,KAAK;AAAA,cACtB;AACA,oBAAM,QAAQ,aAAa,UAAU,WAAW,SAAS;AACzD,qBAAO,iBAAiB,YAAY;AACpC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,KAAK,kBAAkB,IAAI,IAAI,OAAO,KAAK,MAAM,SAAS;AAAA,gBAC1D;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV,MAAM;AAAA,kBACF,OAAAA;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OACK;AACD,kBAAI,CAAC,eAAe;AAChB,sBAAM,IAAI,oBAAoB,IAAI;AAAA,cACtC;AACA,2BAAa,QAAQ,GAAG;AACxB,oBAAM,QAAQ,aAAa,UAAU,WAAW,SAAS;AACzD,qBAAO,iBAAiB,YAAY;AACpC,qBAAO;AAAA,gBACH,MAAM;AAAA,gBACN,MAAM,iBAAiB,IAAI;AAAA,gBAC3B;AAAA,cACJ;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,eAAS,sBAAsB,EAAE,eAAe,cAAc,GAAG;AAC7D,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,YAAY,WAAW;AAAA,UACvB,QAAQ,UAAQ,SAAS;AAAA,UACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,gBAAI,WAAW;AACf,gBAAI,WAAW;AACf,gBAAI,iBAAiB,KAAK,SAAS,qBAAqB;AACpD,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,iBAAiB,KAAK,SAAS,uBAAuB,KAAK,YAAY,QAAW;AAClF,yBAAW;AACX,qBAAO,KAAK;AAAA,YAChB;AACA,gBAAI,KAAK,SAAS,iBAAiB;AAC/B,oBAAM,IAAI,oBAAoB,IAAI;AAAA,YACtC;AACA,mBAAO,QAAQ,GAAG;AAClB,kBAAM,QAAQ,OAAO,UAAU,WAAW,SAAS;AACnD,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,KAAK;AAAA,cACV;AAAA,cACA;AAAA,cACA;AAAA,YACJ;AAAA,UACJ;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,mBAAmB;AAAA,QACrB,GAAG;AAAA,QACH,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,sBAAsB,CAAC,QAAQ,KAAK;AAAA,UACpC,mBAAmB;AAAA,UACnB,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD;AAAA,QACA,6BAA6B;AAAA,UACzB,cAAc,CAAC,UAAU,YAAY,OAAO;AAAA,UAC5C;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA,QACD,kBAAkB;AAAA,UACd,yBAAyB,CAAC,OAAO;AAAA,QACrC,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL;AACA,YAAM,eAAe;AAAA,QACjB,GAAG;AAAA,QACH,oBAAoB;AAAA;AAAA;AAAA,UAGhB,oBAAoB;AAAA,YAChB,kBAAkB;AAAA,cACd,yBAAyB,CAAC,UAAU,IAAI;AAAA,YAC5C,CAAC;AAAA,YACD,yBAAyB;AAAA,cACrB,wBAAwB;AAAA,cACxB,eAAe;AAAA,cACf,eAAe;AAAA,cACf,eAAe;AAAA,YACnB,CAAC;AAAA,YACD,GAAG;AAAA,UACP;AAAA,UACA,eAAe;AAAA,QACnB,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AAEA,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,QAAQ;AACvB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UACzE;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB;AAAA,QACzB,kBAAkB;AAAA,UACd,yBAAyB,CAAC,UAAU,SAAS,SAAS,YAAY,IAAI;AAAA,QAC1E,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,yBAAyB;AAAA,UACrB,wBAAwB;AAAA,UACxB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AACA,YAAM,iBAAiB;AAAA,QACnB,GAAG;AAAA,QACH,oBAAoB;AAAA,UAChB,eAAe;AAAA,UACf,oBAAoB;AAAA,QACxB,CAAC;AAAA,QACD,kBAAkB;AAAA,UACd,yBAAyB,CAAC,SAAS,YAAY,IAAI;AAAA,QACvD,CAAC;AAAA,QACD;AAAA,QACA,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,sBAAsB,CAAC,QAAQ,KAAK;AAAA,UACpC,mBAAmB;AAAA,UACnB,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA;AAAA,QAED,kBAAkB;AAAA,UACd,yBAAyB,CAAC,OAAO;AAAA,QACrC,CAAC;AAAA,QACD,6BAA6B;AAAA,UACzB,cAAc,CAAC,QAAQ;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,MACJ;AAEA,YAAM,iBAAiB,eAAe;AAAA,QAClC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,CAAC,WAAW;AACrB,iBAAO,QAAQ,SAAS;AACxB,gBAAM,OAAO,OAAO,sBAAsB,WAAW,MAAM;AAC3D,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,oBAAoB,MAAM,kEAAkE;AAAA,UAC1G;AACA,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,YACA,OAAO,iBAAiB,OAAO,sBAAsB,WAAW,KAAK,CAAC;AAAA,UAC1E;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,eAAS,mBAAmB,EAAE,kBAAkB,GAAG;AAC/C,eAAO,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,QAAQ,UAAQ,SAAS;AAAA,UACzB,aAAa,YAAU;AACnB,mBAAO,QAAQ,GAAG;AAClB,kBAAM,SAAS;AAAA,cACX,MAAM;AAAA,cACN,UAAU,CAAC;AAAA,YACf;AACA,gBAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,qBAAO;AAAA,YACX;AACA,kBAAM,WAAW,OAAO,sBAAsB,WAAW,GAAG;AAC5D,gBAAI,SAAS,SAAS,0BAA0B;AAC5C,kBAAI,SAAS,SAAS,CAAC,EAAE,SAAS,qBAAqB;AACnD,uBAAO,WAAW,SAAS,SAAS,IAAI,yBAAyB;AAAA,cACrE,OACK;AACD,uBAAO,WAAW,SAAS,SAAS,IAAI,gBAAgB;AAAA,cAC5D;AAAA,YACJ,OACK;AACD,kBAAI,SAAS,SAAS,qBAAqB;AACvC,uBAAO,WAAW,CAAC,0BAA0B,QAAQ,CAAC;AAAA,cAC1D,OACK;AACD,uBAAO,WAAW,CAAC,iBAAiB,QAAQ,CAAC;AAAA,cACjD;AAAA,YACJ;AACA,gBAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,oBAAM,IAAI,MAAM,kBAAoB;AAAA,YACxC;AACA,gBAAI,CAAC,qBAAqB,OAAO,SAAS,KAAK,CAAC,MAAM,EAAE,SAAS,kBAAkB,GAAG;AAClF,oBAAM,IAAI,MAAM,oCAAoC;AAAA,YACxD;AACA,mBAAO;AAAA,UACX;AAAA,QACJ,CAAC;AAAA,MACL;AAEA,YAAM,eAAe,eAAe;AAAA,QAChC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,OAAO;AACtB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,iBAAiB,OAAO,UAAU,WAAW,cAAc,CAAC;AAAA,UACzE;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,gBAAgB,eAAe;AAAA,QACjC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,QAAQ;AACvB,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,0CAA0C;AAAA,UAC9D;AACA,gBAAM,OAAO,OAAO,UAAU,WAAW,MAAM;AAC/C,cAAI,KAAK,SAAS,wBAAwB;AACtC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,kDAAkD;AAAA,UACtE;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS;AAAA,UACb;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,0BAA0B,eAAe;AAAA,QAC3C,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,iBAAO,QAAQ,UAAU;AACzB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,SAAS,OAAO,UAAU,WAAW,SAAS;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,uBAAuB,eAAe;AAAA,QACxC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,YAAY,cAAc,IAAI,EAAE,IAAI,+BAA+B;AAAA,YACnE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,aAAa;AAAA,YACb,YAAY,OAAO,UAAU,WAAW,MAAM;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,sBAAsB,eAAe;AAAA,QACvC,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,WAAW;AAAA,QACvB,YAAY,CAAC,QAAQ,SAAS;AAC1B,iBAAO,QAAQ,GAAG;AAClB,gBAAM,WAAW,CAAC;AAClB,aAAG;AACC,qBAAS,KAAK,OAAO,UAAU,WAAW,YAAY,CAAC;AAAA,UAC3D,SAAS,OAAO,QAAQ,GAAG;AAC3B,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,UAAU,CAAC,iBAAiB,IAAI,GAAG,GAAG,QAAQ;AAAA,UAClD;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,mBAAmB,eAAe;AAAA,QACpC,MAAM;AAAA,QACN,YAAY,WAAW;AAAA,QACvB,QAAQ,UAAQ,SAAS;AAAA,QACzB,YAAY,CAAC,QAAQ,SAAS;AAC1B,cAAI,KAAK,SAAS,iBAAiB;AAC/B,kBAAM,IAAI,oBAAoB,MAAM,oEAAoE;AAAA,UAC5G;AACA,iBAAO,QAAQ,IAAI;AACnB,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,YACA,OAAO,iBAAiB,OAAO,sBAAsB,WAAW,KAAK,CAAC;AAAA,UAC1E;AAAA,QACJ;AAAA,MACJ,CAAC;AAED,YAAM,+BAA+B,eAAe;AAAA,QAChD,MAAM;AAAA,QACN,QAAQ,UAAQ,SAAS;AAAA,QACzB,aAAa,YAAU;AACnB,cAAI,OAAO,eAAe,QAAW;AACjC,kBAAM,IAAI,MAAM,oCAAoC;AAAA,UACxD;AACA,iBAAO,QAAQ,GAAG;AAClB,gBAAM,MAAM,OAAO,MAAM,QAAQ;AACjC,iBAAO,QAAQ,YAAY;AAC3B,cAAI;AACJ,cAAI,OAAO,QAAQ,GAAG,GAAG;AACrB,kBAAM,eAAe,OAAO;AAC5B,yBAAa,iBAAiB,MAAM;AACpC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN;AAAA,cACA,OAAO,aAAa,UAAU,WAAW,cAAc;AAAA,YAC3D;AACA,mBAAO,iBAAiB,YAAY;AAAA,UACxC,WACS,OAAO,QAAQ,IAAI,GAAG;AAC3B,kBAAM,eAAe,OAAO;AAC5B,yBAAa,iBAAiB,MAAM;AACpC,qBAAS;AAAA,cACL,MAAM;AAAA,cACN;AAAA,cACA,OAAO,aAAa,UAAU,WAAW,cAAc;AAAA,YAC3D;AACA,mBAAO,iBAAiB,YAAY;AAAA,UACxC,OACK;AACD,kBAAM,IAAI,MAAM,uDAA2D;AAAA,UAC/E;AACA,cAAI,CAAC,OAAO,QAAQ,GAAG,GAAG;AACtB,kBAAM,IAAI,MAAM,8BAA8B;AAAA,UAClD;AACA,iBAAO;AAAA,QACX;AAAA,MACJ,CAAC;AAED,YAAM,qBAAqB;AAAA,QACvB;AAAA,QACA,kBAAkB;AAAA,UACd,yBAAyB,CAAC,UAAU,SAAS,SAAS,SAAS,YAAY,IAAI;AAAA,QACnF,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,yBAAyB;AAAA,UACrB,wBAAwB;AAAA,UACxB,eAAe;AAAA,UACf,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,QACD;AAAA,MACJ;AACA,YAAM,oBAAoB;AAAA,QACtB,GAAG;AAAA,QACH,oBAAoB;AAAA,UAChB,eAAe;AAAA,UACf;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,yBAAyB;AAAA,UACzB,mBAAmB;AAAA,UACnB,sBAAsB,CAAC,QAAQ,OAAO,MAAM;AAAA,UAC5C,2BAA2B;AAAA,QAC/B,CAAC;AAAA,QACD,mBAAmB;AAAA,UACf,mBAAmB;AAAA,QACvB,CAAC;AAAA,QACD,sBAAsB;AAAA,UAClB,wBAAwB;AAAA,UACxB,cAAc;AAAA,QAClB,CAAC;AAAA,QACD;AAAA,QACA,kBAAkB;AAAA,UACd,yBAAyB,CAAC,SAAS,YAAY,IAAI;AAAA,QACvD,CAAC;AAAA,QACD,6BAA6B;AAAA,UACzB,cAAc,CAAC,QAAQ;AAAA,UACvB;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,8BAA8B;AAAA,UAC9B,qBAAqB;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,QACD;AAAA,QACA;AAAA,QACA,sBAAsB;AAAA,UAClB,eAAe;AAAA,UACf,eAAe;AAAA,QACnB,CAAC;AAAA,MACL;AAOA,eAAS,MAAM,YAAY,MAAM;AAC7B,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAQ,IAAI,OAAO,gBAAgB,UAAU,EAAG,MAAM;AAAA,UAC1D,KAAK;AACD,mBAAQ,IAAI,OAAO,cAAc,UAAU,EAAG,MAAM;AAAA,UACxD,KAAK;AACD,mBAAQ,IAAI,OAAO,mBAAmB,UAAU,EAAG,MAAM;AAAA,QACjE;AAAA,MACJ;AAQA,eAAS,SAAS,YAAY,QAAQ,CAAC,cAAc,WAAW,OAAO,GAAG;AACtE,YAAI;AACJ,mBAAW,QAAQ,OAAO;AACtB,cAAI;AACA,mBAAO,MAAM,YAAY,IAAI;AAAA,UACjC,SACO,GAAG;AACN,oBAAQ;AAAA,UACZ;AAAA,QACJ;AACA,cAAM;AAAA,MACV;AAEA,eAAS,UAAUC,QAAO,aAAa;AACnC,cAAM,OAAOA,OAAM,YAAY,IAAI;AACnC,YAAI,SAAS,QAAW;AACpB,gBAAM,IAAI,MAAM,0DAA0D,YAAY,IAAI,GAAG;AAAA,QACjG;AACA,eAAO,KAAK,aAAa,kBAAgB,UAAUA,QAAO,YAAY,CAAC;AAAA,MAC3E;AACA,eAAS,sBAAsB,aAAa;AACxC,cAAM,IAAI,MAAM,2EAA2E;AAAA,MAC/F;AACA,eAAS,qBAAqB,QAAQ;AAClC,cAAM,SAAS;AAAA,UACX,QAAQ,CAAC;AAAA,QACb;AACA,mBAAW,SAAS,OAAO,YAAY;AACnC,cAAI,MAAM,SAAS,qBAAqB;AACpC,gBAAI,MAAM,QAAQ,QAAQ;AACtB,qBAAO,OAAO,MAAM;AAAA,YACxB,WACS,MAAM,QAAQ,OAAO;AAC1B,qBAAO,MAAM,MAAM;AAAA,YACvB,OACK;AACD,qBAAO,OAAO,KAAK,KAAK;AAAA,YAC5B;AAAA,UACJ,OACK;AACD,mBAAO,OAAO,KAAK,KAAK;AAAA,UAC5B;AAAA,QACJ;AACA,eAAO;AAAA,MACX;AAEA,eAAS,cAAc,UAAU,QAAQ,OAAO;AAC5C,eAAO,aAAa,WAAW,QAAQ,SAAS,SAAS;AAAA,MAC7D;AACA,eAAS,MAAM,OAAOD,QAAO;AACzB,gBAAQA,QAAO;AAAA,UACX,KAAK;AACD,mBAAO,IAAI,KAAK;AAAA,UACpB,KAAK;AACD,mBAAO,IAAI,KAAK;AAAA,UACpB,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,iBAAiB;AACtB,eAAO;AAAA,UACH,sBAAsB,CAAC,QAAQE,eAAc,IAAI,OAAO,YAAY,SAAYA,WAAU,OAAO,OAAO,IAAI,EAAE;AAAA,UAC9G,gBAAgB,CAAC,QAAQA,eAAc,SAASA,WAAU,OAAO,OAAO,CAAC;AAAA,UACzE,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAI,CAAC,OAAO,OAAO;AACf,kBAAI,cAAc,OAAO,cAAc,QAAQ;AAC/C,kBAAI,CAAC,OAAO,aAAa;AACrB,uBAAO;AAAA,cACX;AACA,6BAAe,IAAI,OAAO,WAAW,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAC9D,kBAAI,OAAO,eAAe,QAAW;AACjC,+BAAe,KAAKA,WAAU,OAAO,UAAU,CAAC;AAAA,cACpD;AACA,qBAAO;AAAA,YACX,OACK;AACD,kBAAI,OAAO,eAAe,QAAW;AACjC,sBAAM,IAAI,MAAM,qCAAqC;AAAA,cACzD;AACA,kBAAI,cAAc,IAAI,OAAO,WAAW,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC,QAAQA,WAAU,OAAO,UAAU,CAAC;AACrG,kBAAI,OAAO,aAAa;AACpB,8BAAc,SAAS;AAAA,cAC3B;AACA,qBAAO;AAAA,YACX;AAAA,UACJ;AAAA,UACA,eAAe,YAAU,OAAO;AAAA,UAChC,gBAAgB,CAAC,QAAQA,eAAc,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAAA,UACpF,mBAAmB,CAAC,QAAQA,eAAc,OAAO,KAAK,aAAa,SAC7D,QACA,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,KAAK;AAAA,UAC1E,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,OAAOA,WAAU,OAAO,IAAI;AAClC,kBAAM,QAAQA,WAAU,OAAO,KAAK;AACpC,oBAAQ,OAAO,UAAU;AAAA,cACrB,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,cAC3B,KAAK;AACD,uBAAO,GAAG,IAAI,IAAI,KAAK;AAAA,YAC/B;AAAA,UACJ;AAAA,UACA,sBAAsB,YAAU,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AAAA,UACrE,cAAc,MAAM;AAAA,UACpB,kBAAkB,CAAC,QAAQA,eAAc;AACrC,gBAAI,OAAO,KAAK,aAAa,UAAU;AACnC,oBAAM,UAAU,OAAO,SAAS,CAAC;AACjC,oBAAM,cAAcA,WAAU,OAAO;AACrC,kBAAI,QAAQ,SAAS,oBAAoB,QAAQ,SAAS,yBAAyB;AAC/E,uBAAO,IAAI,WAAW;AAAA,cAC1B,OACK;AACD,uBAAO,GAAG,WAAW;AAAA,cACzB;AAAA,YACJ,OACK;AACD,qBAAO,GAAGA,WAAU,OAAO,IAAI,CAAC,GAAG,OAAO,KAAK,MAAM,MAAM,EAAE,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,IAAI,CAAC;AAAA,YAC9G;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,eAAc,UAAUA,WAAU,OAAO,OAAO,CAAC;AAAA,UAC3E,sBAAsB,CAAC,QAAQA,eAAc;AACzC,gBAAI,OAAO;AACX,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,sBAAQ,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK;AAAA,YAC/C,OACK;AACD,sBAAQA,WAAU,OAAO,GAAG;AAAA,YAChC;AACA,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,UAAU,QAAW;AAC5B,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,OAAO,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,YAC9C;AAAA,UACJ;AAAA,UACA,2BAA2B,CAAC,QAAQA,eAAc;AAC9C,mBAAO,GAAGA,WAAU,OAAO,IAAI,CAAC,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,UAChE;AAAA,UACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAI,OAAO,OAAO;AAClB,gBAAI,OAAO,UAAU;AACjB,sBAAQ;AAAA,YACZ;AACA,gBAAI,OAAO,UAAU;AACjB,qBAAO,QAAQ;AAAA,YACnB;AACA,gBAAI,OAAO,UAAU,QAAW;AAC5B,qBAAO;AAAA,YACX,OACK;AACD,qBAAO,OAAO,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,YAC9C;AAAA,UACJ;AAAA,UACA,0BAA0B,YAAU,GAAG,OAAO,WAAW,IAAI,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,UACnG,sBAAsB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC/G,eAAe,MAAM;AAAA,UACrB,mBAAmB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC5G,iBAAiB,YAAU,OAAO,MAAM,SAAS;AAAA,UACjD,iBAAiB,CAAC,QAAQA,eAAc,IAAI,OAAO,SAAS,IAAIA,UAAS,EAAE,MAAM,OAAO,KAAK,cAAc,UAAU,MAAM,OAAO,GAAG,CAAC;AAAA,UACtI,mBAAmB,CAAC,QAAQA,eAAc,cAAc,OAAO,KAAK,UAAUA,WAAU,OAAO,OAAO,GAAG,GAAG;AAAA,UAC5G,iBAAiB,CAAC,QAAQA,eAAc,GAAG,OAAO,KAAK,IAAI,OAAO,YAAY,SAAYA,WAAU,OAAO,OAAO,IAAI,EAAE;AAAA,UACxH,iBAAiB,CAAC,QAAQA,eAAc,UAAUA,WAAU,OAAO,OAAO,CAAC;AAAA,UAC3E,oBAAoB,MAAM;AAAA,UAC1B,gBAAgB,CAAC,QAAQA,eAAc,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,KAAK;AAAA,UAChF,kBAAkB,MAAM;AAAA,UACxB,uBAAuB,CAAC,QAAQA,eAAc,OAAO,SAAS,IAAIA,UAAS,EAAE,KAAK,KAAK;AAAA,UACvF,mBAAmB,YAAU,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK;AAAA,UAClE,oBAAoB,CAAC,QAAQA,eAAc,GAAGA,WAAU,OAAO,IAAI,CAAC,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,UAClG,yBAAyB,CAAC,QAAQA,eAAc,IAAI,OAAO,GAAG,KAAKA,WAAU,OAAO,KAAK,CAAC;AAAA,UAC1F,qBAAqB,CAAC,QAAQA,eAAc,IAAI,OAAO,GAAG,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,UACxF,kBAAkB,CAAC,QAAQA,eAAc,WAAWA,WAAU,OAAO,IAAI,CAAC,OAAOA,WAAU,OAAO,KAAK,CAAC;AAAA,QAC5G;AAAA,MACJ;AACA,YAAM,uBAAuB,eAAe;AAC5C,eAAS,UAAU,QAAQ;AACvB,eAAO,UAAU,sBAAsB,MAAM;AAAA,MACjD;AAEA,YAAM,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AACA,eAAS,SAAS,OAAO;AACrB,cAAM,SAAS;AAAA,UACX,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AACA,YAAI,cAAc,SAAS,KAAK,GAAG;AAC/B,iBAAO,eAAe;AAAA,QAC1B;AACA,eAAO;AAAA,MACX;AACA,YAAM,0BAA0B;AAAA,QAC5B,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,WAAW;AACvB,iBAAO;AAAA,QACX;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,OAAO,YAAY,QAAW;AAC9B,kBAAM,IAAI,MAAM,sDAAsD;AAAA,UAC1E;AACA,gBAAM,cAAcA,WAAU,OAAO,OAAO;AAC5C,sBAAY,aAAa;AACzB,iBAAO;AAAA,QACX;AAAA,QACA,cAAc,OAAO;AAAA,UACjB,MAAM;AAAA,QACV;AAAA,QACA,eAAe,OAAO;AAAA,UAClB,MAAM;AAAA,QACV;AAAA,QACA,sBAAsB,YAAU,SAAS,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,QAC/E,oBAAoB,OAAO;AAAA,UACvB,MAAM;AAAA,QACV;AAAA,QACA,kBAAkB,OAAO;AAAA,UACrB,MAAM;AAAA,QACV;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,SAAS,qBAAqB,MAAM;AAC1C,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ,OAAO,OAAO,IAAIA,UAAS;AAAA,UACvC;AACA,cAAI,OAAO,SAAS,QAAW;AAC3B,wBAAY,OAAOA,WAAU,OAAO,IAAI;AAAA,UAC5C;AACA,cAAI,OAAO,QAAQ,QAAW;AAC1B,wBAAY,MAAMA,WAAU,OAAO,GAAG;AAAA,UAC1C;AACA,cAAI,OAAO,eAAe,QAAW;AACjC,wBAAY,SAASA,WAAU,OAAO,UAAU;AAAA,UACpD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,UACtC,MAAM;AAAA,UACN,cAAc,OAAO,SAAS,IAAI,CAAAC,OAAKD,WAAUC,EAAC,CAAC;AAAA,UACnD,YAAYD,WAAU,OAAO,IAAI;AAAA,QACrC;AAAA,QACA,0BAA0B,YAAU,SAAS,OAAO,cAAc,MAAM,MAAM,OAAO,OAAO,OAAO,KAAK,KAAK,CAAC;AAAA,QAC9G,eAAe,YAAU;AACrB,cAAI,OAAO,UAAU,YAAY;AAC7B,mBAAO,SAAS,OAAO,KAAK;AAAA,UAChC,OACK;AACD,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,QAAQ,CAAC;AAAA,YACb;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,iBAAiB,YAAU,SAAS,OAAO,MAAM,SAAS,CAAC;AAAA,QAC3D,iBAAiB,CAAC,QAAQA,eAAc;AACpC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,QAAQ,CAAC;AAAA,UACb;AACA,qBAAW,SAAS,OAAO,UAAU;AACjC,gBAAI,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AACrF,0BAAY,OAAO,KAAK;AAAA,gBACpB,MAAM;AAAA,gBACN,KAAKA,WAAU,KAAK;AAAA,gBACpB,OAAO;AAAA,cACX,CAAC;AAAA,YACL,OACK;AACD,0BAAY,OAAO,KAAKA,WAAU,KAAK,CAAC;AAAA,YAC5C;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,cAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,SAAS,MAAM,OAAO,KAAK,OAAO,KAAK,KAAK,CAAC;AAAA,YAClD,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,UAC1E;AAAA,QACJ;AAAA,QACA,2BAA2B,CAAC,QAAQA,gBAAe;AAAA,UAC/C,MAAM;AAAA,UACN,KAAKA,WAAU,OAAO,IAAI;AAAA,UAC1B,OAAOA,WAAU,OAAO,KAAK;AAAA,QACjC;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,UAAU,OAAO,SAAS,IAAI,OAAKA,WAAU,CAAC,CAAC;AAAA,QACnD;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,SAAS,OAAO,GAAG;AAAA,YACxB,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,UAC1E;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,aAAaA,WAAU,OAAO,IAAI;AACxC,cAAI;AACJ,cAAI,OAAO,MAAM,SAAS,4BAA4B;AAClD,yBAAaA,WAAU,OAAO,KAAK,EAAE;AAAA,UACzC,OACK;AACD,yBAAa,MAAM,OAAO,MAAM,OAAO,OAAO,MAAM,KAAK,KAAK;AAAA,UAClE;AACA,gBAAM,SAAS,OAAO,aAAa,UAAU,MAAM,OAAO,aAAa,aAAa,MAAM;AAC1F,iBAAO,SAAS,GAAG,WAAW,IAAI,GAAG,MAAM,GAAG,UAAU,EAAE;AAAA,QAC9D;AAAA,QACA,iBAAiB,YAAU;AACvB,cAAI,QAAQ;AACZ,cAAI,UAAU,OAAO;AACrB,cAAI,eAAe;AACnB,eAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,qBAAqB;AAC1F,gBAAI,QAAQ,KAAK,aAAa,UAAU;AACpC,sBAAQ;AAAA,YACZ,OACK;AACD,6BAAe;AAAA,YACnB;AACA,sBAAU,QAAQ;AAAA,UACtB;AACA,eAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,iBAAiB;AACtF,qBAAS,QAAQ;AAAA,UACrB,YACU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,UAAU,mBAAmB;AAC7F,qBAAS,QAAQ,MAAM,SAAS;AAAA,UACpC;AACA,cAAI,cAAc;AACd,qBAAS;AAAA,UACb;AACA,iBAAO,SAAS,GAAG,OAAO,KAAK,IAAI,KAAK,GAAG;AAAA,QAC/C;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAcA,WAAU,iBAAiB,OAAO,OAAO,CAAC;AAAA,QACvF,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,QACzB,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,uBAAuB;AAAA,QACvB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,kBAAkB;AAAA,MACtB;AACA,eAAS,mBAAmB,QAAQ;AAChC,eAAO,UAAU,yBAAyB,MAAM;AAAA,MACpD;AAEA,eAAS,cAAcF,QAAO;AAC1B,gBAAQA,QAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,cAAc,MAAM;AACzB,gBAAQ,MAAM;AAAA,UACV,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,UACX,KAAK;AACD,mBAAO;AAAA,QACf;AAAA,MACJ;AACA,eAAS,YAAY,MAAM,SAAS;AAChC,YAAI,QAAQ,WAAW,GAAG;AACtB,iBAAO;AAAA,YACH;AAAA,YACA,MAAM,QAAQ,CAAC;AAAA,YACf,OAAO,QAAQ,CAAC;AAAA,UACpB;AAAA,QACJ,OACK;AACD,iBAAO;AAAA,YACH;AAAA,YACA,MAAM,QAAQ,CAAC;AAAA,YACf,OAAO,YAAY,MAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,UAC7C;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,WAAW;AAAA,QACb,mBAAmB,CAAC,QAAQE,gBAAe;AAAA,UACvC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,sBAAsB;AAAA,UACtE;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,gBAAe;AAAA,UACvC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,yBAAyB;AAAA,UACzE;AAAA,QACJ;AAAA,QACA,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,UAC1C,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,UAC/B,MAAM;AAAA,YACF,QAAQ,OAAO,KAAK,aAAa,WAAW,gBAAgB;AAAA,UAChE;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,MAAM;AAAA,cACF,QAAQ,OAAO,KAAK,aAAa,WAC3B,gBACA,OAAO,KAAK,aAAa,WAAW,gBAAgB;AAAA,YAC9D;AAAA,UACJ;AACA,cAAI,OAAO,YAAY,QAAW;AAC9B,wBAAY,QAAQA,WAAU,OAAO,OAAO;AAAA,UAChD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,eAAe,aAAW;AAAA,UACtB,MAAM;AAAA,UACN,MAAM,OAAO;AAAA,QACjB;AAAA,QACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,UACrC,MAAM;AAAA,UACN,MAAMA,WAAU,OAAO,OAAO;AAAA,QAClC;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,SAAS,OAAO,SAAS,IAAIA,UAAS;AAAA,QAC1C;AAAA,QACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,UACpC,MAAM;AAAA,UACN,OAAOA,WAAU,OAAO,OAAO;AAAA,QACnC;AAAA,QACA,iBAAiB,aAAW;AAAA,UACxB,MAAM;AAAA,UACN,MAAM;AAAA,YACF,MAAM;AAAA,YACN,YAAY,cAAc,OAAO,QAAQ,KAAK,KAAK;AAAA,YACnD,QAAQ,OAAO,QAAQ;AAAA,UAC3B;AAAA,QACJ;AAAA,QACA,oBAAoB,OAAO;AAAA,UACvB,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,QACA,cAAc,OAAO;AAAA,UACjB,MAAM;AAAA,QACV;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,gBAAM,gBAAgB,qBAAqB,MAAM;AACjD,gBAAM,cAAc;AAAA,YAChB,MAAM,OAAO,QAAQ,UAAU;AAAA,YAC/B,QAAQ,cAAc,OAAO,IAAI,WAAS;AACtC,kBAAI,MAAM,SAAS,qBAAqB;AACpC,oBAAI,MAAM,UAAU,QAAW;AAC3B,wBAAM,IAAI,MAAM,kEAAsE;AAAA,gBAC1F;AACA,uBAAO;AAAA,kBACH,MAAM;AAAA,kBACN,MAAM,MAAM;AAAA,kBACZ,UAAUA,WAAU,MAAM,KAAK;AAAA,gBACnC;AAAA,cACJ,OACK;AACD,uBAAOA,WAAU,KAAK;AAAA,cAC1B;AAAA,YACJ,CAAC;AAAA,YACD,KAAK;AAAA,YACL,SAAS;AAAA,UACb;AACA,cAAI,cAAc,SAAS,QAAW;AAClC,wBAAY,OAAOA,WAAU,cAAc,IAAI;AAAA,UACnD,WACS,CAAC,OAAO,OAAO;AACpB,wBAAY,OAAO;AAAA,UACvB;AACA,cAAI,cAAc,QAAQ,QAAW;AACjC,wBAAY,MAAMA,WAAU,cAAc,GAAG;AAAA,UACjD;AACA,cAAI,OAAO,eAAe,QAAW;AACjC,wBAAY,UAAUA,WAAU,OAAO,UAAU;AAAA,UACrD;AACA,iBAAO;AAAA,QACX;AAAA,QACA,kBAAkB,CAAC,QAAQA,eAAc;AACrC,gBAAM,cAAc;AAAA,YAChB,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,IAAI;AAAA,YAC9B,SAAS,OAAO,SAAS,IAAIA,UAAS;AAAA,YACtC,MAAM;AAAA,cACF,QAAQ,OAAO,KAAK,aAAa,WAAW,mBAAmB,OAAO,KAAK,MAAM,2BAA2B;AAAA,YAChH;AAAA,UACJ;AACA,cAAI,OAAO,KAAK,aAAa,YAAY,OAAO,SAAS,CAAC,EAAE,SAAS,uBAAuB,CAAC,OAAO,SAAS,CAAC,EAAE,aAAa;AACzH,wBAAY,QAAQ,CAAC,IAAI;AAAA,cACrB,MAAM;AAAA,cACN,MAAM;AAAA,YACV;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,sBAAsB,CAAC,QAAQA,eAAc;AACzC,cAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,kBAAM,IAAI,MAAM,qDAAqD;AAAA,UACzE;AACA,cAAI,OAAO,UAAU,QAAW;AAC5B,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,cAC3C,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,QAAQA,WAAU,OAAO,KAAK;AAClC,cAAI,OAAO,UAAU;AACjB,oBAAQ;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,gBACF,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,OAAO,IAAI,SAAS;AAAA,YACzB,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,YAC3C,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,2BAA2B,MAAM;AAC7B,gBAAM,IAAI,MAAM,2CAA2C;AAAA,QAC/D;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,OAAO,UAAU,QAAW;AAC5B,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,UAAU;AAAA,YACd;AAAA,UACJ;AACA,cAAI,QAAQA,WAAU,OAAO,KAAK;AAClC,cAAI,OAAO,UAAU;AACjB,oBAAQ;AAAA,cACJ,MAAM;AAAA,cACN,OAAO;AAAA,cACP,MAAM;AAAA,gBACF,QAAQ;AAAA,cACZ;AAAA,YACJ;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,UAAU;AAAA,UACd;AAAA,QACJ;AAAA,QACA,iBAAiB,CAAC,QAAQA,eAAc;AACpC,gBAAM,UAAU,CAAC;AACjB,qBAAW,SAAS,OAAO,UAAU;AACjC,gBAAI,MAAM,SAAS,0BAA0B,MAAM,SAAS,6BAA6B;AACrF,sBAAQ,KAAKA,WAAU,KAAK,CAAC;AAAA,YACjC;AAAA,UACJ;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,0BAA0B,YAAU;AAChC,cAAI,OAAO,gBAAgB,UAAU;AACjC,kBAAM,IAAI,MAAM,yCAAyC,OAAO,WAAW,iBAAiB;AAAA,UAChG;AACA,iBAAO;AAAA,YACH,MAAM;AAAA,YACN,OAAO;AAAA,cACH,MAAM;AAAA,cACN,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,cAC3C,MAAM,OAAO;AAAA,YACjB;AAAA,UACJ;AAAA,QACJ;AAAA,QACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,cAAI,iBAAiB;AACrB,cAAI;AACJ,cAAI;AACJ,cAAI,OAAO,MAAM,SAAS,8BAA8B,OAAO,MAAM,gBAAgB,SAAS;AAC1F,6BAAiB;AACjB,mBAAO,OAAO,MAAM;AACpB,yBAAa,cAAc,OAAO,MAAM,KAAK,KAAK;AAAA,UACtD,OACK;AACD,mBAAO,OAAO,MAAM;AACpB,yBAAa,cAAc,OAAO,MAAM,KAAK,KAAK;AAAA,UACtD;AACA,gBAAM,cAAc;AAAA,YAChB,MAAM,cAAc,OAAO,QAAQ;AAAA,YACnC,OAAOA,WAAU,OAAO,IAAI;AAAA,YAC5B;AAAA,YACA;AAAA,YACA;AAAA,UACJ;AACA,cAAI,YAAY,MAAM,SAAS,UAAU;AACrC,kBAAM,UAAU,YAAY;AAC5B,wBAAY,QAAQ,YAAY,MAAM;AACtC,oBAAQ,QAAQ;AAChB,mBAAO;AAAA,UACX,OACK;AACD,mBAAO;AAAA,UACX;AAAA,QACJ;AAAA,QACA,gBAAgB,CAAC,QAAQA,eAAc,YAAY,SAAS,OAAO,SAAS,IAAIA,UAAS,CAAC;AAAA,QAC1F,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,UAC1C,MAAM;AAAA,UACN,OAAOA,WAAU,iBAAiB,OAAO,OAAO,CAAC;AAAA,QACrD;AAAA,QACA,eAAe,OAAO;AAAA,UAClB,MAAM;AAAA,UACN,MAAM;AAAA,QACV;AAAA,QACA,kBAAkB,OAAO;AAAA,UACrB,MAAM;AAAA,QACV;AAAA,QACA,sBAAsB,aAAW;AAAA,UAC7B,MAAM;AAAA,UACN,YAAY,cAAc,OAAO,KAAK,KAAK;AAAA,UAC3C,QAAQ,OAAO;AAAA,QACnB;AAAA,QACA,uBAAuB,CAAC,QAAQA,eAAc,YAAY,gBAAgB,OAAO,SAAS,IAAIA,UAAS,CAAC;AAAA,QACxG,iBAAiB,aAAW;AAAA,UACxB,MAAM;AAAA,UACN,QAAQ,OAAO,MAAM,SAAS;AAAA,QAClC;AAAA,QACA,iBAAiB;AAAA,QACjB,mBAAmB;AAAA,QACnB,oBAAoB;AAAA,QACpB,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,QACzB,kBAAkB;AAAA,MACtB;AACA,eAAS,aAAa,QAAQ;AAC1B,eAAO,UAAU,UAAU,MAAM;AAAA,MACrC;AAEA,eAAS,yBAAyB;AAC9B,eAAO;AAAA,UACH,uBAAuB,CAAC,QAAQA,gBAAe;AAAA,YAC3C,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,YACtC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,YACvC,MAAM;AAAA,cACF,KAAK,OAAO,KAAK;AAAA,cACjB,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,mBAAmB,YAAU;AAAA,UAC7B,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,kBAAkB,YAAU;AAAA,UAC5B,oBAAoB,YAAU;AAAA,UAC9B,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,iBAAiB,CAAC,QAAQA,eAAc;AACpC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,OAAO;AAAA,YAClB;AACA,gBAAI,OAAO,YAAY,QAAW;AAC9B,0BAAY,UAAUA,WAAU,OAAO,OAAO;AAAA,YAClD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,mBAAmB,CAAC,QAAQA,gBAAe;AAAA,YACvC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,YACjC,MAAM;AAAA,cACF,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,MAAM;AAAA,cACF,WAAW;AAAA,YACf;AAAA,YACA,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,iBAAiB,YAAU;AAAA,UAC3B,eAAe,YAAU;AAAA,UACzB,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,YACjC,MAAM;AAAA,cACF,UAAU,OAAO,KAAK;AAAA,YAC1B;AAAA,UACJ;AAAA,UACA,0BAA0B,YAAU;AAAA,UACpC,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,YACtE,UAAU,OAAO;AAAA,YACjB,UAAU,OAAO;AAAA,YACjB,MAAM,OAAO;AAAA,UACjB;AAAA,UACA,2BAA2B,CAAC,QAAQA,gBAAe;AAAA,YAC/C,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,mBAAmB,CAAC,QAAQA,eAAc;AACtC,mBAAO;AAAA,cACH,MAAM;AAAA,cACN,KAAK,OAAO;AAAA,cACZ,OAAO,OAAO,UAAU,SAAY,SAAYA,WAAU,OAAO,KAAK;AAAA,cACtE,UAAU,OAAO;AAAA,cACjB,UAAU,OAAO;AAAA,YACrB;AAAA,UACJ;AAAA,UACA,iBAAiB,CAAC,QAAQA,gBAAe;AAAA,YACrC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,cAAc,YAAU;AAAA,UACxB,sBAAsB,YAAU;AAAA,UAChC,mBAAmB,YAAU;AAAA,UAC7B,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,MAAM;AAAA,gBACF,UAAU,OAAO,KAAK;AAAA,gBACtB,gBAAgB,OAAO,KAAK;AAAA,cAChC;AAAA,YACJ;AACA,gBAAI,OAAO,YAAY,QAAW;AAC9B,0BAAY,UAAUA,WAAU,OAAO,OAAO;AAAA,YAClD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,UAAU,OAAO,SAAS,IAAIA,UAAS;AAAA,UAC3C;AAAA,UACA,eAAe,YAAU;AAAA,UACzB,mBAAmB,CAAC,QAAQA,eAAc;AACtC,kBAAM,cAAc;AAAA,cAChB,MAAM;AAAA,cACN,OAAO,OAAO;AAAA,cACd,YAAY,OAAO,WAAW,IAAIA,UAAS;AAAA,cAC3C,aAAa,OAAO;AAAA,cACpB,aAAa,OAAO;AAAA,YACxB;AACA,gBAAI,OAAO,eAAe,QAAW;AACjC,0BAAY,aAAaA,WAAU,OAAO,UAAU;AAAA,YACxD;AACA,mBAAO;AAAA,UACX;AAAA,UACA,gBAAgB,CAAC,QAAQA,gBAAe;AAAA,YACpC,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,sBAAsB,CAAC,QAAQA,gBAAe;AAAA,YAC1C,MAAM;AAAA,YACN,SAASA,WAAU,OAAO,OAAO;AAAA,UACrC;AAAA,UACA,mBAAmB,YAAU;AAAA,UAC7B,oBAAoB,CAAC,QAAQA,gBAAe;AAAA,YACxC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,yBAAyB,CAAC,QAAQA,gBAAe;AAAA,YAC7C,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,qBAAqB,CAAC,QAAQA,gBAAe;AAAA,YACzC,MAAM;AAAA,YACN,KAAK,OAAO;AAAA,YACZ,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,UACA,kBAAkB,CAAC,QAAQA,gBAAe;AAAA,YACtC,MAAM;AAAA,YACN,MAAMA,WAAU,OAAO,IAAI;AAAA,YAC3B,OAAOA,WAAU,OAAO,KAAK;AAAA,UACjC;AAAA,QACJ;AAAA,MACJ;AAEA,YAAM,cAAc;AAAA,QAChB,cAAc,CAAC;AAAA,QACf,mBAAmB,CAAC,cAAc,YAAY;AAAA,QAC9C,kBAAkB,CAAC,QAAQ,UAAU;AAAA,QACrC,iBAAiB,CAAC;AAAA,QAClB,yBAAyB,CAAC,OAAO;AAAA,QACjC,uBAAuB,CAAC,UAAU;AAAA,QAClC,gBAAgB,CAAC,SAAS;AAAA,QAC1B,mBAAmB,CAAC,OAAO;AAAA,QAC3B,qBAAqB,CAAC,OAAO;AAAA,QAC7B,eAAe,CAAC;AAAA,QAChB,mBAAmB,CAAC,QAAQ,OAAO;AAAA,QACnC,sBAAsB,CAAC,SAAS;AAAA,QAChC,eAAe,CAAC;AAAA,QAChB,mBAAmB,CAAC,SAAS;AAAA,QAC7B,iBAAiB,CAAC;AAAA,QAClB,iBAAiB,CAAC,UAAU;AAAA,QAC5B,sBAAsB,CAAC,OAAO;AAAA,QAC9B,2BAA2B,CAAC,QAAQ,OAAO;AAAA,QAC3C,mBAAmB,CAAC,SAAS;AAAA,QAC7B,sBAAsB,CAAC,SAAS;AAAA,QAChC,0BAA0B,CAAC;AAAA,QAC3B,sBAAsB,CAAC;AAAA,QACvB,iBAAiB,CAAC,SAAS;AAAA,QAC3B,gBAAgB,CAAC,UAAU;AAAA,QAC3B,iBAAiB,CAAC,SAAS;AAAA,QAC3B,oBAAoB,CAAC;AAAA,QACrB,gBAAgB,CAAC,UAAU;AAAA,QAC3B,kBAAkB,CAAC;AAAA,QACnB,mBAAmB,CAAC,SAAS;AAAA,QAC7B,mBAAmB,CAAC;AAAA,QACpB,oBAAoB,CAAC,QAAQ,OAAO;AAAA,QACpC,kBAAkB,CAAC,QAAQ,OAAO;AAAA,MACtC;AAEA,eAAS,UAAU,MAAM,YAAY,UAAU,SAAS,SAAS;AAC7D,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,YAAY,QAAQ;AACpF,cAAM,cAAc,YAAY,KAAK,IAAI;AACzC,mBAAW,OAAO,aAAa;AAC3B,gBAAM,QAAQ,KAAK,GAAG;AACtB,cAAI,UAAU,QAAW;AACrB,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,yBAAW,WAAW,OAAO;AACzB,0BAAU,SAAS,MAAM,KAAK,SAAS,OAAO;AAAA,cAClD;AAAA,YACJ,OACK;AACD,wBAAU,OAAO,MAAM,KAAK,SAAS,OAAO;AAAA,YAChD;AAAA,UACJ;AAAA,QACJ;AACA,oBAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,MAAM,YAAY,QAAQ;AAAA,MACxF;AAOA,eAAS,SAAS,MAAM,SAAS,SAAS;AACtC,kBAAU,MAAM,QAAW,QAAW,SAAS,OAAO;AAAA,MAC1D;AAEA,MAAAP,SAAQ,qBAAqB;AAC7B,MAAAA,SAAQ,yBAAyB;AACjC,MAAAA,SAAQ,eAAe;AACvB,MAAAA,SAAQ,QAAQ;AAChB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,iBAAiB;AACzB,MAAAA,SAAQ,YAAY;AACpB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,WAAW;AACnB,MAAAA,SAAQ,cAAc;AAAA,IAE1B,CAAE;AAAA;AAAA;;;ACngFF,4BAA2C;AAyG3C,IAAAS,yBAA2C;AA+X3C,qCAIO;AAgXP,yBAAwC;AAh2BxC,IAAI,KAAK,OAAO;AAChB,IAAI,IAAI,CAAC,GAAG,MAAM,GAAG,GAAG,QAAQ,EAAE,OAAO,GAAG,cAAc,KAAG,CAAC;AAI9D,IAAI,KAAqB,EAAE,CAAC,MAAM,EAAE,SAAS,WAAW,WAAW;AAAnE,IAAsE,KAAqB,EAAE,CAAC,MAAM,EAAE,MAAM,QAAQ,UAAU,EAAE,GAAG,cAC9H;AADL,IACQ,KAAqB,EAAE,CAAC,MAAM;AACpC,UAAQ,EAAE,MAAM;AAAA,IACd,KAAK;AACH,aAAO,EAAE,MAAM,WAAW;AAAA,IAC5B,KAAK;AACH,UAAI,IAAI,CAAC;AACT,aAAO,EAAE,UAAU,WAAW,QAAQ,CAAC,MAAM;AAC3C,UAAE,EAAE,GAAG,IAAI,EAAE,EAAE,KAAK;AAAA,MACtB,CAAC,GAAG;AAAA,QACF,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACE,YAAM,IAAI,sBAAAC,qBAAG,EAAE,MAAM,GAAG,UAAU,OAAO,CAAC;AAAA,EAC9C;AACF,GAAG,YAAY;AAhBf,IAgBkB,IAAoB,EAAE,CAAC,MAAM;AArB/C;AAsBE,MAAI,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC;AAClC,UAAQ,OAAO,IAAI,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM;AAAA,IAC7C,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE,MAAM;AAAA,IAC/C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,EAAE;AAAA,IACzB,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE;AAAA,IACzD,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IAC1B,KAAK;AACH,eAAO,OAAE,aAAF,mBAAY,MAAM,OAAM,EAAE,GAAG,GAAG,MAAM,QAAQ,QAAO,OAAE,aAAF,mBAAY,IAAI,IAAI,IAAI,EAAE,GAAG,GAAG,MAAM,GAAG,QAAO,OAAE,aAAF,mBAAY,IAAI,GAAG;AAAA,IACjI,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,QAAO,OAAE,aAAF,mBAAY,IAAI,GAAG;AAAA,IACpD;AACE,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE;AAAA,EAC3C;AACF,GAAG,SAAS;AAGZ,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,IAAI,CAAC,GAAG,IAAI,OAAO,KAAK,CAAC;AAC7B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;AACrB,MAAE,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AACA,EAAE,GAAG,WAAW;AAGhB,IAAI,IAAI;AAAR,IAAwB,KAAqB,EAAE,CAAC,MAAM,EAAE,QAAQ,GAAG,EAAE,GAAG,YAAY;AAApF,IAAuF,KAAqB,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,gBAC5H;AADJ,IACO,IAAoB,EAAE,CAAC,MAAM;AAClC,MAAI,IAAI,GAAG,CAAC;AACZ,SAAO,GAAG,CAAC,KAAK,OAAO,MAAM,OAAO,CAAC,CAAC,IAAI,IAAI,OAAO,CAAC;AACxD,GAAG,cAAc;AAGjB,IAAI,KAAK;AAAT,IAAwB,IAAoB,EAAE,CAAC,MAAM;AACnD,MAAI,EAAE,MAAM,GAAG,KAAK,GAAG,UAAU,GAAG,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC;AACzD,UAAQ,OAAO,IAAI,QAAQ,EAAE,MAAM,IAAI,GAAG;AAAA,IACxC,KAAK,QAAQ;AACX,UAAI,IAAI,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC;AACvC,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,OAAO,EAAE;AAAA,IACnC;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,EAAE;AAAA,IACzB,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,WAAW;AAAA,IAClC,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,UAAU;AAAA,IACjC,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,KAAK,EAAE,CAAC,EAAE;AAAA,IACjD,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,EAAE;AAAA,IACzB,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,OAAO,EAAE,CAAC,EAAE;AAAA,IACtC,KAAK;AAAA,IACL,KAAK;AACH,UAAI,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AACxB,aAAO,EAAE,GAAG,GAAG,MAAM,UAAU,OAAO,EAAE;AAAA,IAC1C,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE;AAAA,IAC1D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,SAAS;AACP,WAAI,uBAAG,QAAQ,QAAO;AACpB,YAAI;AACF,cAAI,IAAI,EAAE,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;AAC7C,iBAAO,EAAE,GAAG,GAAG,MAAM,QAAQ,OAAO,EAAE;AAAA,QACxC,QAAQ;AAAA,QACR;AACF,UAAI,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC,IAAI,aAAa;AAC5D,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,OAAO,EAAE;AAAA,IACnC;AAAA,EACF;AACF,GAAG,SAAS;AAIZ,IAAI,KAAqB,EAAE,CAAC,MAAM;AAChC,UAAQ,EAAE,MAAM;AAAA,IACd,KAAK;AACH,aAAO,EAAE,MAAM,WAAW;AAAA,IAC5B,KAAK;AACH,UAAI,IAAI,CAAC;AACT,aAAO,EAAE,UAAU,WAAW,QAAQ,CAAC,MAAM;AAC3C,UAAE,EAAE,GAAG,IAAI,EAAE,EAAE,KAAK;AAAA,MACtB,CAAC,GAAG;AAAA,QACF,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACE,YAAM,IAAI,uBAAAC,qBAAG,EAAE,MAAM,GAAG,UAAU,aAAa,CAAC;AAAA,EACpD;AACF,GAAG,YAAY;AAff,IAekB,IAAoB,EAAE,CAAC,MAAM;AA7H/C;AA8HE,MAAI,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC;AAClC,UAAQ,OAAO,IAAI,QAAQ,EAAE,MAAM,IAAI,EAAE,MAAM;AAAA,IAC7C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,EAAE;AAAA,IACzB,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE,SAAS,IAAI,CAAC,EAAE;AAAA,IACzD,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE;AAAA,IAC1B,KAAK;AACH,UAAI;AACJ,eAAO,OAAE,aAAF,mBAAY,MAAM,CAAC,MAAM,EAAE,SAAS,cAAa,IAAI;AAAA,QAC1D,GAAG;AAAA,QACH,MAAM;AAAA;AAAA,QAEN,QAAO,OAAE,aAAF,mBAAY,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK;AAAA,MACzC,IAAI,IAAI,EAAE,GAAG,GAAG,MAAM,GAAG,QAAO,OAAE,aAAF,mBAAY,IAAI,GAAG,GAAG;AAAA,IACxD,KAAK;AACH,aAAO,EAAE,GAAG,GAAG,MAAM,GAAG,QAAO,OAAE,aAAF,mBAAY,IAAI,GAAG;AAAA,IACpD;AACE,aAAO,EAAE,GAAG,GAAG,MAAM,SAAS,OAAO,EAAE;AAAA,EAC3C;AACF,GAAG,SAAS;AAGZ,IAAI,IAAoB,EAAE,CAAC,MAAM;AAC/B,MAAI,EAAE,MAAM,GAAG,QAAQ,GAAG,UAAU,EAAE,IAAI;AAC1C,MAAI;AACF,QAAI,KAAK;AACP,aAAO,EAAE,CAAC;AACZ,QAAI,KAAK;AACP,aAAO,EAAE,CAAC;AACZ,QAAI,KAAK;AACP,aAAO,EAAE,CAAC;AAAA,EACd,SAAS,GAAG;AACV,YAAQ,MAAM,CAAC;AAAA,EACjB;AACA,SAAO;AACT,GAAG,SAAS;AAGZ,IAAI,MAAsB,CAAC,OAAO,EAAE,aAAa,cAAc,EAAE,OAAO,QAAQ,EAAE,aAAa,cAAc,EAAE,UAAU,WAAW,IAAI,MACxI,CAAC,CAAC;AAGF,IAAI,KAAK,CAAC,QAAQ,WAAW;AAC7B,SAAS,EAAE,GAAG;AACZ,SAAO,GAAG,KAAK,CAAC,MAAM,MAAM,CAAC;AAC/B;AACA,EAAE,GAAG,2BAA2B;AAGhC,IAAI,IAAoB,EAAE,CAAC,MAAM;AAC/B,MAAI,CAAC;AACH,WAAO;AACT,MAAI,OAAO,KAAK;AACd,WAAO;AACT,QAAM,IAAI,MAAM,sCAAsC,KAAK,UAAU,CAAC,CAAC,EAAE;AAC3E,GAAG,KAAK;AAGR,SAAS,EAAE,GAAG;AACZ,SAAO,CAAC,CAAC,EAAE;AACb;AACA,EAAE,GAAG,WAAW;AAChB,SAAS,EAAE,GAAG;AACZ,SAAO,KAAK,QAAQ,OAAO,KAAK,CAAC,EAAE,SAAS;AAC9C;AACA,EAAE,GAAG,sBAAsB;AAC3B,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,EAAE,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI;AACpC;AACA,EAAE,GAAG,kBAAkB;AACvB,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,CAAC,IAAI,EAAE,EAAE,aAAa,WAAW,IAAI;AAChD;AACA,EAAE,GAAG,sBAAsB;AAG3B,IAAI;AAAA,CACH,SAAS,GAAG;AACX,IAAE,QAAQ,OAAO,EAAE,UAAU,QAAQ,EAAE,QAAQ,KAAK,EAAE,MAAM;AAC9D,GAAG,IAAI,MAAM,IAAI,CAAC,EAAE;AAGpB,SAAS,EAAE,GAAG;AACZ,SAAO,QAAQ,KAAK,CAAC;AACvB;AACA,EAAE,GAAG,SAAS;AACd,SAAS,EAAE,GAAG;AACZ,MAAI,IAAI,EAAE,MAAM,MAAM;AACtB,SAAO,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC;AAC/E;AACA,EAAE,GAAG,SAAS;AACd,SAAS,EAAE,GAAG;AACZ,MAAI,IAAI,EAAE,MAAM,MAAM;AACtB,SAAO,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC;AAC7E;AACA,EAAE,GAAG,YAAY;AACjB,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,MAAM,IAAI;AACrB;AACA,EAAE,GAAG,YAAY;AACjB,SAAS,EAAE,IAAI,CAAC,GAAG;AACjB,SAAO,OAAO,OAAO,EAAE,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,UAAU,OAAI,aAAa,IAAI,UAAU,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,CAAC;AAClH;AACA,EAAE,GAAG,UAAU;AACf,SAAS,EAAE,IAAI,CAAC,GAAG;AACjB,SAAO,OAAO,OAAO;AAAA,IAAE,OAAO;AAAA,IAAI,WAAW;AAAA,IAAI,eAAe;AAAA,IAAI,KAAK;AAAA,IAAI,SAAS;AAAA,IAAI,MAAM;AAAA,IAAI,UAAU;AAAA,IAAI,MAAM;AAAA,IAAI,UAAU;AAAA,IAAI,aAAa;AAAA,IACvJ,KAAK;AAAA,IAAI,SAAS;AAAA,EAAG,GAAG,CAAC;AAC3B;AACA,EAAE,GAAG,YAAY;AAGjB,IAAI,KAAK;AACT,SAAS,EAAE,EAAE,OAAO,IAAI,MAAM,IAAI,CAAC,GAAG;AACpC,MAAI,IAAI,GAAG,CAAC,GAAG,IAAoB,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,aAAa;AAC3E,SAAuB,EAAE,SAAS,GAAG;AACnC,QAAI,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI;AAClB,aAAS,KAAK;AACZ,SAAG,KAAK,EAAE,OAAO,WAAW,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,EAAE,EAAE,OAAO,aAAa,CAAC;AAC5G,WAAO;AAAA,EACT,GAAG,YAAY;AACjB;AACA,EAAE,GAAG,WAAW;AAChB,SAAS,GAAG,GAAG;AACb,SAAO,OAAO,KAAK,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,SAAS,MAAM,IAAI;AACrE;AACA,EAAE,IAAI,WAAW;AAGjB,SAAS,EAAE,EAAE,WAAW,IAAI,GAAG,SAAS,IAAI,EAAE,IAAI,CAAC,GAAG;AACpD,MAAI,IAAI,MAAM,IAAI;AAClB,SAAuB,EAAE,SAAS,GAAG;AACnC,QAAI,IAAI,GAAG,IAAI,EAAE;AACjB,QAAI,CAAC,EAAE,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,QAAQ,EAAE,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,WAAW,EAAE,OAAO,MAAM,IAAI,CAAC,GAAG,EAAE,YAC9H,EAAE,MAAM,GAAG,EAAE,MAAM,MAAM,GAAG,IAAI,EAAE,MAAM,EAAE,MAAM,MAAM,GAAG,CAAC,EAAE,eAAe,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM;AAC3F,aAAO,KAAK;AACd,QAAI,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;AACpC,QAAI,EAAE,cAAc,MAAM,EAAE,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,OAAO,IAAI,EAAE,MAAM,EAAE,MAAM,MAAM,GAAG;AAAA,MAAC,EAAE;AAAA,MACnI;AAAA,IAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACb,UAAI,IAAI,EAAE,UAAU;AACpB,QAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,MAAM,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,MAAM;AAAA,IACxE;AACA,QAAI,EAAE,cAAc,GAAG,EAAE,KAAK,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,EAAE,CAAC,GAAG,KAAK,GAAG;AAC1E,UAAI,IAAI,EAAE,MAAM;AAChB,aAAO,IAAI,MAAM;AAAA,IACnB;AACA,WAAO;AAAA,EACT,GAAG,aAAa;AAClB;AACA,EAAE,GAAG,WAAW;AAGhB,SAAS,EAAE,EAAE,YAAY,EAAE,GAAG;AAC5B,SAAuB,EAAE,SAAS,GAAG;AACnC,QAAI;AACJ,QAAI,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC;AACvB,aAAS,KAAK;AACZ,UAAI,IAAI,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,SAAS,EAAE,SAAS,SAAS,CAAC,OAAO,QAAQ,MAAM,WAAW,EAAE;AACrF;AACJ,WAAO;AAAA,EACT,GAAG,WAAW;AAChB;AACA,EAAE,GAAG,WAAW;AAGhB,SAAS,IAAI;AACX,SAAO,CAAC,MAAM;AACZ,QAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,GAAG,IAAI,EAAE,YAAY,MAAM,kBAAkB;AAC3E,WAAO,MAAM,QAAQ,EAAE,SAAS,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,SAAS;AAAA,MACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,MAClB,UAAU;AAAA,IACZ,CAAC,GAAG,MAAM,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,UAAU,EAAE,CAAC,GAAG,EAAE,cAAc,EAAE,YAAY,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,EAAE,CAAC,GAAG;AAAA,EAC5G;AACF;AACA,EAAE,GAAG,cAAc;AAGnB,SAAS,EAAE,IAAI,WAAW;AACxB,MAAI,IAAI,GAAG,CAAC;AACZ,SAAO,CAAC,MAAM;AACZ,QAAI,IAAI,GAAG,IAAI,CAAC;AAChB,aAAS,CAAC,GAAG,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,OAAO,QAAQ,GAAG;AACjD,UAAI,IAAI;AACR,UAAI,MAAM,KAAK,EAAE,YAAY,CAAC,MAAM;AAClC,eAAO;AACT,eAAS,KAAK,EAAE;AACd,YAAI,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,GAAG,MAAM;AACpD;AACJ,UAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACxB;AAAA,IACJ;AACA,QAAI,MAAM;AACR,aAAO,EAAE,SAAS,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,QAClB,UAAU;AAAA,MACZ,CAAC,GAAG;AACN,QAAI,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc;AACtC,aAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ;AAChC,QAAE,OAAO,GAAG,IAAI,MAAM,EAAE,OAAO,EAAE,cAAc,MAAM,CAAC,IAAI,GAAG,EAAE,gBAAgB,EAAE,cAAc,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,WAAW,IACvI,EAAE,EAAE,YAAY,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI;AACjD,WAAO,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,IAAI,EAAE,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,GAAG;AAAA,EAC9F;AACF;AACA,EAAE,GAAG,eAAe;AACpB,IAAI,KAAqB,EAAE,CAAC,MAAM,EAAE,KAAK,GAAG,MAAM;AAClD,SAAS,GAAG,GAAG;AACb,SAAO,MAAM,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,aAAa,CAAC,MAAM,EAAE,KAAK;AAAA,CACvF,IAAI;AACL;AACA,EAAE,IAAI,WAAW;AAGjB,IAAI,KAAqB,EAAE,CAAC,MAAM,KAAK,EAAE,WAAW,GAAG,KAAK,EAAE,SAAS,GAAG,GAAG,UAAU;AACvF,SAAS,IAAI;AACX,MAAI,IAAoB,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,SAAS;AACnF,SAAO,CAAC,MAAM;AACZ,QAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE,YAAY,SAAS,GAAG,IAAI,EAAE,MAAM,GAAG;AAClG,QAAI,EAAE,SAAS,KAAK,EAAE,CAAC,MAAM,MAAM,EAAE,SAAS,MAAM;AAClD,aAAO,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,WAAW,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM,CAAC,GAAG;AACvG,QAAI,IAAI,GAAG,IAAI,IAAI,IAAI,OAAI;AAC3B,aAAS,KAAK,GAAG;AACf,UAAI,MAAM,KAAK,EAAE,CAAC;AAChB;AACF,YAAM,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK;AAAA,IAC3C;AACA,QAAI,MAAM;AACR,aAAO,EAAE,SAAS,KAAK;AAAA,QACrB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,QAClB,UAAU;AAAA,MACZ,CAAC,GAAG;AACN,QAAI,IAAI;AACR,QAAI,EAAE,CAAC,MAAM,OAAO,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AAC3C,UAAI,MAAI,IAAI,EAAE,MAAM,GAAG,EAAE;AACzB,UAAI,IAAI,EAAE,MAAM,GAAG;AACnB,UAAI,IAAI,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,MAAM,WAAW,IAAI,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,MAAM;AAC/E,eAAO,EAAE,SAAS,KAAK;AAAA,UACrB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,UAClB,UAAU;AAAA,QACZ,CAAC,GAAG;AACN,UAAI,MAAM;AACR,eAAO,EAAE,SAAS,KAAK;AAAA,UACrB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,UAClB,UAAU;AAAA,QACZ,CAAC,GAAG;AACN,UAAI,CAAC,GAAG,CAAC,KAAK,SAAS,KAAK,CAAC;AAC3B,eAAO,EAAE,SAAS,KAAK;AAAA,UACrB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM,EAAE,OAAO,CAAC,EAAE;AAAA,UAClB,UAAU;AAAA,QACZ,CAAC,GAAG;AAAA,IACR;AACA,WAAO,EAAE,WAAW,GAAG,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,MAAM,WAAW,EAAE,UAAU,IAAI,CAAC,EAAE,UAAU,EAAE,WAAW,IAAI,EAAE,EAAE,MAAM,EAAE,KAAK,MAAM,CAAC,GACtI;AAAA,EACF;AACF;AACA,EAAE,GAAG,eAAe;AAGpB,SAAS,EAAE,IAAI,WAAW,IAAI,GAAG;AAC/B,MAAI,IAAI,EAAE,CAAC;AACX,SAAO,CAAC,OAAO,EAAE,cAAc,EAAE,EAAE,QAAQ,CAAC,GAAG;AACjD;AACA,EAAE,GAAG,sBAAsB;AAC3B,SAAS,EAAE,GAAG;AACZ,SAAO,MAAM,YAAY,KAAK,MAAM,aAAa,KAAK;AACxD;AACA,EAAE,GAAG,WAAW;AAChB,SAAS,GAAG,GAAG,IAAI,GAAG;AACpB,SAAO,EAAE,IAAI,CAAC,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,MAAM,EAAE,EAAE,KAAK,GAAG;AAC7F;AACA,EAAE,IAAI,eAAe;AACrB,IAAI,KAAqB,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,EAAE,SAAS,KAAK,IAAI,GAAG,QAAQ;AAAnF,IAAsF,KAAqB,EAAE,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,cACnI,KAAK,EAAE,QAAQ,EAAE,cAAc,MAAM,CAAC,KAAK,EAAE,aAAa,gBAAgB;AAC1E,SAAS,GAAG,GAAG,IAAI,GAAG;AACpB,MAAI,EAAE,WAAW;AACf,WAAO;AACT,IAAE,CAAC,EAAE,OAAO,gBAAgB,MAAM,EAAE,CAAC,EAAE,OAAO,cAAc,EAAE,UAAU,IAAI,EAAE,MAAM,CAAC;AACrF,MAAI,IAAI,EAAE,EAAE,SAAS,CAAC;AACtB,SAAO,MAAM,UAAU,EAAE,OAAO,gBAAgB,MAAM,EAAE,OAAO,IAAI,SAAS,EAAE,GAAG,MAAM,IAAI,EAAE,MAAM,GAAG,EAAE,IAAI,IAAI,EAAE,MAAM,EAAE,OAAO,IAAI,CAAC,CAAC,GAAG,EAC1I,IAAI,EAAE,EAAE,KAAK;AAAA,CACd;AACD;AACA,EAAE,IAAI,gBAAgB;AAGtB,SAAS,EAAE,EAAE,WAAW,IAAI,GAAG,OAAO,IAAI,OAAO,SAAS,IAAI,WAAW,SAAS,IAAI,GAAG,YAAY,IAAI;AAAA,EACvG,EAAE;AAAA,EACF,EAAE,CAAC;AAAA,EACH,EAAE;AAAA,EACF,EAAE,CAAC;AACL,EAAE,IAAI,CAAC,GAAG;AACR,MAAI,IAAI,KAAK,IAAI,IAAI;AACnB,UAAM,IAAI,MAAM,mBAAmB;AACrC,MAAI,IAAI,EAAE,EAAE,WAAW,GAAG,SAAS,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,YAAY,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC;AAC/F,SAAO,SAAS,GAAG;AACjB,QAAI,IAAI,CAAC;AACT,aAAS,MAAM,EAAE,CAAC,GAAG;AACnB,UAAI,IAAI,EAAE,EAAE;AACZ,UAAI,MAAM;AACR;AACF,UAAI,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;AAClC,QAAE,KAAK;AAAA,QACL,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;AAAA,QACtB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU,EAAE,OAAO,CAAC,IAAI,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC;AAAA,MAC3D,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,EAAE,GAAG,WAAW;AAGhB,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,MAAM,EACzI;AACF;AACA,EAAE,IAAI,MAAM;AACZ,SAAS,IAAI;AACX,SAAO,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK;AAAA,CAC3D;AACD;AACA,EAAE,GAAG,gBAAgB;AAGrB,IAAI,KAAK;AAAA,EACP,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,eAAe;AAAA,EACf,KAAK;AAAA,EACL,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,MAAM;AAAA,EACN,UAAU;AAAA,EACV,aAAa;AAAA,EACb,KAAK;AAAA,EACL,SAAS;AACX;AACA,IAAI,KAAK,OAAO,KAAK,EAAE;AAGvB,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG;AACpB,SAAO,EAAE,CAAC,EAAE,CAAC;AACf;AACA,EAAE,GAAG,OAAO;AACZ,IAAI,KAAK,EAAE;AAQX,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,QAAQ,EAAE,SAAS,GAAG;AACpC;AACA,EAAE,IAAI,eAAe;AACrB,SAAS,GAAG,GAAG;AACb,MAAI,IAAI;AAAA,KACL,KAAK,IAAI,MAAM;AAAA,CACnB,EAAE,IAAI,CAAC,MAAM,MAAM,CAAC,EAAE,EAAE,KAAK;AAAA,CAC7B,IAAI;AAAA,KACA,IAAI,EAAE,GAAG;AAAA,IACV,SAAS;AAAA,EACX,CAAC;AACD,MAAI,CAAC,KAAK,EAAE,WAAW;AACrB,UAAM,IAAI,MAAM,0BAA0B;AAC5C,SAAO,EAAE,CAAC;AACZ;AACA,EAAE,IAAI,OAAO;AACb,IAAI,KAAK;AAAA,EACP,MAAM,CAAC,SAAS,OAAO,YAAY,WAAW,UAAU,YAAY;AACtE;AAFA,IAEG,IAAoB,EAAE,CAAC,GAAG,IAAI,OAAO;AACtC,MAAI,CAAC,GAAG,CAAC;AACP,WAAO;AAAA,MACL,eAAe;AAAA,MACf,QAAQ;AAAA,IACV;AACF,MAAI,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,EAAE,IAAI;AAC/B,SAAO,EAAE,SAAS;AAAA,IAChB,eAAe;AAAA,IACf,QAAQ;AAAA,EACV,IAAI;AAAA,IACF,eAAe;AAAA,IACf,QAAQ;AAAA;AAAA,IAER,aAAa,EAAE,YAAY,KAAK;AAAA,IAChC,eAAe;AAAA,EACjB;AACF,GAAG,YAAY;AACf,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,IAAI;AAAA,IACN,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,QAAQ;AAAA,EACV;AACA,WAAS,KAAK,EAAE;AACd,QAAI,EAAE,MAAM,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG;AACrC,UAAI,EAAE,QAAQ,UAAU;AACtB,UAAE,SAAS;AACX;AAAA,MACF;AACE,gBAAQ,EAAE,KAAK;AAAA,UAEb,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,YAAY;AACf,gBAAI,IAAI,GAAG,CAAC;AACZ,iBAAK,SAAS,EAAE,UAAU,SAAS,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,KAAK,CAAC;AAClE;AAAA,UACF;AAAA,UACA,KAAK,cAAc;AACjB,gBAAI,IAAI,GAAG,CAAC;AACZ,iBAAK,SAAS,EAAE,aAAa;AAC7B;AAAA,UACF;AAAA,UACA,KAAK,WAAW;AACd,gBAAI,IAAI,GAAG,CAAC;AACZ,iBAAK,SAAS,EAAE,UAAU;AAC1B;AAAA,UACF;AAAA,UACA;AACE;AAAA,QACJ;AACN,SAAO;AACT;AACA,EAAE,IAAI,kBAAkB;AACxB,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,QAAQ,UAAU,EAAE;AAC/B;AACA,EAAE,IAAI,oBAAoB;AAC1B,SAAS,GAAG,GAAG;AACb,MAAI,CAAC,EAAE,QAAQ,EAAE,SAAS;AACxB,WAAO;AACT,MAAI,IAAI,GAAG,EAAE,IAAI;AACjB,SAAO;AAAA,IACL,MAAM,EAAE;AAAA,IACR,MAAM;AAAA,IACN,aAAa,GAAG,EAAE,WAAW;AAAA,IAC7B,eAA+B,EAAE,MAAM,GAAG,EAAE,IAAI,GAAG,eAAe;AAAA,IAClE,aAA6B,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,MAAM,aAAa;AAAA,EACtE;AACF;AACA,EAAE,IAAI,cAAc;AACpB,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,WAAW,IAAI;AAC7C;AACA,EAAE,IAAI,mBAAmB;AACzB,SAAS,EAAE,GAAG,GAAG;AACf,MAAI,IAAI,MAAM,KAAK,IAAI,GAAG,CAAC,IAAI,CAAC;AAChC,SAAO,GAAG,CAAC;AACb;AACA,EAAE,GAAG,wBAAwB;AAC7B,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,EAAE,QAAQ,QAAQ,EAAE,EAAE,KAAK;AACnC,SAAO,MAAM,KAAK,OAAO;AAC3B;AACA,EAAE,IAAI,sBAAsB;AAC5B,SAAS,GAAG,GAAG;AACb,MAAI,IAAI,GAAG,EAAE,IAAI;AACjB,SAAO,IAAI;AAAA,IACT,MAAM;AAAA,IACN,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;AAAA,IACpC,aAA6B,EAAE,MAAM,GAAG,CAAC,GAAG,aAAa;AAAA,EAC3D,IAAI;AACN;AACA,EAAE,IAAI,gBAAgB;AACtB,IAAI,QAAI,+BAAAC,gBAAG;AAAX,IAAc,KAAK,EAAE;AACrB,EAAE,eAAe,MAAM;AACvB,EAAE,kBAAkB,CAAC,GAAG,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;AAC1C,EAAE,oBAAoB,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO;AAC3C,EAAE,oBAAoB,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO;AAC3C,EAAE,uBAAuB,CAAC,GAAG,MAAM,EAAE,EAAE,OAAO;AAC9C,EAAE,iBAAiB,CAAC,GAAG,MAAM,EAAE,SAAS,IAAI,CAAC,EAAE,KAAK,GAAG;AACvD,SAAS,GAAG,GAAG;AACb,MAAI;AACF,eAAO,+BAAAC,OAAG,GAAG,YAAY;AAAA,EAC3B,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,EAAE,IAAI,aAAa;AACnB,SAAS,GAAG,GAAG;AACb,aAAO,+BAAAC,WAAG,GAAG,CAAC;AAChB;AACA,EAAE,IAAI,iBAAiB;AAIvB,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,SAAS;AACpB;AACA,EAAE,GAAG,yBAAyB;AAC9B,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,SAAS;AACpB;AACA,EAAE,IAAI,iCAAiC;AACvC,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,MAAM,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,GAAG,QAAQ,EAAE;AAC5D;AACA,EAAE,GAAG,oBAAoB;AACzB,IAAI,KAAqB,EAAE,CAAC,MAAM,EAAE,QAAQ,WAAW,KAAK,GAAG,mBAAmB;AAGlF,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,KAAK,MAAM;AACb,QAAI,EAAE,OAAO,EAAE,IAAI;AACnB,QAAI,CAAC,EAAE,CAAC;AACN,aAAO,GAAG,CAAC,IAAI,EAAE,uBAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAAA,EACtC;AACA,SAAO;AACT;AACA,EAAE,IAAI,oBAAoB;AAG1B,SAAS,GAAG,EAAE,MAAM,GAAG,OAAO,GAAG,UAAU,GAAG,KAAK,EAAE,GAAG;AACtD,SAAO,MAAM,KAAK,OAAO,EAAE,IAAI,EAAE,EAAE,KAAK,KAAK,IAAI,KAAK;AACxD;AACA,EAAE,IAAI,sBAAsB;AAC5B,SAAS,GAAG,EAAE,MAAM,GAAG,KAAK,GAAG,UAAU,EAAE,GAAG;AAC5C,SAAO,KAAK,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,EAAE,QAAQ,UAAU,EAAE,CAAC,IAAI,EAAE,CAAC;AAC5F;AACA,EAAE,IAAI,eAAe;AACrB,SAAS,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG;AAC/B,SAAO,KAAK,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC;AAC/B;AACA,EAAE,IAAI,uBAAuB;AAC7B,SAAS,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG;AAC/B,SAAO,KAAK,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChD;AACA,EAAE,IAAI,yBAAyB;AAC/B,SAAS,GAAG,GAAG;AACb,MAAI,EAAE,MAAM,EAAE,IAAI;AAClB,SAAO,MAAM,WAAW,GAAG,CAAC,IAAI,GAAG,CAAC;AACtC;AACA,EAAE,IAAI,mBAAmB;AACzB,SAAS,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG;AAC/B,SAAO,KAAK,OAAO,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAChD;AACA,EAAE,IAAI,iBAAiB;AACvB,SAAS,GAAG,GAAG;AACb,MAAI,KAAK;AACP,WAAO;AACT,UAAQ,EAAE,MAAM;AAAA,IACd,KAAK;AACH,aAAO,GAAG,CAAC;AAAA,IACb,KAAK;AACH,aAAO,GAAG,CAAC;AAAA,IACb;AACE,aAAO,GAAG,CAAC;AAAA,EACf;AACF;AACA,EAAE,IAAI,YAAY;AAGlB,IAAI,KAAqB,EAAE,CAAC,GAAG,MAAM;AACnC,MAAI,EAAE,UAAU,GAAG,aAAa,GAAG,UAAU,GAAG,cAAc,EAAE,IAAI;AACpE,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,GAAG,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc,GAAG,KAAK,MAAM,KAAK,IAAI;AAAA,EACvC;AACF,GAAG,mBAAmB;AAGtB,SAAS,GAAG,EAAE,cAAc,EAAE,GAAG;AAC/B,MAAI,KAAK,MAAM;AACb,QAAI,EAAE,OAAO,EAAE,IAAI;AACnB,QAAI,CAAC,EAAE,CAAC;AACN,aAAO,EAAE,CAAC;AAAA,EACd;AACA,SAAO;AACT;AACA,EAAE,IAAI,oBAAoB;AAG1B,SAAS,GAAG,EAAE,QAAQ,GAAG,UAAU,EAAE,GAAG;AACtC,MAAI,KAAK;AACP,WAAO;AACT,MAAI,IAAI,EAAE;AACV,SAAO,MAAM,IAAI,EAAE,QAAQ,gBAAgB,EAAE,IAAI;AAAA,IAC/C,CAAC,SAAS,UAAU,WAAW,EAAE,SAAS,EAAE,IAAI,IAAI,EAAE,MAAM;AAAA,EAC9D;AACF;AACA,EAAE,IAAI,YAAY;AAGlB,IAAI,KAAqB,EAAE,CAAC,GAAG,MAAM;AACnC,MAAI,EAAE,aAAa,GAAG,UAAU,EAAE,IAAI;AACtC,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,GAAG,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc,GAAG,CAAC;AAAA,EACpB;AACF,GAAG,iBAAiB;AAGpB,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,OAAO,EAAE,EAAE,IAAI,IAAI;AACjC;AACA,EAAE,IAAI,YAAY;AAClB,SAAS,GAAG,GAAG;AACb,MAAI,EAAE,UAAU,GAAG,MAAM,EAAE,IAAI;AAC/B,SAAO,OAAO,IAAI,OAAO,OAAO,IAAI;AACtC;AACA,EAAE,IAAI,yBAAyB;AAC/B,SAAS,GAAG,GAAG;AACb,SAAO,IAAI,EAAE,SAAS,WAAW,OAAK,EAAE,SAAS,SAAS,MAAM,QAAQ,EAAE,KAAK,KAAK,EAAE,MAAM;AAAA,IAC1F,CAAC,EAAE,OAAO,EAAE,MAAM,OAAO,KAAK,YAAY,EAAE,CAAC,MAAM,OAAO,EAAE,EAAE,SAAS,CAAC,MAAM;AAAA,EAChF,IAAI,QAAK;AACX;AACA,EAAE,IAAI,gBAAgB;AACtB,SAAS,GAAG,GAAG,GAAG;AAChB,MAAI,KAAK,MAAM;AACb,QAAI,EAAE,OAAO,EAAE,IAAI;AACnB,QAAI,CAAC,EAAE,CAAC;AACN,aAAO,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,KAAK,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;AAAA,EACtD;AACA,SAAO;AACT;AACA,EAAE,IAAI,oBAAoB;AAC1B,SAAS,GAAG,GAAG,GAAG,GAAG;AACnB,MAAI,EAAE,aAAa,GAAG,UAAU,GAAG,cAAc,EAAE,IAAI;AACvD,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,GAAG,CAAC;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,cAAc,GAAG,GAAG,CAAC;AAAA,EACvB;AACF;AACA,EAAE,IAAI,oBAAoB;AAC1B,SAAS,EAAE,GAAG,GAAG;AA7wBjB;AA8wBE,MAAI,uBAAG,eAAe;AACpB,QAAI,EAAE,aAAa,GAAG,eAAe,EAAE,IAAI;AAC3C,SAAK,SAAS,EAAE,cAAc,EAAE;AAChC,QAAI,IAAI;AAAA,MACN,GAAG;AAAA,MACH,SAAQ,4BAAG,WAAH,mBAAW;AAAA,QACjB,CAAC,OAAO;AAAA,UACN,MAAM,EAAE,cAAc;AAAA,UACtB,aAAa,EAAE;AAAA,QACjB;AAAA;AAAA,IAEJ;AACA,WAAO,OAAO,CAAC,EAAE,OAAO,OAAO,EAAE,SAAS,MAAM,EAAE,YAAY;AAAA,EAChE;AACA,SAAO;AACT;AACA,EAAE,GAAG,kBAAkB;AACvB,IAAI,KAAqB,EAAE,CAAC,GAAG,GAAG,MAAM;AACtC,MAAI,IAAI,GAAG,GAAG,EAAE,MAAM,CAAC;AACvB,SAAO,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AAChC,GAAG,mBAAmB;AAHtB,IAGyB,KAAqB,EAAE,CAAC,GAAG,GAAG,MAAM;AAC3D,MAAI,IAAI,GAAG,GAAG,CAAC;AACf,SAAO,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AAChC,GAAG,WAAW;AANd,IAMiB,KAAqB,EAAE,CAAC,GAAG,GAAG,MAAM;AACnD,MAAI,IAAI,GAAG,GAAG,CAAC;AACf,SAAO,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;AAChC,GAAG,aAAa;AAThB,IASmB,KAAqB,EAAE,CAAC,GAAG,GAAG,MAAM;AACrD,MAAI,IAAI,GAAG,GAAG,EAAE,MAAM,UAAU,GAAG,CAAC;AACpC,SAAO,EAAE,GAAG,CAAC;AACf,GAAG,gBAAgB;AAZnB,IAYsB,IAAoB,EAAE,CAAC,MAAM;AACjD,UAAQ,GAAG;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF,GAAG,mBAAmB;AAGtB,IAAI,KAAqB;AAAA,EAAE,CAAC,MAAM,EAAE,QAAQ,OAAO,eAAe,EAAE,YAAY,OAAO,SAAS,EAAE,UAAU,OAAO,eAAe;AAAA,EAClI;AAAe;AADf,IACkB,KAAqB,EAAE,CAAC,MAAM;AAC9C,MAAI,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;AACzB,SAAO,EAAE,IAAI,CAAC,MAAM;AA5zBtB;AA6zBI,QAAI,IAAI;AACR,aAAO,OAAE,SAAF,mBAAQ,cAAa,IAAI;AAAA,MAC9B,GAAG;AAAA,MACH,MAAM;AAAA,QACJ,GAAG,EAAE;AAAA,QACL,OAAO,EAAE,KAAK;AAAA,MAChB;AAAA,IACF,IAAI,GAAG,EAAE,MAAM,GAAG,GAAG,CAAC;AAAA,EACxB,CAAC;AACH,GAAG,8BAA8B;AAbjC,IAaoC,KAAqB,EAAE,CAAC,MAAM;AAChE,MAAI,IAAI,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC;AAChD,SAAO,EAAE,IAAI,CAAC,MAAM;AAClB,QAAI,IAAI,EAAE,CAAC;AACX,WAAO,KAAK,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI;AAAA,EACtC,CAAC,EAAE,OAAO,OAAO;AACnB,GAAG,+BAA+B;AAnBlC,IAmBqC,KAAqB,EAAE,CAAC,GAAG,MAAM;AACpE,MAAI,IAAI,EAAE,GAAG,CAAC;AACd,SAAO,EAAE,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC;AACpD,GAAG,uBAAuB;AAC1B,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,EAAE,EAAE,WAAW;AACvB,SAAO,EAAE,iBAAiB,EAAE,SAAS,OAAO;AAAA,IAC1C,SAAS,EAAE,GAAG,GAAG,CAAC;AAAA,IAClB,WAAW,EAAE;AAAA,IACb,YAAY;AAAA,IACZ,YAAY;AAAA,EACd;AACF;AACA,EAAE,IAAI,aAAa;AACnB,SAAS,GAAG,GAAG;AACb,SAAO,KAAK,OAAO,EAAE,CAAC,IAAI;AAC5B;AACA,EAAE,IAAI,6BAA6B;AAInC,IAAI,KAAqB,EAAE,CAAC,MAAM;AAChC,MAAI;AAAA,IACF,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY,EAAE,MAAM,IAAI,CAAC,EAAE;AAAA,EAC7B,IAAI,GAAG,EAAE,iBAAiB,EAAE,IAAI,GAAG,IAAI,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC;AACxD,SAAO,QAAI,mBAAAC,mBAAG,GAAG,CAAC,IAAI;AACxB,GAAG,iBAAiB;AAGpB,IAAI,KAAK;AAAT,IAA2B,KAAK,GAAG,EAAE;AAArC,IAA4D,KAAK,GAAG,EAAE;AAAtE,IAA2F,MAAsB,CAAC,OAAO,EAAE,OAAO,QAAQ,EAC1I,OAAO,QAAQ,EAAE,UAAU,WAAW,IAAI,MAAM,CAAC,CAAC;AAGlD,IAAI,KAAK;AAAT,IAAgE,KAAqB,EAAE,CAAC,MAAG;AA/2B3F;AA+2B8F,iBAAE,gBAAF,mBAAe,KAAK,CAAC,MAAM,GAAG,KAAK,EAAE,IAAI;AAAA,GAAI,mBACzH;", "names": ["exports", "Precedence", "pathGrammar", "type", "objectFieldGrammar", "quote", "rules", "transform", "o", "import_preview_errors", "Te", "we", "We", "Ue", "Me", "yt"]}