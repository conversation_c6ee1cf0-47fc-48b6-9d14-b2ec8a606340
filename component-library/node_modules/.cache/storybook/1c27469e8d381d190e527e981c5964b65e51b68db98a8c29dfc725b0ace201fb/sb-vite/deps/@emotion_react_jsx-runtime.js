import {
  Emotion$1,
  createEmotionProps,
  hasOwn,
  require_hoist_non_react_statics_cjs
} from "./chunk-QALRF32W.js";
import {
  require_jsx_runtime
} from "./chunk-K7763LNH.js";
import {
  require_react
} from "./chunk-BZ4VO6P4.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// ../node_modules/.pnpm/@emotion+react@11.14.0_@types+react@19.1.6_react@19.1.0/node_modules/@emotion/react/jsx-runtime/dist/emotion-react-jsx-runtime.browser.development.esm.js
var ReactJSXRuntime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());
var Fragment2 = ReactJSXRuntime.Fragment;
var jsx2 = function jsx3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsx(type, props, key);
  }
  return ReactJSXRuntime.jsx(Emotion$1, createEmotionProps(type, props), key);
};
var jsxs2 = function jsxs3(type, props, key) {
  if (!hasOwn.call(props, "css")) {
    return ReactJSXRuntime.jsxs(type, props, key);
  }
  return ReactJSXRuntime.jsxs(Emotion$1, createEmotionProps(type, props), key);
};
export {
  Fragment2 as Fragment,
  jsx2 as jsx,
  jsxs2 as jsxs
};
//# sourceMappingURL=@emotion_react_jsx-runtime.js.map
