try{
(()=>{var g=__STORYBOOK_API__,{ActiveTabs:k,Consumer:x,ManagerContext:O,Provider:T,RequestResponseError:h,addons:u,combineParameters:U,controlOrMetaKey:v,controlOrMetaSymbol:A,eventMatchesShortcut:D,eventToShortcut:j,experimental_MockUniversalStore:P,experimental_UniversalStore:M,experimental_requestResponse:R,experimental_useUniversalStore:C,isMacLike:w,isShortcutTaken:B,keyToSymbol:E,merge:I,mockChannel:K,optionOrAltSymbol:N,shortcutMatchesShortcut:G,shortcutToHumanString:L,types:Y,useAddonState:q,useArgTypes:F,useArgs:H,useChannel:V,useGlobalTypes:z,useGlobals:J,useParameter:Q,useSharedState:W,useStoryPrepared:X,useStorybookApi:Z,useStorybookState:$}=__STORYBOOK_API__;var l=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})(),m="tag-filters",_="static-filter";u.register(m,e=>{let d=Object.entries(l.TAGS_OPTIONS??{}).reduce((o,t)=>{let[s,i]=t;return i.excludeFromSidebar&&(o[s]=!0),o},{});e.experimental_setFilter(_,o=>{let t=o.tags??[];return(t.includes("dev")||o.type==="docs")&&t.filter(s=>d[s]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
