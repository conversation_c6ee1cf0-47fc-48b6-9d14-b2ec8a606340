try{
(()=>{var a=__REACT__,{Children:se,Component:ie,Fragment:ue,Profiler:ce,PureComponent:pe,StrictMode:de,Suspense:me,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:_e,cloneElement:be,createContext:ye,createElement:Se,createFactory:fe,createRef:Te,forwardRef:xe,isValidElement:ve,lazy:Oe,memo:Ce,startTransition:ge,unstable_act:ke,useCallback:x,useContext:Ie,useDebugValue:Ee,useDeferredValue:Ae,useEffect:I,useId:Re,useImperativeHandle:he,useInsertionEffect:De,useLayoutEffect:Le,useMemo:Be,useReducer:Pe,useRef:D,useState:L,useSyncExternalStore:Me,useTransition:Ne,version:Ue}=__REACT__;var We=__STORYBOOK_API__,{ActiveTabs:Fe,Consumer:Ge,ManagerContext:Ke,Provider:Ye,RequestResponseError:$e,addons:E,combineParameters:qe,controlOrMetaKey:ze,controlOrMetaSymbol:Ze,eventMatchesShortcut:Je,eventToShortcut:Qe,experimental_MockUniversalStore:Xe,experimental_UniversalStore:et,experimental_requestResponse:tt,experimental_useUniversalStore:ot,isMacLike:rt,isShortcutTaken:nt,keyToSymbol:at,merge:lt,mockChannel:st,optionOrAltSymbol:it,shortcutMatchesShortcut:ut,shortcutToHumanString:ct,types:B,useAddonState:pt,useArgTypes:dt,useArgs:mt,useChannel:_t,useGlobalTypes:P,useGlobals:A,useParameter:bt,useSharedState:yt,useStoryPrepared:St,useStorybookApi:M,useStorybookState:ft}=__STORYBOOK_API__;var Ct=__STORYBOOK_COMPONENTS__,{A:gt,ActionBar:kt,AddonPanel:It,Badge:Et,Bar:At,Blockquote:Rt,Button:ht,ClipboardCode:Dt,Code:Lt,DL:Bt,Div:Pt,DocumentWrapper:Mt,EmptyTabContent:Nt,ErrorFormatter:Ut,FlexBar:Vt,Form:wt,H1:Ht,H2:jt,H3:Wt,H4:Ft,H5:Gt,H6:Kt,HR:Yt,IconButton:N,IconButtonSkeleton:$t,Icons:R,Img:qt,LI:zt,Link:Zt,ListItem:Jt,Loader:Qt,Modal:Xt,OL:eo,P:to,Placeholder:oo,Pre:ro,ProgressSpinner:no,ResetWrapper:ao,ScrollArea:lo,Separator:U,Spaced:so,Span:io,StorybookIcon:uo,StorybookLogo:co,Symbols:po,SyntaxHighlighter:mo,TT:_o,TabBar:bo,TabButton:yo,TabWrapper:So,Table:fo,Tabs:To,TabsState:xo,TooltipLinkList:V,TooltipMessage:vo,TooltipNote:Oo,UL:Co,WithTooltip:w,WithTooltipPure:go,Zoom:ko,codeCommon:Io,components:Eo,createCopyToClipboardFunction:Ao,getStoryHref:Ro,icons:ho,interleaveSeparators:Do,nameSpaceClassNames:Lo,resetComponents:Bo,withReset:Po}=__STORYBOOK_COMPONENTS__;var F={type:"item",value:""},G=(o,t)=>({...t,name:t.name||o,description:t.description||o,toolbar:{...t.toolbar,items:t.toolbar.items.map(e=>{let r=typeof e=="string"?{value:e,title:e}:e;return r.type==="reset"&&t.toolbar.icon&&(r.icon=t.toolbar.icon,r.hideIcon=!0),{...F,...r}})}}),K=["reset"],Y=o=>o.filter(t=>!K.includes(t.type)).map(t=>t.value),b="addon-toolbars",$=async(o,t,e)=>{e&&e.next&&await o.setAddonShortcut(b,{label:e.next.label,defaultShortcut:e.next.keys,actionName:`${t}:next`,action:e.next.action}),e&&e.previous&&await o.setAddonShortcut(b,{label:e.previous.label,defaultShortcut:e.previous.keys,actionName:`${t}:previous`,action:e.previous.action}),e&&e.reset&&await o.setAddonShortcut(b,{label:e.reset.label,defaultShortcut:e.reset.keys,actionName:`${t}:reset`,action:e.reset.action})},q=o=>t=>{let{id:e,toolbar:{items:r,shortcuts:n}}=t,c=M(),[y,i]=A(),l=D([]),u=y[e],v=x(()=>{i({[e]:""})},[i]),O=x(()=>{let s=l.current,d=s.indexOf(u),m=d===s.length-1?0:d+1,p=l.current[m];i({[e]:p})},[l,u,i]),C=x(()=>{let s=l.current,d=s.indexOf(u),m=d>-1?d:0,p=m===0?s.length-1:m-1,_=l.current[p];i({[e]:_})},[l,u,i]);return I(()=>{n&&$(c,e,{next:{...n.next,action:O},previous:{...n.previous,action:C},reset:{...n.reset,action:v}})},[c,e,n,O,C,v]),I(()=>{l.current=Y(r)},[]),a.createElement(o,{cycleValues:l.current,...t})},H=({currentValue:o,items:t})=>o!=null&&t.find(e=>e.value===o&&e.type!=="reset"),z=({currentValue:o,items:t})=>{let e=H({currentValue:o,items:t});if(e)return e.icon},Z=({currentValue:o,items:t})=>{let e=H({currentValue:o,items:t});if(e)return e.title},J=({active:o,disabled:t,title:e,icon:r,description:n,onClick:c})=>a.createElement(N,{active:o,title:n,disabled:t,onClick:t?()=>{}:c},r&&a.createElement(R,{icon:r,__suppressDeprecationWarning:!0}),e?`\xA0${e}`:null),Q=({right:o,title:t,value:e,icon:r,hideIcon:n,onClick:c,disabled:y,currentValue:i})=>{let l=r&&a.createElement(R,{style:{opacity:1},icon:r,__suppressDeprecationWarning:!0}),u={id:e??"_reset",active:i===e,right:o,title:t,disabled:y,onClick:c};return r&&!n&&(u.icon=l),u},X=q(({id:o,name:t,description:e,toolbar:{icon:r,items:n,title:c,preventDynamicIcon:y,dynamicTitle:i}})=>{let[l,u,v]=A(),[O,C]=L(!1),s=l[o],d=!!s,m=o in v,p=r,_=c;y||(p=z({currentValue:s,items:n})||p),i&&(_=Z({currentValue:s,items:n})||_),!_&&!p&&console.warn(`Toolbar '${t}' has no title or icon`);let j=x(k=>{u({[o]:k})},[o,u]);return a.createElement(w,{placement:"top",tooltip:({onHide:k})=>{let W=n.filter(({type:g})=>{let h=!0;return g==="reset"&&!s&&(h=!1),h}).map(g=>Q({...g,currentValue:s,disabled:m,onClick:()=>{j(g.value),k()}}));return a.createElement(V,{links:W})},closeOnOutsideClick:!0,onVisibleChange:C},a.createElement(J,{active:O||d,disabled:m,description:e||"",icon:p,title:_||""}))}),ee=()=>{let o=P(),t=Object.keys(o).filter(e=>!!o[e].toolbar);return t.length?a.createElement(a.Fragment,null,a.createElement(U,null),t.map(e=>{let r=G(e,o[e]);return a.createElement(X,{key:e,id:e,...r})})):null};E.register(b,()=>E.add(b,{title:b,type:B.TOOL,match:({tabId:o})=>!o,render:()=>a.createElement(ee,null)}));})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
