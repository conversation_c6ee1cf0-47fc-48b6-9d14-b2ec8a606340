// import React from 'react'; // React import is often not needed with "jsx": "react-jsx"
import type { Preview } from "@storybook/react";
import { ThemeProvider } from "../src/providers/ThemeProvider";
import { THEME_NAMES } from "../src/theme";

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i,
			},
		},
		backgrounds: {
			default: "light",
			values: [
				{
					name: "light",
					value: "#ffffff",
				},
				{
					name: "dark",
					value: "#1a1a1a",
				},
			],
		},
	},
	decorators: [
		(Story, context) => {
			const theme = context.globals.theme || THEME_NAMES.LIGHT;

			return (
				<ThemeProvider defaultTheme={theme}>
					<Story />
				</ThemeProvider>
			);
		},
	],
	globalTypes: {
		theme: {
			description: "Global theme for components",
			defaultValue: THEME_NAMES.LIGHT,
			toolbar: {
				title: "Theme",
				icon: "circlehollow",
				items: [
					{ value: THEME_NAMES.LIGHT, title: "Light" },
					{ value: THEME_NAMES.DARK, title: "Dark" },
				],
				dynamicTitle: true,
			},
		},
	},
};

export default preview;
