"use client";

import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { callFirebaseFunction } from "@/src/lib/firebaseFunctions";
import type { CreateCreativeRequest, CreativeType } from "@/types/requests/creativeRequestInterfaces";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "reactfire";
import Link from "next/link";

const creativeTypes: { value: CreativeType; label: string }[] = [
  { value: "photographer", label: "Photographer" },
  { value: "videographer", label: "Videographer" },
  { value: "model", label: "Model" },
  { value: "makeupArtist", label: "Makeup Artist" },
  { value: "hairStylist", label: "Hair Stylist" },
  { value: "other", label: "Other" }
];

interface PendingCreativeProfile {
  firstName: string;
  lastName: string;
  email: string;
  uid: string;
}

export default function CreateCreativeProfilePage() {
  const { status, data: user } = useUser();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pendingProfile, setPendingProfile] = useState<PendingCreativeProfile | null>(null);
  const [formData, setFormData] = useState<Partial<CreateCreativeRequest>>({
    displayName: "",
    story: "",
    creativeType: [],
    instagram: "",
    phoneNumber: "",
  });

  // Load pending profile data from localStorage
  useEffect(() => {
    const pendingData = localStorage.getItem('pendingCreativeProfile');
    if (pendingData) {
      const parsed = JSON.parse(pendingData) as PendingCreativeProfile;
      setPendingProfile(parsed);

      // Pre-fill display name with first and last name
      setFormData(prev => ({
        ...prev,
        displayName: `${parsed.firstName} ${parsed.lastName}`,
      }));
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleCreativeTypeChange = (value: CreativeType) => {
    const currentTypes = formData.creativeType || [];
    const updatedTypes = currentTypes.includes(value)
      ? currentTypes.filter((type: CreativeType) => type !== value)
      : [...currentTypes, value];

    setFormData(prev => ({ ...prev, creativeType: updatedTypes }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsLoading(true);

    if (!user) {
      setError("You must be logged in to create a profile.");
      setIsLoading(false);
      return;
    }

    if (!pendingProfile) {
      setError("Missing signup information. Please start the signup process again.");
      setIsLoading(false);
      return;
    }

    // Validate required fields
    if (!formData.displayName?.trim()) {
      setError("Display name is required.");
      setIsLoading(false);
      return;
    }

    if (!formData.creativeType || formData.creativeType.length === 0) {
      setError("Please select at least one creative type.");
      setIsLoading(false);
      return;
    }

    // Prepare payload according to the Firebase endpoints documentation
    const payload: CreateCreativeRequest = {
      displayName: formData.displayName,
      story: formData.story || "",
      instagram: formData.instagram || "",
      phoneNumber: formData.phoneNumber || "",
      creativeType: formData.creativeType,
    };

    try {
      console.log("Creating creative profile with payload:", payload);

      // Step 1: Call create_creative endpoint
      await callFirebaseFunction("create_creative", payload);
      console.log("Creative profile created successfully!");

      // Clear the pending profile data
      localStorage.removeItem('pendingCreativeProfile');

      // Step 2: Redirect to profile page (admin approval will be handled in the background)
      router.push(`/creative/${user.uid}/under-review`);

    } catch (err: any) {
      console.error("Error creating creative profile:", err);
      setError(err.message || "An unexpected error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    router.push("/");
    return <div>Redirecting to login...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl font-bold text-gray-900">
              Complete Your Creative Profile
            </CardTitle>
            <p className="text-gray-600 mt-2">
              Tell us about yourself and your creative specialties
            </p>
            {pendingProfile && (
              <p className="text-sm text-blue-600 mt-2">
                Welcome, {pendingProfile.firstName}! Let's set up your profile.
              </p>
            )}
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-8">
              {/* Basic Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 border-b pb-2">
                  Basic Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="displayName">Display Name *</Label>
                    <Input
                      id="displayName"
                      name="displayName"
                      value={formData.displayName}
                      onChange={handleChange}
                      required
                      placeholder="Your professional name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      name="phoneNumber"
                      value={formData.phoneNumber}
                      onChange={handleChange}
                      placeholder="+1234567890"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="story">Your Story</Label>
                  <Textarea
                    id="story"
                    name="story"
                    value={formData.story}
                    onChange={handleChange}
                    placeholder="Tell us about your creative journey, experience, and what makes you unique..."
                    rows={4}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="instagram">Instagram Handle</Label>
                  <Input
                    id="instagram"
                    name="instagram"
                    value={formData.instagram}
                    onChange={handleChange}
                    placeholder="your_instagram_handle"
                  />
                </div>
              </div>

              {/* Creative Types */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-gray-900 border-b pb-2">
                  Creative Specialties *
                </h3>
                <p className="text-sm text-gray-600">
                  Select all that apply to your creative work
                </p>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {creativeTypes.map(type => (
                    <div key={type.value} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={type.value}
                        checked={formData.creativeType?.includes(type.value) || false}
                        onChange={() => handleCreativeTypeChange(type.value)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label htmlFor={type.value} className="text-sm font-medium">
                        {type.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Next Steps Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  What happens next?
                </h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your profile will be submitted for admin review</li>
                  <li>• You'll receive an email notification once approved</li>
                  <li>• After approval, you can set your pricing and start receiving bookings</li>
                  <li>• You can always update your profile information later</li>
                </ul>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col gap-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
                size="lg"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Profile...
                  </>
                ) : (
                  "Create Creative Profile"
                )}
              </Button>

              <p className="text-xs text-gray-500 text-center">
                By creating your profile, you agree to our{" "}
                <Link href="/terms" className="text-blue-600 hover:text-blue-700">
                  Terms & Conditions
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}