"use client";

import type React from "react";
import { useMemo } from "react";
import {
	DndContext,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	rectSortingStrategy,
	sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";

import { useEffect, useState } from "react";
import ArchivePanel from "@/components/creative-profile/archive-panel";
import AssignmentsPanel from "@/components/creative-profile/assignment-panel";
import OfferAvailabilityDisplay from "@/components/creative-profile/offer-availability-display";
import BookingModal from "@/components/creative-profile/booking-modal";
import { useCurrentUserProjects } from "@/src/hooks/data/useCurrentUserProjects";
import { useUserContext } from "@/src/contexts/UserContext";
import { useUserDoc } from "@/src/hooks/data/useUsers";
import IncomePanel from "@/components/creative-profile/income-panel";
import PhotoDetailModal from "@/components/creative-profile/photo-detail-modal";
import PhotoGridPanel from "@/components/creative-profile/photo-grid-panel";
import PortfolioTabs from "@/components/creative-profile/portfolio-tabs";
import UserProfileHeader from "@/components/creative-profile/user-profile-header";

import { useBookingModal } from "@/components/hooks/use-booking-modal";
import { usePhotoDetailModal } from "@/components/hooks/use-photo-detail-modal";
import { usePhotoSelection } from "@/components/hooks/use-photo-selection";
import { useTextEditing } from "@/components/hooks/use-text-editing";
import { useTileOperations } from "@/components/hooks/use-tile-operations";
import { useOfferManagement } from "@/components/hooks/useOfferManagement";
import { useCreativePermissions } from "@/components/hooks/use-creative-permissions";
import { useViewMode } from "@/components/hooks/use-view-mode";
import SiteHeader from "@/components/layout/site-header";
import {
	type CreativeOffer,
	type Job,
	type Payment,
	type Photo,
	type Project,
	offersReceivedByCreative,
	offersSentByCreative,
} from "@/instagram-photo-grid-data";

import type { Collaboration as LiveCollaboration } from "@/src/types/collections/booker";
import type { PortfolioItem, Creative } from "@/src/types/collections/creative";


interface CreativeProfileClientPageProps {
	serverCreativeData: Creative;
	serverPortfolioItems: PortfolioItem[];
	serverOffers: any[]; // Use any[] since the server enriches the data
	serverCollaborations: LiveCollaboration[];
	serverProjects: Project[];
}

export default function CreativeProfileClientPage({
	serverCreativeData,
	serverPortfolioItems,
	serverOffers,
	serverCollaborations,
	serverProjects,
}: CreativeProfileClientPageProps) {
	// Always call ALL hooks in the same order, regardless of data state
	const { approveOffer, rejectOffer } = useOfferManagement();
	const { hasEditPermission } = useCreativePermissions(serverCreativeData?.id || "");

	const [photos, setPhotos] = useState<Photo[]>([]);
	const [upcomingJobs, setUpcomingJobs] = useState<Job[]>([]);
	const [receivedOffers, setReceivedOffers] = useState<CreativeOffer[]>([]);
	const [sentOffers, setSentOffers] = useState<CreativeOffer[]>([]);
	const [activeTab, setActiveTab] = useState<"photos" | "assignments" | "income" | "archive">("photos");
	const [assignmentsViewMode, setAssignmentsViewMode] = useState<"upcoming" | "receivedOffers" | "rejectedOffers">("receivedOffers");
	const [selectedInvoice, setSelectedInvoice] = useState<string | null>(null);
	const [likedPhotos, setLikedPhotos] = useState(new Set<string>());

	// Get current user context for booking submission
	const { data: userData } = useUserContext();
	const currentUser = userData?.user;
	const { data: userRootAccount } = useUserDoc(currentUser?.uid || "");

	// Get booker info for booking submission - always provide strings to maintain consistent hook calls
	const currentBookerId = userRootAccount?.bookers ? Object.keys(userRootAccount.bookers)[0] : "";
	const bookerName = currentUser?.displayName || currentUser?.email || "";

	const { selectedTiles, isSelectionMode, selectedTilesCount, toggleTileSelection, clearSelection, toggleSelectionMode } = usePhotoSelection();
	const { isViewMode, toggleViewMode } = useViewMode();
	const {
		createPortfolioTile,
		createIframeTile,
		uploadAndCreateImageTile,
		mergeTiles,
		splitTile,
		convertToTextTile,
		convertToImageTile,
		deleteSelectedTiles,
		saveTileToBackend,
		reorderTiles,
	} = useTileOperations({
		photos,
		setPhotos,
		selectedTiles,
		clearSelection,
		creativeId: serverCreativeData?.id || "",
		isSelectionMode,
		toggleSelectionMode,
	});

	const {
		editingTile,
		tempText,
		startEditingText,
		handleSetTempText,
		saveTextEdit,
		cancelTextEdit,
		tempBackgroundColor,
		handleSetTempBackgroundColor,
		tempFontSize,
		handleSetTempFontSize,
		tempFontFamily,
		handleSetTempFontFamily,
	} = useTextEditing({ setPhotos, saveTileToBackend });

	const {
		showBookingModal,
		bookingForm,
		isSubmitting,
		openBookingModal,
		closeBookingModal,
		updateBookingFormField,
		handleBookingFormProjectSelect,
		handleBookingSubmit,
	} = useBookingModal({
		creativeId: serverCreativeData?.id || "",
		creativeName: serverCreativeData?.displayName || "",
		bookerId: currentBookerId,
		bookerName: bookerName,
	});

	// Get current user's booker projects with stable hook calls
	const { projects: currentUserProjects, isLoading: isLoadingProjects } = useCurrentUserProjects();

	const {
		selectedPhoto,
		openPhotoDetail,
		closePhotoDetail,
	} = usePhotoDetailModal({
		initialLikedPhotos: likedPhotos,
		onToggleLike: (photoId: string) => {
			setLikedPhotos((prev) => {
				const newLikedPhotos = new Set(prev);
				if (newLikedPhotos.has(photoId)) {
					newLikedPhotos.delete(photoId);
				} else {
					newLikedPhotos.add(photoId);
				}
				return newLikedPhotos;
			});
		},
	});

	// Create sensors with consistent structure to avoid useEffect dependency array size changes
	const pointerSensor = useSensor(PointerSensor, {
		activationConstraint: {
			distance: 5,
		},
	});

	const keyboardSensor = useSensor(KeyboardSensor, {
		coordinateGetter: sortableKeyboardCoordinates,
	});

	// Always provide the same sensors array to maintain consistent dependency array size
	const sensors = useMemo(() => [pointerSensor, keyboardSensor], [pointerSensor, keyboardSensor]);

	// Move useEffect BEFORE early return to maintain hook order
	useEffect(() => {
		// Only run if we have server data
		if (!serverCreativeData || !serverPortfolioItems || !serverCollaborations || !serverProjects || !serverOffers) {
			return;
		}
		// First pass: Create all photos with empty originalPhotos arrays
		const mappedPhotos: Photo[] = serverPortfolioItems.map((item) => ({
			id: item.id,
			src: item.imageUrl || `/placeholder.svg?text=${item.alt || "Image"}`,
			alt: item.alt || "Portfolio image",
			likes: item.likes || 0,
			comments: item.comments || 0,
			size: item.size,
			isMerged: item.isMerged,
			originalPhotos: [], // Will be populated in second pass
			type: item.type,
			textContent: item.textContent,
			backgroundColor: item.backgroundColor,
			position: item.position,
			fontSize: item.fontSize,
			fontFamily: item.fontFamily,
			iframeUrl: item.iframeUrl, // Add the missing iframeUrl mapping
			colSpan: item.size === "horizontal" || item.size === "large" ? 2 : 1,
			rowSpan: item.size === "vertical" || item.size === "large" ? 2 : 1,
		}));

		// Second pass: Populate originalPhotos arrays for merged tiles
		const photosWithOriginals = mappedPhotos.map((photo) => {
			const serverItem = serverPortfolioItems.find(item => item.id === photo.id);
			if (serverItem?.isMerged && serverItem.originalItems) {
				// Find the original photos by their IDs
				const originalPhotos = serverItem.originalItems
					.map(originalId => mappedPhotos.find(p => p.id === originalId))
					.filter((p): p is Photo => p !== undefined);

				// If we couldn't find the original photos (they were deleted during merge),
				// create placeholder photos that allow splitting to work
				if (originalPhotos.length === 0 && serverItem.originalItems.length > 0) {
					const placeholderPhotos: Photo[] = serverItem.originalItems.map((originalId, index) => ({
						id: originalId,
						src: photo.src, // Use the merged photo's src as fallback
						alt: `${photo.alt} - Part ${index + 1}`,
						likes: Math.floor((photo.likes || 0) / serverItem.originalItems!.length),
						comments: Math.floor((photo.comments || 0) / serverItem.originalItems!.length),
						size: "standard" as const,
						type: photo.type,
						position: (photo.position || 0) + index,
						textContent: photo.type === "text" ? photo.textContent : undefined,
						backgroundColor: photo.backgroundColor,
						fontSize: photo.fontSize,
						fontFamily: photo.fontFamily,
						iframeUrl: photo.type === "iframe" ? photo.iframeUrl : undefined,
					}));

					return {
						...photo,
						originalPhotos: placeholderPhotos,
					};
				}

				return {
					...photo,
					originalPhotos,
				};
			}
			return photo;
		});

		setPhotos(photosWithOriginals);

		const projectsMap = new Map(serverProjects.map((p) => [p.id, p]));
		const mappedJobs: Job[] = serverCollaborations.map((collab) => {
			const project = projectsMap.get(collab.projectId);
			return {
				id: collab.id,
				title: project?.title || "Untitled Project",
				client: project?.client || "Unknown Client",
				date: new Date(collab.completedDate as any).toLocaleDateString(),
				time: "TBD",
				location: project?.location || "TBD",
				status: collab.status as "confirmed" | "pending" | "completed",
				type: "Collaboration",
			};
		});
		setUpcomingJobs(mappedJobs);
		
		// Use real offers if available, otherwise fallback to mock data for demonstration
		let finalReceivedOffers: CreativeOffer[] = [];
		let finalSentOffers: CreativeOffer[] = [];

		if (serverOffers && serverOffers.length > 0) {
			// Use real Firebase offers
			const mappedOffers: CreativeOffer[] = serverOffers.map(
				(offer: any) => ({
						id: offer.id,
						projectTitle: offer.projectTitle || "Untitled Project",
						clientName: offer.bookerName || "Unknown Client",
						role: offer.role || "Role TBD",
						amount: typeof offer.amount === 'number' ? offer.amount : 0,
						// Use offerStartDate if available
						offerDate: offer.offerStartDate ? new Date(offer.offerStartDate as any).toISOString().split('T')[0] : "Date TBD",
						status: (offer.status === "draft" ? "pending" : offer.status || "pending") as "pending" | "accepted" | "declined" | "expired" | "withdrawn" | "negotiating" | "pending_creative_approval" | "active" | "rejected_by_creative",
						projectType: offer.projectType || "Type TBD",
						location: offer.location || "Location TBD",
						description: offer.description || "",
						isSentByMe: offer.isSentByCreative || offer.createdByRole === "creative" || false,
					})
				);
			finalReceivedOffers = mappedOffers.filter((o) => !o.isSentByMe);
			finalSentOffers = mappedOffers.filter((o) => o.isSentByMe);
		} else {
			// Use mock data for demonstration when no real offers exist
			finalReceivedOffers = offersReceivedByCreative;
			finalSentOffers = offersSentByCreative;
		}

		setReceivedOffers(finalReceivedOffers);
		setSentOffers(finalSentOffers);

	}, [serverPortfolioItems, serverCollaborations, serverProjects, serverOffers]);

	// Debug logging to see what server data we're getting
	console.log("Creative Profile - Server Offers:", serverOffers);
	console.log("Creative Profile - Mapped Received Offers:", receivedOffers);
	console.log("Creative Profile - Mapped Sent Offers:", sentOffers);

	// Redirect to allowed tab if user is on a restricted tab without permission
	useEffect(() => {
		if (!hasEditPermission && (activeTab === "assignments" || activeTab === "income")) {
			setActiveTab("photos"); // Redirect to photos tab
		}
	}, [hasEditPermission, activeTab]);

	// Early return AFTER all hooks to avoid hook order issues
	if (!serverCreativeData) {
		return <div>Loading...</div>;
	}

	// Handle tab changes with permission checks
	const handleTabChange = (tabName: "photos" | "assignments" | "income" | "archive") => {
		// If user doesn't have edit permission, only allow "photos" and "archive" tabs
		if (!hasEditPermission && (tabName === "assignments" || tabName === "income")) {
			return; // Ignore the tab change request
		}
		setActiveTab(tabName);
	};

	const toggleLike = (photoId: string) => {
		setLikedPhotos((prev) => {
			const newLikedPhotos = new Set(prev);
			if (newLikedPhotos.has(photoId)) {
				newLikedPhotos.delete(photoId);
			} else {
				newLikedPhotos.add(photoId);
			}
			return newLikedPhotos;
		});
	};

	const openPhotoDetailInGrid = (photo: Photo) => {
		if (!isSelectionMode && !editingTile) {
			openPhotoDetail(photo);
		}
	};

	const handleDragEnd = async (event: any) => {
		// Only allow drag and drop in edit mode (selection mode)
		if (!isSelectionMode) {
			return;
		}

		const { active, over } = event;

		if (active.id !== over.id) {
			const oldIndex = photos.findIndex((p) => p.id === active.id);
			const newIndex = photos.findIndex((p) => p.id === over.id);

			const newOrder = arrayMove(photos, oldIndex, newIndex);

			try {
				await reorderTiles(newOrder);
				console.log("Successfully updated portfolio order.");
			} catch (error) {
				console.error("Failed to update portfolio order:", error);
				// reorderTiles already handles reverting the state on error
			}
		}
	};

	const handleAcceptOffer = async (offerId: string) => {
		try {
			await approveOffer(offerId);
		} catch (error) {
			console.error("Failed to accept offer:", error);
			alert("Error accepting offer. Please try again.");
		}
	};

	const handleDeclineOffer = async (offerId: string) => {
		try {
			await rejectOffer(offerId);
		} catch (error) {
			console.error("Failed to decline offer:", error);
			alert("Error declining offer. Please try again.");
		}
	};
	
	const handleSelectInvoice = (invoiceId: string) => {
		setSelectedInvoice(prev => prev === invoiceId ? null : invoiceId);
	}

	const totalIncome = ((serverCreativeData as any)?.payments || []).reduce((acc: number, p: Payment) => acc + p.amount, 0);
	const totalIncomeFormatted = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(totalIncome);

	return (
		<DndContext
			sensors={sensors}
			collisionDetection={closestCenter}
			onDragEnd={handleDragEnd}
		>
			<div className="bg-white min-h-screen font-light">
				<SiteHeader />
				<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
					<UserProfileHeader
						creative={serverCreativeData}
						photoCount={photos.length}
					/>

					{/* Offer-Based Availability Display */}
					<OfferAvailabilityDisplay
						activeTab={activeTab}
						receivedOffers={receivedOffers}
						sentOffers={sentOffers}
						onOpenBookingModal={openBookingModal}
						readOnly={!hasEditPermission}
					/>

					<div className="mt-8">
						<PortfolioTabs
							activeTab={activeTab}
							onTabChange={handleTabChange}
							photoCount={photos.length}
							assignmentCount={receivedOffers.length + sentOffers.length + upcomingJobs.length}
							totalIncomeFormatted={totalIncomeFormatted}
							archiveCount={((serverCreativeData as any)?.payments || []).length}
							hasEditPermission={hasEditPermission}
							isSelectionMode={isSelectionMode}
							onToggleSelectionMode={toggleSelectionMode}
							onMergeTiles={mergeTiles}
							onClearSelection={clearSelection}
							onConvertToText={convertToTextTile}
							onConvertToImage={convertToImageTile}
							onDeleteSelected={deleteSelectedTiles}
							selectedTilesCount={selectedTilesCount}
							onAddTile={createPortfolioTile}
							onUploadImage={uploadAndCreateImageTile}
							onAddIframe={createIframeTile}
							isViewMode={isViewMode}
							onToggleViewMode={toggleViewMode}
						/>
						<SortableContext items={photos.map((p) => p.id)} strategy={rectSortingStrategy}>
							<div className="mt-8">
							{activeTab === "photos" && (
									<PhotoGridPanel
										photos={photos}
										isSelectionMode={isSelectionMode}
										selectedTiles={selectedTiles}
										editingTile={editingTile}
										tempText={tempText}
										tempBackgroundColor={tempBackgroundColor}
										tempFontSize={tempFontSize}
										tempFontFamily={tempFontFamily}
										hasEditPermission={hasEditPermission}
										onOpenPhoto={openPhotoDetailInGrid}
										onToggleTileSelection={toggleTileSelection}
										onStartEditingText={startEditingText}
										onSetTempText={handleSetTempText}
										onSetTempBackgroundColor={handleSetTempBackgroundColor}
										onSetTempFontSize={handleSetTempFontSize}
										onSetTempFontFamily={handleSetTempFontFamily}
										onSaveTextEdit={saveTextEdit}
										onCancelTextEdit={cancelTextEdit}
										splitTile={splitTile}
										isViewMode={isViewMode}
									/>
							)}
							</div>
						</SortableContext>
						{activeTab === "assignments" && hasEditPermission && (
							<div className="mt-8">
								<AssignmentsPanel
									viewMode={assignmentsViewMode}
									onViewModeChange={setAssignmentsViewMode}
									jobs={upcomingJobs}
									receivedOffers={receivedOffers}
									sentOffers={sentOffers}
									onAcceptOffer={handleAcceptOffer}
									onDeclineOffer={handleDeclineOffer}
								/>
							</div>
						)}
						{activeTab === "income" && hasEditPermission && (
							<div className="mt-8">
								<IncomePanel
									payments={(serverCreativeData as any)?.payments || []}
									totalIncome={totalIncome}
									onViewInvoice={handleSelectInvoice}
								/>
							</div>
						)}
						{activeTab === "archive" && (
							<div className="mt-8">
								<ArchivePanel payments={(serverCreativeData as any)?.payments || []} />
							</div>
						)}
					</div>
				</div>

				<PhotoDetailModal
					photo={selectedPhoto}
					onClose={closePhotoDetail}
					likedPhotos={likedPhotos}
					onToggleLike={toggleLike}
				/>
				<BookingModal
					show={showBookingModal}
					onClose={closeBookingModal}
					bookingForm={bookingForm}
					onFormChange={updateBookingFormField}
					onSubmit={handleBookingSubmit}
					projects={currentUserProjects}
					onFormProjectSelect={handleBookingFormProjectSelect}
					isSubmitting={isSubmitting}
					isLoadingProjects={isLoadingProjects}
				/>


			</div>
		</DndContext>
	);
}