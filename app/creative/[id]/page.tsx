import { notFound, redirect } from "next/navigation";
import { adminDb } from "@/src/lib/firebase/server-init";
import type {
	Creative,
	PortfolioItem,
} from "@/src/types/collections/creative";
import type {
	Collaboration,
	Offer,
	Project,
} from "@/src/types/collections/booker";
import CreativeProfileClientPage from "./creative-profile-client-page";
import ErrorBoundary from "@/components/error-boundary";
import { createHash } from "crypto";

export const dynamic = "force-dynamic";

// Helper to serialize Firestore Timestamps
function serializeData(doc: any): any {
	if (doc === null || doc === undefined) {
		return doc;
	}
	const data = { ...doc };
	for (const key of Object.keys(data)) {
		if (data[key] && typeof data[key].toDate === "function") {
			// Check for Firestore Timestamp
			data[key] = data[key].toDate().toISOString();
		}
	}
	return data;
}

/**
 * Generates a stable, unique key from the portfolio items.
 * Any change in the items will result in a different key, forcing a re-render.
 */
function generatePortfolioKey(items: PortfolioItem[]): string {
	const hash = createHash("sha256");
	hash.update(JSON.stringify(items));
	return hash.digest("hex");
}

export default async function CreativeProfilePage({
	params,
}: {
	params: Promise<{ id: string }>;
}) {
	const { id } = await params;
	let creativeIdToFetch: string | null = null;

	// Step 1: Check if the provided ID is a direct creative ID.
	const creativeCheckRef = adminDb.collection("creatives").doc(id);
	const creativeCheckSnap = await creativeCheckRef.get();

	if (creativeCheckSnap.exists) {
		// If it exists, the provided ID is a valid creativeId.
		creativeIdToFetch = id;
		console.log(`[Server-Side] ID "${id}" is a direct creative ID.`);
	} else {
		// Step 2: If not, assume it's a userId and look up the associated creativeId.
		console.log(
			`[Server-Side] ID "${id}" not found in creatives. Checking user-root-accounts...`,
		);
		const userRootRef = adminDb.collection("user-root-accounts").doc(id);
		const userRootSnap = await userRootRef.get();

		if (userRootSnap.exists) {
			const userRootData = userRootSnap.data();
			const creativeIds = userRootData?.creatives
				? Object.keys(userRootData.creatives)
				: [];

			if (creativeIds.length > 0) {
				creativeIdToFetch = creativeIds[0]; // Use the first creative profile
				console.log(
					`[Server-Side] Found creative ID "${creativeIdToFetch}" for user "${id}".`,
				);
			} else {
				// User exists but has no creative profile. Redirect them to create one.
				console.log(
					`[Server-Side] User "${id}" exists but has no creative profile. Redirecting to create-profile.`,
				);
				redirect("/creative/create-profile");
			}
		}
	}

	// Step 3: If we couldn't find a creative ID by either method, the page can't be found.
	if (!creativeIdToFetch) {
		console.error(
			`[Server-Side] Could not resolve a creative profile from the provided ID: ${id}`,
		);
		return notFound();
	}

	// Step 4: Fetch the definitive creative document using the resolved ID.
	const creativeDocRef = adminDb.collection("creatives").doc(creativeIdToFetch);
	const creativeDocSnap = await creativeDocRef.get();

	if (!creativeDocSnap.exists) {
		console.error(
			`[Server-Side] Creative document with ID: ${creativeIdToFetch} not found, though it was expected.`,
		);
		return notFound();
	}

	const creativeData = serializeData({
		id: creativeDocSnap.id,
		...creativeDocSnap.data(),
	}) as Creative;

	// Step 5: Fetch portfolio items from the root collection (the correct way).
	const portfolioPromise = adminDb
		.collection("portfolio-items")
		.where("creativeId", "==", creativeIdToFetch)
		.orderBy("position")
		.get();
	const offersPromise = adminDb
		.collection("offers")
		.where("creativeId", "==", creativeIdToFetch)
		.get();
	const collaborationsPromise = adminDb
		.collection("collaborations")
		.where("creativeId", "==", creativeIdToFetch)
		.get();

	const [portfolioSnap, offersSnap, collaborationsSnap] = await Promise.all([
		portfolioPromise,
		offersPromise,
		collaborationsPromise,
	]);

	const portfolioItems = portfolioSnap.docs.map((doc: any) =>
		serializeData({ id: doc.id, ...doc.data() }),
	) as PortfolioItem[];

	const rawOffers = offersSnap.docs.map((doc: any) =>
		serializeData({ id: doc.id, ...doc.data() }),
	) as Offer[];

	const collaborations = collaborationsSnap.docs.map((doc: any) =>
		serializeData({ id: doc.id, ...doc.data() }),
	) as Collaboration[];

	// Step 6: Gather all unique project IDs and booker IDs from offers and collaborations
	const projectIds = [
		...new Set([
			...rawOffers.map((o) => o.projectId),
			...collaborations.map((c) => c.projectId),
		]),
	];

	const bookerIds = [
		...new Set([
			...rawOffers.map((o) => o.bookerId),
			...collaborations.map((c) => c.bookerId),
		]),
	];

	// Fetch projects and bookers in parallel
	const [projectsSnap, bookersSnap] = await Promise.all([
		projectIds.length > 0
			? adminDb
					.collection("projects")
					.where("__name__", "in", projectIds)
					.get()
			: Promise.resolve({ docs: [] }),
		bookerIds.length > 0
			? adminDb
					.collection("bookers")
					.where("__name__", "in", bookerIds)
					.get()
			: Promise.resolve({ docs: [] }),
	]);

	const projects = projectsSnap.docs.map((doc: any) =>
		serializeData({ id: doc.id, ...doc.data() }),
	) as Project[];

	const bookers = bookersSnap.docs.map((doc: any) =>
		serializeData({ id: doc.id, ...doc.data() }),
	) as any[];

	// Step 7: Enrich offers with project and booker data
	const offers = rawOffers.map((offer) => {
		const project = projects.find((p) => p.id === offer.projectId);
		const booker = bookers.find((b) => b.id === offer.bookerId);

		return {
			...offer,
			projectTitle: project?.title || offer.projectTitle || "Untitled Project",
			bookerName: booker?.displayName || offer.bookerName || "Unknown Client",
			projectType: project?.projectType || offer.projectType || "Unknown Type",
			location: project?.location || offer.location || "Location TBD",
		};
	});

	// Step 8. Generate a key to force re-mounting on data change
	const portfolioKey = generatePortfolioKey(portfolioItems);

	// Step 9. Pass serialized data and the key to the Client Component
	return (
		<ErrorBoundary>
			<CreativeProfileClientPage
				key={portfolioKey}
				serverCreativeData={creativeData}
				serverPortfolioItems={portfolioItems}
				serverOffers={offers}
				serverCollaborations={collaborations}
				serverProjects={projects}
			/>
		</ErrorBoundary>
	);
}
