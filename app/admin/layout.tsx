"use client";

import AdminAuthGuard from "@/components/admin/admin-auth-guard";
import { useAuth } from "@/components/hooks/use-auth";
import { useUserContext } from "@/src/contexts/UserContext";

interface AdminLayoutProps {
	children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
	const { data } = useUserContext();
	const { signOutUser, isSigningOut } = useAuth();

	return (
		<AdminAuthGuard>
			<div className="min-h-screen bg-gray-50">
				{/* Admin Header */}
				<header className="bg-white shadow border-b">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="flex justify-between items-center py-4">
							<div className="flex items-center">
								<h1 className="text-2xl font-bold text-gray-900">
									DUA Admin Dashboard
								</h1>
								<span className="ml-3 px-2 py-1 text-xs bg-red-100 text-red-700 rounded">
									Admin Only
								</span>
							</div>
							
							<div className="flex items-center space-x-4">
								{data.user && (
									<div className="text-sm text-gray-600">
										Welcome, {data.user.displayName || data.user.email}
									</div>
								)}
								<button
									onClick={signOutUser}
									disabled={isSigningOut}
									className="px-4 py-2 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
								>
									{isSigningOut ? "Signing out..." : "Sign Out"}
								</button>
							</div>
						</div>
					</div>
				</header>

				{/* Admin Navigation */}
				<nav className="bg-white border-b">
					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
						<div className="flex space-x-8">
							<a
								href="/admin"
								className="border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600"
							>
								Users
							</a>
							{/* Add more navigation items here as needed */}
						</div>
					</div>
				</nav>

				{/* Main Content */}
				<main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
					{children}
				</main>
			</div>
		</AdminAuthGuard>
	);
}
