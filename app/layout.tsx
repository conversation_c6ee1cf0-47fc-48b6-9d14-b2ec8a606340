"use client";

import type { Metadata } from "next";
import "./globals.css";
import { THEME_NAMES, ThemeProvider } from "dua-component-library";
import { FirebaseComponents } from "../src/components/FirebaseComponents"; // Adjust path
import { useEffect } from "react";

// Note: Static metadata export like below is not allowed in Client Components.
// If you need dynamic metadata or metadata that depends on client-side data,
// you'll need to handle it differently (e.g., using the metadata API in child server components or pages).
// For now, I'm commenting it out. You can move it to a Server Component parent if needed.
/*
export const metadata: Metadata = {
  title: 'v0 App',
  description: 'Created with v0',
  generator: 'v0.dev',
}
*/

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	// Global error handler for chunk load errors and Firebase permission errors
	useEffect(() => {
		const handleError = (event: ErrorEvent) => {
			const error = event.error;
			if (error?.name === "ChunkLoadError" || error?.message?.includes("Loading chunk")) {
				console.log("Global ChunkLoadError detected, reloading page...");
				window.location.reload();
			} else if (error?.code === "permission-denied" || error?.message?.includes("Missing or insufficient permissions")) {
				console.log("Global Firebase permission error detected, redirecting to home...");
				window.location.href = "/";
			}
		};

		const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
			const error = event.reason;
			if (error?.name === "ChunkLoadError" || error?.message?.includes("Loading chunk")) {
				console.log("Global ChunkLoadError (unhandled promise) detected, reloading page...");
				window.location.reload();
			} else if (error?.code === "permission-denied" || error?.message?.includes("Missing or insufficient permissions")) {
				console.log("Global Firebase permission error (unhandled promise) detected, redirecting to home...");
				window.location.href = "/";
			}
		};

		window.addEventListener("error", handleError);
		window.addEventListener("unhandledrejection", handleUnhandledRejection);

		return () => {
			window.removeEventListener("error", handleError);
			window.removeEventListener("unhandledrejection", handleUnhandledRejection);
		};
	}, []);

	return (
		<html lang="en">
			<body>
				<FirebaseComponents>
					<ThemeProvider defaultTheme={THEME_NAMES.LIGHT}>
						{children}
					</ThemeProvider>
				</FirebaseComponents>
			</body>
		</html>
	);
}
