"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Camera, Users, Palette, Video, User, Briefcase } from "lucide-react";

export default function SignupPage() {
	const router = useRouter();

	const handleUserTypeSelection = (userType: "creative" | "booker") => {
		router.push(`/signup/${userType}`);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
			{/* Header */}
			<header className="border-b border-gray-200 bg-white">
				<div className="max-w-4xl mx-auto px-6 py-6">
					<div className="flex items-center justify-between">
						<Link href="/" className="text-2xl font-light tracking-wide">
							DUA
						</Link>
						<div className="flex items-center space-x-4">
							<span className="text-sm text-gray-600">Already have an account?</span>
							<Link 
								href="/" 
								className="text-sm text-blue-600 hover:text-blue-700 font-medium"
							>
								Sign In
							</Link>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-4xl mx-auto px-6 py-12">
				<div className="text-center mb-12">
					<p className="text-xl text-gray-600 max-w-2xl mx-auto">
						Connect with creative professionals or find the perfect talent for your next project. 
						Choose your path to get started.
					</p>
				</div>

				{/* User Type Selection */}
				<div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
					{/* Creative Card */}
					<Card 
						className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 hover:border-blue-300"
						onClick={() => handleUserTypeSelection("creative")}
					>
						<CardHeader className="text-center pb-4">
							<div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
								<Palette className="h-8 w-8 text-blue-600" />
							</div>
							<CardTitle className="text-2xl font-bold text-gray-900">
								I'm a Creative
							</CardTitle>
							<CardDescription className="text-gray-600">
								Showcase your work and connect with clients
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center space-x-3">
									<Camera className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Photographers & Videographers</span>
								</div>
								<div className="flex items-center space-x-3">
									<User className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Models & Talent</span>
								</div>
								<div className="flex items-center space-x-3">
									<Palette className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Makeup Artists & Stylists</span>
								</div>
								<div className="flex items-center space-x-3">
									<Video className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Directors & Producers</span>
								</div>
							</div>
							
							<div className="pt-4 border-t border-gray-100">
								<h4 className="font-medium text-gray-900 mb-2">What you'll get:</h4>
								<ul className="text-sm text-gray-600 space-y-1">
									<li>• Professional portfolio showcase</li>
									<li>• Direct booking opportunities</li>
									<li>• Secure payment processing</li>
									<li>• Project management tools</li>
								</ul>
							</div>

							<Button 
								className="w-full mt-6"
								onClick={() => handleUserTypeSelection("creative")}
							>
								Join as Creative
							</Button>
						</CardContent>
					</Card>

					{/* Booker Card */}
					<Card 
						className="cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 border-2 hover:border-green-300"
						onClick={() => handleUserTypeSelection("booker")}
					>
						<CardHeader className="text-center pb-4">
							<div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
								<Briefcase className="h-8 w-8 text-green-600" />
							</div>
							<CardTitle className="text-2xl font-bold text-gray-900">
								I'm a Booker
							</CardTitle>
							<CardDescription className="text-gray-600">
								Find and hire creative professionals
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4">
							<div className="space-y-3">
								<div className="flex items-center space-x-3">
									<Users className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Creative Directors</span>
								</div>
								<div className="flex items-center space-x-3">
									<Briefcase className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Marketing Teams</span>
								</div>
								<div className="flex items-center space-x-3">
									<User className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Event Planners</span>
								</div>
								<div className="flex items-center space-x-3">
									<Video className="h-5 w-5 text-gray-500" />
									<span className="text-sm text-gray-700">Production Companies</span>
								</div>
							</div>
							
							<div className="pt-4 border-t border-gray-100">
								<h4 className="font-medium text-gray-900 mb-2">What you'll get:</h4>
								<ul className="text-sm text-gray-600 space-y-1">
									<li>• Access to verified talent</li>
									<li>• Project management tools</li>
									<li>• Secure escrow payments</li>
									<li>• Streamlined booking process</li>
								</ul>
							</div>

							<Button 
								className="w-full mt-6 bg-green-600 hover:bg-green-700"
								onClick={() => handleUserTypeSelection("booker")}
							>
								Join as Booker
							</Button>
						</CardContent>
					</Card>
				</div>

				{/* Additional Info */}
				<div className="text-center mt-12">
					<p className="text-sm text-gray-500">
						By signing up, you agree to our{" "}
						<Link href="/terms" className="text-blue-600 hover:text-blue-700">
							Terms & Conditions
						</Link>{" "}
						and{" "}
						<Link href="/privacy" className="text-blue-600 hover:text-blue-700">
							Privacy Policy
						</Link>
					</p>
				</div>
			</main>
		</div>
	);
}
