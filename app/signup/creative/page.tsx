"use client";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { auth } from "@/src/components/FirebaseComponents";
import { createUserWithEmailAndPassword } from "firebase/auth";
import { Loader2, ArrowLeft, Palette } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function CreativeSignupPage() {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [formData, setFormData] = useState({
		firstName: "",
		lastName: "",
		email: "",
		password: "",
		confirmPassword: "",
	});

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData(prev => ({
			...prev,
			[name]: value
		}));
	};

	const validateForm = () => {
		if (!formData.firstName.trim()) {
			setError("First name is required");
			return false;
		}
		if (!formData.lastName.trim()) {
			setError("Last name is required");
			return false;
		}
		if (!formData.email.trim()) {
			setError("Email is required");
			return false;
		}
		if (!formData.password) {
			setError("Password is required");
			return false;
		}
		if (formData.password.length < 6) {
			setError("Password must be at least 6 characters");
			return false;
		}
		if (formData.password !== formData.confirmPassword) {
			setError("Passwords do not match");
			return false;
		}
		return true;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		if (!validateForm()) {
			return;
		}

		setIsLoading(true);

		try {
			// Step 1: Create Firebase Auth user with email and password
			const userCredential = await createUserWithEmailAndPassword(
				auth,
				formData.email,
				formData.password
			);
			
			const user = userCredential.user;
			console.log("Creative auth user created successfully:", user.uid);

			// Store the user's name in localStorage to pass to profile creation
			localStorage.setItem('pendingCreativeProfile', JSON.stringify({
				firstName: formData.firstName,
				lastName: formData.lastName,
				email: formData.email,
				uid: user.uid
			}));

			// Step 2: Redirect to creative profile creation page
			// This follows the flow: Auth creation -> Profile creation -> Admin approval
			router.push('/creative/create-profile');

		} catch (error: any) {
			console.error("Creative signup error:", error);
			
			// Handle specific Firebase Auth errors
			switch (error.code) {
				case "auth/email-already-in-use":
					setError("An account with this email already exists. Please sign in instead.");
					break;
				case "auth/invalid-email":
					setError("Please enter a valid email address.");
					break;
				case "auth/weak-password":
					setError("Password is too weak. Please choose a stronger password.");
					break;
				default:
					setError("An unexpected error occurred. Please try again.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
			{/* Header */}
			<header className="border-b border-gray-200 bg-white">
				<div className="max-w-4xl mx-auto px-6 py-6">
					<div className="flex items-center justify-between">
						<Link href="/" className="text-2xl font-light tracking-wide">
							DUA
						</Link>
						<div className="flex items-center space-x-4">
							<Link 
								href="/signup" 
								className="flex items-center text-sm text-gray-600 hover:text-gray-900"
							>
								<ArrowLeft className="h-4 w-4 mr-1" />
								Back to signup options
							</Link>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-md mx-auto px-6 py-12">
				<Card className="shadow-lg">
					<CardHeader className="text-center">
						<div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
							<Palette className="h-8 w-8 text-blue-600" />
						</div>
						<CardTitle className="text-2xl font-bold text-gray-900">
							Join as a Creative
						</CardTitle>
						<CardDescription className="text-gray-600">
							Create your account to start showcasing your work and connecting with clients
						</CardDescription>
					</CardHeader>

					<CardContent>
						<form onSubmit={handleSubmit} className="space-y-4">
							{/* Name Fields */}
							<div className="grid grid-cols-2 gap-4">
								<div className="space-y-2">
									<Label htmlFor="firstName">First Name</Label>
									<Input
										id="firstName"
										name="firstName"
										type="text"
										value={formData.firstName}
										onChange={handleChange}
										disabled={isLoading}
										required
									/>
								</div>
								<div className="space-y-2">
									<Label htmlFor="lastName">Last Name</Label>
									<Input
										id="lastName"
										name="lastName"
										type="text"
										value={formData.lastName}
										onChange={handleChange}
										disabled={isLoading}
										required
									/>
								</div>
							</div>

							{/* Email */}
							<div className="space-y-2">
								<Label htmlFor="email">Email Address</Label>
								<Input
									id="email"
									name="email"
									type="email"
									value={formData.email}
									onChange={handleChange}
									disabled={isLoading}
									required
								/>
							</div>

							{/* Password */}
							<div className="space-y-2">
								<Label htmlFor="password">Password</Label>
								<Input
									id="password"
									name="password"
									type="password"
									value={formData.password}
									onChange={handleChange}
									disabled={isLoading}
									required
								/>
								<p className="text-xs text-gray-500">
									Must be at least 6 characters
								</p>
							</div>

							{/* Confirm Password */}
							<div className="space-y-2">
								<Label htmlFor="confirmPassword">Confirm Password</Label>
								<Input
									id="confirmPassword"
									name="confirmPassword"
									type="password"
									value={formData.confirmPassword}
									onChange={handleChange}
									disabled={isLoading}
									required
								/>
							</div>

							{/* Error Display */}
							{error && (
								<Alert variant="destructive">
									<AlertDescription>{error}</AlertDescription>
								</Alert>
							)}

							{/* Submit Button */}
							<Button type="submit" className="w-full" disabled={isLoading}>
								{isLoading ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Creating Account...
									</>
								) : (
									"Create Creative Account"
								)}
							</Button>
						</form>

						{/* Additional Info */}
						<div className="mt-6 text-center">
							<p className="text-sm text-gray-500">
								Already have an account?{" "}
								<Link href="/" className="text-blue-600 hover:text-blue-700 font-medium">
									Sign in here
								</Link>
							</p>
						</div>

						<div className="mt-4 text-center">
							<p className="text-xs text-gray-500">
								By creating an account, you agree to our{" "}
								<Link href="/terms" className="text-blue-600 hover:text-blue-700">
									Terms & Conditions
								</Link>{" "}
								and{" "}
								<Link href="/privacy" className="text-blue-600 hover:text-blue-700">
									Privacy Policy
								</Link>
							</p>
						</div>
					</CardContent>
				</Card>
			</main>
		</div>
	);
}
