import { adminDb } from "../../src/lib/firebase/server-init";
import { FirebaseCollections } from "../../src/constants/firebaseCollections";
import SearchCreativesClient from "../../search-creatives-client";

const CREATIVES_PER_PAGE = 20;

// Helper to search and paginate creatives
async function searchCreatives(
	db: FirebaseFirestore.Firestore,
	collectionPath: string,
	searchParams: {
		query?: string;
		type?: string;
		location?: string;
		page?: number;
		limit?: number;
	},
) {
	try {
		const {
			query = "",
			type = "",
			location = "",
			page = 1,
			limit = CREATIVES_PER_PAGE,
		} = searchParams;

		let creativesQuery: FirebaseFirestore.Query = db.collection(collectionPath);

		// Apply filters
		if (type && type !== "all" && type !== "") {
			creativesQuery = creativesQuery.where("creativeType", "==", type);
		}

		// Get all matching documents first (for search and count)
		const allMatchingSnapshot = await creativesQuery.get();
		let allCreatives = allMatchingSnapshot.docs.map((doc) => ({
			...doc.data(),
			id: doc.id,
		}));

		// Apply text search filters (client-side for now, as Firestore doesn't have full-text search)
		if (query) {
			const searchTerm = query.toLowerCase();
			allCreatives = allCreatives.filter((creative: any) => {
				const name = (creative.displayName || creative.name || "").toLowerCase();
				const creativeTags = creative.tags || [];
				const creativeLocation = creative.location || creative.address?.city || "";

				return (
					name.includes(searchTerm) ||
					creativeLocation.toLowerCase().includes(searchTerm) ||
					creativeTags.some((tag: string) => tag.toLowerCase().includes(searchTerm))
				);
			});
		}

		if (location) {
			const locationTerm = location.toLowerCase();
			allCreatives = allCreatives.filter((creative: any) => {
				const creativeLocation = creative.location || creative.address?.city || "";
				return creativeLocation.toLowerCase().includes(locationTerm);
			});
		}

		// Sort results
		allCreatives.sort((a: any, b: any) => {
			const nameA = (a.displayName || a.name || "").toLowerCase();
			const nameB = (b.displayName || b.name || "").toLowerCase();
			return nameA.localeCompare(nameB);
		});

		// Apply pagination
		const totalCount = allCreatives.length;
		const totalPages = Math.ceil(totalCount / limit);
		const offset = (page - 1) * limit;
		const paginatedCreatives = allCreatives.slice(offset, offset + limit);

		return {
			creatives: paginatedCreatives,
			totalCount,
			currentPage: page,
			totalPages,
			hasNextPage: page < totalPages,
			hasPrevPage: page > 1,
			searchApplied: !!(query || type || location),
		};
	} catch (error) {
		console.error(`Error searching collection ${collectionPath}:`, error);
		return {
			creatives: [],
			totalCount: 0,
			currentPage: 1,
			totalPages: 0,
			hasNextPage: false,
			hasPrevPage: false,
			searchApplied: false,
		};
	}
}

interface SearchPageProps {
	searchParams: Promise<{
		page?: string;
		q?: string;
		type?: string;
		location?: string;
	}>;
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
	const resolvedSearchParams = await searchParams;
	const page = parseInt(resolvedSearchParams.page || "1", 10);

	// Extract search parameters
	const searchQuery = resolvedSearchParams.q || "";
	const searchType = resolvedSearchParams.type || "";
	const searchLocation = resolvedSearchParams.location || "";

	// Search and fetch paginated creatives data on the server using admin SDK
	const paginationData = await searchCreatives(
		adminDb,
		FirebaseCollections.CREATIVES,
		{
			query: searchQuery,
			type: searchType,
			location: searchLocation,
			page,
			limit: CREATIVES_PER_PAGE,
		},
	);

	// A helper function to convert Firestore Timestamps to JSON-serializable format (ISO strings)
	const serialize = (data: any): any => {
		if (data === null || typeof data !== "object") {
			return data;
		}
		if (data instanceof Date) {
			return data.toISOString();
		}
		if (data?.toDate && typeof data.toDate === "function") {
			return data.toDate().toISOString();
		}
		if (Array.isArray(data)) {
			return data.map(serialize);
		}
		const result: { [key: string]: any } = {};
		for (const key in data) {
			result[key] = serialize(data[key]);
		}
		return result;
	};

	return (
		<SearchCreativesClient
			serverCreativesData={serialize(paginationData.creatives)}
			paginationInfo={{
				currentPage: paginationData.currentPage,
				totalPages: paginationData.totalPages,
				totalCount: paginationData.totalCount,
				hasNextPage: paginationData.hasNextPage,
				hasPrevPage: paginationData.hasPrevPage,
			}}
		/>
	);
}
