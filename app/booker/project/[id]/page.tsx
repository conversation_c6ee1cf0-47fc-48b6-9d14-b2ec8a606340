// app/booker/project/[id]/page.tsx

import { initializeServerApp } from "@/src/lib/firebase/server-init";
import { getFirestore, Timestamp } from "firebase-admin/firestore";
import { Project, Offer } from "@/components/types";
import ProjectDetailPageClient from "@/components/project-detail/project-detail-page-client";

interface ProjectDetailPageProps {
	params: {
		id: string;
	};
}

// This is a new Server Component for displaying a single project.
export default async function ProjectDetailPage({ params }: ProjectDetailPageProps) {
	const { id: projectId } = params;
	
	if (!projectId) {
		return <div>Project ID not found.</div>;
	}
	
	const app = initializeServerApp();
	const firestore = getFirestore(app);

	let projectData: Project | null = null;
	let offersData: Offer[] = [];

	try {
		const projectDoc = await firestore.collection("projects").doc(projectId).get();

		if (projectDoc.exists) {
			const data = projectDoc.data();

			const serializeTimestamp = (field: any): string => {
				if (!field) return "";
				if (field instanceof Timestamp) {
					return field.toDate().toISOString();
				}
				return String(field);
			};
			
			projectData = {
				id: projectDoc.id,
				bookerId: data?.bookerId || "",
				title: data?.title || "",
				client: data?.client || "",
				budget: data?.budget || 0,
				projectType: data?.projectType || "",
				startDate: serializeTimestamp(data?.startDate),
				endDate: serializeTimestamp(data?.endDate),
				location: data?.location || "",
				description: data?.description || "",
				creativesNeeded: data?.creativesNeeded || [],
				status: data?.status || "planning",
				roleBudgetOverrides: data?.roleBudgetOverrides || {},
				roleStatusOverrides: data?.roleStatusOverrides || {},
				createdAt: serializeTimestamp(data?.createdAt),
				updatedAt: serializeTimestamp(data?.updatedAt),
			} as Project;

			// Fetch offers for the project
			const offersSnapshot = await firestore.collection("offers").where("projectId", "==", projectId).get();
			offersData = offersSnapshot.docs.map(doc => {
				const offer = doc.data();
				return {
					id: doc.id,
					projectId: offer.projectId,
					projectTitle: offer.projectTitle,
					bookerId: offer.bookerId,
					bookerName: offer.bookerName,
					creativeId: offer.creativeId,
					creativeName: offer.creativeName,
					role: offer.role,
					amount: offer.amount,
					status: offer.status,
					projectType: offer.projectType,
					location: offer.location,
					description: offer.description,
					offerDate: serializeTimestamp(offer.offerDate),
					responseDate: serializeTimestamp(offer.responseDate),
					notes: offer.notes,
					isSentByCreative: offer.isSentByCreative
				} as Offer;
			});
		}
	} catch (error) {
		console.error("Error fetching project data:", error);
		return <div>Error loading project data.</div>;
	}

	if (!projectData) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<h1 className="text-2xl font-bold">Project Not Found</h1>
					<p className="mt-4 text-lg">
						We couldn't find a project with ID: <span className="font-mono bg-gray-200 p-1 rounded">{projectId}</span>
					</p>
				</div>
			</div>
		);
	}

	// The data is fetched on the server and passed to the client component for rendering.
	return <ProjectDetailPageClient project={projectData} offers={offersData} />;
}
