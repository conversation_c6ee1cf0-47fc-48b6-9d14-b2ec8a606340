import { FirebaseCollections } from "@/src/constants/firebaseCollections";
import type {
	Booker,
	Collaboration,
	Offer,
	Project,
} from "@/src/types/collections/booker";
import {
	Timestamp,
	type WhereFilterOp,
} from "firebase-admin/firestore";
import { notFound, redirect } from "next/navigation";
import BookerProfilePage from "../../../booker-profile-page";
import type { Payment } from "../../../components/types";
import { adminDb } from "../../../src/lib/firebase/server-init";
import { mockInvoices, mockEscrowTransactions } from "../../../src/hooks/data/mockInvoices";

interface PageProps {
	params: {
		id: string;
	};
}

// Helper to safely fetch a collection, returning an empty array on error.
async function getCollectionData(
	db: FirebaseFirestore.Firestore,
	collectionPath: string,
	whereClause: [string, WhereFilterOp, any],
) {
	try {
		const snapshot = await db
			.collection(collectionPath)
			.where(...whereClause)
			.get();
		return snapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }));
	} catch (error) {
		console.error(`Error fetching collection ${collectionPath}:`, error);
		return [];
	}
}

// Helper to safely fetch a single document, returning null on error.
async function getDocumentData(
	db: FirebaseFirestore.Firestore,
	collectionPath: string,
	docId: string,
) {
	try {
		const docRef = db.collection(collectionPath).doc(docId);
		const docSnap = await docRef.get();
		if (docSnap.exists) {
			return { ...docSnap.data(), id: docSnap.id };
		}
		return null;
	} catch (error) {
		console.error(
			`Error fetching document ${docId} from ${collectionPath}:`,
			error,
		);
		return null;
	}
}

export default async function BookerPage({ params }: PageProps) {
	const { id } = params;

	if (!id) {
		return <div>Booker ID not found.</div>;
	}

	// Use mock data for testing when ID is "1"
	if (id === "1") {
		const mockBookerData = {
			id: "1",
			userId: "user1",
			displayName: "SARAH MARKETING",
			email: "<EMAIL>",
			company: "CREATIVE DIRECTOR",
			location: "Los Angeles, CA",
			bio: "Creative director specializing in fashion and lifestyle campaigns",
			createdAt: new Date().toISOString(),
			updatedAt: new Date().toISOString(),
		};

		const mockProjects = [
			{
				id: "1",
				src: "/assets/mood_boards/mb1.png",
				alt: "Fashion Campaign",
				title: "Spring Fashion Campaign",
				description: "A vibrant spring fashion campaign featuring the latest trends",
				status: "completed",
				budget: 15000,
				location: "Los Angeles, CA",
				date: "2024-01-15",
				collaborators: ["Alex Johnson", "Sarah Wilson", "Emma Davis"],
			},
		];

		const mockCollaborations = [
			{
				id: "collab1",
				projectTitle: "Spring Fashion Campaign",
				artistName: "Alex Johnson",
				role: "Photographer",
				date: "2024-01-15",
				status: "completed",
			},
		];

		const mockOffers = [
			{
				id: "offer1",
				projectId: "1",
				projectTitle: "Spring Fashion Campaign",
				bookerId: "1",
				bookerName: "Sarah Marketing",
				creativeId: "1",
				creativeName: "Alex Johnson",
				role: "PHOTOGRAPHER",
				amount: 3500,
				offerDate: "2024-01-10",
				status: "active",
				projectType: "Photography",
				location: "Los Angeles, CA",
				isSentByCreative: false,
			},
			{
				id: "offer2",
				projectId: "2",
				projectTitle: "Product Launch Event",
				bookerId: "1",
				bookerName: "Sarah Marketing",
				creativeId: "2",
				creativeName: "Charlie Creative",
				role: "VIDEOGRAPHER",
				amount: 2800,
				offerDate: "2024-01-15",
				status: "pending",
				projectType: "Videography",
				location: "New York, NY",
				isSentByCreative: false,
			},
		];

		const mockPayments: Payment[] = [];

		// A helper function to convert Firestore Timestamps to JSON-serializable format (ISO strings)
		const serialize = (data: any): any => {
			if (data === null || typeof data !== "object") {
				return data;
			}
			if (data instanceof Timestamp) {
				return data.toDate().toISOString();
			}
			if (Array.isArray(data)) {
				return data.map(serialize);
			}
			const result: { [key: string]: any } = {};
			for (const key in data) {
				result[key] = serialize(data[key]);
			}
			return result;
		};

		return (
			<BookerProfilePage
				serverBookerData={serialize(mockBookerData)}
				serverProjects={serialize(mockProjects)}
				serverCollaborations={serialize(mockCollaborations)}
				serverOffers={serialize(mockOffers)}
				serverPayments={serialize(mockPayments)}
				serverInvoices={serialize(mockInvoices)}
				serverEscrowTransactions={serialize(mockEscrowTransactions)}
			/>
		);
	}

	let bookerIdToFetch: string | null = null;

	// Step 1: Check if the provided ID is a direct booker ID.
	const bookerCheckRef = adminDb.collection("bookers").doc(id);
	const bookerCheckSnap = await bookerCheckRef.get();

	if (bookerCheckSnap.exists) {
		// If it exists, the provided ID is a valid bookerId.
		bookerIdToFetch = id;
		console.log(`[Server-Side] ID "${id}" is a direct booker ID.`);
	} else {
		// Step 2: If not, assume it's a userId and look up the associated bookerId.
		console.log(
			`[Server-Side] ID "${id}" not found in bookers. Checking user-root-accounts...`,
		);
		const userRootRef = adminDb.collection("user-root-accounts").doc(id);
		const userRootSnap = await userRootRef.get();

		if (userRootSnap.exists) {
			const userRootData = userRootSnap.data();
			const bookerIds = userRootData?.bookers
				? Object.keys(userRootData.bookers)
				: [];

			if (bookerIds.length > 0) {
				bookerIdToFetch = bookerIds[0]; // Use the first booker profile
				console.log(
					`[Server-Side] Found booker ID "${bookerIdToFetch}" for user "${id}".`,
				);
			} else {
				// User exists but has no booker profile. Redirect them to create one.
				console.log(
					`[Server-Side] User "${id}" exists but has no booker profile. Redirecting to create-profile.`,
				);
				redirect("/booker/create-profile");
			}
		}
	}

	// Step 3: If we couldn't find a booker ID by either method, the page can't be found.
	if (!bookerIdToFetch) {
		console.error(
			`[Server-Side] Could not resolve a booker profile from the provided ID: ${id}`,
		);
		return notFound();
	}

	// Use the pre-initialized Firebase Admin DB
	const db = adminDb;

	// Fetch all data in parallel on the server using the resolved booker ID
	const [bookerData, projects, collaborations, offers, payments, invoices, escrowTransactions] =
		await Promise.all([
			getDocumentData(
				db,
				FirebaseCollections.BOOKERS,
				bookerIdToFetch,
			) as Promise<Booker | null>,
			getCollectionData(db, FirebaseCollections.PROJECTS, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<Project[]>,
			getCollectionData(db, FirebaseCollections.COLLABORATIONS, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<Collaboration[]>,
			getCollectionData(db, FirebaseCollections.OFFERS, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<Offer[]>,
			getCollectionData(db, FirebaseCollections.PAYMENTS, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<Payment[]>,
			getCollectionData(db, FirebaseCollections.INVOICES, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<any[]>,
			getCollectionData(db, FirebaseCollections.ESCROW_TRANSACTIONS, [
				"bookerId",
				"==",
				bookerIdToFetch,
			]) as Promise<any[]>,
		]);

	// Log the fetched data to see what we're getting from Firebase
	console.log(`[Server-Side] Fetched data for booker ${bookerIdToFetch}:`);
	console.log(`[Server-Side] - Projects: ${projects.length} items`);
	console.log(`[Server-Side] - Collaborations: ${collaborations.length} items`);
	console.log(`[Server-Side] - Offers: ${offers.length} items`);
	console.log(`[Server-Side] - Payments: ${payments.length} items`);
	console.log(`[Server-Side] - Invoices: ${invoices.length} items`);
	console.log(`[Server-Side] - Escrow Transactions: ${escrowTransactions.length} items`);
	if (offers.length > 0) {
		console.log(`[Server-Side] - First offer:`, offers[0]);
	}

	// Enrich offers with creative and project data
	const enrichedOffers = await Promise.all(
		offers.map(async (offer: any) => {
			try {
				// Fetch creative data
				const creativeData = await getDocumentData(
					db,
					FirebaseCollections.CREATIVES,
					offer.creativeId
				) as any;

				// Fetch project data
				const projectData = await getDocumentData(
					db,
					FirebaseCollections.PROJECTS,
					offer.projectId
				) as any;

				// Create enriched offer with all required properties
				return {
					...offer,
					creativeName: creativeData?.displayName || 'Unknown Creative',
					projectTitle: projectData?.title || 'Unknown Project',
					bookerName: (bookerData as any)?.displayName || 'Unknown Booker',
					role: offer.role || 'UNKNOWN',
					amount: offer.amount || 0,
					offerDate: offer.createdAt ? offer.createdAt.toDate().toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
					location: projectData?.location || 'Unknown Location',
					projectType: projectData?.type || 'Unknown Type',
					isSentByCreative: offer.createdByRole === 'creative',
					description: offer.description || '',
				};
			} catch (error) {
				console.error(`[Server-Side] Error enriching offer ${offer.id}:`, error);
				// Return offer with default values if enrichment fails
				return {
					...offer,
					creativeName: 'Unknown Creative',
					projectTitle: 'Unknown Project',
					bookerName: bookerData?.displayName || 'Unknown Booker',
					role: 'UNKNOWN',
					amount: 0,
					offerDate: new Date().toISOString().split('T')[0],
					location: 'Unknown Location',
					projectType: 'Unknown Type',
					isSentByCreative: false,
					description: '',
				};
			}
		})
	);

	console.log(`[Server-Side] Enriched ${enrichedOffers.length} offers`);
	if (enrichedOffers.length > 0) {
		console.log(`[Server-Side] - First enriched offer:`, enrichedOffers[0]);
	}

	if (!bookerData) {
		console.error(
			`[Server-Side] Booker document with ID: ${bookerIdToFetch} not found, though it was expected.`,
		);
		return notFound();
	}

	// A helper function to convert Firestore Timestamps to JSON-serializable format (ISO strings)
	const serialize = (data: any): any => {
		if (data === null || typeof data !== "object") {
			return data;
		}
		if (data instanceof Timestamp) {
			return data.toDate().toISOString();
		}
		if (Array.isArray(data)) {
			return data.map(serialize);
		}
		const result: { [key: string]: any } = {};
		for (const key in data) {
			result[key] = serialize(data[key]);
		}
		return result;
	};

	// Pass the server-fetched and serialized data as props to the client component
	return (
		<BookerProfilePage
			serverBookerData={serialize(bookerData)}
			serverProjects={serialize(projects)}
			serverCollaborations={serialize(collaborations)}
			serverOffers={serialize(enrichedOffers)}
			serverPayments={serialize(payments)}
			serverInvoices={serialize(invoices)}
			serverEscrowTransactions={serialize(escrowTransactions)}
		/>
	);
}
