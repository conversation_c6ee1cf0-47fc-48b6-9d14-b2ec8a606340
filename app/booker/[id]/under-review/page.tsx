"use client";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { FirebaseCollections } from "@/src/constants/firebaseCollections";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { useUser } from "reactfire";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/src/components/FirebaseComponents";
import { CheckCircle, Clock, AlertCircle, RefreshCw } from "lucide-react";
import Link from "next/link";

interface BookerProfile {
	id: string;
	userId: string;
	displayName: string;
	email: string;
	isApproved?: boolean;
	approvedAt?: any;
	createdAt: any;
	updatedAt: any;
}

interface UserRootAccount {
	id: string;
	email: string;
	displayName: string;
	bookers: Record<string, string>;
	creatives: Record<string, string>;
	verifiedByDua: boolean;
	admin: boolean;
	acceptedTAndC: boolean;
	isActive?: boolean;
	createdAt: any;
	updatedAt?: any;
}

export default function BookerUnderReviewPage({ params }: { params: { id: string } }) {
	const { status, data: user } = useUser();
	const router = useRouter();
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [bookerProfile, setBookerProfile] = useState<BookerProfile | null>(null);
	const [userRootAccount, setUserRootAccount] = useState<UserRootAccount | null>(null);
	const [refreshing, setRefreshing] = useState(false);

	const userId = params.id;

	// Check if current user can access this page
	useEffect(() => {
		if (status === "success" && user) {
			if (user.uid !== userId) {
				router.push("/");
				return;
			}
		}
	}, [status, user, userId, router]);

	const fetchProfileStatus = async () => {
		if (!user) return;

		try {
			setError(null);
			
			// Fetch user root account
			const userRootRef = doc(db, FirebaseCollections.USER_ROOT_ACCOUNT, userId);
			const userRootSnap = await getDoc(userRootRef);

			if (!userRootSnap.exists()) {
				setError("User account not found. Please contact support.");
				return;
			}

			const userRootData = userRootSnap.data() as UserRootAccount;
			setUserRootAccount(userRootData);

			// Get booker IDs from user root account
			const bookerIds = Object.keys(userRootData.bookers || {});
			
			if (bookerIds.length === 0) {
				setError("No booker profile found. Please complete your profile creation.");
				return;
			}

			// Fetch the first booker profile (assuming one profile per user for now)
			const bookerId = bookerIds[0];
			const bookerRef = doc(db, FirebaseCollections.BOOKERS, bookerId);
			const bookerSnap = await getDoc(bookerRef);

			if (!bookerSnap.exists()) {
				setError("Booker profile not found. Please contact support.");
				return;
			}

			const bookerData = bookerSnap.data() as BookerProfile;
			setBookerProfile({ ...bookerData, id: bookerId });

		} catch (err: any) {
			console.error("Error fetching profile status:", err);
			setError("Failed to load profile status. Please try again.");
		} finally {
			setLoading(false);
			setRefreshing(false);
		}
	};

	useEffect(() => {
		if (status === "success" && user && user.uid === userId) {
			fetchProfileStatus();
		}
	}, [status, user, userId]);

	const handleRefresh = async () => {
		setRefreshing(true);
		await fetchProfileStatus();
	};

	const getStatusInfo = () => {
		if (!bookerProfile || !userRootAccount) {
			return {
				status: "unknown",
				title: "Status Unknown",
				description: "Unable to determine profile status",
				icon: AlertCircle,
				color: "text-gray-500",
				bgColor: "bg-gray-50",
				borderColor: "border-gray-200"
			};
		}

		// Check verifiedByDua field in UserRootAccount instead of individual profile approval
		if (userRootAccount.verifiedByDua) {
			return {
				status: "approved",
				title: "Profile Approved! 🎉",
				description: "Your booker profile has been approved by DUA and is now active. You can start searching for creative talent and creating projects.",
				icon: CheckCircle,
				color: "text-green-600",
				bgColor: "bg-green-50",
				borderColor: "border-green-200"
			};
		}

		return {
			status: "pending",
			title: "Profile Under Review",
			description: "Your booker profile is currently being reviewed by our admin team. This process typically takes 1-2 business days.",
			icon: Clock,
			color: "text-yellow-600",
			bgColor: "bg-yellow-50",
			borderColor: "border-yellow-200"
		};
	};

	if (status === "loading" || loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
					<p>Loading profile status...</p>
				</div>
			</div>
		);
	}

	if (!user || user.uid !== userId) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<p>Redirecting...</p>
				</div>
			</div>
		);
	}

	const statusInfo = getStatusInfo();
	const StatusIcon = statusInfo.icon;

	return (
		<div className="min-h-screen bg-gray-50 py-12 px-4">
			<div className="max-w-4xl mx-auto">
				<Card className="shadow-lg">
					<CardHeader className="text-center">
						<CardTitle className="text-3xl font-bold text-gray-900">
							Booker Profile Status
						</CardTitle>
						<p className="text-gray-600 mt-2">
							Track the status of your booker profile application
						</p>
					</CardHeader>

					<CardContent className="space-y-8">
						{error && (
							<Alert variant="destructive">
								<AlertDescription>{error}</AlertDescription>
							</Alert>
						)}

						{bookerProfile && (
							<div className={`${statusInfo.bgColor} ${statusInfo.borderColor} border rounded-lg p-6`}>
								<div className="flex items-start gap-4">
									<StatusIcon className={`w-8 h-8 ${statusInfo.color} mt-1`} />
									<div className="flex-1">
										<h3 className={`text-xl font-semibold ${statusInfo.color} mb-2`}>
											{statusInfo.title}
										</h3>
										<p className="text-gray-700 mb-4">
											{statusInfo.description}
										</p>
										
										{statusInfo.status === "approved" && (
											<div className="space-y-3">
												<p className="text-sm text-gray-600">
													Verified by DUA on: {userRootAccount.updatedAt ?
														new Date(userRootAccount.updatedAt.seconds * 1000).toLocaleDateString() :
														"Recently"
													}
												</p>
												<Button
													asChild
													className="bg-green-600 hover:bg-green-700"
												>
													<Link href={`/booker/${userId}`}>
														Go to Your Profile
													</Link>
												</Button>
											</div>
										)}

										{statusInfo.status === "pending" && (
											<div className="space-y-3">
												<p className="text-sm text-gray-600">
													Submitted on: {new Date(bookerProfile.createdAt.seconds * 1000).toLocaleDateString()}
												</p>
												<Button 
													onClick={handleRefresh}
													disabled={refreshing}
													variant="outline"
													className="flex items-center gap-2"
												>
													<RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
													{refreshing ? 'Checking...' : 'Check Status'}
												</Button>
											</div>
										)}
									</div>
								</div>
							</div>
						)}
						{bookerProfile && (
							<div className="bg-white border border-gray-200 rounded-lg p-6">
								<h3 className="text-lg font-semibold text-gray-900 mb-4">
									Profile Information
								</h3>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
									<div>
										<span className="font-medium text-gray-600">Display Name:</span>
										<p className="text-gray-900">{bookerProfile.displayName}</p>
									</div>
									<div>
										<span className="font-medium text-gray-600">Email:</span>
										<p className="text-gray-900">{bookerProfile.email}</p>
									</div>
									<div>
										<span className="font-medium text-gray-600">Profile ID:</span>
										<p className="text-gray-900 font-mono text-xs">{bookerProfile.id}</p>
									</div>
									<div>
										<span className="font-medium text-gray-600">Status:</span>
										<p className={`${statusInfo.color} font-medium`}>
											{statusInfo.status === "approved" ? "Verified by DUA" :
											 statusInfo.status === "pending" ? "Under Review" : "Unknown"}
										</p>
									</div>
									<div>
										<span className="font-medium text-gray-600">Verified by DUA:</span>
										<p className={`${userRootAccount?.verifiedByDua ? 'text-green-600' : 'text-yellow-600'} font-medium`}>
											{userRootAccount?.verifiedByDua ? "Yes" : "Pending"}
										</p>
									</div>
								</div>
							</div>
						)}

						<div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
							<h3 className="text-lg font-semibold text-blue-900 mb-2">
								What happens next?
							</h3>
							<ul className="text-sm text-blue-800 space-y-1">
								{statusInfo.status === "pending" ? (
									<>
										<li>• Our admin team will review your profile within 1-2 business days</li>
										<li>• You'll receive an email notification once your profile is approved</li>
										<li>• After approval, you can access all booker features</li>
										<li>• You can search for creative talent and create projects</li>
									</>
								) : statusInfo.status === "approved" ? (
									<>
										<li>• Start searching for creative talent using the search feature</li>
										<li>• Create your first project and send offers to creatives</li>
										<li>• Manage your budget and track project progress</li>
										<li>• Build your network of creative collaborators</li>
									</>
								) : (
									<>
										<li>• Please contact support if you continue to see this status</li>
										<li>• Check your email for any communication from our team</li>
									</>
								)}
							</ul>
						</div>

						<div className="text-center">
							<p className="text-sm text-gray-500">
								Need help? Contact our support team at{" "}
								<a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700">
									<EMAIL>
								</a>
							</p>
						</div>
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
