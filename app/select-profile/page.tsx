"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { db } from "@/src/components/FirebaseComponents";
import { FirebaseCollections } from "@/src/constants/firebaseCollections";
import type { UserRootAccount } from "@/types/collections/userRootAccountInterfaces";
import { DocumentData, doc, getDoc } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useUser } from "reactfire";

// Helper function to fetch profile names
const fetchProfileName = async (
	collection: string,
	id: string,
): Promise<string> => {
	try {
		const docRef = doc(db, collection, id);
		const docSnap = await getDoc(docRef);
		if (docSnap.exists()) {
			return (
				docSnap.data().displayName || `Unnamed Profile (${id.substring(0, 5)})`
			);
		}
		return `Unknown Profile (${id.substring(0, 5)})`;
	} catch (error) {
		console.error(`Error fetching name for ${id} in ${collection}:`, error);
		return `Error (${id.substring(0, 5)})`;
	}
};

export default function SelectProfilePage() {
	const { status, data: user } = useUser();
	const router = useRouter();
	const [profiles, setProfiles] = useState<
		{ id: string; name: string; type: "creative" | "booker" }[]
	>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [userRootAccount, setUserRootAccount] = useState<UserRootAccount | null>(null);

	useEffect(() => {
		if (status === "success" && user) {
			const fetchProfiles = async () => {
				setIsLoading(true);
				const userRootRef = doc(
					db,
					FirebaseCollections.USER_ROOT_ACCOUNT,
					user.uid,
				);
				const userRootSnap = await getDoc(userRootRef);

				if (userRootSnap.exists()) {
					const data = userRootSnap.data() as UserRootAccount;
					setUserRootAccount(data);

					const creativeProfiles = data.creatives
						? Object.keys(data.creatives)
						: [];
					const bookerProfiles = data.bookers ? Object.keys(data.bookers) : [];

					const creativePromises = creativeProfiles.map(async (id) => ({
						id,
						name: await fetchProfileName(FirebaseCollections.CREATIVES, id),
						type: "creative" as const,
					}));

					const bookerPromises = bookerProfiles.map(async (id) => ({
						id,
						name: await fetchProfileName(FirebaseCollections.BOOKERS, id),
						type: "booker" as const,
					}));

					const allProfiles = await Promise.all([
						...creativePromises,
						...bookerPromises,
					]);
					setProfiles(allProfiles);
				}
				setIsLoading(false);
			};
			fetchProfiles();
		} else if (status === "error" || (status === "success" && !user)) {
			// If there's no user, redirect to login
			router.push("/landing-page");
		}
	}, [user, status, router]);

	if (isLoading || status === "loading") {
		return (
			<div className="min-h-screen flex items-center justify-center">
				Loading profiles...
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
			<Card className="w-full max-w-md">
				<CardHeader>
					<CardTitle className="text-2xl font-bold text-center">
						Select a Profile
					</CardTitle>
					<CardDescription className="text-center">
						You have access to the following profiles. Choose one to continue.
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-4">
					{profiles.map((profile) => {
						const isVerifiedByDua = userRootAccount?.verifiedByDua === true;
						const targetUrl = isVerifiedByDua
							? `/${profile.type}/${user?.uid}`
							: `/${profile.type}/${user?.uid}/under-review`;

						return (
							<Button
								key={profile.id}
								variant="outline"
								className="w-full justify-start h-14"
								onClick={() => router.push(targetUrl)}
							>
								<div className="flex flex-col items-start">
									<span className="font-semibold">{profile.name}</span>
									<div className="flex items-center gap-2">
										<span className="text-sm text-gray-500 capitalize">
											{profile.type} Account
										</span>
										{!isVerifiedByDua && (
											<span className="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded">
												Under Review
											</span>
										)}
										{isVerifiedByDua && (
											<span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
												Verified
											</span>
										)}
									</div>
								</div>
							</Button>
						);
					})}
					{profiles.length === 0 && (
						<p className="text-center text-gray-500">
							You do not have access to any profiles.
						</p>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
