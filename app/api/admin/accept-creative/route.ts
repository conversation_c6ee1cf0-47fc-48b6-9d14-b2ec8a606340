import { NextRequest, NextResponse } from 'next/server';
import { callFirebaseFunction } from '@/src/lib/firebaseFunctions';

interface AcceptCreativeRequest {
	creative_id: string;
	user_id: string;
}

interface AcceptCreativeResponse {
	success: boolean;
	message: string;
}

export async function POST(request: NextRequest) {
	try {
		const body: AcceptCreativeRequest = await request.json();
		
		// Validate required fields
		if (!body.creative_id) {
			return NextResponse.json(
				{ success: false, message: 'creative_id is required' },
				{ status: 400 }
			);
		}

		// Call the Firebase Cloud Function
		const result = await callFirebaseFunction<
			{ creative_id: string },
			AcceptCreativeResponse
		>('accept_creative', {
			creative_id: body.creative_id,
		});

		return NextResponse.json({
			success: true,
			message: result.message || 'Creative profile approved successfully',
		});

	} catch (error) {
		console.error('Error calling accept_creative function:', error);
		
		// Handle Firebase function errors
		if (error instanceof Error) {
			return NextResponse.json(
				{ success: false, message: error.message },
				{ status: 500 }
			);
		}

		return NextResponse.json(
			{ success: false, message: 'Failed to approve creative profile' },
			{ status: 500 }
		);
	}
}
