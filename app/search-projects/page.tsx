"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, Menu, Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useMemo, useState } from "react";
import type { DateRange } from "react-day-picker";
import { useDashboardNavigation } from "@/components/hooks/use-dashboard-navigation";

interface Project {
	id: string;
	title: string;
	description: string;
	projectType: string;
	location: string;
	coordinates: {
		lat: number;
		lng: number;
	};
	startDate: string; // YYYY-MM-DD
	endDate: string; // YYYY-MM-DD
	projectImageUrl: string;
	clientOrBrandName?: string; // Optional
	budgetOrCompensation?: string; // Optional
}

const projectTypes = [
	"FASHION EDITORIAL",
	"COMMERCIAL AD CAMPAIGN",
	"MUSIC VIDEO PRODUCTION",
	"DOCUMENTARY FILM",
	"EVENT PHOTOGRAPHY/VIDEOGRAPHY",
	"BRAND COLLABORATION",
	"PRODUCT SHOOT",
	"CORPORATE VIDEO",
];

const mockProjects: Project[] = [
	{
		id: "proj1",
		title: "Urban Dreams Fashion Editorial",
		description:
			"A cutting-edge fashion shoot exploring modern urban aesthetics. Looking for a dynamic team.",
		projectType: "FASHION EDITORIAL",
		location: "New York, NY",
		coordinates: { lat: 40.7128, lng: -74.006 },
		startDate: "2024-07-10",
		endDate: "2024-07-12",
		projectImageUrl: "/assets/mood_boards/mb1.png",
		clientOrBrandName: "Vogue Magazine",
		budgetOrCompensation: "$5,000 - $8,000",
	},
	{
		id: "proj2",
		title: "EcoGadget Launch Campaign",
		description:
			"Commercial ad campaign for a new sustainable tech gadget. Seeking videographer and models.",
		projectType: "COMMERCIAL AD CAMPAIGN",
		location: "San Francisco, CA",
		coordinates: { lat: 37.7749, lng: -122.4194 },
		startDate: "2024-08-01",
		endDate: "2024-08-05",
		projectImageUrl: "/assets/mood_boards/mb2.png",
		clientOrBrandName: "GreenTech Inc.",
		budgetOrCompensation: "Est. $15,000",
	},
	{
		id: "proj3",
		title: "Indie Artist Music Video",
		description:
			"Creative music video for an up-and-coming indie pop creative. Theme: Surreal Nostalgia.",
		projectType: "MUSIC VIDEO PRODUCTION",
		location: "Los Angeles, CA",
		coordinates: { lat: 34.0522, lng: -118.2437 },
		startDate: "2024-07-20",
		endDate: "2024-07-22",
		projectImageUrl: "/assets/mood_boards/mb3.png",
		clientOrBrandName: "Luna Bloom (Artist)",
		budgetOrCompensation: "Profit Share + Expenses",
	},
	{
		id: "proj4",
		title: "Wildlife Conservation Documentary",
		description:
			"Feature documentary on local wildlife conservation efforts. Requires experienced wildlife photographer.",
		projectType: "DOCUMENTARY FILM",
		location: "Denver, CO",
		coordinates: { lat: 39.7392, lng: -104.9903 },
		startDate: "2024-09-01",
		endDate: "2024-10-15",
		projectImageUrl: "/assets/mood_boards/mb4.png",
		clientOrBrandName: "Nature's Voice Foundation",
		budgetOrCompensation: "Grant Funded",
	},
	{
		id: "proj5",
		title: "Tech Conference Highlights",
		description:
			"Event photography and videography for a major tech conference. Fast turnaround needed.",
		projectType: "EVENT PHOTOGRAPHY/VIDEOGRAPHY",
		location: "Austin, TX",
		coordinates: { lat: 30.2672, lng: -97.7431 },
		startDate: "2024-07-25",
		endDate: "2024-07-27",
		projectImageUrl: "/assets/mood_boards/mb5.png",
		clientOrBrandName: "Innovate Summit",
		budgetOrCompensation: "$3,000",
	},
	{
		id: "proj6",
		title: "Summer Collection Brand Collab",
		description:
			"Lifestyle brand looking for models and a photographer for their summer collection launch.",
		projectType: "BRAND COLLABORATION",
		location: "Miami, FL",
		coordinates: { lat: 25.7617, lng: -80.1918 },
		startDate: "2024-07-01",
		endDate: "2024-07-05",
		projectImageUrl: "/assets/mood_boards/mb6.png",
		clientOrBrandName: "SunKissed Apparel",
		budgetOrCompensation: "Product + $1,500",
	},
	{
		id: "proj7",
		title: "Artisanal Coffee Product Shoot",
		description:
			"High-quality product photography for a new line of artisanal coffee beans.",
		projectType: "PRODUCT SHOOT",
		location: "Portland, OR",
		coordinates: { lat: 45.5051, lng: -122.675 },
		startDate: "2024-08-10",
		endDate: "2024-08-11",
		projectImageUrl: "/assets/mood_boards/mb7.png",
		clientOrBrandName: "Roast & Co.",
		budgetOrCompensation: "$1,200",
	},
	{
		id: "proj8",
		title: "Startup Success Story Video",
		description:
			"Corporate video showcasing the journey and success of a local tech startup.",
		projectType: "CORPORATE VIDEO",
		location: "Seattle, WA",
		coordinates: { lat: 47.6062, lng: -122.3321 },
		startDate: "2024-09-15",
		endDate: "2024-09-18",
		projectImageUrl: "/assets/mood_boards/mb8.png",
		clientOrBrandName: "Innovate Solutions Ltd.",
		budgetOrCompensation: "$4,500",
	},
];

interface DisplayProject extends Project {
	display: boolean;
	colSpan: number;
	rowSpan: number;
	isPrimaryOfMerge?: boolean;
	constituentIds: string[];
	originalIndex: number; // To determine left-most/top-most
}

// Component for dynamic dashboard link
function DashboardLink() {
	const { dashboardUrl } = useDashboardNavigation();

	if (!dashboardUrl) {
		return (
			<span className="text-sm font-medium text-gray-400">
				Dashboard
			</span>
		);
	}

	return (
		<Link
			href={dashboardUrl}
			className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
		>
			Dashboard
		</Link>
	);
}

export default function SearchProjects() {
	const [dateRange, setDateRange] = useState<DateRange | undefined>();
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedProjectType, setSelectedProjectType] = useState<string>("");
	const [locationSearch, setLocationSearch] = useState("");

	const [isEditMode, setIsEditMode] = useState(false);
	const [selectedProjectIds, setSelectedProjectIds] = useState<string[]>([]);

	// This will hold the actual layout configuration, including merged tiles.
	// For now, it's initialized from filteredProjects but will be modified by merge/unmerge actions.
	const [projectDisplayConfig, setProjectDisplayConfig] = useState<
		DisplayProject[]
	>([]);

	// Initial filtering logic (as before)
	const baseFilteredProjects = useMemo(
		() =>
			mockProjects.filter((project) => {
				const matchesSearch =
					searchTerm === "" ||
					project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
					project.description
						.toLowerCase()
						.includes(searchTerm.toLowerCase()) ||
					project.location.toLowerCase().includes(searchTerm.toLowerCase());

				const matchesProjectType =
					selectedProjectType === "" ||
					selectedProjectType === "all" ||
					project.projectType === selectedProjectType;

				const matchesDateRange = () => {
					if (!dateRange || (!dateRange.from && !dateRange.to)) return true;
					if (dateRange.from && !dateRange.to) {
						return new Date(project.endDate) >= dateRange.from;
					}
					if (!dateRange.from && dateRange.to) {
						return new Date(project.startDate) <= dateRange.to;
					}
					if (dateRange.from && dateRange.to) {
						const projectStart = new Date(project.startDate);
						const projectEnd = new Date(project.endDate);
						return projectStart <= dateRange.to && projectEnd >= dateRange.from;
					}
					return true;
				};
				return matchesSearch && matchesProjectType && matchesDateRange();
			}),
		[mockProjects, searchTerm, selectedProjectType, dateRange],
	);

	// Initialize/reset projectDisplayConfig
	useMemo(() => {
		const initialDisplayConfig: DisplayProject[] = baseFilteredProjects.map(
			(p, index) => ({
				...p,
				display: true,
				colSpan: 1,
				rowSpan: 1,
				isPrimaryOfMerge: false,
				constituentIds: [p.id],
				originalIndex: index,
			}),
		);
		setProjectDisplayConfig(initialDisplayConfig);
		setSelectedProjectIds([]);
	}, [baseFilteredProjects]);

	const handleTileSelect = (projectId: string) => {
		if (!isEditMode) return;
		setSelectedProjectIds((prevSelected) =>
			prevSelected.includes(projectId)
				? prevSelected.filter((id) => id !== projectId)
				: [...prevSelected, projectId],
		);
	};

	const performMerge = (mergeType: "horizontal" | "vertical") => {
		if (selectedProjectIds.length !== 2) {
			alert("Please select exactly two tiles to merge.");
			return;
		}

		const [id1, id2] = selectedProjectIds;
		const project1 = projectDisplayConfig.find((p) => p.id === id1);
		const project2 = projectDisplayConfig.find((p) => p.id === id2);

		if (!project1 || !project2) {
			console.error("Selected projects not found in display config.");
			return;
		}

		const primary =
			project1.originalIndex < project2.originalIndex ? project1 : project2;
		const secondary =
			project1.originalIndex < project2.originalIndex ? project2 : project1;

		if (
			primary.constituentIds.some((id) => secondary.constituentIds.includes(id))
		) {
			alert(
				"Cannot merge tiles that are already part of the same group or a tile with itself.",
			);
			setSelectedProjectIds([]);
			return;
		}

		setProjectDisplayConfig((prevConfig) => {
			return prevConfig.map((p) => {
				if (p.id === primary.id) {
					return {
						...p,
						colSpan:
							mergeType === "horizontal"
								? primary.colSpan + secondary.colSpan
								: primary.colSpan,
						rowSpan:
							mergeType === "vertical"
								? primary.rowSpan + secondary.rowSpan
								: primary.rowSpan,
						isPrimaryOfMerge: true,
						constituentIds: Array.from(
							new Set([...primary.constituentIds, ...secondary.constituentIds]),
						),
						display: true,
					};
				}
				if (p.id === secondary.id) {
					return { ...p, display: false };
				}
				return p;
			});
		});

		setSelectedProjectIds([]);
		console.log(
			`${mergeType} merge: Primary: ${primary.id}, Secondary: ${secondary.id}`,
		);
	};

	const handleUnmerge = (primaryProjectIdToUnmerge: string) => {
		setProjectDisplayConfig((prevConfig) => {
			const mergedGroup = prevConfig.find(
				(p) => p.id === primaryProjectIdToUnmerge && p.isPrimaryOfMerge,
			);
			if (!mergedGroup || !mergedGroup.constituentIds) return prevConfig;

			const idsToRestore = mergedGroup.constituentIds;

			return prevConfig.map((p) => {
				if (idsToRestore.includes(p.id)) {
					const originalProject = baseFilteredProjects.find(
						(op) => op.id === p.id,
					);
					return {
						...(originalProject || p),
						id: p.id,
						display: true,
						colSpan: 1,
						rowSpan: 1,
						isPrimaryOfMerge: false,
						constituentIds: [p.id],
						originalIndex: p.originalIndex,
					};
				}
				return p;
			});
		});
		setSelectedProjectIds([]);
		console.log(`Unmerged group associated with: ${primaryProjectIdToUnmerge}`);
	};

	return (
		<div className="min-h-screen bg-background flex flex-col">
			{/* Header Section */}
			<header className="bg-white border-b border-gray-200 sticky top-0 z-40">
				<div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex items-center justify-between h-16">
						{/* Left side: Title */}
						<div className="flex items-center">
							<Link
								href="/"
								className="text-2xl font-light tracking-wide text-gray-800"
							>
								DUA
							</Link>
						</div>

						{/* Right side: Dashboard Link */}
						<div className="flex items-center">
							<DashboardLink />
							{/* You might want to add other icons or profile indicators here in a real scenario */}
						</div>
					</div>
				</div>
			</header>

			{/* Main Content Area with Filters and Grid */}
			<main className="flex-1 overflow-y-auto p-6">
				<div className="max-w-screen-xl mx-auto">
					{/* Filter Section */}
					<div className="mb-8 p-6 bg-white rounded-lg shadow">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-end">
							{/* Search Input */}
							<Input
								placeholder="Search by title, desc, or location"
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="border-gray-200 bg-white font-light"
							/>

							<Select
								value={selectedProjectType}
								onValueChange={setSelectedProjectType}
							>
								<SelectTrigger className="border-gray-200 bg-white font-light">
									<SelectValue placeholder="All Project Types" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Project Types</SelectItem>
									{projectTypes.map((type) => (
										<SelectItem key={type} value={type} className="font-light">
											{type}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Input
								placeholder="Location (e.g., City, State)"
								value={locationSearch}
								onChange={(e) => setLocationSearch(e.target.value)}
								className="border-gray-200 bg-white font-light"
							/>

							<Popover>
								<PopoverTrigger asChild>
									<Button
										variant="outline"
										className="justify-start text-left font-light border-gray-200 bg-white w-full"
									>
										<CalendarIcon className="mr-2 h-4 w-4" />
										{dateRange?.from ? (
											dateRange.to ? (
												<>
													{format(dateRange.from, "LLL dd, y")} -{" "}
													{format(dateRange.to, "LLL dd, y")}
												</>
											) : (
												format(dateRange.from, "LLL dd, y")
											)
										) : (
											<span>Select date range</span>
										)}
									</Button>
								</PopoverTrigger>
								<PopoverContent className="w-auto p-0" align="start">
									<Calendar
										initialFocus
										mode="range"
										defaultMonth={dateRange?.from}
										selected={dateRange}
										onSelect={setDateRange}
										numberOfMonths={2}
									/>
								</PopoverContent>
							</Popover>
						</div>

						{/* Moved Edit Mode Buttons Here */}
						<div className="flex justify-end space-x-2 mt-6">
							<Button
								variant={isEditMode ? "destructive" : "outline"}
								onClick={() => setIsEditMode(!isEditMode)}
							>
								{isEditMode ? "Exit Edit Mode" : "Enter Edit Mode"}
							</Button>
							{isEditMode && selectedProjectIds.length === 2 && (
								<>
									<Button onClick={() => performMerge("horizontal")} size="sm">
										Merge Horizontally
									</Button>
									<Button onClick={() => performMerge("vertical")} size="sm">
										Merge Vertically
									</Button>
								</>
							)}
						</div>
					</div>

					{/* Results Grid */}
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
						{projectDisplayConfig
							.filter((p) => p.display)
							.map((project) => {
								return (
									<div
										key={project.id}
										className={cn(
											"group",
											isEditMode ? "cursor-pointer" : "",
											isEditMode &&
												selectedProjectIds.includes(project.id) &&
												"ring-2 ring-blue-500 ring-offset-2 shadow-lg",
										)}
										onClick={() => handleTileSelect(project.id)}
										style={{
											gridColumn: `span ${project.colSpan}`,
											gridRow: `span ${project.rowSpan}`,
											minWidth: 0,
											overflow: "hidden",
											position: "relative",
										}}
									>
										<Link
											href={`/booker/project/${project.id}`}
											onClick={(e) => {
												if (isEditMode) e.preventDefault();
											}}
											className="block h-full w-full"
											style={{ minWidth: 0 }}
										>
											<div
												className="relative mb-4 h-full flex flex-col w-full"
												style={{ minWidth: 0 }}
											>
												<div
													className="relative w-full"
													style={{ paddingBottom: "75%", overflow: "hidden" }}
												>
													<Image
														src={project.projectImageUrl || "/placeholder.svg"}
														alt={project.title}
														fill
														className="object-cover absolute top-0 left-0 w-full h-full transition-transform duration-500 group-hover:scale-105"
													/>
												</div>
												<div
													className="p-2 space-y-1 flex-grow w-full"
													style={{ minWidth: 0, overflow: "hidden" }}
												>
													<div className="text-xs text-gray-400 font-light tracking-wide w-full">
														{project.projectType}
													</div>
													<h3
														className="font-light text-black tracking-wide break-words w-full"
														style={{
															overflow: "hidden",
															textOverflow: "ellipsis",
															whiteSpace: "normal",
														}}
													>
														{project.title}
													</h3>
													<p className="text-xs text-gray-600 font-light h-10 break-words w-full">
														{project.description}
													</p>
													<div className="text-xs text-gray-500 font-light w-full">
														{project.location}
													</div>
													<div className="text-xs text-gray-500 font-light w-full">
														{format(
															new Date(project.startDate),
															"MMM dd, yyyy",
														)}{" "}
														-{" "}
														{format(new Date(project.endDate), "MMM dd, yyyy")}
													</div>
												</div>
											</div>
										</Link>
										{isEditMode && project.isPrimaryOfMerge && (
											<Button
												variant="destructive"
												size="sm"
												className="absolute top-1 right-1 z-10 opacity-80 hover:opacity-100 p-1 h-auto leading-none"
												onClick={(e) => {
													e.stopPropagation();
													handleUnmerge(project.id);
												}}
											>
												Unmerge
											</Button>
										)}
									</div>
								);
							})}
					</div>

					{projectDisplayConfig.filter((p) => p.display).length === 0 && (
						<div className="text-center py-24">
							<h3 className="text-xl font-light text-gray-900 mb-2">
								No projects found
							</h3>
							<p className="text-gray-500 font-light">
								Try adjusting your search criteria
							</p>
						</div>
					)}
				</div>
			</main>
		</div>
	);
}
