/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
/**
 * Enhanced response model with standardized structure
 */
export interface GacoResponse {
  success: boolean;
  message: string;
  data?: {
    [k: string]: unknown;
  } | null;
  code: number;
  errors?:
    | {
        [k: string]: unknown;
      }[]
    | null;
  meta?: {
    [k: string]: unknown;
  } | null;
}
