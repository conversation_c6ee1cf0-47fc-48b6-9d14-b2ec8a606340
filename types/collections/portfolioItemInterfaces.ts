/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type PortfolioItemType = "image" | "text" | "iframe";
export type PortfolioItemSize = "standard" | "horizontal" | "vertical" | "large";

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface CreatePortfolioItemFullRequest {
  creativeId: string;
  data: PortfolioItemCreateRequest;
}
export interface PortfolioItemCreateRequest {
  type: PortfolioItemType;
  position: number;
  size: PortfolioItemSize;
  imageUrl?: string | null;
  iframeUrl?: string | null;
  alt?: string | null;
  textContent?: string | null;
  backgroundColor?: string | null;
  fontSize?: string | null;
  fontFamily?: string | null;
  isMerged?: boolean | null;
  originalItems?: string[] | null;
}
export interface DeletePortfolioItemFullRequest {
  creativeId: string;
  itemId: string;
}
export interface GetPortfolioItemDetailsFullRequest {
  creativeId: string;
  itemId: string;
}
export interface GetPortfolioItemsFullRequest {
  creativeId: string;
}
export interface PortfolioItemBase {
  creativeId: string;
  type?: PortfolioItemType | null;
  position?: number | null;
  imageUrl?: string | null;
  alt?: string | null;
  textContent?: string | null;
  backgroundColor?: string | null;
  fontSize?: string | null;
  fontFamily?: string | null;
  iframeUrl?: string | null;
  size?: PortfolioItemSize | null;
  isMerged?: boolean | null;
  originalItems?: string[] | null;
  createdAt: Date;
  updatedAt: Date;
}
export interface PortfolioItemOrderData {
  itemId: string;
  position: number;
}
export interface PortfolioItemResponse {
  creativeId: string;
  type?: PortfolioItemType | null;
  position?: number | null;
  imageUrl?: string | null;
  alt?: string | null;
  textContent?: string | null;
  backgroundColor?: string | null;
  fontSize?: string | null;
  fontFamily?: string | null;
  iframeUrl?: string | null;
  size?: PortfolioItemSize | null;
  isMerged?: boolean | null;
  originalItems?: string[] | null;
  createdAt: Date;
  updatedAt: Date;
  id: string;
}
export interface PortfolioItemUpdateRequest {
  type?: PortfolioItemType | null;
  position?: number | null;
  imageUrl?: string | null;
  iframeUrl?: string | null;
  alt?: string | null;
  textContent?: string | null;
  backgroundColor?: string | null;
  fontSize?: string | null;
  fontFamily?: string | null;
  size?: PortfolioItemSize | null;
  isMerged?: boolean | null;
  originalItems?: string[] | null;
}
export interface PortfolioItemsOrderUpdateRequest {
  orderedItemIds: string[];
}
export interface UpdatePortfolioItemFullRequest {
  creativeId: string;
  itemId: string;
  data: PortfolioItemUpdateRequest;
}
export interface UpdatePortfolioItemsOrderFullRequest {
  creativeId: string;
  data: PortfolioItemsOrderUpdateRequest;
}
