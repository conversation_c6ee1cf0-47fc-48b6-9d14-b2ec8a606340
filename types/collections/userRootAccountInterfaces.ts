/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface UserRootAccount {
  createdAt: Date;
  email: string;
  bookers: {
    [k: string]: string;
  };
  creatives: {
    [k: string]: string;
  };
  verifiedByDua: boolean;
  admin: boolean;
  acceptedTAndC: boolean;
  updatedAt?: Date | null;
}
