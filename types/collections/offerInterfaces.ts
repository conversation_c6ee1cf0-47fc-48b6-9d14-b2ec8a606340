/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type JobCompletionStatus = "pendingConfirmation" | "completed" | "cancelled" | "noShow";
export type PayoutStatus =
  | "pending"
  | "awaitingPayout"
  | "confirmed"
  | "paid"
  | "rejected"
  | "cancelled"
  | "investigating";
export type CreativeType = "photographer" | "videographer" | "model" | "makeupArtist" | "hairStylist" | "other";
export type RateType = "daily" | "hourly";

export interface ApprovalStep {
  role: string;
  approverId?: string | null;
  status?: string;
  comments?: string | null;
  timestamp?: Date | null;
}
/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface CommissionValidatorMixin {}
export interface JobCompletionStep {
  role?: string | null;
  approverId?: string | null;
  status?: JobCompletionStatus;
  comments?: string | null;
  timestamp?: Date | null;
}
export interface NegotiationChange {
  userId: string;
  role: string;
  timestamp: Date;
  action: string;
  changes?: {
    [k: string]: unknown;
  } | null;
  comments?: string | null;
}
export interface Offer {
  bookerId: string;
  creativeId: string;
  status?: string;
  projectId: string;
  offerStartDate?: Date | null;
  offerEndDate?: Date | null;
  version?: string;
  approvalWorkflow?: ApprovalStep[];
  jobCompletionWorkflow?: {
    [k: string]: JobCompletionStep;
  };
  payoutStatus?: PayoutStatus | null;
  creativeType: CreativeType;
  rateType: RateType;
  currency?: string;
  invoiceId?: string | null;
  escrowEnabled?: boolean;
  negotiationHistory?: NegotiationChange[] | null;
  createdBy: string;
  createdByRole?: string | null;
  createdAt: Date;
  updatedBy?: string | null;
  updatedAt?: Date | null;
}
