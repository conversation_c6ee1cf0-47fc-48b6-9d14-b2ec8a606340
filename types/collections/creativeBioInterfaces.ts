/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type CreativeType = "photographer" | "videographer" | "model" | "makeupArtist" | "hairStylist" | "other";
export type RateType = "daily" | "hourly";

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface Creative {
  displayName: string;
  story: string;
  instagram: string;
  phoneNumber: string;
  creativeType: CreativeType[];
  updatedAt?: Date;
  website?: string | null;
  parentId: string;
}
export interface CreativeBioPreVerification {
  displayName: string;
  story: string;
  instagram: string;
  phoneNumber: string;
  creativeType: CreativeType[];
  updatedAt?: Date;
  website?: string | null;
}
export interface CreativePrice {
  creativeId: string;
  creativeType: CreativeType;
  price: number;
  currency?: string;
  rateType: RateType;
  updatedAt?: Date;
}
export interface DiscountPrice {
  creativeId: string;
  rateType: RateType;
  aboveRate: number;
  discountPercentage: number;
  creativeType: CreativeType;
  updatedAt?: Date;
}
export interface PayoutAccount {
  paymentReferenceId: string;
  accountHolderName: string;
  email: string;
  phoneNumber?: string | null;
  address: Address;
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  accountType: string;
  bankName: string;
  bankCountry: string;
  currency: string;
  bankAccountNumber: string;
  iban?: string | null;
  routingNumber?: string | null;
  sortCode?: string | null;
  bic?: string | null;
  taxResidenceCountry?: string | null;
  payoutMethod?: string;
}
export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
  [k: string]: unknown;
}
