/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Represents the list of unavailable time slots for a single date.
 */
export interface AvailabilityForDate {
  date: string;
  unavailableSlots: TimeSlot[];
}
/**
 * Represents a time slot with a start and end time.
 */
export interface TimeSlot {
  startTime?: Date;
  endTime?: Date;
  jobId?: string | null;
}
/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
/**
 * Request model for retrieving unavailable slots for a creative over a date range.
 */
export interface GetCreativeAvailabilityRequest {
  creativeId: string;
  startDate: string;
  endDate: string;
}
/**
 * Response model for creative availability, containing a list of dates and their unavailable slots.
 */
export interface GetCreativeAvailabilityResponse {
  availability: AvailabilityForDate[];
}
/**
 * Request model for setting the unavailable time slots for a specific date.
 */
export interface SetCreativeAvailabilityRequest {
  date: string;
  slots: TimeSlot[];
}
