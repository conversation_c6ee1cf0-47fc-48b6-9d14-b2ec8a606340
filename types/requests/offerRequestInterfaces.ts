/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * The status of the job completion.
 */
export type JobCompletionStatus = "pendingConfirmation" | "completed" | "cancelled" | "noShow";
/**
 * The role of the entity either 'booker' or 'creative'
 */
export type EntityType = "creative" | "booker" | "system";
/**
 * The type of the creative receiving the offer.
 */
export type CreativeType = "photographer" | "videographer" | "model" | "makeupArtist" | "hairStylist" | "other";
/**
 * The rate type of the creative receiving the offer.
 */
export type RateType = "daily" | "hourly";

/**
 * Request model to activate an approved offer.
 */
export interface ActivateOfferRequest {
  /**
   * The ID of the offer to activate.
   */
  offerId: string;
}
export interface ApproveOfferRequest {
  offerId: string;
  role: string;
  comments?: string | null;
}
/**
 * Request model for a booker to respond to a creative's negotiation.
 */
export interface BookerRespondToNegotiationRequest {
  /**
   * The ID of the offer.
   */
  offerId: string;
  /**
   * <PERSON>'s action (APPROVED, REJECTED).
   */
  action: string;
  /**
   * Optional comments from the booker.
   */
  comments?: string | null;
}
/**
 * Request model to cancel an offer.
 */
export interface CancelOfferRequest {
  /**
   * The ID of the offer to cancel.
   */
  offerId: string;
  /**
   * Reason for cancellation.
   */
  reason?: string | null;
}
export interface CommissionValidatorMixin {}
/**
 * Request model to mark an offer as completed.
 */
export interface CompleteOfferRequest {
  /**
   * The ID of the offer to complete.
   */
  offerId: string;
  jobCompletionStatus: JobCompletionStatus;
  /**
   * The ID of either booker or creative
   */
  entityId: string;
  entityRole: EntityType;
  /**
   * Optional comments from the entity completing the job.
   */
  comments?: string | null;
}
/**
 * Request model for creating a new offer from a Booker to a Creative.
 */
export interface CreateOfferRequest {
  /**
   * The ID of the Booker sending the offer.
   */
  bookerId: string;
  /**
   * The ID of the Creative receiving the offer.
   */
  creativeId: string;
  /**
   * The ID of the project to which the offer belongs.
   */
  projectId: string;
  creativeType: CreativeType;
  rateType: RateType;
  /**
   * The start date of the offer.
   */
  offerStartDate?: Date;
  /**
   * The end date of the offer.
   */
  offerEndDate?: Date;
}
/**
 * Request model for a creative to respond to an offer.
 */
export interface CreativeRespondToOfferRequest {
  /**
   * The ID of the offer.
   */
  offerId: string;
  /**
   * Creative's action (APPROVED, REJECTED, NEGOTIATING).
   */
  action: string;
  /**
   * Proposed changes if action is NEGOTIATING.
   */
  changes?: {
    [k: string]: unknown;
  } | null;
  /**
   * Optional comments from the creative.
   */
  comments?: string | null;
}
export interface DeleteOfferRequest {
  offerId: string;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
/**
 * Request model to get an offer by its ID.
 */
export interface GetOfferRequest {
  /**
   * The ID of the offer to retrieve.
   */
  offerId: string;
}
/**
 * Generic request for actions on an offer like approve, reject, cancel.
 * Specific actions will be handled by different endpoint methods using this.
 */
export interface OfferActionRequest {
  /**
   * The ID of the offer.
   */
  offerId: string;
  /**
   * Optional comments for the action.
   */
  comments?: string | null;
}
export interface RejectOfferRequest {
  /**
   * The ID of the offer to reject.
   */
  offerId: string;
  /**
   * Reason for rejection.
   */
  reason?: string | null;
}
export interface SubmitOfferForApprovalRequest {
  offerId: string;
  role: string;
}
/**
 * Request model to submit a draft offer to the creative.
 */
export interface SubmitOfferToCreativeRequest {
  /**
   * The ID of the offer to submit.
   */
  offerId: string;
}
export interface TerminateOfferRequest {
  offerId: string;
}
export interface UpdateDraftOfferRequest {
  offerId: string;
  projectId?: string | null;
  offerStartDate?: Date | null;
  offerEndDate?: Date | null;
}
/**
 * Request model for updating an existing offer.
 * Typically used when the offer is in DRAFT status or during negotiation.
 */
export interface UpdateOfferRequest {
  /**
   * The ID of the offer to update.
   */
  offerId: string;
  /**
   * Proposed start date for the project/assignment.
   */
  offerStartDate?: Date | null;
  /**
   * Proposed end date for the project/assignment.
   */
  offerEndDate?: Date | null;
}
