/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export interface BookerBioPreVerification {
  displayName: string;
  story: string;
  website: string;
  instagram: string;
  phoneNumber: string;
  updatedAt?: Date;
}
export interface CreateBookerRequest {
  displayName: string;
  story: string;
  website: string;
  instagram: string;
  phoneNumber: string;
  updatedAt?: Date;
}
export interface DeleteBookerRequest {
  bookerId: string;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
export interface SimplePayoutAccount {
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  swiftCode?: string | null;
  taxResidenceCountry: string;
  accountHolderName: string;
  bankName: string;
  iban: string;
  address: Address;
}
export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
  [k: string]: unknown;
}
export interface UpdateBookerPayoutAccountRequest {
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  swiftCode?: string | null;
  taxResidenceCountry: string;
  accountHolderName: string;
  bankName: string;
  iban: string;
  address: Address;
  bookerId: string;
}
