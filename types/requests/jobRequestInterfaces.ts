/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * The current status of the job.
 */
export type JobStatusEnum = "confirmed" | "completed" | "cancelled";
/**
 * The current status of the job.
 */
export type JobStatusEnum1 = "confirmed" | "completed" | "cancelled";

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface CompleteJobRequest {
  /**
   * The ID of the job to mark as complete.
   */
  jobId: string;
}
export interface ConfirmJobRequest {
  /**
   * The ID of the offer to confirm as a job.
   */
  offerId: string;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
export interface JobBase {
  /**
   * The ID of the offer that was converted to a job.
   */
  offerId: string;
  status?: JobStatusEnum;
  /**
   * The start time of the job.
   */
  startTime: Date;
  /**
   * The end time of the job.
   */
  endTime: Date;
  /**
   * The ID of the creative assigned to the job.
   */
  creativeId: string;
  createdAt: Date;
  updatedAt?: Date;
  updatedBy?: string | null;
}
export interface JobResponseData {
  /**
   * The ID of the offer that was converted to a job.
   */
  offerId: string;
  status?: JobStatusEnum1;
  /**
   * The start time of the job.
   */
  startTime: Date;
  /**
   * The end time of the job.
   */
  endTime: Date;
  /**
   * The ID of the creative assigned to the job.
   */
  creativeId: string;
  createdAt: Date;
  updatedAt?: Date;
  updatedBy?: string | null;
  /**
   * The unique identifier for the job.
   */
  jobId: string;
}
