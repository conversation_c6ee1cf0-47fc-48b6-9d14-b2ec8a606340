/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface Email {
  fromAlias: string;
  fromEmail: string;
  toAlias: string;
  toEmail: string;
  subject: string;
  text: string;
}
export interface EmailTemplate {
  template_display_name: string;
  store_id: string;
  subject: string;
  text: string;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
export interface SendAcceptedEmailFullRequest {
  templateId?: string;
  toAlias: string;
  toEmail: string;
  creativeId: string;
}
export interface SendAcceptedEmailRequest {
  templateId?: string;
  toAlias: string;
  toEmail: string;
}
export interface SendEmailRequest {
  email: Email;
  senderId: string;
  userId: string;
  recipientId: string;
}
export interface SendEmailTask {
  email_request_id: string;
}
export interface SendEmailTaskRequest {
  emailRequestId: string;
  emailRequest: SendEmailRequest;
}
export interface SendInviteEmailRequest {
  templateId?: string;
  storeId: string;
  producerId: string;
}
