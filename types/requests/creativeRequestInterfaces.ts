/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type CreativeType = "photographer" | "videographer" | "model" | "makeupArtist" | "hairStylist" | "other";
export type RateType = "daily" | "hourly";

export interface Address {
  streetNumber?: string | null;
  streetName: string;
  houseNumber?: string | null;
  additionalInfo?: string | null;
  zipCode: string;
  city: string;
  state?: string | null;
  country: string;
}
export interface CreateCreativeRequest {
  displayName: string;
  story: string;
  instagram: string;
  phoneNumber: string;
  creativeType: CreativeType[];
  updatedAt?: Date;
  website?: string | null;
}
export interface CreativeBioPreVerification {
  displayName: string;
  story: string;
  instagram: string;
  phoneNumber: string;
  creativeType: CreativeType[];
  updatedAt?: Date;
  website?: string | null;
}
export interface CreativePrice {
  creativeId: string;
  creativeType: CreativeType;
  price: number;
  currency?: string;
  rateType: RateType;
  updatedAt?: Date;
}
/**
 * Request model for deleting a creative.
 */
export interface DeleteCreativeRequest {
  /**
   * The ID of the creative to delete.
   */
  creativeId: string;
}
export interface DiscountPrice {
  creativeId: string;
  rateType: RateType;
  aboveRate: number;
  discountPercentage: number;
  creativeType: CreativeType;
  updatedAt?: Date;
}
/**
 * Base class for all request models
 */
export interface GacoRequest {}
export interface SetCreativeDiscountPriceRequest {
  creativeId: string;
  rateType: RateType;
  aboveRate: number;
  discountPercentage: number;
  creativeType: CreativeType;
  updatedAt?: Date;
}
export interface SetCreativePriceRequest {
  creativeId: string;
  creativeType: CreativeType;
  price: number;
  currency?: string;
  rateType: RateType;
  updatedAt?: Date;
}
export interface SimplePayoutAccount {
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  swiftCode?: string | null;
  taxResidenceCountry: string;
  accountHolderName: string;
  bankName: string;
  iban: string;
  address: Address;
}
export interface UpdateCreativePayoutAccountRequest {
  companyName?: string | null;
  companyNumber?: string | null;
  vatNumber?: string | null;
  swiftCode?: string | null;
  taxResidenceCountry: string;
  accountHolderName: string;
  bankName: string;
  iban: string;
  address: Address;
  creativeId: string;
}
