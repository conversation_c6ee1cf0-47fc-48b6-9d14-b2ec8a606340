/* tslint:disable */
/* eslint-disable */
/**
/* This file was automatically generated from pydantic models by running pydantic2ts.
/* Do not modify it by hand - just update the pydantic models and then re-run the script
*/

export type ProjectStatusEnum = "active" | "completed" | "planning";

/**
 * Base model with Gaco-specific configuration
 */
export interface BaseGacoModel {}
export interface CreateProjectRequest {
  bookerId: string;
  title: string;
  client?: string | null;
  budget: number;
  status: ProjectStatusEnum;
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  projectType: string;
  creativesNeeded: string[];
  moodBoardUrl?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  userId?: string | null;
}
export interface DeleteProjectRequest {
  projectId: string;
}
export interface ProjectBase {
  bookerId: string;
  title: string;
  client?: string | null;
  budget: number;
  status: ProjectStatusEnum;
  description: string;
  startDate: Date;
  endDate: Date;
  location: string;
  projectType: string;
  creativesNeeded: string[];
  moodBoardUrl?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  userId: string;
}
export interface ProjectResponseData {
  bookerId: string;
  title: string;
  client?: string | null;
  budget: number;
  status: ProjectStatusEnum;
  description: string;
  startDate: Date;
  endDate: Date;
  location: string;
  projectType: string;
  creativesNeeded: string[];
  moodBoardUrl?: string | null;
  createdAt?: Date | null;
  updatedAt?: Date | null;
  userId: string;
  projectId: string;
}
export interface UpdateProjectRequest {
  projectId: string;
  bookerId: string;
  data: UpdateProjectRequestData;
}
export interface UpdateProjectRequestData {
  title?: string | null;
  client?: string | null;
  budget?: number | null;
  status?: string | null;
  description?: string | null;
  startDate?: string | null;
  endDate?: string | null;
  location?: string | null;
  projectType?: string | null;
  creativesNeeded?: string[] | null;
  moodBoardUrl?: string | null;
}
