import type { Timestamp } from "firebase/firestore";
import type { FirestoreDocument } from "../../lib/firebase/converters";

export interface Booker extends FirestoreDocument {
	id: string;
	userId: string; // Reference to UserRootAccount
	displayName: string;
	email: string;
	phone?: string;
	company?: string;
	location?: string;
	createdAt: Timestamp;
	updatedAt: Timestamp;
}

// export interface Booker extends FirestoreDocument {
//   userId: string; // Reference to User document
//   name: string;
//   type: string; // "CREATIVE DIRECTOR", "MARKETING MANAGER", etc.
//   location: string;
//   bio: string;
//   company?: string;
//   followers: number;
//   following: number;
//   isVerified?: boolean;
//   profileImage?: string;
//   website?: string;
//   socialMedia?: {
//     instagram?: string;
//     twitter?: string;
//     linkedin?: string;
//   };
// }

export interface BookerProfile {
	booker: <PERSON>;
	projects: Project[];
	offers: Offer[];
	collaborations: Collaboration[];
}
// Related types for <PERSON> profiles
export interface Project extends FirestoreDocument {
	bookerId: string;
	title: string;
	client: string;
	budget: number;
	status: "active" | "completed" | "planning";
	description: string;
	startDate: string;
	endDate: string;
	location: string;
	projectType: string;
	creativesNeeded: string[];
	moodBoardUrl?: string;
	roleBudgetOverrides?: { [roleName: string]: string };
	roleStatusOverrides?: {
		[roleName: string]: "Needed" | "Pending" | "Confirmed" | "Declined";
	};
}

export interface Offer extends FirestoreDocument {
	projectId: string;
	projectTitle: string;
	bookerId: string;
	bookerName: string;
	creativeId: string;
	creativeName: string;
	role: string;
	amount: number;
	status:
		| "pending"
		| "accepted"
		| "declined"
		| "expired"
		| "withdrawn"
		| "negotiating"
		| "pending_creative_approval"
		| "active"
		| "rejected_by_creative";
	projectType: string;
	location: string;
	description?: string;
	offerDate: Timestamp;
	responseDate?: Timestamp;
	notes?: string;
	isSentByCreative: boolean;
}

export interface Collaboration extends FirestoreDocument {
	projectId: string;
	bookerId: string;
	creativeId: string;
	role: string;
	budget: number;
	completedDate: Timestamp;
	status: "completed" | "ongoing" | "cancelled";
}
