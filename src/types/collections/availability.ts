import { Timestamp } from "firebase/firestore";
import type { FirestoreDocument } from "../../lib/firebase/converters";

// Defines a single slot within a day, can be generic or specific.
export interface TimeSlot {
	start: string; // HH:mm
	end: string; // HH:mm
	isBooked?: boolean; // Optional: if this specific slot is booked
	type?: "available" | "unavailable" | "booked" | "custom"; // Optional: further describe slot
	notes?: string; // Optional: notes for this specific time slot
}

// Represents availability for a single day.
export interface DailyAvailability {
	date: string; // YYYY-MM-DD
	isAvailableAllDay?: boolean; // If true, timeSlots might be ignored or represent exceptions
	timeSlots: TimeSlot[]; // Specific slots of availability or unavailability
	notes?: string; // General notes for the day
}

// Main Availability document, typically per creative.
export interface Availability extends FirestoreDocument {
	entityId: string; // ID of the entity this availability belongs to (e.g., creativeId)
	entityType: "creative" | "resource"; // Type of entity
	dailySchedules: DailyAvailability[]; // Array of daily availability records
	// General availability settings (optional)
	generalNotes?: string;
	timezone?: string; // e.g., "America/Los_Angeles"
}
