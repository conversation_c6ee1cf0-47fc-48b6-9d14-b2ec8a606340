import type { Timestamp } from "firebase/firestore";
import type { FirestoreDocument } from "../../lib/firebase/converters";
import type { Payment, Offer, Collaboration } from "@/components/types";

export interface AvailabilitySlot extends FirestoreDocument {
	id: string; // Add id to satisfy BaseEntity constraint
	date: string; // YYYY-MM-DD format
	isAvailable: boolean;
	timeSlots?: {
		start: string; // HH:mm format
		end: string;
		isBooked: boolean;
	}[];
	notes?: string;
}

export interface Creative extends FirestoreDocument {
	id: string;
	userId: string; // Reference to UserRootAccount
	displayName: string;
	email: string;
	description?: string;
	creativeType?: string;
	tags?: string[];
	portfolioPhotos: string[];
	hourlyRate?: number;
	dayRate?: number;
	location?: string;
	availabilityNotes?: string;
	createdAt: Timestamp;
	updatedAt: Timestamp;
	payments?: Payment[];
	offers?: Offer[];
	collaborations?: Collaboration[];
	portfolio?: PortfolioItem[];
}

// export interface Creative extends FirestoreDocument {
//   userId: string; // Reference to User document
//   name: string;
//   type: string; // "PHOTOGRAPHER", "MODEL", "MAKEUP ARTIST", etc.
//   location: string;
//   bio: string;
//   followers: number;
//   following: number;
//   specialties: string[];
//   portfolioPhotos: string[]; // Array of storage URLs
//   hourlyRate?: number;
//   dayRate?: number;
//   coordinates?: {
//     lat: number;
//     lng: number;
//   };
//   availability: AvailabilitySlot[];
//   isVerified?: boolean;
//   profileImage?: string;
//   website?: string;
//   socialMedia?: {
//     instagram?: string;
//     twitter?: string;
//     linkedin?: string;
//   };
// }

export interface CreativeProfile {
	creative: Creative;
	portfolioItems: PortfolioItem[];
	upcomingJobs: Job[];
	reviews: Review[];
}
// Related types for Creative profiles
export interface PortfolioItem extends FirestoreDocument {
	creativeId: string;
	type: "image" | "text" | "iframe";
	position: number;
	imageUrl?: string;
	alt?: string;
	textContent?: string;
	backgroundColor?: string;
	fontSize?: string;
	fontFamily?: string;
	iframeUrl?: string;
	size: "standard" | "horizontal" | "vertical" | "large";
	isMerged?: boolean;
	originalItems?: string[];
	likes: number;
	comments: number;
}

export interface Job extends FirestoreDocument {
	offerId: string; // Align with MVP, was bookingId. Consider if bookingId is still needed or can be an alias/removed.
	creativeId: string;
	bookerId: string; // Added from MVP
	title: string;
	client: string;
	date: string; // Consider enforcing YYYY-MM-DD if appropriate for your input validation
	time: string; // Consider enforcing HH:mm if appropriate
	location: string;
	status: "confirmed" | "pending" | "completed" | "cancelled"; // MVP is "confirmed" | "pending" | "completed". 'cancelled' is kept as an extension.
	amount: number; // Added from MVP
	type?: string; // Was not optional in original, made optional as it's not in MVP.
	briefing?: string;
	requirements?: string;
	// bookingId?: string; // You can uncomment this if bookingId serves a distinct, necessary purpose.
}

export interface Review extends FirestoreDocument {
	collaborationId: string;
	reviewerId: string;
	revieweeId: string;
	projectTitle: string;
	rating: number; // 1-5
	reviewText: string;
	isPublic: boolean;
}
