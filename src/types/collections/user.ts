import type { Timestamp } from "firebase/firestore";
import type { FirestoreDocument } from "../../lib/firebase/converters";

export interface UserRootAccount extends FirestoreDocument {
	id: string;
	email: string;
	displayName: string;
	creatives: Record<string, string>; // {creative_id: display_name}
	bookers: Record<string, string>; // {booker_id: display_name}
	createdAt: Timestamp;
	updatedAt: Timestamp;
	isActive: boolean;
	profileImage?: string;
	emailVerified?: boolean;
	lastLoginAt?: Timestamp;
}

// export interface User extends FirestoreDocument {
//   email: string;
//   name: string;
//   userType: "creative" | "booker";
//   isActive: boolean;
//   profileImage?: string;
//   emailVerified?: boolean;
//   lastLoginAt?: Timestamp;
// }

export interface AuthUser {
	uid: string;
	email: string | null;
	displayName: string | null;
	photoURL: string | null;
	emailVerified: boolean;
}
