import "./globals.css"; // Assuming you have a global stylesheet
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import type { PropsWithChildren } from "react";
import { FirebaseComponents } from "../components/FirebaseComponents"; // Corrected relative path
import { UserProvider } from "../contexts/UserContext"; // Import UserProvider

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
	title: "DUA Creative Marketplace",
	description: "Connecting Creatives and Bookers",
};

export default function RootLayout({ children }: PropsWithChildren) {
	return (
		<html lang="en">
			<body className={inter.className}>
				<FirebaseComponents>
					<UserProvider>{children}</UserProvider>
				</FirebaseComponents>
			</body>
		</html>
	);
}
