"use server";

import { initializeServerApp } from "@/src/lib/firebase/server-init";
import { getFirestore } from "firebase-admin/firestore";
import { revalidatePath } from "next/cache";
import { z } from "zod";

const projectUpdateSchema = z.object({
  title: z.string().min(1, "Title is required."),
  client: z.string().min(1, "Client is required."),
  budget: z.number().positive("Budget must be a positive number."),
  projectType: z.string().min(1, "Project type is required."),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().min(1, "Location is required."),
  description: z.string().optional(),
  creativesNeeded: z.array(z.string()).optional(),
  roleBudgetOverrides: z.record(z.string()).optional(),
  roleStatusOverrides: z.record(z.string()).optional(),
});

export async function updateProject(
  projectId: string,
  formData: unknown
): Promise<{ success: boolean; message: string }> {
  const validationResult = projectUpdateSchema.safeParse(formData);

  if (!validationResult.success) {
    return {
      success: false,
      message: validationResult.error.errors.map((e) => e.message).join(", "),
    };
  }

  try {
    const app = initializeServerApp();
    const firestore = getFirestore(app);

    const projectRef = firestore.collection("projects").doc(projectId);

    await projectRef.update({
      ...validationResult.data,
      updatedAt: new Date(),
    });

    revalidatePath(`/booker/project/${projectId}`);

    return { success: true, message: "Project updated successfully." };
  } catch (error) {
    console.error("Error updating project:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred.";
    return { success: false, message: `Failed to update project: ${errorMessage}` };
  }
} 