"use client";

import { FirebaseOptions, getApp, getApps, initializeApp } from "firebase/app";
import { type Auth, getAuth } from "firebase/auth";
import { type Firestore, getFirestore } from "firebase/firestore";
import { type Functions, getFunctions } from "firebase/functions";
import { type FirebaseStorage, getStorage } from "firebase/storage";
import type { PropsWithChildren } from "react";
import {
	AuthProvider,
	FirebaseAppProvider,
	FirestoreProvider,
	FunctionsProvider,
	StorageProvider,
	useFirebaseApp,
} from "reactfire";
import { firebaseConfig } from "../lib/firebase/config";
import { UserProvider } from "../contexts/UserContext";

// Declare variables for our services
let app: ReturnType<typeof initializeApp>;
let auth: Auth;
let db: Firestore;
let functions: Functions;
let storage: FirebaseStorage;

// This function safely initializes Firebase, preventing re-initialization
const initializeFirebase = () => {
	if (!getApps().length) {
		if (
			firebaseConfig.apiKey &&
			firebaseConfig.apiKey !== "your_api_key_here"
		) {
			app = initializeApp(firebaseConfig);
			auth = getAuth(app);
			db = getFirestore(app);
			functions = getFunctions(app, "europe-west3");
			storage = getStorage(app);
		} else {
			console.error(
				"Firebase API Key is missing or is still a placeholder. Firebase could not be initialized.",
			);
		}
	} else {
		app = getApp();
		auth = getAuth(app);
		db = getFirestore(app);
		functions = getFunctions(app, "europe-west3");
		storage = getStorage(app);
	}
};

// Run the initialization
initializeFirebase();

// Export the initialized services for direct use in other files
export { auth, db, functions, storage };

// --- Reactfire Provider Component ---

const FirebaseComponentsInner = ({ children }: PropsWithChildren) => {
	const appInstance = useFirebaseApp();
	const authInstance = getAuth(appInstance);
	const firestoreInstance = getFirestore(appInstance);
	const functionsInstance = getFunctions(appInstance, "europe-west3");
	const storageInstance = getStorage(appInstance);

	return (
		<AuthProvider sdk={authInstance}>
			<FirestoreProvider sdk={firestoreInstance}>
				<FunctionsProvider sdk={functionsInstance}>
					<StorageProvider sdk={storageInstance}>
						<UserProvider>{children}</UserProvider>
					</StorageProvider>
				</FunctionsProvider>
			</FirestoreProvider>
		</AuthProvider>
	);
};

export const FirebaseComponents = ({ children }: PropsWithChildren) => {
	// We use the imported config directly here
	if (!firebaseConfig.apiKey || firebaseConfig.apiKey === "your_api_key_here") {
		return (
			<>
				<h1>Firebase configuration is missing or invalid.</h1>
				<p>Please check your configuration in `src/lib/firebase/config.ts`</p>
			</>
		);
	}

	return (
		<FirebaseAppProvider firebaseConfig={firebaseConfig}>
			<FirebaseComponentsInner>{children}</FirebaseComponentsInner>
		</FirebaseAppProvider>
	);
};
