import {
	type CollectionReference,
	type DocumentReference,
	type Query,
	collection,
	doc,
	query,
} from "firebase/firestore";
import { useFirestore } from "reactfire";
import { FirebaseCollections } from "../constants/firebaseCollections";
import {
	type FirestoreDocument,
	createFirestoreConverter,
} from "../lib/firebase/converters";
import type { Availability } from "../types/collections/availability";
import type {
	Booker,
	Collaboration,
	Offer,
	Project,
} from "../types/collections/booker";
import type {
	Creative,
	Job,
	PortfolioItem,
	Review,
} from "../types/collections/creative";
import type { UserRootAccount as User } from "../types/collections/user";

type BaseEntity = FirestoreDocument;

export const useFirestoreTypedRefs = () => {
	const firestore = useFirestore();

	const getTypedCollectionRef = <T extends BaseEntity>(
		collectionPath: string,
	): CollectionReference<T> => {
		return collection(firestore, collectionPath).withConverter(
			createFirestoreConverter<T>(),
		);
	};

	const getTypedDocRef = <T extends BaseEntity>(
		collectionPath: string,
		documentId: string,
	): DocumentReference<T> => {
		return doc(firestore, collectionPath, documentId).withConverter(
			createFirestoreConverter<T>(),
		);
	};

	const getTypedQuery = <T extends BaseEntity>(
		collectionRef: CollectionReference<T>,
		...queryConstraints: any[]
	): Query<T> => {
		return query(collectionRef, ...queryConstraints).withConverter(
			createFirestoreConverter<T>(),
		);
	};

	const usersCollectionRef = getTypedCollectionRef<User>(
		FirebaseCollections.USER_ROOT_ACCOUNT,
	);
	const creativesCollectionRef = getTypedCollectionRef<Creative>(
		FirebaseCollections.CREATIVES,
	);
	const bookersCollectionRef = getTypedCollectionRef<Booker>(
		FirebaseCollections.BOOKERS,
	);
	const projectsCollectionRef = getTypedCollectionRef<Project>(
		FirebaseCollections.PROJECTS,
	);
	const portfolioItemsCollectionRef = getTypedCollectionRef<PortfolioItem>(
		FirebaseCollections.PORTFOLIO_ITEMS,
	);
	const jobsCollectionRef = getTypedCollectionRef<Job>(
		FirebaseCollections.JOBS,
	);
	const offersCollectionRef = getTypedCollectionRef<Offer>(
		FirebaseCollections.OFFERS,
	);
	const reviewsCollectionRef = getTypedCollectionRef<Review>(
		FirebaseCollections.REVIEWS,
	);
	const collaborationsCollectionRef = getTypedCollectionRef<Collaboration>(
		FirebaseCollections.COLLABORATIONS,
	);
	const availabilityCollectionRef = getTypedCollectionRef<Availability>(
		FirebaseCollections.AVAILABILITY,
	);

	return {
		getTypedCollectionRef,
		getTypedDocRef,
		getTypedQuery,
		usersCollectionRef,
		creativesCollectionRef,
		bookersCollectionRef,
		projectsCollectionRef,
		portfolioItemsCollectionRef,
		jobsCollectionRef,
		offersCollectionRef,
		reviewsCollectionRef,
		collaborationsCollectionRef,
		availabilityCollectionRef,
	};
};
