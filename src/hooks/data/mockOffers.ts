import { Timestamp } from "firebase/firestore";
import type { Offer } from "../../types/collections/booker"; // Adjust path

export const mockOffers: Offer[] = [
	{
		id: "offer-001",
		projectId: "project-001",
		projectTitle: "Summer Fashion Campaign",
		bookerId: "booker-001",
		bookerName: "Bob The Builder",
		creativeId: "creative-001",
		creativeName: "Alice Wonderland",
		role: "PHOTOGRAPHER",
		amount: 5000,
		status: "pending",
		projectType: "Fashion Photography",
		location: "Malibu Beach, CA",
		offerDate: Timestamp.fromDate(new Date("2024-05-10T00:00:00Z")),
		isSentByCreative: false,
		// FirestoreDocument fields
		createdAt: Timestamp.fromDate(new Date("2024-05-10T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2024-05-10T00:00:00Z")),
	},
	// Add more mock offer objects
];
