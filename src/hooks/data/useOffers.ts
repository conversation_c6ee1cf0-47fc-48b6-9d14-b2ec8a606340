import { where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData, useFirestoreDocData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
// Offer type is in booker.ts, so adjust path. If you move it, update this.
import type { Offer } from "../../types/collections/booker";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type {
	UseCollectionDataResult,
	UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single offer document using ReactFire.
 * @param offerId The ID of the offer to fetch. If undefined, returns loading/success with null data.
 */
export const useOfferDoc = (
	offerId: string | undefined,
): UseDocumentDataResult<Offer> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	const offerDocRef = useMemo(() => {
		const docId = offerId || "___";
		return getTypedDocRef<Offer>(FirebaseCollections.OFFERS, docId);
	}, [getTypedDocRef, offerId]);

	const { status, data, error } = useFirestoreDocData<Offer>(offerDocRef, {
		idField: "id",
	});

	if (!offerId) {
		return { status: "success", data: null, error: undefined };
	}

	return { status, data: data || null, error };
};

/**
 * Hook to fetch all offers.
 */
export const useOffersCollection = (): UseCollectionDataResult<Offer> => {
	const { getTypedCollectionRef } = useFirestoreTypedRefs();
	const collectionRef = useMemo(
		() => getTypedCollectionRef<Offer>(FirebaseCollections.OFFERS),
		[getTypedCollectionRef],
	);

	const { status, data, error } = useFirestoreCollectionData<Offer>(
		collectionRef,
		{ idField: "id" },
	);

	return { status, data: data || [], error };
};

/**
 * Fetches all offers associated with a specific project.
 * @param projectId The ID of the project whose offers are being fetched.
 * @returns A standard Firebase hook result object with an array of the project's offers.
 */
export function useOffersForProject(projectId: string | undefined) {
	const { offersCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	if (!projectId) {
		return { status: "success" as const, data: [], error: undefined };
	}

	const offersQuery = getTypedQuery(
		offersCollectionRef,
		where("projectId", "==", projectId),
	);

	const result = useFirestoreCollectionData(offersQuery, {
		idField: "id",
	});

	return result;
}

/**
 * Hook to fetch offers for a specific creative.
 * @param creativeId The ID of the creative whose offers to fetch.
 */
export const useOffersForCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<Offer> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Offer>(
			FirebaseCollections.OFFERS,
		);
		// If no ID, the query will search for a creativeId of '___' which will return no results.
		return getTypedQuery(
			collectionRef,
			where("creativeId", "==", creativeId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, creativeId]);

	const { status, data, error } = useFirestoreCollectionData<Offer>(query, {
		idField: "id",
	});

	if (!creativeId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};

/**
 * Hook to fetch offers sent by a specific booker.
 * @param bookerId The ID of the booker whose offers to fetch.
 */
export const useOffersByBooker = (
	bookerId: string | undefined,
): UseCollectionDataResult<Offer> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Offer>(
			FirebaseCollections.OFFERS,
		);
		// If no ID, the query will search for a bookerId of '___' which will return no results.
		return getTypedQuery(
			collectionRef,
			where("bookerId", "==", bookerId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, bookerId]);

	const { status, data, error } = useFirestoreCollectionData<Offer>(query, {
		idField: "id",
	});

	if (!bookerId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};
