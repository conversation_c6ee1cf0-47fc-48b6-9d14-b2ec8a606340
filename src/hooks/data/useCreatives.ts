import { useEffect, useMemo, useState } from "react";
import { useFirestoreDocData, useFirestoreCollectionData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import type { <PERSON> } from "../../types/collections/booker";
import type { Creative } from "../../types/collections/creative"; // Adjust path if your creative.ts is elsewhere
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import {
	UseCollectionDataResult,
	type UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single creative document in real-time using ReactFire.
 * @param creativeId The ID of the creative to fetch. If undefined, returns loading/success with null data.
 */
export const useCreativeDoc = (
	creativeId: string | undefined,
): UseDocumentDataResult<Creative> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	// Create a stable reference to the document.
	// If the ID is missing, use a path that is valid but will not exist.
	const creativeDocRef = useMemo(() => {
		const docId = creativeId || "___"; // Use a placeholder ID if none is provided.
		return getTypedDocRef<Creative>(FirebaseCollections.CREATIVES, docId);
	}, [getTypedDocRef, creativeId]);

	// Always call the hook at the top level.
	const { status, data, error } = useFirestoreDocData<Creative>(
		creativeDocRef,
		{
			idField: "id",
		},
	);

	// If no creativeId was provided, the hook will successfully return 'no data'.
	// We can return null to the consuming component for clarity.
	if (!creativeId) {
		return { status: "success", data: null, error: undefined };
	}

	return { status, data: data || null, error };
};

/**
 * Hook to fetch a single booker document in real-time using ReactFire.
 * @param bookerId The ID of the booker to fetch. If undefined, returns loading/success with null data.
 */
export const useBookerDoc = (
	bookerId: string | undefined,
): UseDocumentDataResult<Booker> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	// Create a stable reference to the document.
	// If the ID is missing, use a path that is valid but will not exist.
	const bookerDocRef = useMemo(() => {
		const docId = bookerId || "___"; // Use a placeholder ID if none is provided.
		return getTypedDocRef<Booker>(FirebaseCollections.BOOKERS, docId);
	}, [getTypedDocRef, bookerId]);

	// Always call the hook at the top level.
	const { status, data, error } = useFirestoreDocData<Booker>(bookerDocRef, {
		idField: "id",
	});

	// If no bookerId was provided, the hook will successfully return 'no data'.
	// We can return null to the consuming component for clarity.
	if (!bookerId) {
		return { status: "success", data: null, error: undefined };
	}

	return { status, data: data || null, error };
};

/**
 * Hook to fetch all creatives from the database in real-time using ReactFire.
 * This is used for the search creatives functionality.
 */
export const useAllCreatives = (): UseCollectionDataResult<Creative> => {
	const { getTypedCollectionRef } = useFirestoreTypedRefs();

	const creativesCollectionRef = useMemo(() => {
		return getTypedCollectionRef<Creative>(FirebaseCollections.CREATIVES);
	}, [getTypedCollectionRef]);

	const { status, data, error } = useFirestoreCollectionData<Creative>(
		creativesCollectionRef,
		{
			idField: "id",
		},
	);

	return {
		status,
		data: data || [],
		error
	};
};
