import { doc } from "firebase/firestore";
import { useFirestoreDocData } from "reactfire";
import type { Availability } from "../../types/collections/availability";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type { UseDocumentDataResult } from "./useUsers";

// Re-export the type for consistency across our data hooks
export type FirebaseHookResult<T> = UseDocumentDataResult<T>;

/**
 * Fetches the availability document for a single creative.
 * @param creativeId The ID of the creative whose availability is being fetched.
 * @returns A standard Firebase hook result object with the creative's availability data.
 */
export function useAvailabilityForCreative(
	creativeId: string,
): FirebaseHookResult<Availability> {
	const { availabilityCollectionRef } = useFirestoreTypedRefs();

	// The document in the 'availability' collection has the same ID as the creative.
	const availabilityDocRef = doc(availabilityCollectionRef, creativeId);

	const { status, data, error } = useFirestoreDocData(availabilityDocRef, {
		idField: "id",
	});

	return { status, data: data || null, error };
}
