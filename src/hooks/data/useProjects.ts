import { collection, documentId, query, where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestore, useFirestoreCollectionData } from "reactfire";
import { Project } from "../../types/collections/booker";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import { createFirestoreConverter } from "../../lib/firebase/converters";

/**
 * Fetches all projects associated with a specific booker.
 * @param bookerId The ID of the booker whose projects are being fetched.
 * @returns The result object from ReactFire's useFirestoreCollectionData hook, or a default state if no ID is provided.
 */
export function useProjectsByBooker(bookerId: string | undefined) {
	console.log("useProjectsByBooker hook received bookerId:", bookerId);

	const firestore = useFirestore();

	// Memoize the query to prevent infinite re-renders
	const projectsQuery = useMemo(() => {
		// Always create the query, but use a dummy value when bookerId is undefined or empty
		const queryBookerId = (bookerId && bookerId.trim() !== "") ? bookerId : "__INVALID_BOOKER_ID__";

		const projectsCollection = collection(firestore, FirebaseCollections.PROJECTS)
			.withConverter(createFirestoreConverter<Project>());

		return query(
			projectsCollection,
			where("bookerId", "==", queryBookerId)
		);
	}, [firestore, bookerId]);

	// Always call the hook to maintain consistent hook order
	const result = useFirestoreCollectionData(projectsQuery, {
		idField: "id",
	});

	// Return empty results if no valid bookerId, but maintain the same return structure
	const hasValidBookerId = bookerId && bookerId.trim() !== "";

	return {
		status: hasValidBookerId ? result.status : "success" as const,
		data: hasValidBookerId ? result.data : [],
		error: hasValidBookerId ? result.error : undefined,
	};
}

/**
 * Fetches multiple project documents from a list of IDs.
 * @param projectIds An array of project IDs to fetch.
 * @returns The result object from ReactFire's useFirestoreCollectionData hook.
 */
export function useProjectsByIds(projectIds: string[] | undefined) {
	const firestore = useFirestore();

	// Always create the query to maintain consistent hook calls
	const projectsQuery = useMemo(() => {
		const projectsCollection = collection(firestore, FirebaseCollections.PROJECTS)
			.withConverter(createFirestoreConverter<Project>());

		// Use a dummy array if no projectIds provided to maintain query structure
		const queryIds = (projectIds && projectIds.length > 0) ? projectIds : ["__INVALID_PROJECT_ID__"];

		// The 'in' query can have at most 30 elements in its array.
		// For a real app, we might need to chunk this for larger arrays.
		if (projectIds && projectIds.length > 30) {
			console.warn("Querying for more than 30 projects at once, this may fail.");
		}

		return query(
			projectsCollection,
			where(documentId(), "in", queryIds)
		);
	}, [firestore, projectIds]);

	// Always call the hook to maintain consistent hook order
	const result = useFirestoreCollectionData(projectsQuery, {
		idField: "id",
	});

	// Return empty results if no valid projectIds, but maintain the same return structure
	const hasValidProjectIds = projectIds && projectIds.length > 0;

	return {
		status: hasValidProjectIds ? result.status : "success" as const,
		data: hasValidProjectIds ? result.data : [],
		error: hasValidProjectIds ? result.error : undefined,
	};
}
