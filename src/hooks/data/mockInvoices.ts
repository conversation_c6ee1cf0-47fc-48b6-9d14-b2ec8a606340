import type { Invoice, EscrowTransaction } from "../../../components/types";

export const mockInvoices: Invoice[] = [
	{
		id: "invoice-001",
		invoiceNumber: "INV-2024-001",
		offerId: "offer-001",
		projectId: "project-001",
		bookerId: "booker-001",
		bookerName: "Bob The Builder",
		creativeId: "creative-001",
		creativeName: "Alice Wonderland",
		amount: 5000,
		status: "sent",
		issueDate: "2024-01-15",
		dueDate: "2024-02-14",
		description: "Photography services for Summer Fashion Campaign",
		projectTitle: "Summer Fashion Campaign",
		role: "PHOTOGRAPHER",
		escrowStatus: "funds_pending",
		notes: "Invoice for completed photography work",
		createdAt: new Date("2024-01-15"),
		updatedAt: new Date("2024-01-15"),
	},
	{
		id: "invoice-002",
		invoiceNumber: "INV-2024-002",
		offerId: "offer-002",
		projectId: "project-002",
		bookerId: "booker-001",
		bookerName: "Bob The Builder",
		creativeId: "creative-002",
		creativeName: "Charlie Creative",
		amount: 3500,
		status: "paid",
		issueDate: "2024-01-10",
		dueDate: "2024-02-09",
		description: "Videography services for Product Launch",
		projectTitle: "Product Launch Event",
		role: "VIDEOGRAPHER",
		escrowStatus: "funds_released",
		paymentDate: "2024-01-20",
		notes: "Payment completed successfully",
		createdAt: new Date("2024-01-10"),
		updatedAt: new Date("2024-01-20"),
	},
	{
		id: "invoice-003",
		invoiceNumber: "INV-2024-003",
		offerId: "offer-003",
		projectId: "project-003",
		bookerId: "booker-001",
		bookerName: "Bob The Builder",
		creativeId: "creative-003",
		creativeName: "Diana Designer",
		amount: 2800,
		status: "sent",
		issueDate: "2024-01-20",
		dueDate: "2024-02-19",
		description: "Styling services for Brand Photoshoot",
		projectTitle: "Brand Photoshoot",
		role: "STYLIST",
		escrowStatus: "funds_held",
		notes: "Funds held in escrow pending completion confirmation",
		createdAt: new Date("2024-01-20"),
		updatedAt: new Date("2024-01-20"),
	},
];

export const mockEscrowTransactions: EscrowTransaction[] = [
	{
		id: "escrow-001",
		invoiceId: "invoice-002",
		offerId: "offer-002",
		bookerId: "booker-001",
		creativeId: "creative-002",
		amount: 3500,
		status: "released_to_creative",
		initiatedDate: "2024-01-10",
		releaseDate: "2024-01-20",
		platformFee: 175, // 5% platform fee
		netAmount: 3325,
		paymentMethod: "Credit Card",
		transactionId: "txn_1234567890",
		notes: "Payment released after successful project completion",
		createdAt: new Date("2024-01-10"),
		updatedAt: new Date("2024-01-20"),
	},
	{
		id: "escrow-002",
		invoiceId: "invoice-003",
		offerId: "offer-003",
		bookerId: "booker-001",
		creativeId: "creative-003",
		amount: 2800,
		status: "funds_held",
		initiatedDate: "2024-01-20",
		platformFee: 140, // 5% platform fee
		netAmount: 2660,
		paymentMethod: "Credit Card",
		transactionId: "txn_0987654321",
		notes: "Funds held in escrow awaiting completion confirmation",
		createdAt: new Date("2024-01-20"),
		updatedAt: new Date("2024-01-20"),
	},
];
