import { where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import type { Payment } from "../../../components/types";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type { UseCollectionDataResult } from "./useUsers";

/**
 * Hook to fetch payments for a specific booker.
 * @param bookerId The ID of the booker whose payments to fetch.
 */
export const usePaymentsByBooker = (
	bookerId: string | undefined,
): UseCollectionDataResult<Payment> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Payment>(
			FirebaseCollections.PAYMENTS,
		);
		return getTypedQuery(
			collectionRef,
			where("bookerId", "==", bookerId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, bookerId]);

	const { status, data, error } = useFirestoreCollectionData<Payment>(query, {
		idField: "id",
	});

	if (!bookerId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};
