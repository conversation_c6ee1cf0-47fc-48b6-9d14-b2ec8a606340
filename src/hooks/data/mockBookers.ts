import { Timestamp } from "firebase/firestore";
import type { <PERSON> } from "../../types/collections/booker"; // Adjust path

export const mockBookers: <PERSON>[] = [
	{
		id: "booker-001", // Should match a User ID if it's a profile
		userId: "user-002", // Reference to a User document
		displayName: "Bob The Builder",
		email: "<EMAIL>",
		phone: "+1234567890",
		company: "Builders Inc.",
		location: "Los Angeles, CA",
		createdAt: Timestamp.fromDate(new Date("2023-02-20T11:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-02-25T14:45:00Z")),
	},
	// Add more mock booker objects
];
