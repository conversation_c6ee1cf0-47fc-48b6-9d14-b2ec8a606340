import { orderBy, where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData, useFirestoreDocData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
// PortfolioItem type is in creative.ts, so adjust path if needed.
import type { PortfolioItem } from "../../types/collections/creative";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type {
	UseCollectionDataResult,
	UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single portfolio item document using ReactFire.
 * @param itemId The ID of the portfolio item to fetch. If undefined, returns loading/success with null data.
 */
export const usePortfolioItemDoc = (
	itemId: string | undefined,
): UseDocumentDataResult<PortfolioItem> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	const itemDocRef = useMemo(() => {
		if (!itemId) return null;
		return getTypedDocRef<PortfolioItem>(
			FirebaseCollections.PORTFOLIO_ITEMS,
			itemId,
		);
	}, [getTypedDocRef, itemId]);

	if (!itemDocRef) {
		return { status: "success", data: null, error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreDocData<PortfolioItem>(
		itemDocRef,
		{ idField: "id" },
	);

	return { status, data: data || null, error };
};

/**
 * Hook to fetch portfolio items for a specific creative in real-time.
 * @param creativeId The ID of the creative whose portfolio items to fetch.
 */
export const usePortfolioItemsForCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<PortfolioItem> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		if (!creativeId) return null;

		const collectionRef = getTypedCollectionRef<PortfolioItem>(
			FirebaseCollections.PORTFOLIO_ITEMS,
		);
		// Query for items belonging to the creative, ordered by their position.
		return getTypedQuery(
			collectionRef,
			where("creativeId", "==", creativeId),
			orderBy("position", "asc"),
		);
	}, [getTypedCollectionRef, getTypedQuery, creativeId]);

	if (!query) {
		return { status: "success", data: [], error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreCollectionData<PortfolioItem>(
		query,
		{
			idField: "id",
		},
	);

	return { status, data: data || [], error };
};
