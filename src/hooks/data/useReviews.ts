import { useEffect, useMemo, useState } from "react";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import type { Review } from "../../types/collections/creative"; // Adjust if Review type is elsewhere
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type {
	UseCollectionDataResult,
	UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single review document using the mock-data-bridge.
 * @param reviewId The ID of the review to fetch. If undefined, returns loading/success with null data.
 */
export const useReviewDoc = (
	reviewId: string | undefined,
): UseDocumentDataResult<Review> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading",
	);
	const [error, setError] = useState<Error | undefined>(undefined);
	const [reviewData, setReviewData] = useState<Review | null>(null);

	const reviewDocRef = useMemo(() => {
		if (!reviewId) return null;
		return getTypedDocRef<Review>(FirebaseCollections.REVIEWS, reviewId);
	}, [getTypedDocRef, reviewId]);

	useEffect(() => {
		if (!reviewDocRef || !reviewId) {
			setStatus(reviewId === undefined ? "loading" : "success");
			setReviewData(null);
			setError(undefined);
			return;
		}

		setStatus("loading");
		setError(undefined);
		reviewDocRef
			._fetchMockData()
			.then((data) => {
				setReviewData(data);
				setStatus("success");
			})
			.catch((err) => {
				console.error(`Error fetching mock review ${reviewId}:`, err);
				setError(
					err instanceof Error
						? err
						: new Error("Failed to fetch mock review data"),
				);
				setReviewData(null);
				setStatus("error");
			});
	}, [reviewDocRef, reviewId]);

	return { status, error, data: reviewData };
};

/**
 * Hook to fetch reviews for a specific creative (reviewee).
 * @param creativeId The ID of the creative whose reviews to fetch.
 */
export const useReviewsForCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<Review> => {
	const { getTypedCollectionRef } = useFirestoreTypedRefs();
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading",
	);
	const [error, setError] = useState<Error | undefined>(undefined);
	const [reviewsData, setReviewsData] = useState<Review[]>([]);

	const reviewsCollectionRef = useMemo(() => {
		return getTypedCollectionRef<Review>(FirebaseCollections.REVIEWS);
	}, [getTypedCollectionRef]);

	useEffect(() => {
		if (!creativeId) {
			setStatus("loading");
			setReviewsData([]);
			setError(undefined);
			return;
		}
		setStatus("loading");
		setError(undefined);
		reviewsCollectionRef
			._fetchMockData() // Fetches all mock reviews
			.then((allReviews) => {
				const filteredReviews = allReviews.filter(
					(review) => review.revieweeId === creativeId,
				);
				setReviewsData(filteredReviews);
				setStatus("success");
			})
			.catch((err) => {
				console.error(
					`Error fetching mock reviews for creative ${creativeId}:`,
					err,
				);
				setError(
					err instanceof Error
						? err
						: new Error("Failed to fetch mock reviews for creative"),
				);
				setReviewsData([]);
				setStatus("error");
			});
	}, [reviewsCollectionRef, creativeId]);

	return { status, error, data: reviewsData };
};

/**
 * Hook to fetch reviews for a specific job (collaboration).
 * @param jobId The ID of the job whose reviews to fetch.
 */
export const useReviewsForJob = (
	jobId: string | undefined,
): UseCollectionDataResult<Review> => {
	const { getTypedCollectionRef } = useFirestoreTypedRefs();
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading",
	);
	const [error, setError] = useState<Error | undefined>(undefined);
	const [reviewsData, setReviewsData] = useState<Review[]>([]);

	const reviewsCollectionRef = useMemo(() => {
		return getTypedCollectionRef<Review>(FirebaseCollections.REVIEWS);
	}, [getTypedCollectionRef]);

	useEffect(() => {
		if (!jobId) {
			setStatus("loading");
			setReviewsData([]);
			setError(undefined);
			return;
		}
		setStatus("loading");
		setError(undefined);
		reviewsCollectionRef
			._fetchMockData() // Fetches all mock reviews
			.then((allReviews) => {
				const filteredReviews = allReviews.filter(
					(review) => review.collaborationId === jobId,
				);
				setReviewsData(filteredReviews);
				setStatus("success");
			})
			.catch((err) => {
				console.error(`Error fetching mock reviews for job ${jobId}:`, err);
				setError(
					err instanceof Error
						? err
						: new Error("Failed to fetch mock reviews for job"),
				);
				setReviewsData([]);
				setStatus("error");
			});
	}, [reviewsCollectionRef, jobId]);

	return { status, error, data: reviewsData };
};
