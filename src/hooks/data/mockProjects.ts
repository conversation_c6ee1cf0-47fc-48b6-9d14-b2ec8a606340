import { Timestamp } from "firebase/firestore";
import type { Project } from "../../types/collections/booker"; // Adjust path

export const mockProjects: Project[] = [
	{
		id: "project-001",
		bookerId: "booker-001", // Links to a Booker
		title: "Summer Fashion Campaign",
		client: "Chic Apparel Co.",
		budget: 25000,
		status: "active",
		description: "Photoshoot for the new summer collection.",
		startDate: "2024-07-15",
		endDate: "2024-07-20",
		location: "Malibu Beach, CA",
		projectType: "Fashion Photography",
		creativesNeeded: ["PHOTOGRAPHER", "MODEL", "MAKEUP_ARTIST"],
		createdAt: Timestamp.fromDate(new Date("2024-05-01T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2024-05-05T00:00:00Z")),
	},
	// Add more mock project objects
];
