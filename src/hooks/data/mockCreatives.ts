import { Timestamp } from "firebase/firestore";
import type {
	AvailabilitySlot,
	Creative,
} from "../../types/collections/creative"; // Adjust path

const mockAvailability: AvailabilitySlot[] = [
	{ date: "2024-08-01", isAvailable: true },
	{ date: "2024-08-02", isAvailable: false, notes: "Booked for shoot" },
];

export const mockCreatives: Creative[] = [
	{
		id: "creative-001", // Should match a User ID if it's a profile
		userId: "user-001", // Reference to a User document
		displayName: "Alice Wonderland",
		email: "<EMAIL>",
		location: "New York, NY",
		description: "Specializing in portrait and fashion photography.",
		portfolioPhotos: ["url/to/photo1.jpg", "url/to/photo2.jpg"],
		hourlyRate: 150,
		dayRate: 1000,
		availabilityNotes: "Available for portrait and fashion shoots.",
		createdAt: Timestamp.fromDate(new Date("2023-01-10T10:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-01-15T12:30:00Z")),
	},
	// Add more mock creative objects
];
