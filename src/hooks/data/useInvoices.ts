import { where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import type { Invoice } from "../../../components/types";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type { UseCollectionDataResult } from "./useUsers";

/**
 * Hook to fetch invoices for a specific booker.
 * @param bookerId The ID of the booker whose invoices to fetch.
 */
export const useInvoicesByBooker = (
	bookerId: string | undefined,
): UseCollectionDataResult<Invoice> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Invoice>(
			FirebaseCollections.INVOICES,
		);
		return getTypedQuery(
			collectionRef,
			where("bookerId", "==", bookerId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, bookerId]);

	const { status, data, error } = useFirestoreCollectionData<Invoice>(query, {
		idField: "id",
	});

	if (!bookerId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};

/**
 * Hook to fetch invoices for a specific creative.
 * @param creativeId The ID of the creative whose invoices to fetch.
 */
export const useInvoicesByCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<Invoice> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Invoice>(
			FirebaseCollections.INVOICES,
		);
		return getTypedQuery(
			collectionRef,
			where("creativeId", "==", creativeId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, creativeId]);

	const { status, data, error } = useFirestoreCollectionData<Invoice>(query, {
		idField: "id",
	});

	if (!creativeId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};

/**
 * Hook to fetch invoices for a specific offer.
 * @param offerId The ID of the offer whose invoice to fetch.
 */
export const useInvoiceByOffer = (
	offerId: string | undefined,
): UseCollectionDataResult<Invoice> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<Invoice>(
			FirebaseCollections.INVOICES,
		);
		return getTypedQuery(
			collectionRef,
			where("offerId", "==", offerId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, offerId]);

	const { status, data, error } = useFirestoreCollectionData<Invoice>(query, {
		idField: "id",
	});

	if (!offerId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};
