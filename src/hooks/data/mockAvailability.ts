import { Timestamp } from "firebase/firestore";
import {
	type Availability,
	DailyAvailability,
	TimeSlot,
} from "../../types/collections/availability"; // Adjust path as needed

const today = new Date();
const tomorrow = new Date(today);
tomorrow.setDate(today.getDate() + 1);
const dayAfterTomorrow = new Date(today);
dayAfterTomorrow.setDate(today.getDate() + 2);

const formatDate = (date: Date): string => {
	return date.toISOString().split("T")[0]; // YYYY-MM-DD
};

export const mockAvailability: Availability[] = [
	{
		id: "avail-creative-001", // Auto-generated ID for the availability document
		entityId: "creative-001", // Links to a Creative
		entityType: "creative",
		timezone: "America/Los_Angeles",
		generalNotes:
			"Generally flexible, prefer bookings at least 2 days in advance.",
		dailySchedules: [
			{
				date: formatDate(today),
				isAvailableAllDay: false,
				timeSlots: [
					{ start: "09:00", end: "12:00", type: "available" },
					{
						start: "12:00",
						end: "13:00",
						type: "unavailable",
						notes: "Lunch Break",
					},
					{ start: "13:00", end: "17:00", type: "available" },
				],
			},
			{
				date: formatDate(tomorrow),
				isAvailableAllDay: true,
				timeSlots: [], // Empty as available all day, or could list specific bookable slots
				notes: "Fully available this day.",
			},
			{
				date: formatDate(dayAfterTomorrow),
				isAvailableAllDay: false,
				timeSlots: [
					{
						start: "10:00",
						end: "14:00",
						type: "booked",
						notes: "Photoshoot with Client X",
					},
					{ start: "15:00", end: "18:00", type: "available" },
				],
			},
		],
		createdAt: Timestamp.fromDate(new Date("2023-01-01T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date()),
	},
	{
		id: "avail-creative-002",
		entityId: "creative-002",
		entityType: "creative",
		timezone: "Europe/London",
		dailySchedules: [
			{
				date: formatDate(today),
				isAvailableAllDay: true,
				timeSlots: [],
			},
		],
		createdAt: Timestamp.fromDate(new Date("2023-01-05T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date()),
	},
	// Add more mock availability objects as needed
];
