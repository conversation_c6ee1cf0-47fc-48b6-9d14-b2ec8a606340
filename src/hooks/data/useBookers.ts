import { useEffect, useMemo, useState } from "react";
import { useFirestoreDocData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import type { <PERSON> } from "../../types/collections/booker"; // Adjust path if your booker.ts is elsewhere
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type {
	UseCollectionDataResult,
	UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single booker document in real-time using ReactFire.
 * @param bookerId The ID of the booker to fetch. If undefined, returns loading/success with null data.
 */
export const useBookerDoc = (
	bookerId: string | undefined,
): UseDocumentDataResult<Booker> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	const bookerDocRef = useMemo(() => {
		if (!bookerId) return null;
		return getTypedDocRef<Booker>(FirebaseCollections.BOOKERS, bookerId);
	}, [getTypedDocRef, bookerId]);

	if (!bookerDocRef) {
		return { status: "success", data: null, error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreDocData<Booker>(bookerDocRef, {
		idField: "id",
	});

	return { status, data: data || null, error };
};

/**
 * Hook to fetch all bookers from the mock-data-bridge.
 * Mimics ReactFire's useFirestoreCollectionData pattern.
 */
export const useBookersCollection = (): UseCollectionDataResult<Booker> => {
	const { getTypedCollectionRef } = useFirestoreTypedRefs();
	const [status, setStatus] = useState<"loading" | "success" | "error">(
		"loading",
	);
	const [error, setError] = useState<Error | undefined>(undefined);
	const [bookersData, setBookersData] = useState<Booker[]>([]);

	const bookersCollectionRef = useMemo(() => {
		return getTypedCollectionRef<Booker>(FirebaseCollections.BOOKERS);
	}, [getTypedCollectionRef]);

	useEffect(() => {
		setStatus("loading");
		setError(undefined);
		bookersCollectionRef
			._fetchMockData()
			.then((data) => {
				setBookersData(data);
				setStatus("success");
			})
			.catch((err) => {
				console.error("Error fetching mock bookers collection:", err);
				setError(
					err instanceof Error
						? err
						: new Error("Failed to fetch mock bookers collection"),
				);
				setBookersData([]);
				setStatus("error");
			});
	}, [bookersCollectionRef]);

	return { status, error, data: bookersData };
};
