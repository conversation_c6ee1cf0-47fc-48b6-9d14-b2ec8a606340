import type { Creative } from "@/components/types";
import { documentId, query, where } from "firebase/firestore";
import { useFirestoreCollectionData } from "reactfire";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";

export const useCreativesByIds = (creativeIds: string[] | undefined) => {
	const { creativesCollectionRef } = useFirestoreTypedRefs();

	const creativesQuery =
		creativeIds && creativeIds.length > 0
			? query(creativesCollectionRef, where(documentId(), "in", creativeIds))
			: null;

	const {
		data: creatives,
		status,
		error,
	} = useFirestoreCollectionData(creativesQuery, {
		idField: "id",
	});

	return { creatives: creatives as Creative[], status, error };
};
