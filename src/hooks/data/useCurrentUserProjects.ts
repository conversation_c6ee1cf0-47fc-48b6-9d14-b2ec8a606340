import { useMemo } from "react";
import { useUserContext } from "../../contexts/UserContext";
import { useProjectsByBooker } from "./useProjects";
import { useUserDoc } from "./useUsers";

/**
 * Hook to get the current authenticated user's booker projects.
 * Handles authentication state changes gracefully without causing hook order issues.
 */
export function useCurrentUserProjects() {
	// Always call all hooks in the same order
	const { data: userData } = useUserContext();
	const currentUser = userData?.user;

	// Always pass a string to maintain consistent hook calls
	const userId = currentUser?.uid || "";
	const { data: userRootAccount } = useUserDoc(userId);
	
	// Get the first booker ID from the user's bookers
	const currentBookerId = useMemo(() => {
		if (!userRootAccount?.bookers) return "";
		const bookerIds = Object.keys(userRootAccount.bookers);
		return bookerIds.length > 0 ? bookerIds[0] : "";
	}, [userRootAccount]);
	
	// Always call the projects hook to maintain consistent hook calls
	const { data: projects = [], status: projectsStatus } = useProjectsByBooker(currentBookerId);

	// Return empty array if user is not authenticated or has no booker profile
	const currentUserProjects = useMemo(() => {
		if (!currentUser || !currentBookerId || currentBookerId.trim() === "") {
			return [];
		}
		return projects;
	}, [currentUser, currentBookerId, projects]);

	// Return loading status based on authentication state
	const isLoading = useMemo(() => {
		if (!currentUser) return false; // Not authenticated, not loading
		if (!currentBookerId || currentBookerId.trim() === "") return false; // No booker profile, not loading
		return projectsStatus === "loading";
	}, [currentUser, currentBookerId, projectsStatus]);

	return {
		projects: currentUserProjects,
		isLoading,
		status: projectsStatus,
		hasBookerProfile: !!(currentUser && currentBookerId && currentBookerId.trim() !== ""),
	};
}
