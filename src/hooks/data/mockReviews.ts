import { Timestamp } from "firebase/firestore";
import type { Review } from "../../types/collections/creative"; // Adjust path as needed

export const mockReviews: Review[] = [
	{
		id: "review-001",
		collaborationId: "job-001", // ID of the job this review is for
		reviewerId: "booker-001", // ID of the user who wrote the review (e.g., a Booker)
		revieweeId: "creative-001", // ID of the user being reviewed (e.g., a Creative)
		projectTitle: "Summer Fashion Campaign Photoshoot",
		rating: 5,
		reviewText:
			"Amazing work, very professional and delivered stunning photos!",
		isPublic: true,
		createdAt: Timestamp.fromDate(new Date("2024-07-20T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2024-07-20T00:00:00Z")),
	},
	{
		id: "review-002",
		collaborationId: "job-002", // Assume another job ID
		reviewerId: "creative-002", // Example: a creative reviewing a booker (if applicable) or another creative
		revieweeId: "booker-001", // Example: a booker being reviewed
		projectTitle: "Corporate Headshots Project",
		rating: 4,
		reviewText:
			"Smooth collaboration, clear communication. Happy with the outcome.",
		isPublic: true,
		createdAt: Timestamp.fromDate(new Date("2024-08-05T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2024-08-05T00:00:00Z")),
	},
	// Add more mock review objects as needed
];
