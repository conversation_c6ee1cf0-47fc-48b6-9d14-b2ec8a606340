import { useMemo } from "react";
import { useFirestoreDocData, useFirestoreCollectionData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import type { UserRootAccount } from "../../types/collections/user";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";

// Common interface for ReactFire-like document data hooks
export interface UseDocumentDataResult<T> {
	status: "loading" | "success" | "error";
	error?: Error;
	data: T | null;
}

// Common interface for ReactFire-like collection data hooks
export interface UseCollectionDataResult<T> {
	status: "loading" | "success" | "error";
	error?: Error;
	data: T[];
}

/**
 * Hook to fetch a single user document using ReactFire.
 * @param userId The ID of the user to fetch. If undefined, returns success with null data.
 */
export const useUserDoc = (
	userId: string | undefined,
): UseDocumentDataResult<UserRootAccount> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	// Always create a document reference to maintain consistent hook calls
	const userDocRef = useMemo(() => {
		const docId = (userId && userId.trim() !== "") ? userId : "___"; // Use placeholder for invalid IDs
		return getTypedDocRef<UserRootAccount>(FirebaseCollections.USER_ROOT_ACCOUNT, docId);
	}, [getTypedDocRef, userId]);

	// Always call the hook to maintain consistent hook order
	const { status, data, error } = useFirestoreDocData<UserRootAccount>(userDocRef, {
		idField: "id",
	});

	// Return null data if no valid userId, but maintain the same return structure
	const hasValidUserId = userId && userId.trim() !== "";

	return {
		status: hasValidUserId ? status : "success",
		data: hasValidUserId ? (data || null) : null,
		error: hasValidUserId ? error : undefined,
	};
};

/**
 * Hook to fetch all users using ReactFire.
 * Note: Fetching all users might not always be practical. Consider querying for specific needs.
 */
export const useUsersCollection =
	(): UseCollectionDataResult<UserRootAccount> => {
		const { getTypedCollectionRef } = useFirestoreTypedRefs();

		// Memoize the collection reference
		const usersCollectionRef = useMemo(() => {
			return getTypedCollectionRef<UserRootAccount>(FirebaseCollections.USER_ROOT_ACCOUNT);
		}, [getTypedCollectionRef]);

		// Use ReactFire's useFirestoreCollectionData hook
		const { status, data, error } = useFirestoreCollectionData<UserRootAccount>(usersCollectionRef, {
			idField: "id",
		});

		return { status, error, data: data || [] };
	};
