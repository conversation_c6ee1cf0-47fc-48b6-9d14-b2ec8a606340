import { where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData, useFirestoreDocData } from "reactfire";
import { FirebaseCollections } from "../../constants/firebaseCollections";
// Job type is in creative.ts, so adjust path if needed.
import type { Job } from "../../types/collections/creative";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type {
	UseCollectionDataResult,
	UseDocumentDataResult,
} from "./useUsers"; // Re-use common result types

/**
 * Hook to fetch a single job document using ReactFire.
 * @param jobId The ID of the job to fetch. If undefined, returns loading/success with null data.
 */
export const useJobDoc = (
	jobId: string | undefined,
): UseDocumentDataResult<Job> => {
	const { getTypedDocRef } = useFirestoreTypedRefs();

	const jobDocRef = useMemo(() => {
		if (!jobId) return null;
		return getTypedDocRef<Job>(FirebaseCollections.JOBS, jobId);
	}, [getTypedDocRef, jobId]);

	if (!jobDocRef) {
		return { status: "success", data: null, error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreDocData<Job>(jobDocRef, {
		idField: "id",
	});

	return { status, data: data || null, error };
};

/**
 * Hook to fetch jobs for a specific creative in real-time.
 * @param creativeId The ID of the creative whose jobs to fetch.
 */
export const useJobsForCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<Job> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		if (!creativeId) return null;
		const collectionRef = getTypedCollectionRef<Job>(FirebaseCollections.JOBS);
		return getTypedQuery(collectionRef, where("creativeId", "==", creativeId));
	}, [getTypedCollectionRef, getTypedQuery, creativeId]);

	if (!query) {
		return { status: "success", data: [], error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreCollectionData<Job>(query, {
		idField: "id",
	});

	return { status, data: data || [], error };
};

/**
 * Hook to fetch jobs for a specific booker in real-time.
 * @param bookerId The ID of the booker whose jobs to fetch.
 */
export const useJobsForBooker = (
	bookerId: string | undefined,
): UseCollectionDataResult<Job> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		if (!bookerId) return null;
		const collectionRef = getTypedCollectionRef<Job>(FirebaseCollections.JOBS);
		return getTypedQuery(collectionRef, where("bookerId", "==", bookerId));
	}, [getTypedCollectionRef, getTypedQuery, bookerId]);

	if (!query) {
		return { status: "success", data: [], error: undefined };
	}

	// eslint-disable-next-line react-hooks/rules-of-hooks
	const { status, data, error } = useFirestoreCollectionData<Job>(query, {
		idField: "id",
	});

	return { status, data: data || [], error };
};
