import { where } from "firebase/firestore";
import { useFirestoreCollectionData } from "reactfire";
import { Collaboration } from "../../types/collections/booker";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";

/**
 * Fetches all collaborations (confirmed jobs) for a specific creative.
 * @param creativeId The ID of the creative whose collaborations are being fetched.
 * @returns A standard Firebase hook result object with an array of the creative's collaborations.
 */
export function useCollaborationsForCreative(creativeId: string | undefined) {
	const { collaborationsCollectionRef, getTypedQuery } =
		useFirestoreTypedRefs();

	// Always create the query to maintain consistent hook calls
	const collaborationsQuery = getTypedQuery(
		collaborationsCollectionRef,
		where("creativeId", "==", creativeId || "___"),
	);

	// Always call the hook to maintain consistent hook order
	const result = useFirestoreCollectionData(collaborationsQuery, {
		idField: "id",
	});

	// Return empty results if no valid creativeId, but maintain the same return structure
	const hasValidCreativeId = creativeId && creativeId.trim() !== "";

	return {
		status: hasValidCreativeId ? result.status : "success" as const,
		data: hasValidCreativeId ? result.data : [],
		error: hasValidCreativeId ? result.error : undefined,
	};
}

/**
 * Fetches all collaborations (confirmed jobs) for a specific booker.
 * @param bookerId The ID of the booker whose collaborations are being fetched.
 * @returns A standard Firebase hook result object with an array of the booker's collaborations.
 */
export function useCollaborationsForBooker(bookerId: string | undefined) {
	const { collaborationsCollectionRef, getTypedQuery } =
		useFirestoreTypedRefs();

	if (!bookerId) {
		return { status: "success" as const, data: [], error: undefined };
	}

	const collaborationsQuery = getTypedQuery(
		collaborationsCollectionRef,
		where("bookerId", "==", bookerId),
	);

	const result = useFirestoreCollectionData(collaborationsQuery, {
		idField: "id",
	});

	return result;
}
