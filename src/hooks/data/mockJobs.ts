import { Timestamp } from "firebase/firestore";
import type { Job } from "../../types/collections/creative"; // Adjust path

export const mockJobs: Job[] = [
	{
		id: "job-001",
		offerId: "offer-001", // Links to an accepted Offer
		creativeId: "creative-001",
		bookerId: "booker-001",
		title: "Summer Fashion Campaign Photoshoot",
		client: "Chic Apparel Co.",
		date: "2024-07-18",
		time: "10:00",
		location: "Malibu Beach, CA",
		status: "confirmed",
		amount: 5000,
		createdAt: Timestamp.fromDate(new Date("2024-05-15T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2024-05-15T00:00:00Z")),
	},
	// Add more mock job objects
];
