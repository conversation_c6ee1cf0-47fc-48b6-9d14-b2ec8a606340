import { Timestamp } from "firebase/firestore";
import type { PortfolioItem } from "../../types/collections/creative"; // Adjust path

export const mockPortfolioItems: PortfolioItem[] = [
	{
		id: "pitem-001",
		creativeId: "creative-001", // Links to a Creative
		type: "image",
		position: 0,
		imageUrl: "https://example.com/portfolio/creative-001/image1.jpg",
		alt: "Fashion shoot",
		size: "standard",
		likes: 150,
		comments: 12,
		createdAt: Timestamp.fromDate(new Date("2023-03-01T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-03-01T00:00:00Z")),
	},
	{
		id: "pitem-002",
		creativeId: "creative-001",
		type: "text",
		position: 1,
		textContent: "My latest work explores themes of urban decay.",
		backgroundColor: "#333333",
		fontSize: "18px",
		fontFamily: "Arial",
		size: "horizontal",
		likes: 50,
		comments: 5,
		createdAt: Timestamp.fromDate(new Date("2023-03-02T00:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-03-02T00:00:00Z")),
	},
	// Add more mock portfolio items
];
