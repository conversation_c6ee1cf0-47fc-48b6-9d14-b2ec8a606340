import { Timestamp } from "firebase/firestore"; // If your User type uses actual Timestamps
import type { UserRootAccount } from "../../types/collections/user"; // Adjust path as needed

export const mockUsers: UserRootAccount[] = [
	{
		id: "user-001",
		displayName: "Alice Wonderland",
		email: "<EMAIL>",
		creatives: {
			"creative-001": "editor",
		},
		bookers: {},
		isActive: true,
		// FirestoreDocument fields (id is already above)
		createdAt: Timestamp.fromDate(new Date("2023-01-10T10:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-01-15T12:30:00Z")),
		// Optional fields from your User interface
		profileImage: "https://example.com/alice.jpg",
		emailVerified: true,
		lastLoginAt: Timestamp.fromDate(new Date("2024-03-10T09:00:00Z")),
	},
	{
		id: "user-002",
		displayName: "<PERSON> The Builder",
		email: "<EMAIL>",
		bookers: {},
		creatives: {},
		isActive: true,
		createdAt: Timestamp.fromDate(new Date("2023-02-20T11:00:00Z")),
		updatedAt: Timestamp.fromDate(new Date("2023-02-25T14:45:00Z")),
		// company: 'Builders Inc.',
	},
	// Add more mock user objects as needed
];
