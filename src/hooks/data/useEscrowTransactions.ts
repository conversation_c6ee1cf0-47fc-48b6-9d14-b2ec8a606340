import { where } from "firebase/firestore";
import { useMemo } from "react";
import { useFirestoreCollectionData } from "reactfire";
import type { EscrowTransaction } from "../../../components/types";
import { FirebaseCollections } from "../../constants/firebaseCollections";
import { useFirestoreTypedRefs } from "../useFirestoreTypedRefs";
import type { UseCollectionDataResult } from "./useUsers";

/**
 * Hook to fetch escrow transactions for a specific booker.
 * @param bookerId The ID of the booker whose escrow transactions to fetch.
 */
export const useEscrowTransactionsByBooker = (
	bookerId: string | undefined,
): UseCollectionDataResult<EscrowTransaction> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<EscrowTransaction>(
			FirebaseCollections.ESCROW_TRANSACTIONS,
		);
		return getTypedQuery(
			collectionRef,
			where("bookerId", "==", bookerId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, bookerId]);

	const { status, data, error } = useFirestoreCollectionData<EscrowTransaction>(query, {
		idField: "id",
	});

	if (!bookerId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};

/**
 * Hook to fetch escrow transactions for a specific creative.
 * @param creativeId The ID of the creative whose escrow transactions to fetch.
 */
export const useEscrowTransactionsByCreative = (
	creativeId: string | undefined,
): UseCollectionDataResult<EscrowTransaction> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<EscrowTransaction>(
			FirebaseCollections.ESCROW_TRANSACTIONS,
		);
		return getTypedQuery(
			collectionRef,
			where("creativeId", "==", creativeId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, creativeId]);

	const { status, data, error } = useFirestoreCollectionData<EscrowTransaction>(query, {
		idField: "id",
	});

	if (!creativeId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};

/**
 * Hook to fetch escrow transaction for a specific invoice.
 * @param invoiceId The ID of the invoice whose escrow transaction to fetch.
 */
export const useEscrowTransactionByInvoice = (
	invoiceId: string | undefined,
): UseCollectionDataResult<EscrowTransaction> => {
	const { getTypedCollectionRef, getTypedQuery } = useFirestoreTypedRefs();

	const query = useMemo(() => {
		const collectionRef = getTypedCollectionRef<EscrowTransaction>(
			FirebaseCollections.ESCROW_TRANSACTIONS,
		);
		return getTypedQuery(
			collectionRef,
			where("invoiceId", "==", invoiceId || "___"),
		);
	}, [getTypedCollectionRef, getTypedQuery, invoiceId]);

	const { status, data, error } = useFirestoreCollectionData<EscrowTransaction>(query, {
		idField: "id",
	});

	if (!invoiceId) {
		return { status: "success", data: [], error: undefined };
	}

	return { status, data: data || [], error };
};
