import {
	type HttpsCallable,
	httpsCallable as firebaseHttpsCallable,
} from "firebase/functions";
import { useFunctions } from "reactfire";

// Define a generic type for the callable function for better type safety
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type CallableFunction<
	RequestData = any,
	ResponseData = any,
> = HttpsCallable<RequestData, ResponseData>;

/**
 * A hook to provide typed Firebase Callable Functions.
 * These are placeholders and will simulate calls, returning mock responses.
 * The actual Cloud Functions are to be implemented separately.
 */
export const useFirebaseFunctions = () => {
	const functions = useFunctions(); // Get Firebase Functions instance from ReactFire

	/**
	 * Placeholder for creating a user profile.
	 * Simulates a call to a Firebase Function named 'createUserProfile'.
	 * @param data - The data to be sent to the function (e.g., { email, name, userType }).
	 */
	const createUserProfile: CallableFunction<
		{
			name: string;
			userType: "creative" | "booker";
			email: string /* other necessary fields */;
		},
		{ success: boolean; userId?: string; error?: string }
	> = firebaseHttpsCallable(functions, "createUserProfile");

	const callCreateUserProfile = async (data: {
		name: string;
		userType: "creative" | "booker";
		email: string;
		uid: string;
	}) => {
		console.log("Simulating call to createUserProfile with data:", data);
		// In a real scenario: return createUserProfile(data);
		// Simulate a successful response with a mock user ID
		// Ensure the mock response matches the expected structure from your actual cloud function (dua-firebase-endpoints.md)
		await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay
		if (!data.email || !data.name || !data.userType || !data.uid) {
			console.error("Mock createUserProfile: Missing required fields", data);
			return {
				data: { success: false, error: "Mock: Missing required fields." },
			};
		}
		console.log(
			`Mock createUserProfile: Simulating profile creation for UID: ${data.uid}`,
		);
		return { data: { success: true, userId: data.uid } };
	};

	/**
	 * Placeholder for updating a user profile.
	 * Simulates a call to a Firebase Function named 'updateUserProfile'.
	 * @param data - The data for updating the profile (e.g., { userId, name, bio }).
	 */
	const updateUserProfile: CallableFunction<
		{ userId: string; [key: string]: any },
		{ success: boolean; error?: string }
	> = firebaseHttpsCallable(functions, "updateUserProfile");

	const callUpdateUserProfile = async (data: {
		userId: string;
		[key: string]: any;
	}) => {
		console.log("Simulating call to updateUserProfile with data:", data);
		// In a real scenario: return updateUserProfile(data);
		await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate network delay
		if (!data.userId) {
			console.error("Mock updateUserProfile: Missing userId");
			return { data: { success: false, error: "Mock: Missing userId." } };
		}
		console.log(
			`Mock updateUserProfile: Simulating profile update for UID: ${data.userId}`,
		);
		return { data: { success: true } };
	};

	// Add other placeholder function calls here as needed, following the pattern above
	// e.g., createProject, sendOffer, etc.

	return {
		callCreateUserProfile,
		callUpdateUserProfile,
		// Export other callable function wrappers
	};
};
