import type { User as FirebaseUser } from "firebase/auth"; // Firebase Auth User type
// src/contexts/UserContext.tsx
import {
	type PropsWithChildren,
	createContext,
	useContext,
	useMemo,
} from "react";
import { useSigninCheck, useUser } from "reactfire";

// Define the shape of the context data
interface UserContextType {
	status: "loading" | "success" | "error";
	data: {
		signedIn: boolean;
		user: FirebaseUser | null; // This is the Firebase Auth user object
		// We can extend this later with DUA-specific profile data (e.g., from Firestore)
		// creativeProfile: Creative | null;
		// bookerProfile: Booker | null;
	};
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children }: PropsWithChildren) => {
	const { status: signInStatus, data: signInCheckResult } = useSigninCheck();
	const { data: firebaseUser } = useUser(); // Provides the Firebase Auth user

	const contextValue = useMemo(() => {
		if (signInStatus === "loading") {
			return {
				status: "loading",
				data: { signedIn: false, user: null },
			} as UserContextType;
		}
		if (signInStatus === "error" || !signInCheckResult) {
			return {
				status: "error",
				data: { signedIn: false, user: null },
			} as UserContextType;
		}
		return {
			status: "success",
			data: {
				signedIn: signInCheckResult.signedIn,
				user: firebaseUser, // The user object from Firebase Auth
			},
		} as UserContextType;
	}, [signInStatus, signInCheckResult, firebaseUser]);

	return (
		<UserContext.Provider value={contextValue}>{children}</UserContext.Provider>
	);
};

export const useUserContext = () => {
	const context = useContext(UserContext);
	if (context === undefined) {
		throw new Error("useUserContext must be used within a UserProvider");
	}
	return context;
};
