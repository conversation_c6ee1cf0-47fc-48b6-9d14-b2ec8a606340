"use client";

import { connectAuthEmulator, getAuth } from "firebase/auth";
import { connectFirestoreEmulator, getFirestore } from "firebase/firestore";
import { connectFunctionsEmulator, getFunctions } from "firebase/functions";
import { connectStorageEmulator, getStorage } from "firebase/storage";
import type { PropsWithChildren } from "react";
import {
	AuthProvider,
	FirebaseAppProvider,
	FirestoreProvider,
	FunctionsProvider,
	StorageProvider,
	useFirebaseApp,
} from "reactfire";
import {
	emulatorPorts,
	firebaseConfig,
	useEmulators,
} from "../lib/firebase/config";

export const FirebaseProvider = ({ children }: PropsWithChildren) => {
	return (
		<FirebaseAppProvider firebaseConfig={firebaseConfig}>
			<FirebaseProviderInner>{children}</FirebaseProviderInner>
		</FirebaseAppProvider>
	);
};

export const FirebaseProviderInner = ({ children }: PropsWithChildren) => {
	const app = useFirebaseApp();

	// Initialize Firebase services
	const auth = getAuth(app);
	const firestore = getFirestore(app);
	const functions = getFunctions(app, "europe-west3"); // DUA platform region
	const storage = getStorage(app);
	// Connect to emulators in development
	if (useEmulators && typeof window !== "undefined") {
		try {
			connectAuthEmulator(auth, `http://localhost:${emulatorPorts.auth}`, {
				disableWarnings: true,
			});
			connectFirestoreEmulator(firestore, "localhost", emulatorPorts.firestore);
			connectFunctionsEmulator(functions, "localhost", emulatorPorts.functions);
			connectStorageEmulator(storage, "localhost", emulatorPorts.storage);
		} catch (error) {
			// Emulators already connected
		}
	}

	return (
		<AuthProvider sdk={auth}>
			<FirestoreProvider sdk={firestore}>
				<FunctionsProvider sdk={functions}>
					<StorageProvider sdk={storage}>{children}</StorageProvider>
				</FunctionsProvider>
			</FirestoreProvider>
		</AuthProvider>
	);
};
