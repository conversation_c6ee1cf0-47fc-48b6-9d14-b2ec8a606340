import { functions } from "@/src/components/FirebaseComponents";
import { httpsCallable } from "firebase/functions";

/**
 * A generic helper function to call a Firebase Callable Function.
 *
 * @param functionName The name of the callable function to execute.
 * @param data The data payload to send to the function.
 * @returns A promise that resolves with the result of the function call.
 * @throws Throws an error if the function call fails, which can be caught
 *         in a try/catch block.
 */
export const callFirebaseFunction = async <T, R>(
	functionName: string,
	data: T,
): Promise<R> => {
	try {
		const callableFunction = httpsCallable<T, R>(functions, functionName);
		const result = await callableFunction(data);
		return result.data;
	} catch (error) {
		// Log the error for debugging purposes and re-throw it to be handled
		// by the calling code (e.g., in a UI component to show an error message).
		console.error(`Error calling Firebase function "${functionName}":`, error);
		throw error;
	}
};
