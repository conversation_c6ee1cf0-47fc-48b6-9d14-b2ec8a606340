import {
	type DocumentData,
	type FirestoreDataConverter,
	type PartialWithFieldValue,
	type QueryDocumentSnapshot,
	type SnapshotOptions,
	type Timestamp,
	type WithFieldValue,
	serverTimestamp,
} from "firebase/firestore";

// Base interface for all Firestore documents
export interface FirestoreDocument {
	id: string;
	createdAt: Timestamp;
	updatedAt: Timestamp;
}

// Generic Firestore converter factory
export const createFirestoreConverter = <
	T extends FirestoreDocument,
>(): FirestoreDataConverter<T> => {
	return {
		toFirestore: (
			modelObject: WithFieldValue<T> | PartialWithFieldValue<T>,
		): DocumentData => {
			// Assert modelObject to access its properties, acknowledging it might be partial.
			const data = modelObject as Partial<T> & {
				createdAt?: any;
				updatedAt?: any;
				id?: string;
			};
			const { id, ...dataToWrite } = data; // Exclude id if present

			const result: DocumentData = {
				...dataToWrite, // Spread the actual data fields
				updatedAt: serverTimestamp(), // Always set/update 'updatedAt'
			};

			// Handle 'createdAt':
			// If 'createdAt' is not present in the incoming data (dataToWrite),
			// it implies this might be a new document or 'createdAt' is not being explicitly set.
			// In such cases, set it to serverTimestamp().
			// If 'createdAt' is explicitly provided (even as undefined), this logic respects that.
			// Note: Firestore typically doesn't allow 'undefined' as a field value.
			// If 'createdAt' is already a FieldValue (e.g., serverTimestamp()), it will be preserved by the spread.
			if (!dataToWrite.hasOwnProperty("createdAt")) {
				result.createdAt = serverTimestamp();
			}
			// If dataToWrite.createdAt was explicitly 'undefined', it would have been spread.
			// To truly prevent 'undefined' fields if they are not desired, they should be
			// stripped from dataToWrite before spreading, or ensure modelObject doesn't contain them.
			// For now, this covers setting it if not provided at all.

			return result;
		},
		fromFirestore: (
			snapshot: QueryDocumentSnapshot<DocumentData>,
			options?: SnapshotOptions,
		): T => {
			const data = snapshot.data(options);
			return {
				id: snapshot.id,
				...data,
			} as T;
		},
	};
};

// Specific converters for type safety
export const userConverter = createFirestoreConverter<FirestoreDocument>();
export const creativeConverter = createFirestoreConverter<FirestoreDocument>();
export const bookerConverter = createFirestoreConverter<FirestoreDocument>();
export const projectConverter = createFirestoreConverter<FirestoreDocument>();
export const offerConverter = createFirestoreConverter<FirestoreDocument>();
export const portfolioItemConverter =
	createFirestoreConverter<FirestoreDocument>();
export const availabilityConverter =
	createFirestoreConverter<FirestoreDocument>();
export const collaborationConverter =
	createFirestoreConverter<FirestoreDocument>();
export const reviewConverter = createFirestoreConverter<FirestoreDocument>();
