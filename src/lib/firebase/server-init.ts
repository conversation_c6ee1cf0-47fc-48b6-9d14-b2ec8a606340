import { type App, cert, getApps, initializeApp } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";
import { getAuth } from "firebase-admin/auth";

// Path to your service account key file
// IMPORTANT: This file should NOT be committed to your public repository.
// It should be loaded from a secure location, like a secret manager or environment variable.
const serviceAccount = process.env.FIREBASE_SERVICE_ACCOUNT_KEY
	? JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)
	: require("../../../serviceAccountKey.json"); // Fallback for local dev

export const initializeServerApp = (): App => {
	const alreadyCreated = getApps();
	if (alreadyCreated.length > 0) {
		return alreadyCreated[0];
	}

	// Initialize the Firebase Admin SDK
	const app = initializeApp({
		credential: cert(serviceAccount),
	});

	return app;
};

export const adminApp = initializeServerApp();
export const adminDb = getFirestore(adminApp);
export const adminAuth = getAuth(adminApp);
