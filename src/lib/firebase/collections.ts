// Firebase Collection Names following DUA platform schema
export enum FirebaseCollections {
	// User Management
	USERS = "users",
	CREATIVES = "creatives",
	BOOKERS = "bookers",

	// Project & Offer Management
	PROJECTS = "projects",
	OFFERS = "offers",
	BOOKINGS = "bookings",

	// Portfolio & Media
	PORTFOLIO_ITEMS = "portfolioItems",

	// Financial Management
	PAYMENTS = "payments",
	BUDGET_ITEMS = "budgetItems",

	// Collaboration & Reviews
	COLLABORATIONS = "collaborations",
	REVIEWS = "reviews",

	// Availability & Scheduling
	AVAILABILITY = "availability",
	JOBS = "jobs",
}

// Subcollections for nested data
export enum FirebaseSubcollections {
	PORTFOLIO = "portfolio",
	AVAILABILITY_SLOTS = "availabilitySlots",
	MESSAGES = "messages",
	NOTIFICATIONS = "notifications",
}
