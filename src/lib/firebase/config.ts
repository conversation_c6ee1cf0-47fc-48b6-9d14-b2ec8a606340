import type { FirebaseOptions } from "firebase/app";

// WARNING: This is a temporary hardcoded configuration for debugging purposes.
// Do NOT commit this to version control with real credentials.
export const firebaseConfig: FirebaseOptions = {
	apiKey: "AIzaSyAR2vW70A7yX95L1RSUi2hRSuh9HU0FXqM",
	authDomain: "dream-unit-agency.firebaseapp.com",
	projectId: "dream-unit-agency",
	storageBucket: "dream-unit-agency.firebasestorage.app",
	messagingSenderId: "364954502659",
	appId: "1:364954502659:web:c0ea1247be97d8fdf13036",
	measurementId: "G-1000000000",
};

// Emulator configuration
export const useEmulators = process.env.NEXT_PUBLIC_USE_EMULATORS === "true";

export const emulatorPorts = {
	firestore: 8089,
	auth: 9099,
	functions: 5001,
	storage: 9199,
} as const;
