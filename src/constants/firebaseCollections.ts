/**
 * Defines the names of Firestore collections used in the DUA platform.
 * These names should align with the backend setup and `dua-firebase-endpoints.md`.
 */
export enum FirebaseCollections {
	USER_ROOT_ACCOUNT = "user-root-accounts",
	CREATIVES = "creatives",
	BOOKERS = "bookers",
	PROJECTS = "projects",
	OFFERS = "offers",
	COLLABORATIONS = "collaborations",
	PORTFOLIO_ITEMS = "portfolio-items",
	JOBS = "jobs",
	REVIEWS = "reviews",
	AVAILABILITY = "availability",
	PAYMENTS = "payments",
	INVOICES = "invoices",
	ESCROW_TRANSACTIONS = "escrow-transactions",
	ADMIN_USERS = "admin-users",
	// Add any other collection names as they are defined and needed
}

// Alternatively, as a const object if preferred over an enum:
// export const FirebaseCollections = {
//   USERS: 'users',
//   CREATIVES: 'creatives',
//   BOOKERS: 'bookers',
//   PROJECTS: 'projects',
//   OFFERS: 'offers',
//   PORTFOLIO_ITEMS: 'portfolioItems',
//   JOBS: 'jobs',
//   AVAILABILITY: 'availability',
// } as const;
