# ---- Base Node ----
FROM node:20-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable

WORKDIR /app

# ---- Dependencies ----
FROM base AS deps

# Copy workspace definition and root package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml .npmrc* ./
# Copy workspace packages - adjust if you have more under 'packages/*'
COPY component-library ./component-library/
# If you have other packages under a 'packages' directory, uncomment and adjust:
# COPY packages ./packages/

# Install all dependencies for all workspace packages and the root
RUN pnpm install --frozen-lockfile

# ---- Builder ----
FROM base AS builder
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/component-library ./component-library/
# If you copied a 'packages' dir in 'deps' stage, copy it here too:
# COPY --from=deps /app/packages ./packages/
COPY --from=deps /app/package.json ./package.json

# Declare build arguments for Firebase config
ARG NEXT_PUBLIC_API_KEY
ARG NEXT_PUBLIC_AUTH_DOMAIN
ARG NEXT_PUBLIC_PROJECT_ID
ARG NEXT_PUBLIC_STORAGE_BUCKET
ARG NEXT_PUBLIC_MESSAGING_SENDER_ID
ARG NEXT_PUBLIC_APP_ID

# Copy all other source files
# List specific files and directories to ensure a clean build context
COPY app ./app/
COPY components ./components/
COPY hooks ./hooks/
COPY lib ./lib/
COPY public ./public/
COPY src ./src/
COPY styles ./styles/

# Copy root configuration files AND root .tsx/.ts source files
COPY next.config.mjs tsconfig.json tailwind.config.ts postcss.config.mjs components.json .npmrc* ./
COPY landing-page.tsx creative-profile-page.tsx booker-profile-page.tsx search-creatives.tsx ./
COPY booker-profile-data.ts instagram-photo-grid-data.ts ./ 
# Add any other .ts or .tsx files that live at the root and are imported

ENV NODE_ENV=production
# Set the ENV variables from the ARG values
ENV NEXT_PUBLIC_API_KEY=$NEXT_PUBLIC_API_KEY
ENV NEXT_PUBLIC_AUTH_DOMAIN=$NEXT_PUBLIC_AUTH_DOMAIN
ENV NEXT_PUBLIC_PROJECT_ID=$NEXT_PUBLIC_PROJECT_ID
ENV NEXT_PUBLIC_STORAGE_BUCKET=$NEXT_PUBLIC_STORAGE_BUCKET
ENV NEXT_PUBLIC_MESSAGING_SENDER_ID=$NEXT_PUBLIC_MESSAGING_SENDER_ID
ENV NEXT_PUBLIC_APP_ID=$NEXT_PUBLIC_APP_ID

# Build the Next.js application
# This will use the 'standalone' output mode
RUN pnpm run build

# ---- Runner ----
FROM base AS runner
ENV NODE_ENV=production
# Set a default port, can be overridden by Cloud Run
ENV PORT=3000

WORKDIR /app

COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# The standalone output includes a server.js file
EXPOSE 3000
CMD ["node", "server.js"]
