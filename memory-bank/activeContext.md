# Active Context

## Current Goal: Backend Tasks & File Uploads

With the successful integration of live data on both the Creative and Booker profiles, the core frontend work of replacing mock data is complete. The next priority is to address the remaining backend tasks and implement file storage for portfolio images.

### Immediate Objectives

1.  **Implement Portfolio D&D Backend**: Create and deploy the `update_portfolio_items_order` Firebase function to persist the drag-and-drop reordering of a creative's portfolio.
2.  **Configure Firebase Storage**: Set up Firebase Storage with appropriate security rules for image uploads.
3.  **Implement Portfolio Image Upload**: Build the UI and logic for creatives to upload new images to their portfolio. This will involve creating a `create_portfolio_item` function that handles both the image upload and the creation of the Firestore document.
4.  **Remove Mock Data Files**: Once all live data integrations are complete and verified, delete all mock data files (e.g., `booker-profile-data.ts`, `instagram-photo-grid-data.ts`) and remove any remaining mock data logic from the components.

### Key Considerations

-   **Backend Dependency**: The frontend drag-and-drop feature is currently not saving its state. The backend function is critical to completing this feature.
-   **Storage Rules**: Security rules for Firebase Storage are crucial to ensure that users can only upload files to their own portfolio and that file types and sizes are restricted.

## Recent Changes & Discoveries

### Booker Profile Live Data COMPLETE
- **Profile Header**: The `BookerProfileHeader` now displays the booker's name and company from Firestore.
- **Offers Tab**: The "Offers" tab now uses the `useOffersByBooker` hook to display a live list of all offers sent by the booker.
- **Budget Tab**: The "Budget" tab now uses live data from the `projects` and `payments` collections, with data transformed to fit the UI.
- **Data Hooks Created**: The `useBookerDoc` and `usePaymentsByBooker` hooks were created and implemented.
- **Mock Data Removed**: All mock data related to the booker profile, offers, and budget has been removed from `booker-profile-page.tsx`.

## Next Steps & Priorities

### Future Phases
- **Full Mock Data Removal**: Remove all remaining mock data from the application.
- **Component Library Migration**: Re-evaluate migrating from Tailwind CSS to `dua-component-library` at a later stage.
- **Member Management**: Implement the "Members" tab functionality for bookers.
- **Notifications**: Implement a notification system for offers and other events.

## Active Development Considerations

### Migration Strategy
-   **File by File**: It's best to perform the migration on a file-by-file basis to make it easier to track changes and debug any issues that may arise.
-   **Workspace Setup**: The `dua-component-library` is a workspace package. Ensure that the local development server is correctly configured to handle workspace dependencies.
