# System Patterns - DUA Platform Architecture

## Application Architecture

### Next.js App Router Structure
```
/app
├── /booker/[id]         # Dynamic booker profile pages  
├── /creative/[id]       # Dynamic creative profile pages
├── /search-creatives    # Talent discovery interface
├── /search-projects     # Project discovery interface
├── layout.tsx           # Root layout with theme provider
└── page.tsx            # Landing page entry point
```

### Component Organization Pattern
```
/components
├── /creative-profile    # Creative-specific UI components
├── /booker-profile     # Booker-specific UI components  
├── /layout             # Shared layout components
├── /ui                 # Base UI component library
├── /utils              # Utility functions and helpers
├── /hooks              # Custom React hooks
└── types.ts           # Shared TypeScript interfaces
```

## Design Patterns

### User Type Routing Strategy
- **Dynamic Routes**: `[id]` parameter determines profile display
- **Type Detection**: User type inferred from data, not URL structure
- **Shared Components**: Common UI elements reused across user types
- **Role-based Views**: Different interfaces for Creatives vs Bookers

### State Management Patterns
- **Local Component State**: React useState for UI-specific state
- **Custom Hooks**: Extracted business logic for reusability
- **Modal Management**: Centralized modal state with custom hooks
- **Form Handling**: React Hook Form integration with validation

### Data Flow Architecture
```
Mock Data Files → TypeScript Interfaces → Component Props → UI Rendering
```

### Component Composition Strategy
- **Atomic Design Principles**: Atoms → Molecules → Organisms → Pages
- **Slot-based Layouts**: Flexible component composition with defined slots
- **Props Interface Consistency**: Standardized prop patterns across components
- **Forward Ref Support**: Proper ref forwarding for component library integration

## Key Technical Patterns

### Photo Grid Management
- **Dynamic Grid Layouts**: Responsive grid with size variants (standard, horizontal, vertical, large)
- **Drag & Drop Operations**: Photo reordering with position tracking
- **Merge/Split Operations**: Photo combination and separation functionality
- **Text Tile Integration**: Mixed content types (images + text) in unified grid

### Modal System Architecture
- **Modal State Hooks**: Centralized modal opening/closing logic
- **Dynamic Content**: Context-aware modal content based on user actions
- **Escape & Overlay Handling**: Consistent modal dismissal patterns
- **Focus Management**: Proper accessibility for modal interactions

### Custom Hook Patterns
- **Business Logic Extraction**: `usePhotoSelection`, `useTextEditing`, `useTileOperations`
- **Modal Management**: `useBookingModal`, `usePhotoDetailModal`, `useProjectModals`
- **Form State**: `useAddProjectForm`, `useReviewModal` for complex form workflows
- **Data Operations**: `useTileManagement` for CRUD operations on grid items

### File Organization Strategy
- **Feature-based Grouping**: Components grouped by user type (creative-profile, booker-profile)
- **Shared Utilities**: Common hooks, utilities, and types in dedicated directories
- **Mock Data Separation**: Business logic interfaces separate from presentation components
- **Type Safety**: Comprehensive TypeScript interfaces for all data structures

This architecture supports both rapid prototyping with mock data and future integration with real APIs while maintaining clean separation of concerns and reusable patterns.
