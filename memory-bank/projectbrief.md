# Project Brief - DUA Creative Marketplace Platform

## Overview
DUA is a comprehensive creative talent marketplace platform that connects creative professionals ("Creatives") with clients who hire them ("Booker<PERSON>"). The platform facilitates discovering, booking, managing, and paying creative professionals across various disciplines including photography, videography, modeling, makeup artistry, and more.

## Core Requirements & Goals

### Primary Platform Goals
- Create a seamless marketplace for creative talent discovery and booking
- Provide comprehensive profile management for both Creatives and Bookers
- Enable project management, payment processing, and collaboration tools
- Support portfolio showcasing with Instagram-like photo grid interfaces
- Facilitate real-time availability management and booking workflows

### Technical Migration Goals
- **Component Library Adoption**: Migrate from Tailwind CSS to `dua-component-library`
- **Design System Consistency**: Leverage the component library's design tokens and theming
- **Performance Optimization**: Reduce bundle size and improve runtime performance
- **Type Safety**: Enhance developer experience with TypeScript-first component approach
- **API Compatibility**: Maintain seamless transition from existing shadcn/ui components

## User Types & Core Personas

### Creatives (Artists)
Primary user type representing talent providers:
- **Photographers**: Portrait, fashion, commercial, event photography
- **Videographers**: Documentary, commercial, music videos, event coverage  
- **Models**: Fashion, commercial, runway modeling
- **Makeup Artists**: Editorial, bridal, SFX makeup
- **Hair Stylists**: Editorial, commercial, bridal styling
- **Fashion Stylists**: Wardrobe and fashion coordination
- **Set Designers**: Creative set and environment design
- **Other Specialists**: Prop masters, lighting technicians, sound engineers

### Bookers (Clients)
Secondary user type representing talent hirers:
- **Creative Directors**: Agency and brand creative leadership
- **Marketing Teams**: Corporate marketing departments
- **Event Planners**: Event and wedding coordination
- **Production Companies**: Film, video, and content production
- **Brand Managers**: In-house brand creative teams

## Key Platform Features

### For Creatives
- **Portfolio Management**: Instagram-style photo grid with drag-and-drop organization
- **Availability Calendar**: Real-time scheduling and booking management
- **Assignment Tracking**: Job status, upcoming bookings, payment tracking
- **Income Dashboard**: Payment history, pending invoices, financial analytics
- **Profile Optimization**: Skills, specialties, location, bio management

### For Bookers  
- **Project Management**: Create, manage, and track creative projects
- **Talent Discovery**: Search and filter creatives by type, location, availability
- **Budget Management**: Project budgets, payment tracking, expense allocation
- **Offer Management**: Send offers, track responses, manage negotiations
- **Collaboration History**: Past projects, reviews, repeat hiring workflows

### Shared Platform Features
- **Real-time Messaging**: Direct communication between Creatives and Bookers
- **Payment Processing**: Secure payment handling with escrow protection
- **Review System**: Mutual rating and feedback system
- **Geographic Search**: Location-based talent discovery
- **Mobile Responsive**: Full functionality across devices

## Business Domain Language

### Core Terminology
- **Creative/Artist**: Talent provider (used interchangeably)
- **Booker/Client**: Talent hirer (used interchangeably)  
- **Project**: A job/assignment posted by Bookers
- **Assignment/Job**: Work accepted by Creatives
- **Offer**: Formal work proposal from Booker to Creative
- **Booking**: Confirmed work arrangement
- **Portfolio**: Creative's work showcase
- **Profile**: User account and information display

### Project Workflow States
- **Planning**: Project in development, not yet ready for talent
- **Active**: Project open for applications/offers
- **Completed**: Project finished and delivered
- **Archived**: Historical project records

### Payment & Financial Terms
- **Budget**: Total project allocation
- **Rate**: Creative's pricing structure
- **Invoice**: Payment request from Creative
- **Escrow**: Protected payment holding
- **Payout**: Released payment to Creative

## Success Metrics
- **User Engagement**: Profile completion rates, portfolio uploads, active bookings
- **Marketplace Health**: Successful matches, repeat collaborations, payment completion
- **Platform Growth**: User acquisition, geographic expansion, category diversity
- **Technical Performance**: Page load times, component library adoption rate

This platform bridges the gap between creative talent and clients, providing professional tools for discovery, collaboration, and business management in the creative services industry.
