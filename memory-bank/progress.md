# Progress: Frontend & Backend Integration

This document tracks the integration of the Next.js frontend with the Firebase backend. All data retrieval (GET operations) are implemented on the frontend using `reactfire` for real-time updates. All data mutations (POST operations) are handled by calling dedicated Firebase Cloud Functions.

## Integration Status

### ✅ Completed Integrations (Done)

**Core & Authentication**
- **User Signup & Login**: Landing page "Join" modal successfully creates and signs in a user in Firebase Auth.
- **Two-Step Profile Creation**:
    - Booker signup creates an auth user, then redirects to a dedicated page to collect and submit full profile details to the `create_booker` endpoint.
    - Creative signup creates auth user and profile in one step via `create_creative` endpoint.
- **User Type Detection**: After login, the app correctly identifies the user as a 'creative' or 'booker' and redirects to the appropriate profile page.

**Creative Profile**
- **Data Reading**: The entire Creative Profile page fetches its data (profile header, portfolio, assignments, offers) in real-time from Firestore using `reactfire` hooks.
- **Portfolio C/U/D**:
    - **Create**: "Add Tile" button calls the `create_portfolio_item` endpoint.
    - **Update**: "Convert to Text/Image" buttons call the `update_portfolio_item` endpoint.
    - **Delete**: "Delete Selected" button calls the `delete_portfolio_item` endpoint.
- **Portfolio Drag-and-Drop (Frontend)**: The UI for reordering portfolio items with `dnd-kit` is complete. The frontend calls the `update_portfolio_items_order` endpoint on drag end.
- **Availability Calendar**: The calendar fetches and updates the creative's availability via the `set_creative_availability` endpoint.
- **Offer Lifecycle (Partial)**: The UI for sending offers (`send_offer`) and viewing them is complete for both Bookers and Creatives.
- **Collaboration UI**: Both Booker and Creative profiles display active and past collaborations based on live data from the `collaborations` collection.
- **Offer Acceptance/Rejection**: Creatives can accept or decline offers, triggering the `accept_offer` and `decline_offer` endpoints.

**Booker Profile**
- **Data Reading**: The entire Booker Profile page fetches its data (profile header, projects, offers, budget, collaborations) in real-time from Firestore using `reactfire` hooks.
- **Project C/D**:
    - **Create**: "Add Project" modal calls the `create_project` endpoint.
    - **Delete**: "Delete Project" button calls the `delete_project` endpoint.

**Offers & Collaborations**
- **Collaboration UI**: Both Booker and Creative profiles display active and past collaborations based on live data from the `collaborations` collection.

### ⏳ In Progress Integrations (Doing)

- **Portfolio Drag-and-Drop (Backend)**:
    - **Task**: Implement and deploy the `update_portfolio_items_order` Firebase Function.
    - **Status**: The frontend is ready and calling this endpoint, but the endpoint itself needs to be created. This is the highest priority backend task.

### 📝 Planned Integrations (Planning)

**High Priority**
- **Portfolio Image Uploads**:
    - Configure Firebase Storage with appropriate security rules.
    - Implement the frontend UI for uploading images.
    - Update the `create_portfolio_item` or create a new endpoint to handle image uploads to Storage and create the corresponding Firestore document.
- **Full Mock Data Removal**:
    - Delete all mock data files (`/booker-profile-data.ts`, etc.).
    - Remove any remaining mock data logic from components (e.g., `Members` tab on Booker Profile).

**Medium Priority**
- **Full Offer & Job Lifecycle**:
    - Implement UI and endpoint calls for all offer state transitions (e.g., `accept_offer`, `decline_offer`, `withdraw_offer`).
    - Implement UI and endpoint calls for job lifecycle (`confirm_job`, `complete_job`, etc.).
- **Firestore Security Rules**:
    - Write and deploy robust security rules for all collections to ensure users can only read/write their own data.

**Low Priority / Future Features**
- **Booker "Members" Feature**:
    - Implement backend logic and UI for inviting/managing team members for a Booker account.
- **Notification System**:
    - Design and implement a system for in-app and/or email notifications for key events like new offers.

---

## Technical Debt & Known Issues

### Technical Debt
- **Data-Mapping Layers**: The Creative and Booker Profile pages use mapping to adapt live data to old UI types. This should be refactored in the future by updating the UI components to use the live types directly.
- **Component Library Migration (Terminated)**: The migration to `dua-component-library` was halted. The codebase contains a mix of `shadcn/ui` components and a few remnants of the DUA library. This should be standardized.

### Known Issues
- **Loading/Error States**: UI has inconsistent handling for loading and error states during data fetching.
- **Image Optimization**: Portfolio images currently lack a proper optimization strategy.
