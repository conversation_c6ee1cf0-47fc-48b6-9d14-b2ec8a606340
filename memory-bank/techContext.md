# Technical Context - DUA Platform Technology Stack

## Frontend Technology Stack

### Core Framework & Language
- **Next.js 15.2.4**: App Router with React Server Components support
- **React 19**: Latest React with concurrent features and modern hooks
- **TypeScript 5**: Strict type checking for enhanced developer experience
- **Styled Components 6.1.18**: CSS-in-JS styling solution
- **Firebase**: (Intended for backend services, auth, database)
- **ReactFire**: (Intended for Firebase integration in React)

### UI Component Architecture
- **DUA Component Library**: Custom component library built on styled-components
  - Located in `/component-library` workspace package
  - GACO architecture (General, Atomic, Composable, Orchestrated)
  - Storybook documentation and development environment
  - API compatibility with shadcn/ui components

### Component Library Stack
- **Radix UI**: Headless component primitives for accessibility
- **Class Variance Authority**: Type-safe component variant handling
- **Tailwind Merge**: Utility class merging for component composition
- **Lucide React**: Icon library with consistent design language

### Form & Data Handling
- **React Hook Form 7.54.1**: Performant form state management
- **Zod 3.24.1**: TypeScript-first schema validation
- **@hookform/resolvers**: Integration between React Hook Form and Zod
- **Date-fns**: Date manipulation and formatting utilities

### Development & Build Tools
- **Vite**: Component library build tool with optimized bundling
- **PostCSS**: CSS processing with Tailwind CSS integration
- **PNPM**: Package manager with workspace support
- **ESLint**: Code quality and consistency enforcement

## Project Structure & Dependencies

### Workspace Configuration
```json
{
  "name": "my-v0-project",
  "dependencies": {
    "dua-component-library": "workspace:*"
  }
}
```

### Key Libraries Integration
- **Embla Carousel**: Touch-friendly carousel components
- **React Resizable Panels**: Dynamic layout management
- **Recharts**: Data visualization and chart components
- **Sonner**: Toast notification system
- **Vaul**: Drawer/sheet component implementation

## Migration Strategy

### From Tailwind to DUA Component Library
- **Current State**: Application uses Tailwind CSS classes throughout components
- **Target State**: Replace Tailwind classes with DUA component library components
- **API Compatibility**: DUA library designed to be drop-in replacement for shadcn/ui
- **Theming Support**: Built-in theme provider with light/dark mode capabilities

### Technical Constraints
- **Bundle Size**: Component library should reduce overall bundle size
- **Performance**: Styled-components should provide better runtime performance
- **Type Safety**: Enhanced TypeScript experience with component library
- **Backwards Compatibility**: Gradual migration path without breaking existing functionality

### Development Environment
- **Hot Reload**: Fast development experience with Next.js and Vite
- **Component Development**: Storybook environment for component library
- **Type Checking**: Strict TypeScript configuration for better DX
- **Code Quality**: ESLint and Prettier for consistent code style

## Data Management Strategy

### Mock Data Architecture
- **Static Data Files**: TypeScript files with comprehensive mock data
- **Type Definitions**: Shared interfaces across components and data
- **Business Logic**: Mock data reflects real-world business scenarios
- **Future API Integration**: Data structure designed for easy API migration

### Firebase Integration Readiness
- **Goal**: Structure frontend to seamlessly adopt Firebase services.
- **ReactFire Patterns**: Adopt ReactFire hooks (`useUser`, `useFirestoreDocData`, `useFirestoreCollectionData`, `useFunctions`) for data fetching and function calls.
- **Mock Data Bridge**: Initially, ReactFire-patterned hooks will read from existing mock data sources.
- **Typed Converters**: Implement Firestore data converters for type safety, similar to GACO patterns.
- **Placeholder Function Hooks**: Frontend hooks for Firebase Cloud Functions will be created as placeholders, simulating calls and returning mock responses.
- **Provider Setup**: Core Firebase providers (`FirebaseAppProvider`, `AuthProvider`, `FirestoreProvider`, etc.) will be set up for structural readiness.

### State Management Approach
- **Local State**: React useState for component-level state
- **Custom Hooks**: Business logic extraction for reusability
- **Context API**: Theme and global state management
- **No External State Library**: Simplified approach for prototype phase

This technical foundation supports rapid development while maintaining professional code quality and preparing for future scaling needs.
