# Dream Unit Agency (DUA) Platform API Documentation

This documentation provides an overview of all available cloud functions for the Dream Unit Agency (DUA) Platform, managed by the Gaco Framework. These functions support the frontend application.

**Important Note:** This document reflects the current DUA platform architecture. Previous documentation related to a "Gaco Platform" with entities like Stores, Producers, and Products is now outdated for the active API.

## Table of Contents
- [Authentication](#authentication)
- [User Account Management](#user-account-management)
- [Booker Management](#booker-management)
- [Creative Management](#creative-management)
- [Offer Management](#offer-management)
- [Project Management](#project-management)
- [Portfolio Management](#portfolio-management)
- [Job Management](#job-management)
- [Availability Management](#availability-management)
- [Event-Driven Functions](#event-driven-functions)
- [Common Response Format](#common-response-format)
- [Error Handling](#error-handling)
- [Best Practices](#best-practices)
- [Additional Information](#additional-information)

## Authentication

All callable cloud functions require user authentication. The user must be logged in via Firebase Authentication. The Gaco framework is configured with `enable_auth_check=True`.

```typescript
// Example of calling a Gaco-managed cloud function with authentication
// (Assuming Gaco uses standard Firebase callable functions)
import { getFunctions, httpsCallable } from "firebase/functions";

const functions = getFunctions();
// Replace 'your_function_name' with the actual function name, e.g., 'create_booker'
const callableFunction = httpsCallable(functions, 'your_function_name');

// Example request data (NEEDS TO BE DEFINED PER FUNCTION)
const requestData = { /* ... your data ... */ };

try {
  const result = await callableFunction(requestData);
  // Process result.data
} catch (error) {
  // Handle error
  console.error("Cloud function call failed:", error);
}
```
If authentication fails, the Gaco framework is configured to return:
```json
{
  "success": false, // Or structure defined by Gaco framework for auth errors
  "message": "User must be authenticated" // As per auth_error_message in main.py
  // Potentially a specific error code
}
```

## User Account Management

Manages user root accounts within the DUA platform.

### `on_update_user_root_account`

This function is likely an **event-driven background function** (e.g., triggered by Firestore document updates in the `user-root-accounts` collection or by Firebase Authentication events). It is not typically called directly by the frontend.

*   **Type:** Event-Driven (e.g., Firestore Trigger, Firebase Auth Trigger)
*   **Purpose:** Handles logic upon the creation or update of a user root account. Specific actions performed by this function need to be documented by the backend team.
*   **Modules:** Logic resides in `dua_cloud_functions.user_root_account`.

## Booker Management

Endpoints for managing booker profiles.

### Create Booker

Creates a new booker profile in the system.

*   **Function:** `create_booker`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/create_booker` (Replace `YOUR_PROJECT_ID`)
*   **Region:** `europe-west3` (Gaco default)
*   **Memory:** `1GB` (Gaco default)
*   **Timeout:** `60 seconds` (Gaco default)
*   **Modules:** Logic resides in `dua_cloud_functions.booker_manager_endpoints`.

**Request Format:**
```typescript
interface CreateBookerRequest {
  data: {
    // TODO: Define the request payload for creating a booker
    // Example:
    // displayName: string;
    // email: string;
    // ... other booker properties
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface CreateBookerResponse extends CommonResponse {
  data?: {
    bookerId?: string; // ID of the created booker
    // TODO: Define other potential data returned
  };
}
```

### Delete Booker

Deletes an existing booker profile from the system.

*   **Function:** `delete_booker`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/delete_booker` (Replace `YOUR_PROJECT_ID`)
*   **Region:** `europe-west3` (Gaco default)
*   **Memory:** `1GB` (Gaco default)
*   **Timeout:** `60 seconds` (Gaco default)
*   **Modules:** Logic resides in `dua_cloud_functions.booker_manager_endpoints`.

**Request Format:**
```typescript
interface DeleteBookerRequest {
  data: {
    bookerId: string; // ID of the booker to delete
    // TODO: Define any other parameters, e.g., hardDelete flag
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface DeleteBookerResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned, e.g., status of related entities
  };
}
```

## Creative Management

Endpoints for managing creative profiles.

### Create Creative

Creates a new creative profile in the system.

*   **Function:** `create_creative`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/create_creative` (Replace `YOUR_PROJECT_ID`)
*   **Region:** `europe-west3` (Gaco default)
*   **Memory:** `1GB` (Gaco default)
*   **Timeout:** `60 seconds` (Gaco default)
*   **Modules:** Logic resides in `dua_cloud_functions.creative_manager_endpoints`.

**Request Format:**
```typescript
interface CreateCreativeRequest {
  data: {
    // TODO: Define the request payload for creating a creative
    // Example:
    // displayName: string;
    // talentCategory: string;
    // portfolioUrl?: string;
    // ... other creative properties
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface CreateCreativeResponse extends CommonResponse {
  data?: {
    creativeId?: string; // ID of the created creative
    // TODO: Define other potential data returned
  };
}
```

### Delete Creative

Deletes an existing creative profile from the system.

*   **Function:** `delete_creative`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/delete_creative` (Replace `YOUR_PROJECT_ID`)
*   **Region:** `europe-west3` (Gaco default)
*   **Memory:** `1GB` (Gaco default)
*   **Timeout:** `60 seconds` (Gaco default)
*   **Modules:** Logic resides in `dua_cloud_functions.creative_manager_endpoints`.

**Request Format:**
```typescript
interface DeleteCreativeRequest {
  data: {
    creativeId: string; // ID of the creative to delete
    // TODO: Define any other parameters
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface DeleteCreativeResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned
  };
}
```

## Offer Management

Endpoints for managing the lifecycle of offers between bookers and creatives. All functions in this section have the following default configurations from Gaco, unless specified otherwise:
*   **Region:** `europe-west3`
*   **Memory:** `1GB`
*   **Timeout:** `60 seconds`
*   **Modules:** Logic resides in `dua_cloud_functions.booking_manager_endpoints`.

### Create Draft Offer

Creates a new draft offer.

*   **Function:** `create_draft_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/create_draft_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CreateDraftOfferRequest {
  data: {
    bookerId: string;
    creativeId: string;
    // TODO: Define offer details (job description, rate, dates, terms, etc.)
    // Example:
    // title: string;
    // proposedRate: number;
    // currency: string;
    // startDate: string; // ISO Date
    // endDate?: string; // ISO Date
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface CreateDraftOfferResponse extends CommonResponse {
  data?: {
    offerId?: string; // ID of the created draft offer
    // TODO: Define other potential data returned
  };
}
```

### Submit Offer to Creative

Submits a draft offer to a creative for review.

*   **Function:** `submit_offer_to_creative`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/submit_offer_to_creative` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface SubmitOfferToCreativeRequest {
  data: {
    offerId: string; // ID of the draft offer to submit
    // TODO: Define any additional submission details if necessary
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Creative Respond to Offer

Allows a creative to respond to an offer (e.g., accept, reject, negotiate).

*   **Function:** `creative_respond_to_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/creative_respond_to_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CreativeRespondToOfferRequest {
  data: {
    offerId: string;
    responseType: "ACCEPT" | "REJECT" | "NEGOTIATE"; // Example response types
    // TODO: Define payload for negotiation (e.g., counterOfferDetails, comments)
    // counterOfferDetails?: { /* ... */ };
    // comments?: string;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Booker Respond to Negotiation

Allows a booker to respond to a creative's negotiation proposal.

*   **Function:** `booker_respond_to_negotiation`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/booker_respond_to_negotiation` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface BookerRespondToNegotiationRequest {
  data: {
    offerId: string;
    responseType: "ACCEPT_NEGOTIATION" | "REJECT_NEGOTIATION" | "COUNTER_PROPOSAL"; // Example
    // TODO: Define payload for counter-proposal or acceptance details
    // comments?: string;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Update Draft Offer

Updates details of an offer that is still in a draft state.

*   **Function:** `update_draft_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/update_draft_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface UpdateDraftOfferRequest {
  data: {
    offerId: string;
    // TODO: Define updatable fields for a draft offer
    // (similar to CreateDraftOfferRequest, but all fields optional)
    // Example:
    // title?: string;
    // proposedRate?: number;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Cancel Offer

Cancels an offer (rules for when an offer can be cancelled should be defined).

*   **Function:** `cancel_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/cancel_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CancelOfferRequest {
  data: {
    offerId: string;
    reason?: string; // Optional reason for cancellation
    // TODO: Define who can cancel (Booker, Creative) and under what conditions
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Activate Offer

Activates an offer, typically after all parties have agreed. This might signify the booking is confirmed.

*   **Function:** `activate_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/activate_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface ActivateOfferRequest {
  data: {
    offerId: string;
    // TODO: Define any additional details required for activation
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

### Complete Offer

Marks an offer as completed, typically after the work/event has occurred.

*   **Function:** `complete_offer`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/complete_offer` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CompleteOfferRequest {
  data: {
    offerId: string;
    // TODO: Define any details related to completion (e.g., feedback, payment confirmation reference)
    // completionNotes?: string;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).

## Project Management

Endpoints for managing project profiles.

### Create Project

Creates a new project profile in the system.

*   **Function:** `create_project`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/create_project` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CreateProjectRequest {
  data: {
    // TODO: Define the request payload for creating a project
    // Example from FE `Project` interface
    // title: string;
    // description: string;
    // bookerId: string;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface CreateProjectResponse extends CommonResponse {
  data?: {
    projectId?: string; // ID of the created project
  };
}
```

### Delete Project

Deletes an existing project profile from the system.

*   **Function:** `delete_project`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/delete_project` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface DeleteProjectRequest {
  data: {
    projectId: string; // ID of the project to delete
    // TODO: Define any other parameters
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface DeleteProjectResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned
  };
}
```

## Portfolio Management

Endpoints for managing portfolio items.

### Create Portfolio Item

Creates a new portfolio item in the system.

*   **Function:** `create_portfolio_item`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/create_portfolio_item` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CreatePortfolioItemRequest {
  data: {
    // TODO: Define the request payload for creating a portfolio item
    // Example:
    // title: string;
    // description: string;
    // ... other portfolio item properties
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface CreatePortfolioItemResponse extends CommonResponse {
  data?: {
    portfolioItemId?: string; // ID of the created portfolio item
    // TODO: Define other potential data returned
  };
}
```

### Delete Portfolio Item

Deletes an existing portfolio item from the system.

*   **Function:** `delete_portfolio_item`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/delete_portfolio_item` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface DeletePortfolioItemRequest {
  data: {
    portfolioItemId: string; // ID of the portfolio item to delete
    // TODO: Define any other parameters
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface DeletePortfolioItemResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned
  };
}
```

### Update Portfolio Item

Updates details of an existing portfolio item.

*   **Function:** `update_portfolio_item`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/update_portfolio_item` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface UpdatePortfolioItemRequest {
  data: {
    portfolioItemId: string;
    // TODO: Define updatable fields for a portfolio item
    // (similar to CreatePortfolioItemRequest, but all fields optional)
    // Example:
    // title?: string;
    // description?: string;
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface UpdatePortfolioItemResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned
  };
}
```

### Update Portfolio Items Order

Updates the order of portfolio items.

*   **Function:** `update_portfolio_items_order`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/update_portfolio_items_order` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface UpdatePortfolioItemsOrderRequest {
  data: {
    portfolioItemIds: string[]; // Array of portfolio item IDs in the desired order
    // TODO: Define any other parameters
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface UpdatePortfolioItemsOrderResponse extends CommonResponse {
  data?: {
    // TODO: Define potential data returned
  };
}
```

### Get Portfolio Items

Retrieves a list of portfolio items.

*   **Function:** `get_portfolio_items`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/get_portfolio_items` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface GetPortfolioItemsRequest {
  data: {
    // TODO: Define any parameters for filtering or pagination
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface GetPortfolioItemsResponse extends CommonResponse {
  data?: {
    portfolioItems?: {
      id: string;
      title: string;
      description: string;
      // ... other portfolio item properties
    }[];
    // TODO: Define other potential data returned
  };
}
```

### Get Portfolio Item Details

Retrieves details of a specific portfolio item.

*   **Function:** `get_portfolio_item_details`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/get_portfolio_item_details` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface GetPortfolioItemDetailsRequest {
  data: {
    portfolioItemId: string; // ID of the portfolio item to retrieve details for
    // TODO: Define any other parameters
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface GetPortfolioItemDetailsResponse extends CommonResponse {
  data?: {
    portfolioItem?: {
      id: string;
      title: string;
      description: string;
      // ... other portfolio item properties
    };
    // TODO: Define other potential data returned
  };
}
```

## Job Management

Endpoints for managing the lifecycle of jobs after an offer is accepted.

**Integration Note:** When a job is confirmed via the `confirm_job` endpoint, the system automatically creates a corresponding "unavailable" time slot in the creative's availability calendar, linked via the new `jobId`.

### Confirm Job

Converts an activated offer into a confirmed job and blocks the creative's calendar.

*   **Function:** `confirm_job`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/confirm_job` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface ConfirmJobRequest {
  data: {
    offerId: string; // The ID of the offer to confirm
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format). The `data` field will contain the new Job object.
```typescript
interface Job {
  jobId: string;
  offerId: string;
  creativeId: string;
  status: 'confirmed' | 'completed' | 'cancelled';
  startTime: string; // ISO 8601 Date
  endTime: string;   // ISO 8601 Date
  createdAt: string; // ISO 8601 Date
  updatedAt: string; // ISO 8601 Date
}
```

### Complete Job

Marks a confirmed job as completed.

*   **Function:** `complete_job`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/complete_job` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface CompleteJobRequest {
  data: {
    jobId: string; // The ID of the job to complete
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format). The `data` field will contain the updated Job object.

## Availability Management

Endpoints for managing a creative's availability calendar.

### Set Creative Availability

Sets or updates the list of unavailable time slots for the currently authenticated creative on a specific date. If new slots overlap with existing ones, they will be merged.

*   **Function:** `set_creative_availability`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/set_creative_availability` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface TimeSlot {
  startTime: string; // ISO 8601 Date
  endTime: string;   // ISO 8601 Date
  jobId?: string;    // Optional: Only used by the system for job-related blocks
}

interface SetCreativeAvailabilityRequest {
  data: {
    date: string;       // "YYYY-MM-DD"
    slots: TimeSlot[];
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format). The `data` field will contain the final, merged list of unavailable slots for that date.

### Get Creative Availability

Retrieves all unavailable time slots for a specified creative within a given date range.

*   **Function:** `get_creative_availability`
*   **Endpoint:** `https://europe-west3-YOUR_PROJECT_ID.cloudfunctions.net/get_creative_availability` (Replace `YOUR_PROJECT_ID`)

**Request Format:**
```typescript
interface GetCreativeAvailabilityRequest {
  data: {
    creativeId: string;
    startDate: string;  // "YYYY-MM-DD"
    endDate: string;    // "YYYY-MM-DD"
  }
}
```
**Response Format:** See [Common Response Format](#common-response-format).
```typescript
interface AvailabilityForDate {
  date: string; // "YYYY-MM-DD"
  unavailableSlots: TimeSlot[];
}

interface GetCreativeAvailabilityResponse extends CommonResponse {
  data?: {
    availability: AvailabilityForDate[];
  }
}
```

## Event-Driven Functions

These functions are triggered by events within Firebase or other integrated services, not called directly by the frontend.

*   **`on_update_user_root_account`**: See [User Account Management](#user-account-management).

*(If other background/event-driven functions are part of the Gaco setup or `dua_cloud_functions`, they should be listed here by the backend team.)*

## Common Response Format

Most Gaco-exposed functions are expected to return responses in a consistent JSON format:

```typescript
interface CommonResponse {
  success: boolean; // True if the operation was successful, false otherwise
  message: string;  // A human-readable message describing the outcome
  code: number;     // An HTTP-like status code (e.g., 200, 201, 400, 401, 500)
  data?: any;      // Optional payload containing requested data or operation results
                  // The structure of 'data' will vary per function and outcome.
}
```
**Example Success Response:**
```json
{
  "success": true,
  "message": "Operation completed successfully.",
  "code": 200,
  "data": {
    "some_key": "some_value"
  }
}
```

**Example Error Response (e.g., Validation Error):**
```json
{
  "success": false,
  "message": "Invalid input: 'email' field is required.",
  "code": 400,
  "data": {
    "field_errors": [
      { "field": "email", "error": "Required" }
    ]
  }
}
```
**Note:** The exact structure of `data` in error responses, especially for validation, needs to be consistently defined by the backend team.

## Error Handling

### Common Error Codes

*   **401 Unauthorized:** User is not authenticated. (Handled by Gaco framework as per `auth_error_message`).
*   **400 Bad Request:** The request was malformed, or validation of the input data failed (e.g., missing required fields, invalid data types). The `message` field and potentially the `data` field will provide more details.
*   **403 Forbidden:** User is authenticated but does not have permission to perform the requested action.
*   **404 Not Found:** The requested resource (e.g., a specific Booker, Creative, or Offer ID) does not exist.
*   **422 Unprocessable Entity:** The request was well-formed but could not be processed due to semantic errors (e.g., trying to activate an offer that isn't in an activatable state).
*   **500 Internal Server Error:** An unexpected error occurred on the server.

Frontend clients should handle these error codes appropriately, providing user-friendly feedback.

## Best Practices

1.  **Define Request/Response Payloads:** **Crucially, the TypeScript interfaces for request and response payloads for each function must be clearly defined, versioned, and shared with the frontend team.** This document currently uses placeholders (e.g., `// TODO: Define ...`).
2.  **Error Handling:** Always implement robust error handling in your frontend code to manage both expected (e.g., validation errors) and unexpected (e.g., network issues, 500 errors) responses.
3.  **Loading States:** Display loading indicators during cloud function calls to provide feedback to the user.
4.  **Idempotency:** For operations that modify data, understand if they are idempotent and design frontend interactions accordingly (e.g., preventing duplicate submissions).
5.  **User Feedback:** Provide clear and concise feedback to the user based on the function's response (both success and error messages).
6.  **Environment Configuration:** Ensure your frontend is configured to call the correct Firebase project and function endpoints for your environment (development, staging, production).

## Additional Information

*   **Region:** All DUA platform functions configured via Gaco default to `europe-west3`.
*   **Memory:** Default memory is `1GB` (options.MemoryOption.GB_1) per Gaco config. This can be overridden per function if needed.
*   **Timeout:** Default timeout is `60 seconds` per Gaco config. This can be overridden.
*   **Framework:** The backend leverages the `gaco_framework` for managing and configuring Cloud Functions.
*   **Source Modules:** Business logic for the endpoints is primarily located in the `