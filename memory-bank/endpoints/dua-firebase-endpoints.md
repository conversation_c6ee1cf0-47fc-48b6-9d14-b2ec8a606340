# DUA Platform - Firebase Backend Endpoints (MVP)

## Overview

This document outlines the minimal Firebase backend endpoints needed for the DUA platform MVP. Communication will be handled via email notifications rather than an internal chat system.

## Complete Creative User Flow

This section documents the end-to-end flow for a creative user from registration to receiving payment, demonstrating how to achieve this flow by calling the available endpoints.

### Flow Overview
**START**: Creative user registration  
**END**: Invoice generated and sent to booker

### Step-by-Step Flow Implementation

#### 1. Creative Registration and Profile Setup

**Step 1.1: Create Creative Profile**
```typescript
// Endpoint: create_creative
const createCreativeRequest = {
  data: {
    display_name: "<PERSON> Photographer",
    story: "Professional photographer with 5 years experience",
    instagram: "john_photographer",
    phone_number: "+1234567890",
    creative_type: ["PHOTOGRAPHER", "VIDEOGRAPHER"]
  }
}
```

**Step 1.2: Admin Accepts Creative (Backend Process)**
```typescript
// Endpoint: accept_creative (Admin-only function)
const acceptCreativeRequest = {
  data: {
    creative_id: "creative_id_from_step_1"
  }
}
// This sets verified_by_dua claim to true in Firebase Auth
```

#### 2. Pricing Setup

**Step 2.1: Set Regular Pricing**
```typescript
// Endpoint: set_creative_price (for each creative type)
const setPriceRequest = {
  data: {
    creative_id: "creative_id",
    creative_type: "PHOTOGRAPHER", // Repeat for each type
    price: 100,
    currency: "EUR",
    rate_type: "HOURLY"
  }
}
```

**Step 2.2: Set Discount Pricing**
```typescript
// Endpoint: set_creative_discount_price (for each creative type)
const setDiscountPriceRequest = {
  data: {
    creative_id: "creative_id",
    rate_type: "HOURLY",
    above_rate: 10, // If booked more than 10 hours
    discount_percentage: 10, // 10% discount
    creative_type: "PHOTOGRAPHER"
  }
}
```

#### 3. Portfolio Management

**Step 3.1: Create Portfolio Items**
```typescript
// Endpoint: create_portfolio_item
const createPortfolioRequest = {
  data: {
    creative_id: "creative_id",
    data: {
      type: "image",
      imageUrl: "gs://bucket/portfolio/image1.jpg",
      alt: "Wedding photography sample",
      size: "standard",
      position: 1
    }
  }
}
```

**Step 3.2: Update Portfolio Order**
```typescript
// Endpoint: update_portfolio_items_order
const updateOrderRequest = {
  data: {
    creative_id: "creative_id",
    data: {
      ordered_item_ids: ["item_1", "item_2", "item_3"]
    }
  }
}
```

#### 4. Offer Management Flow

**Step 4.1: Booker Creates and Submits Offer**
```typescript
// Endpoint: create_draft_offer
const createOfferRequest = {
  data: {
    booker_id: "booker_id",
    creative_id: "creative_id",
    project_id: "project_id",
    offer_start_date: "2024-03-15",
    offer_end_date: "2024-03-16",
    creative_type: "PHOTOGRAPHER",
    rate_type: "HOURLY"
  }
}

// Endpoint: submit_offer_to_creative
const submitOfferRequest = {
  data: {
    offer_id: "offer_id_from_draft"
  }
}
```

**Step 4.2: Creative Responds to Offer**
```typescript
// Endpoint: creative_respond_to_offer
const respondToOfferRequest = {
  data: {
    offer_id: "offer_id",
    response_type: "ACCEPT", // or "REJECT", "NEGOTIATE"
    comments: "LGTM! Approved."
  }
}
// This sets offer status to ACTIVE when approved
```

#### 5. Job Completion Flow

**Step 5.1: Booker Marks Job Complete**
```typescript
// Endpoint: complete_offer
const bookerCompleteRequest = {
  data: {
    offer_id: "offer_id",
    entity_role: "BOOKER",
    job_completion_status: "COMPLETED",
    entity_id: "booker_id",
    comments: "Offer completed by booker."
  }
}
// This sets payout_status to PENDING
```

**Step 5.2: Creative Marks Job Complete**
```typescript
// Endpoint: complete_offer
const creativeCompleteRequest = {
  data: {
    offer_id: "offer_id",
    entity_role: "CREATIVE",
    job_completion_status: "COMPLETED",
    entity_id: "creative_id",
    comments: "Offer completed by creative."
  }
}
// This sets payout_status to AWAITING_PAYOUT
```

#### 6. Automatic Invoice Generation

**Step 6.1: Triggered Payment Calculation**
```typescript
// This is automatically triggered by the on_offer_updated event function
// when payout_status changes to AWAITING_PAYOUT

// The PaymentCalculator service automatically:
// 1. Calculates total payment based on hours and rates
// 2. Applies any applicable discounts
// 3. Creates invoice document in invoices collection
// 4. Links invoice ID to the offer document
```

### Data Flow Summary

1. **Creative Profile**: `creatives` collection
2. **Pricing Data**: `creative_prices` and `creative_discount_prices` collections
3. **Portfolio**: `portfolio_items` collection
4. **Offers**: `offers` collection with status tracking
5. **Invoices**: `invoices` collection (auto-generated)

### Key Status Transitions

**Offer Status Flow:**
- `DRAFT` → `PENDING` (submit_offer_to_creative)
- `PENDING` → `ACTIVE` (creative accepts)
- `ACTIVE` → `COMPLETED` (both parties mark complete)

**Payout Status Flow:**
- `null` → `PENDING` (booker completes)
- `PENDING` → `AWAITING_PAYOUT` (creative completes)
- `AWAITING_PAYOUT` → Auto invoice generation triggered

### Required Collections and Models

This flow requires the following collections to be properly set up:
- `creatives`
- `creative_prices`
- `creative_discount_prices`
- `offers`
- `portfolio_items`
- `projects`
- `invoices`

## Core Collections (MVP)

### User Management

#### `users` Collection (MVP)
```typescript
interface User {
  id: string;
  email: string;
  name: string;
  userType: "creative" | "booker";
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
}
```

#### `creatives` Collection (MVP)
```typescript
interface Creative {
  id: string; // Same as user.id
  userId: string;
  name: string;
  type: string; // "PHOTOGRAPHER", "MODEL", "MAKEUP ARTIST", etc.
  location: string;
  bio: string;
  portfolioPhotos: string[]; // Array of storage URLs
  hourlyRate?: number;
  dayRate?: number;
  specialties: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Additional fields from test flow
  display_name: string;
  story: string;
  instagram: string;
  phone_number: string;
  creative_type: CreativeType[]; // Array of creative types
}

enum CreativeType {
  PHOTOGRAPHER = "PHOTOGRAPHER",
  VIDEOGRAPHER = "VIDEOGRAPHER",
  MODEL = "MODEL",
  MAKEUP_ARTIST = "MAKEUP_ARTIST"
}
```

#### `creative_prices` Collection (MVP)
```typescript
interface CreativePrice {
  id: string;
  creative_id: string;
  creative_type: CreativeType;
  price: number;
  currency: string;
  rate_type: RateType;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

enum RateType {
  HOURLY = "HOURLY",
  DAILY = "DAILY",
  PROJECT = "PROJECT"
}
```

#### `creative_discount_prices` Collection (MVP)
```typescript
interface DiscountPrice {
  id: string;
  creative_id: string;
  creative_type: CreativeType;
  rate_type: RateType;
  above_rate: number; // Minimum hours/days for discount
  discount_percentage: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### `invoices` Collection (MVP)
```typescript
interface Invoice {
  id: string;
  offer_id: string;
  creative_id: string;
  booker_id: string;
  total_amount: number;
  currency: string;
  hours_worked?: number;
  discount_applied?: number;
  invoice_date: Timestamp;
  status: "generated" | "sent" | "paid";
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

#### `bookers` Collection (MVP)
```typescript
interface Booker {
  id: string; // Same as user.id
  userId: string;
  name: string;
  type: string; // "CREATIVE DIRECTOR", "MARKETING MANAGER", etc.
  location: string;
  bio: string;
  company?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Project Management

#### `projects` Collection (MVP)
```typescript
interface Project {
  id: string;
  bookerId: string;
  title: string;
  client: string;
  budget: number;
  status: "active" | "completed" | "planning";
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  projectType: string;
  creativesNeeded: string[];
  moodBoardUrl?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Offer System

#### `offers` Collection (MVP)
```typescript
interface Offer {
  id: string;
  projectId: string;
  projectTitle: string;
  bookerId: string;
  bookerName: string;
  creativeId: string;
  creativeName: string;
  role: string;
  amount: number;
  status: "pending" | "accepted" | "declined";
  projectType: string;
  location: string;
  offerDate: Timestamp;
  isSentByCreative: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Additional fields from test flow
  offer_start_date: string;
  offer_end_date: string;
  creative_type: CreativeType;
  rate_type: RateType;
  approval_workflow: ApprovalWorkflow[];
  job_completion_workflow: JobCompletionWorkflow;
  payout_status: PayoutStatus;
  invoice_id?: string; // Links to generated invoice
}

enum OfferStatus {
  DRAFT = "DRAFT",
  PENDING = "PENDING", 
  ACTIVE = "ACTIVE",
  COMPLETED = "COMPLETED",
  CANCELLED = "CANCELLED"
}

enum ApprovalStatus {
  PENDING = "PENDING",
  APPROVED = "APPROVED",
  REJECTED = "REJECTED"
}

enum PayoutStatus {
  PENDING = "PENDING",
  AWAITING_PAYOUT = "AWAITING_PAYOUT",
  PAID = "PAID"
}

enum JobCompletionStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED"
}

interface ApprovalWorkflow {
  entity_type: "BOOKER" | "CREATIVE";
  status: ApprovalStatus;
  comments?: string;
  timestamp: Timestamp;
}

interface JobCompletionWorkflow {
  booker: {
    status: JobCompletionStatus;
    comments?: string;
    timestamp?: Timestamp;
  };
  creative: {
    status: JobCompletionStatus;
    comments?: string;
    timestamp?: Timestamp;
  };
}
```

### Portfolio Management

#### `portfolioItems` Collection (MVP)
```typescript
interface PortfolioItem {
  id: string;
  creativeId: string;
  type: "image" | "text";
  position: number;
  
  // For image tiles
  imageUrl?: string;
  alt?: string;
  
  // For text tiles
  textContent?: string;
  backgroundColor?: string;
  fontSize?: string;
  fontFamily?: string;
  
  // Layout properties
  size: "standard" | "horizontal" | "vertical" | "large";
  isMerged?: boolean;
  originalItems?: string[]; // IDs of merged items
  
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Job Management

#### `jobs` Collection (MVP)
```typescript
interface Job {
  id: string;
  offerId: string; // Links to accepted offer
  creativeId: string;
  bookerId: string;
  title: string;
  client: string;
  date: string;
  time: string;
  location: string;
  status: "confirmed" | "pending" | "completed";
  amount: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### Availability

#### `availability` Collection (MVP)
```typescript
interface Availability {
  id: string;
  creativeId: string;
  date: string; // YYYY-MM-DD format
  isAvailable: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

## Firebase Cloud Functions (MVP)

### Authentication Functions
- `createUserProfile` (MVP) - Create user profile on signup (Available to: Both Creatives and Bookers).
- `updateUserProfile` (MVP) - Update user information (Available to: Both Creatives and Bookers for their own profiles).

### Creative Management Functions
- `create_creative` (MVP) - Create new creative profile (Available to: New users signing up as creatives).
- `accept_creative` (MVP) - Admin accepts creative application (Available to: Admin users only).
- `delete_creative` (MVP) - Delete creative profile (Available to: Creative users for their own profile).

### Pricing Management Functions
- `set_creative_price` (MVP) - Set regular pricing for creative services (Available to: Creatives for their own pricing).
- `set_creative_discount_price` (MVP) - Set discount pricing rules (Available to: Creatives for their own pricing).

### Project Management Functions
- `createProject` (MVP) - Create new project (Available to: Bookers only).
- `updateProject` (MVP) - Update project details (Available to: Bookers only, for projects they own).
- `deleteProject` (MVP) - Delete project (Available to: Bookers only, for projects they own).

### Offer Management Functions
- `create_draft_offer` (MVP) - Create draft offer (Available to: Bookers to create offers for creatives).
- `submit_offer_to_creative` (MVP) - Submit draft offer to creative (Available to: Bookers for their draft offers).
- `creative_respond_to_offer` (MVP) - Creative responds to offer (Available to: Creatives for offers sent to them).
- `complete_offer` (MVP) - Mark offer as completed (Available to: Both Bookers and Creatives for their active offers).

### Job Functions
- `confirmJob` (MVP) - Convert accepted offer to job (Available to: Bookers, after Creative accepts offer).
- `completeJob` (MVP) - Mark job as completed (Available to: Both Bookers and Creatives).

### Portfolio Functions
- `create_portfolio_item` (MVP) - Create portfolio item (Available to: Creatives only).
- `update_portfolio_item` (MVP) - Update portfolio item (Available to: Creatives only).
- `delete_portfolio_item` (MVP) - Remove portfolio item (Available to: Creatives only).
- `update_portfolio_items_order` (MVP) - Reorder portfolio items (Available to: Creatives only).
- `uploadPortfolioImage` (MVP) - Handle image upload to Cloud Storage (Available to: Creatives only).

### Availability Management Functions
- `set_creative_availability` (MVP) - Set creative availability slots (Available to: Creatives only).
- `get_creative_availability` (MVP) - Get creative availability (Available to: Bookers and Creatives).

### Search Functions
- `searchCreatives` (MVP) - Basic search by type and location (Available to: Both Bookers and Creatives).
- `searchProjects` (MVP) - Basic search for creatives (Available to: Both Creatives and Bookers).

### Email Notification Functions
- `sendOfferEmail` (MVP) - Email when offer sent (System-triggered).
- `sendOfferResponseEmail` (MVP) - Email when offer accepted/declined (System-triggered).
- `sendJobConfirmationEmail` (MVP) - Email when job confirmed (System-triggered).
- `enqueue_send_accepted_email` (MVP) - Queue email for sending (System-triggered).

### Payment & Invoice Functions
- `on_offer_updated` (MVP) - Auto-triggered when offer status changes to calculate payments (Event-driven).
- `calculate_payment` (MVP) - Calculate payment and generate invoice (System-triggered).

## Firebase Storage Structure (MVP)

```
/portfolios/{creativeId}/{itemId}/{filename}
/projects/{projectId}/moodboards/{filename}
/profiles/{userId}/avatar/{filename}
```

## Security Rules (MVP)

### Firestore Security Rules
```javascript
// Users can read any profile but only write their own
match /users/{userId} {
  allow read: if request.auth != null;
  allow write: if request.auth.uid == userId;
}

// Creatives profiles are public read, owner write
match /creatives/{creativeId} {
  allow read: if true;
  allow write: if request.auth.uid == creativeId;
}

// Projects visible to all authenticated users
match /projects/{projectId} {
  allow read: if request.auth != null;
  allow write: if request.auth.uid == resource.data.bookerId;
}

// Offers visible to sender and recipient
match /offers/{offerId} {
  allow read: if request.auth.uid == resource.data.creativeId 
              || request.auth.uid == resource.data.bookerId;
  allow create: if request.auth != null;
  allow update: if request.auth.uid == resource.data.creativeId 
                || request.auth.uid == resource.data.bookerId;
}

// Portfolio items public read, owner write
match /portfolioItems/{itemId} {
  allow read: if true;
  allow write: if request.auth.uid == resource.data.creativeId;
}
```

## Non-MVP Features (Future Phases)

### Phase 2
- Payment processing and invoicing
- Reviews and ratings system
- Advanced search with filters
- Analytics dashboard

### Phase 3
- Internal messaging system
- Calendar integration
- Automated budgeting suggestions
- Mobile app support

## MVP Implementation Priority

1. **Authentication & User Profiles** - Enable user signup/login
2. **Portfolio Management** - Allow creatives to showcase work
3. **Project Creation** - Enable bookers to post projects
4. **Offer System** - Connect bookers and creatives
5. **Job Tracking** - Track confirmed work
6. **Email Notifications** - Keep users informed

This MVP structure provides the core functionality needed to connect creatives with bookers while keeping the backend simple and maintainable.
