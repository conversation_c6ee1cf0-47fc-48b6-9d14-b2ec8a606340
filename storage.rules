rules_version = '2';

// Firebase Storage Security Rules for DUA Platform
service firebase.storage {
  match /b/{bucket}/o {

    // Helper function to check if user has permission to access a creative profile
    function hasCreativePermission(creativeId, requiredPermission) {
      let userRootDoc = firestore.get(/databases/(default)/documents/user-root-accounts/$(request.auth.uid));
      let creatives = userRootDoc.data.creatives;

      return creativeId in creatives &&
             (creatives[creativeId] == requiredPermission ||
              creatives[creativeId] == 'editor' ||
              creatives[creativeId] == 'admin');
    }

    // Helper function to check if user has permission to access a booker profile
    function hasBookerPermission(bookerId, requiredPermission) {
      let userRootDoc = firestore.get(/databases/(default)/documents/user-root-accounts/$(request.auth.uid));
      let bookers = userRootDoc.data.bookers;

      return bookerId in bookers &&
             (bookers[bookerId] == requiredPermission ||
              bookers[bookerId] == 'editor' ||
              bookers[bookerId] == 'admin');
    }

    // Portfolio images - readable by all, writable by users with editor+ permissions
    match /portfolio/{creativeId}/{allPaths=**} {
      allow read: if true; // Portfolio images are publicly readable
      allow write: if request.auth != null &&
                   (request.auth.uid == creativeId ||
                    hasCreativePermission(creativeId, 'editor'));
    }

    // Profile avatars - readable by all, writable by profile owner or users with editor+ permissions
    match /profiles/{userId}/avatar/{allPaths=**} {
      allow read: if true; // Profile avatars are publicly readable
      allow write: if request.auth != null &&
                   (request.auth.uid == userId ||
                    hasCreativePermission(userId, 'editor') ||
                    hasBookerPermission(userId, 'editor'));
    }

    // Project moodboards - readable by authenticated users, writable by bookers with editor+ permissions
    match /projects/{projectId}/moodboards/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
      // Note: Project-specific permissions should be handled by backend functions
    }

    // Default deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
