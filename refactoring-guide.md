# Refactoring Large React Components: A Step-by-Step Guide

This guide outlines a systematic approach to refactoring large React components, making them more manageable, maintainable, and easier to understand. The process focuses on extracting logic into custom hooks, breaking down UI into smaller components, and improving overall code organization.

## Phase 1: Preparation & Initial Cleanup

1.  **Understand the Component:**
    *   <PERSON>oughly review the component's current responsibilities, state variables, and functions.
    *   Identify which parts handle UI, state management, side effects, and utility operations.
2.  **Data Extraction (If Applicable):**
    *   If mock data or large static data structures are embedded, move them to separate files (e.g., `*-data.ts`). Import as needed.
3.  **Basic Code Cleanup:**
    *   Remove any obviously unused variables, functions, or imports.
    *   Ensure consistent formatting.

## Phase 2: Extracting Utility Functions

Pure functions that perform calculations or transformations without relying on component state or lifecycle can often be extracted.

1.  **Identify Utility Functions:** Look for self-contained functions that take inputs and return outputs without side effects related to the component's state (e.g., our `getGridClasses` function).
2.  **Create a Utilities File:**
    *   Place these functions in a dedicated utilities file (e.g., `src/utils/myUtils.ts` or as we did, `components/utils/grid-utils.ts`).
3.  **Update Usage:**
    *   Import the utility function directly where needed (e.g., `PhotoGridPanel` importing `getGridClasses`).
    *   If the function was previously defined in the component and passed as a prop to a child, remove it from the child's props and update the child component to import it directly.
    *   Remove the local definition from the parent component (`ArtistProfilePage` no longer defines or passes `getGridClasses`).

## Phase 3: Extracting Logic into Custom Hooks

This is the core of refactoring stateful logic out of large components.

1.  **Identify Cohesive Slices of State & Logic:**
    *   Look for groups of `useState` variables and the functions that operate on them. These often represent a specific feature or concern (e.g., selection management, form handling, API call state).
    *   Examples from our refactor:
        *   `selectedTiles`, `isSelectionMode` -> `usePhotoSelection`
        *   `editingTile`, `tempText` -> `useTextEditing`
        *   Tile modification functions (`mergeTiles`, `splitTile`, etc.) -> `useTileOperations`
        *   Modal states and handlers (`showBookingModal`, `bookingForm`) -> `useBookingModal`
        *   `selectedPhoto` for modal -> `usePhotoDetailModal`
2.  **Create a Custom Hook:**
    *   Create a new file for your hook (e.g., `components/hooks/useMyFeature.ts`).
    *   Define the hook as a function starting with `use` (e.g., `export const useMyFeature = (initialProps) => { ... }`).
    *   **Move State:** Transfer the relevant `useState` calls into the hook.
    *   **Move Logic:** Transfer the related functions into the hook.
        *   Wrap these functions in `useCallback` to memoize them, ensuring their dependencies are correctly listed in the dependency array.
    *   **Define Props:** Determine what parameters your hook needs from the calling component (e.g., initial state values, callback functions to update parent state like `setPhotos` for `useTextEditing`, or other state/functions it depends on).
    *   **Return Values:** Return an object or array containing the state values and functions that the calling component will need.
3.  **Integrate the Custom Hook into the Parent Component:**
    *   Import the newly created custom hook into the parent (`ArtistProfilePage`).
    *   Call the hook at the top of your functional component, passing any necessary props:
        ```javascript
        const { stateValue, handleAction } = useMyFeature({ parentSetState });
        ```
    *   Remove the original `useState` declarations and functions that were moved into the hook from the parent component.
    *   Update all places in the parent component's JSX and other logic to use the state values and handler functions returned by the hook.
    *   Carefully review and update the dependency arrays of any `useEffect` or `useCallback` hooks remaining in the parent component (or within the new hook) to ensure they correctly reference values from custom hooks or other dependencies.
4.  **Iterate and Refine:**
    *   Address any type errors or linter warnings that arise (e.g., ensuring prop types match, fixing dependency order for `toggleLike`).
    *   Test the functionality thoroughly.
    *   You might find that one custom hook can call another or that some props passed to a hook are themselves setters from another hook.

## Phase 4: Extracting Presentational Components

Break down large JSX structures into smaller, focused presentational components.

1.  **Identify Self-Contained UI Blocks:** Look for distinct sections of JSX in your main component's `return` statement that could logically be their own component (e.g., our `SiteHeader`).
2.  **Create New Component Files:**
    *   For each UI block, create a new component file (e.g., `components/layout/SiteHeader.tsx`).
3.  **Move JSX and Props:**
    *   Transfer the relevant JSX into the new component's `return` statement.
    *   Define the necessary props (`interface MyComponentProps { ... }`) that the new component will need to receive from its parent (if any, `SiteHeader` needed none).
    *   Ensure all necessary imports (other components, icons, utilities like `Link`, `Button`, `Search`, `Menu` for `SiteHeader`) are moved to the new component file.
4.  **Replace JSX in Parent:**
    *   Import the new component into the parent.
    *   Replace the original JSX block in the parent with an instance of the new component, passing the required props.
5.  **Cleanup Parent:**
    *   Remove any imports from the parent component that were solely used by the extracted JSX and are now handled within the new child component.

## Phase 5: File and Component Renaming (If Needed)

As a component's responsibilities change or become clearer through refactoring, its name might no longer be accurate.

1.  **Choose a Descriptive Name:** Select a name that accurately reflects the component's primary function and scope (e.g., `InstagramPhotoGrid` -> `ArtistProfilePage`).
2.  **Rename Component Function & Props:**
    *   In the component file, rename the function itself (e.g., `export default function ArtistProfilePage(...)`).
    *   Rename its props interface (e.g., `interface ArtistProfilePageProps { ... }`).
3.  **Rename the File:**
    *   Create the new file (e.g., `creative-profile-page.tsx`) with the updated content.
    *   Delete the old file (e.g., `instagram-photo-grid.tsx`).
4.  **Update Imports:**
    *   Search the codebase for all places where the old component was imported (e.g., in `app/creative/[id]/page.tsx`).
    *   Update the import statements to use the new component name and the new file path.
    *   Update the JSX tags where the component is used.

## Phase 6: Final Review & Iteration

1.  **Code Review:**
    *   Check for any remaining unused variables, functions, or imports (like the `notes` state we removed).
    *   Ensure consistency in naming and coding style.
    *   Verify that all dependency arrays for `useCallback` and `useEffect` are correct and minimal.
2.  **State Management Review:**
    *   Are there any state variables that can be further localized or lifted appropriately?
    *   Is any state redundant (e.g., our upcoming review of `mergeCount`)?
3.  **UX Enhancements (Example):**
    *   During refactoring, opportunities for UX improvements might arise, like changing the booking modal's submission from an `alert` to a loading state, which involved modifying a hook (`useBookingModal`) and its consuming component (`BookingModal.tsx`).
4.  **Testing:**
    *   Ensure all functionality behaves as expected after refactoring. Update unit or integration tests.

By following these phases, you can incrementally refactor large React components into a more modular, testable, and understandable codebase. Remember that refactoring is an iterative process; you may revisit earlier phases as you gain more clarity. 