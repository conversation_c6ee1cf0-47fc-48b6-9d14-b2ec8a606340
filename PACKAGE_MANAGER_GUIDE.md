# Package Manager Guide

## Using pnpm with the DUA Component Library (Recommended)

You can use any of these package managers with the component library, but **pnpm is recommended**.

## Quick Start with pnpm

```bash
# Install dependencies for the component library
cd component-library
pnpm install

# Start Storybook for development
pnpm storybook

# Build the library
pnpm build

# Run tests
pnpm test
```

## Package Manager Comparison

| Feature | pnpm | Yarn | npm |
|---------|------|------|-----|
| **Speed** | ⭐ Fastest | Fast | Good |
| **Disk Usage** | ⭐ Excellent | Good | Standard |
| **Offline Support** | Good | Excellent | Good |
| **Monorepo Support** | ⭐ Excellent | Excellent | Good |
| **Ecosystem** | ⭐ Growing rapidly | React-friendly | Universal |
| **Phantom Dependencies** | ⭐ Prevents them | Allows them | Allows them |

## Installation Commands

### Using pnpm (Recommended)
```bash
# Install the library in your project
pnpm add dua-component-library styled-components

# Install dev dependencies
pnpm add -D @types/styled-components
```

### Using Yarn
```bash
yarn add dua-component-library styled-components
yarn add -D @types/styled-components
```

### Using npm
```bash
npm install dua-component-library styled-components
npm install -D @types/styled-components
```

## Why pnpm is Better

### 1. **Performance & Speed**
- **Fastest installs**: Up to 2x faster than yarn/npm
- **Parallel processing**: Better dependency resolution
- **Smart caching**: Reuses packages across projects

### 2. **Disk Space Efficiency**
- **Hard links**: Only stores one copy of each package version
- **50-70% space savings**: Compared to npm/yarn
- **Content-addressable storage**: Eliminates duplication

### 3. **Strict Dependency Management** 
- **No phantom dependencies**: Prevents accessing undeclared deps
- **Hoisting control**: Better control over dependency trees
- **Reproducible installs**: More predictable builds

### 4. **Modern Features**
- **Excellent monorepo support**: Built-in workspace handling
- **Node.js compatibility**: Works with all Node versions
- **Growing ecosystem**: Adopted by Vue, Nuxt, and other major projects

## Migration from yarn to pnpm

If you want to switch the component library to use pnpm:

```bash
# Remove yarn lock file
rm yarn.lock

# Install with pnpm
pnpm install

# This creates pnpm-lock.yaml
```

## VS Code Integration

For better VS Code integration with pnpm, add to your `.vscode/settings.json`:

```json
{
  "npm.packageManager": "pnpm",
  "typescript.preferences.includePackageJsonAutoImports": "on"
}
```

## Scripts Available

All these scripts work with pnpm:

```bash
pnpm storybook        # Start component development
pnpm build           # Build the library
pnpm test            # Run tests
pnpm check:types     # TypeScript checking
pnpm check:format    # ESLint checking
```

## Conclusion

**Use pnpm if you:** (Recommended!)
- Want the fastest installs and smallest disk usage
- Work with large projects or monorepos
- Need strict dependency management
- Want the most modern package manager

**Use Yarn if you:**
- Work primarily with React/frontend projects
- Want excellent offline support
- Need fast parallel installs
- Work with established yarn-based projects

**Use npm if you:**
- Want universal compatibility
- Prefer the default Node.js package manager
- Work in mixed technology environments

**Bottom line: pnpm is the future** - it's faster, more efficient, and prevents many common dependency issues. The DUA Component Library is optimized for pnpm!
