import {
	<PERSON>ge,
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON>ooter,
	CardHeader,
	CardTitle,
	Input,
	ThemeProvider,
	useTheme,
} from "dua-component-library";
import React from "react";

// Theme toggle component
function ThemeToggle() {
	const { theme, toggleTheme } = useTheme();

	return (
		<Button variant="outline" size="sm" onClick={toggleTheme}>
			{theme === "light" ? "🌙" : "☀️"}
			{theme === "light" ? "Dark" : "Light"} Mode
		</Button>
	);
}

// Example component showcasing the library
function ComponentLibraryDemo() {
	const [inputValue, setInputValue] = React.useState("");

	return (
		<div style={{ padding: "2rem", maxWidth: "800px", margin: "0 auto" }}>
			<div
				style={{
					display: "flex",
					justifyContent: "space-between",
					alignItems: "center",
					marginBottom: "2rem",
				}}
			>
				<h1>DUA Component Library Demo</h1>
				<ThemeToggle />
			</div>

			<div
				style={{
					display: "grid",
					gap: "2rem",
					gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
				}}
			>
				{/* Button Examples */}
				<Card>
					<CardHeader>
						<CardTitle>Buttons</CardTitle>
						<CardDescription>Various button variants and sizes</CardDescription>
					</CardHeader>
					<CardContent>
						<div
							style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
						>
							<div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
								<Button>Default</Button>
								<Button variant="secondary">Secondary</Button>
								<Button variant="outline">Outline</Button>
								<Button variant="ghost">Ghost</Button>
							</div>
							<div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
								<Button size="sm">Small</Button>
								<Button>Default</Button>
								<Button size="lg">Large</Button>
							</div>
							<div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
								<Button variant="destructive">Delete</Button>
								<Button disabled>Disabled</Button>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Form Example */}
				<Card>
					<CardHeader>
						<CardTitle>Form Elements</CardTitle>
						<CardDescription>Input fields and form components</CardDescription>
					</CardHeader>
					<CardContent>
						<div
							style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
						>
							<Input
								placeholder="Enter your name..."
								value={inputValue}
								onChange={(e) => setInputValue(e.target.value)}
							/>
							<Input type="email" placeholder="Enter your email..." />
							<Input type="password" placeholder="Enter your password..." />
							<Input disabled placeholder="Disabled input" />
						</div>
					</CardContent>
					<CardFooter>
						<Button>Submit</Button>
						<Button variant="outline">Cancel</Button>
					</CardFooter>
				</Card>

				{/* Badge Examples */}
				<Card>
					<CardHeader>
						<CardTitle>Badges</CardTitle>
						<CardDescription>Status indicators and labels</CardDescription>
					</CardHeader>
					<CardContent>
						<div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
							<Badge>Default</Badge>
							<Badge variant="secondary">Secondary</Badge>
							<Badge variant="destructive">Error</Badge>
							<Badge variant="outline">Outline</Badge>
						</div>
					</CardContent>
				</Card>

				{/* Complex Example */}
				<Card size="lg">
					<CardHeader>
						<div
							style={{
								display: "flex",
								justifyContent: "space-between",
								alignItems: "start",
							}}
						>
							<div>
								<CardTitle>User Profile</CardTitle>
								<CardDescription>
									Manage your account settings and preferences
								</CardDescription>
							</div>
							<Badge variant="secondary">Premium</Badge>
						</div>
					</CardHeader>
					<CardContent>
						<div
							style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
						>
							<div>
								<label
									style={{
										display: "block",
										marginBottom: "0.5rem",
										fontWeight: 600,
									}}
								>
									Full Name
								</label>
								<Input defaultValue="John Doe" />
							</div>
							<div>
								<label
									style={{
										display: "block",
										marginBottom: "0.5rem",
										fontWeight: 600,
									}}
								>
									Email
								</label>
								<Input type="email" defaultValue="<EMAIL>" />
							</div>
							<div style={{ display: "flex", gap: "0.5rem", flexWrap: "wrap" }}>
								<Badge>React</Badge>
								<Badge>TypeScript</Badge>
								<Badge>Styled Components</Badge>
							</div>
						</div>
					</CardContent>
					<CardFooter>
						<Button>Save Changes</Button>
						<Button variant="outline">Reset</Button>
					</CardFooter>
				</Card>
			</div>
		</div>
	);
}

// Main App component with theme provider
export default function App() {
	return (
		<ThemeProvider defaultTheme="light" enableSystem>
			<ComponentLibraryDemo />
		</ThemeProvider>
	);
}
