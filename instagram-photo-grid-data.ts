export interface Photo {
	id: string;
	src: string;
	alt: string;
	likes: number;
	comments: number;
	size?: "standard" | "horizontal" | "vertical" | "large";
	isMerged?: boolean;
	originalPhotos?: Photo[];
	type?: "image" | "text" | "iframe";
	textContent?: string;
	backgroundColor?: string;
	position?: number;
	fontSize?: string;
	fontFamily?: string;
	iframeUrl?: string;
}

export interface Job {
	id: string;
	title: string;
	client: string;
	date: string;
	time: string;
	location: string;
	status: "confirmed" | "pending" | "completed";
	type: string;
}

export interface Payment {
	id: string;
	jobId: string;
	jobTitle: string;
	client: string;
	amount: number;
	date: string;
	status: "paid" | "pending" | "overdue";
	invoiceNumber: string;
}

export interface Project {
	id: string;
	title: string;
	client: string;
	projectType: string;
	startDate: string;
	endDate: string;
	location: string;
	budget: number;
	description: string;
	creativesNeeded: string[];
}

// New Interface for Creative's Offers
export interface CreativeOffer {
	id: string;
	projectTitle: string;
	clientName: string; // Booker or Company name
	clientId?: string; // Optional ID of the client/booker
	role: string; // Role offered/applied for (e.g., Photographer, Model)
	amount: number; // Offer amount
	offerDate: string; // Date offer was made/received
	status:
		| "pending"
		| "accepted"
		| "declined"
		| "expired"
		| "withdrawn"
		| "negotiating"
		| "pending_creative_approval"
		| "active"
		| "rejected_by_creative";
	projectType: string; // e.g., Fashion, Product
	location?: string;
	description?: string; // Brief project description or offer notes
	isSentByMe: boolean; // True if the creative sent this offer, false if they received it
}

export const creativesData: Record<string, any> = {
	"1": {
		name: "ALEX JOHNSON",
		type: "PHOTOGRAPHER",
		location: "New York, NY",
		followers: 1234,
		following: 567,
		bio: "Professional photographer specializing in portraits and fashion",
	},
	"2": {
		name: "SARAH WILSON",
		type: "MODEL",
		location: "Los Angeles, CA",
		followers: 2456,
		following: 432,
		bio: "Fashion and commercial model",
	},
	"3": {
		name: "MICHAEL CHEN",
		type: "VIDEOGRAPHER",
		location: "Chicago, IL",
		followers: 987,
		following: 234,
		bio: "Documentary and commercial videographer",
	},
	"4": {
		name: "EMMA DAVIS",
		type: "MAKEUP ARTIST",
		location: "Miami, FL",
		followers: 1567,
		following: 345,
		bio: "Editorial and bridal makeup creative",
	},
	"5": {
		name: "DAVID RODRIGUEZ",
		type: "PHOTOGRAPHER",
		location: "Austin, TX",
		followers: 2134,
		following: 678,
		bio: "Product and architecture photographer",
	},
};

export const initialPhotos: Photo[] = [
	{
		id: "1",
		src: "/assets/creatives_portfolio/crt1.png",
		alt: "Sunset beach",
		likes: 324,
		comments: 18,
		position: 0,
	},
	{
		id: "2",
		src: "/assets/creatives_portfolio/crt2.png",
		alt: "Pasta dinner",
		likes: 189,
		comments: 12,
		position: 1,
	},
	{
		id: "3",
		src: "/assets/creatives_portfolio/crt3.png",
		alt: "Mountain view",
		likes: 456,
		comments: 23,
		position: 2,
	},
	{
		id: "4",
		src: "/assets/creatives_portfolio/crt4.png",
		alt: "Morning coffee",
		likes: 203,
		comments: 15,
		position: 3,
	},
	{
		id: "5",
		src: "/assets/creatives_portfolio/crt5.png",
		alt: "City lights",
		likes: 378,
		comments: 26,
		position: 4,
	},
	{
		id: "6",
		src: "/assets/creatives_portfolio/crt6.png",
		alt: "Golden retriever",
		likes: 567,
		comments: 41,
		position: 5,
	},
	{
		id: "7",
		src: "/assets/creatives_portfolio/crt8.png",
		alt: "Sushi dinner",
		likes: 234,
		comments: 19,
		position: 6,
	},
	{
		id: "8",
		src: "/assets/creatives_portfolio/crt7.png",
		alt: "Road trip",
		likes: 292,
		comments: 17,
		position: 7,
	},
	{
		id: "9",
		src: "/placeholder.svg?height=400&width=400&text=Colorful flower garden in spring",
		alt: "Spring flowers",
		likes: 445,
		comments: 31,
		position: 8,
	},
];

export const upcomingJobs: Job[] = [
	{
		id: "job1",
		title: "Wedding Photography",
		client: "Sarah & Mike Johnson",
		date: "2024-01-15",
		time: "2:00 PM",
		location: "Central Park, NYC",
		status: "confirmed",
		type: "photography",
	},
	{
		id: "job2",
		title: "Corporate Headshots",
		client: "Tech Solutions Inc.",
		date: "2024-01-18",
		time: "10:00 AM",
		location: "Downtown Office",
		status: "pending",
		type: "photography",
	},
	{
		id: "job3",
		title: "Product Photography",
		client: "Fashion Brand Co.",
		date: "2024-01-22",
		time: "1:00 PM",
		location: "Studio A",
		status: "confirmed",
		type: "photography",
	},
	{
		id: "job4",
		title: "Event Coverage",
		client: "Marketing Agency",
		date: "2024-01-25",
		time: "6:00 PM",
		location: "Convention Center",
		status: "confirmed",
		type: "photography",
	},
];

export const payments: Payment[] = [
	{
		id: "payment1",
		jobId: "job5",
		jobTitle: "Wedding Photography",
		client: "Jennifer & David Smith",
		amount: 2500,
		date: "2023-12-20",
		status: "paid",
		invoiceNumber: "INV-2023-001",
	},
	{
		id: "payment2",
		jobId: "job6",
		jobTitle: "Corporate Event",
		client: "Global Tech Inc.",
		amount: 1800,
		date: "2023-12-05",
		status: "paid",
		invoiceNumber: "INV-2023-002",
	},
	{
		id: "payment3",
		jobId: "job7",
		jobTitle: "Product Shoot",
		client: "Luxury Brands Co.",
		amount: 1200,
		date: "2023-11-28",
		status: "paid",
		invoiceNumber: "INV-2023-003",
	},
];

export const initialProjects: Project[] = [
	{
		id: "project1",
		title: "Summer Lookbook",
		client: "Zara",
		projectType: "fashion",
		startDate: "2024-07-01",
		endDate: "2024-07-15",
		location: "Miami Beach",
		budget: 20000,
		description: "Create a summer lookbook for Zara's new collection.",
		creativesNeeded: ["Photographer", "Model", "Makeup Artist", "Stylist"],
	},
	{
		id: "project2",
		title: "Corporate Headshots",
		client: "Google",
		projectType: "corporate",
		startDate: "2024-08-01",
		endDate: "2024-08-05",
		location: "Mountain View",
		budget: 10000,
		description: "Take corporate headshots for Google's employees.",
		creativesNeeded: ["Photographer"],
	},
	{
		id: "project3",
		title: "Wedding Photography",
		client: "John and Jane Doe",
		projectType: "wedding",
		startDate: "2024-09-01",
		endDate: "2024-09-01",
		location: "Central Park",
		budget: 5000,
		description: "Photograph John and Jane Doe's wedding.",
		creativesNeeded: ["Photographer"],
	},
];

// Mock Data for Offers Received by Creative
export const offersReceivedByCreative: CreativeOffer[] = [
	{
		id: "offer_rec1",
		projectTitle: "Summer Glow Campaign",
		clientName: "Sunlight Beauty Co.",
		role: "Lead Photographer",
		amount: 2200,
		offerDate: "2025-01-15",
		status: "pending",
		projectType: "Beauty Product Shoot",
		location: "Miami Beach, FL",
		description:
			"Looking for a photographer with a bright and airy style for our new sunscreen line.",
		isSentByMe: false,
	},
	{
		id: "offer_rec2",
		projectTitle: "Urban Explorers Video",
		clientName: "City Adventures Magazine",
		role: "Videographer",
		amount: 1800,
		offerDate: "2025-02-10",
		status: "accepted",
		projectType: "Travel Documentary Short",
		location: "Various, NYC",
		description:
			"Short documentary piece following urban explorers. Drone skills a plus.",
		isSentByMe: false,
	},
	{
		id: "offer_rec3",
		projectTitle: "Tech Conference Live Model",
		clientName: "Innovate Corp",
		role: "Promotional Model",
		amount: 750,
		offerDate: "2025-03-05",
		status: "declined",
		projectType: "Tech Event",
		location: "San Francisco, CA",
		description:
			"Need engaging models for our booth at the upcoming Innovate Summit.",
		isSentByMe: false,
	},
	{
		id: "offer_rec4",
		projectTitle: "Fashion Week Backstage Photography",
		clientName: "Elite Fashion Events",
		role: "Backstage Photographer",
		amount: 3500,
		offerDate: "2025-03-20",
		status: "pending_creative_approval",
		projectType: "Fashion Photography",
		location: "New York, NY",
		description:
			"Exclusive backstage access to capture behind-the-scenes moments during Fashion Week.",
		isSentByMe: false,
	},
	{
		id: "offer_rec5",
		projectTitle: "Corporate Brand Campaign",
		clientName: "TechStart Solutions",
		role: "Lead Photographer",
		amount: 4200,
		offerDate: "2025-04-01",
		status: "active",
		projectType: "Corporate Photography",
		location: "San Francisco, CA",
		description:
			"Active project for corporate brand campaign. Shooting starts next week.",
		isSentByMe: false,
	},
];

// Mock Data for Offers Sent by Creative
export const offersSentByCreative: CreativeOffer[] = [
	{
		id: "offer_sent1",
		projectTitle: "Indie Band Music Video",
		clientName: "The Wandering Souls (Band)", // Creative is offering services to this client
		role: "Director of Photography",
		amount: 1500, // Creative's proposed rate
		offerDate: "2025-01-22",
		status: "pending",
		projectType: "Music Video",
		location: "Austin, TX",
		description:
			"Proposal to shoot and direct the photography for your upcoming music video.",
		isSentByMe: true,
	},
	{
		id: "offer_sent2",
		projectTitle: "Artisan Bakery Branding",
		clientName: "The Sweet Spot Bakery",
		role: "Food Photographer",
		amount: 900,
		offerDate: "2025-02-18",
		status: "expired", // Booker didn't respond in time
		projectType: "Branding & Lifestyle",
		location: "Portland, OR",
		description:
			"Offered to create a series of lifestyle and product shots for their new website.",
		isSentByMe: true,
	},
	{
		id: "offer_sent3",
		projectTitle: "Local Cafe Social Media Content",
		clientName: "Corner Brew Cafe",
		role: "Content Creator (Photo/Video)",
		amount: 600, // per month retainer proposal
		offerDate: "2025-03-12",
		status: "negotiating",
		projectType: "Social Media Marketing",
		location: "Local",
		description:
			"Proposed a monthly retainer for creating engaging social media content.",
		isSentByMe: true,
	},
	{
		id: "offer_rec6",
		projectTitle: "Luxury Hotel Campaign",
		clientName: "Grand Resort & Spa",
		role: "Lifestyle Photographer",
		amount: 2800,
		offerDate: "2025-02-28",
		status: "rejected_by_creative",
		projectType: "Hospitality Photography",
		location: "Aspen, CO",
		description:
			"Offer was rejected due to scheduling conflicts with existing commitments.",
		isSentByMe: false,
	},
	{
		id: "offer_sent4",
		projectTitle: "Startup Product Launch",
		clientName: "TechFlow Innovations",
		role: "Product Photographer",
		amount: 1200,
		offerDate: "2025-03-15",
		status: "rejected_by_creative",
		projectType: "Product Photography",
		location: "Seattle, WA",
		description:
			"Creative rejected this offer due to budget constraints and timeline issues.",
		isSentByMe: true,
	},
];
