import type { Timestamp } from "firebase/firestore";
import type { FirestoreDocument } from "../src/lib/firebase/converters";
import type { Collaboration as LiveCollaboration } from "../src/types/collections/booker";
import type { PortfolioItem } from "@/src/types/collections/creative";

// This file defines LOCAL, UI-specific types.
// For backend types, import directly from 'src/types/collections/...'

// This Project type is now aligned with the backend definition in `src/types/collections/booker.ts`.
// The UI will need to adapt to this stricter, data-first model.
export interface Project extends FirestoreDocument {
	bookerId: string;
	title: string;
	client: string;
	budget: number;
	status: "active" | "completed" | "planning";
	description: string;
	startDate: string;
	endDate: string;
	location: string;
	projectType: string;
	creativesNeeded: string[];
	moodBoardUrl?: string;
	roleBudgetOverrides?: { [roleName: string]: string };
	roleStatusOverrides?: {
		[roleName: string]: "Needed" | "Pending" | "Confirmed" | "Declined";
	};
}

export interface Offer extends FirestoreDocument {
	projectId: string;
	projectTitle: string;
	bookerId: string;
	bookerName: string;
	creativeId: string;
	creativeName: string;
	role: string;
	amount: number;
	status:
		| "pending"
		| "accepted"
		| "declined"
		| "expired"
		| "withdrawn"
		| "negotiating"
		| "pending_creative_approval"
		| "active"
		| "rejected_by_creative";
	projectType: string;
	location: string;
	description?: string;
	offerDate: string;
	responseDate?: string;
	notes?: string;
	isSentByCreative: boolean;
}

export interface BudgetItem {
	id: string;
	projectId: string;
	projectTitle: string;
	category: string;
	amount: number;
	date: string;
	status: "paid" | "pending" | "scheduled";
	paymentMethod: string;
}

export interface Payment extends FirestoreDocument {
	bookerId: string;
	projectId: string;
	creativeId: string;
	amount: number;
	date: string;
	status: "paid" | "pending" | "failed";
	method: string; // e.g., 'Credit Card', 'PayPal'
	transactionId?: string; // from payment processor
}

export interface Invoice extends FirestoreDocument {
	invoiceNumber: string;
	offerId: string;
	projectId: string;
	bookerId: string;
	bookerName: string;
	creativeId: string;
	creativeName: string;
	amount: number;
	status: "draft" | "sent" | "paid" | "overdue" | "cancelled";
	issueDate: string;
	dueDate: string;
	description: string;
	projectTitle: string;
	role: string;
	escrowStatus: "funds_pending" | "funds_held" | "funds_released" | "funds_disputed";
	paymentDate?: string;
	notes?: string;
}

export interface EscrowTransaction extends FirestoreDocument {
	invoiceId: string;
	offerId: string;
	bookerId: string;
	creativeId: string;
	amount: number;
	status: "initiated" | "funds_held" | "released_to_creative" | "refunded_to_booker" | "disputed";
	initiatedDate: string;
	releaseDate?: string;
	platformFee: number;
	netAmount: number; // amount - platformFee
	paymentMethod: string;
	transactionId?: string;
	disputeReason?: string;
	notes?: string;
}

export interface Collaboration extends FirestoreDocument {
	projectId: string;
	bookerId: string;
	creativeId: string;
	role: string;
	budget: number;
	status: "pending" | "confirmed" | "completed" | "cancelled";
	paymentStatus: "pending" | "paid" | "failed";
	artistRating?: number;
	startDate: string;
	completedDate?: string;
}

export interface PastCollaboration extends Collaboration {
	projectTitle?: string;
	artistName?: string;
	artistAvatar?: string;
	rating?: number;
	review?: string;
	projectType?: string;
}
