import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, CreditCard, DollarSign, FileText, Shield, AlertTriangle } from "lucide-react";
import type React from "react";
import type { Invoice, EscrowTransaction } from "../types";

interface InvoiceCardProps {
	invoice: Invoice;
	escrowTransaction?: EscrowTransaction;
	onPayInvoice?: (invoiceId: string) => void;
	onReleaseEscrow?: (invoiceId: string) => void;
	onDisputeEscrow?: (invoiceId: string) => void;
	onViewDetails?: (invoiceId: string) => void;
	userRole: "booker" | "creative";
}

const InvoiceCard: React.FC<InvoiceCardProps> = ({
	invoice,
	escrowTransaction,
	onPayInvoice,
	onReleaseEscrow,
	onDisputeEscrow,
	onViewDetails,
	userRole,
}) => {
	const getStatusColor = (status: string) => {
		switch (status) {
			case "paid":
				return "bg-green-100 text-green-800";
			case "sent":
				return "bg-blue-100 text-blue-800";
			case "overdue":
				return "bg-red-100 text-red-800";
			case "cancelled":
				return "bg-gray-100 text-gray-800";
			default:
				return "bg-yellow-100 text-yellow-800";
		}
	};

	const getEscrowStatusColor = (status: string) => {
		switch (status) {
			case "funds_held":
				return "bg-blue-100 text-blue-800";
			case "funds_released":
				return "bg-green-100 text-green-800";
			case "funds_disputed":
				return "bg-red-100 text-red-800";
			default:
				return "bg-yellow-100 text-yellow-800";
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	const isOverdue = new Date(invoice.dueDate) < new Date() && invoice.status !== "paid";

	return (
		<div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
			{/* Header */}
			<div className="flex items-start justify-between mb-4">
				<div className="flex items-center gap-3">
					<FileText className="w-5 h-5 text-gray-600" />
					<div>
						<h3 className="font-medium text-gray-900">{invoice.invoiceNumber}</h3>
						<p className="text-sm text-gray-600">{invoice.projectTitle}</p>
					</div>
				</div>
				<div className="flex flex-col items-end gap-2">
					<span
						className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
							invoice.status,
						)}`}
					>
						{invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
					</span>
					{isOverdue && (
						<span className="flex items-center gap-1 text-xs text-red-600">
							<AlertTriangle className="w-3 h-3" />
							Overdue
						</span>
					)}
				</div>
			</div>

			{/* Amount and Escrow Status */}
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<DollarSign className="w-4 h-4 text-gray-600" />
					<span className="text-lg font-semibold text-gray-900">
						{formatCurrency(invoice.amount)}
					</span>
				</div>
				{escrowTransaction && (
					<div className="flex items-center gap-2">
						<Shield className="w-4 h-4 text-gray-600" />
						<span
							className={`px-2 py-1 text-xs font-medium rounded-full ${getEscrowStatusColor(
								invoice.escrowStatus,
							)}`}
						>
							{invoice.escrowStatus.replace("_", " ").toUpperCase()}
						</span>
					</div>
				)}
			</div>

			{/* Details */}
			<div className="space-y-2 mb-4">
				<div className="flex items-center gap-2 text-sm text-gray-600">
					<Calendar className="w-4 h-4" />
					<span>Due: {new Date(invoice.dueDate).toLocaleDateString()}</span>
				</div>
				<div className="text-sm text-gray-600">
					<span className="font-medium">Role:</span> {invoice.role}
				</div>
				<div className="text-sm text-gray-600">
					<span className="font-medium">
						{userRole === "booker" ? "Creative:" : "Booker:"}
					</span>{" "}
					{userRole === "booker" ? invoice.creativeName : invoice.bookerName}
				</div>
			</div>

			{/* Escrow Transaction Details */}
			{escrowTransaction && (
				<div className="bg-gray-50 rounded-lg p-3 mb-4">
					<h4 className="text-sm font-medium text-gray-900 mb-2">Escrow Details</h4>
					<div className="space-y-1 text-xs text-gray-600">
						<div>Platform Fee: {formatCurrency(escrowTransaction.platformFee)}</div>
						<div>Net Amount: {formatCurrency(escrowTransaction.netAmount)}</div>
						<div>Payment Method: {escrowTransaction.paymentMethod}</div>
					</div>
				</div>
			)}

			{/* Actions */}
			<div className="flex gap-2 pt-4 border-t border-gray-100">
				{onViewDetails && (
					<Button
						variant="outline"
						size="sm"
						onClick={() => onViewDetails(invoice.id)}
						className="flex-1"
					>
						View Details
					</Button>
				)}

				{/* Booker Actions */}
				{userRole === "booker" && (
					<>
						{invoice.status === "sent" && onPayInvoice && (
							<Button
								size="sm"
								onClick={() => onPayInvoice(invoice.id)}
								className="flex-1 bg-blue-600 hover:bg-blue-700"
							>
								<CreditCard className="w-4 h-4 mr-2" />
								Pay Invoice
							</Button>
						)}
						{invoice.escrowStatus === "funds_held" && onReleaseEscrow && (
							<Button
								size="sm"
								onClick={() => onReleaseEscrow(invoice.id)}
								className="flex-1 bg-green-600 hover:bg-green-700"
							>
								Release Funds
							</Button>
						)}
						{invoice.escrowStatus === "funds_held" && onDisputeEscrow && (
							<Button
								variant="outline"
								size="sm"
								onClick={() => onDisputeEscrow(invoice.id)}
								className="border-red-300 text-red-600 hover:bg-red-50"
							>
								Dispute
							</Button>
						)}
					</>
				)}

				{/* Creative Actions */}
				{userRole === "creative" && invoice.escrowStatus === "funds_held" && (
					<div className="flex-1 text-center text-sm text-gray-600">
						Funds held in escrow - awaiting release
					</div>
				)}
			</div>
		</div>
	);
};

export default InvoiceCard;
