import { useState } from "react";
import type React from "react";
import type { Invoice, EscrowTransaction } from "../types";
import InvoiceCard from "./invoice-card";
import { useInvoiceManagement } from "../hooks/useInvoiceManagement";

interface InvoiceListProps {
	invoices: Invoice[];
	escrowTransactions?: EscrowTransaction[];
	userRole: "booker" | "creative";
	onInvoiceUpdate?: () => void;
}

const InvoiceList: React.FC<InvoiceListProps> = ({
	invoices,
	escrowTransactions = [],
	userRole,
	onInvoiceUpdate,
}) => {
	const [isProcessing, setIsProcessing] = useState<string | null>(null);
	const { processEscrowPayment, releaseEscrowFunds, disputeEscrow } = useInvoiceManagement();

	// Create a map of escrow transactions by invoice ID for quick lookup
	const escrowMap = new Map(
		escrowTransactions.map((transaction) => [transaction.invoiceId, transaction])
	);

	const handlePayInvoice = async (invoiceId: string) => {
		setIsProcessing(invoiceId);
		try {
			// In a real implementation, you would integrate with a payment processor
			// For now, we'll simulate the escrow payment process
			await processEscrowPayment({
				invoiceId,
				paymentMethod: "Credit Card", // This would come from a payment form
				transactionId: `txn_${Date.now()}`, // This would come from the payment processor
			});
			
			onInvoiceUpdate?.();
		} catch (error) {
			console.error("Failed to process payment:", error);
			// Show error message to user
		} finally {
			setIsProcessing(null);
		}
	};

	const handleReleaseEscrow = async (invoiceId: string) => {
		setIsProcessing(invoiceId);
		try {
			await releaseEscrowFunds({
				invoiceId,
				releaseReason: "Work completed satisfactorily",
			});
			
			onInvoiceUpdate?.();
		} catch (error) {
			console.error("Failed to release escrow funds:", error);
			// Show error message to user
		} finally {
			setIsProcessing(null);
		}
	};

	const handleDisputeEscrow = async (invoiceId: string) => {
		setIsProcessing(invoiceId);
		try {
			// In a real implementation, you would show a dispute form
			const disputeReason = prompt("Please provide a reason for the dispute:");
			if (disputeReason) {
				await disputeEscrow({
					invoiceId,
					disputeReason,
					notes: "Dispute initiated by booker",
				});
				
				onInvoiceUpdate?.();
			}
		} catch (error) {
			console.error("Failed to dispute escrow:", error);
			// Show error message to user
		} finally {
			setIsProcessing(null);
		}
	};

	const handleViewDetails = (invoiceId: string) => {
		// Navigate to invoice details page or open modal
		console.log("View invoice details:", invoiceId);
		// In a real implementation, you would navigate to a detailed invoice view
	};

	// Filter and sort invoices
	const sortedInvoices = [...invoices].sort((a, b) => {
		// Sort by status priority (unpaid first), then by due date
		const statusPriority = { sent: 0, overdue: 1, paid: 2, cancelled: 3, draft: 4 };
		const aPriority = statusPriority[a.status as keyof typeof statusPriority] ?? 5;
		const bPriority = statusPriority[b.status as keyof typeof statusPriority] ?? 5;
		
		if (aPriority !== bPriority) {
			return aPriority - bPriority;
		}
		
		return new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
	});

	if (invoices.length === 0) {
		return (
			<div className="text-center py-12">
				<div className="text-gray-500 mb-2">No invoices found</div>
				<div className="text-sm text-gray-400">
					{userRole === "booker" 
						? "Invoices will appear here when projects are completed"
						: "Invoices will appear here when your work is completed"
					}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-4">
			{/* Summary Stats */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
				<div className="bg-blue-50 rounded-lg p-4">
					<div className="text-sm text-blue-600 font-medium">Total Invoices</div>
					<div className="text-2xl font-bold text-blue-900">{invoices.length}</div>
				</div>
				<div className="bg-yellow-50 rounded-lg p-4">
					<div className="text-sm text-yellow-600 font-medium">Pending</div>
					<div className="text-2xl font-bold text-yellow-900">
						{invoices.filter(inv => inv.status === "sent").length}
					</div>
				</div>
				<div className="bg-green-50 rounded-lg p-4">
					<div className="text-sm text-green-600 font-medium">Paid</div>
					<div className="text-2xl font-bold text-green-900">
						{invoices.filter(inv => inv.status === "paid").length}
					</div>
				</div>
				<div className="bg-purple-50 rounded-lg p-4">
					<div className="text-sm text-purple-600 font-medium">In Escrow</div>
					<div className="text-2xl font-bold text-purple-900">
						{invoices.filter(inv => inv.escrowStatus === "funds_held").length}
					</div>
				</div>
			</div>

			{/* Invoice Cards */}
			<div className="space-y-4">
				{sortedInvoices.map((invoice) => (
					<InvoiceCard
						key={invoice.id}
						invoice={invoice}
						escrowTransaction={escrowMap.get(invoice.id)}
						onPayInvoice={userRole === "booker" ? handlePayInvoice : undefined}
						onReleaseEscrow={userRole === "booker" ? handleReleaseEscrow : undefined}
						onDisputeEscrow={userRole === "booker" ? handleDisputeEscrow : undefined}
						onViewDetails={handleViewDetails}
						userRole={userRole}
					/>
				))}
			</div>
		</div>
	);
};

export default InvoiceList;
