import type { Photo } from "../../instagram-photo-grid-data";

// Assuming Project type might be needed, or adjust as necessary.
// For now, using a generic size type.
// If Project interface is specific and used here, ensure it\'s defined or imported.
// For now, let\'s define a simple Size type for getGridClasses if Project is too complex or not solely used for this.
type GridItemSize =
	| "standard"
	| "horizontal"
	| "vertical"
	| "large"
	| undefined;

export const getGridClasses = (size?: GridItemSize) => {
	switch (size) {
		case "horizontal":
			return "col-span-2 row-span-1";
		case "vertical":
			return "col-span-1 row-span-2";
		case "large":
			return "col-span-2 row-span-2";
		default:
			return "col-span-1 row-span-1";
	}
};
