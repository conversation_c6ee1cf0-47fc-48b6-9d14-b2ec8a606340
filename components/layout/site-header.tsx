"use client";

import { <PERSON><PERSON> } from "@/components/ui/button"; // Adjust path as necessary
import { LogOut, Menu, Search, Shield } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type React from "react";
import { useAuth } from "@/components/hooks/use-auth";
import { useAdminCheck } from "@/components/hooks/use-admin-check";
import { useUser } from "reactfire";

const SiteHeader: React.FC = () => {
	const router = useRouter();
	const { data: user, status } = useUser();
	const { signOutUser, isSigningOut } = useAuth();
	const { isAdmin } = useAdminCheck();

	const handleSearchClick = () => {
		router.push('/search-creatives');
	};

	const handleSignOut = async () => {
		await signOutUser();
	};

	return (
		<header className="border-b border-gray-100">
			<div className="max-w-4xl mx-auto px-6 py-6">
				<div className="flex items-center justify-between">
					<Link href="/" className="text-2xl font-light tracking-wide">
						DUA
					</Link>

					<nav className="hidden md:flex items-center space-x-8 text-sm">
						<button
							onClick={handleSearchClick}
							className="text-gray-600 hover:text-black transition-colors cursor-pointer"
						>
							Search
						</button>
						<span className="text-black">Portfolio</span>
						<span className="text-gray-600">About</span>
						<span className="text-gray-600">Contact</span>
						<button
							onClick={handleSearchClick}
							className="text-gray-600 hover:text-black transition-colors cursor-pointer"
						>
							<Search className="w-4 h-4" />
						</button>
						{status === "success" && user && isAdmin && (
							<Link
								href="/admin"
								className="text-orange-600 hover:text-orange-800 transition-colors cursor-pointer flex items-center gap-1"
								title="Admin Dashboard"
							>
								<Shield className="w-4 h-4" />
								Admin
							</Link>
						)}
						{status === "success" && user && (
							<button
								onClick={handleSignOut}
								disabled={isSigningOut}
								className="text-gray-600 hover:text-black transition-colors cursor-pointer flex items-center gap-1 disabled:opacity-50"
								title="Sign Out"
							>
								<LogOut className="w-4 h-4" />
								{isSigningOut ? "Signing out..." : "Sign Out"}
							</button>
						)}
					</nav>

					<div className="md:hidden flex items-center gap-2">
						<Button
							variant="ghost"
							size="icon"
							onClick={handleSearchClick}
						>
							<Search className="h-5 w-5" />
						</Button>
						{status === "success" && user && isAdmin && (
							<Link href="/admin">
								<Button
									variant="ghost"
									size="icon"
									title="Admin Dashboard"
								>
									<Shield className="h-5 w-5 text-orange-600" />
								</Button>
							</Link>
						)}
						{status === "success" && user && (
							<Button
								variant="ghost"
								size="icon"
								onClick={handleSignOut}
								disabled={isSigningOut}
								title="Sign Out"
							>
								<LogOut className="h-5 w-5" />
							</Button>
						)}
					</div>
				</div>
			</div>
		</header>
	);
};

export default SiteHeader;
