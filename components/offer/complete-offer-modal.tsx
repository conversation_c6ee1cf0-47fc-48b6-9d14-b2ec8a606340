import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, X, FileText, DollarSign } from "lucide-react";
import { useState } from "react";
import type React from "react";
import type { Offer } from "../types";
import { useOfferManagement } from "../hooks/useOfferManagement";

interface CompleteOfferModalProps {
	offer: Offer;
	isOpen: boolean;
	onClose: () => void;
	onComplete?: () => void;
}

const CompleteOfferModal: React.FC<CompleteOfferModalProps> = ({
	offer,
	isOpen,
	onClose,
	onComplete,
}) => {
	const [notes, setNotes] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { completeOffer } = useOfferManagement();

	const handleComplete = async () => {
		setIsSubmitting(true);
		try {
			await completeOffer(offer.id, notes);
			onComplete?.();
			onClose();
		} catch (error) {
			console.error("Failed to complete offer:", error);
			// Show error message to user
		} finally {
			setIsSubmitting(false);
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
		}).format(amount);
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-hidden">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-100">
					<div className="flex items-center gap-3">
						<CheckCircle className="w-6 h-6 text-green-600" />
						<h2 className="text-xl font-medium text-gray-900">Complete Project</h2>
					</div>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600 transition-colors"
					>
						<X className="w-5 h-5" />
					</button>
				</div>

				{/* Content */}
				<div className="p-6 space-y-6">
					{/* Project Summary */}
					<div className="bg-gray-50 rounded-lg p-4">
						<h3 className="font-medium text-gray-900 mb-3">Project Summary</h3>
						<div className="space-y-2 text-sm">
							<div>
								<span className="font-medium text-gray-700">Project:</span>{" "}
								{offer.projectTitle}
							</div>
							<div>
								<span className="font-medium text-gray-700">Role:</span> {offer.role}
							</div>
							<div>
								<span className="font-medium text-gray-700">Creative:</span>{" "}
								{offer.creativeName}
							</div>
							<div className="flex items-center gap-2">
								<DollarSign className="w-4 h-4 text-gray-600" />
								<span className="font-medium text-gray-700">Amount:</span>
								<span className="font-semibold text-green-600">
									{formatCurrency(offer.amount)}
								</span>
							</div>
						</div>
					</div>

					{/* What happens next */}
					<div className="bg-blue-50 rounded-lg p-4">
						<div className="flex items-start gap-3">
							<FileText className="w-5 h-5 text-blue-600 mt-0.5" />
							<div>
								<h4 className="font-medium text-blue-900 mb-2">What happens next?</h4>
								<ul className="text-sm text-blue-800 space-y-1">
									<li>• An invoice will be automatically created</li>
									<li>• Funds will be held in escrow for secure payment</li>
									<li>• You can release funds once work is confirmed</li>
									<li>• The creative will be notified of completion</li>
								</ul>
							</div>
						</div>
					</div>

					{/* Notes */}
					<div>
						<label htmlFor="completion-notes" className="block text-sm font-medium text-gray-700 mb-2">
							Completion Notes (Optional)
						</label>
						<textarea
							id="completion-notes"
							value={notes}
							onChange={(e) => setNotes(e.target.value)}
							placeholder="Add any notes about the project completion..."
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
							rows={3}
						/>
					</div>

					{/* Warning */}
					<div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
						<p className="text-sm text-yellow-800">
							<strong>Note:</strong> Once you mark this project as complete, an invoice will be
							created and the payment process will begin. Make sure all work has been delivered
							and approved.
						</p>
					</div>
				</div>

				{/* Footer */}
				<div className="flex gap-3 p-6 border-t border-gray-100">
					<Button
						variant="outline"
						onClick={onClose}
						disabled={isSubmitting}
						className="flex-1"
					>
						Cancel
					</Button>
					<Button
						onClick={handleComplete}
						disabled={isSubmitting}
						className="flex-1 bg-green-600 hover:bg-green-700"
					>
						{isSubmitting ? (
							<>
								<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
								Completing...
							</>
						) : (
							<>
								<CheckCircle className="w-4 h-4 mr-2" />
								Complete Project
							</>
						)}
					</Button>
				</div>
			</div>
		</div>
	);
};

export default CompleteOfferModal;
