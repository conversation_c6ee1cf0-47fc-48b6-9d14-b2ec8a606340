import { useCallback } from "react";
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";

export interface CreateInvoiceRequest {
	offerId: string;
	dueDate?: string; // Optional, defaults to 30 days from issue date
	notes?: string;
}

export interface ProcessEscrowPaymentRequest {
	invoiceId: string;
	paymentMethod: string;
	transactionId?: string;
}

export interface ReleaseEscrowFundsRequest {
	invoiceId: string;
	releaseReason?: string;
}

export interface DisputeEscrowRequest {
	invoiceId: string;
	disputeReason: string;
	notes?: string;
}

export const useInvoiceManagement = () => {
	/**
	 * Create an invoice for a completed offer
	 */
	const createInvoice = useCallback(async (request: CreateInvoiceRequest) => {
		try {
			const response = await callFirebaseFunction("create_invoice", request);
			return response;
		} catch (error) {
			console.error("Failed to create invoice:", error);
			throw error;
		}
	}, []);

	/**
	 * Process escrow payment for an invoice (booker pays into escrow)
	 */
	const processEscrowPayment = useCallback(async (request: ProcessEscrowPaymentRequest) => {
		try {
			const response = await callFirebaseFunction("process_escrow_payment", request);
			return response;
		} catch (error) {
			console.error("Failed to process escrow payment:", error);
			throw error;
		}
	}, []);

	/**
	 * Release escrow funds to creative (after work completion confirmation)
	 */
	const releaseEscrowFunds = useCallback(async (request: ReleaseEscrowFundsRequest) => {
		try {
			const response = await callFirebaseFunction("release_escrow_funds", request);
			return response;
		} catch (error) {
			console.error("Failed to release escrow funds:", error);
			throw error;
		}
	}, []);

	/**
	 * Initiate dispute for escrow transaction
	 */
	const disputeEscrow = useCallback(async (request: DisputeEscrowRequest) => {
		try {
			const response = await callFirebaseFunction("dispute_escrow", request);
			return response;
		} catch (error) {
			console.error("Failed to dispute escrow:", error);
			throw error;
		}
	}, []);

	/**
	 * Refund escrow funds to booker (in case of cancellation or dispute resolution)
	 */
	const refundEscrowFunds = useCallback(async (invoiceId: string, refundReason?: string) => {
		try {
			const response = await callFirebaseFunction("refund_escrow_funds", {
				invoiceId,
				refundReason: refundReason || "Refund requested"
			});
			return response;
		} catch (error) {
			console.error("Failed to refund escrow funds:", error);
			throw error;
		}
	}, []);

	/**
	 * Mark invoice as paid (for non-escrow payments)
	 */
	const markInvoicePaid = useCallback(async (invoiceId: string, paymentDate?: string) => {
		try {
			const response = await callFirebaseFunction("mark_invoice_paid", {
				invoiceId,
				paymentDate: paymentDate || new Date().toISOString().split('T')[0]
			});
			return response;
		} catch (error) {
			console.error("Failed to mark invoice as paid:", error);
			throw error;
		}
	}, []);

	/**
	 * Cancel an invoice
	 */
	const cancelInvoice = useCallback(async (invoiceId: string, reason?: string) => {
		try {
			const response = await callFirebaseFunction("cancel_invoice", {
				invoiceId,
				reason: reason || "Invoice cancelled"
			});
			return response;
		} catch (error) {
			console.error("Failed to cancel invoice:", error);
			throw error;
		}
	}, []);

	return {
		createInvoice,
		processEscrowPayment,
		releaseEscrowFunds,
		disputeEscrow,
		refundEscrowFunds,
		markInvoicePaid,
		cancelInvoice,
	};
};
