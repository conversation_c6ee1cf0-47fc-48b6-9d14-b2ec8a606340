import { useCallback, useState } from "react";
import type { Photo } from "../../instagram-photo-grid-data"; // Adjust path as needed

interface UsePhotoDetailModalProps {
	initialLikedPhotos: Set<string>;
	onToggleLike: (photoId: string) => void;
}

export const usePhotoDetailModal = ({
	initialLikedPhotos,
	onToggleLike,
}: UsePhotoDetailModalProps) => {
	const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);
	// The likedPhotos state and toggleLike function are managed by the parent component (InstagramPhotoGrid)
	// and passed into this hook, which then passes them to the PhotoDetailModal component.
	// This hook itself doesn't own the likedPhotos state, it just facilitates its use by the modal.

	const openPhotoDetail = useCallback((photo: Photo) => {
		setSelectedPhoto(photo);
	}, []);

	const closePhotoDetail = useCallback(() => {
		setSelectedPhoto(null);
	}, []);

	return {
		selectedPhoto,
		openPhotoDetail,
		closePhotoDetail,
		// We also need to return likedPhotos and onToggleLike to be passed to the modal component
		// However, these are passed into the hook, so the calling component already has them.
		// The modal component will receive them directly from the main component or via this hook's return if we choose.
		// For simplicity, let's have the main component continue to pass likedPhotos and onToggleLike directly to the modal.
		// This hook will only manage the selectedPhoto state and open/close actions.
	};
};
