import { useCallback } from "react";
import type { Photo } from "../../instagram-photo-grid-data"; // Adjust as needed
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { useRouter } from "next/navigation";

interface UseTileOperationsProps {
	photos: Photo[];
	setPhotos: React.Dispatch<React.SetStateAction<Photo[]>>;
	selectedTiles: Set<string>;
	clearSelection: () => void;
	isSelectionMode: boolean;
	toggleSelectionMode: () => void;
	creativeId: string;
}

export const useTileOperations = ({
	photos,
	setPhotos,
	selectedTiles,
	clearSelection,
	isSelectionMode,
	toggleSelectionMode,
	creativeId,
}: UseTileOperationsProps) => {
	const router = useRouter();

	const createPortfolioTile = useCallback(
		async (type: "text" | "image" | "iframe" = "text") => {
			if (type === "image") {
				console.warn(
					"Use uploadAndCreateImageTile for creating image tiles.",
				);
				return;
			}

			const newPosition =
				photos.length > 0
					? Math.max(...photos.map((p) => p.position || 0)) + 1
					: 0;

			let payload;
			if (type === "iframe") {
				payload = {
					creativeId,
					data: {
						type: "iframe" as const,
						position: newPosition,
						size: "standard" as const,
						textContent: null,
						backgroundColor: null,
						imageUrl: null,
						alt: "Embedded content",
						fontSize: null,
						fontFamily: null,
						isMerged: false,
						originalItems: [],
						iframeUrl: "https://www.youtube.com/embed/dQw4w9WgXcQ",
					},
				};
			} else {
				payload = {
					creativeId,
					data: {
						type: "text" as const,
						position: newPosition,
						size: "standard" as const,
						textContent: "New Text Tile",
						backgroundColor: "#e5e7eb",
						imageUrl: null,
						alt: null,
						fontSize: "16px",
						fontFamily: "Arial",
						isMerged: false,
						originalItems: [],
						iframeUrl: null,
					},
				};
			}

			try {
				await callFirebaseFunction("create_portfolio_item", payload);
				console.log(`Successfully created a new ${type} tile.`);
				router.refresh();
			} catch (error) {
				console.error("Failed to create portfolio item:", error);
			}
		},
		[creativeId, photos, router],
	);

	const createIframeTile = useCallback(
		async (iframeUrl: string) => {
			console.log("Creating iframe tile with URL:", iframeUrl);

			// Enhance the URL with video controls
			const enhanceVideoUrl = (url: string): string => {
				try {
					const urlObj = new URL(url);

					// YouTube enhancements
					if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
						urlObj.searchParams.set('autoplay', '1');
						urlObj.searchParams.set('loop', '1');
						urlObj.searchParams.set('controls', '0');
						urlObj.searchParams.set('modestbranding', '1');
						urlObj.searchParams.set('rel', '0');
						urlObj.searchParams.set('mute', '1'); // Mute for autoplay to work

						// For looping to work on YouTube, we need the playlist parameter
						const videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
						if (videoId) {
							urlObj.searchParams.set('playlist', videoId);
						}
					}
					// Vimeo enhancements
					else if (urlObj.hostname.includes('vimeo.com')) {
						urlObj.searchParams.set('autoplay', '1');
						urlObj.searchParams.set('loop', '1');
						urlObj.searchParams.set('controls', '0');
						urlObj.searchParams.set('muted', '1');
					}
					// Generic video enhancements
					else {
						urlObj.searchParams.set('autoplay', '1');
						urlObj.searchParams.set('controls', '0');
					}

					return urlObj.toString();
				} catch (error) {
					console.warn('Failed to enhance video URL:', error);
					return url;
				}
			};

			const enhancedUrl = enhanceVideoUrl(iframeUrl);
			console.log("Enhanced URL:", enhancedUrl);

			const newPosition =
				photos.length > 0
					? Math.max(...photos.map((p) => p.position || 0)) + 1
					: 0;

			const payload = {
				creativeId,
				data: {
					type: "iframe" as const,
					position: newPosition,
					size: "standard" as const,
					textContent: null,
					backgroundColor: null,
					imageUrl: null,
					alt: "Embedded content",
					fontSize: null,
					fontFamily: null,
					isMerged: false,
					originalItems: [],
					iframeUrl: enhancedUrl,
				},
			};

			try {
				await callFirebaseFunction("create_portfolio_item", payload);
				console.log("Successfully created iframe tile!");
				router.refresh();
			} catch (error) {
				console.error("Failed to create iframe portfolio item:", error);
			}
		},
		[creativeId, photos, router],
	);

	const uploadAndCreateImageTile = useCallback(
		async (file: File) => {
			if (!file || !creativeId) {
				console.error("File or creative ID is missing.");
				return;
			}

			const storage = getStorage();
			const fileName = `portfolio/${creativeId}/${Date.now()}-${file.name}`;
			const storageRef = ref(storage, fileName);

			try {
				console.log(`Uploading ${file.name} to ${fileName}...`);
				const snapshot = await uploadBytes(storageRef, file);
				const downloadURL = await getDownloadURL(snapshot.ref);
				console.log("File available at", downloadURL);

				const newPosition =
					photos.length > 0
						? Math.max(...photos.map((p) => p.position || 0)) + 1
						: 0;

				const payload = {
					creativeId,
					data: {
						type: "image" as const,
						position: newPosition,
						size: "standard" as const,
						imageUrl: downloadURL,
						alt: file.name,
						textContent: null,
						backgroundColor: null,
						fontSize: null,
						fontFamily: null,
						isMerged: false,
						originalItems: [],
					},
				};

				await callFirebaseFunction("create_portfolio_item", payload);
				console.log("Successfully created image portfolio item in Firestore.");
				router.refresh();
			} catch (error) {
				console.error(
					"Failed to upload image or create portfolio item:",
					error,
				);
			}
		},
		[creativeId, photos, router],
	);

	const mergeTiles = useCallback(
		async (direction: "horizontal" | "vertical" | "large") => {
			if (selectedTiles.size < 2) {
				console.warn("Merge attempt with fewer than 2 tiles selected.");
				return;
			}

			const selectedPhotosArray = photos
				.filter((photo) => selectedTiles.has(photo.id))
				.sort((a, b) => (a.position || 0) - (b.position || 0));

			if (selectedPhotosArray.length === 0) {
				console.error(
					"Selected photos array is empty despite selectedTiles having items.",
				);
				return;
			}

			if (direction === "large") {
				console.log("[Large Merge Debug] Initiating large merge.");
				console.log(
					"[Large Merge Debug] Selected tiles count (from selectedTiles.size):",
					selectedTiles.size,
				);
				console.log(
					"[Large Merge Debug] Selected photos array length:",
					selectedPhotosArray.length,
				);
				console.log(
					"[Large Merge Debug] Selected photo IDs:",
					selectedPhotosArray.map((p) => p.id),
				);
				console.log(
					"[Large Merge Debug] Selected photo positions:",
					selectedPhotosArray.map((p) => p.position),
				);
			}

			const leftMostOverall = selectedPhotosArray[0];
			const firstPotentialImage = selectedPhotosArray.find(
				(p) => p.type === "image" || p.type === undefined,
			);
			const firstPotentialIframe = selectedPhotosArray.find(
				(p) => p.type === "iframe",
			);

			let mergedType: "image" | "text" | "iframe";
			let finalSrc: string;
			let finalAlt = "Merged content";
			let finalTextContent: string | undefined = undefined;
			let finalBackgroundColor: string | undefined = undefined;
			let finalIframeUrl: string | undefined = undefined;

			// Priority: iframe > image > text
			if (firstPotentialIframe) {
				mergedType = "iframe";
				finalSrc = "/placeholder.svg?text=Merged+Iframe";
				finalAlt = "Merged iframe content";
				finalIframeUrl = firstPotentialIframe.iframeUrl;
				finalTextContent = undefined;
				finalBackgroundColor = undefined;
			} else if (firstPotentialImage) {
				mergedType = "image";
				finalSrc = firstPotentialImage.src;
				finalAlt = firstPotentialImage.alt;
				finalTextContent = undefined;
				finalBackgroundColor = undefined;
				finalIframeUrl = undefined;
			} else {
				mergedType = "text";
				finalSrc = "/placeholder.svg?text=Merged+Text";
				finalAlt = "Merged text content";
				finalTextContent =
					selectedPhotosArray.map((p) => p.textContent || "").join("\n") ||
					"Merged text";
				finalBackgroundColor = leftMostOverall.backgroundColor || "#f3f4f6";
				finalIframeUrl = undefined;
			}

			if (direction === "large") {
				console.log(
					"[Large Merge Debug] Leftmost overall photo ID for position:",
					leftMostOverall.id,
					"pos:",
					leftMostOverall.position,
				);
				console.log(
					"[Large Merge Debug] First potential image for content ID:",
					firstPotentialImage ? firstPotentialImage.id : "None",
				);
				console.log(
					"[Large Merge Debug] Determined mergedType:",
					mergedType,
					"finalSrc:",
					finalSrc,
				);
			}

			const minPosition = leftMostOverall.position || 0;

			const mergedPhoto: Photo = {
				id: `merged-${Date.now()}`,
				src: finalSrc,
				alt: finalAlt,
				likes: selectedPhotosArray.reduce((sum, p) => sum + (p.likes || 0), 0),
				comments: selectedPhotosArray.reduce(
					(sum, p) => sum + (p.comments || 0),
					0,
				),
				size: direction,
				isMerged: true,
				originalPhotos: [...selectedPhotosArray],
				position: minPosition,
				type: mergedType,
				textContent: finalTextContent,
				backgroundColor: finalBackgroundColor,
				iframeUrl: finalIframeUrl,
			};

			if (direction === "large") {
				console.log(
					"[Large Merge Debug] Created mergedPhoto object:",
					JSON.parse(JSON.stringify(mergedPhoto)),
				);
			}

			const remainingPhotos = photos.filter(
				(photo) => !selectedTiles.has(photo.id),
			);
			const newPhotos = [...remainingPhotos, mergedPhoto].sort(
				(a, b) => (a.position || 0) - (b.position || 0),
			);

			if (direction === "large") {
				console.log(
					"[Large Merge Debug] New photos array length:",
					newPhotos.length,
				);
			}

			try {

				// Step 1: Delete the original tiles from backend (non-iframe tiles only)
				const deletePromises = selectedPhotosArray.map((photo) => {
					const payload = {
						creativeId: creativeId,
						itemId: photo.id,
					};
					return callFirebaseFunction("delete_portfolio_item", payload);
				});

				await Promise.all(deletePromises);
				console.log(`Successfully deleted ${selectedPhotosArray.length} original tiles from backend.`);

				// Step 2: Create the new merged tile in backend
				const createMergedPayload = {
					creativeId: creativeId,
					data: {
						type: mergedPhoto.type,
						textContent: mergedPhoto.textContent,
						backgroundColor: mergedPhoto.backgroundColor,
						fontSize: mergedPhoto.fontSize || "16px",
						fontFamily: mergedPhoto.fontFamily || "Arial",
						imageUrl: mergedPhoto.type === "image" ? mergedPhoto.src : undefined,
						alt: mergedPhoto.alt,
						size: direction,
						isMerged: true,
						originalItems: selectedPhotosArray.map(photo => photo.id),
						position: mergedPhoto.position,
						likes: mergedPhoto.likes,
						comments: mergedPhoto.comments,
						...(mergedPhoto.type === "iframe" && { iframeUrl: mergedPhoto.iframeUrl }),
					},
				};

				const createResult = await callFirebaseFunction("create_portfolio_item", createMergedPayload);
				console.log("Successfully created merged tile in backend:", createResult);

				// Update the merged photo with the backend-generated ID
				const updatedMergedPhoto = {
					...mergedPhoto,
					id: (createResult as any)?.itemId || mergedPhoto.id,
				};

				// Step 3: Update local state with the new merged tile
				const remainingPhotos = photos.filter(
					(photo) => !selectedTiles.has(photo.id),
				);
				const newPhotos = [...remainingPhotos, updatedMergedPhoto].sort(
					(a, b) => (a.position || 0) - (b.position || 0),
				);

				setPhotos(newPhotos);
				clearSelection();
				if (isSelectionMode) {
					toggleSelectionMode();
				}

				if (direction === "large") {
					console.log("[Large Merge Debug] Large merge process completed.");
				}
			} catch (error) {
				console.error("Failed to merge tiles in backend:", error);
				// Fallback: still update local state even if backend fails
				setPhotos(newPhotos);
				clearSelection();
				if (isSelectionMode) {
					toggleSelectionMode();
				}
			}
		},
		[
			photos,
			selectedTiles,
			setPhotos,
			clearSelection,
			isSelectionMode,
			toggleSelectionMode,
			creativeId,
		],
	);

	const splitTile = useCallback(
		(photoToSplit: Photo) => {
			if (!photoToSplit.isMerged || !photoToSplit.originalPhotos) {
				return;
			}

			const mergedPosition = photoToSplit.position || 0;
			const photosWithoutMerged = photos.filter(
				(p) => p.id !== photoToSplit.id,
			);
			const originalPhotos = photoToSplit.originalPhotos.map(
				(p): Photo => ({
					...p,
					position: p.position != null ? p.position : mergedPosition,
					isMerged: false,
					size: p.size || "standard",
					src: p.src,
				}),
			);

			const newPhotos = [...photosWithoutMerged, ...originalPhotos].sort(
				(a, b) => (a.position || 0) - (b.position || 0),
			);
			setPhotos(newPhotos);
		},
		[photos, setPhotos],
	);

	const convertToTextTile = useCallback(async () => {
		const conversionPromises = Array.from(selectedTiles).map((photoId) => {
			const originalPhoto = photos.find((p) => p.id === photoId);
			if (!originalPhoto) {
				console.warn(`Photo with id ${photoId} not found for conversion.`);
				return Promise.resolve();
			}

			const payload = {
				creativeId: creativeId,
				itemId: photoId,
				data: {
					type: "text" as const,
					textContent: originalPhoto.textContent || "Click to edit text",
					backgroundColor: originalPhoto.backgroundColor || "#f3f4f6",
					fontSize: originalPhoto.fontSize || "16px",
					fontFamily: originalPhoto.fontFamily || "Arial",
				},
			};
			return callFirebaseFunction("update_portfolio_item", payload);
		});

		try {
			await Promise.all(conversionPromises);
			console.log(
				`Successfully converted ${selectedTiles.size} tile(s) to text.`,
			);

			// Update local state to reflect the conversion
			setPhotos(prevPhotos =>
				prevPhotos.map(photo => {
					if (selectedTiles.has(photo.id)) {
						return {
							...photo,
							type: "text" as const,
							textContent: photo.textContent || "Click to edit text",
							backgroundColor: photo.backgroundColor || "#f3f4f6",
							fontSize: photo.fontSize || "16px",
							fontFamily: photo.fontFamily || "Arial",
							src: `/placeholder.svg?text=${encodeURIComponent(photo.textContent || "Text")}`,
						};
					}
					return photo;
				})
			);

			clearSelection();
		} catch (error) {
			console.error("Failed to convert one or more tiles to text:", error);
		}
	}, [photos, selectedTiles, clearSelection, creativeId, setPhotos]);

	const convertToImageTile = useCallback(async () => {
		if (selectedTiles.size === 0) {
			console.warn("No tiles selected for conversion to image.");
			return;
		}

		// Create a file input element to trigger file selection
		const fileInput = document.createElement('input');
		fileInput.type = 'file';
		fileInput.accept = 'image/*';
		fileInput.multiple = selectedTiles.size > 1;

		return new Promise<void>((resolve, reject) => {
			fileInput.onchange = async (event) => {
				const files = (event.target as HTMLInputElement).files;
				if (!files || files.length === 0) {
					console.log("No files selected for image conversion.");
					resolve();
					return;
				}

				try {
					const selectedTilesArray = Array.from(selectedTiles);
					const conversionPromises = selectedTilesArray.map(async (photoId, index) => {
						const originalPhoto = photos.find((p) => p.id === photoId);
						if (!originalPhoto) {
							console.warn(`Photo with id ${photoId} not found for conversion.`);
							return;
						}

						// Use the corresponding file, or the first file if there are fewer files than tiles
						const file = files[index] || files[0];

						// Upload the image to Firebase Storage
						const storage = getStorage();
						const fileName = `portfolio/${creativeId}/${Date.now()}-${file.name}`;
						const storageRef = ref(storage, fileName);

						console.log(`Uploading ${file.name} for tile conversion...`);
						const snapshot = await uploadBytes(storageRef, file);
						const downloadURL = await getDownloadURL(snapshot.ref);
						console.log("File available at", downloadURL);

						// Update the tile in the backend
						const payload = {
							creativeId: creativeId,
							itemId: photoId,
							data: {
								type: "image" as const,
								textContent: null,
								backgroundColor: null,
								fontSize: null,
								fontFamily: null,
								imageUrl: downloadURL,
								alt: file.name,
							},
						};

						await callFirebaseFunction("update_portfolio_item", payload);

						return {
							photoId,
							downloadURL,
							alt: file.name,
						};
					});

					const results = await Promise.all(conversionPromises);
					console.log(`Successfully converted ${selectedTiles.size} tile(s) to image with uploads.`);

					// Update local state to reflect the conversion
					setPhotos(prevPhotos =>
						prevPhotos.map(photo => {
							const result = results.find(r => r?.photoId === photo.id);
							if (result) {
								return {
									...photo,
									type: "image" as const,
									textContent: undefined,
									backgroundColor: undefined,
									fontSize: undefined,
									fontFamily: undefined,
									src: result.downloadURL,
									alt: result.alt,
								};
							}
							return photo;
						})
					);

					clearSelection();
					resolve();
				} catch (error) {
					console.error("Failed to convert tiles to image with upload:", error);
					reject(error);
				}
			};

			fileInput.oncancel = () => {
				console.log("File selection cancelled.");
				resolve();
			};

			// Trigger the file selection dialog
			fileInput.click();
		});
	}, [photos, selectedTiles, clearSelection, creativeId, setPhotos]);

	const deleteSelectedTiles = useCallback(async () => {
		if (selectedTiles.size === 0) {
			console.warn("Delete attempt with no tiles selected.");
			return;
		}

		const selectedPhotosArray = photos.filter(photo => selectedTiles.has(photo.id));

		try {
			const deletePromises = selectedPhotosArray.map((photo) => {
				const payload = {
					creativeId: creativeId,
					itemId: photo.id,
				};
				return callFirebaseFunction("delete_portfolio_item", payload);
			});

			const results = await Promise.all(deletePromises);
			console.log(
				`Deletion results for ${selectedPhotosArray.length} item(s):`,
				results,
			);

			// Update local state to remove all deleted items
			setPhotos(prevPhotos =>
				prevPhotos.filter(photo => !selectedTiles.has(photo.id))
			);

			clearSelection();
			console.log(`Successfully deleted ${selectedTiles.size} tile(s)`);
		} catch (error) {
			console.error("Failed to delete one or more portfolio items:", error);
		}
	}, [selectedTiles, clearSelection, creativeId, setPhotos, photos]);

	const saveTileToBackend = useCallback(async (
		tileId: string,
		updates: {
			textContent?: string;
			backgroundColor?: string;
			fontSize?: string;
			fontFamily?: string;
			imageUrl?: string;
			alt?: string;
			type?: "text" | "image" | "iframe";
			iframeUrl?: string;
		}
	) => {
		try {
			// Structure payload according to UpdatePortfolioItemFullRequest interface
			const payload = {
				creativeId,
				itemId: tileId,
				data: updates,
			};

			await callFirebaseFunction("update_portfolio_item", payload);
			console.log(`Successfully updated tile ${tileId} in backend.`);

			// Update local state to reflect the changes
			setPhotos(prevPhotos =>
				prevPhotos.map(photo => {
					if (photo.id === tileId) {
						return {
							...photo,
							...updates,
							// Update src for text tiles to reflect new content
							...(updates.type === "text" && updates.textContent && {
								src: `/placeholder.svg?text=${encodeURIComponent(updates.textContent)}`
							})
						};
					}
					return photo;
				})
			);
		} catch (error) {
			console.error(`Failed to save tile ${tileId} to backend:`, error);
			throw error; // Re-throw so calling code can handle the error
		}
	}, [creativeId, setPhotos]);

	const reorderTiles = useCallback(async (newOrder: Photo[]) => {
		try {
			// Update local state immediately for better UX
			setPhotos(newOrder);

			// Prepare payload for backend according to UpdatePortfolioItemsOrderFullRequest interface
			const orderedItemIds = newOrder.map(photo => photo.id);
			const payload = {
				creativeId,
				data: {
					orderedItemIds,
				},
			};

			await callFirebaseFunction("update_portfolio_items_order", payload);
			console.log("Successfully updated portfolio order in backend.");
		} catch (error) {
			console.error("Failed to update portfolio order in backend:", error);
			// Revert to original order on error
			setPhotos(photos);
			throw error;
		}
	}, [creativeId, setPhotos, photos]);

	return {
		createPortfolioTile,
		createIframeTile,
		uploadAndCreateImageTile,
		mergeTiles,
		splitTile,
		convertToTextTile,
		convertToImageTile,
		deleteSelectedTiles,
		saveTileToBackend,
		reorderTiles,
	};
};
