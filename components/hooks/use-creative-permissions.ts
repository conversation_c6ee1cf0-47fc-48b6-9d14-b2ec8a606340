import { useEffect, useState } from "react";
import { useUser } from "reactfire";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/src/components/FirebaseComponents";

interface UseCreativePermissionsResult {
	hasEditPermission: boolean;
	isLoading: boolean;
	error: string | null;
}

/**
 * Hook to determine if the current user has edit permissions for a specific creative profile
 * @param creativeId - The ID of the creative profile being viewed
 * @returns Object containing permission status, loading state, and error
 */
export const useCreativePermissions = (creativeId: string): UseCreativePermissionsResult => {
	const { data: user, status: userStatus } = useUser();
	const [hasEditPermission, setHasEditPermission] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		let isCancelled = false;

		const checkPermissions = async () => {
			if (isCancelled) return;

			setIsLoading(true);
			setError(null);

			// If user is not authenticated, they have no edit permissions
			if (userStatus !== "success" || !user) {
				if (!isCancelled) {
					setHasEditPermission(false);
					setIsLoading(false);
				}
				return;
			}

			// If creativeId is empty or undefined, don't proceed
			if (!creativeId || creativeId.trim() === "") {
				if (!isCancelled) {
					setHasEditPermission(false);
					setIsLoading(false);
				}
				return;
			}

			try {
				// Check if the current user's ID matches the creative ID (direct ownership)
				if (user.uid === creativeId) {
					if (!isCancelled) {
						setHasEditPermission(true);
						setIsLoading(false);
					}
					return;
				}

				// Check user-root-accounts for creative permissions
				const userRootRef = doc(db, "user-root-accounts", user.uid);
				const userRootSnap = await getDoc(userRootRef);

				if (isCancelled) return; // Check if cancelled after async operation

				if (userRootSnap.exists()) {
					const userRootData = userRootSnap.data();
					const creatives = userRootData.creatives || {};

					// Check if user has editor or admin permissions for this creative
					const permission = creatives[creativeId];
					const hasPermission = permission === "editor" || permission === "admin";

					if (!isCancelled) {
						setHasEditPermission(hasPermission);
					}
				} else {
					// User root account doesn't exist, no permissions
					if (!isCancelled) {
						setHasEditPermission(false);
					}
				}
			} catch (err: any) {
				if (isCancelled) return; // Don't update state if cancelled

				console.error("Error checking creative permissions:", err);

				// Handle specific Firebase permission errors gracefully
				if (err.code === "permission-denied" || err.message?.includes("Missing or insufficient permissions")) {
					console.log("Permission denied - user likely signed out, setting no permissions");
					setHasEditPermission(false);
					setError(null); // Don't show error for permission denied during sign out
				} else {
					setError("Failed to check permissions");
					setHasEditPermission(false);
				}
			} finally {
				if (!isCancelled) {
					setIsLoading(false);
				}
			}
		};

		checkPermissions();

		// Cleanup function to cancel pending operations
		return () => {
			isCancelled = true;
		};
	}, [user, userStatus, creativeId]);

	return {
		hasEditPermission,
		isLoading,
		error,
	};
};
