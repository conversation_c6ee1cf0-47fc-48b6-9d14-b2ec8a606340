import { useCallback, useState } from "react";

export const usePhotoSelection = () => {
	const [selectedTiles, setSelectedTiles] = useState<Set<string>>(new Set());
	const [isSelectionMode, setIsSelectionMode] = useState(false);

	const toggleTileSelection = useCallback(
		(photoId: string) => {
			// We only allow tile selection if selection mode is active.
			// The calling component (PhotoGridPanel) should already enforce this,
			// but it's a good safeguard.
			if (!isSelectionMode) return;

			setSelectedTiles((prevSelectedTiles) => {
				const newSet = new Set(prevSelectedTiles);
				if (newSet.has(photoId)) {
					newSet.delete(photoId);
				} else {
					newSet.add(photoId);
				}
				return newSet;
			});
		},
		[isSelectionMode],
	); // Depends on isSelectionMode to ensure closure is fresh if mode changes

	const clearSelection = useCallback(() => {
		setSelectedTiles(new Set());
	}, []);

	const toggleSelectionMode = useCallback(() => {
		setIsSelectionMode((prevMode) => {
			const newMode = !prevMode;
			if (!newMode) {
				// If we are turning selection mode OFF, clear any existing selection.
				setSelectedTiles(new Set());
			}
			return newMode;
		});
	}, []); // No dependencies, setSelectedTiles is stable

	return {
		selectedTiles,
		isSelectionMode,
		selectedTilesCount: selectedTiles.size,
		toggleTileSelection,
		clearSelection,
		toggleSelectionMode,
	};
};
