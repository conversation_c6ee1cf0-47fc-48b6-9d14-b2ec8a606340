import { useEffect, useState } from "react";

const getVisibleMonths = (width: number): number => {
	if (width < 640) {
		// Tailwind's 'sm' breakpoint
		return 1;
	} else if (width < 1024) {
		// Tailwind's 'lg' breakpoint
		return 2;
	}
	return 3;
};

const useResponsiveMonths = (): number => {
	const [visibleMonths, setVisibleMonths] = useState<number>(() => {
		if (typeof window !== "undefined") {
			return getVisibleMonths(window.innerWidth);
		}
		return 3; // Default to 3 for SSR or environments without window
	});

	useEffect(() => {
		if (typeof window === "undefined") {
			return;
		}

		const handleResize = () => {
			setVisibleMonths(getVisibleMonths(window.innerWidth));
		};

		window.addEventListener("resize", handleResize);
		// Call handler right away so state gets updated with initial window size
		handleResize();

		return () => window.removeEventListener("resize", handleResize);
	}, []);

	return visibleMonths;
};

export default useResponsiveMonths;
