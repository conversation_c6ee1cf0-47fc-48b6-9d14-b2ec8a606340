import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";
import type { Project } from "../types";
import type { NewProjectFormData } from "./use-add-project-form";

export const useBookerProjects = (bookerId: string, bookerName: string) => {
	const router = useRouter();
	const createProject = useCallback(
		async (formData: NewProjectFormData) => {
			const payload = {
				bookerId: bookerId,
				...formData,
				budget: Number.parseInt(formData.budget, 10) || 0,
				status: "planning",
			};

			try {
				await callFirebaseFunction("create_project", payload);
			} catch (error) {
				console.error("Failed to create project:", error);
				// Re-throw or handle error as needed for the UI
				throw error;
			}
		},
		[bookerId],
	);

	const deleteProject = useCallback(async (projectId: string) => {
		const payload = { projectId };
		try {
			await callFirebaseFunction("delete_project", payload);
			// Refresh the page to show updated project list
			router.refresh();
		} catch (error) {
			console.error("Failed to delete project:", error);
			throw error;
		}
	}, [router]);

	const sendOffer = useCallback(
		async (
			project: Project,
			creativeId: string,
			creativeName: string,
			role: string,
		) => {
			const payload = {
				projectId: project.id,
				projectTitle: project.title,
				bookerId: bookerId,
				bookerName: bookerName,
				creativeId: creativeId,
				creativeName: creativeName,
				role: role,
				amount: project.roleBudgetOverrides?.[role]
					? Number.parseInt(project.roleBudgetOverrides[role], 10)
					: project.budget / project.creativesNeeded.length,
				status: "pending",
				projectType: project.projectType,
				location: project.location,
				description: project.description,
				isSentByCreative: false,
			};

			try {
				await callFirebaseFunction("submit_offer_to_creative", payload);
				// Refresh the page to show updated offers
				router.refresh();
			} catch (error) {
				console.error("Failed to send offer:", error);
				throw error;
			}
		},
		[bookerId, bookerName],
	);

	// Future: Add updateProject function here

	return {
		createProject,
		deleteProject,
		sendOffer,
	};
};
