import { useCallback, useState } from "react";
import type { PastCollaboration } from "../types"; // Adjust if PastCollaboration type is defined elsewhere

export interface ReviewFormData {
	rating: number;
	review: string;
}

export interface UseReviewModalReturn {
	showReviewModal: boolean;
	selectedCollaboration: PastCollaboration | null;
	reviewForm: ReviewFormData;
	openReviewModal: (collaboration: PastCollaboration) => void;
	closeReviewModal: () => void;
	updateReviewFormField: (
		field: keyof ReviewFormData,
		value: string | number,
	) => void;
	handleReviewSubmit: (e: React.FormEvent) => void; // Consider making this async if it involves API calls
}

const initialReviewFormState: ReviewFormData = {
	rating: 0,
	review: "",
};

export const useReviewModal = (): UseReviewModalReturn => {
	const [showReviewModal, setShowReviewModal] = useState(false);
	const [selectedCollaboration, setSelectedCollaboration] =
		useState<PastCollaboration | null>(null);
	const [reviewForm, setReviewForm] = useState<ReviewFormData>(
		initialReviewFormState,
	);

	const openReviewModal = useCallback((collaboration: PastCollaboration) => {
		setSelectedCollaboration(collaboration);
		setReviewForm({
			rating: collaboration.rating || 0,
			review: collaboration.review || "",
		});
		setShowReviewModal(true);
	}, []);

	const closeReviewModal = useCallback(() => {
		setShowReviewModal(false);
		setSelectedCollaboration(null);
		setReviewForm(initialReviewFormState);
	}, []);

	const updateReviewFormField = useCallback(
		(field: keyof ReviewFormData, value: string | number) => {
			setReviewForm((prev) => ({
				...prev,
				[field]: value,
			}));
		},
		[],
	);

	const handleReviewSubmit = useCallback(
		(e: React.FormEvent) => {
			e.preventDefault();
			// TODO: Implement actual review submission logic (e.g., API call)
			console.log(
				"Review submitted:",
				reviewForm,
				"for collaboration:",
				selectedCollaboration?.id,
			);
			alert("Review updated successfully! (Placeholder)"); // Placeholder
			// Potentially update the pastCollaborations list in the parent component or re-fetch
			closeReviewModal();
		},
		[reviewForm, selectedCollaboration, closeReviewModal],
	);

	return {
		showReviewModal,
		selectedCollaboration,
		reviewForm,
		openReviewModal,
		closeReviewModal,
		updateReviewFormField,
		handleReviewSubmit,
	};
};
