import { useCallback, useState } from "react";
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";
import type { CreateOfferRequest } from "../../types/requests/offerRequestInterfaces";
import type { Project } from "../../instagram-photo-grid-data"; // Adjust path as needed

// Define the shape of the booking form
interface BookingFormData {
	projectId: string;
	projectTitle: string;
	projectType: string;
	startDate: string;
	endDate: string;
	location: string;
	budget: string;
	description: string;
	role: string;
	proposedRate: string;
	rateType: string;
	notes: string;
}

const initialBookingFormState: BookingFormData = {
	projectId: "",
	projectTitle: "",
	projectType: "",
	startDate: "",
	endDate: "",
	location: "",
	budget: "",
	description: "",
	role: "",
	proposedRate: "",
	rateType: "",
	notes: "",
};

interface UseBookingModalProps {
	creativeId?: string;
	creativeName?: string;
	bookerId?: string;
	bookerName?: string;
}

export const useBookingModal = (props: UseBookingModalProps = {}) => {
	const { creativeId, creativeName, bookerId, bookerName } = props;
	const [showBookingModal, setShowBookingModal] = useState(false);
	const [bookingForm, setBookingForm] = useState<BookingFormData>(
		initialBookingFormState,
	);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const openBookingModal = useCallback(() => {
		setShowBookingModal(true);
	}, []);

	const closeBookingModal = useCallback(() => {
		setShowBookingModal(false);
		setBookingForm(initialBookingFormState); // Reset form on close
	}, []);

	const updateBookingFormField = useCallback(
		(field: keyof BookingFormData, value: string) => {
			setBookingForm((prev) => ({
				...prev,
				[field]: value,
			}));
		},
		[],
	);

	const handleBookingFormProjectSelect = useCallback(
		(selectedProject: Project | null | undefined) => {
			if (selectedProject) {
				setBookingForm({
					// Reset fields not directly from project, or pre-fill them as makes sense
					...initialBookingFormState, // Start with a clean slate for non-project fields
					projectId: selectedProject.id,
					projectTitle: selectedProject.title,
					projectType: selectedProject.projectType || "",
					startDate: selectedProject.startDate || "",
					endDate: selectedProject.endDate || "",
					location: selectedProject.location || "",
					budget: selectedProject.budget.toString(),
					description: selectedProject.description || "",
					// role, proposedRate, rateType, notes are kept empty or reset
				});
			} else {
				setBookingForm(initialBookingFormState); // Reset if no project is selected
			}
		},
		[],
	);

	const handleBookingSubmit = useCallback(
		async (event: React.FormEvent) => {
			event.preventDefault();
			if (isSubmitting) return;

			// Validate required fields
			if (!bookingForm.projectId || !creativeId || !bookerId || bookerId.trim() === "") {
				console.error("Missing required fields for booking submission");
				return;
			}

			// Validate date fields
			if (!bookingForm.startDate || !bookingForm.endDate) {
				console.error("Start date and end date are required");
				return;
			}

			setIsSubmitting(true);

			try {
				// Prepare payload matching CreateOfferRequest interface
				const payload: CreateOfferRequest = {
					bookerId: bookerId,
					creativeId: creativeId,
					projectId: bookingForm.projectId,
					offerStartDate: new Date(bookingForm.startDate),
					offerEndDate: new Date(bookingForm.endDate),
				};

				console.log("Submitting booking offer:", payload);
				await callFirebaseFunction("submit_offer_to_creative", payload);
				console.log("Booking offer submitted successfully!");

				closeBookingModal();
			} catch (error) {
				console.error("Failed to submit booking offer:", error);
				// You might want to show an error message to the user here
			} finally {
				setIsSubmitting(false);
			}
		},
		[bookingForm, closeBookingModal, isSubmitting, creativeId, creativeName, bookerId, bookerName],
	);

	return {
		showBookingModal,
		bookingForm,
		isSubmitting,
		openBookingModal,
		closeBookingModal,
		updateBookingFormField,
		handleBookingFormProjectSelect,
		handleBookingSubmit,
	};
};
