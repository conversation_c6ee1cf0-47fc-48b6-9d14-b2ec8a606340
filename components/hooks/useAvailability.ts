import { useCallback, useMemo } from "react";
import { useAvailabilityForCreative } from "../../src/hooks/data/useAvailability";
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";
import type { DailyAvailability } from "../../src/types/collections/availability";

// A Set of YYYY-MM-DD strings for quick lookups
type AvailabilitySet = Set<string>;

export const useAvailability = (creativeId: string) => {
	const {
		status,
		data: availabilityDoc,
		error,
	} = useAvailabilityForCreative(creativeId);

	// Memoize the conversion of the availability array into a fast-lookup Set
	const availableDatesSet: AvailabilitySet = useMemo(() => {
		const dateSet = new Set<string>();
		if (availabilityDoc?.dailySchedules) {
			availabilityDoc.dailySchedules.forEach((day) => {
				if (day.isAvailableAllDay) {
					dateSet.add(day.date);
				}
			});
		}
		return dateSet;
	}, [availabilityDoc]);

	const toggleAvailability = useCallback(
		async (date: Date) => {
			if (!availabilityDoc) {
				console.error("Availability document not loaded yet.");
				return;
			}

			const dateString = date.toISOString().split("T")[0]; // Format to YYYY-MM-DD

			const newDailySchedules: DailyAvailability[] = [
				...availabilityDoc.dailySchedules,
			];
			const existingScheduleIndex = newDailySchedules.findIndex(
				(d) => d.date === dateString,
			);

			if (existingScheduleIndex > -1) {
				// Toggle the existing date's availability
				newDailySchedules[existingScheduleIndex].isAvailableAllDay =
					!newDailySchedules[existingScheduleIndex].isAvailableAllDay;
			} else {
				// Add a new availability record for this date
				newDailySchedules.push({
					date: dateString,
					isAvailableAllDay: true, // Default to available when first toggled
					timeSlots: [],
				});
			}

			const payload = {
				creativeId: creativeId,
				dailySchedules: newDailySchedules,
			};

			try {
				await callFirebaseFunction("set_creative_availability", payload);
			} catch (err) {
				console.error("Failed to update availability:", err);
				// TODO: Add user-facing error notification and potentially revert optimistic UI update
			}
		},
		[creativeId, availabilityDoc],
	);

	return {
		status,
		error,
		availableDatesSet,
		toggleAvailability,
	};
};
