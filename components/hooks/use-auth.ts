import { useCallback, useState } from "react";
import { signOut } from "firebase/auth";
import { useRouter } from "next/navigation";
import { auth } from "@/src/components/FirebaseComponents";

interface UseAuthResult {
	signOutUser: () => Promise<void>;
	isSigningOut: boolean;
	signOutError: string | null;
}

/**
 * Hook for authentication actions like sign out
 */
export const useAuth = (): UseAuthResult => {
	const router = useRouter();
	const [isSigningOut, setIsSigningOut] = useState(false);
	const [signOutError, setSignOutError] = useState<string | null>(null);

	const signOutUser = useCallback(async () => {
		setIsSigningOut(true);
		setSignOutError(null);

		try {
			await signOut(auth);
			console.log("User signed out successfully");
			// Redirect to home page after successful sign out
			router.push("/");
		} catch (error) {
			console.error("Error signing out:", error);
			setSignOutError("Failed to sign out. Please try again.");
		} finally {
			setIsSigningOut(false);
		}
	}, [router]);

	return {
		signOutUser,
		isSigningOut,
		signOutError,
	};
};
