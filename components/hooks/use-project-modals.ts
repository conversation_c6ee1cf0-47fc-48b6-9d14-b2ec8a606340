import { useState } from "react";
import type { Project } from "../types"; // Corrected path again

export interface UseProjectModalsReturn {
	selectedProject: Project | null;
	openProjectModal: (project: Project) => void;
	closeProjectModal: () => void;
	showPaymentModal: boolean;
	openPaymentModal: () => void;
	closePaymentModal: () => void;
}

export const useProjectModals = (): UseProjectModalsReturn => {
	const [selectedProject, setSelectedProject] = useState<Project | null>(null);
	const [showPaymentModal, setShowPaymentModal] = useState(false);

	const openProjectModal = (project: Project) => {
		setSelectedProject(project);
	};

	const closeProjectModal = () => {
		setSelectedProject(null);
	};

	const openPaymentModal = () => {
		setShowPaymentModal(true);
	};

	const closePaymentModal = () => {
		setShowPaymentModal(false);
	};

	return {
		selectedProject,
		openProjectModal,
		closeProjectModal,
		showPaymentModal,
		openPaymentModal,
		closePaymentModal,
	};
};
