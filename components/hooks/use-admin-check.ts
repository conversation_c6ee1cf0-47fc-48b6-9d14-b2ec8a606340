"use client";

import { useState, useEffect } from "react";
import { useUser } from "reactfire";

/**
 * List of admin email addresses
 * In production, this should be fetched from a secure source
 */
const ADMIN_EMAILS = [
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>",
	"<EMAIL>"
	// Add more admin emails as needed
];

interface UseAdminCheckResult {
	isAdmin: boolean;
	isLoading: boolean;
	user: any;
}

/**
 * Hook to check if the current user is an admin
 */
export const useAdminCheck = (): UseAdminCheckResult => {
	const { data: user, status } = useUser();
	const [isAdmin, setIsAdmin] = useState(false);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const checkAdminStatus = () => {
			setIsLoading(true);

			if (status === "loading") {
				return;
			}

			if (status === "success" && user?.email) {
				const adminStatus = ADMIN_EMAILS.includes(user.email);
				setIsAdmin(adminStatus);
			} else {
				setIsAdmin(false);
			}

			setIsLoading(false);
		};

		checkAdminStatus();
	}, [status, user]);

	return {
		isAdmin,
		isLoading: status === "loading" || isLoading,
		user,
	};
};
