import { useCallback, useState } from "react";
import type { Photo } from "../../instagram-photo-grid-data"; // Adjust path as needed

interface UseTextEditingProps {
	setPhotos: React.Dispatch<React.SetStateAction<Photo[]>>;
	saveTileToBackend?: (
		tileId: string,
		updates: {
			textContent?: string;
			backgroundColor?: string;
			fontSize?: string;
			fontFamily?: string;
			imageUrl?: string;
			alt?: string;
			type?: "text" | "image" | "iframe";
			iframeUrl?: string;
		}
	) => Promise<void>;
}

export const useTextEditing = ({ setPhotos, saveTileToBackend }: UseTextEditingProps) => {
	const [editingTile, setEditingTile] = useState<string | null>(null);
	const [tempText, setTempText] = useState("");
	const [tempBackgroundColor, setTempBackgroundColor] = useState("#f3f4f6");
	const [tempFontSize, setTempFontSize] = useState("16px");
	const [tempFontFamily, setTempFontFamily] = useState("Arial");

	const startEditingText = useCallback(
		(photoId: string, currentPhoto: Photo) => {
			setEditingTile(photoId);
			// For iframe tiles, use iframeUrl as the temp text
			if (currentPhoto.type === "iframe") {
				setTempText(currentPhoto.iframeUrl || "");
			} else {
				setTempText(currentPhoto.textContent || "");
			}
			setTempBackgroundColor(currentPhoto.backgroundColor || "#f3f4f6");
			setTempFontSize(currentPhoto.fontSize || "16px");
			setTempFontFamily(currentPhoto.fontFamily || "Arial");
		},
		[],
	);

	const handleSetTempText = useCallback((text: string) => {
		setTempText(text);
	}, []);

	const handleSetTempBackgroundColor = useCallback((color: string) => {
		setTempBackgroundColor(color);
	}, []);

	const handleSetTempFontSize = useCallback((size: string) => {
		setTempFontSize(size);
	}, []);

	const handleSetTempFontFamily = useCallback((family: string) => {
		setTempFontFamily(family);
	}, []);

	const saveTextEdit = useCallback(async () => {
		if (!editingTile) return;

		// Function to enhance video URLs
		const enhanceVideoUrl = (url: string): string => {
			try {
				const urlObj = new URL(url);

				// YouTube enhancements
				if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
					urlObj.searchParams.set('autoplay', '1');
					urlObj.searchParams.set('loop', '1');
					urlObj.searchParams.set('controls', '0');
					urlObj.searchParams.set('modestbranding', '1');
					urlObj.searchParams.set('rel', '0');
					urlObj.searchParams.set('mute', '1'); // Mute for autoplay to work

					// For looping to work on YouTube, we need the playlist parameter
					const videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
					if (videoId) {
						urlObj.searchParams.set('playlist', videoId);
					}
				}
				// Vimeo enhancements
				else if (urlObj.hostname.includes('vimeo.com')) {
					urlObj.searchParams.set('autoplay', '1');
					urlObj.searchParams.set('loop', '1');
					urlObj.searchParams.set('controls', '0');
					urlObj.searchParams.set('muted', '1');
				}
				// Generic video enhancements
				else {
					urlObj.searchParams.set('autoplay', '1');
					urlObj.searchParams.set('controls', '0');
				}

				return urlObj.toString();
			} catch (error) {
				console.warn('Failed to enhance video URL:', error);
				return url;
			}
		};

		let updates: any;

		// Update local state immediately for better UX
		setPhotos((prevPhotos) => {
			return prevPhotos.map((photo) => {
				if (photo.id === editingTile) {
					if (photo.type === "iframe") {
						const enhancedUrl = enhanceVideoUrl(tempText || "");
						updates = {
							iframeUrl: enhancedUrl,
							type: "iframe" as const,
						};
						return {
							...photo,
							iframeUrl: enhancedUrl,
						};
					} else {
						updates = {
							textContent: tempText || "Empty text",
							backgroundColor: tempBackgroundColor,
							fontSize: tempFontSize,
							fontFamily: tempFontFamily,
							type: "text" as const,
						};
						return {
							...photo,
							...updates,
							src: `/placeholder.svg?text=${encodeURIComponent(updates.textContent)}`,
						};
					}
				}
				return photo;
			});
		});

		// Save to backend if function is provided
		if (saveTileToBackend && updates) {
			try {
				await saveTileToBackend(editingTile, updates);
				console.log(`Successfully saved tile ${editingTile} to backend.`);
			} catch (error) {
				console.error(`Failed to save tile ${editingTile} to backend:`, error);
				// Optionally, you could revert the local state here or show an error message
			}
		}

		setEditingTile(null);
		// Reset temp states
		setTempText("");
		setTempBackgroundColor("#f3f4f6");
		setTempFontSize("16px");
		setTempFontFamily("Arial");
	}, [
		editingTile,
		tempText,
		tempBackgroundColor,
		tempFontSize,
		tempFontFamily,
		setPhotos,
		saveTileToBackend,
	]);

	const cancelTextEdit = useCallback(() => {
		setEditingTile(null);
		// Resetting temp states on cancel is good practice
		setTempText(""); // Though usually the caller might also reset tempText if it controls it
		setTempBackgroundColor("#f3f4f6");
		setTempFontSize("16px");
		setTempFontFamily("Arial");
	}, []);

	return {
		editingTile,
		tempText,
		startEditingText,
		handleSetTempText,
		saveTextEdit,
		cancelTextEdit,
		tempBackgroundColor,
		handleSetTempBackgroundColor,
		tempFontSize,
		handleSetTempFontSize,
		tempFontFamily,
		handleSetTempFontFamily,
	};
};
