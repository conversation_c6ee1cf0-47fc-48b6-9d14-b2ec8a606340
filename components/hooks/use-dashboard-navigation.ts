"use client";

import { useCallback, useEffect, useState } from "react";
import { useUser } from "reactfire";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/src/components/FirebaseComponents";
import { FirebaseCollections } from "@/src/constants/firebaseCollections";

interface DashboardNavigationResult {
  dashboardUrl: string | null;
  isLoading: boolean;
  error: string | null;
}

/**
 * Hook to get the appropriate dashboard URL for the logged-in user
 * Returns the URL to redirect to based on user's accessible profiles
 */
export const useDashboardNavigation = (): DashboardNavigationResult => {
  const { data: user, status } = useUser();
  const [dashboardUrl, setDashboardUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const determineDashboardUrl = useCallback(async () => {
    if (status !== "success" || !user) {
      setDashboardUrl(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Fetch the user's root account document to see which profiles they have access to
      const userRootRef = doc(db, FirebaseCollections.USER_ROOT_ACCOUNT, user.uid);
      const userRootSnap = await getDoc(userRootRef);

      if (!userRootSnap.exists()) {
        setError("User profile not found");
        setDashboardUrl(null);
        return;
      }

      const userRootData = userRootSnap.data();
      const accessibleCreatives = userRootData.creatives ? Object.keys(userRootData.creatives) : [];
      const accessibleBookers = userRootData.bookers ? Object.keys(userRootData.bookers) : [];
      const totalProfiles = accessibleCreatives.length + accessibleBookers.length;

      if (totalProfiles === 0) {
        setError("No accessible profiles found");
        setDashboardUrl(null);
        return;
      }

      // If user has access to exactly one profile, redirect them directly
      if (totalProfiles === 1) {
        if (accessibleCreatives.length === 1) {
          setDashboardUrl(`/creative/${accessibleCreatives[0]}`);
        } else {
          setDashboardUrl(`/booker/${accessibleBookers[0]}`);
        }
        return;
      }

      // If user has multiple profiles, prioritize booker profile for dashboard
      // This matches the existing behavior where bookers are the primary users
      if (accessibleBookers.length > 0) {
        setDashboardUrl(`/booker/${accessibleBookers[0]}`);
      } else if (accessibleCreatives.length > 0) {
        setDashboardUrl(`/creative/${accessibleCreatives[0]}`);
      } else {
        setError("No valid profiles found");
        setDashboardUrl(null);
      }
    } catch (err) {
      console.error("Error determining dashboard URL:", err);
      setError("Failed to determine dashboard URL");
      setDashboardUrl(null);
    } finally {
      setIsLoading(false);
    }
  }, [user, status]);

  useEffect(() => {
    determineDashboardUrl();
  }, [determineDashboardUrl]);

  return {
    dashboardUrl,
    isLoading,
    error,
  };
};
