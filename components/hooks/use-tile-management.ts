import { useCallback, useState } from "react";
import type { Project } from "../types"; // Adjust if Project type is defined elsewhere

export interface UseTileManagementProps {
	projects: Project[];
	setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
	initialMergeCount?: number;
}

export interface UseTileManagementReturn {
	selectedTiles: Set<string>;
	isSelectionMode: boolean;
	mergeCount: number;
	toggleTileSelection: (projectId: string) => void;
	clearSelection: () => void;
	toggleSelectionMode: () => void;
	mergeTiles: (direction: "horizontal" | "vertical" | "large") => void;
	splitTile: (project: Project, event: React.MouseEvent) => void;
	convertToTextTile: (projectId: string) => void;
	convertToImageTile: (projectId: string) => void;
}

export const useTileManagement = ({
	projects,
	setProjects,
	initialMergeCount = 0,
}: UseTileManagementProps): UseTileManagementReturn => {
	const [selectedTiles, setSelectedTiles] = useState<Set<string>>(new Set());
	const [isSelectionMode, setIsSelectionMode] = useState(false);
	const [mergeCount, setMergeCount] = useState(initialMergeCount);

	const toggleSelectionMode = useCallback(() => {
		setIsSelectionMode((prev) => !prev);
		setSelectedTiles(new Set()); // Clear selection when toggling mode
	}, []);

	const toggleTileSelection = useCallback(
		(projectId: string) => {
			if (!isSelectionMode) return;
			setSelectedTiles((prev) => {
				const newSet = new Set(prev);
				if (newSet.has(projectId)) {
					newSet.delete(projectId);
				} else {
					newSet.add(projectId);
				}
				return newSet;
			});
		},
		[isSelectionMode],
	);

	const clearSelection = useCallback(() => {
		setSelectedTiles(new Set());
	}, []);

	const mergeTiles = useCallback(
		(direction: "horizontal" | "vertical" | "large") => {
			if (selectedTiles.size < 2) return;

			const selectedProjectsArray = projects.filter((project) =>
				selectedTiles.has(project.id),
			);
			if (selectedProjectsArray.length === 0) return; // Should not happen if selectedTiles.size >= 2

			const minPosition = Math.min(
				...selectedProjectsArray.map((project) => project.position || 0),
			);
			const hasTextTiles = selectedProjectsArray.some(
				(project) => project.type === "text",
			);

			const mergedProject: Project = {
				id: `merged-${Date.now()}-${mergeCount}`,
				src: "/placeholder.svg?height=800&width=800&text=Merged Projects", // Consider dynamic text/alt
				alt: `Merged: ${selectedProjectsArray.map((p) => p.title.substring(0, 10)).join(", ")}...`,
				title: `Merged: ${selectedProjectsArray.map((p) => p.title).join(" + ")}`,
				client: "Multiple Clients", // Or derive from originals
				budget: selectedProjectsArray.reduce((sum, p) => sum + p.budget, 0),
				status: "active", // Or derive
				size: direction,
				isMerged: true,
				originalProjects: [...selectedProjectsArray],
				position: minPosition,
				type: hasTextTiles ? "text" : "image",
				textContent: hasTextTiles
					? selectedProjectsArray
							.map((p) =>
								p.type === "text" ? p.textContent || "Empty text" : p.title,
							)
							.join("\n\n") || "Merged content"
					: undefined,
				backgroundColor: hasTextTiles ? "#f3f4f6" : undefined, // Consider a default color or derive
			};

			const remainingProjects = projects.filter(
				(project) => !selectedTiles.has(project.id),
			);
			const newProjects = [...remainingProjects, mergedProject].sort(
				(a, b) => (a.position || 0) - (b.position || 0),
			);

			setProjects(newProjects);
			setSelectedTiles(new Set());
			setIsSelectionMode(false);
			setMergeCount((prev) => prev + 1);
		},
		[projects, selectedTiles, setProjects, mergeCount],
	);

	const splitTile = useCallback(
		(projectToSplit: Project, event: React.MouseEvent) => {
			event.stopPropagation();
			if (!projectToSplit.isMerged || !projectToSplit.originalProjects) return;

			const mergedPosition = projectToSplit.position || 0;
			// Restore original projects, ensuring their positions are respected relative to the merged tile or globally.
			// This simplistic approach might need refinement for complex layouts.
			const restoredOriginals = projectToSplit.originalProjects.map(
				(p, index) => ({
					...p,
					position:
						p.position !== undefined ? p.position : mergedPosition + index, // Fallback positioning logic
					isMerged: false, // Clear merged flag from originals if it was somehow set
					size: p.size || "standard", // Reset size if it was part of merge
				}),
			);

			const projectsWithoutMerged = projects.filter(
				(p) => p.id !== projectToSplit.id,
			);
			const newProjects = [...projectsWithoutMerged, ...restoredOriginals].sort(
				(a, b) => (a.position || 0) - (b.position || 0),
			);

			setProjects(newProjects);
			// No need to change mergeCount on split usually, unless specifically tracked that way.
		},
		[projects, setProjects],
	);

	const convertToTextTile = useCallback(
		(projectId: string) => {
			setProjects((prevProjects) =>
				prevProjects.map((project) =>
					project.id === projectId
						? {
								...project,
								type: "text",
								textContent: project.textContent || "Click to edit text",
								backgroundColor: project.backgroundColor || "#f3f4f6",
								src: undefined,
								alt: project.alt || "Text tile",
							}
						: project,
				),
			);
		},
		[setProjects],
	);

	const convertToImageTile = useCallback(
		(projectId: string) => {
			setProjects((prevProjects) =>
				prevProjects.map((project) =>
					project.id === projectId
						? {
								...project,
								type: "image",
								textContent: undefined,
								backgroundColor: undefined,
								src:
									project.src ||
									"/placeholder.svg?height=400&width=400&text=Image",
								alt: project.alt || "Image tile",
							}
						: project,
				),
			);
		},
		[setProjects],
	);

	return {
		selectedTiles,
		isSelectionMode,
		mergeCount,
		toggleTileSelection,
		clearSelection,
		toggleSelectionMode,
		mergeTiles,
		splitTile,
		convertToTextTile,
		convertToImageTile,
	};
};
