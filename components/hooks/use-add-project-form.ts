import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";

export interface NewProjectFormData {
	title: string;
	client: string;
	budget: string; // Keep as string for form input, convert on submit
	projectType: string;
	startDate: string;
	endDate: string;
	location: string;
	description: string;
	creativesNeeded: string[];
}

const initialNewProjectFormState: NewProjectFormData = {
	title: "",
	client: "",
	budget: "",
	projectType: "",
	startDate: "",
	endDate: "",
	location: "",
	description: "",
	creativesNeeded: [],
};

export interface UseAddProjectFormProps {
	createProject: (formData: NewProjectFormData) => Promise<void>;
}

export const useAddProjectForm = ({
	createProject,
}: UseAddProjectFormProps) => {
	const router = useRouter();
	const [showAddProjectModal, setShowAddProjectModal] = useState(false);
	const [newProjectForm, setNewProjectForm] = useState<NewProjectFormData>(
		initialNewProjectFormState,
	);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const openAddProjectModal = useCallback(() => {
		setNewProjectForm(initialNewProjectFormState);
		setShowAddProjectModal(true);
	}, []);

	const closeAddProjectModal = useCallback(() => {
		setShowAddProjectModal(false);
		setNewProjectForm(initialNewProjectFormState);
	}, []);

	const updateNewProjectFormField = useCallback(
		(field: keyof NewProjectFormData, value: string | string[]) => {
			setNewProjectForm((prev) => ({
				...prev,
				[field]: value,
			}));
		},
		[],
	);

	const handleAddProjectSubmit = useCallback(
		async (e: React.FormEvent) => {
			e.preventDefault();
			setIsSubmitting(true);
			try {
				await createProject(newProjectForm);
				closeAddProjectModal();
				// Refresh the page to show the new project
				router.refresh();
			} catch (error) {
				alert("Error: Could not create project."); // Show user-friendly error
			} finally {
				setIsSubmitting(false);
			}
		},
		[createProject, newProjectForm, closeAddProjectModal, router],
	);

	// The 'add/remove artist role' functions can be kept as they modify local form state
	const addArtistRoleToForm = useCallback(() => {
		// ... (implementation remains the same)
	}, []);

	const removeArtistRoleFromForm = useCallback((index: number) => {
		// ... (implementation remains the same)
	}, []);

	return {
		showAddProjectModal,
		newProjectForm,
		isSubmitting,
		openAddProjectModal,
		closeAddProjectModal,
		updateNewProjectFormField,
		handleAddProjectSubmit,
		addArtistRoleToForm,
		removeArtistRoleFromForm,
	};
};
