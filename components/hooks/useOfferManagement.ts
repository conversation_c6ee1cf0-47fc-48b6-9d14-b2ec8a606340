import { useCallback } from "react";
import { callFirebaseFunction } from "../../src/lib/firebaseFunctions";
import type { CreativeRespondToOfferRequest } from "../../types/requests/offerRequestInterfaces";
import { useInvoiceManagement } from "./useInvoiceManagement";

export const useOfferManagement = () => {
	const { createInvoice } = useInvoiceManagement();

	const acceptOffer = useCallback(async (offerId: string) => {
		const payload = { offerId };
		try {
			await callFirebaseFunction("accept_offer", payload);
		} catch (error) {
			console.error("Failed to accept offer:", error);
			throw error;
		}
	}, []);

	const declineOffer = useCallback(async (offerId: string) => {
		const payload = { offerId };
		try {
			await callFirebaseFunction("decline_offer", payload);
		} catch (error) {
			console.error("Failed to decline offer:", error);
			throw error;
		}
	}, []);

	const respondToOffer = useCallback(async (request: CreativeRespondToOfferRequest) => {
		try {
			await callFirebaseFunction("creative_respond_to_offer", request);
		} catch (error) {
			console.error("Failed to respond to offer:", error);
			throw error;
		}
	}, []);

	// Convenience functions for common actions
	const approveOffer = useCallback(async (offerId: string, comments?: string) => {
		return respondToOffer({
			offerId,
			action: "APPROVED",
			comments: comments || null,
			changes: null
		});
	}, [respondToOffer]);

	const rejectOffer = useCallback(async (offerId: string, comments?: string) => {
		return respondToOffer({
			offerId,
			action: "REJECTED",
			comments: comments || null,
			changes: null
		});
	}, [respondToOffer]);

	const negotiateOffer = useCallback(async (offerId: string, changes: { [k: string]: unknown }, comments?: string) => {
		return respondToOffer({
			offerId,
			action: "NEGOTIATING",
			comments: comments || null,
			changes
		});
	}, [respondToOffer]);

	/**
	 * Complete an offer and automatically create an invoice for payment
	 */
	const completeOffer = useCallback(async (offerId: string, notes?: string) => {
		try {
			// First complete the offer
			await callFirebaseFunction("complete_offer", { offerId });

			// Then create an invoice for the completed offer
			await createInvoice({
				offerId,
				notes: notes || "Invoice for completed project work"
			});

			console.log("Offer completed and invoice created successfully");
		} catch (error) {
			console.error("Failed to complete offer and create invoice:", error);
			throw error;
		}
	}, [createInvoice]);

	return {
		acceptOffer,
		declineOffer,
		respondToOffer,
		approveOffer,
		rejectOffer,
		negotiateOffer,
		completeOffer,
	};
};
