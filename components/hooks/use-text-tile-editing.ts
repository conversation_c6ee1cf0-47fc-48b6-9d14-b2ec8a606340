import { useCallback, useState } from "react";
import type { Project } from "../types"; // Adjust if Project type is defined elsewhere

export interface UseTextTileEditingProps {
	setProjects: React.Dispatch<React.SetStateAction<Project[]>>;
}

export interface UseTextTileEditingReturn {
	editingTile: string | null;
	tempText: string;
	startEditingText: (projectId: string, currentText?: string) => void;
	handleSetTempText: (text: string) => void;
	saveTextEdit: () => void;
	cancelTextEdit: () => void;
}

export const useTextTileEditing = ({
	setProjects,
}: UseTextTileEditingProps): UseTextTileEditingReturn => {
	const [editingTile, setEditingTile] = useState<string | null>(null);
	const [tempText, setTempText] = useState("");

	const startEditingText = useCallback(
		(projectId: string, currentText = "") => {
			setEditingTile(projectId);
			setTempText(currentText);
		},
		[],
	);

	const handleSetTempText = useCallback((text: string) => {
		setTempText(text);
	}, []);

	const saveTextEdit = useCallback(() => {
		if (!editingTile) return;

		setProjects((prevProjects) =>
			prevProjects.map((project) =>
				project.id === editingTile
					? { ...project, textContent: tempText || "Empty text" }
					: project,
			),
		);

		setEditingTile(null);
		setTempText("");
	}, [editingTile, tempText, setProjects]);

	const cancelTextEdit = useCallback(() => {
		setEditingTile(null);
		setTempText("");
	}, []);

	return {
		editingTile,
		tempText,
		startEditingText,
		handleSetTempText,
		saveTextEdit,
		cancelTextEdit,
	};
};
