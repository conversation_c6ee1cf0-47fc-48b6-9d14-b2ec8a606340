"use client";

import React, { useState, useEffect } from "react";
import { getAllUsers, getUserStats, searchUsers, getUserProfileDetails, acceptCreativeProfile, acceptBookerProfile, type AdminUserData } from "@/src/actions/adminActions";
import { ChevronDown, ChevronRight, User, Briefcase, FileText } from "lucide-react";
import type { Creative, CreativeType } from "@/types/collections/creativeBioInterfaces";

interface UserStats {
	totalUsers: number;
	totalCreatives: number;
	totalBookers: number;
	activeUsers: number;
}

type UserTypeFilter = "all" | "creative" | "booker" | "both" | "no-profile";

export default function AdminUserList() {
	const [allUsers, setAllUsers] = useState<AdminUserData[]>([]); // Store all users
	const [filteredUsers, setFilteredUsers] = useState<AdminUserData[]>([]); // Store filtered users
	const [stats, setStats] = useState<UserStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [searchQuery, setSearchQuery] = useState("");
	const [searchLoading, setSearchLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [userTypeFilter, setUserTypeFilter] = useState<UserTypeFilter>("all");
	const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
	const [profileDetails, setProfileDetails] = useState<Record<string, any>>({});
	const [acceptingProfiles, setAcceptingProfiles] = useState<Set<string>>(new Set());
	const [acceptedProfiles, setAcceptedProfiles] = useState<Set<string>>(new Set());
	const [acceptingBookerProfiles, setAcceptingBookerProfiles] = useState<Set<string>>(new Set());
	const [acceptedBookerProfiles, setAcceptedBookerProfiles] = useState<Set<string>>(new Set());
	const [acceptMessage, setAcceptMessage] = useState<string | null>(null);

	// Helper function to get user type
	const getUserType = (user: AdminUserData): UserTypeFilter => {
		const { userRootAccount } = user;
		if (!userRootAccount) {
			return "no-profile";
		}

		const hasCreatives = Object.keys(userRootAccount.creatives).length > 0;
		const hasBookers = Object.keys(userRootAccount.bookers).length > 0;

		if (hasCreatives && hasBookers) {
			return "both";
		} else if (hasCreatives) {
			return "creative";
		} else if (hasBookers) {
			return "booker";
		}

		return "no-profile";
	};

	// Helper function to filter users based on type
	const filterUsersByType = (users: AdminUserData[], filter: UserTypeFilter): AdminUserData[] => {
		if (filter === "all") {
			return users;
		}
		return users.filter(user => getUserType(user) === filter);
	};

	// Load initial data
	useEffect(() => {
		const loadData = async () => {
			try {
				setLoading(true);
				setError(null);

				// Load users and stats in parallel
				const [usersResult, statsResult] = await Promise.all([
					getAllUsers(100), // Get first 100 users
					getUserStats(),
				]);

				setAllUsers(usersResult.users);
				setFilteredUsers(filterUsersByType(usersResult.users, userTypeFilter));
				setStats(statsResult);
			} catch (err) {
				console.error("Error loading admin data:", err);
				setError("Failed to load user data. Please try again.");
			} finally {
				setLoading(false);
			}
		};

		loadData();
	}, []);

	// Handle filter changes
	useEffect(() => {
		if (allUsers.length > 0) {
			const filtered = filterUsersByType(allUsers, userTypeFilter);
			setFilteredUsers(filtered);
		}
	}, [userTypeFilter, allUsers]);

	// Handle search
	const handleSearch = async (query: string) => {
		if (!query.trim()) {
			// If search is empty, show filtered users based on current filter
			setFilteredUsers(filterUsersByType(allUsers, userTypeFilter));
			return;
		}

		try {
			setSearchLoading(true);
			setError(null);
			const searchResults = await searchUsers(query);
			// Apply current filter to search results
			const filteredSearchResults = filterUsersByType(searchResults, userTypeFilter);
			setFilteredUsers(filteredSearchResults);
		} catch (err) {
			console.error("Error searching users:", err);
			setError("Failed to search users. Please try again.");
		} finally {
			setSearchLoading(false);
		}
	};

	// Format date for display
	const formatDate = (dateString: string | undefined) => {
		if (!dateString) return "Never";
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	// Get user type badge
	const getUserTypeBadge = (user: AdminUserData) => {
		const userType = getUserType(user);

		switch (userType) {
			case "both":
				return <span className="px-2 py-1 text-xs bg-purple-100 text-purple-700 rounded">Both</span>;
			case "creative":
				return <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">Creative</span>;
			case "booker":
				return <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">Booker</span>;
			case "no-profile":
			default:
				return <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">No Profile</span>;
		}
	};

	// Handle accepting creative profile
	const handleAcceptCreative = async (userId: string, creativeId: string) => {
		const profileKey = `${userId}-${creativeId}`;
		setAcceptingProfiles(prev => new Set([...prev, profileKey]));
		setAcceptMessage(null);

		try {
			const result = await acceptCreativeProfile(userId, creativeId);
			
			if (result.success) {
				setAcceptMessage(`✅ ${result.message}`);
				// Mark this profile as accepted so button won't show again
				setAcceptedProfiles(prev => new Set([...prev, profileKey]));
			} else {
				setAcceptMessage(`❌ ${result.message}`);
			}
		} catch (error) {
			console.error("Error accepting creative profile:", error);
			setAcceptMessage("❌ Failed to accept creative profile");
		} finally {
			setAcceptingProfiles(prev => {
				const newSet = new Set(prev);
				newSet.delete(profileKey);
				return newSet;
			});
			
			// Clear message after 3 seconds
			setTimeout(() => setAcceptMessage(null), 3000);
		}
	};

	// Handle accepting booker profile
	const handleAcceptBooker = async (userId: string, bookerId: string) => {
		const profileKey = `${userId}-${bookerId}`;
		setAcceptingBookerProfiles(prev => new Set([...prev, profileKey]));
		setAcceptMessage(null);

		try {
			const result = await acceptBookerProfile(userId, bookerId);
			
			if (result.success) {
				setAcceptMessage(`✅ ${result.message}`);
				// Mark this booker profile as accepted so button won't show again
				setAcceptedBookerProfiles(prev => new Set([...prev, profileKey]));
			} else {
				setAcceptMessage(`❌ ${result.message}`);
			}
		} catch (error) {
			console.error("Error accepting booker profile:", error);
			setAcceptMessage("❌ Failed to accept booker profile");
		} finally {
			setAcceptingBookerProfiles(prev => {
				const newSet = new Set(prev);
				newSet.delete(profileKey);
				return newSet;
			});
			
			// Clear message after 3 seconds
			setTimeout(() => setAcceptMessage(null), 3000);
		}
	};

	// Handle row expansion
	const toggleRowExpansion = async (userId: string) => {
		const newExpandedRows = new Set(expandedRows);

		if (expandedRows.has(userId)) {
			newExpandedRows.delete(userId);
		} else {
			newExpandedRows.add(userId);

			// Fetch profile details if not already loaded
			if (!profileDetails[userId]) {
				try {
					console.log(`[Client] Fetching profile details for user: ${userId}`);
					const details = await getUserProfileDetails(userId);
					console.log(`[Client] Received profile details:`, details);
					setProfileDetails(prev => ({
						...prev,
						[userId]: details
					}));
				} catch (error) {
					console.error("[Client] Error fetching profile details:", error);
					// Set error state so we don't keep loading
					setProfileDetails(prev => ({
						...prev,
						[userId]: { error: (error as Error).message || 'Unknown error', creativeProfiles: [], bookerProfiles: [], offers: [] }
					}));
				}
			}
		}

		setExpandedRows(newExpandedRows);
	};

	// Render creative profile details
	const renderCreativeDetails = (creative: Creative) => {
		// Helper function to format creative types
		const formatCreativeTypes = (types: CreativeType[]) => {
			return types.map(type => {
				switch(type) {
					case "photographer": return "Photographer";
					case "videographer": return "Videographer"; 
					case "model": return "Model";
					case "makeupArtist": return "Makeup Artist";
					case "hairStylist": return "Hair Stylist";
					case "other": return "Other";
					default: return type;
				}
			}).join(", ");
		};

		// Helper function to format date
		const formatDate = (date: Date | undefined) => {
			if (!date) return "N/A";
			return new Date(date).toLocaleDateString("en-US", {
				year: "numeric",
				month: "short",
				day: "numeric",
			});
		};

		return (
			<div key={creative.parentId} className="bg-blue-50 p-3 rounded-lg mb-2">
				<div className="flex items-center gap-2 mb-2">
					<User className="w-4 h-4 text-blue-600" />
					<span className="font-medium text-blue-800">Creative Profile</span>
				</div>
				<div className="grid grid-cols-2 gap-2 text-sm">
					<div><span className="font-medium">Display Name:</span> {creative.displayName || "N/A"}</div>
					<div><span className="font-medium">Creative Types:</span> {creative.creativeType?.length > 0 ? formatCreativeTypes(creative.creativeType) : "N/A"}</div>
					<div><span className="font-medium">Phone:</span> {creative.phoneNumber || "N/A"}</div>
					<div><span className="font-medium">Instagram:</span> {creative.instagram ? `@${creative.instagram}` : "N/A"}</div>
					{creative.website && (
						<div>
							<span className="font-medium">Website:</span>{" "}
							<a href={creative.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800 break-all">
								{creative.website}
							</a>
						</div>
					)}
					<div><span className="font-medium">Updated:</span> {formatDate(creative.updatedAt)}</div>
					<div className="col-span-2"><span className="font-medium">Parent ID:</span> <span className="text-xs text-gray-500 font-mono">{creative.parentId}</span></div>
				</div>
				{creative.story && (
					<div className="mt-3">
						<span className="font-medium">Story:</span>
						<p className="text-gray-600 text-sm mt-1 whitespace-pre-wrap">{creative.story}</p>
					</div>
				)}
			</div>
		);
	};

	// Render booker profile details
	const renderBookerDetails = (booker: any) => (
		<div key={booker.id} className="bg-green-50 p-3 rounded-lg mb-2">
			<div className="flex items-center gap-2 mb-2">
				<Briefcase className="w-4 h-4 text-green-600" />
				<span className="font-medium text-green-800">Booker Profile</span>
			</div>
			<div className="grid grid-cols-2 gap-2 text-sm">
				<div><span className="font-medium">Name:</span> {booker.name || "N/A"}</div>
				<div><span className="font-medium">Type:</span> {booker.type || "N/A"}</div>
				<div><span className="font-medium">Location:</span> {booker.location || "N/A"}</div>
				{booker.company && <div><span className="font-medium">Company:</span> {booker.company}</div>}
			</div>
			{booker.bio && (
				<div className="mt-2">
					<span className="font-medium">Bio:</span>
					<p className="text-gray-600 text-sm mt-1">{booker.bio}</p>
				</div>
			)}
		</div>
	);

	// Render offers details
	const renderOffersDetails = (offers: any[]) => {
		if (offers.length === 0) {
			return (
				<div className="bg-gray-50 p-3 rounded-lg">
					<div className="flex items-center gap-2 mb-2">
						<FileText className="w-4 h-4 text-gray-600" />
						<span className="font-medium text-gray-800">Offers</span>
					</div>
					<p className="text-sm text-gray-600">No offers found</p>
				</div>
			);
		}

		return (
			<div className="bg-purple-50 p-3 rounded-lg">
				<div className="flex items-center gap-2 mb-2">
					<FileText className="w-4 h-4 text-purple-600" />
					<span className="font-medium text-purple-800">Offers ({offers.length})</span>
				</div>
				<div className="space-y-2 max-h-40 overflow-y-auto">
					{offers.map((offer) => (
						<div key={offer.id} className="bg-white p-2 rounded border text-sm">
							<div className="flex justify-between items-start">
								<div>
									<div className="font-medium">{offer.projectTitle || "Untitled Project"}</div>
									<div className="text-gray-600">
										{offer.type === "sent" ? "Sent" : "Received"} •
										Status: <span className="capitalize">{offer.status || "unknown"}</span>
									</div>
									{offer.budget && <div className="text-green-600">Budget: ${offer.budget}</div>}
								</div>
								<div className="text-xs text-gray-500">
									{offer.createdAt?.seconds ? new Date(offer.createdAt.seconds * 1000).toLocaleDateString() : "N/A"}
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		);
	};

	// Get filter options
	const filterOptions = [
		{ value: "all", label: "All Users", count: allUsers.length },
		{ value: "creative", label: "Creatives", count: allUsers.filter(u => getUserType(u) === "creative").length },
		{ value: "booker", label: "Bookers", count: allUsers.filter(u => getUserType(u) === "booker").length },
		{ value: "both", label: "Both", count: allUsers.filter(u => getUserType(u) === "both").length },
		{ value: "no-profile", label: "No Profile", count: allUsers.filter(u => getUserType(u) === "no-profile").length },
	];

	if (loading) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
				<span className="ml-2">Loading users...</span>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Stats Cards */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="bg-white p-4 rounded-lg shadow border">
						<h3 className="text-sm font-medium text-gray-500">Total Users</h3>
						<p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
					</div>
					<div className="bg-white p-4 rounded-lg shadow border">
						<h3 className="text-sm font-medium text-gray-500">Active Users</h3>
						<p className="text-2xl font-bold text-green-600">{stats.activeUsers}</p>
					</div>
					<div className="bg-white p-4 rounded-lg shadow border">
						<h3 className="text-sm font-medium text-gray-500">Creatives</h3>
						<p className="text-2xl font-bold text-blue-600">{stats.totalCreatives}</p>
					</div>
					<div className="bg-white p-4 rounded-lg shadow border">
						<h3 className="text-sm font-medium text-gray-500">Bookers</h3>
						<p className="text-2xl font-bold text-purple-600">{stats.totalBookers}</p>
					</div>
				</div>
			)}

			{/* Search */}
			<div className="bg-white p-4 rounded-lg shadow border">
				<div className="flex gap-4">
					<input
						type="text"
						placeholder="Search users by email or name..."
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
					<button
						onClick={() => handleSearch(searchQuery)}
						disabled={searchLoading}
						className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
					>
						{searchLoading ? "Searching..." : "Search"}
					</button>
				</div>
			</div>

			{/* User Type Filter */}
			<div className="bg-white p-4 rounded-lg shadow border">
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="flex items-center gap-2">
						<span className="text-sm font-medium text-gray-700">Filter by type:</span>
					</div>
					<div className="flex flex-wrap gap-2">
						{filterOptions.map((option) => (
							<button
								key={option.value}
								onClick={() => setUserTypeFilter(option.value as UserTypeFilter)}
								className={`px-3 py-1 text-sm rounded-md border transition-colors ${
									userTypeFilter === option.value
										? "bg-blue-600 text-white border-blue-600"
										: "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
								}`}
							>
								{option.label} ({option.count})
							</button>
						))}
					</div>
				</div>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
					{error}
				</div>
			)}

			{/* Accept Message */}
			{acceptMessage && (
				<div className={`border px-4 py-3 rounded ${
					acceptMessage.startsWith('✅') 
						? 'bg-green-50 border-green-200 text-green-700' 
						: 'bg-red-50 border-red-200 text-red-700'
				}`}>
					{acceptMessage}
				</div>
			)}

			{/* Users Table */}
			<div className="bg-white rounded-lg shadow border overflow-hidden">
				<div className="px-4 py-3 border-b border-gray-200">
					<div className="flex justify-between items-center">
						<h3 className="text-lg font-medium text-gray-900">
							Platform Users ({filteredUsers.length})
						</h3>
						{userTypeFilter !== "all" && (
							<span className="text-sm text-gray-500">
								Showing {filteredUsers.length} of {allUsers.length} users
							</span>
						)}
					</div>
				</div>
				
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									User
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Type
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Created
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Last Sign In
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredUsers.map((user) => {
								const userType = getUserType(user);
								const hasProfiles = userType !== "no-profile";
								const isExpanded = expandedRows.has(user.uid);
								const details = profileDetails[user.uid];

								return (
									<React.Fragment key={user.uid}>
										<tr className="hover:bg-gray-50">
											<td className="px-6 py-4 whitespace-nowrap">
												<div>
													<div className="text-sm font-medium text-gray-900">
														{user.displayName || "No name"}
													</div>
													<div className="text-sm text-gray-500">{user.email}</div>
													<div className="text-xs text-gray-400">{user.uid}</div>
												</div>
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												{getUserTypeBadge(user)}
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												<div className="flex flex-col gap-1">
													{user.emailVerified ? (
														<span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
															Verified
														</span>
													) : (
														<span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded">
															Unverified
														</span>
													)}
													{user.disabled && (
														<span className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded">
															Disabled
														</span>
													)}
													{user.userRootAccount?.isActive && (
														<span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
															Active
														</span>
													)}
													{user.userRootAccount?.verifiedByDua && (
														<span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
															Verified by DUA
														</span>
													)}
													{user.userRootAccount && !user.userRootAccount.verifiedByDua && (
														<span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-700 rounded">
															Pending Verification
														</span>
													)}
												</div>
											</td>
											<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
												{formatDate(user.createdAt)}
											</td>
											<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
												{formatDate(user.lastSignInTime)}
											</td>
											<td className="px-6 py-4 whitespace-nowrap">
												<div className="flex items-center gap-2">
													{hasProfiles && (
														<button
															onClick={() => toggleRowExpansion(user.uid)}
															className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
														>
															{isExpanded ? (
																<ChevronDown className="w-4 h-4" />
															) : (
																<ChevronRight className="w-4 h-4" />
															)}
															Details
														</button>
													)}
													
													{/* Accept Create button for creative users - only show if not verified by DUA */}
													{(userType === "creative" || userType === "both") && user.userRootAccount && Object.keys(user.userRootAccount.creatives).length > 0 && !user.userRootAccount.verifiedByDua && (
														(() => {
															const creativeId = Object.keys(user.userRootAccount.creatives)[0];
															const profileKey = `${user.uid}-${creativeId}`;

															// Only show button if profile hasn't been accepted yet
															if (acceptedProfiles.has(profileKey)) {
																return (
																	<span className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 text-gray-500 rounded-md">
																		✓ Accepted
																	</span>
																);
															}

															return (
																<button
																	onClick={() => handleAcceptCreative(user.uid, creativeId)}
																	disabled={acceptingProfiles.has(profileKey)}
																	className="flex items-center gap-1 px-3 py-1 text-sm bg-green-100 hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-500 text-green-700 rounded-md transition-colors"
																>
																	{acceptingProfiles.has(profileKey) ? (
																		<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600"></div>
																	) : (
																		"✓"
																	)}
																	Accept Create
																</button>
															);
														})()
													)}
													
													{/* Accept Create button for booker users - only show if not verified by DUA */}
													{(userType === "booker" || userType === "both") && user.userRootAccount && Object.keys(user.userRootAccount.bookers).length > 0 && !user.userRootAccount.verifiedByDua && (
														(() => {
															const bookerId = Object.keys(user.userRootAccount.bookers)[0];
															const profileKey = `${user.uid}-${bookerId}`;

															// Only show button if profile hasn't been accepted yet
															if (acceptedBookerProfiles.has(profileKey)) {
																return (
																	<span className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-100 text-gray-500 rounded-md">
																		✓ Accepted
																	</span>
																);
															}

															return (
																<button
																	onClick={() => handleAcceptBooker(user.uid, bookerId)}
																	disabled={acceptingBookerProfiles.has(profileKey)}
																	className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 disabled:bg-gray-100 disabled:text-gray-500 text-blue-700 rounded-md transition-colors"
																>
																	{acceptingBookerProfiles.has(profileKey) ? (
																		<div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
																	) : (
																		"✓"
																	)}
																	Accept Create
																</button>
															);
														})()
													)}
												</div>
											</td>
										</tr>

										{/* Expanded Details Row */}
										{isExpanded && hasProfiles && (
											<tr>
												<td colSpan={6} className="px-6 py-4 bg-gray-50">
													<div className="space-y-4">
														{details ? (
															details.error ? (
																<div className="bg-red-50 p-4 rounded-lg">
																	<div className="text-red-800 font-medium">Error loading profile details</div>
																	<div className="text-red-600 text-sm mt-1">{details.error}</div>
																</div>
															) : (
																<>
																	{/* Creative Profiles */}
																	{details.creativeProfiles?.length > 0 && (
																		<div>
																			<h4 className="font-medium text-gray-900 mb-2">Creative Profiles</h4>
																			{details.creativeProfiles.map(renderCreativeDetails)}
																		</div>
																	)}

																	{/* Booker Profiles */}
																	{details.bookerProfiles?.length > 0 && (
																		<div>
																			<h4 className="font-medium text-gray-900 mb-2">Booker Profiles</h4>
																			{details.bookerProfiles.map(renderBookerDetails)}
																		</div>
																	)}

																	{/* Offers */}
																	<div>
																		<h4 className="font-medium text-gray-900 mb-2">Offers</h4>
																		{renderOffersDetails(details.offers || [])}
																	</div>

																	{/* Show message if no profiles found */}
																	{details.creativeProfiles?.length === 0 && details.bookerProfiles?.length === 0 && (
																		<div className="bg-yellow-50 p-4 rounded-lg">
																			<div className="text-yellow-800 font-medium">No profile data found</div>
																			<div className="text-yellow-600 text-sm mt-1">
																				This user may have profiles that are not properly linked or the profile data may be missing.
																			</div>
																		</div>
																	)}
																</>
															)
														) : (
															<div className="flex items-center justify-center py-4">
																<div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
																<span className="ml-2 text-gray-600">Loading details...</span>
															</div>
														)}
													</div>
												</td>
											</tr>
										)}
									</React.Fragment>
								);
							})}
						</tbody>
					</table>
				</div>

				{filteredUsers.length === 0 && !loading && (
					<div className="text-center py-8 text-gray-500">
						{userTypeFilter === "all" ? "No users found." : `No ${userTypeFilter === "no-profile" ? "users without profiles" : userTypeFilter + "s"} found.`}
					</div>
				)}
			</div>
		</div>
	);
}
