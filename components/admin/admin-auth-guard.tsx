"use client";

import { useEffect, useState } from "react";
import { useUserContext } from "@/src/contexts/UserContext";
import { useRouter } from "next/navigation";
import { useAdminCheck } from "@/components/hooks/use-admin-check";

interface AdminAuthGuardProps {
	children: React.ReactNode;
	fallback?: React.ReactNode;
}

/**
 * Component that guards admin pages and ensures only admins can access them
 */
export default function AdminAuthGuard({ children, fallback }: AdminAuthGuardProps) {
	const { status, data } = useUserContext();
	const { isAdmin, isLoading: adminLoading } = useAdminCheck();
	const router = useRouter();

	useEffect(() => {
		const checkAccess = () => {
			// If user context is still loading, wait
			if (status === "loading" || adminLoading) {
				return;
			}

			// If user is not signed in, redirect to home
			if (status === "error" || !data.signedIn || !data.user) {
				console.log("User not authenticated, redirecting to home");
				router.push("/");
				return;
			}

			// If not admin, redirect to home after a brief delay
			if (!isAdmin) {
				console.log("User is not admin, redirecting to home");
				setTimeout(() => {
					router.push("/");
				}, 2000);
			}
		};

		checkAccess();
	}, [status, data, isAdmin, adminLoading, router]);

	// Show loading state
	if (adminLoading || status === "loading") {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
					<p className="text-gray-600">Verifying admin access...</p>
				</div>
			</div>
		);
	}

	// Show access denied if not admin
	if (!isAdmin) {
		return (
			fallback || (
				<div className="min-h-screen flex items-center justify-center">
					<div className="text-center max-w-md mx-auto p-6">
						<div className="text-red-500 text-6xl mb-4">🚫</div>
						<h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
						<p className="text-gray-600 mb-4">
							You don't have permission to access this admin area.
						</p>
						<p className="text-sm text-gray-500">
							Redirecting to home page...
						</p>
					</div>
				</div>
			)
		);
	}

	// Show admin content if user is admin
	return <>{children}</>;
}
