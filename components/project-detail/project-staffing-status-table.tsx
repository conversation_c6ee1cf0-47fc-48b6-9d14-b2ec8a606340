"use client";

import type { FC } from 'react';
import type { Project, Offer } from '@/components/types';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from '@/components/ui/input';

interface ProjectStaffingStatusTableProps {
  isEditing: boolean;
  project: Project;
  offers: Offer[];
  onOverrideChange: <K extends keyof Project>(field: K, value: Project[K]) => void;
}

const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'confirmed':
    case 'accepted':
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'pending':
    case 'pending_creative_approval':
      return 'bg-yellow-100 text-yellow-800';
    case 'needed':
      return 'bg-orange-100 text-orange-800';
    case 'declined':
    case 'cancelled':
    case 'rejected_by_creative':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const ProjectStaffingStatusTable: FC<ProjectStaffingStatusTableProps> = ({ isEditing, project, offers, onOverrideChange }) => {

  const handleBudgetOverride = (role: string, value: string) => {
    const newOverrides = { ...project.roleBudgetOverrides, [role]: value };
    onOverrideChange('roleBudgetOverrides', newOverrides);
  };

  const handleStatusOverride = (role: string, value: string) => {
    const newOverrides = { ...project.roleStatusOverrides, [role]: value };
    onOverrideChange('roleStatusOverrides', newOverrides);
  };
  
  return (
    <div className="border-t border-gray-200 pt-5 mt-5">
      <h3 className="text-base font-light text-gray-600 tracking-wide mb-4">Project Staffing Status</h3>
      <div className="overflow-x-auto">
        <table className="w-full text-sm">
          <thead className="border-b border-gray-200">
            <tr>
              <th className="text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide">Role</th>
              <th className="text-left py-2 px-2 text-xs font-light text-gray-500 tracking-wide">Offers Sent</th>
              <th className="text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide">Budget Range</th>
              <th className="text-right py-2 px-2 text-xs font-light text-gray-500 tracking-wide">Status</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {(project.creativesNeeded || [])
              .filter(role => role && typeof role === 'string')
              .map((role, index) => {
              const roleOffers = offers.filter(o =>
                o.role && role &&
                typeof o.role === 'string' &&
                typeof role === 'string' &&
                o.role.toLowerCase() === role.toLowerCase()
              );
              
              const deriveStatusForRole = (): "Needed" | "Pending" | "Confirmed" | "Declined" => {
                const acceptedOffers = roleOffers.filter(o => o.status === 'accepted' || o.status === 'active');
                if (acceptedOffers.length > 0) return "Confirmed";

                const pendingOffers = roleOffers.filter(o => o.status === 'pending' || o.status === 'negotiating' || o.status === 'pending_creative_approval');
                if (pendingOffers.length > 0) return "Pending";
                
                return "Needed";
              };
              
              const derivedStatus = deriveStatusForRole();
              const displayStatus = project.roleStatusOverrides?.[role] || derivedStatus;
              
              const deriveBudgetForRole = (): string => {
                if (roleOffers.length === 0) return "-";
                const amounts = roleOffers.map(o => o.amount);
                const min = Math.min(...amounts);
                const max = Math.max(...amounts);
                return min === max ? `$${min.toLocaleString()}` : `$${min.toLocaleString()} - $${max.toLocaleString()}`;
              };

              const derivedBudget = deriveBudgetForRole();
              const displayBudget = project.roleBudgetOverrides?.[role] || derivedBudget;

              return (
                <tr key={role + index} className="hover:bg-gray-50 transition-colors">
                  <td className="py-3 px-2 font-light text-gray-900 whitespace-nowrap">{role}</td>
                  <td className="py-3 px-2">
                    <div className="flex flex-wrap gap-1">
                      {roleOffers.length > 0 ? (
                        roleOffers.map((offer) => (
                          <Link key={offer.id} href={`/creative/${offer.creativeId}`} className="text-xs bg-gray-100 hover:bg-gray-200 rounded-full px-2 py-1 border border-gray-200">
                            {offer.creativeName}
                          </Link>
                        ))
                      ) : (
                        <Link href={`/`} className="text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 rounded px-3 py-1 border border-blue-200">
                           Search {role?.toUpperCase() || 'CREATIVE'}
                        </Link>
                      )}
                    </div>
                  </td>
                  <td className="py-3 px-2 text-right font-light text-gray-900 whitespace-nowrap">
                    {isEditing ? (
                      <Input
                        value={displayBudget}
                        onChange={(e) => handleBudgetOverride(role, e.target.value)}
                        placeholder="$1,000"
                        className="font-light text-sm py-1 px-2 min-w-[120px] text-right"
                      />
                    ) : (
                      displayBudget
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                    {isEditing ? (
                      <Select value={displayStatus} onValueChange={(value) => handleStatusOverride(role, value)}>
                        <SelectTrigger className="w-full text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Needed">Needed</SelectItem>
                          <SelectItem value="Pending">Pending</SelectItem>
                          <SelectItem value="Confirmed">Confirmed</SelectItem>
                          <SelectItem value="Declined">Declined</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(displayStatus)}`}>
                        {displayStatus}
                      </span>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ProjectStaffingStatusTable;
