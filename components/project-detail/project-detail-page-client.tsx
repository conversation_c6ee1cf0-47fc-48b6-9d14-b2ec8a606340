"use client";

import React, { useState } from 'react';
import type { Project, Offer } from '@/components/types';
import SiteHeader from '@/components/layout/site-header';
import PageSectionContainer from '@/components/layout/page-section-container';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, XCircle, PlusCircle, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { updateProject } from '@/src/actions/projectActions';
import ProjectStaffingStatusTable from './project-staffing-status-table';

interface ProjectDetailPageClientProps {
  project: Project;
  offers: Offer[];
}

const projectTypes = [
  "Photography", "Videography", "Event Coverage", "Social Media Content", "Brand Identity", "Music Video Production", "Other"
];

// Helper function for status classes
const getStatusClasses = (status: string) => {
  switch (status) {
    case "active":
      return "bg-blue-100 text-blue-800";
    case "completed":
      return "bg-green-100 text-green-800";
    default:
      return "bg-yellow-100 text-yellow-800";
  }
};

const ProjectDetailPageClient: React.FC<ProjectDetailPageClientProps> = ({ project, offers }) => {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [editableProjectData, setEditableProjectData] = useState<Project>(project);
  const [newRoleName, setNewRoleName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof Project, value: string | number | Project[keyof Project]) => {
    setEditableProjectData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddRole = () => {
    if (newRoleName.trim() !== "") {
      const role = newRoleName.trim();
      setEditableProjectData(prev => ({
        ...prev,
        creativesNeeded: [...(prev.creativesNeeded || []), role],
      }));
      setNewRoleName("");
    }
  };

  const handleRemoveRole = (indexToRemove: number) => {
    setEditableProjectData(prev => ({
      ...prev,
      creativesNeeded: (prev.creativesNeeded || []).filter((_, index) => index !== indexToRemove),
    }));
  };

  const handleSaveChanges = async () => {
    setIsSubmitting(true);
    // We only need to pass the fields that are actually editable
    const { id, createdAt, updatedAt, bookerId, status, ...formData } = editableProjectData;

    const result = await updateProject(project.id, formData);
    
    if (result.success) {
      // The page will be revalidated, so we just need to exit edit mode
      setIsEditing(false);
    } else {
      // Handle error, e.g., show a toast notification
      alert(`Error: ${result.message}`);
    }
    setIsSubmitting(false);
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    if (isEditing) {
      setEditableProjectData(project);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <SiteHeader />
      <PageSectionContainer>
        <div className="py-8">
          <div className="flex justify-between items-center mb-6">
            <Button onClick={() => router.back()} variant="outline" className="font-light tracking-wide">
              <ArrowLeft className="w-4 h-4 mr-2" /> Back
            </Button>
            
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button onClick={handleEditToggle} variant="outline" className="font-light tracking-wide" disabled={isSubmitting}>
                    Cancel
                  </Button>
                  <Button onClick={handleSaveChanges} variant="default" className="font-light tracking-wide bg-green-600 hover:bg-green-700 text-white" disabled={isSubmitting}>
                    {isSubmitting ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Save Changes'}
                  </Button>
                </>
              ) : (
                <Button onClick={handleEditToggle} variant="default" className="font-light tracking-wide bg-black text-white hover:bg-gray-800">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Project
                </Button>
              )}
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-xl overflow-hidden flex flex-col md:flex-row">
            {/* Left Side - Image/Moodboard */}
            <div className="w-full md:w-1/3 relative min-h-[300px] md:min-h-0 bg-gray-100">
              <Image
                src={editableProjectData.moodBoardUrl || "/placeholder.svg"}
                alt={editableProjectData.title}
                fill
                className="object-cover"
              />
            </div>

            {/* Right Side - Project Details */}
            <div className="flex-1 flex flex-col">
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                {isEditing ? (
                   <Input
                    value={editableProjectData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Project Title"
                    className="text-2xl font-light tracking-wide"
                  />
                ) : (
                  <h1 className="text-2xl font-light tracking-wide text-gray-800">{project.title}</h1>
                )}
                <span className={`px-3 py-1 text-xs font-light tracking-wide rounded ${getStatusClasses(project.status)}`}>
                  {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                </span>
              </div>

              <div className="flex-1 p-6 overflow-y-auto space-y-5">
                {/* Client */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Client</h3>
                  {isEditing ? (
                    <Input value={editableProjectData.client} onChange={(e) => handleInputChange('client', e.target.value)} placeholder="Client Name" className="font-light" />
                  ) : (
                    <p className="font-light text-gray-700">{project.client}</p>
                  )}
                </div>
                
                {/* Budget */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Budget (USD)</h3>
                   {isEditing ? (
                    <Input type="number" value={editableProjectData.budget} onChange={(e) => handleInputChange('budget', Number(e.target.value))} placeholder="15000" className="font-light" />
                  ) : (
                    <p className="font-light text-gray-700">${project.budget.toLocaleString()}</p>
                  )}
                </div>

                {/* Project Type */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Project Type</h3>
                   {isEditing ? (
                     <Select value={editableProjectData.projectType} onValueChange={(value) => handleInputChange('projectType', value)}>
                      <SelectTrigger className="font-light"><SelectValue placeholder="Select Type" /></SelectTrigger>
                      <SelectContent>
                        {projectTypes.map(type => <SelectItem key={type} value={type}>{type}</SelectItem>)}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="font-light text-gray-700">{project.projectType}</p>
                  )}
                </div>

                {/* Timeline */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Timeline</h3>
                  {isEditing ? (
                    <div className="flex gap-4 items-center">
                      <Input type="date" value={editableProjectData.startDate.split('T')[0]} onChange={(e) => handleInputChange('startDate', e.target.value)} className="font-light" />
                      <span>to</span>
                      <Input type="date" value={editableProjectData.endDate.split('T')[0]} onChange={(e) => handleInputChange('endDate', e.target.value)} className="font-light" />
                    </div>
                  ) : (
                    <p className="font-light text-gray-700">
                      {project.startDate ? new Date(project.startDate).toLocaleDateString() : 'N/A'}
                      {project.endDate && project.endDate !== project.startDate ? ` to ${new Date(project.endDate).toLocaleDateString()}` : ""}
                    </p>
                  )}
                </div>

                {/* Location */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Location</h3>
                   {isEditing ? (
                    <Input value={editableProjectData.location} onChange={(e) => handleInputChange('location', e.target.value)} placeholder="e.g., Downtown Studio, Los Angeles" className="font-light" />
                  ) : (
                    <p className="font-light text-gray-700">{project.location}</p>
                  )}
                </div>

                {/* Description */}
                <div>
                  <h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">Description</h3>
                   {isEditing ? (
                    <Textarea value={editableProjectData.description} onChange={(e) => handleInputChange('description', e.target.value)} placeholder="Project description..." className="font-light min-h-[100px]" />
                  ) : (
                    <p className="text-gray-700 font-light whitespace-pre-wrap">{project.description}</p>
                  )}
                </div>

                 {/* Creatives Needed */}
                 <div className="border-t border-gray-200 pt-5 mt-5">
                   <h3 className="text-base font-light text-gray-600 tracking-wide mb-4">Creatives Needed</h3>
                   {isEditing ? (
                      <div className="space-y-3">
                        {(editableProjectData.creativesNeeded || []).map((role, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded bg-gray-50">
                            <span className="text-sm text-gray-800">{role}</span>
                            <Button variant="ghost" size="sm" onClick={() => handleRemoveRole(index)} className="text-red-500 hover:text-red-700">
                              <XCircle className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                        <div className="flex items-center gap-2 pt-2">
                          <Input 
                            value={newRoleName} 
                            onChange={(e) => setNewRoleName(e.target.value)} 
                            placeholder="Add new role..."
                            className="flex-grow"
                          />
                          <Button onClick={handleAddRole} variant="outline" size="icon">
                            <PlusCircle className="w-5 h-5" />
                          </Button>
                        </div>
                      </div>
                   ) : (
                    <div className="flex flex-wrap gap-3">
                      {project.creativesNeeded.map((role, index) => (
                        <div key={index} className="bg-gray-100 rounded-full px-4 py-2 text-sm text-gray-800 font-light">
                          {role}
                        </div>
                      ))}
                      {project.creativesNeeded.length === 0 && (
                        <p className="text-gray-500 text-sm font-light">No specific creative roles listed.</p>
                      )}
                    </div>
                   )}
                 </div>

                {/* --- Project Staffing Status --- */}
                <ProjectStaffingStatusTable
                  isEditing={isEditing}
                  project={editableProjectData}
                  offers={offers}
                  onOverrideChange={handleInputChange}
                />
              </div>
            </div>
          </div>
        </div>
      </PageSectionContainer>
    </div>
  );
};

export default ProjectDetailPageClient;