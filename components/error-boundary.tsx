"use client";

import React from "react";

interface ErrorBoundaryState {
	hasError: boolean;
	error?: Error;
}

interface ErrorBoundaryProps {
	children: React.ReactNode;
	fallback?: React.ComponentType<{ error?: Error; retry: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
	constructor(props: ErrorBoundaryProps) {
		super(props);
		this.state = { hasError: false };
	}

	static getDerivedStateFromError(error: Error): ErrorBoundaryState {
		// Update state so the next render will show the fallback UI
		return { hasError: true, error };
	}

	componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
		// Log the error to console or error reporting service
		console.error("ErrorBoundary caught an error:", error, errorInfo);

		// Check if it's a ChunkLoadError and attempt to reload
		if (error.name === "ChunkLoadError" || error.message.includes("Loading chunk")) {
			console.log("ChunkLoadError detected, attempting to reload...");
			// Delay reload to avoid infinite loops
			setTimeout(() => {
				window.location.reload();
			}, 1000);
			return;
		}

		// Check if it's a Firebase permissions error during sign out
		if (error.message.includes("Missing or insufficient permissions") ||
			error.message.includes("permission-denied") ||
			error.code === "permission-denied" ||
			error.message.includes("PERMISSION_DENIED") ||
			(error as any)?.code === "permission-denied") {
			console.log("Firebase permissions error detected, likely due to authentication state change");
			// Redirect to home page after a short delay
			setTimeout(() => {
				window.location.href = "/";
			}, 1500);
		}
	}

	retry = () => {
		this.setState({ hasError: false, error: undefined });
	};

	render() {
		if (this.state.hasError) {
			// Check if it's a ChunkLoadError
			const isChunkError = this.state.error?.name === "ChunkLoadError" ||
				this.state.error?.message.includes("Loading chunk");

			// Check if it's a Firebase permissions error
			const isPermissionError = this.state.error?.message.includes("Missing or insufficient permissions") ||
				this.state.error?.message.includes("permission-denied") ||
				this.state.error?.message.includes("PERMISSION_DENIED") ||
				(this.state.error as any)?.code === "permission-denied";

			if (isChunkError) {
				return (
					<div className="min-h-screen flex items-center justify-center bg-gray-50">
						<div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
							<div className="mb-4">
								<svg className="mx-auto h-12 w-12 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
								</svg>
							</div>
							<h3 className="text-lg font-medium text-gray-900 mb-2">
								Loading Issue Detected
							</h3>
							<p className="text-sm text-gray-500 mb-4">
								The application is updating. Please wait while we refresh the page...
							</p>
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
						</div>
					</div>
				);
			}

			if (isPermissionError) {
				return (
					<div className="min-h-screen flex items-center justify-center bg-gray-50">
						<div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
							<div className="mb-4">
								<svg className="mx-auto h-12 w-12 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
								</svg>
							</div>
							<h3 className="text-lg font-medium text-gray-900 mb-2">
								Authentication Changed
							</h3>
							<p className="text-sm text-gray-500 mb-4">
								Your authentication status has changed. Redirecting to home page...
							</p>
							<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
						</div>
					</div>
				);
			}

			// For other errors, show the custom fallback or default error UI
			if (this.props.fallback) {
				const FallbackComponent = this.props.fallback;
				return <FallbackComponent error={this.state.error} retry={this.retry} />;
			}

			return (
				<div className="min-h-screen flex items-center justify-center bg-gray-50">
					<div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6 text-center">
						<div className="mb-4">
							<svg className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
							</svg>
						</div>
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							Something went wrong
						</h3>
						<p className="text-sm text-gray-500 mb-4">
							{this.state.error?.message || "An unexpected error occurred"}
						</p>
						<button
							onClick={this.retry}
							className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
						>
							Try Again
						</button>
					</div>
				</div>
			);
		}

		return this.props.children;
	}
}

export default ErrorBoundary;
