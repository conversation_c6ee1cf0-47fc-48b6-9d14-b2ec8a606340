import { Button } from "@/components/ui/button"; // Assuming you have a Button component
import { PlusCircle } from "lucide-react"; // Icon for the button
import Image from "next/image";
import type React from "react";

interface Member {
	id: string;
	name: string;
	avatarUrl?: string;
	permission: "admin" | "editor" | "viewer";
}

interface MembersTabContentProps {
	members: Member[];
	onInviteMember: () => void; // Callback for the invite button
}

const MembersTabContent: React.FC<MembersTabContentProps> = ({
	members,
	onInviteMember,
}) => {
	return (
		<div className="py-8">
			<div className="flex justify-between items-center mb-6 px-4 md:px-0">
				<h2 className="text-2xl font-light tracking-wide text-gray-700">
					Members
				</h2>
				<Button
					variant="outline"
					size="sm"
					onClick={onInviteMember}
					className="text-xs font-light tracking-wide"
				>
					<PlusCircle className="w-4 h-4 mr-2" />+ Member
				</Button>
			</div>

			{members.length > 0 ? (
				<ul className="space-y-2">
					{members.map((member) => (
						<li
							key={member.id}
							className="p-3 border border-gray-200 rounded-md text-sm bg-white shadow-sm hover:shadow-md transition-shadow"
						>
							<span className="font-medium text-gray-800">{member.name}</span> -{" "}
							<span className="capitalize text-gray-600">
								{member.permission}
							</span>
						</li>
					))}
				</ul>
			) : (
				<div className="text-center text-gray-500 font-light py-10">
					<p>No members have been added to this booker profile yet.</p>
					<Button
						variant="default"
						size="sm"
						onClick={onInviteMember}
						className="mt-4 text-xs font-light tracking-wide"
					>
						<PlusCircle className="w-4 h-4 mr-2" />
						Invite First Member
					</Button>
				</div>
			)}
		</div>
	);
};

export default MembersTabContent;
