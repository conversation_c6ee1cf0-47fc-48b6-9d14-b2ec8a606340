import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import type React from "react";
import type { Creative } from "../../../src/types/collections/creative";
import type { Project } from "../../types";

interface FindCreativesModalProps {
	isOpen: boolean;
	onClose: () => void;
	role: string | null;
	project: Project;
	creatives: Creative[];
	onSendOffer: (creativeId: string, creativeName: string, role: string) => void;
}

const FindCreativesModal: React.FC<FindCreativesModalProps> = ({
	isOpen,
	onClose,
	role,
	project,
	creatives,
	onSendOffer,
}) => {
	if (!isOpen || !role) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded-lg max-w-lg w-full max-h-[80vh] flex flex-col">
				<div className="flex items-center justify-between p-4 border-b border-gray-200">
					<h2 className="text-lg font-light">
						Find Creative for: <span className="font-semibold">{role}</span>
					</h2>
					<Button variant="ghost" size="icon" onClick={onClose}>
						<X className="w-5 h-5" />
					</Button>
				</div>
				<div className="flex-1 p-4 overflow-y-auto">
					<div className="space-y-3">
						{creatives.map((creative) => (
							<div
								key={creative.id}
								className="flex items-center justify-between p-3 border rounded-md"
							>
								<div>
									<h3 className="font-medium">{creative.displayName}</h3>
									<p className="text-sm text-gray-500">
										{creative.tags?.join(", ")}
									</p>
								</div>
								<Button
									size="sm"
									onClick={() =>
										onSendOffer(creative.id, creative.displayName, role)
									}
								>
									Send Offer
								</Button>
							</div>
						))}
					</div>
				</div>
			</div>
		</div>
	);
};

export default FindCreativesModal;
