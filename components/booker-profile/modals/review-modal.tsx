import type { ReviewFormData } from "@/components/hooks/use-review-modal"; // Adjusted path
import type { PastCollaboration } from "@/components/types"; // Adjusted path
import { But<PERSON> } from "@/components/ui/button";
import { MoreHorizontal, Star } from "lucide-react";
import Image from "next/image";
import type React from "react";

interface ReviewModalProps {
	showModal: boolean;
	selectedCollaboration: PastCollaboration | null;
	reviewForm: ReviewFormData;
	onClose: () => void;
	onFormChange: (field: keyof ReviewFormData, value: string | number) => void;
	onSubmit: (e: React.FormEvent) => void;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
	showModal,
	selectedCollaboration,
	reviewForm,
	onClose,
	onFormChange,
	onSubmit,
}) => {
	if (!showModal || !selectedCollaboration) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden">
				<div className="flex items-center justify-between p-6 border-b border-gray-100">
					<h2 className="text-xl font-light tracking-wide">Review Artist</h2>
					<button onClick={onClose}>
						<MoreHorizontal className="w-5 h-5" />
					</button>
				</div>

				<form
					onSubmit={onSubmit}
					className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
				>
					<div className="space-y-6">
						<div className="flex items-center gap-4">
							<Image
								src={selectedCollaboration.artistAvatar || "/placeholder.svg"}
								alt={selectedCollaboration.artistName}
								width={60}
								height={60}
								className="rounded-full object-cover"
							/>
							<div>
								<h3 className="font-light text-lg">
									{selectedCollaboration.artistName}
								</h3>
								<p className="text-gray-600 font-light">
									{selectedCollaboration.projectTitle}
								</p>
							</div>
						</div>

						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Rating *
							</label>
							<div className="flex gap-2">
								{[1, 2, 3, 4, 5].map((star) => (
									<button
										key={star}
										type="button"
										onClick={() => onFormChange("rating", star)}
										className="focus:outline-none"
									>
										<Star
											className={`w-8 h-8 ${
												star <= reviewForm.rating
													? "fill-yellow-400 text-yellow-400"
													: "text-gray-300 hover:text-yellow-300"
											} transition-colors`}
										/>
									</button>
								))}
							</div>
						</div>

						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Review
							</label>
							<textarea
								rows={4}
								value={reviewForm.review}
								onChange={(e) => onFormChange("review", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="Share your experience working with this creative..."
							/>
						</div>
					</div>

					<div className="flex gap-4 mt-8 pt-6 border-t border-gray-100">
						<Button
							type="button"
							variant="outline"
							onClick={onClose}
							className="flex-1 font-light tracking-wide"
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide"
						>
							Save Review
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default ReviewModal;
