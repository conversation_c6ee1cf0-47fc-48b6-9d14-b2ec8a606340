import type { Offer } from "@/components/types";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import type React from "react";

interface ViewOffersModalProps {
	isOpen: boolean;
	onClose: () => void;
	role: string;
	offers: Offer[];
}

const ViewOffersModal: React.FC<ViewOffersModalProps> = ({
	isOpen,
	onClose,
	role,
	offers,
}) => {
	if (!isOpen) return null;

	const getStatusClass = (
		status:
			| "pending"
			| "accepted"
			| "declined"
			| "expired"
			| "withdrawn"
			| "negotiating"
			| "pending_creative_approval"
			| "active"
			| "rejected_by_creative",
	) => {
		switch (status) {
			case "accepted":
			case "active":
				return "bg-green-100 text-green-800";
			case "declined":
			case "rejected_by_creative":
				return "bg-red-100 text-red-800";
			case "pending":
			case "pending_creative_approval":
				return "bg-yellow-100 text-yellow-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded-lg max-w-lg w-full max-h-[80vh] flex flex-col">
				<div className="flex items-center justify-between p-4 border-b border-gray-200">
					<h2 className="text-lg font-light">
						Offers for: <span className="font-semibold">{role}</span>
					</h2>
					<Button variant="ghost" size="icon" onClick={onClose}>
						<X className="w-5 h-5" />
					</Button>
				</div>
				<div className="flex-1 p-4 overflow-y-auto">
					<div className="space-y-3">
						{offers.length > 0 ? (
							offers.map((offer) => (
								<div
									key={offer.id}
									className="flex items-center justify-between p-3 border rounded-md"
								>
									<div>
										<h3 className="font-medium">{offer.creativeName}</h3>
										<p className="text-sm text-gray-500">
											Amount: ${offer.amount.toLocaleString()}
										</p>
									</div>
									<span
										className={`px-3 py-1 text-xs font-light tracking-wide rounded ${getStatusClass(offer.status)}`}
									>
										{offer.status.charAt(0).toUpperCase() +
											offer.status.slice(1)}
									</span>
								</div>
							))
						) : (
							<p className="text-center text-gray-500 py-4">
								No offers have been sent for this role yet.
							</p>
						)}
					</div>
				</div>
			</div>
		</div>
	);
};

export default ViewOffersModal;
