import type { Offer, Project } from "@/components/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Edit, MoreHorizontal, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import type React from "react";
import { mockCreatives } from "../creative-data"; // Using mock data for now
import { useFindCreativesModal } from "../hooks/use-find-creatives-modal";
import { useViewOffersModal } from "../hooks/use-view-offers-modal";
import StaffingStatusRow from "../staffing-status-row";
import FindCreativesModal from "./find-creatives-modal";
import ViewOffersModal from "./view-offers-modal";

interface ProjectDetailModalProps {
	project: Project;
	offers: Offer[];
	offersStatus: "loading" | "error" | "success";
	onClose: () => void;
	onEdit: () => void;
	onDelete: () => void;
	sendOffer: (
		project: Project,
		creativeId: string,
		creativeName: string,
		role: string,
	) => Promise<void>;
}

const ProjectDetailModal: React.FC<ProjectDetailModalProps> = ({
	project,
	offers,
	offersStatus,
	onClose,
	onEdit,
	onDelete,
	sendOffer,
}) => {
	const {
		isModalOpen: isFindCreativesModalOpen,
		selectedRole: selectedRoleForFind,
		openModal: openFindCreativesModal,
		closeModal: closeFindCreativesModal,
	} = useFindCreativesModal();
	const {
		isModalOpen: isViewOffersModalOpen,
		selectedRoleOffers,
		selectedRoleName,
		openModal: openViewOffersModal,
		closeModal: closeViewOffersModal,
	} = useViewOffersModal();

	if (!project) return null;

	const handleSendOffer = async (
		creativeId: string,
		creativeName: string,
		role: string,
	) => {
		try {
			await sendOffer(project, creativeId, creativeName, role);
			alert(`Offer sent to ${creativeName}!`);
			closeFindCreativesModal();
		} catch (error) {
			alert("Failed to send offer. Please try again.");
		}
	};

	const handleViewOffers = (role: string) => {
		const offersForRole = offers.filter((offer) => offer.role === role);
		openViewOffersModal(role, offersForRole);
	};

	return (
		<>
			<div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
				<div className="bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row">
					<div className="w-full md:w-80 relative min-h-[250px] md:min-h-0 bg-gray-100">
						<div className="w-full h-full flex items-center justify-center">
							<span className="text-5xl font-light text-gray-400">
								{project.title.charAt(0)}
							</span>
						</div>
					</div>

					<div className="flex-1 flex flex-col">
						<div className="flex items-center justify-between p-4 border-b border-gray-100">
							<span className="font-light tracking-wide">{project.title}</span>
							<div className="flex items-center gap-2">
								<Button variant="ghost" size="icon" onClick={onEdit}>
									<Edit className="w-4 h-4" />
								</Button>
								<Button
									variant="ghost"
									size="icon"
									onClick={onDelete}
									className="text-red-500 hover:text-red-600"
								>
									<Trash2 className="w-4 h-4" />
								</Button>
								<Button variant="ghost" size="icon" onClick={onClose}>
									<MoreHorizontal className="w-5 h-5" />
								</Button>
							</div>
						</div>

						<div className="flex-1 p-4 overflow-y-auto">
							<div className="space-y-4">
								<div className="mb-4">
									<span
										className={`px-3 py-1 text-xs font-light tracking-wide rounded ${
											project.status === "active"
												? "bg-blue-100 text-blue-800"
												: project.status === "completed"
													? "bg-green-100 text-green-800"
													: "bg-yellow-100 text-yellow-800"
										}`}
									>
										{project.status.charAt(0).toUpperCase() +
											project.status.slice(1)}
									</span>
								</div>

								<div>
									<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
										Client
									</h3>
									<p className="font-light">{project.client}</p>
								</div>

								<div>
									<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
										Budget
									</h3>
									<p className="font-light">
										${project.budget.toLocaleString()}
									</p>
								</div>

								{project.projectType && (
									<div>
										<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
											Project Type
										</h3>
										<p className="font-light">{project.projectType}</p>
									</div>
								)}

								{project.startDate && (
									<div>
										<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
											Timeline
										</h3>
										<p className="font-light">
											{project.startDate}
											{project.endDate && project.endDate !== project.startDate
												? ` to ${project.endDate}`
												: ""}
										</p>
									</div>
								)}

								{project.location && (
									<div>
										<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
											Location
										</h3>
										<p className="font-light">{project.location}</p>
									</div>
								)}

								{project.description && (
									<div>
										<h3 className="text-sm font-light text-gray-500 tracking-wide mb-1">
											Description
										</h3>
										<p className="text-gray-700 font-light">
											{project.description}
										</p>
									</div>
								)}

								<div className="border-t border-gray-100 pt-4">
									<h3 className="text-sm font-light text-gray-500 tracking-wide mb-3">
										Project Staffing
									</h3>
									{offersStatus === "loading" && (
										<p>Loading staffing status...</p>
									)}
									{offersStatus === "error" && (
										<p>Error loading staffing status.</p>
									)}
									{offersStatus === "success" && (
										<table className="w-full text-sm">
											<thead>
												<tr className="border-b border-gray-100">
													<th className="text-left py-2 px-4 text-xs font-light text-gray-500 tracking-wide">
														Role
													</th>
													<th className="text-left py-2 px-4 text-xs font-light text-gray-500 tracking-wide">
														Status
													</th>
													<th className="text-right py-2 px-4 text-xs font-light text-gray-500 tracking-wide">
														Action
													</th>
												</tr>
											</thead>
											<tbody>
												{project.creativesNeeded &&
												project.creativesNeeded.length > 0 ? (
													project.creativesNeeded.map((role) => (
														<StaffingStatusRow
															key={role}
															role={role}
															offers={offers}
															onFindCreatives={openFindCreativesModal}
															onViewOffers={() => handleViewOffers(role)}
														/>
													))
												) : (
													<tr>
														<td
															colSpan={3}
															className="py-6 text-center text-gray-500"
														>
															<p className="text-sm font-light">
																No creative roles specified for this project.
															</p>
														</td>
													</tr>
												)}
											</tbody>
										</table>
									)}
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<FindCreativesModal
				isOpen={isFindCreativesModalOpen}
				onClose={closeFindCreativesModal}
				role={selectedRoleForFind}
				project={project}
				creatives={mockCreatives}
				onSendOffer={handleSendOffer}
			/>

			<ViewOffersModal
				isOpen={isViewOffersModalOpen}
				onClose={closeViewOffersModal}
				role={selectedRoleName}
				offers={selectedRoleOffers}
			/>
		</>
	);
};

export default ProjectDetailModal;
