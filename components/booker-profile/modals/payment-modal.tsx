import { <PERSON><PERSON> } from "@/components/ui/button";
import { CreditCard, MoreHorizontal, Plus, Trash2 } from "lucide-react"; // Added Plus for Add New Card
import type React from "react";

// Dummy data for payment methods - in a real app, this would come from props or state management
const paymentMethods = [
	{ id: "pm_1", type: "Visa", last4: "1234", expiry: "08/25", isDefault: true },
	{
		id: "pm_2",
		type: "Mastercard",
		last4: "5678",
		expiry: "12/26",
		isDefault: false,
	},
	{
		id: "pm_3",
		type: "Amex",
		last4: "9012",
		expiry: "03/24",
		isDefault: false,
	},
];

interface PaymentModalProps {
	showModal: boolean;
	onClose: () => void;
	// Add props for handling payment method actions if needed, e.g.,
	// onSetDefault: (paymentMethodId: string) => void;
	// onDeletePaymentMethod: (paymentMethodId: string) => void;
	// onAddNewCard: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({ showModal, onClose }) => {
	if (!showModal) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden">
				<div className="flex items-center justify-between p-6 border-b border-gray-100">
					<h2 className="text-xl font-light tracking-wide">
						Manage Payment Methods
					</h2>
					<button onClick={onClose}>
						<MoreHorizontal className="w-5 h-5" />
					</button>
				</div>

				<div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
					{" "}
					{/* Adjusted max-h for footer */}
					<div className="space-y-4 mb-6">
						{paymentMethods.map((method) => (
							<div
								key={method.id}
								className="border border-gray-200 rounded p-4 flex items-center justify-between hover:shadow-sm transition-shadow"
							>
								<div className="flex items-center gap-3">
									<CreditCard
										className={`w-6 h-6 ${method.isDefault ? "text-blue-600" : "text-gray-500"}`}
									/>
									<div>
										<span className="font-light tracking-wide text-sm text-gray-800">
											{method.type} ending in {method.last4}
										</span>
										<span className="block text-xs text-gray-500 font-light">
											Expires {method.expiry}
										</span>
									</div>
									{method.isDefault && (
										<span className="px-2 py-0.5 text-xs font-light tracking-wide bg-blue-100 text-blue-700 rounded-full">
											Default
										</span>
									)}
								</div>
								<div className="flex items-center gap-2">
									{!method.isDefault && (
										<Button
											variant="outline"
											size="sm"
											className="text-xs font-light tracking-wide"
										>
											Set as Default
										</Button>
									)}
									<Button
										variant="ghost"
										size="sm"
										className="text-red-500 hover:text-red-700"
									>
										<Trash2 className="w-4 h-4" />
									</Button>
								</div>
							</div>
						))}
						{paymentMethods.length === 0 && (
							<p className="text-center text-gray-500 font-light py-8">
								No payment methods saved.
							</p>
						)}
					</div>
				</div>

				<div className="p-6 border-t border-gray-100 flex justify-end">
					<Button
						// onClick={onAddNewCard} // Add handler if functionality is implemented
						className="bg-black text-white hover:bg-gray-800 font-light tracking-wide"
					>
						<Plus className="w-4 h-4 mr-2" />
						Add New Card
					</Button>
				</div>
			</div>
		</div>
	);
};

export default PaymentModal;
