import type { NewProjectFormData } from "@/components/hooks/use-add-project-form"; // Adjusted path
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, MoreHorizontal } from "lucide-react";
import type React from "react";

interface AddProjectModalProps {
	showModal: boolean;
	isSubmitting: boolean;
	onClose: () => void;
	formData: NewProjectFormData;
	onFormChange: (
		field: keyof NewProjectFormData,
		value: string | string[],
	) => void;
	onSubmit: (e: React.FormEvent) => void;
	onAddArtistRole: () => void;
	onRemoveArtistRole: (index: number) => void;
	isEditing?: boolean;
}

const AddProjectModal: React.FC<AddProjectModalProps> = ({
	showModal,
	isSubmitting,
	onClose,
	formData,
	onFormChange,
	onSubmit,
	onAddArtistRole,
	onRemoveArtistRole,
	isEditing,
}) => {
	if (!showModal) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden">
				<div className="flex items-center justify-between p-6 border-b border-gray-100">
					<h2 className="text-xl font-light tracking-wide">
						{isEditing ? "Edit Project" : "Add New Project"}
					</h2>
					<button onClick={onClose}>
						<MoreHorizontal className="w-5 h-5" />
					</button>
				</div>
				<form
					onSubmit={onSubmit}
					className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
				>
					<div className="space-y-6">
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Project Title *
							</label>
							<input
								type="text"
								required
								value={formData.title}
								onChange={(e) => onFormChange("title", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="e.g., Summer Fashion Campaign"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Client *
							</label>
							<input
								type="text"
								required
								value={formData.client}
								onChange={(e) => onFormChange("client", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="e.g., Nike"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Budget (USD) *
							</label>
							<input
								type="number"
								required
								value={formData.budget}
								onChange={(e) => onFormChange("budget", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="15000"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Project Type *
							</label>
							<select
								required
								value={formData.projectType}
								onChange={(e) => onFormChange("projectType", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
							>
								<option value="">Select Type</option>
								<option value="Photography">Photography</option>
								<option value="Videography">Videography</option>
								<option value="Event Coverage">Event Coverage</option>
								<option value="Social Media Content">
									Social Media Content
								</option>
								<option value="Brand Identity">Brand Identity</option>
								<option value="Music Video Production">
									Music Video Production
								</option>
								<option value="Other">Other</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Start Date *
							</label>
							<input
								type="date"
								required
								value={formData.startDate}
								onChange={(e) => onFormChange("startDate", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								End Date
							</label>
							<input
								type="date"
								value={formData.endDate}
								onChange={(e) => onFormChange("endDate", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Location *
							</label>
							<input
								type="text"
								required
								value={formData.location}
								onChange={(e) => onFormChange("location", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="e.g., Los Angeles, CA"
							/>
						</div>
						<div>
							<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
								Description
							</label>
							<textarea
								rows={3}
								value={formData.description}
								onChange={(e) => onFormChange("description", e.target.value)}
								className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
								placeholder="Describe your project requirements and vision..."
							/>
						</div>
						<div>
							<div className="flex items-center justify-between mb-4">
								<h3 className="text-lg font-light tracking-wide">
									creatives Needed
								</h3>
								<Button
									type="button"
									variant="outline"
									size="sm"
									onClick={onAddArtistRole}
									className="text-xs font-light tracking-wide"
								>
									+ Add Role
								</Button>
							</div>
							<div className="flex flex-wrap gap-2">
								{formData.creativesNeeded.map((role, index) => (
									<div
										key={index}
										className="flex items-center gap-2 bg-gray-100 rounded-full px-3 py-1"
									>
										<span className="text-sm font-light">{role}</span>
										<button
											type="button"
											onClick={() => onRemoveArtistRole(index)}
											className="text-red-500 hover:text-red-700"
										>
											×
										</button>
									</div>
								))}
								{formData.creativesNeeded.length === 0 && (
									<p className="text-gray-500 text-sm font-light">
										No creative roles added yet.
									</p>
								)}
							</div>
						</div>
					</div>
					<div className="flex gap-4 mt-8 pt-6 border-t border-gray-100">
						<Button
							type="button"
							variant="outline"
							onClick={onClose}
							className="flex-1 font-light tracking-wide"
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide"
							disabled={isSubmitting}
						>
							{isSubmitting ? (
								<Loader2 className="animate-spin" />
							) : isEditing ? (
								"Save Changes"
							) : (
								"Create Project"
							)}
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default AddProjectModal;
