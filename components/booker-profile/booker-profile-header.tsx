import type React from "react";

interface BookerProfileHeaderProps {
	name: string;
	type: string;
	projectCount: number;
	offersSentCount: number;
	collaborationsCount: number;
	membersCount: number;
}

const BookerProfileHeader: React.FC<BookerProfileHeaderProps> = ({
	name,
	type,
	projectCount,
	offersSentCount,
	collaborationsCount,
	membersCount,
}) => {
	return (
		<div className="text-center mb-12">
			<h2 className="text-3xl font-light tracking-wide mb-3">{name}</h2>
			<p className="text-gray-600 font-light tracking-wide mb-6">{type}</p>
			<div className="flex justify-center gap-12 text-sm font-light">
				<span>
					<strong className="font-normal">{projectCount}</strong> Projects
				</span>
				<span>
					<strong className="font-normal">{offersSentCount}</strong> Offers Sent
				</span>
				<span>
					<strong className="font-normal">{collaborationsCount}</strong>{" "}
					Collaborations
				</span>
				<span>
					<strong className="font-normal">{membersCount}</strong> Members
				</span>
			</div>
		</div>
	);
};

export default BookerProfileHeader;
