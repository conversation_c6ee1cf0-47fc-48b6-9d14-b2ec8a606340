import type { Offer } from "@/components/types";
import { Button } from "@/components/ui/button";
import { ChevronRight, Search } from "lucide-react";
import type React from "react";

interface StaffingStatusRowProps {
	role: string;
	offers: Offer[];
	onFindCreatives: (role: string) => void;
	onViewOffers: (role: string) => void;
}

const StaffingStatusRow: React.FC<StaffingStatusRowProps> = ({
	role,
	offers,
	onFindCreatives,
	onViewOffers,
}) => {
	const getStatus = (): { text: string; color: string } => {
		const roleOffers = offers.filter(
			(offer) => offer.role.toLowerCase() === role.toLowerCase(),
		);

		if (roleOffers.some((offer) => offer.status === "accepted" || offer.status === "active")) {
			return { text: "Confirmed", color: "text-green-600 bg-green-100" };
		}
		if (roleOffers.some((offer) => offer.status === "pending" || offer.status === "pending_creative_approval")) {
			return { text: "Pending", color: "text-yellow-600 bg-yellow-100" };
		}
		return { text: "Needed", color: "text-orange-600 bg-orange-100" };
	};

	const status = getStatus();
	const hasOffers = offers.some(
		(offer) => offer.role.toLowerCase() === role.toLowerCase(),
	);

	return (
		<tr className="border-b border-gray-100 hover:bg-gray-50">
			<td className="py-3 px-4 font-light text-gray-900">{role}</td>
			<td className="py-3 px-4">
				<span
					className={`px-2 py-1 text-xs font-light tracking-wide rounded-full ${status.color}`}
				>
					{status.text}
				</span>
			</td>
			<td className="py-3 px-4 text-right">
				{hasOffers ? (
					<Button variant="ghost" size="sm" onClick={() => onViewOffers(role)}>
						View Offers
						<ChevronRight className="w-4 h-4 ml-1" />
					</Button>
				) : (
					<Button
						variant="outline"
						size="sm"
						onClick={() => onFindCreatives(role)}
					>
						<Search className="w-4 h-4 mr-2" />
						Find Creatives
					</Button>
				)}
			</td>
		</tr>
	);
};

export default StaffingStatusRow;
