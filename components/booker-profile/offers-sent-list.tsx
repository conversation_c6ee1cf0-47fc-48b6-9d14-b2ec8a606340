import type { Offer } from "@/components/types";
import Link from "next/link";
import type React from "react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { CheckCircle } from "lucide-react";
import CompleteOfferModal from "../offer/complete-offer-modal";

interface OffersSentListProps {
	offers: Offer[];
	onOfferUpdate?: () => void;
}

const OffersSentList: React.FC<OffersSentListProps> = ({ offers, onOfferUpdate }) => {
	const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
	const [showCompleteModal, setShowCompleteModal] = useState(false);

	const handleCompleteOffer = (offer: Offer) => {
		setSelectedOffer(offer);
		setShowCompleteModal(true);
	};

	const handleModalClose = () => {
		setShowCompleteModal(false);
		setSelectedOffer(null);
	};

	const handleOfferCompleted = () => {
		onOfferUpdate?.();
		handleModalClose();
	};
	if (offers.length === 0) {
		return (
			<div className="text-center py-12">
				<p className="text-gray-500 font-light tracking-wide">
					No offers have been sent yet.
				</p>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<h2 className="text-xl font-light tracking-wide text-center mb-12">
				Offers Sent to creatives
			</h2>
			{offers.map((offer) => (
				<div
					key={offer.id}
					className="border border-gray-100 p-6 hover:shadow-sm transition-shadow"
				>
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<h3 className="font-light tracking-wide text-lg mb-2">
								{offer.projectTitle}
							</h3>
							<p className="text-gray-600 mb-2 font-light tracking-wide">
								Artist:
								<Link
									href={`/creative/${offer.creativeId}`}
									className="text-blue-600 hover:text-blue-800 underline ml-1"
								>
									{offer.creativeName}
								</Link>
							</p>
							<div className="flex items-center gap-6 text-sm text-gray-500 font-light tracking-wide">
								<span>{offer.projectType}</span>
								<span>${offer.amount?.toLocaleString() || 'N/A'}</span>
								<span>{offer.location}</span>
								<span>{offer.offerDate}</span>
							</div>
						</div>
						<div className="text-right flex flex-col items-end gap-2">
							<span
								className={`px-3 py-1 text-xs font-light tracking-wide ${
									offer.status === "accepted"
										? "bg-green-100 text-green-800"
										: offer.status === "declined"
											? "bg-red-100 text-red-800"
											: offer.status === "expired"
												? "bg-gray-100 text-gray-800"
												: offer.status === "active"
													? "bg-blue-100 text-blue-800"
													: "bg-yellow-100 text-yellow-800"
								}`}
							>
								{offer.status.charAt(0).toUpperCase() + offer.status.slice(1)}
							</span>
							{offer.status === "active" && (
								<Button
									size="sm"
									onClick={() => handleCompleteOffer(offer)}
									className="bg-green-600 hover:bg-green-700 text-white"
								>
									<CheckCircle className="w-4 h-4 mr-2" />
									Complete
								</Button>
							)}
						</div>
					</div>
				</div>
			))}

			{/* Complete Offer Modal */}
			{selectedOffer && (
				<CompleteOfferModal
					offer={selectedOffer}
					isOpen={showCompleteModal}
					onClose={handleModalClose}
					onComplete={handleOfferCompleted}
				/>
			)}
		</div>
	);
};

export default OffersSentList;
