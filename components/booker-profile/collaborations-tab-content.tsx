import type { PastCollaboration } from "@/components/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Edit3, Search, Star } from "lucide-react";
import Image from "next/image";
import type React from "react";
import { useMemo, useState } from "react";

interface CollaborationsTabContentProps {
	collaborations: PastCollaboration[];
	onOpenReviewModal: (collaboration: PastCollaboration) => void;
	title?: string;
}

const ALL_TYPES_SELECT_VALUE = "__ALL_TYPES__";

const CollaborationsTabContent: React.FC<CollaborationsTabContentProps> = ({
	collaborations,
	onOpenReviewModal,
	title = "Collaborations",
}) => {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedProjectType, setSelectedProjectType] = useState<string>("");

	const uniqueProjectTypes = useMemo(() => {
		const types = new Set(
			collaborations
				.map((collab) => collab.projectType)
				.filter((type): type is string => !!type),
		);
		return Array.from(types).sort();
	}, [collaborations]);

	const handleProjectTypeChange = (value: string) => {
		if (value === ALL_TYPES_SELECT_VALUE) {
			setSelectedProjectType("");
		} else {
			setSelectedProjectType(value);
		}
	};

	const filteredCollaborations = collaborations.filter((collab) => {
		const term = searchTerm.toLowerCase();
		const matchesSearchTerm =
			term === "" ||
			(collab.projectTitle || "").toLowerCase().includes(term) ||
			(collab.artistName || "").toLowerCase().includes(term);

		const matchesProjectType =
			selectedProjectType === "" || collab.projectType === selectedProjectType;

		return matchesSearchTerm && matchesProjectType;
	});

	if (collaborations.length === 0) {
		return (
			<div className="text-center py-12">
				<p className="text-gray-500 font-light tracking-wide">
					No collaborations to display.
				</p>
			</div>
		);
	}

	return (
		<div>
			<h2 className="text-xl font-light tracking-wide text-center mb-6">
				{title}
			</h2>

			<div className="mb-8 max-w-xl mx-auto flex flex-col md:flex-row gap-4 items-center">
				<div className="relative flex-grow w-full md:w-auto">
					<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<Search className="h-4 w-4 text-gray-400" />
					</div>
					<Input
						type="text"
						placeholder="Search by project or creative..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full pl-10 pr-4 py-2 h-10 border-gray-200 rounded-md font-light focus:border-gray-300 focus:ring-0"
					/>
				</div>
				<div className="w-full md:w-auto md:min-w-[200px]">
					<Select
						value={selectedProjectType}
						onValueChange={handleProjectTypeChange}
					>
						<SelectTrigger className="w-full h-10 border-gray-200 rounded-md font-light focus:ring-0">
							<SelectValue placeholder="Filter by type..." />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value={ALL_TYPES_SELECT_VALUE} className="font-light">
								All Types
							</SelectItem>
							{uniqueProjectTypes.map((type) => (
								<SelectItem key={type} value={type} className="font-light">
									{type}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>
			</div>

			{filteredCollaborations.length === 0 &&
				(searchTerm || selectedProjectType) && (
					<div className="text-center py-12">
						<p className="text-gray-500 font-light tracking-wide">
							No collaborations found matching your criteria.
						</p>
					</div>
				)}

			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
				{filteredCollaborations.map((collaboration) => (
					<div
						key={collaboration.id}
						className="group bg-white rounded-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300 ease-in-out flex flex-col overflow-hidden"
					>
						<div className="relative aspect-[3/4] w-full overflow-hidden bg-gray-50">
							<Image
								src={collaboration.artistAvatar || "/placeholder.svg"}
								alt={collaboration.artistName || "Unknown Artist"}
								fill
								className="object-cover transition-transform duration-300 group-hover:scale-105"
							/>
						</div>
						<div className="p-4 flex flex-col flex-grow">
							<h3 className="text-base font-light tracking-wide text-gray-800 mb-1 truncate">
								{collaboration.projectTitle || "Untitled Project"}
							</h3>
							<p className="text-sm text-gray-500 font-light mb-2">
								Artist:{" "}
								<span className="text-gray-700">
									{collaboration.artistName || "Unknown Artist"}
								</span>
							</p>

							<div className="text-xs text-gray-400 font-light mb-3 space-y-0.5">
								<p>Type: {collaboration.projectType || "N/A"}</p>
								<p>Budget: ${collaboration.budget.toLocaleString()}</p>
								<p>
									Completed:{" "}
									{collaboration.completedDate.toDate().toLocaleDateString()}
								</p>
							</div>

							{/* Rating */}
							<div className="flex items-center gap-1 mb-2">
								{[1, 2, 3, 4, 5].map((star) => (
									<Star
										key={star}
										className={`w-3.5 h-3.5 ${
											star <= (collaboration.rating || 0)
												? "fill-yellow-400 text-yellow-400"
												: "text-gray-300"
										}`}
									/>
								))}
								{collaboration.rating ? (
									<span className="text-xs text-gray-500 ml-1 font-light">
										({collaboration.rating}/5)
									</span>
								) : (
									<span className="text-xs text-gray-400 font-light">
										Not rated
									</span>
								)}
							</div>

							{/* Review */}
							{collaboration.review && (
								<p className="text-xs text-gray-600 italic font-light mb-3 flex-grow leading-relaxed line-clamp-3">
									"{collaboration.review}"
								</p>
							)}
							{
								!collaboration.review && (
									<div className="flex-grow min-h-[2rem]"></div>
								) /* Spacer if no review, ensures consistent card height */
							}

							<div className="mt-auto pt-3">
								<Button
									variant="outline"
									size="sm"
									onClick={() => onOpenReviewModal(collaboration)}
									className="w-full text-xs font-light tracking-wide hover:border-gray-400"
								>
									<Edit3 className="w-3 h-3 mr-1.5" />
									{collaboration.review || collaboration.rating
										? "Edit Review"
										: "Add Review"}
								</Button>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default CollaborationsTabContent;
