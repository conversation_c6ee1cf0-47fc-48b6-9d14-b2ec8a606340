import { Timestamp } from "firebase/firestore";
import type { Creative } from "../../src/types/collections/creative";

export const mockCreatives: Creative[] = [
	{
		id: "creative-1",
		userId: "user-c1",
		displayName: "<PERSON>",
		email: "<EMAIL>",
		tags: ["Fashion Photography", "Portraits"],
		location: "Milan, Italy",
		portfolioPhotos: [],
		createdAt: Timestamp.fromDate(new Date()),
		updatedAt: Timestamp.fromDate(new Date()),
	},
	{
		id: "creative-2",
		userId: "user-c2",
		displayName: "<PERSON>",
		email: "<EMAIL>",
		tags: ["Street Style Videography", "Documentary"],
		location: "New York, USA",
		portfolioPhotos: [],
		createdAt: Timestamp.fromDate(new Date()),
		updatedAt: Timestamp.fromDate(new Date()),
	},
	{
		id: "creative-3",
		userId: "user-c3",
		displayName: "<PERSON>",
		email: "<EMAIL>",
		tags: ["Editorial Modeling", "Runway"],
		location: "Paris, France",
		portfolioPhotos: [],
		createdAt: Timestamp.fromDate(new Date()),
		updatedAt: Timestamp.fromDate(new Date()),
	},
];
