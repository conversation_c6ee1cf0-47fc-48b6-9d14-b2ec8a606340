"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import type React from "react";
import { useState, useMemo } from "react";
import type { Project } from "@/components/types";

interface EnhancedProjectTimelineProps {
	projects: Project[];
}

interface ProjectEvent {
	id: string;
	title: string;
	startDate: Date;
	endDate: Date;
	status: "active" | "completed" | "planning";
	budget: number;
	client: string;
	projectType: string;
}

const EnhancedProjectTimeline: React.FC<EnhancedProjectTimelineProps> = ({
	projects,
}) => {
	const [currentMonthOffset, setCurrentMonthOffset] = useState(0);
	const [selectedProject, setSelectedProject] = useState<ProjectEvent | null>(null);

	// Convert projects to timeline events
	const projectEvents = useMemo(() => {
		return projects.map((project): ProjectEvent => ({
			id: project.id,
			title: project.title,
			startDate: new Date(project.startDate),
			endDate: new Date(project.endDate),
			status: project.status,
			budget: project.budget,
			client: project.client,
			projectType: project.projectType,
		}));
	}, [projects]);

	// Generate months to display (3 months at a time)
	const displayMonths = useMemo(() => {
		const today = new Date();
		const months = [];
		
		for (let i = 0; i < 3; i++) {
			const monthDate = new Date(today.getFullYear(), today.getMonth() + currentMonthOffset + i, 1);
			months.push(monthDate);
		}
		
		return months;
	}, [currentMonthOffset]);

	// Check if a date has any project events
	const getProjectsForDate = (date: Date) => {
		return projectEvents.filter(project => {
			const projectStart = new Date(project.startDate);
			const projectEnd = new Date(project.endDate);
			
			// Reset time to compare only dates
			const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
			const startDate = new Date(projectStart.getFullYear(), projectStart.getMonth(), projectStart.getDate());
			const endDate = new Date(projectEnd.getFullYear(), projectEnd.getMonth(), projectEnd.getDate());
			
			return checkDate >= startDate && checkDate <= endDate;
		});
	};

	// Navigate months
	const goToPreviousMonths = () => {
		setCurrentMonthOffset(prev => prev - 3);
	};

	const goToNextMonths = () => {
		setCurrentMonthOffset(prev => prev + 3);
	};

	const goToCurrentMonth = () => {
		setCurrentMonthOffset(0);
	};

	// Get days in month
	const getDaysInMonth = (date: Date) => {
		return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
	};

	// Get first day of month (0 = Sunday, 1 = Monday, etc.)
	const getFirstDayOfMonth = (date: Date) => {
		return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
	};

	return (
		<div className="bg-gray-900 rounded p-8 mb-12">
			{/* Header */}
			<div className="flex justify-between items-center mb-6">
				<div className="flex items-center gap-4">
					<span className="text-white text-sm font-light tracking-wide">
						Project Timeline
					</span>
					<button
						onClick={goToCurrentMonth}
						className="text-xs text-gray-400 hover:text-white transition-colors px-2 py-1 rounded border border-gray-600 hover:border-gray-500"
					>
						Today
					</button>
				</div>
				<div className="flex items-center gap-6">
					<span className="flex items-center gap-2">
						<div className="w-2 h-2 rounded-full bg-blue-400"></div>
						<span className="text-gray-400 text-xs font-light tracking-wide">
							Active
						</span>
					</span>
					<span className="flex items-center gap-2">
						<div className="w-2 h-2 rounded-full bg-green-400"></div>
						<span className="text-gray-400 text-xs font-light tracking-wide">
							Completed
						</span>
					</span>
					<span className="flex items-center gap-2">
						<div className="w-2 h-2 rounded-full bg-yellow-400"></div>
						<span className="text-gray-400 text-xs font-light tracking-wide">
							Planning
						</span>
					</span>
				</div>
			</div>

			{/* Navigation */}
			<div className="flex justify-between items-center mb-6">
				<button
					onClick={goToPreviousMonths}
					className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
				>
					<ChevronLeft className="w-4 h-4" />
					<span className="text-sm">Previous</span>
				</button>
				
				<div className="text-sm text-gray-300">
					<span className="font-light">
						{new Date().getFullYear()}
					</span>
				</div>

				<button
					onClick={goToNextMonths}
					className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
				>
					<span className="text-sm">Next</span>
					<ChevronRight className="w-4 h-4" />
				</button>
			</div>

			{/* Calendar Grid */}
			<div className="grid grid-cols-3 gap-8">
				{displayMonths.map((monthDate) => {
					const monthName = monthDate.toLocaleDateString('en-US', { month: 'short' });
					const daysInMonth = getDaysInMonth(monthDate);
					const firstDayOfMonth = getFirstDayOfMonth(monthDate);

					return (
						<div key={`${monthDate.getFullYear()}-${monthDate.getMonth()}`}>
							<div className="text-center text-gray-400 text-xs mb-4 font-light tracking-wide">
								{monthName}
							</div>
							<div className="grid grid-cols-7 gap-1 mb-3">
								{["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
									<div
										key={day}
										className="text-center text-gray-500 text-xs font-light tracking-wide"
									>
										{day}
									</div>
								))}
							</div>
							<div className="grid grid-cols-7 gap-1">
								{/* Empty cells for days before month starts */}
								{Array.from({ length: firstDayOfMonth }).map((_, i) => (
									<div key={`empty-${i}`} className="w-6 h-6" />
								))}
								
								{/* Days of the month */}
								{Array.from({ length: daysInMonth }).map((_, i) => {
									const dayNumber = i + 1;
									const currentDate = new Date(monthDate.getFullYear(), monthDate.getMonth(), dayNumber);
									const projectsOnDate = getProjectsForDate(currentDate);
									const hasProjects = projectsOnDate.length > 0;
									
									// Determine the primary status for the day
									const hasActive = projectsOnDate.some(p => p.status === "active");
									const hasCompleted = projectsOnDate.some(p => p.status === "completed");
									const hasPlanning = projectsOnDate.some(p => p.status === "planning");
									
									let dayClass = "bg-gray-800 hover:bg-gray-700 text-gray-400";
									if (hasProjects) {
										if (hasActive) {
											dayClass = "bg-blue-900 hover:bg-blue-800 text-blue-200";
										} else if (hasCompleted) {
											dayClass = "bg-green-900 hover:bg-green-800 text-green-200";
										} else if (hasPlanning) {
											dayClass = "bg-yellow-900 hover:bg-yellow-800 text-yellow-200";
										}
									}

									return (
										<div
											key={`day-${dayNumber}`}
											className={`w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light cursor-pointer transition-colors ${dayClass}`}
											title={hasProjects ? 
												`${projectsOnDate.length} project(s): ${projectsOnDate.map(p => p.title).join(', ')}` : 
												`${monthName} ${dayNumber}: Available`
											}
											onClick={() => {
												if (projectsOnDate.length > 0) {
													setSelectedProject(projectsOnDate[0]);
												}
											}}
										>
											{dayNumber}
										</div>
									);
								})}
							</div>
						</div>
					);
				})}
			</div>

			{/* Project Details Modal/Popup */}
			{selectedProject && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
						<div className="flex justify-between items-start mb-4">
							<h3 className="text-lg font-semibold text-gray-900">{selectedProject.title}</h3>
							<button
								onClick={() => setSelectedProject(null)}
								className="text-gray-400 hover:text-gray-600"
							>
								×
							</button>
						</div>
						<div className="space-y-2 text-sm text-gray-600">
							<p><span className="font-medium">Client:</span> {selectedProject.client}</p>
							<p><span className="font-medium">Type:</span> {selectedProject.projectType}</p>
							<p><span className="font-medium">Budget:</span> ${selectedProject.budget.toLocaleString()}</p>
							<p><span className="font-medium">Status:</span> 
								<span className={`ml-1 px-2 py-1 rounded text-xs ${
									selectedProject.status === 'active' ? 'bg-blue-100 text-blue-800' :
									selectedProject.status === 'completed' ? 'bg-green-100 text-green-800' :
									'bg-yellow-100 text-yellow-800'
								}`}>
									{selectedProject.status}
								</span>
							</p>
							<p><span className="font-medium">Duration:</span> {selectedProject.startDate.toLocaleDateString()} - {selectedProject.endDate.toLocaleDateString()}</p>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default EnhancedProjectTimeline;
