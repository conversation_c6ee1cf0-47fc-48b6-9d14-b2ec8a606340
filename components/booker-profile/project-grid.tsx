import type { Project } from "@/components/types";
import Link from "next/link";
import type React from "react";

interface ProjectGridProps {
	projects: Project[];
	// onProjectClick is removed as we will link directly to the project page.
}

const ProjectGrid: React.FC<ProjectGridProps> = ({ projects }) => {
	return (
		<div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
			{projects.map((project) => (
				<Link key={project.id} href={`/booker/project/${project.id}`} passHref>
					<div className="relative group cursor-pointer aspect-[4/5] bg-gray-100 rounded-lg overflow-hidden border border-gray-200">
						{/* Use a placeholder or a moodboard image if available */}
						<div className="w-full h-full bg-gray-200 flex items-center justify-center">
							<span className="text-gray-500 font-light">
								{project.title.charAt(0)}
							</span>
						</div>

						{/* Project Info Overlay */}
						<div className="absolute bottom-0 left-0 right-0 bg-white/95 backdrop-blur-sm p-4 border-t border-gray-100">
							<h3 className="font-light text-base tracking-wide mb-1 truncate">
								{project.title}
							</h3>
							<p className="text-sm text-gray-600 mb-2 truncate font-light">
								{project.client}
							</p>
							<div className="flex items-center justify-between text-sm text-gray-500">
								<span className="truncate font-light">
									{project.projectType || "Creative Project"}
								</span>
								<span className="font-semibold text-black">
									${(project.budget / 1000).toFixed(0)}K
								</span>
							</div>
						</div>

						{/* Status Badge */}
						<div className="absolute top-3 right-3 z-10">
							<div
								className={`px-2 py-1 text-xs font-light tracking-wide rounded-full shadow-md ${
									project.status === "active"
										? "bg-blue-100 text-blue-800"
										: project.status === "completed"
											? "bg-green-100 text-green-800"
											: "bg-yellow-100 text-yellow-800"
								}`}
							>
								{project.status.charAt(0).toUpperCase() +
									project.status.slice(1)}
							</div>
						</div>

						{/* Hover Effect */}
						<div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />
					</div>
				</Link>
			))}
		</div>
	);
};

export default ProjectGrid;
