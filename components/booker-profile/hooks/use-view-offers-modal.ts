import type { Offer } from "@/components/types";
import { useCallback, useState } from "react";

export const useViewOffersModal = () => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [selectedRoleOffers, setSelectedRoleOffers] = useState<Offer[]>([]);
	const [selectedRoleName, setSelectedRoleName] = useState("");

	const openModal = useCallback((role: string, offersForRole: Offer[]) => {
		setSelectedRoleName(role);
		setSelectedRoleOffers(offersForRole);
		setIsModalOpen(true);
	}, []);

	const closeModal = useCallback(() => {
		setIsModalOpen(false);
		setSelectedRoleName("");
		setSelectedRoleOffers([]);
	}, []);

	return {
		isModalOpen,
		selectedRoleOffers,
		selectedRoleName,
		openModal,
		closeModal,
	};
};
