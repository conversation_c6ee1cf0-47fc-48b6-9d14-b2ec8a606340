import { useCallback, useState } from "react";

export const useFindCreativesModal = () => {
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [selectedRole, setSelectedRole] = useState<string | null>(null);

	const openModal = useCallback((role: string) => {
		setSelectedRole(role);
		setIsModalOpen(true);
	}, []);

	const closeModal = useCallback(() => {
		setIsModalOpen(false);
		setSelectedRole(null);
	}, []);

	return {
		isModalOpen,
		selectedRole,
		openModal,
		closeModal,
	};
};
