import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import type React from "react";

export type Tab =
	| "projects"
	| "offers"
	| "budget"
	| "invoices"
	| "collaborations"
	| "archive"
	| "members";

export interface ProfileTabsProps {
	activeTab: Tab;
	onTabChange: (tab: Tab) => void;
	projectCount: number;
	offersSentCount: number;
	totalBudgetFormatted: string;
	invoicesCount: number;
	collaborationsCount: number;
	archiveCount: number;
	membersCount: number;
	onOpenAddProjectModal: () => void;
}

const ProfileTabs: React.FC<ProfileTabsProps> = ({
	activeTab,
	onTabChange,
	projectCount,
	offersSentCount,
	totalBudgetFormatted,
	invoicesCount,
	collaborationsCount,
	archiveCount,
	membersCount,
	onOpenAddProjectModal,
}) => {
	return (
		<div className="border-b border-gray-100">
			<div className="max-w-4xl mx-auto px-6 py-8">
				<div className="flex items-center justify-center gap-8 mb-8">
					<button
						onClick={() => onTabChange("projects")}
						className={`text-sm font-light tracking-wide ${
							activeTab === "projects" ? "text-black" : "text-gray-400"
						}`}
					>
						Projects ({projectCount})
					</button>
					<button
						onClick={() => onTabChange("offers")}
						className={`text-sm font-light tracking-wide ${activeTab === "offers" ? "text-black" : "text-gray-400"}`}
					>
						Offers Sent ({offersSentCount})
					</button>
					<button
						onClick={() => onTabChange("budget")}
						className={`text-sm font-light tracking-wide ${activeTab === "budget" ? "text-black" : "text-gray-400"}`}
					>
						Budget ({totalBudgetFormatted})
					</button>
					<button
						onClick={() => onTabChange("invoices")}
						className={`text-sm font-light tracking-wide ${activeTab === "invoices" ? "text-black" : "text-gray-400"}`}
					>
						Invoices ({invoicesCount})
					</button>
					<button
						onClick={() => onTabChange("collaborations")}
						className={`text-sm font-light tracking-wide ${activeTab === "collaborations" ? "text-black" : "text-gray-400"}`}
					>
						Collaborations ({collaborationsCount})
					</button>
					<button
						onClick={() => onTabChange("archive")}
						className={`text-sm font-light tracking-wide ${activeTab === "archive" ? "text-black" : "text-gray-400"}`}
					>
						Archive ({archiveCount})
					</button>
					<button
						onClick={() => onTabChange("members")}
						className={`text-sm font-light tracking-wide ${activeTab === "members" ? "text-black" : "text-gray-400"}`}
					>
						Members ({membersCount})
					</button>
				</div>

				{/* Controls - shown only when projects tab is active */}
				{activeTab === "projects" && (
					<div className="flex items-center justify-center gap-4">
						<Button
							variant="outline"
							size="sm"
							className="text-xs font-light tracking-wide"
							onClick={onOpenAddProjectModal}
						>
							<Plus className="w-4 h-4 mr-2" />
							Add Project
						</Button>
					</div>
				)}
			</div>
		</div>
	);
};

export default ProfileTabs;
