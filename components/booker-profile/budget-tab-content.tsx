import { format } from "date-fns"; // For formatting dates
import type React from "react";

// Define example structures for props, adjust if your actual data differs
interface ProjectBudget {
	id: string;
	name: string;
	allocatedBudget: number;
	status?: string;
}

interface Transaction {
	id: string;
	date: string | Date;
	description: string;
	amount: number;
	type?: "deposit" | "payment" | "fee" | "refund" | string; // Allow other string types too
	creativeName?: string;
	projectName?: string;
}

interface BudgetTabContentProps {
	totalBudget?: number;
	depositedBudget?: number;
	budgetLeft?: number;
	projects?: ProjectBudget[];
	transactions?: Transaction[];
}

const BudgetTabContent: React.FC<BudgetTabContentProps> = ({
	totalBudget = 0,
	depositedBudget = 0,
	budgetLeft = 0,
	projects = [], // Default to empty array
	transactions = [], // Default to empty array
}) => {
	const getTransactionAmountColor = (type?: string) => {
		switch (type) {
			case "deposit":
				return "text-green-600";
			case "payment":
			case "fee":
				return "text-red-600";
			case "refund":
				return "text-blue-600";
			default:
				return "text-gray-800";
		}
	};

	const formatTransactionDescription = (transaction: Transaction): string => {
		if (transaction.type === "payment" && transaction.creativeName) {
			let desc = `Paid to: ${transaction.creativeName}`;
			if (transaction.projectName) {
				desc += ` for ${transaction.projectName}`;
			}
			return desc;
		}
		return transaction.description;
	};

	return (
		<div className="py-8">
			{/* Summary Section */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center mb-16 px-4">
				<div>
					<h2 className="text-lg font-light tracking-wide text-gray-500 mb-3 uppercase">
						Deposited Budget
					</h2>
					<div className="text-4xl font-light text-green-600 mb-2">
						${(depositedBudget || 0).toLocaleString()}
					</div>
				</div>
				<div>
					<h2 className="text-lg font-light tracking-wide text-gray-500 mb-3 uppercase">
						Total Allocated
					</h2>
					<div className="text-4xl font-light text-blue-600 mb-2">
						${(totalBudget || 0).toLocaleString()}
					</div>
					<p className="text-gray-500 text-sm font-light tracking-wide">
						Across All Projects
					</p>
				</div>
				<div>
					<h2 className="text-lg font-light tracking-wide text-gray-500 mb-3 uppercase">
						Budget Left
					</h2>
					<div className="text-4xl font-light text-orange-500 mb-2">
						${(budgetLeft || 0).toLocaleString()}
					</div>
				</div>
			</div>

			{/* Project Allocations Section */}
			{projects.length > 0 && (
				<div className="mb-16 px-4">
					<h3 className="text-2xl font-light tracking-wide mb-6 text-center text-gray-700">
						Project Allocations
					</h3>
					<div className="bg-white shadow overflow-hidden rounded-lg">
						<ul className="divide-y divide-gray-200">
							{projects.map((project) => (
								<li
									key={project.id}
									className="px-6 py-4 hover:bg-gray-50 transition-colors"
								>
									<div className="flex items-center justify-between">
										<div>
											<p className="text-md font-medium text-gray-800 truncate">
												{project.name}
											</p>
											{project.status && (
												<p className="text-xs text-gray-500">
													Status: {project.status}
												</p>
											)}
										</div>
										<div className="text-md text-blue-600 font-light">
											${project.allocatedBudget.toLocaleString()}
										</div>
									</div>
								</li>
							))}
						</ul>
					</div>
				</div>
			)}

			{/* Transaction History Section */}
			{transactions.length > 0 && (
				<div className="px-4 mt-16">
					<h3 className="text-2xl font-light tracking-wide mb-6 text-center text-gray-700">
						Payment History
					</h3>
					<div className="bg-white shadow overflow-hidden rounded-lg">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Date
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Details
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Amount
									</th>
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{transactions.map((transaction) => (
									<tr
										key={transaction.id}
										className="hover:bg-gray-50 transition-colors"
									>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{format(new Date(transaction.date), "MMM dd, yyyy")}
										</td>
										<td className="px-6 py-4 text-sm text-gray-800">
											{formatTransactionDescription(transaction)}
										</td>
										<td
											className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getTransactionAmountColor(transaction.type)}`}
										>
											{transaction.type === "deposit" ||
											transaction.type === "refund"
												? "+"
												: "-"}
											${Math.abs(transaction.amount).toLocaleString()}
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			)}

			{projects.length === 0 && transactions.length === 0 && (
				<div className="text-center text-gray-400 font-light mt-12 px-4">
					<p>No project allocations or transaction history to display.</p>
				</div>
			)}
		</div>
	);
};

export default BudgetTabContent;
