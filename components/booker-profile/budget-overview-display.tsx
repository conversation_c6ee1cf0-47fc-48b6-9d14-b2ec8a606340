import type React from "react";
import type { Project } from "@/components/types";
import EnhancedProjectTimeline from "./enhanced-project-timeline";

interface BudgetOverviewDisplayProps {
	activeTab: "projects" | "offers" | "budget" | "invoices" | "collaborations" | "archive" | "members"; // To decide which view to show
	projects?: Project[]; // Add projects data for the enhanced timeline
}

// Define interface for Member Activity
interface MemberActivity {
	id: string;
	userName: string;
	userAvatar?: string; // Optional: if you want to show avatars next to activity
	action: string; // e.g., "invited", "updated project", "viewed project"
	target?: string; // e.g., "Bob The Builder", "Project X", "Project Y"
	timestamp: string; // e.g., "2 hours ago", "1 day ago"
	status: "active" | "completed"; // Added for coloring
}

// Mock Member Activity Data
const mockActivities: MemberActivity[] = [
	{
		id: "act1",
		userName: "Alice Wonderland",
		action: "invited",
		target: "Bob The Builder",
		timestamp: "2 hours ago",
		status: "active", // Invitations are ongoing
	},
	{
		id: "act2",
		userName: "Bob The Builder",
		action: "updated settings for",
		target: "Spring Fashion Campaign",
		timestamp: "1 day ago",
		status: "completed",
	},
	{
		id: "act3",
		userName: "Charlie Chaplin",
		action: "viewed",
		target: "Product Launch Event",
		timestamp: "3 days ago",
		status: "completed",
	},
	{
		id: "act4",
		userName: "Alice Wonderland",
		action: "changed permission for",
		target: "Diana Prince to Viewer",
		timestamp: "5 days ago",
		status: "completed",
	},
];

const BudgetOverviewDisplay: React.FC<BudgetOverviewDisplayProps> = ({
	activeTab,
	projects = [],
}) => {
	return (
		<div className="bg-gray-900 rounded p-8 mb-12">
			<div className="flex justify-between items-center mb-6">
				<span className="text-white text-sm font-light tracking-wide">
					{activeTab === "budget"
						? "Budget Breakdown"
						: activeTab === "members"
							? "Latest Member Activity"
							: activeTab === "invoices"
								? "Invoice & Payment Overview"
								: "Project Timeline"}
				</span>
				{activeTab !== "members" && (
					<div className="flex items-center gap-6">
						<span className="flex items-center gap-2">
							<div className="w-2 h-2 rounded-full bg-blue-400"></div>
							<span className="text-gray-400 text-xs font-light tracking-wide">
								Active
							</span>
						</span>
						<span className="flex items-center gap-2">
							<div className="w-2 h-2 rounded-full bg-green-400"></div>
							<span className="text-gray-400 text-xs font-light tracking-wide">
								Completed
							</span>
						</span>
					</div>
				)}
			</div>

			{activeTab === "budget" ? (
				/* Budget Bar Graph */
				<div className="h-48 relative">
					<svg className="w-full h-full" viewBox="0 0 400 200">
						<defs>
							<pattern
								id="grid"
								width="40"
								height="20"
								patternUnits="userSpaceOnUse"
							>
								<path
									d="M 40 0 L 0 0 0 20"
									fill="none"
									stroke="#374151"
									strokeWidth="0.5"
									opacity="0.3"
								/>
							</pattern>
						</defs>
						<rect width="100%" height="100%" fill="url(#grid)" />
						<line
							x1="20"
							y1="180"
							x2="380"
							y2="180"
							stroke="#4b5563"
							strokeWidth="1"
						/>
						<line
							x1="20"
							y1="20"
							x2="20"
							y2="180"
							stroke="#4b5563"
							strokeWidth="1"
						/>
						{[
							{
								x: 40,
								width: 20,
								height: 70,
								amount: 3500,
								category: "Photography",
							},
							{
								x: 80,
								width: 20,
								height: 56,
								amount: 2800,
								category: "Videography",
							},
							{
								x: 120,
								width: 20,
								height: 24,
								amount: 1200,
								category: "Makeup",
							},
							{
								x: 160,
								width: 20,
								height: 80,
								amount: 4000,
								category: "Modeling",
							},
							{
								x: 200,
								width: 20,
								height: 60,
								amount: 3000,
								category: "Styling",
							},
							{
								x: 240,
								width: 20,
								height: 40,
								amount: 2000,
								category: "Location",
							},
							{
								x: 280,
								width: 20,
								height: 100,
								amount: 5000,
								category: "Equipment",
							},
							{
								x: 320,
								width: 20,
								height: 32,
								amount: 1600,
								category: "Catering",
							},
						].map((bar, index) => (
							<g key={index} className="group">
								<rect
									x={bar.x}
									y={180 - bar.height}
									width={bar.width}
									height={bar.height}
									fill="#3b82f6"
									className="hover:fill-blue-600 transition-colors cursor-pointer"
								>
									<title>
										${bar.amount.toLocaleString()} - {bar.category}
									</title>
								</rect>
								<text
									x={bar.x + bar.width / 2}
									y={175 - bar.height}
									fill="#1e40af"
									fontSize="10"
									textAnchor="middle"
									className="opacity-0 group-hover:opacity-100 transition-opacity"
								>
									${bar.amount}
								</text>
							</g>
						))}
						<text x="15" y="30" fill="#9ca3af" fontSize="10" textAnchor="end">
							$5K
						</text>
						<text x="15" y="80" fill="#9ca3af" fontSize="10" textAnchor="end">
							$4K
						</text>
						<text x="15" y="130" fill="#9ca3af" fontSize="10" textAnchor="end">
							$2K
						</text>
						<text x="15" y="180" fill="#9ca3af" fontSize="10" textAnchor="end">
							$0
						</text>
						<text
							x="50"
							y="195"
							fill="#9ca3af"
							fontSize="8"
							textAnchor="middle"
						>
							Photo
						</text>
						<text
							x="130"
							y="195"
							fill="#9ca3af"
							fontSize="8"
							textAnchor="middle"
						>
							Makeup
						</text>
						<text
							x="210"
							y="195"
							fill="#9ca3af"
							fontSize="8"
							textAnchor="middle"
						>
							Styling
						</text>
						<text
							x="290"
							y="195"
							fill="#9ca3af"
							fontSize="8"
							textAnchor="middle"
						>
							Equipment
						</text>
					</svg>
				</div>
			) : activeTab === "members" ? (
				/* Latest Member Activity */
				<div className="h-auto min-h-48 relative text-gray-300 p-4 text-sm font-light">
					{mockActivities.length > 0 ? (
						<ul className="space-y-3">
							{mockActivities.map((activity) => (
								<li key={activity.id} className="flex items-start">
									<span className="text-xs text-gray-500 whitespace-nowrap mr-4 w-24 text-right">
										{activity.timestamp}
									</span>
									<div
										className={`flex-grow ${activity.status === "active" ? "text-blue-400" : "text-green-400"}`}
									>
										<span className="font-medium">{activity.userName}</span>
										<span
											className={`${activity.status === "active" ? "text-blue-300" : "text-green-300"} opacity-80`}
										>
											{" "}
											{activity.action}{" "}
										</span>
										{activity.target && (
											<span
												className={`font-normal ${activity.status === "active" ? "text-blue-400" : "text-green-400"}`}
											>
												{activity.target}
											</span>
										)}
									</div>
								</li>
							))}
						</ul>
					) : (
						<p className="text-center text-gray-400">
							No recent member activity.
						</p>
					)}
				</div>
			) : activeTab === "projects" ? (
				/* Enhanced Project Timeline */
				<EnhancedProjectTimeline projects={projects} />
			) : activeTab === "invoices" ? (
				/* Invoice Overview */
				<div className="h-48 relative flex items-center justify-center">
					<div className="text-center text-gray-400">
						<div className="text-lg font-light mb-2">Invoice Management</div>
						<div className="text-sm">View and manage all project invoices and payments below</div>
					</div>
				</div>
			) : (
				/* Default Project Timeline for other tabs */
				<div className="grid grid-cols-3 gap-8">
					{["Jan", "Feb", "Mar"].map((month, monthIndex) => (
						<div key={month}>
							<div className="text-center text-gray-400 text-xs mb-4 font-light tracking-wide">
								{month}
							</div>
							<div className="grid grid-cols-7 gap-1 mb-3">
								{["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map(
									(day) => (
										<div
											key={day}
											className="text-center text-gray-500 text-xs font-light tracking-wide"
										>
											{day}
										</div>
									),
								)}
							</div>
							<div className="grid grid-cols-7 gap-1">
								{[...Array(31)].map((_, i) => {
									const hasProject = Math.random() > 0.8; // This should be data-driven
									const isCompleted = Math.random() > 0.6; // This should be data-driven
									return (
										<div
											key={`${month}-day-${i + 1}`}
											className={`w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ${
												hasProject
													? isCompleted
														? "bg-green-900 hover:bg-green-800 text-green-200"
														: "bg-blue-900 hover:bg-blue-800 text-blue-200"
													: "bg-gray-800 hover:bg-gray-700 text-gray-400"
											} cursor-pointer transition-colors`}
											title={`${month} ${i + 1}: ${hasProject ? (isCompleted ? "Completed" : "Active") : "Available"}`}
										>
											{i + 1}
										</div>
									);
								})}
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default BudgetOverviewDisplay;
