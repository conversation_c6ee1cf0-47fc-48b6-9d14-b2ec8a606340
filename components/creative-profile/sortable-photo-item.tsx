import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Scissors, GripVertical } from "lucide-react";
import Image from "next/image";
import type React from "react";
import type { Photo } from "../../instagram-photo-grid-data";
import { getGridClasses } from "../utils/grid-utils";

// Function to enhance video URLs with playback controls
const enhanceVideoUrl = (url: string): string => {
	try {
		const urlObj = new URL(url);

		// YouTube enhancements
		if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
			// Add autoplay and loop without controls
			urlObj.searchParams.set('autoplay', '1');
			urlObj.searchParams.set('loop', '1');
			urlObj.searchParams.set('controls', '0');
			urlObj.searchParams.set('modestbranding', '1');
			urlObj.searchParams.set('rel', '0');
			urlObj.searchParams.set('mute', '1'); // Mute for autoplay to work

			// For looping to work on YouTube, we need the playlist parameter
			const videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
			if (videoId) {
				urlObj.searchParams.set('playlist', videoId);
			}
		}

		// Vimeo enhancements
		else if (urlObj.hostname.includes('vimeo.com')) {
			urlObj.searchParams.set('autoplay', '1');
			urlObj.searchParams.set('loop', '1');
			urlObj.searchParams.set('controls', '0');
			urlObj.searchParams.set('muted', '1');
		}

		// Generic video enhancements for other platforms
		else {
			urlObj.searchParams.set('autoplay', '1');
			urlObj.searchParams.set('controls', '0');
		}

		return urlObj.toString();
	} catch (error) {
		// If URL parsing fails, return original URL
		console.warn('Failed to enhance video URL:', error);
		return url;
	}
};

interface SortablePhotoItemProps {
	photo: Photo;
	selectedTiles: Set<string>;
	isSelectionMode: boolean;
	editingTile: string | null;
	tempText: string;
	hasEditPermission?: boolean;
	onOpenPhoto: (photo: Photo) => void;
	onToggleTileSelection: (photoId: string) => void;
	onStartEditingText: (photoId: string, photo: Photo) => void;
	onSetTempText: (text: string) => void;
	onSaveTextEdit: () => void;
	onCancelTextEdit: () => void;
	splitTile: (photo: Photo) => void;
	tempBackgroundColor: string;
	onSetTempBackgroundColor: (color: string) => void;
	tempFontSize: string;
	onSetTempFontSize: (size: string) => void;
	tempFontFamily: string;
	onSetTempFontFamily: (family: string) => void;
	isViewMode?: boolean;
}

const SortablePhotoItem: React.FC<SortablePhotoItemProps> = (props) => {
	const {
		photo,
		selectedTiles,
		isSelectionMode,
		editingTile,
		tempText,
		hasEditPermission = true,
		onOpenPhoto,
		onToggleTileSelection,
		onStartEditingText,
		onSetTempText,
		onSaveTextEdit,
		onCancelTextEdit,
		splitTile,
		tempBackgroundColor,
		onSetTempBackgroundColor,
		tempFontSize,
		onSetTempFontSize,
		tempFontFamily,
		onSetTempFontFamily,
		isViewMode = false,
	} = props;

	const { attributes, listeners, setNodeRef, transform, transition } =
		useSortable({ id: photo.id, disabled: !isSelectionMode || !hasEditPermission });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
	};

	const fontFamilies = [
		"Arial",
		"Verdana",
		"Georgia",
		"Times New Roman",
		"Courier New",
	];
	const fontSizes = ["12px", "16px", "20px", "24px", "32px"];

	return (
		<div
			ref={setNodeRef}
			style={style}
			{...attributes}
			{...listeners}
			className={`relative group ${getGridClasses(photo.size)} ${
				selectedTiles.has(photo.id) ? "ring-2 ring-black" : ""
			} ${isSelectionMode && hasEditPermission ? "hover:ring-1 hover:ring-gray-400 cursor-grab" : "cursor-pointer"}`}
			onClick={() => {
				if (isSelectionMode && hasEditPermission) {
					onToggleTileSelection(photo.id);
				} else if ((photo.type === "text" || photo.type === "iframe") && editingTile !== photo.id && hasEditPermission && !isViewMode) {
					onStartEditingText(photo.id, photo);
				} else {
					onOpenPhoto(photo);
				}
			}}
		>
			{/* The rest of the rendering logic from PhotoGridPanel goes here */}
			{photo.type === "text" ? (
				<div
					className="w-full h-full flex items-center justify-center p-4 text-center relative"
					style={{
						backgroundColor:
							editingTile === photo.id
								? tempBackgroundColor
								: photo.backgroundColor || "#f3f4f6",
					}}
				>
					{editingTile === photo.id && hasEditPermission ? (
						<div
							className="w-full h-full flex flex-col"
							onClick={(e) => e.stopPropagation()}
						>
							<textarea
								value={tempText}
								onChange={(e) => onSetTempText(e.target.value)}
								className="flex-1 w-full p-2 border-none outline-none resize-none bg-transparent text-center font-light"
								placeholder="Enter your text..."
								autoFocus
								onClick={(e) => e.stopPropagation()}
								style={{
									backgroundColor: tempBackgroundColor,
									fontSize: tempFontSize,
									fontFamily: tempFontFamily,
									color: "#000000",
								}}
							/>
							<div className="grid grid-cols-3 gap-2 mt-2">
								<div>
									<label
										htmlFor={`bgColor-${photo.id}`}
										className="block text-xs font-light text-gray-600 mb-1"
									>
										BG
									</label>
									<input
										type="color"
										id={`bgColor-${photo.id}`}
										value={tempBackgroundColor}
										onChange={(e) => onSetTempBackgroundColor(e.target.value)}
										className="w-full h-8 p-0 border-none rounded cursor-pointer"
									/>
								</div>
								<div>
									<label
										htmlFor={`fontSize-${photo.id}`}
										className="block text-xs font-light text-gray-600 mb-1"
									>
										Size
									</label>
									<select
										id={`fontSize-${photo.id}`}
										value={tempFontSize}
										onChange={(e) => onSetTempFontSize(e.target.value)}
										className="w-full p-1 border border-gray-300 rounded text-xs font-light h-8"
									>
										{fontSizes.map((size) => (
											<option key={size} value={size}>
												{size}
											</option>
										))}
									</select>
								</div>
								<div>
									<label
										htmlFor={`fontFamily-${photo.id}`}
										className="block text-xs font-light text-gray-600 mb-1"
									>
										Font
									</label>
									<select
										id={`fontFamily-${photo.id}`}
										value={tempFontFamily}
										onChange={(e) => onSetTempFontFamily(e.target.value)}
										className="w-full p-1 border border-gray-300 rounded text-xs font-light h-8 truncate"
									>
										{fontFamilies.map((family) => (
											<option key={family} value={family}>
												{family}
											</option>
										))}
									</select>
								</div>
							</div>
							<div className="flex gap-1 mt-2">
								<button
									onClick={(e) => {
										e.stopPropagation();
										onSaveTextEdit();
									}}
									className="px-2 py-1 bg-black text-white text-xs rounded font-light"
								>
									Save
								</button>
								<button
									onClick={(e) => {
										e.stopPropagation();
										onCancelTextEdit();
									}}
									className="px-2 py-1 bg-gray-500 text-white text-xs rounded font-light"
								>
									Cancel
								</button>
							</div>
						</div>
					) : (
						<p
							className={`text-gray-800 font-light break-words z-20 relative whitespace-pre-line ${hasEditPermission && !isViewMode ? "cursor-text" : "cursor-default"}`}
							style={{
								backgroundColor: photo.backgroundColor || "#f3f4f6",
								fontSize: photo.fontSize || "16px",
								fontFamily: photo.fontFamily || "Arial",
								color: "#000000",
							}}
							onClick={(e) => {
								e.stopPropagation();
								if (hasEditPermission && !isViewMode) {
									onStartEditingText(photo.id, photo);
								}
							}}
						>
							{photo.textContent || "Click to edit text"}
						</p>
					)}
				</div>
			) : photo.type === "iframe" ? (
				<div className="w-full h-full relative">
					{editingTile === photo.id && hasEditPermission ? (
						<div
							className="w-full h-full flex flex-col p-4"
							onClick={(e) => e.stopPropagation()}
						>
							<label className="block text-xs font-light text-gray-600 mb-2">
								Embed URL or iframe code
							</label>
							<textarea
								value={tempText}
								onChange={(e) => {
									const input = e.target.value;

									// Extract URL from iframe HTML if it looks like iframe code
									const extractUrlFromIframe = (input: string): string => {
										const trimmedInput = input.trim();

										// If it's already a URL, return as-is
										if (trimmedInput.startsWith('http')) {
											return trimmedInput;
										}

										// Extract URL from iframe HTML
										const srcMatch = trimmedInput.match(/src=["']([^"']+)["']/);
										if (srcMatch) {
											// Decode HTML entities
											return srcMatch[1].replace(/&amp;/g, '&');
										}

										return trimmedInput;
									};

									// If input contains iframe tags, extract the URL, otherwise use as-is
									const processedValue = input.includes('<iframe') ? extractUrlFromIframe(input) : input;
									onSetTempText(processedValue);
								}}
								className="flex-1 w-full p-2 border border-gray-300 rounded text-sm resize-none"
								placeholder="https://www.youtube.com/embed/... or paste full iframe code"
								autoFocus
								onClick={(e) => e.stopPropagation()}
								rows={2}
							/>
							<div className="text-xs text-gray-500 mt-1 mb-2">
								💡 Videos will auto-play muted and loop continuously
							</div>
							<div className="flex gap-1 mt-2">
								<button
									onClick={(e) => {
										e.stopPropagation();
										onSaveTextEdit();
									}}
									className="px-2 py-1 bg-black text-white text-xs rounded font-light"
								>
									Save
								</button>
								<button
									onClick={(e) => {
										e.stopPropagation();
										onCancelTextEdit();
									}}
									className="px-2 py-1 bg-gray-500 text-white text-xs rounded font-light"
								>
									Cancel
								</button>
							</div>
						</div>
					) : (
						<div
							className="w-full h-full relative cursor-pointer"
							onClick={(e) => {
								e.stopPropagation();
								if (hasEditPermission && !isViewMode) {
									onStartEditingText(photo.id, photo);
								}
							}}
						>
							{photo.iframeUrl ? (
								<iframe
									src={enhanceVideoUrl(photo.iframeUrl.replace(/&amp;/g, '&'))}
									title={photo.alt}
									className="w-full h-full border-0 pointer-events-auto"
									allowFullScreen
									loading="lazy"
									allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
									referrerPolicy="strict-origin-when-cross-origin"
								/>
							) : (
								<div className="w-full h-full bg-gray-100 flex items-center justify-center border-2 border-dashed border-gray-300">
									<div className="text-center text-gray-500">
										<div className="text-2xl mb-2">🎬</div>
										<div className="text-sm">Iframe Content</div>
										<div className="text-xs mt-1">Click to add URL</div>
									</div>
								</div>
							)}
							{hasEditPermission && !isViewMode && (
								<div className="absolute inset-0 bg-transparent hover:bg-black hover:bg-opacity-10 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
									<span className="bg-black bg-opacity-75 text-white px-2 py-1 rounded text-xs">
										Click to edit
									</span>
								</div>
							)}
						</div>
					)}
				</div>
			) : (
				<Image
					src={photo.src || "/placeholder.svg"}
					alt={photo.alt}
					fill
					className="object-cover"
					sizes="(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw"
				/>
			)}
			<div className="absolute top-2 left-2 flex gap-1 z-20">
				{photo.isMerged && hasEditPermission && (
					<div
						className="bg-black text-white rounded-full p-1 cursor-pointer"
						onClick={(e) => {
							e.stopPropagation();
							splitTile(photo);
						}}
					>
						<Scissors className="w-3 h-3" />
					</div>
				)}
				{photo.type === "text" && isSelectionMode && (
					<div
						className="bg-black text-white rounded-full p-1"
						title="Text Tile"
					>
						<span className="text-xs font-light">T</span>
					</div>
				)}
				{photo.type === "iframe" && isSelectionMode && (
					<div
						className="bg-blue-600 text-white rounded-full p-1"
						title="Iframe Tile"
					>
						<span className="text-xs font-light">I</span>
					</div>
				)}
			</div>

			{/* Drag handle - only visible in edit mode */}
			{isSelectionMode && hasEditPermission && (
				<div className="absolute top-2 right-2 z-20">
					<div
						className="bg-black bg-opacity-50 text-white rounded p-1 opacity-0 group-hover:opacity-100 transition-opacity"
						title="Drag to reorder"
					>
						<GripVertical className="w-3 h-3" />
					</div>
				</div>
			)}
		</div>
	);
};

export default SortablePhotoItem;
