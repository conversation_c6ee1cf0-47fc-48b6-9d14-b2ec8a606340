import type React from "react";
import type { Payment } from "../../instagram-photo-grid-data"; // Adjust path

interface ArchivePanelProps {
	payments: Payment[]; // Assuming archive items are based on payments data
}

const ArchivePanel: React.FC<ArchivePanelProps> = ({ payments }) => {
	return (
		<div className="space-y-6">
			<h2 className="text-xl font-light tracking-wide text-center mb-12">
				Completed Work
			</h2>
			{payments.map((payment) => (
				<div
					key={payment.id}
					className="border border-gray-100 p-6 hover:shadow-sm transition-shadow"
				>
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<h3 className="font-light tracking-wide text-lg mb-2">
								{payment.jobTitle}
							</h3>
							<p className="text-gray-600 mb-2 font-light tracking-wide">
								Client: {payment.client}
							</p>
							<div className="text-sm text-gray-500 font-light tracking-wide">
								{payment.date}
							</div>
						</div>
						<div className="text-right">
							<span
								className={`px-3 py-1 text-xs font-light tracking-wide ${
									payment.status === "paid"
										? "bg-green-100 text-green-800"
										: "bg-yellow-100 text-yellow-800"
								}`}
							>
								{payment.status.charAt(0).toUpperCase() +
									payment.status.slice(1)}
							</span>
						</div>
					</div>
				</div>
			))}
		</div>
	);
};

export default ArchivePanel;
