import type React from "react";
import type { Photo } from "../../instagram-photo-grid-data";
import SortablePhotoItem from "./sortable-photo-item";

interface PhotoGridPanelProps {
	photos: Photo[];
	selectedTiles: Set<string>;
	isSelectionMode: boolean;
	editingTile: string | null;
	tempText: string;
	hasEditPermission?: boolean;
	onOpenPhoto: (photo: Photo) => void;
	onToggleTileSelection: (photoId: string) => void;
	onStartEditingText: (photoId: string, photo: Photo) => void;
	onSetTempText: (text: string) => void;
	onSaveTextEdit: () => void;
	onCancelTextEdit: () => void;
	splitTile: (photo: Photo) => void;
	tempBackgroundColor: string;
	onSetTempBackgroundColor: (color: string) => void;
	tempFontSize: string;
	onSetTempFontSize: (size: string) => void;
	tempFontFamily: string;
	onSetTempFontFamily: (family: string) => void;
	isViewMode?: boolean;
}

const PhotoGridPanel: React.FC<PhotoGridPanelProps> = (props) => {
	return (
		<div className="grid grid-cols-3 gap-2 auto-rows-[200px]">
			{props.photos.map((photo) => (
				<SortablePhotoItem key={photo.id} {...props} photo={photo} />
			))}
		</div>
	);
};

export default PhotoGridPanel;
