import { Bookmark, MoreHorizontal } from "lucide-react";
import Image from "next/image";
import type React from "react";
import type { Photo } from "../../instagram-photo-grid-data"; // Adjust path

interface PhotoDetailModalProps {
	photo: Photo | null;
	onClose: () => void;
	likedPhotos: Set<string>;
	onToggleLike: (photoId: string) => void;
}

const PhotoDetailModal: React.FC<PhotoDetailModalProps> = ({
	photo,
	onClose,
	likedPhotos,
	onToggleLike,
}) => {
	if (!photo) {
		return null;
	}

	return (
		<div className="fixed inset-0 bg-gray-500 bg-opacity-50 z-50 flex items-center justify-center p-4 backdrop-blur-sm">
			<div className="bg-white rounded max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col md:flex-row shadow-xl">
				<div className="flex-1 relative min-h-[300px]">
					<Image
						src={photo.src || "/placeholder.svg"}
						alt={photo.alt}
						fill
						className="object-cover"
					/>
				</div>
				<div className="w-full md:w-80 flex flex-col">
					<div className="flex items-center justify-between p-4 border-b border-gray-100">
						<div className="flex items-center gap-3">
							<span className="font-light tracking-wide text-lg">
								{photo.alt}
							</span>
						</div>
						<button onClick={onClose} aria-label="Close modal">
							<MoreHorizontal className="w-5 h-5" />
						</button>
					</div>
					<div className="flex-1 p-4 overflow-y-auto">
						{/* Content area for potential future details, if any. Currently photo.alt is the main descriptor. */}
					</div>
					<div className="border-t border-gray-100 p-4">
						<div className="flex items-center justify-end mb-3">
							<Bookmark className="w-6 h-6" />
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default PhotoDetailModal;
