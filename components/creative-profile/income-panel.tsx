import type React from "react";
import type { Payment } from "../../instagram-photo-grid-data"; // Adjust path

interface IncomePanelProps {
	payments: Payment[];
	totalIncome: number;
	onViewInvoice: (invoiceNumber: string) => void;
}

const IncomePanel: React.FC<IncomePanelProps> = ({
	payments,
	totalIncome,
	onViewInvoice,
}) => {
	return (
		<div className="space-y-8">
			<div className="text-center mb-12">
				<h2 className="text-xl font-light tracking-wide mb-6">Total Income</h2>
				<div className="text-4xl font-light text-green-600 mb-3">
					${totalIncome.toLocaleString()}
				</div>
				<p className="text-gray-600 font-light tracking-wide">
					Lifetime Earnings
				</p>
			</div>

			<div className="overflow-x-auto overflow-y-auto max-h-96 border border-gray-100 rounded">
				<table className="w-full">
					<thead className="bg-gray-50 sticky top-0">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Job
							</th>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Client
							</th>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Amount
							</th>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Date
							</th>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Status
							</th>
							<th className="px-6 py-3 text-left text-xs font-light text-gray-500 tracking-wide">
								Invoice
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-100">
						{payments.map((payment) => (
							<tr key={payment.id} className="hover:bg-gray-50">
								<td className="px-6 py-4 whitespace-nowrap text-sm font-light text-gray-900">
									{payment.jobTitle}
								</td>
								<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light">
									{payment.client}
								</td>
								<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-light">
									${payment.amount.toLocaleString()}
								</td>
								<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-light">
									{payment.date}
								</td>
								<td className="px-6 py-4 whitespace-nowrap">
									<span
										className={`px-2 py-1 text-xs font-light tracking-wide rounded-full ${
											payment.status === "paid"
												? "bg-green-100 text-green-800"
												: "bg-yellow-100 text-yellow-800"
										}`}
									>
										{payment.status.charAt(0).toUpperCase() +
											payment.status.slice(1)}
									</span>
								</td>
								<td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-light">
									<button
										onClick={() => onViewInvoice(payment.invoiceNumber)}
										className="hover:text-blue-800 underline"
									>
										{payment.invoiceNumber}
									</button>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default IncomePanel;
