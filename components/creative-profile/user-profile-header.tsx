import type React from "react";
import type { Creative } from "../../src/types/collections/creative"; // Import the live Creative type

interface UserProfileHeaderProps {
	creative: Creative; // Use the live Creative type directly
	photoCount: number;
}

const UserProfileHeader: React.FC<UserProfileHeaderProps> = ({
	creative,
	photoCount,
}) => {
	return (
		<div className="border-b border-gray-100">
			<div className="max-w-4xl mx-auto px-6 py-16">
				<div className="text-center mb-12">
					<h2 className="text-3xl font-light tracking-wide mb-3">
						{creative.displayName}
					</h2>
					<p className="text-gray-600 font-light tracking-wide mb-6">
						{creative.creativeType || "Creative"}
					</p>
					<p className="text-sm text-gray-500 mb-6">
						{creative.description || ""}
					</p>
					<div className="flex justify-center gap-12 text-sm font-light">
						<span>
							<strong className="font-normal">{photoCount}</strong> Works
						</span>
						{/* These will be re-implemented when the data model supports them */}
						{/* <span>
              <strong className="font-normal">{creative.followers}</strong> Followers
            </span>
            <span>
              <strong className="font-normal">{creative.following}</strong> Following
            </span> */}
					</div>
				</div>
				{/* The Availability Calendar section will be extracted later */}
			</div>
		</div>
	);
};

export default UserProfileHeader;
