import { <PERSON><PERSON> } from "@/components/ui/button"; // Adjust path
import { ArrowDown, ArrowRight, Maximize2, Plus, Trash2 } from "lucide-react"; // Assuming these are used by controls
import React, { useRef } from "react";

type TabName = "photos" | "assignments" | "income" | "archive";

interface PortfolioTabsProps {
	activeTab: TabName;
	onTabChange: (tabName: TabName) => void;
	photoCount: number;
	assignmentCount: number;
	totalIncomeFormatted: string;
	archiveCount: number;
	// Permission control - determines what tabs and controls are visible
	hasEditPermission?: boolean;
	// Photo Grid Control Props (only relevant if activeTab === 'photos')
	isSelectionMode?: boolean;
	onToggleSelectionMode?: () => void;
	onMergeTiles?: (direction: "horizontal" | "vertical" | "large") => Promise<void>;
	onClearSelection?: () => void;
	onConvertToText?: () => void; // Simplified: operates on all selected tiles
	onConvertToImage?: () => void; // Simplified: operates on all selected tiles
	onDeleteSelected?: () => void;
	selectedTilesCount?: number;
	onAddTile?: () => void; // Add the new prop
	onUploadImage?: (file: File) => void;
	onAddIframe?: (url: string) => void;
	isViewMode?: boolean;
	onToggleViewMode?: () => void;
}

const PortfolioTabs: React.FC<PortfolioTabsProps> = ({
	activeTab,
	onTabChange,
	photoCount,
	assignmentCount,
	totalIncomeFormatted,
	archiveCount,
	hasEditPermission = true, // Default to true for backward compatibility
	isSelectionMode,
	onToggleSelectionMode,
	onMergeTiles,
	onClearSelection,
	onConvertToText,
	onConvertToImage,
	onDeleteSelected,
	selectedTilesCount,
	onAddTile,
	onUploadImage,
	onAddIframe,
	isViewMode = false,
	onToggleViewMode,
}) => {
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleImageUploadClick = () => {
		fileInputRef.current?.click();
	};

	const extractUrlFromIframe = (input: string): string => {
		const trimmedInput = input.trim();

		// If it's already a URL, return as-is
		if (trimmedInput.startsWith('http')) {
			return trimmedInput;
		}

		// Extract URL from iframe HTML
		const srcMatch = trimmedInput.match(/src=["']([^"']+)["']/);
		if (srcMatch) {
			// Decode HTML entities
			return srcMatch[1].replace(/&amp;/g, '&');
		}

		return trimmedInput;
	};

	const enhanceVideoUrl = (url: string): string => {
		try {
			const urlObj = new URL(url);

			// YouTube enhancements
			if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
				urlObj.searchParams.set('autoplay', '1');
				urlObj.searchParams.set('loop', '1');
				urlObj.searchParams.set('controls', '0');
				urlObj.searchParams.set('modestbranding', '1');
				urlObj.searchParams.set('rel', '0');
				urlObj.searchParams.set('mute', '1'); // Mute for autoplay to work

				// For looping to work on YouTube, we need the playlist parameter
				const videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
				if (videoId) {
					urlObj.searchParams.set('playlist', videoId);
				}
			}
			// Vimeo enhancements
			else if (urlObj.hostname.includes('vimeo.com')) {
				urlObj.searchParams.set('autoplay', '1');
				urlObj.searchParams.set('loop', '1');
				urlObj.searchParams.set('controls', '0');
				urlObj.searchParams.set('muted', '1');
			}
			// Generic video enhancements
			else {
				urlObj.searchParams.set('autoplay', '1');
				urlObj.searchParams.set('controls', '0');
			}

			return urlObj.toString();
		} catch (error) {
			console.warn('Failed to enhance video URL:', error);
			return url;
		}
	};

	const handleAddIframe = () => {
		const input = prompt("Enter the embed URL or paste the full iframe code:");
		if (input && onAddIframe) {
			const url = extractUrlFromIframe(input);
			if (url) {
				const enhancedUrl = enhanceVideoUrl(url);
				onAddIframe(enhancedUrl);
			} else {
				alert("Please enter a valid URL or iframe code.");
			}
		}
	};

	const handleFileSelected = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file && onUploadImage) {
			onUploadImage(file);
		}
		// Reset file input to allow uploading the same file again if needed
		e.target.value = "";
	};

	return (
		<div className="border-b border-gray-100">
			<div className="max-w-4xl mx-auto px-6 py-8">
				{/* Tab Buttons */}
				<div className="flex items-center justify-center gap-12 mb-8">
					<button
						onClick={() => onTabChange("photos")}
						className={`text-sm font-light tracking-wide ${activeTab === "photos" ? "text-black" : "text-gray-400"}`}
					>
						Work ({photoCount})
					</button>
					{hasEditPermission && (
						<button
							onClick={() => onTabChange("assignments")}
							className={`text-sm font-light tracking-wide ${
								activeTab === "assignments" ? "text-black" : "text-gray-400"
							}`}
						>
							Assignments ({assignmentCount})
						</button>
					)}
					{hasEditPermission && (
						<button
							onClick={() => onTabChange("income")}
							className={`text-sm font-light tracking-wide ${activeTab === "income" ? "text-black" : "text-gray-400"}`}
						>
							Income ({totalIncomeFormatted})
						</button>
					)}
					<button
						onClick={() => onTabChange("archive")}
						className={`text-sm font-light tracking-wide ${activeTab === "archive" ? "text-black" : "text-gray-400"}`}
					>
						Archive ({archiveCount})
					</button>
				</div>

				{/* Photo Grid Controls (Conditional) */}
				{activeTab === "photos" &&
					hasEditPermission &&
					onToggleSelectionMode &&
					onMergeTiles &&
					onClearSelection &&
					onConvertToText &&
					onConvertToImage &&
					onDeleteSelected &&
					onAddTile && (
						<div className="flex items-center justify-center gap-4 flex-wrap">
							<Button
								variant={isSelectionMode ? "default" : "outline"}
								size="sm"
								onClick={onToggleSelectionMode}
								className="text-xs font-light tracking-wide"
							>
								{isSelectionMode ? "Exit Selection" : "Select Tiles"}
							</Button>

							{onToggleViewMode && (
								<Button
									variant={isViewMode ? "default" : "outline"}
									size="sm"
									onClick={onToggleViewMode}
									className="text-xs font-light tracking-wide"
								>
									{isViewMode ? "Exit View Mode" : "View Mode"}
								</Button>
							)}

							{!isSelectionMode && (
								<>
									<Button
										variant="outline"
										size="sm"
										onClick={onAddTile}
										className="text-xs font-light tracking-wide"
									>
										<Plus className="w-3 h-3 mr-1" />
										Add Text
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={handleImageUploadClick}
										className="text-xs font-light tracking-wide"
									>
										<Plus className="w-3 h-3 mr-1" />
										Add Image
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={handleAddIframe}
										className="text-xs font-light tracking-wide"
									>
										<Plus className="w-3 h-3 mr-1" />
										Add Iframe
									</Button>
									<input
										type="file"
										ref={fileInputRef}
										onChange={handleFileSelected}
										style={{ display: "none" }}
										accept="image/*"
									/>
								</>
							)}

							{isSelectionMode && (
								<>
									<Button
										variant="outline"
										size="sm"
										onClick={async () => await onMergeTiles("horizontal")}
										disabled={(selectedTilesCount || 0) < 2}
										className="text-xs font-light tracking-wide"
									>
										<ArrowRight className="w-3 h-3 mr-1" />
										Horizontal
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={async () => await onMergeTiles("vertical")}
										disabled={(selectedTilesCount || 0) < 2}
										className="text-xs font-light tracking-wide"
									>
										<ArrowDown className="w-3 h-3 mr-1" />
										Vertical
									</Button>
									{(selectedTilesCount || 0) >= 4 && ( // Condition for "Large" merge
										<Button
											variant="outline"
											size="sm"
											onClick={async () => await onMergeTiles("large")}
											className="text-xs font-light tracking-wide"
										>
											<Maximize2 className="w-3 h-3 mr-1" />
											Large
										</Button>
									)}
									<Button
										variant="outline"
										size="sm"
										onClick={onClearSelection}
										disabled={(selectedTilesCount || 0) === 0}
										className="text-xs font-light tracking-wide"
									>
										Clear
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={onConvertToText}
										disabled={(selectedTilesCount || 0) === 0}
										className="text-xs font-light tracking-wide"
									>
										Convert to Text
									</Button>
									<Button
										variant="outline"
										size="sm"
										onClick={onConvertToImage}
										disabled={(selectedTilesCount || 0) === 0}
										className="text-xs font-light tracking-wide"
									>
										Convert to Image
									</Button>
									<Button
										variant="destructive"
										size="sm"
										onClick={onDeleteSelected}
										disabled={(selectedTilesCount || 0) === 0}
										className="text-xs font-light tracking-wide"
									>
										<Trash2 className="w-3 h-3 mr-1" />
										Delete Selected
									</Button>
								</>
							)}
						</div>
					)}
			</div>
		</div>
	);
};

export default PortfolioTabs;
