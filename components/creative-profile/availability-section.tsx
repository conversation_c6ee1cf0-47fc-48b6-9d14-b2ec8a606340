import { Button } from "@/components/ui/button"; // Adjust path if your ui/button is elsewhere
import { format, getDay, getDaysInMonth, startOfMonth } from "date-fns";
import type React from "react";
import { useAvailability } from "../hooks/useAvailability";
import useResponsiveMonths from "../hooks/useResponsiveMonths"; // Import the new hook

interface AvailabilitySectionProps {
	activeTab: "photos" | "assignments" | "income" | "archive"; // Or a more specific type if only some tabs affect this section
	onOpenBookingModal: () => void;
	creativeId: string; // Add creativeId to props
	readOnly?: boolean; // Add readOnly prop for bookers viewing creative profiles
}

const AvailabilitySection: React.FC<AvailabilitySectionProps> = ({
	activeTab,
	onOpenBookingModal,
	creativeId,
	readOnly = false,
}) => {
	const { status, error, availableDatesSet, toggleAvailability } =
		useAvailability(creativeId);
	const visibleMonthsCount = useResponsiveMonths();

	const renderCalendar = () => {
		const currentYear = new Date().getFullYear();
		const monthsToDisplay = Array.from(
			{ length: visibleMonthsCount },
			(_, i) => new Date(currentYear, i),
		);

		if (status === "loading")
			return <p className="text-white">Loading availability...</p>;
		if (status === "error")
			return (
				<p className="text-red-500">
					Error loading availability: {error?.message}
				</p>
			);

		// Use explicit grid classes instead of dynamic template literals
		const getGridClass = (count: number) => {
			switch (count) {
				case 1:
					return "grid grid-cols-1 gap-8";
				case 2:
					return "grid grid-cols-2 gap-8";
				case 3:
					return "grid grid-cols-3 gap-8";
				default:
					return "grid grid-cols-3 gap-8";
			}
		};

		return (
			<div className={getGridClass(visibleMonthsCount)}>
				{monthsToDisplay.map((monthDate) => {
					const monthName = format(monthDate, "MMM");
					const year = monthDate.getFullYear();
					const month = monthDate.getMonth();
					const daysInMonth = getDaysInMonth(monthDate);
					const firstDayOfMonth = getDay(startOfMonth(monthDate)); // 0 for Sunday, 1 for Monday, etc.

					return (
						<div key={`${year}-${month}`}>
							<div className="text-center text-gray-400 text-xs mb-4 font-light tracking-wide">
								{monthName}
							</div>
							<div className="grid grid-cols-7 gap-1 mb-3">
								{["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
									<div
										key={`day-header-${index}`}
										className="text-center text-gray-500 text-xs font-light tracking-wide"
									>
										{day}
									</div>
								))}
							</div>
							<div className="grid grid-cols-7 gap-1">
								{/* Render blank tiles for days before the start of the month */}
								{Array.from({ length: firstDayOfMonth }).map((_, i) => (
									<div key={`blank-${i}`} />
								))}
								{/* Render actual day tiles */}
								{Array.from({ length: daysInMonth }, (_, i) => {
									const dayNumber = i + 1;
									const dayDate = new Date(year, month, dayNumber);
									const dateString = format(dayDate, "yyyy-MM-dd");
									const isAvailable = availableDatesSet.has(dateString);

									return (
										<div
											key={`${monthName}-day-${dayNumber}`}
											onClick={readOnly ? undefined : () => toggleAvailability(dayDate)}
											className={`w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light ${
												isAvailable
													? `bg-green-900 text-green-200 ${!readOnly ? "hover:bg-green-800" : ""}`
													: `bg-red-900 text-red-200 ${!readOnly ? "hover:bg-red-800" : ""}`
											} ${readOnly ? "cursor-default" : "cursor-pointer"} transition-colors`}
											title={`${monthName} ${dayNumber}: ${isAvailable ? "Available" : "Booked/Unavailable"}`}
										>
											{dayNumber}
										</div>
									);
								})}
							</div>
						</div>
					);
				})}
			</div>
		);
	};

	return (
		<div className="border-b border-gray-100">
			<div className="max-w-4xl mx-auto px-6 py-16">
				{/* Availability Calendar / Income Graph */}
				<div className="bg-gray-900 rounded p-8 mb-12">
					<div className="flex justify-between items-center mb-6">
						<span className="text-white text-sm font-light tracking-wide">
							{activeTab === "income" ? "Income Over Time" : "Availability"}
						</span>
						<div className="flex items-center gap-6">
							<span className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-green-400"></div>
								<span className="text-gray-400 text-xs font-light tracking-wide">
									Available
								</span>
							</span>
							<span className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-red-400"></div>
								<span className="text-gray-400 text-xs font-light tracking-wide">
									Booked
								</span>
							</span>
						</div>
					</div>

					{/* Calendar with Day Headers or Income Graph */}
					{activeTab === "income" ? (
						/* Income Bar Graph */
						<div className="h-48 relative">
							<svg className="w-full h-full" viewBox="0 0 400 200">
								{/* Grid lines */}
								<defs>
									<pattern
										id="grid"
										width="40"
										height="20"
										patternUnits="userSpaceOnUse"
									>
										<path
											d="M 40 0 L 0 0 0 20"
											fill="none"
											stroke="#374151"
											strokeWidth="0.5"
											opacity="0.3"
										/>
									</pattern>
								</defs>
								<rect width="100%" height="100%" fill="url(#grid)" />

								{/* X and Y axis */}
								<line
									x1="20"
									y1="180"
									x2="380"
									y2="180"
									stroke="#4b5563"
									strokeWidth="1"
								/>
								<line
									x1="20"
									y1="20"
									x2="20"
									y2="180"
									stroke="#4b5563"
									strokeWidth="1"
								/>

								{/* Bar chart data - This is static, consider making it dynamic if needed */}
								{[
									{
										x: 40,
										width: 20,
										height: 60,
										amount: 1200,
										date: "Jan 15",
									},
									{ x: 80, width: 20, height: 90, amount: 1800, date: "Feb 3" },
									{
										x: 120,
										width: 20,
										height: 125,
										amount: 2500,
										date: "Mar 22",
									},
									{
										x: 160,
										width: 20,
										height: 50,
										amount: 1000,
										date: "Apr 10",
									},
									{
										x: 200,
										width: 20,
										height: 75,
										amount: 1500,
										date: "May 5",
									},
									{
										x: 240,
										width: 20,
										height: 110,
										amount: 2200,
										date: "Jun 18",
									},
									{ x: 280, width: 20, height: 40, amount: 800, date: "Jul 7" },
									{
										x: 320,
										width: 20,
										height: 100,
										amount: 2000,
										date: "Aug 29",
									},
									{
										x: 360,
										width: 20,
										height: 80,
										amount: 1600,
										date: "Sep 14",
									},
								].map((bar, index) => (
									<g key={index} className="group">
										<rect
											x={bar.x}
											y={180 - bar.height}
											width={bar.width}
											height={bar.height}
											fill="#10b981"
											className="hover:fill-green-600 transition-colors cursor-pointer"
										>
											<title>
												${bar.amount.toLocaleString()} - {bar.date}
											</title>
										</rect>
										<text
											x={bar.x + bar.width / 2}
											y={175 - bar.height}
											fill="#065f46"
											fontSize="10"
											textAnchor="middle"
											className="opacity-0 group-hover:opacity-100 transition-opacity"
										>
											${bar.amount}
										</text>
									</g>
								))}
								{/* Y-axis labels */}
								<text
									x="15"
									y="30"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="end"
								>
									$2.5K
								</text>
								<text
									x="15"
									y="80"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="end"
								>
									$2K
								</text>
								<text
									x="15"
									y="130"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="end"
								>
									$1K
								</text>
								<text
									x="15"
									y="180"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="end"
								>
									$0
								</text>
								{/* X-axis labels (months) */}
								<text
									x="50"
									y="195"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="middle"
								>
									Jan
								</text>
								<text
									x="130"
									y="195"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="middle"
								>
									Mar
								</text>
								<text
									x="210"
									y="195"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="middle"
								>
									May
								</text>
								<text
									x="290"
									y="195"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="middle"
								>
									Jul
								</text>
								<text
									x="370"
									y="195"
									fill="#9ca3af"
									fontSize="10"
									textAnchor="middle"
								>
									Sep
								</text>
							</svg>
						</div>
					) : (
						renderCalendar()
					)}
				</div>

				{/* Send Offer Button - Only show for bookers (readOnly mode) */}
				{readOnly && (
					<div className="text-center">
						<Button
							onClick={onOpenBookingModal}
							className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide"
						>
							Send Booking Offer
						</Button>
					</div>
				)}
			</div>
		</div>
	);
};

export default AvailabilitySection;
