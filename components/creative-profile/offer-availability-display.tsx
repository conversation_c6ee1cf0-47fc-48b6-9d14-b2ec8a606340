import type React from "react";
import { useState, useMemo } from "react";
import { format, getDay, getDaysInMonth, startOfMonth, parseISO } from "date-fns";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import type { CreativeOffer } from "@/instagram-photo-grid-data";
import { Button } from "@/components/ui/button";

interface OfferAvailabilityDisplayProps {
	activeTab: "photos" | "assignments" | "income" | "archive";
	receivedOffers: CreativeOffer[];
	sentOffers: CreativeOffer[];
	onOpenBookingModal?: () => void; // Optional for bookers viewing creative profiles
	readOnly?: boolean; // To determine if this is a booker viewing a creative profile
}

const OfferAvailabilityDisplay: React.FC<OfferAvailabilityDisplayProps> = ({
	activeTab,
	receivedOffers,
	sentOffers,
	onOpenBookingModal,
	readOnly = false,
}) => {
	const [currentMonthOffset, setCurrentMonthOffset] = useState(0);
	const [selectedDate, setSelectedDate] = useState<Date | null>(null);
	const [showOfferModal, setShowOfferModal] = useState(false);

	// Generate months to display (3 months at a time)
	const displayMonths = useMemo(() => {
		const today = new Date();
		const months = [];

		for (let i = 0; i < 3; i++) {
			const monthDate = new Date(today.getFullYear(), today.getMonth() + currentMonthOffset + i, 1);
			months.push(monthDate);
		}

		return months;
	}, [currentMonthOffset]);

	// Navigation functions
	const goToPreviousMonths = () => {
		setCurrentMonthOffset(prev => prev - 3);
	};

	const goToNextMonths = () => {
		setCurrentMonthOffset(prev => prev + 3);
	};

	const goToCurrentMonth = () => {
		setCurrentMonthOffset(0);
	};

	// Function to get active offers for a specific date
	const getActiveOffersForDate = (date: Date): CreativeOffer[] => {
		const dateString = format(date, "yyyy-MM-dd");
		const allOffers = [...receivedOffers, ...sentOffers];

		return allOffers.filter(offer => {
			if (!offer.offerDate || offer.status !== "active") return false;
			try {
				const offerDate = parseISO(offer.offerDate);
				const offerDateString = format(offerDate, "yyyy-MM-dd");
				return offerDateString === dateString;
			} catch {
				return false;
			}
		});
	};

	// Handle clicking on a busy day
	const handleDayClick = (date: Date, offerStatus: string) => {
		if (offerStatus !== "available") {
			setSelectedDate(date);
			setShowOfferModal(true);
		}
	};



	// Helper function to determine if a date has active offers (busy)
	const getDateOfferStatus = (date: Date) => {
		const dateString = format(date, "yyyy-MM-dd");

		// Check received offers for this date - only active offers
		const hasActiveReceivedOffer = receivedOffers.some(offer => {
			if (!offer.offerDate || offer.status !== "active") return false;
			try {
				const offerDate = parseISO(offer.offerDate);
				const offerDateString = format(offerDate, "yyyy-MM-dd");
				return offerDateString === dateString;
			} catch {
				return false;
			}
		});

		// Check sent offers for this date - only active offers
		const hasActiveSentOffer = sentOffers.some(offer => {
			if (!offer.offerDate || offer.status !== "active") return false;
			try {
				const offerDate = parseISO(offer.offerDate);
				const offerDateString = format(offerDate, "yyyy-MM-dd");
				return offerDateString === dateString;
			} catch {
				return false;
			}
		});

		if (hasActiveReceivedOffer && hasActiveSentOffer) return "both";
		if (hasActiveReceivedOffer) return "received";
		if (hasActiveSentOffer) return "sent";
		return "available";
	};

	const renderCalendar = () => {
		return (
			<div className="grid grid-cols-3 gap-8">
				{displayMonths.map((monthDate) => {
					const monthName = format(monthDate, "MMM");
					const year = monthDate.getFullYear();
					const month = monthDate.getMonth();
					const daysInMonth = getDaysInMonth(monthDate);
					const firstDayOfMonth = getDay(startOfMonth(monthDate)); // 0 for Sunday, 1 for Monday, etc.

					return (
						<div key={`${year}-${month}`}>
							<div className="text-center text-gray-400 text-xs mb-4 font-light tracking-wide">
								{monthName}
							</div>
							<div className="grid grid-cols-7 gap-1 mb-3">
								{["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
									<div
										key={`day-header-${index}`}
										className="text-center text-gray-500 text-xs font-light tracking-wide"
									>
										{day}
									</div>
								))}
							</div>
							<div className="grid grid-cols-7 gap-1">
								{/* Render blank tiles for days before the start of the month */}
								{Array.from({ length: firstDayOfMonth }).map((_, i) => (
									<div key={`blank-${i}`} />
								))}
								{/* Render actual day tiles */}
								{Array.from({ length: daysInMonth }, (_, i) => {
									const dayNumber = i + 1;
									const dayDate = new Date(year, month, dayNumber);
									const offerStatus = getDateOfferStatus(dayDate);

									const getStatusStyles = () => {
										switch (offerStatus) {
											case "received":
												return "bg-blue-900 text-blue-200 hover:bg-blue-800";
											case "sent":
												return "bg-purple-900 text-purple-200 hover:bg-purple-800";
											case "both":
												return "bg-gradient-to-br from-blue-900 to-purple-900 text-white hover:from-blue-800 hover:to-purple-800";
											default:
												return "text-gray-400 hover:text-gray-300"; // No background for available days
										}
									};

									const getTooltipText = () => {
										switch (offerStatus) {
											case "received":
												return `${monthName} ${dayNumber}: Busy (Active Offer Received) - Click for details`;
											case "sent":
												return `${monthName} ${dayNumber}: Busy (Active Offer Sent) - Click for details`;
											case "both":
												return `${monthName} ${dayNumber}: Busy (Active Offers) - Click for details`;
											default:
												return `${monthName} ${dayNumber}: Available`;
										}
									};

									return (
										<div
											key={`${monthName}-day-${dayNumber}`}
											className={`w-6 h-6 rounded-sm flex items-center justify-center text-xs font-light transition-colors ${getStatusStyles()} ${
												offerStatus !== "available" ? "cursor-pointer hover:scale-110" : "cursor-default"
											}`}
											title={getTooltipText()}
											onClick={() => handleDayClick(dayDate, offerStatus)}
										>
											{dayNumber}
										</div>
									);
								})}
							</div>
						</div>
					);
				})}
			</div>
		);
	};

	const renderIncomeGraph = () => {
		// Calculate income data from accepted offers
		const acceptedOffers = [...receivedOffers, ...sentOffers].filter(
			offer => offer.status === "accepted"
		);

		// Group by month and calculate totals
		const monthlyIncome = acceptedOffers.reduce((acc, offer) => {
			if (!offer.offerDate) return acc;
			try {
				const offerDate = parseISO(offer.offerDate);
				const monthKey = format(offerDate, "MMM");
				acc[monthKey] = (acc[monthKey] || 0) + offer.amount;
			} catch {
				// Skip invalid dates
			}
			return acc;
		}, {} as Record<string, number>);

		const maxAmount = Math.max(...Object.values(monthlyIncome), 5000);
		const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep"];

		return (
			<div className="h-48 relative">
				<svg className="w-full h-full" viewBox="0 0 400 200">
					{/* Grid lines */}
					<defs>
						<pattern
							id="grid"
							width="40"
							height="20"
							patternUnits="userSpaceOnUse"
						>
							<path
								d="M 40 0 L 0 0 0 20"
								fill="none"
								stroke="#374151"
								strokeWidth="0.5"
								opacity="0.3"
							/>
						</pattern>
					</defs>
					<rect width="100%" height="100%" fill="url(#grid)" />

					{/* X and Y axis */}
					<line
						x1="20"
						y1="180"
						x2="380"
						y2="180"
						stroke="#4b5563"
						strokeWidth="1"
					/>
					<line
						x1="20"
						y1="20"
						x2="20"
						y2="180"
						stroke="#4b5563"
						strokeWidth="1"
					/>

					{/* Income bars */}
					{months.map((month, index) => {
						const income = monthlyIncome[month] || 0;
						const barHeight = (income / maxAmount) * 140;
						const x = 40 + index * 40;

						return (
							<g key={month} className="group">
								<rect
									x={x}
									y={180 - barHeight}
									width="20"
									height={barHeight}
									fill="#10b981"
									className="hover:fill-green-600 transition-colors cursor-pointer"
								>
									<title>
										${income.toLocaleString()} - {month}
									</title>
								</rect>
								<text
									x={x + 10}
									y={175 - barHeight}
									fill="#065f46"
									fontSize="10"
									textAnchor="middle"
									className="opacity-0 group-hover:opacity-100 transition-opacity"
								>
									${income}
								</text>
							</g>
						);
					})}

					{/* Y-axis labels */}
					<text x="15" y="30" fill="#9ca3af" fontSize="10" textAnchor="end">
						${Math.round(maxAmount / 1000)}K
					</text>
					<text x="15" y="80" fill="#9ca3af" fontSize="10" textAnchor="end">
						${Math.round(maxAmount * 0.75 / 1000)}K
					</text>
					<text x="15" y="130" fill="#9ca3af" fontSize="10" textAnchor="end">
						${Math.round(maxAmount * 0.25 / 1000)}K
					</text>
					<text x="15" y="180" fill="#9ca3af" fontSize="10" textAnchor="end">
						$0
					</text>

					{/* X-axis labels (months) */}
					{months.slice(0, 5).map((month, index) => (
						<text
							key={month}
							x={50 + index * 80}
							y="195"
							fill="#9ca3af"
							fontSize="10"
							textAnchor="middle"
						>
							{month}
						</text>
					))}
				</svg>
			</div>
		);
	};

	return (
		<div className="bg-gray-900 rounded p-8 mb-12">
			{/* Header */}
			<div className="flex justify-between items-center mb-6">
				<div className="flex items-center gap-4">
					<span className="text-white text-sm font-light tracking-wide">
						{activeTab === "income" ? "Income Over Time" : "Availability (Active Offers Only)"}
					</span>
					{activeTab !== "income" && (
						<button
							onClick={goToCurrentMonth}
							className="text-xs text-gray-400 hover:text-white transition-colors px-2 py-1 rounded border border-gray-600 hover:border-gray-500"
						>
							Today
						</button>
					)}
				</div>
				{activeTab !== "income" && (
					<div className="flex items-center gap-6">
						<span className="flex items-center gap-2">
							<div className="w-2 h-2 rounded-full bg-blue-400"></div>
							<span className="text-gray-400 text-xs font-light tracking-wide">
								Active Received Offers
							</span>
						</span>
						<span className="flex items-center gap-2">
							<div className="w-2 h-2 rounded-full bg-purple-400"></div>
							<span className="text-gray-400 text-xs font-light tracking-wide">
								Active Sent Offers
							</span>
						</span>
					</div>
				)}
			</div>

			{/* Navigation for calendar */}
			{activeTab !== "income" && (
				<div className="flex justify-between items-center mb-6">
					<button
						onClick={goToPreviousMonths}
						className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
					>
						<ChevronLeft className="w-4 h-4" />
						<span className="text-sm">Previous</span>
					</button>

					<div className="text-sm text-gray-300">
						<span className="font-light">
							{new Date().getFullYear()}
						</span>
					</div>

					<button
						onClick={goToNextMonths}
						className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
					>
						<span className="text-sm">Next</span>
						<ChevronRight className="w-4 h-4" />
					</button>
				</div>
			)}

			{/* Calendar or Income Graph */}
			{activeTab === "income" ? renderIncomeGraph() : renderCalendar()}

			{/* Send Offer Button - Only show for bookers (readOnly mode) */}
			{readOnly && onOpenBookingModal && (
				<div className="text-center mt-8">
					<Button
						onClick={onOpenBookingModal}
						className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-sm font-light tracking-wide"
					>
						Send Booking Offer
					</Button>
				</div>
			)}

			{/* Offer Details Modal */}
			{showOfferModal && selectedDate && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
						<div className="flex justify-between items-center mb-4">
							<h3 className="text-lg font-semibold text-gray-900">
								Active Offers - {format(selectedDate, "MMM d, yyyy")}
							</h3>
							<button
								onClick={() => setShowOfferModal(false)}
								className="text-gray-400 hover:text-gray-600 transition-colors"
							>
								<X className="w-5 h-5" />
							</button>
						</div>

						<div className="space-y-4">
							{getActiveOffersForDate(selectedDate).map((offer) => (
								<div key={offer.id} className="border border-gray-200 rounded-lg p-4">
									<div className="flex justify-between items-start mb-2">
										<h4 className="font-medium text-gray-900">{offer.projectTitle}</h4>
										<span className={`px-2 py-1 text-xs rounded-full ${
											offer.isSentByMe ? "bg-purple-100 text-purple-800" : "bg-blue-100 text-blue-800"
										}`}>
											{offer.isSentByMe ? "Sent" : "Received"}
										</span>
									</div>
									<p className="text-sm text-gray-600 mb-2">{offer.clientName}</p>
									<p className="text-sm text-gray-700 mb-2">{offer.role}</p>
									<p className="text-sm text-gray-600 mb-2">{offer.location}</p>
									<p className="text-sm text-gray-600 mb-3">{offer.description}</p>
									<div className="flex justify-between items-center">
										<span className="text-lg font-semibold text-green-600">
											${offer.amount.toLocaleString()}
										</span>
										<span className="text-xs text-gray-500">
											{offer.projectType}
										</span>
									</div>
								</div>
							))}
						</div>

						<div className="mt-6 text-center">
							<Button
								onClick={() => setShowOfferModal(false)}
								variant="outline"
								className="px-6"
							>
								Close
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default OfferAvailabilityDisplay;
