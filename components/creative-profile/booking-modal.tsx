import { <PERSON><PERSON> } from "@/components/ui/button"; // Adjust path
import { MoreHorizontal } from "lucide-react";
import type React from "react";
import { type Project } from "../../instagram-photo-grid-data"; // Adjust path

// Define a more specific type for the booking form if possible
interface BookingFormState {
	projectId: string;
	projectTitle: string;
	projectType: string;
	startDate: string;
	endDate: string;
	location: string;
	budget: string; // Keep as string as in original state
	description: string;
	role: string;
	proposedRate: string; // Keep as string
	rateType: string;
	notes: string;
}

interface BookingModalProps {
	show: boolean;
	onClose: () => void;
	bookingForm: BookingFormState;
	onFormChange: (field: keyof BookingFormState, value: string) => void; // Use keyof for better type safety
	onFormProjectSelect: (selectedProject: Project | undefined) => void;
	onSubmit: (e: React.FormEvent) => void;
	projects: Project[]; // To populate the dropdown
	isSubmitting: boolean; // Add isSubmitting prop
	isLoadingProjects?: boolean; // Add loading state for projects
}

const BookingModal: React.FC<BookingModalProps> = ({
	show,
	onClose,
	bookingForm,
	onFormChange,
	onFormProjectSelect,
	onSubmit,
	projects,
	isSubmitting, // Destructure isSubmitting
	isLoadingProjects = false, // Destructure isLoadingProjects with default
}) => {
	if (!show) {
		return null;
	}

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
			<div className="bg-white rounded max-w-2xl w-full max-h-[90vh] overflow-hidden">
				<div className="flex items-center justify-between p-6 border-b border-gray-100">
					<h2 className="text-xl font-light tracking-wide">
						Send Booking Offer
					</h2>
					<button onClick={onClose}>
						<MoreHorizontal className="w-5 h-5" />
					</button>
				</div>

				<form
					onSubmit={onSubmit}
					className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]"
				>
					<div className="space-y-6">
						{/* Project Selection */}
						<div>
							<h3 className="text-lg font-light tracking-wide mb-4">
								Select Project
							</h3>
							<div className="mb-4">
								<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
									Project *
								</label>
								<select
									required
									value={bookingForm.projectId || ""}
									onChange={(e) => {
										const selectedProj = projects.find(
											(p) => p.id === e.target.value,
										);
										onFormProjectSelect(selectedProj);
									}}
									className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
									disabled={isLoadingProjects}
								>
									<option value="">
										{isLoadingProjects ? "Loading your projects..." : "Select a project"}
									</option>
									{!isLoadingProjects && projects.map((project) => (
										<option key={project.id} value={project.id}>
											{project.title} - {project.client}
										</option>
									))}
								</select>
							</div>
						</div>

						{/* Project Details (Read-only if a project is selected) */}
						{bookingForm.projectId && (
							<div>
								<h3 className="text-lg font-light tracking-wide mb-4">
									Project Details
								</h3>
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div>
										<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
											Project Title
										</label>
										<input
											type="text"
											value={bookingForm.projectTitle}
											readOnly
											className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
										/>
									</div>
									<div>
										<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
											Project Type
										</label>
										<input
											type="text"
											value={bookingForm.projectType}
											readOnly
											className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
										/>
									</div>
								</div>
								<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
									<div>
										<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
											Start Date
										</label>
										<input
											type="text"
											value={bookingForm.startDate}
											readOnly
											className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
										/>
									</div>
									<div>
										<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
											End Date
										</label>
										<input
											type="text"
											value={bookingForm.endDate}
											readOnly
											className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
										/>
									</div>
									<div>
										<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
											Budget (USD)
										</label>
										<input
											type="text"
											value={`$${Number.parseInt(bookingForm.budget || "0").toLocaleString()}`}
											readOnly
											className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
										/>
									</div>
								</div>
								<div className="mt-4">
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Location
									</label>
									<input
										type="text"
										value={bookingForm.location}
										readOnly
										className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
									/>
								</div>
								<div className="mt-4">
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Description
									</label>
									<textarea
										rows={3}
										value={bookingForm.description}
										readOnly
										className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded font-light"
									/>
								</div>
							</div>
						)}

						{/* Offer Details */}
						{bookingForm.projectId && (
							<div>
								<h3 className="text-lg font-light tracking-wide mb-4">
									Offer Details
								</h3>
								<div className="mb-4">
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Role *
									</label>
									<select
										required
										value={bookingForm.role || ""}
										onChange={(e) => onFormChange("role", e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
									>
										<option value="">Select Role</option>
										{projects
											.find((p) => p.id === bookingForm.projectId)
											?.creativesNeeded?.map((role, index) => (
												<option key={index} value={role}>
													{role}
												</option>
											)) || (
											<>
												<option value="Photographer">Photographer</option>
												<option value="Videographer">Videographer</option>
												<option value="Model">Model</option>
												<option value="Makeup Artist">Makeup Artist</option>
												<option value="Hair Stylist">Hair Stylist</option>
												<option value="Stylist">Stylist</option>
											</>
										)}
									</select>
								</div>
								<div className="mt-4">
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Additional Notes
									</label>
									<textarea
										rows={3}
										value={bookingForm.notes || ""}
										onChange={(e) => onFormChange("notes", e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
										placeholder="Any specific requirements or details for this creative..."
									/>
								</div>
							</div>
						)}

						{/* Rate Information */}
						<div>
							{" "}
							{/* This section should always be visible for a new offer, might need adjustment if projectId is prerequisite */}
							<h3 className="text-lg font-light tracking-wide mb-4">
								Rate Information
							</h3>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Proposed Rate *
									</label>
									<input
										type="number"
										required
										value={bookingForm.proposedRate}
										onChange={(e) =>
											onFormChange("proposedRate", e.target.value)
										}
										className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
										placeholder="1500"
									/>
								</div>
								<div>
									<label className="block text-sm font-light text-gray-700 mb-2 tracking-wide">
										Rate Type *
									</label>
									<select
										required
										value={bookingForm.rateType}
										onChange={(e) => onFormChange("rateType", e.target.value)}
										className="w-full px-3 py-2 border border-gray-200 rounded font-light focus:outline-none focus:ring-2 focus:ring-black"
									>
										<option value="">Select Rate Type</option>
										<option value="hourly">Per Hour</option>
										<option value="daily">Per Day</option>
										<option value="project">Per Project</option>
										<option value="weekly">Per Week</option>
										<option value="monthly">Per Month</option>
									</select>
								</div>
							</div>
						</div>
					</div>

					<div className="flex gap-4 mt-8 pt-6 border-t border-gray-100">
						<Button
							type="button"
							variant="outline"
							onClick={onClose}
							className="flex-1 font-light tracking-wide"
							disabled={isSubmitting}
						>
							Cancel
						</Button>
						<Button
							type="submit"
							className="flex-1 bg-black text-white hover:bg-gray-800 font-light tracking-wide"
							disabled={!bookingForm.projectId || isSubmitting} // Disable if submitting or no project ID
						>
							{isSubmitting ? "Sending..." : "Send Offer"}{" "}
							{/* Conditional text/spinner */}
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default BookingModal;
