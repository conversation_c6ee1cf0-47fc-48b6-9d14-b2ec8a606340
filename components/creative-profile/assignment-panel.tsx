import { Button } from "@/components/ui/button"; // Assuming Button component is available
import type React from "react";
import type { CreativeOffer, Job } from "../../instagram-photo-grid-data"; // Adjust path, import CreativeOffer

interface AssignmentsPanelProps {
	viewMode: "upcoming" | "receivedOffers" | "rejectedOffers";
	onViewModeChange: (
		mode: "upcoming" | "receivedOffers" | "rejectedOffers",
	) => void;
	jobs: Job[];
	receivedOffers: CreativeOffer[];
	sentOffers: CreativeOffer[];
	onAcceptOffer: (offerId: string) => void;
	onDeclineOffer: (offerId: string) => void;
}

const AssignmentsPanel: React.FC<AssignmentsPanelProps> = ({
	viewMode,
	onViewModeChange,
	jobs,
	receivedOffers,
	sentOffers,
	onAcceptOffer,
	onDeclineOffer,
}) => {
	// Filter active offers from both received and sent offers
	const activeOffers = [...receivedOffers, ...sentOffers].filter(offer => offer.status === "active");

	// Filter rejected offers from both received and sent offers
	const rejectedOffers = [...receivedOffers, ...sentOffers].filter(offer => offer.status === "rejected_by_creative");

	// Filter pending approval offers from received offers only
	const pendingApprovalOffers = receivedOffers.filter(offer => offer.status === "pending_creative_approval");

	let title = "Upcoming Assignments";
	let dataToDisplay: (Job | CreativeOffer)[] = [...jobs, ...activeOffers];

	if (viewMode === "receivedOffers") {
		title = "Offers Received";
		dataToDisplay = receivedOffers.filter(offer => offer.status === "pending_creative_approval");
	} else if (viewMode === "rejectedOffers") {
		title = "Rejected Offers";
		dataToDisplay = rejectedOffers;
	}

	const getStatusColor = (status: Job["status"] | CreativeOffer["status"]) => {
		switch (status) {
			case "confirmed": // Job status
			case "active": // Offer status
				return "bg-green-100 text-green-800";
			case "pending": // Job & Offer status
			case "negotiating": // Offer status
			case "pending_creative_approval": // Offer status
				return "bg-yellow-100 text-yellow-800";
			case "declined": // Offer status
			case "expired": // Offer status
			case "withdrawn": // Offer status
			case "rejected_by_creative": // Offer status
				return "bg-red-100 text-red-800";
			case "completed": // Job status
				return "bg-blue-100 text-blue-800"; // Using blue for completed jobs
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-center gap-4 mb-8">
				<Button
					variant={viewMode === "upcoming" ? "default" : "outline"}
					onClick={() => onViewModeChange("upcoming")}
					className={`font-light tracking-wide ${
						activeOffers.length > 0 && viewMode === "upcoming"
							? "bg-green-600 hover:bg-green-700 text-white"
							: ""
					}`}
				>
					Upcoming Assignments ({jobs.length + activeOffers.length})
				</Button>
				<Button
					variant={viewMode === "receivedOffers" ? "default" : "outline"}
					onClick={() => onViewModeChange("receivedOffers")}
					className="font-light tracking-wide"
				>
					Offers Received ({pendingApprovalOffers.length})
				</Button>
				<Button
					variant={viewMode === "rejectedOffers" ? "default" : "outline"}
					onClick={() => onViewModeChange("rejectedOffers")}
					className="font-light tracking-wide"
				>
					Rejected Offers ({rejectedOffers.length})
				</Button>
			</div>

			<h2 className="text-xl font-light tracking-wide text-center mb-12">
				{title}
			</h2>

			{dataToDisplay.length === 0 ? (
				<p className="text-center text-gray-500 font-light">
					No items to display in this view.
				</p>
			) : (
				dataToDisplay.map((item) => {
					if ("time" in item) {
						// It's a Job
						const job = item as Job;
						return (
							<div
								key={job.id}
								className="border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg"
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<h3 className="font-light tracking-wide text-lg mb-2">
											{job.title}
										</h3>
										<p className="text-gray-600 mb-1 font-light tracking-wide">
											Client: {job.client}
										</p>
										<div className="flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2">
											<span>Date: {job.date}</span>
											<span>Time: {job.time}</span>
											<span>Location: {job.location}</span>
											<span>Type: {job.type}</span>
										</div>
									</div>
									<div className="text-right ml-4 flex-shrink-0">
										<span
											className={`px-3 py-1 text-xs font-light tracking-wide rounded-full ${getStatusColor(job.status)}`}
										>
											{job.status.charAt(0).toUpperCase() + job.status.slice(1)}
										</span>
									</div>
								</div>
							</div>
						);
					} else {
						// It's a CreativeOffer
						const offer = item as CreativeOffer;
						return (
							<div
								key={offer.id}
								className="border border-gray-100 p-6 hover:shadow-sm transition-shadow rounded-lg"
							>
								<div className="flex items-start justify-between">
									<div className="flex-1">
										<h3 className="font-light tracking-wide text-lg mb-2">
											{offer.projectTitle}
										</h3>
										<p className="text-gray-600 mb-1 font-light tracking-wide">
											{offer.isSentByMe ? "To: " : "From: "} {offer.clientName}
										</p>
										<p className="text-gray-600 mb-1 font-light tracking-wide">
											Role: {offer.role}
										</p>
										<p className="text-gray-600 mb-1 font-light tracking-wide">
											Amount: ${(offer.amount || 0).toLocaleString()}
										</p>
										<div className="flex flex-wrap items-center gap-x-6 gap-y-1 text-sm text-gray-500 font-light tracking-wide mt-2">
											<span>Offered: {offer.offerDate}</span>
											<span>Type: {offer.projectType}</span>
											{offer.location && (
												<span>Location: {offer.location}</span>
											)}
										</div>
										{offer.description && (
											<p className="text-xs text-gray-500 mt-2 font-light italic">
												Note: {offer.description}
											</p>
										)}
									</div>
									<div className="text-right ml-4 flex-shrink-0">
										<span
											className={`px-3 py-1 text-xs font-light tracking-wide rounded-full ${getStatusColor(offer.status)}`}
										>
											{offer.status.charAt(0).toUpperCase() +
												offer.status.slice(1)}
										</span>
									</div>
								</div>
								{!offer.isSentByMe && (offer.status === "pending" || offer.status === "pending_creative_approval") && (
									<div className="flex justify-end gap-2 mt-4 pt-4 border-t border-gray-100">
										<Button
											variant="outline"
											size="sm"
											onClick={() => onDeclineOffer(offer.id)}
											className="font-light"
										>
											Decline
										</Button>
										<Button
											size="sm"
											onClick={() => onAcceptOffer(offer.id)}
											className="font-light bg-black text-white"
										>
											Accept Offer
										</Button>
									</div>
								)}
							</div>
						);
					}
				})
			)}
		</div>
	);
};

export default AssignmentsPanel;
