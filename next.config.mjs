/** @type {import('next').NextConfig} */
const nextConfig = {
	output: "standalone",
	transpilePackages: ["dua-component-library"],
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	images: {
		unoptimized: true,
	},
	webpack: (config, { isServer }) => {
		config.resolve.symlinks = true; // Explicitly enable symlink resolution
		// Optionally, you could try `false` if `true` doesn't work,
		// as sometimes disabling and relying on direct path resolution can help in edge cases.
		return config;
	},
};

export default nextConfig;
